<template>
  <div class="bg-white rounded-lg shadow-sm border border-gray-200 w-full overflow-hidden">
    <div v-if="files.length === 0" class="flex flex-col items-center justify-center p-8 text-center">
      <HugeiconsIcon :icon="Folder01Icon" class="text-5xl text-gray-300 mb-3" />
      <h3 class="text-lg font-medium text-gray-700">{{ t('fileManager.noFiles') }}</h3>
    </div>

    <div v-else class="w-full">
      <div class="p-4 border-b border-gray-200 flex justify-between items-center">
        <div v-if="selectedFiles.length > 0" class="flex items-center gap-4">
          <span class="text-sm text-gray-600">{{ selectedFiles.length }} {{ t('fileManager.selected') }}</span>
          <button 
            @click="deleteSelected"
            class="px-3 py-1.5 text-sm text-red-600 hover:bg-red-50 rounded-md border border-red-200 flex items-center gap-1.5"
            :disabled="isDeleting"
          >
            <HugeiconsIcon :icon="Delete01Icon" class="text-base" />
            {{ t('fileManager.deleteSelected') }}
          </button>
        </div>
        <div v-else class="text-sm text-gray-500">
          {{ t('fileManager.selectFiles') }}
        </div>
      </div>
      <table class="min-w-full text-left border-collapse">
        <colgroup>
          <col class="w-8">
          <col class="w-2/5">
          <col class="w-1/5">
          <col class="w-1/6">
          <col class="w-1/6">
          <col class="w-1/6">
        </colgroup>
        <thead>
          <tr>
            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-y border-gray-200">
              <input 
                type="checkbox" 
                :checked="allSelected"
                @change="toggleSelectAll"
                class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </th>
            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-y border-gray-200">{{ t('fileManager.name') }}</th>
            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-y border-gray-200 whitespace-nowrap">{{ t('fileManager.modified') }}</th>
            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-y border-gray-200">{{ t('fileManager.source') }}</th>
            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-y border-gray-200">{{ t('fileManager.size') }}</th>
            <th class="px-4 py-3 text-xs font-medium text-gray-500 uppercase tracking-wider bg-gray-50 border-y border-gray-200 text-right">{{ t('fileManager.actions') }}</th>
          </tr>
        </thead>
        <tbody>
          <tr
            v-for="file in files"
            :key="file.id || file.name"
            :class="[
              'group hover:bg-gray-50 transition-all duration-200',
              file.type === 'FOLDER' ? 'drop-zone-row' : '',
              'draggable-row'
            ]"
            :draggable="file.source !== 'SYSTEM'"
            @dragstart="file.source !== 'SYSTEM' ? handleDragStart($event, file) : $event.preventDefault()"
            @dragend="handleDragEnd"
            @dragover="file.type === 'FOLDER' ? handleDragOver($event, file) : $event.preventDefault()"
            @dragleave="file.type === 'FOLDER' ? handleDragLeave($event) : null"
            @drop="file.type === 'FOLDER' ? handleDrop($event, file) : $event.preventDefault()"
          >
            <td class="px-4 py-3 text-center border-b border-gray-200">
              <input 
                type="checkbox" 
                :checked="isSelected(file)"
                @change="toggleSelect(file)"
                @click.stop
                class="h-4 w-4 rounded border-gray-300 text-blue-600 focus:ring-blue-500"
              />
            </td>
            <td class="px-4 py-3 text-sm text-gray-700 border-b border-gray-200" @click="!editingFile?.id && handleFileClick(file)">
              <div class="flex items-center gap-3 relative min-h-[44px]">
                <div class="flex-shrink-0">
                  <HugeiconsIcon :icon="getFileIcon(file)" :class="`text-${environmentColor} text-xl`" size="22" />
                </div>
                <div class="min-w-0 flex-1">
                  <template v-if="editingFile?.id === file.id">
                    <div @click.stop class="w-full">
                      <IluriaInputText
                        v-model="newFileName"
                        @keyup.enter="saveEdit"
                        @blur="saveEdit"
                        @keyup.escape="editingFile = null"
                        class="w-full"
                      />
                    </div>
                  </template>
                  <template v-else>
                    <div class="relative group/name w-full">
                      <div class="truncate max-w-[200px] text-sm leading-6">{{ file.name }}</div>
                      <div v-if="file.name.length > 25" class="absolute z-50 hidden group-hover/name:block min-w-max bg-gray-900 text-white text-xs rounded py-1 px-2 -top-8 left-1/2 -translate-x-1/2">
                        {{ file.name }}
                        <div class="absolute w-2 h-2 bg-gray-900 transform rotate-45 -bottom-1 left-1/2 -ml-1"></div>
                      </div>
                    </div>
                  </template>
                </div>
              </div>
            </td>
            <td class="px-4 py-3 text-sm text-gray-700 border-b border-gray-200 whitespace-nowrap">{{ formatDate(file.updatedAt) }}</td>
            <td class="px-4 py-3 text-sm text-gray-700 border-b border-gray-200">{{ file.source }}</td>
            <td class="px-4 py-3 text-sm text-gray-700 border-b border-gray-200">{{ formatSize(file.size) }}</td>
            <td class="px-4 py-3 text-sm text-gray-700 border-b border-gray-200">
              <div class="flex items-center justify-end gap-1 opacity-100 transition-opacity">
                
                <button 
                  @click.stop="editingFile?.id === file.id ? saveEdit() : handleRename(file)"
                  class="p-2 rounded-lg transition-all duration-200 border"
                  :class="[
                    editingFile?.id === file.id 
                      ? 'bg-green-100 text-green-600 border-green-200 hover:bg-green-200'
                      : 'bg-white text-gray-600 border-gray-200 hover:bg-gray-50 hover:text-gray-800 hover:border-gray-300'
                  ]"
                  :title="editingFile?.id === file.id ? 'Salvar' : 'Editar'"
                >
                  <HugeiconsIcon 
                    :icon="editingFile?.id === file.id ? CheckmarkCircle02Icon : PencilEdit01Icon" 
                    class="text-lg" 
                    size="15"
                  />
                </button>
                <button 
                  @click.stop="handleDownload(file)"
                  class="p-2 rounded-lg transition-all duration-200 bg-white text-gray-600 border border-gray-200 hover:bg-gray-50 hover:text-gray-800 hover:border-gray-300 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-500" 
                  title="Download"
                >
                  <HugeiconsIcon :icon="CloudDownloadIcon" class="text-lg" size="15" />
                </button>
                <button 
                  @click.stop="handleDelete($event, file)"
                  class="p-2 rounded-lg transition-all duration-200 bg-white text-gray-600 border border-gray-200 hover:bg-red-50 hover:text-red-600 hover:border-red-200 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-red-500" 
                  title="Delete"
                  :disabled="isDeleting">
                  <HugeiconsIcon :icon="Delete01Icon" class="text-lg" size="15" />
                </button>
              </div>
            </td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>
<script setup>
import {HugeiconsIcon} from '@hugeicons/vue';
import { Folder01Icon, Image01Icon, Doc01Icon, Pdf01Icon,
  Xls01Icon, Ppt01Icon, File01Icon, DocumentCodeIcon, Zip01Icon,
  CheckmarkCircle02Icon, PencilEdit01Icon, Delete01Icon, CloudDownloadIcon } from '@hugeicons-pro/core-bulk-rounded';
import { ref, computed, nextTick } from 'vue';
import IluriaInputText from '@/components/Iluria/form/IluriaInputText.vue';
import fileManagerService from '@/services/fileManager.service.js';
import { useToast } from '@/services/toast.service';
import { useI18n } from 'vue-i18n';
import { useDragAndDrop } from '@/composables/useDragAndDrop';

const { t } = useI18n();

const toast = useToast();

const props = defineProps({
  files: Array,
  viewMode: String,
  environmentColor: String
});

const emit = defineEmits(['file-click', 'file-updated', 'file-delete-requested', 'folder-click', 'item-moved']);

// Drag and Drop
const {
  isDragging,
  draggedItem,
  dragOverTarget,
  isMoving,
  handleDragStart,
  handleDragEnd,
  handleDragOver,
  handleDragLeave,
  handleDrop: baseDrop,
  moveItem
} = useDragAndDrop();

// Custom drop handler for FileListView
const handleDrop = async (event, targetFile) => {
  try {
    const result = await baseDrop(event, targetFile);
    if (result) {
      // Emit the event immediately for parent component updates
      emit('item-moved', result);

      // Force immediate UI update
      await nextTick();

      // Remove dragged item from current view if it was moved
      const draggedItemId = result.movedItem.id;
      const updatedFiles = props.files.filter(file => file.id !== draggedItemId);
      
      // Update local state by emitting file-updated event for each remaining file
      updatedFiles.forEach(file => {
        emit('file-updated', file);
      });
    }
  } catch (error) {
    console.error('Error in file list drop:', error);
  }
};

const editingFile = ref(null);
const newFileName = ref('');
const isDeleting = ref(false);
const selectedFiles = ref([]);

const allSelected = computed(() => {
  return props.files.length > 0 && selectedFiles.value.length === props.files.length;
});

const isSelected = (file) => {
  return selectedFiles.value.some(f => f.id === file.id);
};

const toggleSelect = (file) => {
  const index = selectedFiles.value.findIndex(f => f.id === file.id);
  if (index === -1) {
    selectedFiles.value.push(file);
  } else {
    selectedFiles.value.splice(index, 1);
  }
};

const toggleSelectAll = () => {
  if (allSelected.value) {
    selectedFiles.value = [];
  } else {
    selectedFiles.value = [...props.files];
  }
};

const deleteSelected = async () => {
  if (selectedFiles.value.length === 0 || isDeleting.value) return;
  
  try {
    isDeleting.value = true;
    emit('delete-selected-requested', selectedFiles.value);
  } finally {
    isDeleting.value = false;
  }
};

const formatDate = (date) => {
  return new Date(date).toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: false
  });
};

const handleFileClick = (file) => {
  if (file.type === 'FOLDER' || file.isFolder) {
    emit('folder-click', file);
  } else {
    emit('file-click', file);
  }
};

const handleDelete = async (event, file) => {
  if (event) {
    event.stopPropagation();
    event.preventDefault();
  }
  
  if (isDeleting.value) return;
  
  try {
    isDeleting.value = true;
    emit('file-delete-requested', file);
  } finally {
    isDeleting.value = false;
  }
};

const handleRename = (file) => {
  editingFile.value = file;
  newFileName.value = file.name;
};

const saveEdit = async () => {
  if (!editingFile.value || newFileName.value === editingFile.value.name) {
    editingFile.value = null;
    return;
  }

  try {
    await fileManagerService.update(editingFile.value.id, { 
      name: newFileName.value,
      parentFolderId: editingFile.value.parentFolderId,
      environment: editingFile.value.environment 
    });
    
    const updatedFile = { ...editingFile.value, name: newFileName.value };
    
    emit('file-updated', updatedFile);
    
    toast.showSuccess(t('fileManager.renameSuccess'));
    
  } catch (error) {
    console.error('Erro ao renomear:', error);
    toast.showError(t('fileManager.renameError'));
  } finally {
    editingFile.value = null;
  }
};

const getFileIcon = (file) => {
  if (file.isFolder || file.type === 'FOLDER') {
    return Folder01Icon;
  }
  
  const fileName = file.name || '';
  
  const extension = fileName.split('.').pop().toLowerCase();
  
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
  if (imageExtensions.includes(extension)) return Image01Icon;
  
  const docExtensions = ['doc', 'docx'];
  if (docExtensions.includes(extension)) return Doc01Icon;
  
  const pdfExtensions = ['pdf'];
  if (pdfExtensions.includes(extension)) return Pdf01Icon;
  
  const xlsExtensions = ['xls', 'xlsx', 'csv'];
  if (xlsExtensions.includes(extension)) return Xls01Icon;
  
  const pptExtensions = ['ppt', 'pptx'];
  if (pptExtensions.includes(extension)) return Ppt01Icon;
  
  const codeExtensions = ['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'json'];
  if (codeExtensions.includes(extension)) return DocumentCodeIcon;
  
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz'];
  if (archiveExtensions.includes(extension)) return Zip01Icon;
  
  return File01Icon ;
};

const formatSize = (size) => {
  if (!size || isNaN(parseInt(size))) return '0 B';
  
  const sizeNum = parseInt(size);
  if (sizeNum < 1024) return `${sizeNum} B`;
  if (sizeNum < 1024 * 1024) return `${(sizeNum / 1024).toFixed(1)} KB`;
  return `${(sizeNum / (1024 * 1024)).toFixed(1)} MB`;
};

const handleDownload = async (file) => {
  try {
    let response;
    let fileName;
    
    if (file.type === 'FOLDER' || file.isFolder) {
      response = await fileManagerService.downloadFolderById(file.id, file.environment);
      fileName = `${file.name}.zip`;
    } else {
      response = await fileManagerService.downloadFileById(file.id, file.environment);
      fileName = file.name;
    }
    
    const blob = new Blob([response.data]);
    
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    
    link.setAttribute('download', fileName);
    document.body.appendChild(link);
    link.click();
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading file:', error);
    toast.showError(t('fileManager.downloadError'));
  }
};
</script>

<style scoped>

</style>
