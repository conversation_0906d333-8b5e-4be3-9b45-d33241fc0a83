<template>
  <div>
    <div class="form-fields">
      <!-- Oferecer SEDEX -->
      <div class="field-container">
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <label 
              for="correiosOfferSedex"
              class="block text-sm font-medium option-label cursor-pointer"
              @click="correiosOfferSedex = !correiosOfferSedex"
            >
              {{ t('shippingConfig.correios.offerSedex') }}
            </label>
            <IluriaToggleSwitch
              id="correiosOfferSedex"
              name="correiosOfferSedex"
              v-model="correiosOfferSedex"
            />
          </div>
        </div>
      </div>

      <!-- Oferecer PAC -->
      <div class="field-container">
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <label 
              for="correiosOfferPac"
              class="block text-sm font-medium option-label cursor-pointer"
              @click="correiosOfferPac = !correiosOfferPac"
            >
              {{ t('shippingConfig.correios.offerPac') }}
            </label>
            <IluriaToggleSwitch
              id="correiosOfferPac"
              name="correiosOfferPac"
              v-model="correiosOfferPac"
            />
          </div>
        </div>
      </div>

      <!-- Incluir preço do seguro -->
      <div class="field-container">
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <label 
              for="correiosIncludeInsurance"
              class="block text-sm font-medium option-label cursor-pointer"
              @click="correiosIncludeInsurance = !correiosIncludeInsurance"
            >
              {{ t('shippingConfig.correios.includeInsurance') }}
            </label>
            <IluriaToggleSwitch
              id="correiosIncludeInsurance"
              name="correiosIncludeInsurance"
              v-model="correiosIncludeInsurance"
            />
          </div>
        </div>
      </div>

      <!-- Exibir prazo estimado -->
      <div class="field-container">
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <label 
              for="correiosShowDeliveryTime"
              class="block text-sm font-medium option-label cursor-pointer"
              @click="correiosShowDeliveryTime = !correiosShowDeliveryTime"
            >
              {{ t('shippingConfig.correios.showDeliveryTime') }}
            </label>
            <IluriaToggleSwitch
              id="correiosShowDeliveryTime"
              name="correiosShowDeliveryTime"
              v-model="correiosShowDeliveryTime"
            />
          </div>
        </div>
      </div>

      <!-- Tem contrato com os Correios -->
      <div class="field-container">
        <div class="space-y-2">
          <div class="flex items-center justify-between">
            <label 
              for="correiosHasContract"
              class="block text-sm font-medium option-label cursor-pointer"
              @click="correiosHasContract = !correiosHasContract"
            >
              {{ t('shippingConfig.correios.hasContract') }}
            </label>
            <IluriaToggleSwitch
              id="correiosHasContract"
              name="correiosHasContract"
              v-model="correiosHasContract"
            />
          </div>
        </div>
      </div>

      <!-- Credenciais dos Correios -->
      <div v-if="correiosHasContract" class="field-container">
        <div class="space-y-4">
          <h3 class="text-lg font-medium section-title">{{ t('shippingConfig.correios.credentials') }}</h3>
          
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <IluriaInputText
                id="correiosUser"
                name="correiosUser"
                type="text"
                :label="t('shippingConfig.correios.user')"
                v-model="correiosUser"
                :formContext="formContext?.correiosUser"
              />
            </div>
            
            <div>
              <IluriaInputText
                id="correiosApiKey"
                name="correiosApiKey"
                type="password"
                :label="t('shippingConfig.correios.apiKey')"
                v-model="correiosApiKey"
                :formContext="formContext?.correiosApiKey"
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { requiredText } from '@/services/validation.service';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

// Props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Models
const correiosOfferSedex = defineModel('correiosOfferSedex', { type: Boolean, default: false });
const correiosOfferPac = defineModel('correiosOfferPac', { type: Boolean, default: false });
const correiosIncludeInsurance = defineModel('correiosIncludeInsurance', { type: Boolean, default: false });
const correiosShowDeliveryTime = defineModel('correiosShowDeliveryTime', { type: Boolean, default: false });
const correiosHasContract = defineModel('correiosHasContract', { type: Boolean, default: false });
const correiosUser = defineModel('correiosUser', { type: String, default: '' });
const correiosApiKey = defineModel('correiosApiKey', { type: String, default: '' });

// Validation rules
const validationRules = {
  correiosUser: requiredText(t('shippingConfig.correios.user')),
  correiosApiKey: requiredText(t('shippingConfig.correios.apiKey'))
};

// Watch para o contrato
watch(correiosHasContract, (newValue) => {
  if (!newValue) {
    correiosUser.value = '';
    correiosApiKey.value = '';
  }
});

// Expose validation rules for parent component
defineExpose({
  validationRules
});
</script>

<style scoped>
.space-y-2 > * + * {
  margin-top: 8px;
}

.space-y-4 > * + * {
  margin-top: 16px;
}

.space-y-6 > * + * {
  margin-top: 24px;
}

/* Container para os campos com separadores */
.form-fields {
  display: flex;
  flex-direction: column;
}

.field-container {
  padding: 16px 0;
  position: relative;
}

.field-container:not(:last-child)::after {
  content: '';
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: 1px;
  background-color: #e5e7eb;
  opacity: 0.6;
}

/* Estilização consistente com o sistema de temas da Iluria */
.option-label {
  color: var(--iluria-color-text-primary) !important;
}

.section-title {
  color: var(--iluria-color-text-primary) !important;
}

.cursor-pointer {
  cursor: pointer;
}

/* Modo escuro */
@media (prefers-color-scheme: dark) {
  .field-container:not(:last-child)::after {
    background-color: #374151;
  }
}

/* Responsividade */
@media (max-width: 768px) {
  .field-container {
    padding: 12px 0;
  }
}
</style> 
