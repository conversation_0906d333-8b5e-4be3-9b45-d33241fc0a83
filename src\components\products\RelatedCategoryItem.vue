<template>
  <div class="related-category-item py-1.5" :style="{ paddingLeft: `${level * 16}px` }">
    <div class="flex items-center">
      <!-- Expand/Collapse Icon (only if has children) -->
      <div 
        v-if="category.children && category.children.length > 0" 
        @click="toggleExpand(category)"
        class="w-4 h-4 flex items-center justify-center cursor-pointer text-gray-400 text-xs"
      >
        <span v-if="category.expanded">▼</span>
        <span v-else>▶</span>
      </div>
      <div v-else class="w-4 h-4"></div>
      
      <!-- Checkbox with Label -->
      <div class="flex items-center">
        <IluriaCheckBox
          :id="`${keyPrefix}-category-${category.id}`"
          :name="`${keyPrefix}-category-${category.id}`"
          :label="category.name"
          :modelValue="category.checked"
          @update:modelValue="handleCheckboxChange"
          class="mx-2"
        />
      </div>
    </div>
    
    <!-- Render Children (if expanded) -->
    <div v-if="category.expanded && category.children && category.children.length > 0">
      <div v-for="child in category.children" :key="`${keyPrefix}-${child.id}`">
        <RelatedCategoryItem 
          :category="child" 
          :level="level + 1"
          :toggleExpand="toggleExpand"
          :toggleCheck="toggleCheck"
          :keyPrefix="keyPrefix"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { defineProps, defineAsyncComponent } from 'vue';
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';

const RelatedCategoryItem = defineAsyncComponent(() => import('./RelatedCategoryItem.vue'));

const props = defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  },
  toggleExpand: {
    type: Function,
    required: true
  },
  toggleCheck: {
    type: Function,
    required: true
  },
  keyPrefix: {
    type: String,
    default: 'related'
  }
});

const handleCheckboxChange = (value) => {
  props.toggleCheck(props.category);
};
</script>

<style scoped>
.related-category-item {
  margin: 1px 0;
}
</style> 
