import { ref } from 'vue'
import { useComponentRegistry } from '@/composables/useComponentRegistry.js'
import { useEditorRegistry } from '@/components/layoutEditor/registry/EditorRegistry.js'

/**
 * Composable para facilitar o uso do sistema automático de editores
 */
export function useEditorSystem() {
  const { registerComponent, getToolbarActionsForElement } = useComponentRegistry()
  const { registerEditor, loadEditor } = useEditorRegistry()

  /**
   * Registra um componente completo com seus editores
   * @param {Object} config - Configuração completa do componente
   */
  const registerComponentWithEditors = (config) => {
    const {
      // Configurações do componente
      type,
      name,
      description,
      html,
      category = 'custom',
      icon = null,
      
      // Editores disponíveis
      editors = [],
      
      // Configurações avançadas
      autoDetection = {},
      customActions = []
    } = config



    // 1. Registra o componente base
    const toolbarActions = []
    const propertyEditors = []

    // 2. Processa editores
    editors.forEach(editor => {
      const {
        type: editorType,
        title,
        icon: editorIcon,
        component,
        path,
        conditions = [],
        priority = 0
      } = editor

      // Registra o editor no EditorRegistry
      registerEditor(editorType, {
        component,
        path,
        category: category,
        priority,
        conditions,
        autoSave: editor.autoSave !== false,
        debounceTime: editor.debounceTime || 300
      })

      // Adiciona ação da toolbar
      toolbarActions.push({
        type: editorType,
        title,
        icon: editorIcon,
        conditions,
        priority
      })

      // Adiciona editor de propriedades
      propertyEditors.push({
        type: editorType,
        component,
        path
      })
    })

    // 3. Adiciona ações customizadas
    customActions.forEach(action => {
      toolbarActions.push(action)
    })

    // 4. Registra no ComponentRegistry
    registerComponent({
      type,
      name,
      description,
      html,
      category,
      icon,
      toolbarActions,
      propertyEditors,
      computedCheckers: autoDetection
    })


  }

  /**
   * Registra apenas um editor individual
   * @param {string} type - Tipo do editor
   * @param {Object} config - Configuração do editor
   */
  const registerIndividualEditor = (type, config) => {

    
    registerEditor(type, {
      ...config,
      category: config.category || 'individual',
      autoSave: config.autoSave !== false,
      debounceTime: config.debounceTime || 300
    })
  }

  /**
   * Registra ações de toolbar para elementos existentes
   * @param {string} elementType - Tipo do elemento
   * @param {Array} actions - Ações a serem adicionadas
   */
  const addToolbarActions = (elementType, actions) => {

    
    // Esta função permite adicionar ações a elementos já existentes
    // É útil para estender componentes existentes com novos editores
    actions.forEach(action => {
      registerEditor(action.type, {
        component: action.component,
        path: action.path,
        category: 'extension',
        targetElement: elementType
      })
    })
  }

  /**
   * Cria um sistema de detecção automática
   * @param {Object} detectionRules - Regras de detecção
   */
  const createAutoDetection = (detectionRules) => {
    const checkers = {}
    
    Object.entries(detectionRules).forEach(([ruleName, rule]) => {
      checkers[ruleName] = (element) => {
        if (typeof rule === 'function') {
          return rule(element)
        }
        
        if (typeof rule === 'string') {
          // Rule é um seletor CSS
          return element.matches(rule) || element.closest(rule)
        }
        
        if (Array.isArray(rule)) {
          // Rule é um array de seletores
          return rule.some(selector => 
            element.matches(selector) || element.closest(selector)
          )
        }
        
        return false
      }
    })
    
    return checkers
  }

  /**
   * Helper para criar configurações comuns de editores
   */
  const createEditorConfig = (type, options = {}) => {
    const defaultPath = `@/components/layoutEditor/Sidebar/editors/${type.charAt(0).toUpperCase() + type.slice(1)}Editor.vue`
    
    return {
      type,
      title: options.title || `Edit ${type}`,
      icon: options.icon || null,
      component: options.component || null,
      path: options.path || defaultPath,
      conditions: options.conditions || [],
      priority: options.priority || 0,
      autoSave: options.autoSave !== false,
      debounceTime: options.debounceTime || 300
    }
  }

  /**
   * Configurações pré-definidas para editores comuns
   */
  const commonEditors = {
    text: () => createEditorConfig('text', {
      title: 'Edit Text',
      conditions: ['data-element-type="text"', 'data-text-element']
    }),
    
    font: () => createEditorConfig('font', {
      title: 'Edit Font',
      conditions: ['data-element-type="text"', 'h1,h2,h3,h4,h5,h6,p']
    }),
    
    spacing: () => createEditorConfig('spacing', {
      title: 'Edit Spacing',
      conditions: ['*:not([data-component="dynamic-grid-produtos"])']
    }),
    
    border: () => createEditorConfig('border', {
      title: 'Edit Border'
    }),
    
    background: () => createEditorConfig('background', {
      title: 'Edit Background'
    }),
    
    image: () => createEditorConfig('image', {
      title: 'Edit Image',
      conditions: ['img', 'data-element-type="media"']
    }),
    
    link: () => createEditorConfig('link', {
      title: 'Edit Link',
      conditions: ['a', 'data-interactive-element']
    }),
    
    animation: () => createEditorConfig('animation', {
      title: 'Edit Animation'
    }),
    
    transform: () => createEditorConfig('transform', {
      title: 'Edit Transform'
    }),
    
    filter: () => createEditorConfig('filter', {
      title: 'Edit Filter'
    })
  }

  return {
    // Funções principais
    registerComponentWithEditors,
    registerIndividualEditor,
    addToolbarActions,
    
    // Helpers
    createAutoDetection,
    createEditorConfig,
    commonEditors,
    
    // Registry direto
    getToolbarActionsForElement,
    loadEditor
  }
}

export default useEditorSystem 