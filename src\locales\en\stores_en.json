{"welcomeBack": "Welcome back", "createStore": "Create store", "manageAccount": "Manage account", "switchAccount": "Switch account", "logout": "Log out", "loadingStores": "Loading stores...", "noStores": "No stores found", "noStoresDescription": "You don't have any stores yet. Create your first store to get started.", "createFirstStore": "Create first store", "createNewStore": "Create new store", "storeName": "Store name", "storeNamePlaceholder": "Enter your store name", "storeUrl": "Store URL", "storeUrlPlaceholder": "my-store", "storeUrlHelp": "This will be your store URL: my-store.iluria.com", "storeNameRequired": "Store name is required", "storeCreatedSuccess": "Store created successfully!", "storeCreationError": "Error creating store. Please try again.", "loadStoresError": "Error loading stores. Please try again.", "enteringStore": "Entering store", "switchAccountNotImplemented": "Feature not yet implemented", "initializingStore": "Setting up your store", "settingUpEverything": "We're getting everything ready for you", "redirectingToStore": "Redirecting to your store...", "initializationCompleted": "Store setup completed successfully!", "initializationError": "Setup error", "initializationSteps": {"starting": "Starting setup", "creatingDomain": "Setting up domain", "creatingCategories": "Creating categories", "creatingProducts": "Creating products", "creatingEmailNotificationSettings": "Creating Email Notification Settings", "completed": "Setup completed"}, "initializationMessages": {"starting": "Starting store configuration...", "creatingDomain": "Setting up temporary domain...", "creatingCategories": "Creating sample categories...", "creatingEmailNotificationSettings": "Setting up email notifications...", "creatingProducts": "Creating sample products...", "completed": "Store configured successfully!"}}