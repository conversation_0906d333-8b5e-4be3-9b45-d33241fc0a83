<template>
  <div class="file-manager-container">
    <IluriaHeader :title="t('fileManager.title')" :subtitle="t('fileManager.subtitle')"/>
    <ViewContainer :title="$t('fileManager.title')" class="w-full file-manager-theme" :icon="FolderLibraryIcon" iconColor="gray">
      <div class="w-full px-4 sm:px-6 lg:px-8 py-8 min-w-0">
        <div class="flex flex-col space-y-6">
          <!-- Header -->
          <header class="flex justify-between items-center border-b border-gray-200 pb-4">
            <EnvironmentSwitcher 
            :current-environment="currentEnvironment" 
              @change="handleEnvironmentChange" 
            />
          </header>
  
          <!-- Main content with navigation tabs -->
          <div class="flex flex-col">
            <div class="border-b border-gray-200">
              <nav class="flex -mb-px">
                <button 
                @click="switchComponent('explorer')"
                  :class="[ 'py-3 px-4 border-b-2 font-medium text-sm focus:outline-none transition-colors duration-200',
                            activeComponent === 'explorer' ? 'font-semibold' : 'text-gray-500 hover:text-gray-700 hover:border-gray-300' ]"
                  :style="activeComponent === 'explorer' ? { borderColor: 'var(--iluria-color-primary)', color: 'var(--iluria-color-primary)' } : {}"
                  >
                  {{ $t('fileManager.explorer') }}
                </button>
              </nav>
            </div>
  
            <!-- Content area -->
            <div class="mt-6 w-full overflow-x-auto">
              <!-- Loading state -->
              <div v-if="loading" class="flex justify-center items-center py-8">
                <div class="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-blue-500"></div>
              </div>
              
              <!-- Error state -->
              <div v-else-if="error" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
                <span class="block sm:inline">{{ error }}</span>
                <button 
                  @click="fetchFileTree(currentEnvironment)" 
                  class="mt-3 bg-red-100 hover:bg-red-200 text-red-800 font-semibold py-2 px-4 rounded-md transition-colors duration-200">
                  {{ $t('fileManager.tryAgain') }}
                </button>
              </div>
              
              <!-- Content when data is loaded -->
              <template v-else>
                <FileExplorer 
                  v-if="activeComponent === 'explorer'"
                  ref="fileExplorerRef"
                  :file-structure="fileStructure" 
                  :environment="currentEnvironment"
                  @folder-click="handleFolderClick"
                  @update:file-structure="updateFileStructure"
                  @folder-created="fetchFileTree(currentEnvironment)"
                  @folder-deleted="handleFolderDeleted"
                  @file-deleted="handleFileDeleted"
                  @file-created="fetchFileTree(currentEnvironment)"
                  @node-delete-request="handleNodeDeleteRequest"
                  @refresh-tree="refreshFileStructure"
                />
              </template>
            </div>
          </div>
        </div>
      </div>
    </ViewContainer>
  </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch } from 'vue'
  import { useI18n } from 'vue-i18n'
  import FileExplorer from '@/components/FileManager/FileExplorer.vue'
  import EnvironmentSwitcher from '@/components/FileManager/EnvironmentSwitcher.vue'
  import fileManagerService from '@/services/fileManager.service.js'
  import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
  import { FolderLibraryIcon } from '@hugeicons-pro/core-stroke-rounded';
  
  const { t } = useI18n()
  
  const currentEnvironment = ref('DEVELOP')
  
  const getValidEnvironment = (env) => {
    if (typeof env === 'string') {
      return env.toUpperCase() === 'PRODUCTION' ? 'PRODUCTION' : 'DEVELOP';
    }
    return 'DEVELOP';
  }
  const fileStructure = ref([])
  const loading = ref(false)
  const error = ref(null)
  
  const transformApiData = (apiData) => {
    return apiData.map(item => ({
      key: item.id,
      label: item.name,
      parentFolderId: item.parentFolderId,
      type: item.type,
      size: item.size,
      createdAt: item.createdAt,
      updatedAt: item.updatedAt,
      children: item.children && item.children.length > 0 ? transformApiData(item.children) : []
    }));
  };
  
  const fetchFileTree = async (environment) => {
    loading.value = true
    error.value = null
    
    try {
      const data = await fileManagerService.getFileTree(environment)
      fileStructure.value = transformApiData(data)
    } catch (err) {
      console.error('Erro ao buscar estrutura de arquivos:', err)
    } finally {
      loading.value = false
    }
  }
  
  const activeComponent = ref('explorer')
  
  const switchComponent = (component) => {
    activeComponent.value = component
  }
  
  const handleEnvironmentChange = (env) => {
    const validEnv = getValidEnvironment(env);
    currentEnvironment.value = validEnv;
    fetchFileTree(validEnv);
  }
  
  const handleFolderClick = (folder) => {
  }
  
  const updateFileStructure = (newStructure) => {
    fileStructure.value = newStructure;
  }

  const refreshFileStructure = async () => {
    try {
      await fetchFileTree(currentEnvironment.value);
    } catch (error) {
      console.error('Error refreshing file structure:', error);
    }
  }
  
  const removeNodeFromTree = (nodeId) => {
    const removeNode = (nodes) => {
      if (!nodes || !Array.isArray(nodes)) return false;
      
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].key === nodeId) {
          nodes.splice(i, 1);
          return true;
        }
        
        if (nodes[i].children && nodes[i].children.length > 0) {
          if (removeNode(nodes[i].children)) {
            return true;
          }
        }
      }
      
      return false;
    };
    
    const newStructure = [...fileStructure.value];
    removeNode(newStructure);
    fileStructure.value = newStructure;
  };
  
  const handleFileDeleted = (file) => {
    removeNodeFromTree(file.id);
    fetchFileTree(currentEnvironment.value);
  };
  
  const handleFolderDeleted = (folder) => {
    if (folder && folder.id) {
      removeNodeFromTree(folder.id);
    } else {
      fetchFileTree(currentEnvironment.value);
    }
  };
  
  const fileExplorerRef = ref(null);
  
  const handleNodeDeleteRequest = (node) => {
    if (fileExplorerRef.value) {
      if (node.type === 'FOLDER') {
        fileExplorerRef.value.handleNodeDelete(node);
      } else {
        fileExplorerRef.value.handleDeleteRequest(node);
      }
    }
  };
  
  const handleUploadComplete = async () => {
    try {
      await fetchFileTree(currentEnvironment.value);
      
      if (fileExplorerRef.value) {
        await fileExplorerRef.value.handleRefreshTree();
      }
    } catch (error) {
      console.error('Erro ao atualizar estrutura após upload:', error);
    }
  };
  
  watch(currentEnvironment, (newEnvironment) => {
    fetchFileTree(newEnvironment)
  })
  onMounted(() => {
    fetchFileTree(currentEnvironment.value)
  })
  </script>

<style>
/* Theme overrides scoped to FileManager component */
.file-manager-theme .bg-white {
  background-color: var(--iluria-color-container-bg) !important;
}

.file-manager-theme .bg-gray-50, .file-manager-theme .bg-gray-100, .file-manager-theme .bg-gray-200 {
  background-color: var(--iluria-color-sidebar-bg) !important;
}

.file-manager-theme .border-gray-200 {
  border-color: var(--iluria-color-border) !important;
}

.file-manager-theme .border-gray-300 {
  border-color: var(--iluria-color-border-hover) !important;
}

.file-manager-theme .text-gray-300, .file-manager-theme .text-gray-400, .file-manager-theme .text-gray-500, .file-manager-theme .text-gray-600, .file-manager-theme .text-gray-700 {
  color: var(--iluria-color-text-primary) !important;
}

.file-manager-theme .hover\:bg-gray-50:hover, .file-manager-theme .hover\:bg-gray-100:hover, .file-manager-theme .hover\:bg-gray-200:hover {
  background-color: var(--iluria-color-hover) !important;
}

.file-manager-theme .hover\:border-gray-300:hover {
  border-color: var(--iluria-color-border-hover) !important;
}

.file-manager-theme .hover\:text-gray-800:hover {
  color: var(--iluria-color-text-primary) !important;
}

.file-manager-theme .border-gray-400 {
  border-color: var(--iluria-color-border-hover) !important;
}

/* Compact rename field */
.file-manager-theme .custom-input-group {
  background-color: var(--iluria-color-sidebar-bg) !important;
  border-color: var(--iluria-color-border-hover) !important;
}

.file-manager-theme .custom-input-group .input-themed-custom {
  color: var(--iluria-color-text-primary) !important;
}

.file-manager-theme .custom-input-group .input-themed-custom::placeholder {
  color: var(--iluria-color-text-muted) !important;
}

.file-manager-theme .custom-input-group .input-themed-custom::selection {
  background-color: var(--iluria-color-primary) !important;
  color: #ffffff !important;
}

.file-manager-container{
  padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}
</style>
