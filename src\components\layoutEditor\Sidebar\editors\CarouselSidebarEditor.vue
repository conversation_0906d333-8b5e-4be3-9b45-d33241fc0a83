<template>
  <div class="carousel-editor">
    <!-- Header -->
    <div class="editor-header">
      <h2 class="editor-title">Editor <PERSON></h2>
      <p class="editor-subtitle">Editando componente carousel</p>
    </div>

    <!-- Navigation Tabs -->
    <div class="tabs-container">
      <div class="tabs-nav">
        <button 
          v-for="tab in tabs" 
          :key="tab.id"
          :class="['tab-btn', { active: activeTab === tab.id }]"
          @click="setActiveTab(tab.id)"
        >
          {{ tab.label }}
        </button>
      </div>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      <!-- Slides Tab -->
      <div v-if="activeTab === 'slides'" class="tab-panel">
        <div class="section-header">
          <h3>Gerenciar Slides</h3>
          <MyButton 
            type="primary" 
            size="small"
            @click="addNewSlide"
            class="add-btn"
          >
            <Plus class="btn-icon" />
            Adicionar Slide
          </MyButton>
        </div>

        <div class="slides-grid">
          <div 
            v-for="(slide, index) in slides" 
            :key="slide.id"
            :class="['slide-card', { 
              active: currentSlideIndex === index,
              inactive: !slide.active 
            }]"
            @click="selectSlide(index)"
          >
            <div class="slide-preview">
              <!-- Preview para imagem -->
              <div class="slide-preview-background" :style="{
                backgroundImage: slide.imageUrl ? `url('${slide.imageUrl}')` : '',
                backgroundSize: 'cover',
                backgroundPosition: 'center',
                backgroundRepeat: 'no-repeat',
                width: '100%',
                height: '100%',
                position: 'absolute',
                top: 0,
                left: 0,
                transition: 'background-image 0.3s ease'
              }">
                <!-- Placeholder quando não há imagem -->
                <div v-if="!slide.imageUrl" class="slide-placeholder">
                  <ImageIcon class="placeholder-icon" />
                </div>
              </div>
              
              <div class="slide-content">
                <h4>{{ slide.title || 'Novo Slide' }}</h4>
                <p v-if="slide.description">{{ truncateText(slide.description, 50) }}</p>
              </div>
            </div>

            <div class="slide-controls">
              <div class="slide-status">
                <span :class="['status-badge', { active: slide.active }]">
                  {{ slide.active ? 'Ativo' : 'Inativo' }}
                </span>
              </div>
              
              <div class="slide-actions">
                <button 
                  @click.stop="moveSlide(index, -1)"
                  :disabled="index === 0"
                  class="action-btn"
                  title="Mover para cima"
                >
                  <ChevronUp class="action-icon" />
                </button>
                
                <button 
                  @click.stop="moveSlide(index, 1)"
                  :disabled="index === slides.length - 1"
                  class="action-btn"
                  title="Mover para baixo"
                >
                  <ChevronDown class="action-icon" />
                </button>
                
                <button 
                  @click.stop="deleteSlide(index)"
                  :disabled="slides.length <= 1"
                  class="action-btn danger"
                  title="Remover slide"
                >
                  <Trash class="action-icon" />
                </button>
              </div>
            </div>
          </div>
        </div>

        <div v-if="slides.length === 0" class="empty-state">
          <ImageIcon class="empty-icon" />
          <h4>Nenhum slide criado</h4>
          <p>Clique em "Adicionar Slide" para começar</p>
        </div>
      </div>

      <!-- Content Tab -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        <div v-if="currentSlide" class="content-editor">
          <div class="section-header">
            <h3>Editando: {{ currentSlide.title || 'Novo Slide' }}</h3>
          </div>

          <div class="form-sections">
            <!-- Basic Info -->
            <div class="form-section">
              <h4>Informações Básicas</h4>
              
              <div class="form-group">
                <IluriaLabel>Título do Slide</IluriaLabel>
                <IluriaInputText
                  v-model="currentSlide.title"
                  placeholder="Digite o título do slide"
                  @input="updateSlideData"
                />
              </div>

              <div class="form-group">
                <IluriaLabel>Descrição</IluriaLabel>
                <Textarea
                  v-model="currentSlide.description"
                  placeholder="Digite a descrição do slide"
                  rows="3"
                  @update:modelValue="updateSlideData"
                />
              </div>

              <div class="form-group">
                <IluriaLabel>Status do Slide</IluriaLabel>
                <IluriaToggleSwitch
                  v-model="currentSlide.active"
                  :label="currentSlide.active ? 'Slide Ativo' : 'Slide Inativo'"
                  @update:modelValue="updateSlideData"
                />
              </div>
            </div>

            <!-- Image Section -->
            <div class="form-section">
              <h4>Imagem do Slide</h4>
              
              <div class="form-group">
                <IluriaLabel>Imagem</IluriaLabel>
                <div class="image-input-group">
                  <IluriaInputText
                    v-model="currentSlide.imageUrl"
                    placeholder="https://exemplo.com/imagem.jpg"
                    @input="updateSlideData"
                  />
                  <MyButton 
                    type="outline" 
                    size="small"
                    @click="selectImage"
                  >
                    <ImageIcon class="btn-icon" />
                    Selecionar
                  </MyButton>
                </div>
                
                <div v-if="currentSlide.imageUrl" class="image-preview">
                  <div class="image-preview-normal">
                    <img :src="currentSlide.imageUrl" :alt="currentSlide.title" />
                    <button 
                      @click="removeImage" 
                      class="remove-image-btn"
                      title="Remover imagem"
                    >
                      <X class="remove-icon" />
                    </button>
                  </div>
                </div>
              </div>
            </div>



            <!-- Configurações do Slide -->
            <div class="form-section">
              <h4>Configurações do Slide</h4>
              
              <!-- Dimensões -->
              <div class="form-group">
                <IluriaLabel>Altura ({{ design.height }}px)</IluriaLabel>
                <input
                  v-model="design.height"
                  type="range"
                  min="200"
                  max="800"
                  step="20"
                  class="range-input"
                  @input="updateDesign"
                />
                <div class="range-labels">
                  <span>200px</span>
                  <span>800px</span>
                </div>
              </div>

              <div class="form-group">
                <IluriaLabel>Bordas Arredondadas ({{ design.borderRadius }}px)</IluriaLabel>
                <input
                  v-model="design.borderRadius"
                  type="range"
                  min="0"
                  max="24"
                  step="2"
                  class="range-input"
                  @input="updateDesign"
                />
                <div class="range-labels">
                  <span>0px</span>
                  <span>24px</span>
                </div>
              </div>

              <!-- Colors -->
              <div class="color-grid">
                <div class="color-group">
                  <IluriaLabel>Cor do Título</IluriaLabel>
                  <IluriaColorPicker
                    v-model="currentSlide.titleColor"
                    @update:modelValue="updateSlideData"
                  />
                </div>

                <div class="color-group">
                  <IluriaLabel>Cor da Descrição</IluriaLabel>
                  <IluriaColorPicker
                    v-model="currentSlide.descriptionColor"
                    @update:modelValue="updateSlideData"
                  />
                </div>
              </div>

              <!-- Typography -->
              <div class="form-group">
                <IluriaLabel>Tamanho do Título ({{ currentSlide.titleFontSize || 48 }}px)</IluriaLabel>
                <input
                  v-model="currentSlide.titleFontSize"
                  type="range"
                  min="24"
                  max="64"
                  step="2"
                  class="range-input"
                  @input="updateSlideData"
                />
                <div class="range-labels">
                  <span>24px</span>
                  <span>64px</span>
                </div>
              </div>

              <div class="form-group">
                <IluriaLabel>Tamanho da Descrição ({{ currentSlide.descriptionFontSize || 18 }}px)</IluriaLabel>
                <input
                  v-model="currentSlide.descriptionFontSize"
                  type="range"
                  min="14"
                  max="24"
                  step="1"
                  class="range-input"
                  @input="updateSlideData"
                />
                <div class="range-labels">
                  <span>14px</span>
                  <span>24px</span>
                </div>
              </div>

              <div class="form-group">
                <MyButton 
                  type="outline" 
                  size="small"
                  @click="resetSlideDesign"
                  style="width: 100%;"
                >
                  Resetar para Configurações Globais
                </MyButton>
              </div>
            </div>
          </div>
        </div>

        <div v-else class="no-slide-selected">
          <ImageIcon class="empty-icon" />
          <h4>Nenhum slide selecionado</h4>
          <p>Selecione um slide na aba "Slides" para editá-lo</p>
        </div>
      </div>

      <!-- Settings Tab -->
      <div v-if="activeTab === 'settings'" class="tab-panel">
        <div class="settings-content">
          <div class="form-sections">
            <!-- Playback Settings -->
            <div class="form-section">
              <h4>Configurações de Reprodução</h4>
              
              <div class="form-group">
                <IluriaToggleSwitch
                  v-model="settings.autoplay"
                  label="Reprodução Automática"
                  @update:modelValue="updateSettings"
                />
              </div>

              <div v-if="settings.autoplay" class="form-group">
                <IluriaLabel>Velocidade ({{ settings.interval }}ms)</IluriaLabel>
                <input
                  v-model="settings.interval"
                  type="range"
                  min="2000"
                  max="10000"
                  step="500"
                  class="range-input"
                  @input="updateSettings"
                />
                <div class="range-labels">
                  <span>2s</span>
                  <span>10s</span>
                </div>
              </div>
            </div>

            <!-- Navigation Settings -->
            <div class="form-section">
              <h4>Configurações de Navegação</h4>
              
              <div class="form-group">
                <IluriaToggleSwitch
                  v-model="settings.showArrows"
                  label="Mostrar Setas de Navegação"
                  @update:modelValue="updateSettings"
                />
              </div>

              <div class="form-group">
                <IluriaToggleSwitch
                  v-model="settings.showDots"
                  label="Mostrar Indicadores de Slides"
                  @update:modelValue="updateSettings"
                />
              </div>
            </div>

            <!-- Transition Settings -->
            <div class="form-section">
              <h4>Configurações de Transição</h4>
              
              <div class="form-group">
                <IluriaLabel>Tipo de Transição</IluriaLabel>
                <select 
                  v-model="settings.transition" 
                  class="select-input"
                  @change="updateSettings"
                >
                  <option value="slide">Deslizar</option>
                  <option value="fade">Fade</option>
                  <option value="zoom">Zoom</option>
                </select>
              </div>
            </div>
          </div>
        </div>
      </div>


    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'

// Icons - usando simples ícones SVG para evitar dependências externas
const Plus = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M12 5v14m-7-7h14"/></svg>' }
const ImageIcon = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="3" y="3" width="18" height="18" rx="2" ry="2"/><circle cx="9" cy="9" r="2"/><path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/></svg>' }
const ChevronUp = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m18 15-6-6-6 6"/></svg>' }
const ChevronDown = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="m6 9 6 6 6-6"/></svg>' }
const Trash = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M3 6h18m-2 0v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6m3 0V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/></svg>' }
const X = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><path d="M18 6 6 18M6 6l12 12"/></svg>' }
const Settings = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="M12 1v6m0 6v6m11-7h-6m-6 0H1m9-4a3 3 0 1 0 0-6 3 3 0 0 0 0 6Z"/></svg>' }
const Palette = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="13.5" cy="6.5" r=".5"/><circle cx="17.5" cy="10.5" r=".5"/><circle cx="8.5" cy="7.5" r=".5"/><circle cx="6.5" cy="12.5" r=".5"/><path d="M12 2C6.5 2 2 6.5 2 12s4.5 10 10 10c.926 0 1.648-.746 1.648-1.688 0-.437-.18-.835-.437-1.125-.29-.289-.438-.652-.438-1.125a1.64 1.64 0 0 1 1.668-1.668h1.996c3.051 0 5.555-2.503 5.555-5.554C21.965 6.012 17.455 2 12 2z"/></svg>' }
const Grid3x3 = { template: '<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><rect x="3" y="3" width="6" height="6"/><rect x="15" y="3" width="6" height="6"/><rect x="3" y="15" width="6" height="6"/><rect x="15" y="15" width="6" height="6"/></svg>' }

// Componentes Iluria
import MyButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import Textarea from '@/components/Textarea.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close'])

// Tabs configuration
const tabs = [
  { id: 'slides', label: 'Slides' },
  { id: 'content', label: 'Conteúdo' },
  { id: 'settings', label: 'Configurações' }
]

// Reactive state
const activeTab = ref('slides')
const currentSlideIndex = ref(0)

const slides = ref([])
const settings = ref({
  autoplay: true,
  interval: 5000,
  showArrows: true,
  showDots: true,
  transition: 'slide'
})

const design = ref({
  height: 400,
  borderRadius: 12,
  titleColor: '#ffffff',
  descriptionColor: '#f0f0f0',
  titleFontSize: 48,
  descriptionFontSize: 18
})

// Computed
const currentSlide = computed(() => {
  return slides.value[currentSlideIndex.value] || null
})

// Methods
const setActiveTab = (tabId) => {
  activeTab.value = tabId
}

const selectSlide = (index) => {
  currentSlideIndex.value = index
  activeTab.value = 'content'
  
  // Navegar automaticamente para o slide selecionado no carrossel visual
  navigateToSlide(index)
}

const addNewSlide = () => {
  const newSlide = {
    id: `slide-${Date.now()}-${Math.random()}`,
    title: `Slide ${slides.value.length + 1}`,
    description: 'Descrição do novo slide',
    imageUrl: '',
    active: true,
    // Configurações específicas do slide - inicializar com configurações globais
    titleColor: design.value.titleColor,
    descriptionColor: design.value.descriptionColor,
    overlayColor: design.value.overlayColor,
    overlayOpacity: design.value.overlayOpacity,
    overlayEnabled: undefined, // undefined = usar configuração global
    titleFontSize: design.value.titleFontSize,
    descriptionFontSize: design.value.descriptionFontSize
  }
  
  slides.value.push(newSlide)
  currentSlideIndex.value = slides.value.length - 1
  activeTab.value = 'content'
  
  saveData()
}

const deleteSlide = (index) => {
  if (slides.value.length <= 1) return
  
  slides.value.splice(index, 1)
  
  if (currentSlideIndex.value >= slides.value.length) {
    currentSlideIndex.value--
  } else if (currentSlideIndex.value > index) {
    currentSlideIndex.value--
  }
  
  saveData()
}

const moveSlide = (index, direction) => {
  const newIndex = index + direction
  if (newIndex < 0 || newIndex >= slides.value.length) return
  
  const slide = slides.value.splice(index, 1)[0]
  slides.value.splice(newIndex, 0, slide)
  
  if (currentSlideIndex.value === index) {
    currentSlideIndex.value = newIndex
  } else if (currentSlideIndex.value === newIndex) {
    currentSlideIndex.value = index
  }
  
  saveData()
}

const selectImage = async () => {
  try {
    // Abre um input de arquivo
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    
    input.onchange = async (e) => {
      const file = e.target.files[0]
      if (file) {
        const reader = new FileReader()
        reader.onload = () => {
          // Atualiza o slide atual com a nova imagem como base64
          if (currentSlide.value) {
            currentSlide.value.imageUrl = reader.result
            updateSlideData()
            
            // Atualiza o visual do slide imediatamente
            const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
            if (targetElement) {
              const slides = targetElement.querySelectorAll('.carousel-slide')
              const currentIndex = currentSlideIndex.value
              
              if (slides[currentIndex]) {
                slides[currentIndex].style.backgroundImage = `url('${reader.result}')`
                slides[currentIndex].style.backgroundSize = 'cover'
                slides[currentIndex].style.backgroundPosition = 'center'
                slides[currentIndex].style.backgroundRepeat = 'no-repeat'
              }
            }
          }
        }
        reader.readAsDataURL(file)
      }
    }
    
    input.click()
  } catch (error) {
    console.error('Erro ao selecionar imagem:', error)
  }
}

const selectBackgroundImage = async () => {
  try {
    // Abre um input de arquivo para imagem de fundo
    const input = document.createElement('input')
    input.type = 'file'
    input.accept = 'image/*'
    
    input.onchange = async (e) => {
      const file = e.target.files[0]
      if (file) {
        // Cria uma URL temporária para a imagem
        const imageUrl = URL.createObjectURL(file)
        
        // Atualiza o slide atual com a nova imagem de fundo
        if (currentSlide.value) {
          currentSlide.value.backgroundUrl = imageUrl
          updateSlideData()
        }
      }
    }
    
    input.click()
  } catch (error) {
    console.error('Erro ao selecionar imagem de fundo:', error)
  }
}

const removeImage = () => {
  if (currentSlide.value) {
    currentSlide.value.imageUrl = ''
    updateSlideData()
  }
}

const removeBackgroundImage = () => {
  if (currentSlide.value) {
    currentSlide.value.backgroundUrl = ''
    updateSlideData()
  }
}

const updateSlideData = () => {
  // Atualiza o visual do slide quando a URL da imagem mudar
  if (currentSlide.value) {
    const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
    if (targetElement) {
      const slides = targetElement.querySelectorAll('.carousel-slide')
      const currentIndex = currentSlideIndex.value
      
      if (slides[currentIndex]) {
        if (currentSlide.value.imageUrl) {
          // Se a URL começa com http ou https, carrega a imagem primeiro
          if (currentSlide.value.imageUrl.startsWith('http')) {
            const img = new Image()
            img.onload = () => {
              slides[currentIndex].style.backgroundImage = `url('${currentSlide.value.imageUrl}')`
              slides[currentIndex].style.backgroundSize = 'cover'
              slides[currentIndex].style.backgroundPosition = 'center'
              slides[currentIndex].style.backgroundRepeat = 'no-repeat'
            }
            img.src = currentSlide.value.imageUrl
          } else {
            // Se for base64 ou URL local, aplica diretamente
            slides[currentIndex].style.backgroundImage = `url('${currentSlide.value.imageUrl}')`
            slides[currentIndex].style.backgroundSize = 'cover'
            slides[currentIndex].style.backgroundPosition = 'center'
            slides[currentIndex].style.backgroundRepeat = 'no-repeat'
          }
        } else {
          // Se não houver imagem, volta para o gradiente padrão
          const gradientColor = getGradientColors(currentIndex)
          slides[currentIndex].style.background = `linear-gradient(135deg, ${gradientColor})`
          slides[currentIndex].style.backgroundImage = 'none'
        }
      }
    }
  }
  saveData()
}

const updateSettings = () => {
  // Forçar atualização visual imediata
  const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
  if (targetElement) {
    updateCarouselVisual(targetElement)
  }
  
  saveData()
}

const updateDesign = () => {
  // Forçar atualização visual imediata
  const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
  if (targetElement) {
    updateCarouselVisual(targetElement)
  }
  
  saveData()
}

const resetSlideDesign = () => {
  if (currentSlide.value) {
    // Resetar para usar configurações globais
    currentSlide.value.titleColor = design.value.titleColor
    currentSlide.value.descriptionColor = design.value.descriptionColor
    currentSlide.value.overlayColor = design.value.overlayColor
    currentSlide.value.overlayOpacity = design.value.overlayOpacity
    currentSlide.value.overlayEnabled = undefined // Resetar para usar configuração global
    currentSlide.value.titleFontSize = design.value.titleFontSize
    currentSlide.value.descriptionFontSize = design.value.descriptionFontSize
    
    updateSlideData()
  }
}

const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

// Função para navegar para um slide específico
const navigateToSlide = (slideIndex) => {
  try {
    const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
    if (!targetElement) return
    
    const track = targetElement.querySelector('.carousel-track')
    const indicators = targetElement.querySelectorAll('.carousel-indicator')
    
    if (track && indicators.length > 0) {
      // Calcular a transformação necessária
      const activeSlides = slides.value.filter(slide => slide.active)
      const slideWidth = 100 / activeSlides.length
      const translateX = -(slideIndex * slideWidth)
      
      // Aplicar transformação no track
      track.style.transform = `translateX(${translateX}%)`
      
      // Atualizar indicadores ativos
      indicators.forEach((indicator, index) => {
        if (index === slideIndex) {
          indicator.classList.add('active')
          indicator.style.opacity = '1'
        } else {
          indicator.classList.remove('active')
          indicator.style.opacity = '0.5'
        }
      })
      
      // Atualizar slides ativos
      const slideElements = targetElement.querySelectorAll('.carousel-slide')
      slideElements.forEach((slide, index) => {
        if (index === slideIndex) {
          slide.classList.add('active')
        } else {
          slide.classList.remove('active')
        }
      })
      
    }
  } catch (error) {
    console.error('Erro ao navegar para slide:', error)
  }
}



// Data persistence
const loadData = () => {
  if (!props.element) return
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'carousel') {
      targetElement = props.element.closest('[data-component="carousel"]')
    }
    
    if (!targetElement) return
    
    // Load slides - cada slide mantém suas próprias configurações
    const slidesData = targetElement.getAttribute('data-slides')
    if (slidesData) {
      try {
        const parsedSlides = JSON.parse(slidesData)
        // Garantir que cada slide tenha todas as propriedades necessárias
        slides.value = parsedSlides.map(slide => ({
          id: slide.id || `slide-${Date.now()}-${Math.random()}`,
          title: slide.title || 'Novo Slide',
          description: slide.description || 'Descrição do slide',
          imageUrl: slide.imageUrl || '',
          active: slide.active !== undefined ? slide.active : true,
          // Configurações específicas do slide
          titleColor: slide.titleColor || '#ffffff',
          descriptionColor: slide.descriptionColor || '#f0f0f0',
          overlayColor: slide.overlayColor || '#000000',
          overlayOpacity: slide.overlayOpacity !== undefined ? slide.overlayOpacity : 0.3,
          overlayEnabled: slide.overlayEnabled !== undefined ? slide.overlayEnabled : undefined,
          titleFontSize: slide.titleFontSize || 48,
          descriptionFontSize: slide.descriptionFontSize || 18
        }))
      } catch (error) {
        slides.value = getDefaultSlides()
      }
    } else {
      slides.value = getDefaultSlides()
    }

    // Load settings
    settings.value = {
      autoplay: targetElement.getAttribute('data-autoplay') === 'true',
      interval: parseInt(targetElement.getAttribute('data-interval')) || 5000,
      showArrows: targetElement.getAttribute('data-show-arrows') !== 'false',
      showDots: targetElement.getAttribute('data-show-dots') !== 'false',
      transition: targetElement.getAttribute('data-transition') || 'slide'
    }

    // Load design (configurações globais)
    design.value = {
      height: parseInt(targetElement.getAttribute('data-height')) || 400,
      borderRadius: parseInt(targetElement.getAttribute('data-border-radius')) || 12,
      titleColor: targetElement.getAttribute('data-title-color') || '#ffffff',
      descriptionColor: targetElement.getAttribute('data-description-color') || '#f0f0f0',
      overlayColor: targetElement.getAttribute('data-overlay-color') || '#000000',
      overlayOpacity: parseFloat(targetElement.getAttribute('data-overlay-opacity')) || 0.3,
      overlayEnabled: targetElement.getAttribute('data-overlay-enabled') !== 'false',
      titleFontSize: parseInt(targetElement.getAttribute('data-title-font-size')) || 48,
      descriptionFontSize: parseInt(targetElement.getAttribute('data-description-font-size')) || 18
    }
    
  } catch (error) {
    console.error('Erro ao carregar dados:', error)
  }
}

const saveData = async () => {
  if (!props.element) return
  
  await nextTick()
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'carousel') {
      targetElement = props.element.closest('[data-component="carousel"]')
    }
    
    if (!targetElement) return
    
    // Save all data as attributes - mantendo dados específicos de cada slide
    targetElement.setAttribute('data-slides', JSON.stringify(slides.value))
    targetElement.setAttribute('data-autoplay', settings.value.autoplay.toString())
    targetElement.setAttribute('data-interval', settings.value.interval.toString())
    targetElement.setAttribute('data-show-arrows', settings.value.showArrows.toString())
    targetElement.setAttribute('data-show-dots', settings.value.showDots.toString())
    targetElement.setAttribute('data-transition', settings.value.transition)
    targetElement.setAttribute('data-height', design.value.height.toString())
    targetElement.setAttribute('data-border-radius', design.value.borderRadius.toString())
    
    // Design global (fallback para slides que não têm configurações específicas)
    targetElement.setAttribute('data-title-color', design.value.titleColor)
    targetElement.setAttribute('data-description-color', design.value.descriptionColor)
    targetElement.setAttribute('data-overlay-color', design.value.overlayColor)
    targetElement.setAttribute('data-overlay-opacity', design.value.overlayOpacity.toString())
    targetElement.setAttribute('data-overlay-enabled', design.value.overlayEnabled.toString())
    targetElement.setAttribute('data-title-font-size', design.value.titleFontSize.toString())
    targetElement.setAttribute('data-description-font-size', design.value.descriptionFontSize.toString())
    
    // Update visual immediately
    updateCarouselVisual(targetElement)
    
    // Emit change event
    emit('data-changed', {
      slides: slides.value,
      settings: settings.value,
      design: design.value
    })
    
  } catch (error) {
    console.error('Erro ao salvar dados:', error)
  }
}

const updateCarouselVisual = (targetElement) => {
  if (!targetElement) {
    return
  }
  
  try {
    // Update carousel container styles
    targetElement.style.height = design.value.height + 'px'
    targetElement.style.borderRadius = design.value.borderRadius + 'px'
    
    const container = targetElement.querySelector('.carousel-container')
    if (container) {
      container.style.height = design.value.height + 'px'
    }

    // Atualiza os botões de navegação
    const updateNavigationButtons = () => {
      const prevBtn = targetElement.querySelector('.carousel-prev')
      const nextBtn = targetElement.querySelector('.carousel-next')
      const currentSlide = targetElement.querySelector('.carousel-slide.active')
      
      if (prevBtn && nextBtn && currentSlide) {
        // Determina se o fundo é claro ou escuro
        const backgroundColor = window.getComputedStyle(currentSlide).backgroundColor
        const rgb = backgroundColor.match(/\d+/g)
        const brightness = rgb ? (parseInt(rgb[0]) * 299 + parseInt(rgb[1]) * 587 + parseInt(rgb[2]) * 114) / 1000 : 128

        // Ajusta as cores dos botões baseado no brilho do fundo
        const buttonStyle = brightness > 128 ? {
          background: 'rgba(0, 0, 0, 0.6)',
          color: '#ffffff',
          border: '2px solid rgba(255, 255, 255, 0.3)'
        } : {
          background: 'rgba(255, 255, 255, 0.6)',
          color: '#000000',
          border: '2px solid rgba(0, 0, 0, 0.3)'
        }

        ;[prevBtn, nextBtn].forEach(btn => {
          btn.style.background = buttonStyle.background
          btn.style.color = buttonStyle.color
          btn.style.border = buttonStyle.border
          btn.style.backdropFilter = 'blur(4px)'
          
          // Atualiza o ícone SVG dentro do botão
          const svg = btn.querySelector('svg')
          if (svg) {
            svg.style.stroke = buttonStyle.color
          }

          // Adiciona hover effects
          btn.onmouseenter = () => {
            btn.style.background = brightness > 128 ? 
              'rgba(0, 0, 0, 0.8)' : 
              'rgba(255, 255, 255, 0.8)'
            btn.style.transform = 'translateY(-50%) scale(1.1)'
          }
          
          btn.onmouseleave = () => {
            btn.style.background = buttonStyle.background
            btn.style.transform = 'translateY(-50%) scale(1)'
          }
        })
      }
    }

    // Update carousel track
    const track = targetElement.querySelector('.carousel-track')
    if (track) {
      const activeSlides = slides.value.filter(slide => slide.active)
      track.style.width = (activeSlides.length * 100) + '%'
      
      // Chama a atualização dos botões após atualizar os slides
      updateNavigationButtons()
      
      // Generate slides HTML - usando configurações específicas de cada slide
      const slidesHTML = activeSlides.map((slide, index) => {
        const slideWidth = 100 / activeSlides.length
        
        // Usar configurações específicas do slide ou fallback para design global
        const slideConfig = {
          titleColor: slide.titleColor || design.value.titleColor,
          descriptionColor: slide.descriptionColor || design.value.descriptionColor,
          overlayColor: slide.overlayColor || design.value.overlayColor,
          overlayOpacity: slide.overlayOpacity !== undefined ? slide.overlayOpacity : design.value.overlayOpacity,
          titleFontSize: slide.titleFontSize || design.value.titleFontSize,
          descriptionFontSize: slide.descriptionFontSize || design.value.descriptionFontSize
        }
        
        let backgroundContent = ''
        let overlayContent = ''
        
        // Determinar se deve mostrar overlay
        // Se o slide tem configuração específica de overlay, usar ela
        // Senão, usar a configuração global
        const slideOverlayEnabled = slide.overlayEnabled !== undefined ? slide.overlayEnabled : design.value.overlayEnabled
        const shouldShowOverlay = slideOverlayEnabled && slideConfig.overlayOpacity > 0
        
        if (slide.imageUrl) {
          // É uma imagem normal
          backgroundContent = `background-image: url('${slide.imageUrl}'); background-size: cover; background-position: center;`
          overlayContent = shouldShowOverlay ? `<div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(${hexToRgb(slideConfig.overlayColor)}, ${slideConfig.overlayOpacity}); z-index: 1;"></div>` : ''
        } else {
          // Gradiente padrão
          backgroundContent = `background: linear-gradient(135deg, ${getGradientColors(index)});`
          // Também aplicar overlay no gradiente se configurado
          overlayContent = shouldShowOverlay ? `<div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: rgba(${hexToRgb(slideConfig.overlayColor)}, ${slideConfig.overlayOpacity}); z-index: 1;"></div>` : ''
        }
        
        return `
          <div class="carousel-slide ${index === currentSlideIndex.value ? 'active' : ''}" 
               style="flex: 0 0 ${slideWidth}%; height: 100%; position: relative; ${slide.imageUrl ? '' : backgroundContent}">
            ${overlayContent}
            <div class="slide-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; z-index: 3; max-width: 90%; padding: 0 20px;">
              <h3 style="font-size: ${slideConfig.titleFontSize}px; margin-bottom: 1rem; font-weight: bold; color: ${slideConfig.titleColor}; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); line-height: 1.2;">${slide.title}</h3>
              <p style="font-size: ${slideConfig.descriptionFontSize}px; opacity: 0.95; margin-bottom: 1.5rem; color: ${slideConfig.descriptionColor}; text-shadow: 1px 1px 2px rgba(0,0,0,0.7); line-height: 1.4;">${slide.description}</p>
            </div>
          </div>
        `
      }).join('')
      
      track.innerHTML = slidesHTML
    }
    
    // Update navigation with better positioning and visibility
    const navigation = targetElement.querySelector('.carousel-navigation')
    if (navigation) {
      // Primeiro definir o container da navegação
      navigation.style.cssText = `
        position: absolute; 
        top: 0; 
        left: 0; 
        width: 100%; 
        height: 100%; 
        pointer-events: none;
        z-index: 5;
        display: ${settings.value.showArrows ? 'block' : 'none'};
      `
      
      // Melhorar posicionamento das setas para não ficarem cortadas
      const prevBtn = navigation.querySelector('.carousel-prev')
      const nextBtn = navigation.querySelector('.carousel-next')
      
      if (prevBtn && nextBtn && settings.value.showArrows) {
        // Setas com melhor posicionamento
        prevBtn.style.cssText = `
          background: rgba(255,255,255,0.95); 
          border: none; 
          width: 48px; 
          height: 48px; 
          border-radius: 50%; 
          cursor: pointer; 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          transition: all 0.3s ease; 
          pointer-events: auto; 
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          position: absolute;
          left: 15px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 10;
        `
        
        nextBtn.style.cssText = `
          background: rgba(255,255,255,0.95); 
          border: none; 
          width: 48px; 
          height: 48px; 
          border-radius: 50%; 
          cursor: pointer; 
          display: flex; 
          align-items: center; 
          justify-content: center; 
          transition: all 0.3s ease; 
          pointer-events: auto; 
          box-shadow: 0 4px 12px rgba(0,0,0,0.2);
          position: absolute;
          right: 15px;
          top: 50%;
          transform: translateY(-50%);
          z-index: 10;
        `
        
        // Remover event listeners anteriores para evitar duplicatas
        prevBtn.removeEventListener('mouseenter', prevBtn._hoverIn)
        prevBtn.removeEventListener('mouseleave', prevBtn._hoverOut)
        nextBtn.removeEventListener('mouseenter', nextBtn._hoverIn)
        nextBtn.removeEventListener('mouseleave', nextBtn._hoverOut)
        
        // Adicionar hover effects
        prevBtn._hoverIn = () => {
          prevBtn.style.background = 'rgba(255,255,255,1)'
          prevBtn.style.transform = 'translateY(-50%) scale(1.1)'
        }
        
        prevBtn._hoverOut = () => {
          prevBtn.style.background = 'rgba(255,255,255,0.95)'
          prevBtn.style.transform = 'translateY(-50%) scale(1)'
        }
        
        nextBtn._hoverIn = () => {
          nextBtn.style.background = 'rgba(255,255,255,1)'
          nextBtn.style.transform = 'translateY(-50%) scale(1.1)'
        }
        
        nextBtn._hoverOut = () => {
          nextBtn.style.background = 'rgba(255,255,255,0.95)'
          nextBtn.style.transform = 'translateY(-50%) scale(1)'
        }
        
        prevBtn.addEventListener('mouseenter', prevBtn._hoverIn)
        prevBtn.addEventListener('mouseleave', prevBtn._hoverOut)
        nextBtn.addEventListener('mouseenter', nextBtn._hoverIn)
        nextBtn.addEventListener('mouseleave', nextBtn._hoverOut)
      }
    }
    
    const indicators = targetElement.querySelector('.carousel-indicators')
    if (indicators) {
      // Novo design dos indicadores - mais moderno e elegante
      indicators.style.cssText = `
        position: absolute; 
        bottom: 16px; 
        left: 50%; 
        transform: translateX(-50%); 
        display: ${settings.value.showDots ? 'flex' : 'none'}; 
        align-items: center;
        justify-content: center;
        gap: 0px; 
        background: rgba(0,0,0,0.35); 
        padding: 6px 12px; 
        border-radius: 25px;
        backdrop-filter: blur(8px);
        border: 1px solid rgba(255,255,255,0.1);
        box-shadow: 0 4px 16px rgba(0,0,0,0.3);
        min-height: 16px;
      `
      
      if (settings.value.showDots) {
        // Update indicators with new beautiful rectangular design
        const activeSlides = slides.value.filter(slide => slide.active)
        const indicatorsHTML = activeSlides.map((_, index) => {
          const isActive = index === currentSlideIndex.value
          return `<div class="carousel-indicator-wrapper" style="position: relative; padding: 0px; display: flex; align-items: center; justify-content: center; height: 16px;">
                     <button class="carousel-indicator ${isActive ? 'active' : ''}" 
                             data-slide="${index}" 
                             onclick="window.navigateCarouselToSlide && window.navigateCarouselToSlide(${index})"
                             style="
                               width: ${isActive ? '12px' : '4px'}; 
                               height: 1.5px; 
                               border-radius: 0.75px; 
                               border: none; 
                               background: ${isActive ? 'rgba(255,255,255,0.95)' : 'rgba(255,255,255,0.5)'};
                               cursor: pointer; 
                               transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); 
                               margin: 0 2px; 
                               box-shadow: 0 1px 4px rgba(0,0,0,0.3);
                               position: relative;
                               overflow: hidden;
                             ">
                        ${isActive ? '<div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, rgba(255,255,255,0.4), transparent, rgba(255,255,255,0.4)); border-radius: 0.75px;"></div>' : ''}
                     </button>
                   </div>`
        }).join('')
        
        indicators.innerHTML = indicatorsHTML
      }
      
      // Sistema de navegação universal - funciona tanto no editor quanto na visualização
      const carouselId = targetElement.id || `carousel-${Date.now()}`
      targetElement.id = carouselId
      
      if (settings.value.showDots) {
        // Atualizar indicadores para usar funções específicas
        const newIndicators = indicators.querySelectorAll('.carousel-indicator')
        newIndicators.forEach((indicator, index) => {
          indicator.setAttribute('onclick', `window.navigateCarouselToSlide_${carouselId} && window.navigateCarouselToSlide_${carouselId}(${index})`)
          
          // Hover effects mais sofisticados para indicadores retangulares
          indicator.addEventListener('mouseenter', () => {
            if (index !== currentSlideIndex.value) {
              indicator.style.background = 'rgba(255,255,255,0.8)'
              indicator.style.transform = 'scaleY(1.8)'
              indicator.style.boxShadow = '0 2px 8px rgba(0,0,0,0.5)'
            }
          })
          
          indicator.addEventListener('mouseleave', () => {
            if (index !== currentSlideIndex.value) {
              indicator.style.background = 'rgba(255,255,255,0.5)'
              indicator.style.transform = 'scaleY(1)'
              indicator.style.boxShadow = '0 1px 4px rgba(0,0,0,0.3)'
            }
          })
        })
      }
      
      // Criar funções globais específicas para este carrossel
      window[`navigateCarouselToSlide_${carouselId}`] = (slideIndex) => {
        navigateToSlide(slideIndex)
        currentSlideIndex.value = slideIndex
        
        // Atualizar indicadores retangulares com animação suave (só se estiverem visíveis)
        if (settings.value.showDots) {
          const newIndicators = indicators.querySelectorAll('.carousel-indicator')
          newIndicators.forEach((indicator, index) => {
            if (index === slideIndex) {
              indicator.style.width = '12px'
              indicator.style.height = '1.5px'
            } else {
              indicator.style.width = '4px'
              indicator.style.height = '1.5px'
            }
          })
        }
      }
      
      window[`navigateCarouselPrev_${carouselId}`] = () => {
        const activeSlides = slides.value.filter(slide => slide.active)
        const currentIndex = currentSlideIndex.value
        const newIndex = currentIndex > 0 ? currentIndex - 1 : activeSlides.length - 1
        
        window[`navigateCarouselToSlide_${carouselId}`](newIndex)
      }
      
      window[`navigateCarouselNext_${carouselId}`] = () => {
        const activeSlides = slides.value.filter(slide => slide.active)
        const currentIndex = currentSlideIndex.value
        const newIndex = currentIndex < activeSlides.length - 1 ? currentIndex + 1 : 0
        
        window[`navigateCarouselToSlide_${carouselId}`](newIndex)
      }
      
      // Atualizar os botões de navegação para usar as funções específicas (só se estiverem visíveis)
      if (settings.value.showArrows) {
        const prevBtn = navigation?.querySelector('.carousel-prev')
        const nextBtn = navigation?.querySelector('.carousel-next')
        
        if (prevBtn) {
          prevBtn.setAttribute('onclick', `window.navigateCarouselPrev_${carouselId} && window.navigateCarouselPrev_${carouselId}()`)
        }
        if (nextBtn) {
          nextBtn.setAttribute('onclick', `window.navigateCarouselNext_${carouselId} && window.navigateCarouselNext_${carouselId}()`)
        }
      }
      
      // Criar também as funções globais gerais (fallback)
      window.navigateCarouselToSlide = window[`navigateCarouselToSlide_${carouselId}`]
      window.navigateCarouselPrev = window[`navigateCarouselPrev_${carouselId}`]
      window.navigateCarouselNext = window[`navigateCarouselNext_${carouselId}`]
    }
    
  } catch (error) {
    console.error('Erro ao atualizar visual do carrossel:', error)
  }
}

const getDefaultSlides = () => [
  {
    id: 'slide-1',
    title: 'Slide 1',
    description: 'Descrição do primeiro slide',
    imageUrl: '',
    active: true,
    titleColor: '#ffffff',
    descriptionColor: '#f0f0f0',
    titleFontSize: 48,
    descriptionFontSize: 18
  },
  {
    id: 'slide-2',
    title: 'Slide 2',
    description: 'Descrição do segundo slide',
    imageUrl: '',
    active: true,
    titleColor: '#ffffff',
    descriptionColor: '#f0f0f0',
    titleFontSize: 48,
    descriptionFontSize: 18
  }
]

const getGradientColors = (index) => {
  const gradients = [
    '#667eea 0%, #764ba2 100%',
    '#f093fb 0%, #f5576c 100%', 
    '#4facfe 0%, #00f2fe 100%',
    '#43e97b 0%, #38f9d7 100%',
    '#fa709a 0%, #fee140 100%'
  ]
  return gradients[index % gradients.length]
}

const hexToRgb = (hex) => {
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? 
    `${parseInt(result[1], 16)}, ${parseInt(result[2], 16)}, ${parseInt(result[3], 16)}` :
    '0, 0, 0'
}

// Sistema de navegação global que funciona tanto no editor quanto na visualização
const setupCarouselNavigation = () => {
  if (!props.element) return
  
  const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
  if (!targetElement) return
  
  // Remover listeners anteriores para evitar duplicatas
  targetElement.removeEventListener('carousel:prev', handlePrevious)
  targetElement.removeEventListener('carousel:next', handleNext)
  targetElement.removeEventListener('carousel:goto', handleGoto)
  
  // Adicionar novos listeners
  targetElement.addEventListener('carousel:prev', handlePrevious)
  targetElement.addEventListener('carousel:next', handleNext)
  targetElement.addEventListener('carousel:goto', handleGoto)
}

const handlePrevious = () => {
  const activeSlides = slides.value.filter(slide => slide.active)
  const currentIndex = currentSlideIndex.value
  const newIndex = currentIndex > 0 ? currentIndex - 1 : activeSlides.length - 1
  
  navigateToSlide(newIndex)
  currentSlideIndex.value = newIndex
  updateIndicatorsVisual()
}

const handleNext = () => {
  const activeSlides = slides.value.filter(slide => slide.active)
  const currentIndex = currentSlideIndex.value
  const newIndex = currentIndex < activeSlides.length - 1 ? currentIndex + 1 : 0
  
  navigateToSlide(newIndex)
  currentSlideIndex.value = newIndex
  updateIndicatorsVisual()
}

const handleGoto = (event) => {
  const slideIndex = event.detail?.slide
  if (slideIndex !== undefined && slideIndex >= 0 && slideIndex < slides.value.filter(slide => slide.active).length) {
    navigateToSlide(slideIndex)
    currentSlideIndex.value = slideIndex
    updateIndicatorsVisual()
  }
}

const updateIndicatorsVisual = () => {
  const targetElement = props.element?.closest('[data-component="carousel"]') || props.element
  if (!targetElement) return
  
  const indicators = targetElement.querySelector('.carousel-indicators')
  if (!indicators || !settings.value.showDots) return
  
  const indicatorButtons = indicators.querySelectorAll('.carousel-indicator')
  indicatorButtons.forEach((indicator, index) => {
    if (index === currentSlideIndex.value) {
      indicator.style.width = '12px'
      indicator.style.height = '1.5px'
    } else {
      indicator.style.width = '4px'
      indicator.style.height = '1.5px'
    }
  })
}

// Lifecycle
onMounted(() => {
  loadData()
  setupCarouselNavigation()
})

// Watch para reconfigurar navegação quando elemento mudar
watch(() => props.element, () => {
  setupCarouselNavigation()
}, { immediate: false })
</script>

<style scoped>
.carousel-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--iluria-color-container-bg);
}

/* Header */
.editor-header {
  padding: 20px 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.editor-title {
  margin: 0 0 4px 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.editor-subtitle {
  margin: 0;
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
}

/* Tabs */
.tabs-container {
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.tabs-nav {
  display: flex;
  padding: 0 24px;
}

.tab-btn {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border: none;
  background: none;
  cursor: pointer;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
}

.tab-btn:hover {
  color: var(--iluria-color-text-primary);
}

.tab-btn.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
}

.tab-icon {
  width: 16px;
  height: 16px;
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
  background: var(--iluria-color-container-bg);
}

.tab-panel {
  padding: 24px;
}

/* Section Headers */
.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
}

.section-header h3 {
  margin: 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.add-btn {
  display: flex;
  align-items: center;
  gap: 8px;
}

.btn-icon {
  width: 16px;
  height: 16px;
}

/* Slides Grid */
.slides-grid {
  display: grid;
  gap: 16px;
}

.slide-card {
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
  cursor: pointer;
  transition: all 0.2s;
  overflow: hidden;
}

.slide-card:hover {
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-sm);
}

.slide-card.active {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 1px var(--iluria-color-primary);
}

.slide-card.inactive {
  opacity: 0.6;
}

.slide-preview {
  position: relative;
  height: 120px;
  overflow: hidden;
}

.slide-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-bg-muted);
  color: var(--iluria-color-text-muted);
}

.placeholder-icon {
  width: 32px;
  height: 32px;
}

.slide-content {
  text-align: center;
  color: var(--iluria-color-text-primary);
}

.slide-content h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
}

.slide-content p {
  margin: 0;
  font-size: 12px;
  opacity: 0.9;
}

.slide-controls {
  padding: 12px 16px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-top: 1px solid var(--iluria-color-border);
}

.slide-status .status-badge {
  padding: 2px 8px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 500;
  background: var(--iluria-color-error-light);
  color: var(--iluria-color-error);
}

.status-badge.active {
  background: var(--iluria-color-success-light);
  color: var(--iluria-color-success);
}

.slide-actions {
  display: flex;
  gap: 4px;
}

.action-btn {
  padding: 4px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  background: var(--iluria-color-container-bg);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-btn:hover:not(:disabled) {
  border-color: var(--iluria-color-border-hover);
  background: var(--iluria-color-bg-hover);
}

.action-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.action-btn.danger:hover:not(:disabled) {
  border-color: var(--iluria-color-error);
  background: var(--iluria-color-error-light);
  color: var(--iluria-color-error);
}

.action-icon {
  width: 14px;
  height: 14px;
}

/* Empty State */
.empty-state,
.no-slide-selected {
  text-align: center;
  padding: 48px 24px;
  color: var(--iluria-color-text-secondary);
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin: 0 auto 16px;
  color: var(--iluria-color-text-muted);
}

.empty-state h4,
.no-slide-selected h4 {
  margin: 0 0 8px 0;
  font-size: 16px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.empty-state p,
.no-slide-selected p {
  margin: 0;
  font-size: 14px;
}

/* Form Sections */
.form-sections {
  display: flex;
  flex-direction: column;
  gap: 32px;
}

.form-section {
  padding: 24px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
}

.form-section h4 {
  margin: 0 0 20px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.form-group {
  margin-bottom: 20px;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* Image Input */
.image-input-group {
  display: flex;
  gap: 8px;
}

.image-preview {
  margin-top: 12px;
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  border: 1px solid var(--iluria-color-border);
}

.image-preview img {
  display: block;
  max-width: 200px;
  max-height: 120px;
  object-fit: cover;
}

.image-preview-normal {
  display: inline-block;
  position: relative;
}

.remove-image-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 50%;
  background: var(--iluria-color-error-light);
  color: var(--iluria-color-error);
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.remove-image-btn:hover {
  background: var(--iluria-color-error);
  color: white;
  transform: scale(1.1);
}

.remove-icon {
  width: 12px;
  height: 12px;
}

/* Range Inputs */
.range-input {
  width: 100%;
  margin: 8px 0;
  -webkit-appearance: none;
  height: 6px;
  border-radius: 3px;
  background: var(--iluria-color-bg-muted);
  outline: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--iluria-color-primary);
  cursor: pointer;
  border: 2px solid var(--iluria-color-container-bg);
  box-shadow: var(--iluria-shadow-sm);
}

.range-input::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: var(--iluria-color-primary);
  cursor: pointer;
  border: 2px solid var(--iluria-color-container-bg);
  box-shadow: var(--iluria-shadow-sm);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin-top: 4px;
}

/* Select Input */
.select-input {
  width: 100%;
  padding: 10px 12px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  background: var(--iluria-color-container-bg);
  font-size: 14px;
  cursor: pointer;
  transition: border-color 0.2s;
  color: var(--iluria-color-text-primary);
}

.select-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-light);
}

/* Color Grid */
.color-grid {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 16px;
}

@media (max-width: 600px) {
  .color-grid {
    grid-template-columns: 1fr;
  }
}

.color-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

/* Carousel Indicators - Estilos retangulares ultra minimalistas para melhor UX */
:deep(.carousel-indicator) {
  height: 1.5px !important;
  border-radius: 0.75px !important;
}

:deep(.carousel-indicator.active) {
  width: 12px !important;
}

:deep(.carousel-indicator:not(.active)) {
  width: 4px !important;
}

/* Carousel Navigation - Melhorias nos botões */
:deep(.carousel-prev:hover),
:deep(.carousel-next:hover) {
  background: rgba(255,255,255,1) !important;
  transform: translateY(-50%) scale(1.1) !important;
  box-shadow: 0 6px 20px rgba(0,0,0,0.3) !important;
}

:deep(.carousel-prev:active),
:deep(.carousel-next:active) {
  transform: translateY(-50%) scale(0.95) !important;
}

/* Carousel Indicators Container */
:deep(.carousel-indicators) {
  transition: all 0.3s ease !important;
}

:deep(.carousel-indicators:hover) {
  background: rgba(0,0,0,0.45) !important;
  backdrop-filter: blur(12px) !important;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-panel {
    padding: 16px;
  }
  
  .form-section {
    padding: 16px;
  }
}
</style> 
