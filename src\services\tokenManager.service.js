import { jwtDecode } from 'jwt-decode';
import axios from 'axios';
import { API_URLS } from '../config/api.config';

class TokenManager {
    #refreshPromises = new Map(); // Prevent multiple refresh attempts
    #sessionTimeouts = new Map(); // Track session timeouts
    #refreshMutex = new Map(); // Mutex for critical refresh operations
    
    // Token refresh thresholds (in minutes before expiration)
    #REFRESH_THRESHOLD_MINUTES = 5;
    #SESSION_TIMEOUT_MINUTES = 240;

    /**
     * Verifica se um token JWT está válido e não expirado
     * @param {string} token - JWT token
     * @returns {Object} { isValid, expiresAt, timeUntilExpiry }
     */
    validateToken(token) {
        if (!token || typeof token !== 'string') {
            return { isValid: false, expiresAt: null, timeUntilExpiry: 0 };
        }

        try {
            const decoded = jwtDecode(token);
            const now = Math.floor(Date.now() / 1000);
            const expiresAt = decoded.exp;
            
            if (!expiresAt) {
                return { isValid: true, expiresAt: null, timeUntilExpiry: Infinity };
            }

            const timeUntilExpiry = expiresAt - now;
            const isValid = timeUntilExpiry > 0;

            return {
                isValid,
                expiresAt: new Date(expiresAt * 1000),
                timeUntilExpiry: timeUntilExpiry * 1000, // Convert to milliseconds
                claims: decoded
            };
        } catch (error) {
            console.error('Token validation error:', error);
            return { isValid: false, expiresAt: null, timeUntilExpiry: 0 };
        }
    }

    /**
     * Verifica se um token precisa ser renovado
     * @param {string} token - JWT token
     * @returns {boolean}
     */
    needsRefresh(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid) return false;
        
        const refreshThresholdMs = this.#REFRESH_THRESHOLD_MINUTES * 60 * 1000;
        return validation.timeUntilExpiry <= refreshThresholdMs;
    }

    /**
     * Acquire mutex lock for token refresh operations
     * @param {string} mutexKey - Unique key for the mutex
     * @returns {Promise<Function>} Release function
     */
    async #acquireMutex(mutexKey) {
        while (this.#refreshMutex.has(mutexKey)) {
            // Wait for the current operation to complete
            await new Promise(resolve => setTimeout(resolve, 10));
        }
        
        // Set mutex
        const releasePromise = new Promise(resolve => {
            this.#refreshMutex.set(mutexKey, resolve);
        });
        
        // Return release function
        return () => {
            const resolve = this.#refreshMutex.get(mutexKey);
            this.#refreshMutex.delete(mutexKey);
            if (resolve) resolve();
        };
    }

    /**
     * Refresh automático do user token
     * @param {string} currentToken - Token atual do usuário
     * @returns {Promise<string|null>} Novo token ou null se falhou
     */
    async refreshUserToken(currentToken) {
        const tokenKey = `user_${currentToken?.substring(0, 10)}`;
        
        // CRITICAL FIX: Implement proper mutex for race condition prevention
        const releaseMutex = await this.#acquireMutex(`refresh_${tokenKey}`);
        
        try {
            // Double-check if refresh is still needed after acquiring mutex
            const validation = this.validateToken(currentToken);
            if (!validation.isValid || !this.needsRefresh(currentToken)) {
                return currentToken; // Token is still valid, no refresh needed
            }
            
            // Prevent multiple concurrent refresh attempts
            if (this.#refreshPromises.has(tokenKey)) {
                return await this.#refreshPromises.get(tokenKey);
            }

            const refreshPromise = this.#performUserTokenRefresh(currentToken);
            this.#refreshPromises.set(tokenKey, refreshPromise);

            try {
                const result = await refreshPromise;
                return result;
            } finally {
                this.#refreshPromises.delete(tokenKey);
            }
        } finally {
            releaseMutex(); // Always release mutex
        }
    }

    /**
     * Executa o refresh do user token usando AuthService
     * @param {string} currentToken 
     * @returns {Promise<string|null>}
     */
    async #performUserTokenRefresh(currentToken) {
        try {
            // Import AuthService dinamicamente para evitar dependência circular
            const { default: authService } = await import('./auth.service');
            
            // Import AuthStore dinamicamente
            const { useAuthStore } = await import('@/stores/auth.store');
            const authStore = useAuthStore();
            
            // Obter refresh token do store
            const refreshToken = authStore.getRefreshToken();
            
            if (!refreshToken) {
                console.error('No refresh token available for refresh');
                return null;
            }
            
            // Usar AuthService para fazer refresh
            const result = await authService.refreshToken(refreshToken);
            
            if (result.success && result.jwt) {
                // Atualizar tokens no store se um novo refresh token foi fornecido
                if (result.refreshToken) {
                    authStore.updateTokens(result.jwt, result.refreshToken);
                } else {
                    // Apenas atualizar access token mantendo refresh token atual
                    authStore.setUserToken(result.jwt);
                }
                
                return result.jwt;
            }
            
            return null;
        } catch (error) {
            // IMPROVED ERROR HANDLING: Handle specific error types
            if (error.message === 'REFRESH_TOKEN_EXPIRED') {
                // Force logout for expired tokens
                authStore.logout();
                return null;
            } else if (error.message === 'NETWORK_ERROR') {
                // Network error - retry could be handled here
                console.warn('Network error during token refresh - user may retry');
                return null;
            } else {
                // Other errors - log and return null
                console.error('User token refresh failed:', error.message);
                return null;
            }
        }
    }

    /**
     * Refresh do store token usando o user token
     * @param {string} storeId - ID da loja
     * @param {string} userToken - Token do usuário válido
     * @returns {Promise<string|null>}
     */
    async refreshStoreToken(storeId, userToken) {
        if (!storeId || !userToken) {
            console.error('Missing storeId or userToken for store refresh');
            return null;
        }

        const tokenKey = `store_${storeId}`;
        
        if (this.#refreshPromises.has(tokenKey)) {
            return await this.#refreshPromises.get(tokenKey);
        }

        const refreshPromise = this.#performStoreTokenRefresh(storeId, userToken);
        this.#refreshPromises.set(tokenKey, refreshPromise);

        try {
            const result = await refreshPromise;
            return result;
        } finally {
            this.#refreshPromises.delete(tokenKey);
        }
    }

    /**
     * Executa o refresh do store token
     * @param {string} storeId 
     * @param {string} userToken 
     * @returns {Promise<string|null>}
     */
    async #performStoreTokenRefresh(storeId, userToken) {
        try {
            const response = await axios.post(`${API_URLS.AUTH_BASE_URL}/authenticate-store`, {
                storeId
            }, {
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': `Bearer ${userToken}`
                }
            });

            return response.data.jwt || response.data.token;
        } catch (error) {
            console.error('Store token refresh failed:', error);
            return null;
        }
    }

    /**
     * Configura timeout automático para uma sessão
     * @param {string} tokenKey - Chave única para o token
     * @param {Function} timeoutCallback - Callback a ser executado no timeout
     */
    setSessionTimeout(tokenKey, timeoutCallback) {
        // Clear existing timeout
        this.clearSessionTimeout(tokenKey);

        // Set new timeout
        const timeoutMs = this.#SESSION_TIMEOUT_MINUTES * 60 * 1000;
        const timeoutId = setTimeout(() => {
            timeoutCallback();
            this.#sessionTimeouts.delete(tokenKey);
        }, timeoutMs);

        this.#sessionTimeouts.set(tokenKey, timeoutId);
    }

    /**
     * Limpa timeout de sessão
     * @param {string} tokenKey 
     */
    clearSessionTimeout(tokenKey) {
        const timeoutId = this.#sessionTimeouts.get(tokenKey);
        if (timeoutId) {
            clearTimeout(timeoutId);
            this.#sessionTimeouts.delete(tokenKey);
        }
    }

    /**
     * Reset do timeout de sessão (chama quando há atividade)
     * @param {string} tokenKey 
     * @param {Function} timeoutCallback 
     */
    resetSessionTimeout(tokenKey, timeoutCallback) {
        this.setSessionTimeout(tokenKey, timeoutCallback);
    }

    /**
     * Limpa todos os timeouts e promises
     */
    cleanup() {
        // Clear all timeouts
        for (const [key, timeoutId] of this.#sessionTimeouts) {
            clearTimeout(timeoutId);
        }
        this.#sessionTimeouts.clear();

        // Clear refresh promises
        this.#refreshPromises.clear();
        
        // CRITICAL FIX: Clear mutex locks to prevent memory leaks
        for (const [key, resolve] of this.#refreshMutex) {
            if (resolve) resolve(); // Release any pending locks
        }
        this.#refreshMutex.clear();
    }

    /**
     * Extrai informações do usuário do token
     * @param {string} token 
     * @returns {Object|null}
     */
    extractUserInfo(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid) return null;

        const claims = validation.claims;
        return {
            email: claims.email || claims.sub,
            name: claims.name || claims.username,
            userId: claims.userId || claims.sub,
            roles: claims.roles || [],
            storeId: claims.storeId || null
        };
    }

    /**
     * Extrai informações de sessão do token
     * @param {string} token 
     * @returns {Object|null}
     */
    extractSessionInfo(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid) return null;

        const claims = validation.claims;
        return {
            sessionId: claims.sessionId || null,
            deviceFingerprint: claims.deviceFingerprint || null,
            deviceInfo: claims.deviceInfo || null,
            browserInfo: claims.browserInfo || null,
            platform: claims.platform || null,
            location: claims.location || null,
            ipAddress: claims.ipAddress || null,
            loginTime: claims.iat ? new Date(claims.iat * 1000) : null,
            expiresAt: claims.exp ? new Date(claims.exp * 1000) : null,
            lastActivity: claims.lastActivity ? new Date(claims.lastActivity * 1000) : null
        };
    }

    /**
     * Extrai informações completas (usuário + sessão) do token
     * @param {string} token 
     * @returns {Object|null}
     */
    extractCompleteTokenInfo(token) {
        const validation = this.validateToken(token);
        if (!validation.isValid) return null;

        const userInfo = this.extractUserInfo(token);
        const sessionInfo = this.extractSessionInfo(token);

        return {
            ...userInfo,
            session: sessionInfo,
            tokenValidation: {
                isValid: validation.isValid,
                expiresAt: validation.expiresAt,
                timeUntilExpiry: validation.timeUntilExpiry
            }
        };
    }

    /**
     * Verifica se o token atual corresponde a uma sessão específica
     * @param {string} token 
     * @param {string} sessionId 
     * @returns {boolean}
     */
    isTokenForSession(token, sessionId) {
        const sessionInfo = this.extractSessionInfo(token);
        return sessionInfo?.sessionId === sessionId;
    }

    /**
     * Verifica se o token atual corresponde ao device fingerprint
     * @param {string} token 
     * @param {string} deviceFingerprint 
     * @returns {boolean}
     */
    isTokenForDevice(token, deviceFingerprint) {
        const sessionInfo = this.extractSessionInfo(token);
        return sessionInfo?.deviceFingerprint === deviceFingerprint;
    }

    /**
     * Verifica se o token tem permissão para uma loja específica
     * @param {string} token 
     * @param {string} storeId 
     * @returns {boolean}
     */
    hasStoreAccess(token, storeId) {
        const userInfo = this.extractUserInfo(token);
        if (!userInfo) return false;

        // Store tokens should have storeId in claims
        if (userInfo.storeId === storeId) return true;

        // User tokens don't have store access by default
        return false;
    }
}

export default new TokenManager();