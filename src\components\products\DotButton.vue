<template>
  <button
    :class="[
      'dot-btn',
      sizeClass,
      {
        'dot-btn--active': active,
        'dot-btn--disabled': disabled
      }
    ]"
    :disabled="disabled"
    @click="handleClick"
    @mousedown="handleMouseDown"
    @dblclick="handleDoubleClick"
  >
    <div class="dot-btn__inner">
      <slot />
    </div>
  </button>
</template>

<script setup>
import { computed, defineProps, defineEmits } from 'vue'

const props = defineProps({
  size: {
    type: String,
    default: 'md', // 'sm', 'md', 'lg'
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  },
  active: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  color: {
    type: String,
    default: 'primary', // 'primary', 'secondary', 'success', 'warning', 'danger'
    validator: (value) => ['primary', 'secondary', 'success', 'warning', 'danger'].includes(value)
  }
})

const emit = defineEmits(['click', 'mousedown', 'dblclick'])

const sizeClass = computed(() => ({
  sm: 'dot-btn--sm',
  md: 'dot-btn--md', 
  lg: 'dot-btn--lg'
})[props.size] || 'dot-btn--md')

const handleClick = (event) => {
  if (!props.disabled) {
    emit('click', event)
  }
}

const handleMouseDown = (event) => {
  if (!props.disabled) {
    emit('mousedown', event)
  }
}

const handleDoubleClick = (event) => {
  if (!props.disabled) {
    emit('dblclick', event)
  }
}
</script>

<style scoped>
.dot-btn {
  border-radius: 50%;
  border: 2px solid transparent;
  background: #22c55e;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.15s ease;
  cursor: pointer;
  outline: none;
  position: relative;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.dot-btn:hover {
  transform: scale(1.1);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
}

.dot-btn:active:not(:disabled) {
  transform: translateY(1px) scale(1.05);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.dot-btn:focus {
  box-shadow: 0 0 0 3px rgba(34, 197, 94, 0.3);
}

/* Size variants */
.dot-btn--sm {
  width: 16px;
  height: 16px;
  border-width: 1px;
}

.dot-btn--md {
  width: 20px;
  height: 20px;
  border-width: 2px;
}

.dot-btn--lg {
  width: 24px;
  height: 24px;
  border-width: 2px;
}

/* Active state */
.dot-btn--active {
  background: #3b82f6;
  border-color: #ffffff;
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.3);
}

.dot-btn--active:hover {
  background: #2563eb;
}

.dot-btn--active:focus {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

/* Disabled state */
.dot-btn--disabled {
  background: #d1d5db;
  cursor: not-allowed;
  opacity: 0.6;
}

.dot-btn--disabled:hover {
  transform: none;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Color variants */
.dot-btn[data-color="primary"] {
  background: #3b82f6;
}

.dot-btn[data-color="secondary"] {
  background: #6b7280;
}

.dot-btn[data-color="success"] {
  background: #22c55e;
}

.dot-btn[data-color="warning"] {
  background: #f59e0b;
}

.dot-btn[data-color="danger"] {
  background: #ef4444;
}

.dot-btn__inner {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  height: 100%;
}

/* Ripple effect */
.dot-btn::before {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  width: 0;
  height: 0;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.3);
  transform: translate(-50%, -50%);
  transition: width 0.3s, height 0.3s;
}

.dot-btn:active::before {
  width: 100%;
  height: 100%;
}
</style>
