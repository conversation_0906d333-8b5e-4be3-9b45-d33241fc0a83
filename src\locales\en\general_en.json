{"pageTitle": "<PERSON><PERSON>", "error": "Error", "success": "Success", "info": "Information", "tryAgain": "Try Again", "hello": "Hello", "add": "Add", "home": "Home", "actions": "Actions", "status": "Status", "lastUpdate": "LastUpdate", "createdAt": "Created At", "logout": "Logout", "description": "Description", "selectType": "Select Type", "save": "Save", "update": "Update", "cancel": "Cancel", "requiredFieldsMessage": "Fill in all required fields", "saveSuccess": "Saved successfully", "saveError": "Error saving", "loadError": "Error loading", "edit": "Edit", "confirm": "Confirm", "remove": "Remove", "delete": "Delete", "UnknownError": "Unexpected error. Please try again.", "search": "Search", "image": "Image", "name": "Name", "sku": "SKU", "price": "Price", "create": "Create", "productType": "Product Type", "stock": "Stock", "addProduct": "Add Product", "onlyInStock": "Only in stock", "noProductsFound": "No products found", "confirmDelete": "Are you sure you want to delete {name}?", "page": "Page {current} of {total}", "go": "Go", "showing": "Showing", "to": "to", "of": "of", "results": "results", "previous": "Previous", "next": "Next", "products": "Products", "tags": "Tags", "value": "value", "active": "Active", "type": "Type", "yes": "Yes", "no": "No", "shippingConfig": {"title": "Shipping Settings", "description": "Here you can define values that will be added to the shipping price. This is useful to cover packaging costs, taxes etc.", "addFixedValueLabel": "Do you want to add a fixed value to the shipping price?", "fixedValueLabel": "Fixed value to be added to shipping:", "addPercentageLabel": "Do you want to add a percentage to the shipping price?", "percentageLabel": "Percentage increase:", "fixedValuePlaceholder": "Ex: 10.00", "percentagePlaceholder": "0", "saveButton": "Save", "savedSuccess": "Shipping settings saved successfully", "savedError": "Error saving shipping settings", "validationError": "Please correct the validation errors before saving.", "loadError": "Error loading shipping settings.", "atLeastOneOption": "Select at least one increase option.", "savedLocallyMessage": "Shipping settings saved locally. They will be synchronized when the server is available.", "pickupTitle": "In-Store Pickup", "offerPickupLabel": "Offer 'In-Store Pickup' as a shipping method in your store?", "pickupOption": "Offer 'In-Store Pickup' as a shipping method in your store?", "pickupDescription": "This shipping option can be used if the customer wishes to pick up the order at the store, studio, office, etc.", "pickupDetailedDescription": "This shipping option can be used if the customer wishes to pick up the order at the store, studio, office, etc. All orders with this shipping option selected will have their shipping costs automatically set to zero, and payment can be made online at the store at the time of purchase.", "pickupDescriptionLabel": "Enter the description for the in-store pickup option. Example: \"Pick up at store\":", "pickupDescriptionPlaceholder": "Enter how the customer can pick up the order at the store", "yesLabel": "Yes", "noLabel": "No", "fixedValueRequired": "The fixed value is required when this option is enabled.", "percentageRequired": "The percentage is required when this option is enabled.", "pickupDescriptionRequired": "The pickup description is required when this option is enabled.", "cepRangeRequired": "You need to add at least one ZIP code range.", "cepRangeFieldsRequired": "All ZIP code range fields must be filled in.", "cepRangeInvalid": "In the region '{name}', the initial ZIP code must be less than the final ZIP code."}, "layoutEditor": {"videoConfigEditor": "Video Editor", "content": "Content", "video": "Video", "design": "Design", "left": "Left", "center": "Center", "right": "Right", "horizontal": "Horizontal", "vertical": "Vertical"}, "videoEditor": {"title": "Configure Video", "content": {"title": "Content", "videoTitle": "Video Title", "videoTitlePlaceholder": "Enter video title...", "description": "Description", "descriptionPlaceholder": "Enter video description...", "buttonSettings": "Button Settings", "enableButton": "Show action button", "buttonText": "Button Text", "buttonTextPlaceholder": "Shop now", "buttonUrl": "Button URL", "buttonUrlPlaceholder": "https://example.com"}, "video": {"title": "Video Settings", "videoUrl": "Video URL", "videoUrlPlaceholder": "Paste YouTube, Vimeo or direct video URL", "videoUrlHint": "Supports YouTube, Vimeo and direct video files", "videoType": "Video Type", "directVideo": "Direct Video", "aspectRatio": "Aspect Ratio", "videoPoster": "Poster Image", "videoPosterPlaceholder": "Poster image URL (direct videos only)", "playbackOptions": "Playback Options", "autoplay": "Autoplay", "loop": "Loop video", "muted": "Start muted", "showControls": "Show controls"}, "design": {"title": "Layout & Design", "layout": "Layout", "layoutType": "Layout Type", "horizontal": "Horizontal", "vertical": "Vertical", "videoPosition": "Video Position", "positionLeft": "Left", "positionRight": "Right"}, "colors": {"title": "Colors", "backgroundColor": "Background Color", "textColor": "Text Color", "buttonColor": "Button Color", "buttonTextColor": "Button Text Color"}, "spacing": {"title": "Spacing", "paddingTop": "Top Padding", "paddingBottom": "Bottom Padding", "borderRadius": "Border Radius"}, "actions": {"preview": "Preview Video", "reset": "Reset to Defaults", "apply": "Apply"}}, "saveChanges": "Save Changes", "cancelChanges": "Cancel Changes", "selectAll": "Select All", "unselectAll": "Unselect All", "deleteSelected": "Delete Selected", "clear": "Clear", "clearAll": "Clear All", "preview": "Preview", "toast": {"titles": {"success": "Success", "error": "Error", "warning": "Warning", "info": "Information"}, "close": "Close notification", "undo": "Undo"}, "saving": "Saving...", "loadingCategories": "Loading categories...", "idNotFound": "ID not found", "confirmDeleteTitle": "Confirm Deletion", "common": {"cancel": "Cancel", "close": "Close", "add": "Add", "edit": "Edit", "delete": "Delete", "actions": "Actions", "saveChanges": "Save Changes", "create": "Create", "retry": "Try again", "loadingCategories": "Loading categories...", "pagination": {"showing": "Showing {first} to {last} of {totalRecords} entries"}, "confirmDeleteTitle": "Confirm Deletion", "idNotFound": "ID not found"}}