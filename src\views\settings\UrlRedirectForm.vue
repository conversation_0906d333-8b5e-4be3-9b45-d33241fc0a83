<template>
  <div class="form-container">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>{{ t('loading') }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Header with actions -->
      <IluriaHeader
        :title="isEditing ? t('urlRedirect.editTitle', 'Editar Redirecionamento') : t('urlRedirect.createTitle', 'Novo Redirecionamento')"
        :subtitle="isEditing ? t('urlRedirect.editSubtitle', 'Modifique as informações do redirecionamento de URL') : t('urlRedirect.createSubtitle', 'Configure um novo redirecionamento de URL para sua loja')"
        :showCancel="true"
        :cancelText="t('urlRedirect.cancel')"
        :showSave="true"
        :saveText="isEditing ? t('urlRedirect.update') : t('urlRedirect.save')"
        @cancel-click="goBackToList"
        @save-click="handleSubmit"
      />

      <!-- Form Content -->
      <div class="form-content">
        <ViewContainer 
          :title="t('urlRedirect.basicDataTitle', 'Dados do Redirecionamento')"
          :subtitle="t('urlRedirect.basicDataSubtitle', 'Configure as URLs de origem e destino')"
          :icon="LinkForwardIcon"
          iconColor="blue"
        >
          <Form 
            id="redirect-form"
            @submit="handleSubmit" 
            v-slot="$form" 
            :resolver="resolver" 
            :validate-on-value-update="false" 
            :validate-on-blur="true"
            :initial-values="form"
          >
            <div class="form-grid">
              <!-- URL Original -->
              <div class="form-field">
                <IluriaInputText 
                  id="url" 
                  name="url" 
                  :label="t('urlRedirect.url')" 
                  :placeholder="t('urlRedirect.urlPlaceholder', '/promocao/calcados')"
                  v-model="form.url" 
                  :formContext="$form.url"
                  required
                />
              </div>

              <!-- URL de Redirecionamento -->
              <div class="form-field">
                <IluriaInputText 
                  id="redirectUrl" 
                  name="redirectUrl" 
                  :label="t('urlRedirect.redirectUrl')" 
                  :placeholder="t('urlRedirect.redirectUrlPlaceholder', '/calcados')"
                  v-model="form.redirectUrl" 
                  :formContext="$form.redirectUrl"
                  required
                />
              </div>

              <!-- Status Ativo -->
              <div class="form-field toggle-field">
                <div class="toggle-switch-container">
                  <div class="toggle-content">
                    <div class="toggle-info">
                      <h4 class="toggle-title">{{ t('urlRedirect.activeLabel', 'Redirecionamento Ativo') }}</h4>
                      <p class="field-help">
                        {{ t('urlRedirect.activeHelp', 'Quando ativo, as requisições para a URL original serão automaticamente redirecionadas') }}
                      </p>
                    </div>
                    <IluriaToggleSwitch 
                      id="active" 
                      name="active" 
                      v-model="form.active" 
                      :formContext="$form.active" 
                    />
                  </div>
                </div>
              </div>
            </div>
          </Form>
        </ViewContainer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { Form } from '@primevue/forms'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import { ref, onMounted, computed } from 'vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import settingsService from '@/services/settings.service'
import { useRouter } from 'vue-router'
import { useToast } from '@/services/toast.service'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { requiredText } from '@/services/validation.service'
import { z } from 'zod'
import { 
  LinkForwardIcon, 
  Cancel01Icon, 
  FloppyDiskIcon 
} from '@hugeicons-pro/core-stroke-rounded'

const toast = useToast()
const router = useRouter()
const { t } = useI18n()

const loading = ref(false)
const saving = ref(false)

const form = ref({
    url: '',
    redirectUrl: '',
    active: true
})

const props = defineProps({
    id: {
        type: String,
        required: false
    }
})

const isEditing = computed(() => !!props.id)

const resolver = zodResolver(
    z.object({
        url: requiredText(t('urlRedirect.url')),
        redirectUrl: requiredText(t('urlRedirect.redirectUrl')),
    })
)

async function handleSubmit() {
    saving.value = true;
    try {
        if (props.id) {
            await settingsService.updateUrlRedirect(props.id, form.value)
            toast.showSuccess(t('urlRedirect.updateSuccess', 'Redirecionamento atualizado com sucesso'))
        } else {
            await settingsService.createUrlRedirect(form.value)
            toast.showSuccess(t('urlRedirect.createdSuccess', 'Redirecionamento criado com sucesso'))
        }
        
        router.push('/settings/url-redirect')
    } catch (error) {
        console.error(error)

        if (error.response?.data?.errors?.length > 0) {
            toast.showError(error.response.data.errors[0].message)
        } else {
            toast.showError(t('urlRedirect.saveError', 'Erro ao salvar redirecionamento'))
        }
    } finally {
        saving.value = false;
    }
}

function goBackToList() {
    router.push('/settings/url-redirect')
}

async function loadUrlRedirect() {
    loading.value = true;
    try {
        const urlRedirect = await settingsService.getUrlRedirect(props.id)
        form.value = { ...urlRedirect }
    } catch (error) {
        console.error('Error loading URL redirect:', error)
        toast.showError(t('urlRedirect.loadError', 'Erro ao carregar redirecionamento'))
        router.push('/settings/url-redirect')
    } finally {
        loading.value = false;
    }
}

onMounted(() => {
    if (props.id) {
        loadUrlRedirect()
    }
})
</script>

<style scoped>
.form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
  position: relative;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  color: var(--iluria-color-text-secondary);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--iluria-color-border);
  border-top: 4px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Header */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Form Content */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.toggle-field {
  grid-column: span 2;
  padding: 20px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.toggle-field:hover {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.toggle-switch-container {
  width: 100%;
}

.toggle-content {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 48px;
}

.toggle-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.toggle-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
}

.field-help {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .form-container {
    padding: 16px;
  }
  
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-subtitle {
    font-size: 14px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .toggle-field {
    grid-column: span 1;
    padding: 16px;
  }
  
  .toggle-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
    min-height: auto;
  }
  
  .toggle-info {
    width: 100%;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .page-title {
    font-size: 22px;
  }
}
</style>
