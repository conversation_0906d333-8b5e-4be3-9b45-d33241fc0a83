<template>
  <div class="space-y-4">
    <!-- Header com título e botão de adicionar -->
    <div class="flex justify-between items-center" v-if="title">
      <h3 class="text-lg font-medium text-gray-900">{{ title }}</h3>
      <button
        v-if="canAdd"
        type="button"
        class="flex items-center text-blue-600 hover:text-blue-800 font-medium"
        @click="addItem"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        {{ addButtonText }}
      </button>
    </div>

    <!-- Lista de itens -->
    <div v-for="(item, index) in items" :key="getItemKey(item, index)" :class="itemClass">
      <div class="flex justify-between items-start mb-4" v-if="showItemHeader">
        <h4 class="text-md font-medium text-gray-800">
          {{ getItemTitle(item, index) }}
        </h4>
        <button
          v-if="canRemove"
          type="button"
          class="p-2 rounded-full text-red-500 hover:bg-red-50"
          @click="removeItem(index)"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
          </svg>
        </button>
      </div>

      <!-- Slot para conteúdo personalizado do item -->
      <slot 
        name="item" 
        :item="item" 
        :index="index" 
        :updateItem="(updatedItem) => updateItem(index, updatedItem)"
        :removeItem="() => removeItem(index)"
      >
        <!-- Conteúdo padrão se nenhum slot for fornecido -->
        <div class="p-4 bg-gray-50 rounded border">
          <p class="text-gray-600">Forneça um slot "item" para personalizar o conteúdo</p>
        </div>
      </slot>
    </div>

    <!-- Botão de adicionar item (quando não há título) -->
    <button
      v-if="!title && canAdd"
      type="button"
      class="mt-2 flex items-center text-blue-600 hover:text-blue-800 font-medium"
      @click="addItem"
    >
      <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-1" viewBox="0 0 20 20" fill="currentColor">
        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
      </svg>
      {{ addButtonText }}
    </button>

    <!-- Mensagem quando não há itens -->
    <div v-if="items.length === 0 && emptyMessage" class="text-center py-6 text-gray-500">
      {{ emptyMessage }}
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  title: {
    type: String,
    default: null
  },
  addButtonText: {
    type: String,
    default: 'Adicionar Item'
  },
  emptyMessage: {
    type: String,
    default: null
  },
  itemClass: {
    type: String,
    default: 'border  p-4 bg-white'
  },
  showItemHeader: {
    type: Boolean,
    default: true
  },
  itemTitleTemplate: {
    type: String,
    default: 'Item {index}'
  },
  maxItems: {
    type: Number,
    default: null
  },
  minItems: {
    type: Number,
    default: 0
  },
  createNewItem: {
    type: Function,
    default: () => ({})
  },
  itemKeyField: {
    type: String,
    default: null
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'add-item', 'remove-item', 'update-item']);

// Estado local
const items = ref([]);

// Computed
const canAdd = computed(() => {
  return props.maxItems === null || items.value.length < props.maxItems;
});

const canRemove = computed(() => {
  return items.value.length > props.minItems;
});

// Funções
const addItem = () => {
  if (canAdd.value) {
    const newItem = props.createNewItem();
    items.value.push(newItem);
    emit('add-item', newItem);
    updateModelValue();
  }
};

const removeItem = (index) => {
  if (canRemove.value) {
    const removedItem = items.value.splice(index, 1)[0];
    emit('remove-item', removedItem, index);
    updateModelValue();
  }
};

const updateItem = (index, updatedItem) => {
  if (index >= 0 && index < items.value.length) {
    items.value[index] = updatedItem;
    emit('update-item', updatedItem, index);
    updateModelValue();
  }
};

const getItemKey = (item, index) => {
  if (props.itemKeyField && item[props.itemKeyField]) {
    return item[props.itemKeyField];
  }
  return `item-${index}`;
};

const getItemTitle = (item, index) => {
  return props.itemTitleTemplate.replace('{index}', index + 1);
};

const updateModelValue = () => {
  emit('update:modelValue', [...items.value]);
};

// Watch para mudanças no modelValue
watch(() => props.modelValue, (newValue) => {
  if (Array.isArray(newValue)) {
    items.value = [...newValue];
  } else {
    items.value = [];
  }
}, { immediate: true, deep: true });

// Watch para mudanças nos itens
watch(items, () => {
  updateModelValue();
}, { deep: true });
</script>

<style scoped>
.item-container {
  @apply border  p-4 bg-white;
}

.add-button {
  @apply mt-2 flex items-center text-blue-600 hover:text-blue-800 font-medium;
}

.remove-button {
  @apply p-2 rounded-full text-red-500 hover:bg-red-50;
}
</style> 
