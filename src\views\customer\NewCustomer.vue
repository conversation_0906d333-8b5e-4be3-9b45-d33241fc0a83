<template>
  <div class="customer-form-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="customerId ? t('customer.editTitle') : t('customer.createTitle')"
      :subtitle="customerId ? t('customer.editSubtitle') : t('customer.createSubtitle')"
      :showCancel="true"
      :cancelText="t('customer.cancel')"
      :showSave="true"
      :saveText="customerId ? t('customer.update') : t('customer.save')"
      @cancel-click="goBack"
      @save-click="handleSubmit"
    />

    <!-- Main Content -->
    <div class="form-content">
      <Form 
        @submit.prevent="handleSubmit" 
        v-slot="$form" 
        :validate-on-blur="false" 
        :validate-on-value-update="false" 
        :initial-values="reactive(form)"
      >
        <div class="form-sections">
          <!-- Basic Customer Data -->
          <ViewContainer :title="t('customer.basicDataTitle')" :subtitle="t('customer.basicDataSubtitle')"
            :icon="UserIcon" iconColor="blue">
            <div class="customer-container">
              <!-- Form Fields -->
              <div class="form-section">
                <div class="form-grid">
                  <!-- Linha 1: Tipo de Cliente + Documento -->
                  <div class="form-row">
                    <div class="form-col">
                      <IluriaRadioGroup
                        direction="horizontal"
                        id="customer-type"
                        :label="t('customer.typeLabel')"
                        :options="[
                          { value: 'pf', label: t('customer.cpfLabel') },
                          { value: 'pj', label: t('customer.cnpjLabel') }
                        ]"
                        v-model="customerType"
                      />
                    </div>
                    <div class="form-col">
                      <IluriaInputMask
                        id="taxId"
                        name="taxId"
                        :placeholder="customerType === 'pj' ? '00.000.000/0000-00' : '000.000.000-00'"
                        :label="customerType === 'pj' ? t('customer.cnpjLabel') : t('customer.cpfLabel')"
                        v-model="form.taxId"
                        :mask="customerType === 'pj' ? '99.999.999/9999-99' : '999.999.999-99'"
                      />
                    </div>
                  </div>

                  <!-- Linha 2: Nome Completo + E-mail -->
                  <div class="form-row">
                    <div class="form-col">
                      <IluriaInputText
                        id="fullName"
                        name="fullName"
                        :label="t('customer.nameLabel')"
                        v-model="form.fullName"
                      />
                    </div>
                    <div class="form-col">
                      <IluriaInputText
                        id="email"
                        name="email"
                        :label="t('customer.emailLabel')"
                        v-model="form.email"
                      />
                    </div>
                  </div>

                  <!-- Linha 3: Celular + Data de Nascimento -->
                  <div class="form-row">
                    <div class="form-col">
                      <IluriaInputMask
                        id="phone"
                        name="phone"
                        :label="t('customer.phoneLabel')"
                        mask="(99) 99999-9999"
                        placeholder="(11) 99999-9999"
                        v-model="form.phone"
                      />
                    </div>
                    <div class="form-col">
                      <IluriaInputMask
                        id="birthDate"
                        name="birthDate"
                        :label="t('customer.birthDateLabel')"
                        mask="99/99/9999"
                        placeholder="dd/mm/aaaa"
                        v-model="form.birthDate"
                      />
                    </div>
                  </div>

                  <!-- Linha 4: Status + Marketing -->
                  <div class="form-row">
                    <div class="form-col">
                      <div class="toggle-field">
                        <IluriaLabel class="field-label">{{ t('customer.statusLabel') }}</IluriaLabel>
                        <div class="toggle-wrapper">
                          <IluriaToggleSwitch id="active" v-model="customerStatus" />
                          <span class="toggle-description">
                            {{ customerStatus ? t('customer.inactiveStatus') : t('customer.activeStatus') }}
                          </span>
                        </div>
                      </div>
                    </div>
                    <div class="form-col">
                      <div class="toggle-field">
                        <IluriaLabel class="field-label">{{ t('customer.marketingLabel') }}</IluriaLabel>
                        <div class="toggle-wrapper">
                          <IluriaToggleSwitch id="allowsEmailMarketing" v-model="form.allowsEmailMarketing" />
                          <span class="toggle-description">
                            {{ t('customer.marketingDescription') }}
                          </span>
                        </div>
                        <!-- Novo switch: Marketing por Telefone -->
                        <div class="toggle-wrapper">
                          <IluriaToggleSwitch id="allowsPhoneMarketing" v-model="form.allowsPhoneMarketing" />
                          <span class="toggle-description">
                            {{ t('customer.phoneMarketingDescription') }}
                          </span>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Customer Photo (Circular) - Following UserSettings Pattern -->
              <div class="avatar-section">
                <div class="avatar-content" @click="editCustomerPhoto">
                  <div class="avatar-container">
                    <div class="avatar">
                      <img
                        v-if="customerPhoto"
                        :src="getPhotoPreview()"
                        alt="Foto do cliente"
                        class="avatar-image"
                      />
                      <HugeiconsIcon
                        v-else
                        :icon="UserIcon"
                        size="72"
                        :strokeWidth="1.5"
                      />
                    </div>
                    <div class="edit-button">
                      <HugeiconsIcon :icon="UserEditIcon" size="16" :strokeWidth="1.5" />
                      {{ customerPhoto ? 'Alterar' : 'Selecionar Foto' }}
                    </div>
                  </div>
                  <h4 class="avatar-title">{{ t('customer.photoLabel') }}</h4>
                </div>
              </div>
            </div>

            <!-- Hidden file input -->
            <input
              ref="fileInput"
              type="file"
              accept="image/*"
              style="display: none"
              @change="onFileSelected"
            />
          </ViewContainer>

          <!-- Address Information -->
          <ViewContainer v-if="isLoaded && validStoreId" :title="t('customer.addressTitle')" :subtitle="t('customer.addressSubtitle')"
            :icon="Location01Icon" iconColor="orange">
            <AddressForm :addresses="addresses" :customerId="customerId" :storeId="validStoreId"
              @update:addresses="addresses = $event" />
          </ViewContainer>


        </div>
      </Form>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useRouter, useRoute  } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { Form } from '@primevue/forms'
import CustomerService from '@/services/customer.service'
import AddressForm from '@/components/customer/AddressForm.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'

import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaInputMask from '@/components/iluria/form/IluriaInputMask.vue'
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'


import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserIcon,
  UserEditIcon,
  Location01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import StoreService from '@/services/store.service.js'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const toast = useToast()

const customerId = route.params.id
const storeId = ref(null)
const isLoaded = ref(false)
const saving = ref(false)
const customerType = ref('pf')
const addresses = ref([])
const customerPhoto = ref(null)
const fileInput = ref(null)

// Photo handling functions
const editCustomerPhoto = () => {
  fileInput.value?.click()
}

const onFileSelected = (event) => {
  const file = event.target.files[0]
  if (file) {
    customerPhoto.value = file
  }
}

const getPhotoPreview = () => {
  if (!customerPhoto.value) return null

  if (customerPhoto.value instanceof File) {
    return URL.createObjectURL(customerPhoto.value)
  }

  // Para URLs de string, adicionar cache buster simples
  if (typeof customerPhoto.value === 'string') {
    const url = customerPhoto.value
    const separator = url.includes('?') ? '&' : '?'
    return `${url}${separator}t=${Date.now()}`
  }

  return customerPhoto.value
}

// Form state
const form = reactive({
  fullName: '',
  email: '',
  phone: '',
  taxId: '',
  documentType: '',
  birthDate: '',
  active: true,
  allowsEmailMarketing: false,
  allowsPhoneMarketing: false
})

const customerStatus = computed({
  get: () => !form.active,
  set: (value) => {
    form.active = !value
  }
})

const validStoreId = computed(() => {
  return storeId.value && storeId.value.trim() !== '' ? storeId.value : null
})

const getStoreId = async () => {
  try {
    const response = await StoreService.getStoreData()
    storeId.value = response.id || null
  } catch (error) {
    console.error('Error getting store ID:', error)
    storeId.value = null
  }
}


const loadCustomer = async () => {
  if (!customerId) {
    isLoaded.value = true
    return
  }

  try {
    const response = await CustomerService.getCustomer(customerId)
    form.fullName = response.name || ''
    form.email = response.email || ''
    form.phone = response.phone || ''
    form.taxId = response.document || ''
    form.active = response.active !== false
    form.allowsEmailMarketing = response.allowsEmailMarketing || false
    form.allowsPhoneMarketing = response.allowsPhoneMarketing || false

    // Carregar foto existente se houver
    if (response.photoUrl) {
      customerPhoto.value = response.photoUrl
    } else {
      customerPhoto.value = null
    }

    const cleanedTaxId = form.taxId.toString().replace(/\D/g, '')
    customerType.value = cleanedTaxId.length === 14 ? 'pj' : 'pf'

    // Formatar data de nascimento
    if (response.dayOfBirth) {
      const date = new Date(response.dayOfBirth)
      const day = String(date.getDate()).padStart(2, '0')
      const month = String(date.getMonth() + 1).padStart(2, '0')
      const year = date.getFullYear()
      form.birthDate = `${day}/${month}/${year}`
    }

    const processedAddresses = (response.addresses || []).map(address => {
      return {
        id: address.id || null,
        customerId: customerId,
        type: address.type || 'residential',
        label: address.label || '',
        recipient: address.recipient || form.fullName,
        street: address.street || '',
        number: address.number || '',
        complement: address.complement || '',
        neighborhood: address.district || '',
        city: address.city || '',
        state: address.state || '',
        zipCode: address.zipCode || '',
        country: address.country || 'Brasil',
        mainAddress: address.mainAddress || false,
      }
    })

    addresses.value = processedAddresses

    const hasPrimary = addresses.value.some(a => a.mainAddress)
    if (!hasPrimary && addresses.value.length > 0) {
      addresses.value[0].mainAddress = true
    }

  } catch (error) {
    console.error('Error loading customer:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('customer.loadError'),
      life: 3000
    })
  } finally {
    isLoaded.value = true
  }
}

const validateForm = () => {
  if (!form.fullName?.trim()) {
    showErrorToast('Nome completo é obrigatório')
    return false
  }
  if (!form.email?.trim()) {
    showErrorToast('Email é obrigatório')
    return false
  }
  return true
}

const showErrorToast = (detail) => {
  toast.add({
    severity: 'error',
    summary: t('error'),
    detail,
    life: 3000
  })
}

const formatBirthDate = () => {
  if (!form.birthDate || form.birthDate.length !== 8) return null
  
  const day = form.birthDate.substring(0, 2)
  const month = form.birthDate.substring(2, 4)
  const year = form.birthDate.substring(4, 8)
  return `${year}-${month}-${day}`
}

const transformAddressesForBackend = (addresses) => {
  return addresses.map(address => ({
    id: address.id,
    customerId: address.customerId,
    zipCode: address.zipCode,
    street: address.street,
    number: address.number,
    complement: address.complement,
    district: address.neighborhood, // Mapear neighborhood para district
    city: address.city,
    state: address.state,
    country: address.country,
    label: address.label,
    type: address.type,
    mainAddress: address.mainAddress
    // Remover recipient pois não existe no backend
  }))
}

const buildCustomerData = () => {
  const documentType = customerType.value === 'pj' ? 'CNPJ' : 'CPF'
  const formattedBirthDate = formatBirthDate()

  const customerData = {
    name: form.fullName.trim(),
    email: form.email.trim(),
    active: form.active,
    allowsEmailMarketing: form.allowsEmailMarketing || false,
    defaultLanguage: 'pt-BR',
    addresses: transformAddressesForBackend(addresses.value)
  }
  
  if (form.phone?.trim()) customerData.phone = form.phone.trim()
  if (form.taxId?.trim()) {
    customerData.document = form.taxId.trim()
    customerData.documentType = documentType
  }
  if (formattedBirthDate) customerData.dayOfBirth = formattedBirthDate
  
  return customerData
}

const getErrorMessage = (error) => {
  return error.response?.data?.message || 
         error.response?.data?.error || 
         error.message || 
         t('customer.saveError')
}

const ensurePrimaryAddress = () => {
  if (addresses.value.length > 0) {
    const hasPrimary = addresses.value.some(addr => addr.mainAddress)
    if (!hasPrimary) addresses.value[0].mainAddress = true
  }
}

const showSuccessMessage = () => {
  const successMessage = customerId ? t('customer.updateSuccess') : t('customer.createSuccess')
  const addressInfo = addresses.value.length > 0 ? ` (${addresses.value.length} endereço(s) incluído(s))` : ''
  
  toast.add({
    severity: 'success',
    summary: t('success'),
    detail: successMessage + addressInfo,
    life: 3000
  })
}

const handleSubmit = async () => {
  saving.value = true

  if (!validateForm()) {
    saving.value = false
    return
  }

  try {
    const customerData = buildCustomerData()

    let savedCustomer
    if (customerId) {
      savedCustomer = await CustomerService.updateCustomer(customerId, customerData)
    } else {
      savedCustomer = await CustomerService.createCustomer(customerData)
    }

    // Upload da foto se uma nova foto foi selecionada
    if (customerPhoto.value && customerPhoto.value instanceof File) {
      try {
        const customerIdForPhoto = customerId || savedCustomer.id
        const uploadResponse = await CustomerService.uploadPhoto(customerIdForPhoto, customerPhoto.value)

        // Atualizar a URL da foto
        if (uploadResponse && uploadResponse.photoUrl) {
          customerPhoto.value = uploadResponse.photoUrl
        }
      } catch (photoError) {
        console.error('Erro ao fazer upload da foto:', photoError)
        toast.add({
          severity: 'warn',
          summary: t('warning'),
          detail: t('customer.photoUploadError'),
          life: 3000
        })
      }
    }

    ensurePrimaryAddress()
    showSuccessMessage()
    saving.value = false

    router.push('/customer-list')

  } catch (error) {
    console.error('Erro ao salvar cliente:', error)

    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: getErrorMessage(error),
      life: 5000
    })
    saving.value = false
  }
}

const goBack = () => {
  router.push('/customer-list')
}

onMounted(async () => {
  await getStoreId()
  await loadCustomer()
})
</script>

<style scoped>
.customer-form-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



/* Form Content */
.form-content {
  display: flex;
  flex-direction: column;
}

.form-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Customer Container - Positioned layout like the image */
.customer-container {
  position: relative;
  width: 100%;
  min-height: 400px;
  padding-right: 180px; /* Space for avatar on the right */
}

.form-section {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Form Grid */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: start;
}

.form-col {
  display: flex;
  flex-direction: column;
}

/* Avatar Section - Top Right Position like the image */
.avatar-section {
  position: absolute;
  top: 0;
  right: 0;
  display: flex;
  align-items: flex-start;
  justify-content: center;
  width: 160px;
}

.avatar-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  padding: 1rem;
  border-radius: 12px;
}

.avatar-content:hover {
  background: rgba(var(--iluria-color-primary-rgb), 0.05);
}

.avatar-content:hover .edit-button {
  background: rgba(0, 0, 0, 0.8);
  transform: scale(1.05);
}

.avatar-content:hover .avatar {
  transform: scale(1.02);
}

.avatar-container {
  position: relative;
}

.avatar-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0.75rem 0 0 0;
  text-align: center;
}

.avatar {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: var(--iluria-color-primary);
  color: white;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 3px solid var(--iluria-color-container-bg);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  overflow: hidden;
}

.avatar-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 50%;
}

.edit-button {
  position: absolute;
  bottom: 4px;
  left: 4px;
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: rgba(0, 0, 0, 0.7);
  border: none;
  border-radius: 16px;
  color: white;
  font-size: 0.7rem;
  font-weight: 500;
  pointer-events: none;
  transition: all 0.2s ease;
  backdrop-filter: blur(4px);
}

/* Field Styles */
.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 8px;
}

.toggle-field {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.toggle-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
}

.toggle-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}



/* Responsive */
@media (max-width: 1024px) {
  .form-row {
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .customer-form-container {
    padding: 16px;
  }

  .customer-container {
    position: static;
    padding-right: 0;
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
  }

  .form-section {
    width: 100%;
    order: 2;
  }

  .avatar-section {
    position: static;
    width: 100%;
    align-items: center;
    order: 1;
    margin-bottom: 1rem;
  }

  .avatar {
    width: 100px;
    height: 100px;
  }

  .avatar-content {
    padding: 0.5rem;
  }

  .edit-button {
    position: static;
    margin-top: 1rem;
    background: var(--iluria-color-container-bg);
    color: var(--iluria-color-text-primary);
    border: 1px solid var(--iluria-color-border);
    border-radius: 6px;
    padding: 0.5rem 1rem;
    font-size: 0.875rem;
    pointer-events: auto;
  }

  .avatar-content:hover .edit-button {
    background: var(--iluria-color-hover);
    border-color: var(--iluria-color-border-hover);
    transform: none;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content h1.page-title {
    font-size: 24px;
  }

  .header-content .page-subtitle {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .form-row {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-content h1.page-title {
    font-size: 22px;
  }

  .form-grid {
    gap: 16px;
  }

  .form-row {
    gap: 12px;
  }
}
</style>
