<template>
  <div class="combined-product-form-container">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>{{ $t('combinedProduct.edit.loading') }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Header with actions -->
      <div class="form-header">
        <div class="header-content">
          <h1 class="page-title">
            {{ isEditing ? $t('combinedProduct.edit.titleEdit') : $t('combinedProduct.edit.titleCreate') }}
          </h1>
          <p class="page-subtitle">
            {{ isEditing ? 'Edite as informações do produto combinado' : 'Cadastre e edite informações de produto combinado' }}
          </p>
        </div>
        <div class="header-actions">
          <IluriaButton 
            color="dark"
            variant="outline" 
            @click="goBackToList"
          >
            {{ $t('combinedProduct.edit.actions.cancel') }}
          </IluriaButton>
          <IluriaButton 
            color="dark"
            :hugeIcon="FloppyDiskIcon" 
            @click="saveCombinedProduct"
            :loading="saving"
          >
            {{ isEditing ? $t('combinedProduct.edit.actions.save') : $t('combinedProduct.edit.actions.create') }}
          </IluriaButton>
        </div>
      </div>

      <!-- Form Content -->
      <div class="form-content">
        <div class="grid grid-cols-1 lg:grid-cols-3 gap-6">
          <!-- Main Content Column -->
          <div class="lg:col-span-2 space-y-6">
            <!-- Dados Básicos do Produto -->
            <ViewContainer 
              :title="$t('combinedProduct.edit.sections.basicInfo')"
              :subtitle="$t('combinedProduct.edit.sections.basicInfoDescription')"
              :icon="TagIcon"
              iconColor="blue"
            >
              <div class="grid grid-cols-1 gap-6">
                <!-- Nome do Produto -->
                <div class="space-y-2">
                  <IluriaLabel for="productName">{{ $t('combinedProduct.edit.fields.productName') }}</IluriaLabel>
                  <IluriaInputText 
                    id="productName" 
                    v-model="form.name"
                    :placeholder="$t('combinedProduct.edit.fields.productNamePlaceholder')" 
                    required 
                    class="w-full"
                  />
                </div>

                <!-- Descrição -->
                <div class="space-y-2">
                  <IluriaLabel for="productDesc">{{ $t('combinedProduct.edit.fields.description') }}</IluriaLabel>
                  <Textarea 
                    id="productDesc" 
                    v-model="form.description"
                    :placeholder="$t('combinedProduct.edit.fields.descriptionPlaceholder')" 
                    rows="4"
                    class="w-full"
                  />
                </div>

                <!-- Status do Produto -->
                <div class="space-y-2">
                  <IluriaLabel for="status">{{ $t('combinedProduct.edit.fields.productStatus') }}</IluriaLabel>
                  <IluriaSelectButton 
                    id="status" 
                    v-model="form.status" 
                    :options="statusOptions" 
                    option-label="label" 
                    option-value="value" 
                    required 
                    class="w-full"
                  />
                </div>
              </div>
            </ViewContainer>

            <!-- Upload de Imagens -->
            <ViewContainer 
              :title="$t('combinedProduct.edit.sections.images')"
              :subtitle="$t('combinedProduct.edit.sections.imagesDescription')"
              :icon="UploadCircle02Icon"
              iconColor="green"
            >
              <div class="space-y-6">
                <ProductPhotoUpload v-model="images" :max-files="5" />
                
                <!-- Preview e Stickers -->
                <div v-if="showPreviewControls()" class="preview-section">
                  <div class="preview-header">
                    <h3 class="preview-title">{{ $t('combinedProduct.edit.preview.title', { index: previewIndex + 1 }) }}</h3>
                    <div class="preview-navigation">
                      <IluriaButton
                        size="small"
                        variant="outline"
                        @click="prevPreview" 
                        :disabled="previewIndex === 0"
                        class="nav-button"
                      >
                        ←
                      </IluriaButton>
                      <span class="preview-counter">{{ previewIndex + 1 }} / {{ images.length }}</span>
                      <IluriaButton
                        size="small"
                        variant="outline"
                        @click="nextPreview" 
                        :disabled="previewIndex === images.length - 1"
                        class="nav-button"
                      >
                        →
                      </IluriaButton>
                    </div>
                  </div>

                  <div class="preview-container">
                    <div class="preview-image-wrapper" @drop.prevent="onImageDrop">

                      
                      <img 
                        :src="getPreviewUrl(images[previewIndex])" 
                        alt="Pré-visualização" 
                        class="preview-image" 
                      />
                      
                      <!-- Stickers sobre a imagem -->
                      <div 
                        v-for="(sticker, sIdx) in (stickers[previewIndex] || [])"
                        :key="sIdx"
                        class="sticker-container"
                        :style="{ left: (sticker.x * 100) + '%', top: (sticker.y * 100) + '%' }"
                        @mousedown="(e) => onStickerDragStart(sIdx, e)"
                        @dblclick="(e) => { e.preventDefault(); e.stopPropagation(); openProductModal(previewIndex, sIdx); }"
                      >
                                                <!-- Sticker principal -->
                        <DotButton
                          :active="hasStickerProduct(sIdx)"
                          :class="['sticker-button', { 'has-product': hasStickerProduct(sIdx) }]"
                          :title="getStickerTooltip(sIdx)"
                        >
                          <span class="sticker-number">{{ sIdx + 1 }}</span>
                        </DotButton>
                        
                        <!-- Tooltip do produto no hover -->
                        <div 
                          v-if="hasStickerProduct(sIdx)"
                          class="product-tooltip"
                        >
                          <div class="product-info">
                            <img 
                              v-if="getStickerProductImage(sIdx)"
                              :src="getStickerProductImage(sIdx)"
                              alt="Produto"
                              class="product-thumb"
                            />
                            <div class="product-details">
                              <div class="product-name">{{ getStickerProductName(sIdx) }}</div>
                              <div class="product-price">{{ getStickerProductPrice(sIdx) }}</div>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>

                    <div class="preview-controls">
                      <p class="preview-hint">{{ $t('combinedProduct.edit.preview.dragHint') }}</p>
                      <DotButton
                        size="lg"
                        @click="addStickerToImage(previewIndex)"
                        :title="$t('combinedProduct.edit.tooltips.newSticker')"
                        class="add-sticker-button"
                      >
                        <span class="add-icon">+</span>
                      </DotButton>
                      <div class="preview-instructions">
                        {{ $t('combinedProduct.edit.preview.stickerHint') }}<br>
                        {{ $t('combinedProduct.edit.preview.removeHint') }}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </ViewContainer>
          </div>

          <!-- Sidebar -->
          <div class="space-y-6">
            <!-- Informações Rápidas -->
            <ViewContainer 
              :title="$t('combinedProduct.edit.sidebar.quickInfo')"
              :subtitle="$t('combinedProduct.edit.sidebar.quickInfoDescription')"
              :icon="Settings01Icon"
              iconColor="purple"
            >
              <div class="space-y-4">
                <div class="info-item">
                  <label class="info-label">{{ $t('combinedProduct.edit.sidebar.totalImages') }}</label>
                  <span class="info-value">{{ images.length }}/5</span>
                </div>
                <div class="info-item">
                  <label class="info-label">{{ $t('combinedProduct.edit.sidebar.totalStickers') }}</label>
                  <span class="info-value">{{ getTotalStickers() }}</span>
                </div>
                <div class="info-item">
                  <label class="info-label">{{ $t('combinedProduct.edit.sidebar.linkedProducts') }}</label>
                  <span class="info-value">{{ getLinkedProductsCount() }}</span>
                </div>
              </div>
            </ViewContainer>


          </div>
        </div>
      </div>
    </div>

    <!-- Product Selection Modal -->
    <ProductSelectionModal
      v-if="showProductModal"
      :visible="showProductModal"
      :multi-select="false"
      @update:visible="handleProductModalClose"
      @select="handleAddProducts"
    />
  </div>
</template>

<script setup>
import { HugeiconsIcon } from '@hugeicons/vue';
import { TagIcon, UploadCircle02Icon, Settings01Icon, Alert01Icon, FloppyDiskIcon } from '@hugeicons-pro/core-bulk-rounded';
import { ref, reactive, onMounted, onBeforeUnmount, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelectButton from '@/components/iluria/form/IluriaSelectButton.vue'
import Textarea from '@/components/Textarea.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import ProductPhotoUpload from '@/components/products/ProductPhotoUpload.vue'
import DotButton from '@/components/products/DotButton.vue'
import ProductSelectionModal from '@/components/products/ProductSelectorModal.vue'
import { combinedProductService } from '@/services/combined-product.service'

const router = useRouter()
const route = useRoute()
const toast = useToast()
const { t } = useI18n()
const saving = ref(false)
const loading = ref(true)

const MAX_COMBINED_PRODUCTS = 6

const isEditing = computed(() => !!route.params.id)

const form = ref({
  name: '',
  description: '',
  type: 'SHOP_THE_LOOK',
  status: 'ACTIVE'
})

const statusOptions = computed(() => [
  { label: t('combinedProduct.edit.statusOptions.active'), value: 'ACTIVE' },
  { label: t('combinedProduct.edit.statusOptions.inactive'), value: 'INACTIVE' }
])

const images = ref([])
const previewIndex = ref(0)
const stickers = reactive([])
const stickerProductMap = reactive({})
const showProductModal = ref(false)
const currentSticker = ref(null)
const hasReachedLimit = computed(() => {
  return getProductCount() >= MAX_COMBINED_PRODUCTS
})

function ensureStickerArray(idx) {
  while (stickers.length <= idx) stickers.push([])
}

function addStickerToImage(idx, x = 0.5, y = 0.5) {
  ensureStickerArray(idx)
  stickers[idx].push({ x, y })
}

function updateStickerPosition(imgIdx, stickerIdx, x, y) {
  if (stickers[imgIdx] && stickers[imgIdx][stickerIdx]) {
    stickers[imgIdx][stickerIdx].x = x;
    stickers[imgIdx][stickerIdx].y = y;
  }
}

function removeSticker(imgIdx, stickerIdx) {
  stickers[imgIdx].splice(stickerIdx, 1)
  
  // Remove produto associado se existir
  const productKey = `${imgIdx}-${stickerIdx}`;
  if (stickerProductMap[productKey]) {
    delete stickerProductMap[productKey];
  }
}

function getStickerTooltip(stickerIdx) {
  const productKey = `${previewIndex.value}-${stickerIdx}`;
  const product = stickerProductMap[productKey];
  
  if (product) {
    return t('combinedProduct.edit.tooltips.withProduct', { name: product.name, price: product.price });
  }
  
  return t('combinedProduct.edit.tooltips.withoutProduct', { index: stickerIdx + 1 });
}

function getStickerProductImage(stickerIdx) {
  const productKey = `${previewIndex.value}-${stickerIdx}`;
  const product = stickerProductMap[productKey];
  
  if (product && product.photos && product.photos.length > 0) {
    return product.photos[0].url || product.photos[0];
  }
  
  return null;
}

function getStickerProductName(stickerIdx) {
  const productKey = `${previewIndex.value}-${stickerIdx}`;
  const product = stickerProductMap[productKey];
  return product?.name || '';
}

function getStickerProductPrice(stickerIdx) {
  const productKey = `${previewIndex.value}-${stickerIdx}`;
  const product = stickerProductMap[productKey];
  
  if (product?.price) {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(product.price);
  }
  
  return '';
}

function hasStickerProduct(stickerIdx) {
  const productKey = `${previewIndex.value}-${stickerIdx}`;
  return !!stickerProductMap[productKey];
}

// LÓGICA DE DRAG RESTAURADA
const draggingSticker = ref(null) 

function onStickerDragStart(sIdx, event) {
  event.preventDefault();
  event.stopPropagation();
  
  let stickerContainer = event.currentTarget;
  if (!stickerContainer.classList.contains('sticker-container')) {
    stickerContainer = event.target.closest('.sticker-container');
  }
  
  const imgWrapper = stickerContainer.closest('.preview-image-wrapper');
  if (!imgWrapper) return;
  
  const img = imgWrapper.querySelector('img');
  if (!img) return;
  
  const imgRect = img.getBoundingClientRect();
  
  if (!stickerContainer) return;
  
  draggingSticker.value = {
    imgIdx: previewIndex.value,
    stickerIdx: sIdx,
    element: stickerContainer,
    inRemoveZone: false,
    imgRect: imgRect,
    startMouseX: event.clientX,
    startMouseY: event.clientY,
    startStickerX: stickers[previewIndex.value][sIdx].x,
    startStickerY: stickers[previewIndex.value][sIdx].y
  };
  
  stickerContainer.classList.add('dragging');
  
  window.addEventListener('mousemove', onStickerDragMove);
  window.addEventListener('mouseup', onStickerDragEnd);
}

function onStickerDragMove(event) {
  if (!draggingSticker.value) return;
  
  const { 
    imgIdx, 
    stickerIdx, 
    element, 
    imgRect, 
    startMouseX, 
    startMouseY, 
    startStickerX, 
    startStickerY 
  } = draggingSticker.value;
  
  const deltaMouseX = event.clientX - startMouseX;
  const deltaMouseY = event.clientY - startMouseY;
  
  const deltaX = deltaMouseX / imgRect.width;
  const deltaY = deltaMouseY / imgRect.height;
  
  let x = startStickerX + deltaX;
  let y = startStickerY + deltaY;
  
  // Zona de exclusão simples: fora das bordas da imagem
  const borderMargin = 0.1;
  const isInRemoveZone = (
    x < -borderMargin ||        // Esquerda
    x > (1 + borderMargin) ||   // Direita  
    y < -borderMargin ||        // Cima
    y > (1 + borderMargin)      // Baixo
  );
  
  // Clamp para manter sticker visível na imagem
  const clampedX = Math.max(0, Math.min(1, x));
  const clampedY = Math.max(0, Math.min(1, y));
  
  if (isInRemoveZone) {
    if (element && !draggingSticker.value.inRemoveZone) {
      element.classList.add('remove-zone');
      draggingSticker.value.inRemoveZone = true;
    }
    updateStickerPosition(imgIdx, stickerIdx, clampedX, clampedY);
  } else {
    if (element && draggingSticker.value.inRemoveZone) {
      element.classList.remove('remove-zone');
      draggingSticker.value.inRemoveZone = false;
    }
    
    updateStickerPosition(imgIdx, stickerIdx, clampedX, clampedY);
  }
}

function onStickerDragEnd(event) {
  if (draggingSticker.value) {
    const { imgIdx, stickerIdx, element, inRemoveZone } = draggingSticker.value;
    
    if (inRemoveZone) {
      removeSticker(imgIdx, stickerIdx);
      toast.showSuccess(t('combinedProduct.messages.stickerRemoved'));
    }
    
    if (element) {
      element.classList.remove('remove-zone');
      element.classList.remove('dragging');
    }
  }
  
  window.removeEventListener('mousemove', onStickerDragMove);
  window.removeEventListener('mouseup', onStickerDragEnd);
  draggingSticker.value = null;
}

onBeforeUnmount(() => {
  window.removeEventListener('mousemove', onStickerDragMove);
  window.removeEventListener('mouseup', onStickerDragEnd);
});

function onImageDrop(e) {
  if (draggingSticker.value) {
    draggingSticker.value = null
  } else {
    const imgBox = e.target.getBoundingClientRect()
    const x = (e.clientX - imgBox.left) / imgBox.width
    const y = (e.clientY - imgBox.top) / imgBox.height
    addStickerToImage(previewIndex.value, x, y)
  }
}

function showPreviewControls() {
  return images.value.length > 0;
}

function setPreviewIndex(idx) {
  previewIndex.value = idx;
}

function nextPreview() {
  if (previewIndex.value < images.value.length - 1) previewIndex.value++;
}

function prevPreview() {
  if (previewIndex.value > 0) previewIndex.value--;
}

function getPreviewUrl(img) {
  if (!img) return '';
  if (img.url) return img.url;
  if (typeof File !== 'undefined' && img instanceof File) {
    return URL.createObjectURL(img);
  }
  return '';
}

function openProductModal(imgIdx, stickerIdx) {
  currentSticker.value = { imgIdx, stickerIdx }
  showProductModal.value = true
}

function handleProductModalClose() {
  showProductModal.value = false
}

function handleAddProducts(products) {
  if (currentSticker.value && products && products.length > 0) {
    const productKey = `${currentSticker.value.imgIdx}-${currentSticker.value.stickerIdx}`;
    stickerProductMap[productKey] = products[0];
  }
  showProductModal.value = false
}

function getTotalStickers() {
  return stickers.reduce((total, stickerArray) => total + stickerArray.length, 0);
}

function getLinkedProductsCount() {
  return Object.keys(stickerProductMap).length;
}

function getProductCount() {
  return getLinkedProductsCount();
}

// LÓGICA DE SALVAMENTO RESTAURADA
const saveCombinedProduct = async () => {
  try {
    saving.value = true
    
    const imageStickers = []
    
    for (let imgIdx = 0; imgIdx < images.value.length; imgIdx++) {
      const imageStickersData = {
        imageIndex: imgIdx,
        stickers: []
      }
      
      if (stickers[imgIdx] && stickers[imgIdx].length > 0) {
        for (let stickerIdx = 0; stickerIdx < stickers[imgIdx].length; stickerIdx++) {
          const sticker = stickers[imgIdx][stickerIdx]
          const productKey = `${imgIdx}-${stickerIdx}`
          const associatedProduct = stickerProductMap[productKey]
          
          const stickerData = {
            x: sticker.x,
            y: sticker.y,
            product: associatedProduct ? {
              id: associatedProduct.id,
              name: associatedProduct.name,
              price: associatedProduct.price,
              photos: associatedProduct.photos
            } : null
          }
          
          imageStickersData.stickers.push(stickerData)
        }
      }
      
      // Sempre inclui a imagem, mesmo sem stickers
      imageStickers.push(imageStickersData)
    }
    
    const productData = {
      name: form.value.name,
      description: form.value.description,
      photos: images.value, 
      stickers: imageStickers, 
      type: 'SHOP_THE_LOOK',
      status: form.value.status
    }
    

    
    if (!productData.name || productData.name.trim() === '') {
      toast.showError(t('combinedProduct.edit.validation.nameRequired'))
      return
    }
    
    if (productData.photos.length === 0) {
      toast.showError(t('combinedProduct.edit.validation.imageRequired'))
      return
    }
    
    if (productData.photos.length > 5) {
      toast.showError(t('combinedProduct.edit.validation.maxImages'))
      return
    }
    
    const hasProductStickers = imageStickers.some(imgData => 
      imgData.stickers.some(sticker => sticker.product !== null)
    )
    
    if (!hasProductStickers) {
      toast.showError(t('combinedProduct.edit.validation.productRequired'))
      return
    }

    const totalStickers = imageStickers.reduce((total, imgData) => total + imgData.stickers.length, 0)
    const totalProductStickers = imageStickers.reduce((total, imgData) => 
      total + imgData.stickers.filter(s => s.product).length, 0
    )
    
    toast.showInfo(t('combinedProduct.edit.saving.info', { productCount: totalProductStickers, stickerCount: totalStickers }))
    
    let response
    
    if (isEditing.value) {
      const productId = String(route.params.id).trim()
      response = await combinedProductService.updateCombinedProduct(productId, productData)
      
      if (response && response.id) {
        form.value.id = response.id
      }
      
      toast.showSuccess(t('combinedProduct.messages.updateSuccess'))
    } else {
      response = await combinedProductService.createCombinedProduct(productData)
      
      if (response && response.id) {
        form.value.id = response.id
      }
      
      toast.showSuccess(t('combinedProduct.messages.createSuccess'))
    }
    
    await new Promise(resolve => setTimeout(resolve, 500))
    
    await router.push('/products/combinado')
    
  } catch (error) {
    const errorMessage = isEditing.value 
      ? t('combinedProduct.messages.updateError')
      : t('combinedProduct.messages.createError')
    
    if (error.code === 'ERR_NETWORK' || error.response?.status >= 500) {
      toast.showWarning(t('combinedProduct.messages.savedLocally'))
    } else {
      toast.showError(errorMessage)
    }
    
  } finally {
    saving.value = false
  }
}

function goBackToList() {
  router.push('/products/combinado');
}

// LÓGICA DE CARREGAMENTO RESTAURADA
onMounted(async () => {
  if (route.params.id) {
    const productId = String(route.params.id)
    try {
      const product = await combinedProductService.getCombinedProduct(productId)
      
      form.value.name = product.name || ''
      form.value.description = product.description || ''
      form.value.status = product.status || 'ACTIVE'
      
      if (product.photos && product.photos.length > 0) {
        images.value = [...product.photos]
      }
      
      if (product.stickers && product.stickers.length > 0) {
        while (stickers.length > 0) {
          stickers.pop()
        }
        
        Object.keys(stickerProductMap).forEach(key => {
          delete stickerProductMap[key]
        })
        
        product.stickers.forEach(imageData => {
          const imgIdx = imageData.imageIndex
          
          ensureStickerArray(imgIdx)
          
          imageData.stickers.forEach((stickerData, stickerIdx) => {
            stickers[imgIdx].push({
              x: stickerData.x,
              y: stickerData.y
            })
            
            if (stickerData.product) {
              const productKey = `${imgIdx}-${stickerIdx}`
              stickerProductMap[productKey] = stickerData.product
            }
          })
        })
      }
      
      toast.showSuccess(t('combinedProduct.messages.loadSuccess'))
    } catch (error) {
      toast.showError(t('combinedProduct.messages.loadEditError'))
    }
  }
  loading.value = false
})
</script>

<style scoped>
.combined-product-form-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-body-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
  transition: all 0.3s ease;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--iluria-color-border);
  border-top: 4px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Header */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Main Content */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Preview Section */
.preview-section {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 24px;
  transition: all 0.3s ease;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
  transition: color 0.3s ease;
}

.preview-navigation {
  display: flex;
  align-items: center;
  gap: 12px;
}

.nav-button {
  min-width: 36px;
  height: 36px;
}

.preview-counter {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
  transition: color 0.3s ease;
}

.preview-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 24px;
}

.preview-image-wrapper {
  position: relative;
  max-width: 600px;
  width: 100%;
  border-radius: 12px;
  overflow: hidden;
  background: var(--iluria-color-sidebar-bg);
  box-shadow: var(--iluria-shadow-lg);
}



.preview-image {
  width: 100%;
  height: auto;
  max-height: 400px;
  object-fit: contain;
  display: block;
}

.sticker-container {
  position: absolute;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: grab;
  padding: 20px;
  margin: -20px;
  transform: translate(-50%, -50%);
  z-index: 10;
  transition: none;
}

.sticker-container:active {
  cursor: grabbing;
}

.sticker-container.dragging {
  z-index: 1000;
  transition: none;
}

.sticker-button {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              background-color 0.2s ease;
  user-select: none;
  -webkit-user-drag: none;
  cursor: grab;
  transform-origin: center;
  will-change: transform;
}

.sticker-container:hover .sticker-button {
  transform: scale(1.08);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.sticker-button:active,
.sticker-button.dragging {
  cursor: grabbing;
  transform: scale(1.1);
  z-index: 1000;
  box-shadow: 0 4px 16px rgba(0, 0, 0, 0.25);
  transition: none;
}

.sticker-button.has-product {
  background: var(--iluria-color-success) !important;
  border: 2px solid var(--iluria-color-surface);
  box-shadow: 0 0 0 2px var(--iluria-color-success);
}

.sticker-container:hover .sticker-button.has-product {
  background: var(--iluria-color-primary-hover) !important;
  transform: scale(1.12);
  box-shadow: 0 0 0 3px var(--iluria-color-success), 
              0 4px 12px rgba(0, 0, 0, 0.2);
}

.sticker-number {
  font-size: 12px;
  font-weight: bold;
  color: white;
}

/* Product Tooltip */
.product-tooltip {
  position: absolute;
  bottom: 120%;
  left: 50%;
  transform: translateX(-50%) translateY(8px);
  background: var(--iluria-color-surface);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg);
  padding: 12px;
  min-width: 200px;
  z-index: 1001;
  opacity: 0;
  visibility: hidden;
  transition: opacity 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              visibility 0.4s cubic-bezier(0.4, 0, 0.2, 1),
              transform 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  pointer-events: none;
  border: 1px solid var(--iluria-color-border);
}

.sticker-container:hover .product-tooltip {
  opacity: 1;
  visibility: visible;
  transform: translateX(-50%) translateY(-8px);
  transition-delay: 0.2s;
}

/* Seta do tooltip */
.product-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  margin-left: -5px;
  border-width: 5px;
  border-style: solid;
  border-color: var(--iluria-color-surface) transparent transparent transparent;
}

/* Drag state */
.sticker-container.dragging .product-tooltip {
  display: none;
}

/* Remove zone para o container */
.sticker-container.remove-zone .sticker-button {
  background: #ef4444 !important;
  transform: translate(-50%, -50%) scale(1.3) !important;
  box-shadow: 0 0 0 6px rgba(239, 68, 68, 0.4) !important;
  animation: pulse-remove 0.4s infinite alternate;
  border: 2px solid #ffffff !important;
}

@keyframes pulse-remove {
  0% {
    box-shadow: 0 0 0 6px rgba(239, 68, 68, 0.4);
    transform: translate(-50%, -50%) scale(1.3);
  }
  100% {
    box-shadow: 0 0 0 12px rgba(239, 68, 68, 0.1);
    transform: translate(-50%, -50%) scale(1.4);
  }
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-thumb {
  width: 50px;
  height: 50px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid var(--iluria-color-border);
}

.product-details {
  flex: 1;
}

.product-name {
  font-weight: 600;
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.product-price {
  font-weight: 700;
  font-size: 16px;
  color: var(--iluria-color-success);
  transition: color 0.3s ease;
}

.preview-controls {
  text-align: center;
}

.preview-hint {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 16px;
  transition: color 0.3s ease;
}

.add-sticker-button {
  margin-bottom: 16px;
}

.add-icon {
  font-size: 18px;
  font-weight: bold;
  color: white;
}

.preview-instructions {
  font-size: 12px;
  color: var(--iluria-color-text-muted);
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Sidebar */
.info-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.info-label {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

.info-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}



/* Responsividade */
@media (max-width: 1024px) {
  .form-content .grid {
    grid-template-columns: 1fr;
  }
  
  .lg\\:col-span-2 {
    grid-column: span 1;
  }
}

@media (max-width: 768px) {
  .combined-product-form-container {
    padding: 16px;
  }

  .form-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 12px;
  }

  .preview-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .preview-navigation {
    justify-content: center;
  }
}

/* Tema escuro - força aplicação */
.theme-dark .combined-product-form-container {
  background: var(--iluria-color-body-bg) !important;
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .page-title {
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .page-subtitle {
  color: var(--iluria-color-text-secondary) !important;
}

.theme-dark .preview-section {
  background: var(--iluria-color-surface) !important;
  border-color: var(--iluria-color-border) !important;
}

.theme-dark .preview-title {
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .preview-image-wrapper {
  background: var(--iluria-color-sidebar-bg) !important;
}

.theme-dark .product-tooltip {
  background: var(--iluria-color-surface) !important;
  border-color: var(--iluria-color-border) !important;
}

.theme-dark .product-name {
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .product-price {
  color: var(--iluria-color-success) !important;
}

.theme-dark .info-label {
  color: var(--iluria-color-text-secondary) !important;
}

.theme-dark .info-value {
  color: var(--iluria-color-text-primary) !important;
}
</style> 
