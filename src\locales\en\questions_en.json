{"title": "Questions & Answers", "subtitle": "Manage customer questions about your products.", "kpis": {"pending": "Pending Response", "answered": "Answered", "highlighted": "Highlighted", "hidden": "Hidden", "total": "Total"}, "status": {"pending": "Pending Response", "answered": "Answered", "hidden": "Hidden", "highlighted": "Highlighted", "all": "All"}, "filters": {"allCategories": "All categories", "searchProduct": "Search product...", "searchQuestion": "Search question...", "date": "Date"}, "sort": {"newest": "Newest", "oldest": "Oldest"}, "table": {"product": "Product", "question": "Question", "customer": "Customer", "date": "Date", "status": "Status", "actions": "Actions", "recentQuestions": "Recent Questions"}, "response": {"backToQuestions": "Back to Questions", "productActive": "Active product", "yourResponse": "Your response", "editYourResponse": "Edit your response", "editMode": "Edit Mode", "aiSuggestion": "AI Suggestion", "useThisResponse": "Use this response", "generateAnother": "Generate another", "predefinedAnswers": "Predefined answers", "selectPredefinedAnswer": "Select a predefined answer", "saveAsPredefined": "Save as predefined answer", "predefinedTitle": "Title for predefined answer", "cancel": "Cancel", "publishResponse": "Publish Response", "publishHighlighted": "Publish Highlighted", "updateResponse": "Update Response", "updateHighlighted": "Update Highlighted", "responseUpdated": "Response updated successfully!", "willRemovePredefined": "⚠️ By unchecking, the predefined response \"{title}\" will be permanently removed."}, "actions": {"respond": "Respond", "highlight": "Highlight", "hide": "<PERSON>de", "delete": "Delete"}, "messages": {"loadError": "Failed to load question details.", "responseEmpty": "Response field cannot be empty.", "predefinedTitleRequired": "Predefined answer title is required.", "responseSuccess": "Response sent successfully!", "responseError": "Failed to send response.", "kpisError": "Error loading KPIs.", "questionsError": "Error loading questions.", "statusUpdateSuccess": "Question status updated successfully.", "statusUpdateError": "Error updating question status.", "questionHidden": "Question hidden successfully.", "questionShown": "Question shown successfully."}, "predefinedTypes": {"materials": "About clothing materials", "sizing": "About sizes and measurements", "shipping": "About shipping and delivery"}}