<template>
    <div class="blog-post-list-container">
        <!-- Header with actions -->
        <IluriaHeader
            :title="$t('blogPost.title')"
            :subtitle="$t('blogPost.subtitle')"
            :showAdd="true"
            :addText="$t('blogPost.new')"
            @add-click="$router.push('/blog/posts/new')"
        >
            <template #rightHeader>
                <router-link
                    to="/blog"
                    class="action-button secondary"
                >
                    <HugeiconsIcon
                        :icon="ArrowLeft01Icon"
                        :size="16"
                        class="mr-2"
                    />
                    {{ t('blogDashboard.title') }}
                </router-link>
                <router-link
                    to="/blog/categories"
                    class="action-button secondary"
                >
                    <HugeiconsIcon
                        :icon="FolderIcon"
                        :size="16"
                        class="mr-2"
                    />
                    {{ $t('blogCategory.title') }}
                </router-link>
            </template>
        </IluriaHeader>

        <!-- Content -->
        <div class="list-content">
            <!-- Posts Table Container with integrated filters -->
            <ViewContainer 
                :title="t('blogPost.titleView')"
                :icon="LicenseDraftIcon"
                iconColor="blue"
            >
                <!-- Integrated Filters -->
                <div class="filters-section">
                    <div class="filters-layout">
                        <!-- Search -->
                        <div class="search-container">
                            <IluriaInputText
                                v-model="filters.search"
                                type="text"
                                :placeholder="$t('blogPost.searchByTitle')"
                                @input="debouncedSearch"
                                class="search-input"
                            />
                        </div>

                        <!-- Filters with counter -->
                        <div class="filters-with-counter">
                            <div class="filters-container">
                                <IluriaSelect
                                    v-model="filters.published"
                                    :options="publishedOptions"
                                    :placeholder="$t('blogPost.filters.all')"
                                    class="filter-select"
                                />
                                
                                <IluriaSelect
                                    v-model="filters.featured"
                                    :options="featuredOptions"
                                    :placeholder="$t('blogPost.filters.all')"
                                    class="filter-select"
                                />
                            </div>
                            
                            <!-- Posts Counter -->
                            <div class="posts-counter">
                                <span class="counter-text">
                                    {{ posts.totalElements || 0 }} {{ posts.totalElements === 1 ? 'post' : 'posts' }}
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
                <!-- Loading -->
                <div v-if="loading" class="flex justify-center py-12">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
                </div>

                <!-- Empty state -->
                <div v-else-if="!posts.content?.length" class="text-center py-12">
                    <HugeiconsIcon
                        :icon="LicenseDraftIcon"
                        :size="48"
                        class="mx-auto text-gray-400 mb-4"
                    />
                    <h3 class="empty-state-title">
                        {{ $t('blogPost.noData') }}
                    </h3>
                    <p class="empty-state-subtitle">
                        {{ $t('blogPost.subtitle') }}
                    </p>
                    <IluriaButton
                        color="primary"
                        :hugeIcon="Add01Icon"
                        @click="$router.push('/blog/posts/new')"
                    >
                        {{ $t('blogPost.new') }}
                    </IluriaButton>
                </div>

                <!-- Posts DataTable -->
                <div v-else>
                    <IluriaDataTable
                        :value="sortedPosts"
                        :columns="mainTableColumns"
                        dataKey="id"
                        class="blog-posts-table"
                    >

                        <!-- Header Title -->
                        <template #header-title="{ column }">
                            <div @click="toggleSort('title')" class="sortable-header">
                                <span>{{ column.header }}</span>
                                <span v-if="sortField === 'title'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                            </div>
                        </template>

                        <!-- Header Status -->
                        <template #header-published="{ column }">
                            <div @click="toggleSort('published')" class="sortable-header">
                                <span>{{ column.header }}</span>
                                <span v-if="sortField === 'published'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                            </div>
                        </template>

                        <!-- Header Views -->
                        <template #header-viewCount="{ column }">
                            <div @click="toggleSort('viewCount')" class="sortable-header">
                                <span>{{ column.header }}</span>
                                <span v-if="sortField === 'viewCount'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                            </div>
                        </template>

                        <!-- Header Created At -->
                        <template #header-createdAt="{ column }">
                            <div @click="toggleSort('createdAt')" class="sortable-header">
                                <span>{{ column.header }}</span>
                                <span v-if="sortField === 'createdAt'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                            </div>
                        </template>

                        <!-- Header Image -->
                        <template #header-image="{ column }">
                            <span>{{ column.header }}</span>
                        </template>

                        <!-- Header Actions -->
                        <template #header-actions="{ column }">
                            <div class="flex items-center justify-center">
                                <span>{{ column.header }}</span>
                            </div>
                        </template>

                        <!-- Image Column -->
                        <template #column-image="{ data }">
                            <div class="w-16 h-16 bg-gray-200 dark:bg-gray-600 rounded-lg overflow-hidden">
                                <img
                                    v-if="data.featuredImageUrl"
                                    :src="data.featuredImageUrl"
                                    :alt="data.title"
                                    class="w-full h-full object-cover"
                                />
                                <div v-else class="w-full h-full flex items-center justify-center">
                                    <HugeiconsIcon
                                        :icon="Image02Icon"
                                        :size="24"
                                        class="text-gray-400"
                                    />
                                </div>
                            </div>
                        </template>

                        <!-- Title Column -->
                        <template #column-title="{ data }">
                            <div class="flex flex-col">
                                <div class="post-title">
                                    {{ data.title }}
                                </div>
                                <div v-if="data.excerpt" class="post-excerpt">
                                    {{ data.excerpt }}
                                </div>
                                <div class="flex items-center mt-2 space-x-2">
                                    <span v-if="data.published" class="status-badge status-published">
                                        {{ $t('blogPost.status.published') }}
                                    </span>
                                    <span v-else class="status-badge status-draft">
                                        {{ $t('blogPost.status.draft') }}
                                    </span>
                                    <span v-if="data.featured" class="status-badge status-featured">
                                        <HugeiconsIcon
                                            :icon="StarIcon"
                                            :size="12"
                                            class="mr-1"
                                        />
                                        {{ $t('blogPost.status.featured') }}
                                    </span>
                                </div>
                            </div>
                        </template>

                        <!-- Status Column -->
                        <template #column-published="{ data }">
                            <span v-if="data.published" class="status-badge status-published">
                                {{ $t('blogPost.status.published') }}
                            </span>
                            <span v-else class="status-badge status-draft">
                                {{ $t('blogPost.status.draft') }}
                            </span>
                        </template>

                        <!-- Views Column -->
                        <template #column-viewCount="{ data }">
                            <div class="views-count">
                                {{ data.viewCount || 0 }}
                            </div>
                        </template>

                        <!-- Created At Column -->
                        <template #column-createdAt="{ data }">
                            <span class="date-text">{{ formatDate(data.createdAt) }}</span>
                        </template>

                        <!-- Actions Column -->
                        <template #column-actions="{ data }">
                            <div class="flex items-center justify-end space-x-2">
                                <IluriaButton
                                    color="primary"
                                    size="small"
                                    :hugeIcon="Edit02Icon"
                                    @click="editPost(data.id)"
                                    variant="ghost"
                                />
                                <IluriaButton
                                    color="danger"
                                    size="small"
                                    :hugeIcon="Delete02Icon"
                                    @click="deletePost(data)"
                                    variant="ghost"
                                />
                            </div>
                        </template>
                    </IluriaDataTable>

                    <!-- Pagination -->
                    <div v-if="posts.totalPages > 1" class="flex justify-center mt-6">
                        <IluriaPagination
                            :current-page="currentPage"
                            :total-pages="posts.totalPages"
                            @page-change="handlePageChange"
                        />
                    </div>
                </div>
            </ViewContainer>
        </div>
    </div>
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter } from 'vue-router'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
    Add01Icon,
    FolderIcon,
    LicenseDraftIcon,
    Image02Icon,
    StarIcon,
    Edit02Icon,
    Delete02Icon,
    FilterIcon,
    ArrowLeft01Icon
} from '@hugeicons-pro/core-stroke-standard'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import BlogPostService from '@/services/blogPost.service'
import { useToast } from 'primevue/usetoast'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import { useTheme } from '@/composables/useTheme'
import { debounce } from '@/utils/debounce'
import { useI18n } from 'vue-i18n'

const router = useRouter()
const toast = useToast()
const { t } = useI18n()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

// Initialize theme system
const { initTheme } = useTheme()
initTheme()

const loading = ref(false)
const posts = ref({ content: [], totalPages: 0, totalElements: 0 })
const currentPage = ref(0)
const sortField = ref('createdAt')
const sortOrder = ref(-1) // -1 for descending, 1 for ascending

const filters = reactive({
    search: '',
    published: '',
    featured: ''
})

const mainTableColumns = [
    { field: 'image', header: 'Imagem', class: 'col-image' },
    { field: 'title', header: 'Título', sortable: true, class: 'col-flex' },
    { field: 'published', header: 'Status', sortable: true, class: 'col-medium' },
    { field: 'viewCount', header: 'Visualizações', sortable: true, class: 'col-small' },
    { field: 'createdAt', header: 'Criado em', sortable: true, class: 'col-date' },
    { field: 'actions', header: 'Ações', class: 'col-actions' }
]

const publishedOptions = [
    { label: t('blogPost.filters.all'), value: '' },
    { label: t('blogPost.status.published'), value: 'true' },
    { label: t('blogPost.status.draft'), value: 'false' }
]

const featuredOptions = [
    { label: t('blogPost.filters.all'), value: '' },
    { label: t('blogPost.status.featured'), value: 'true' },
    { label: t('blogPost.filters.normal'), value: 'false' }
]

const toggleSort = (field) => {
    if (sortField.value === field) {
        sortOrder.value *= -1
    } else {
        sortField.value = field
        sortOrder.value = 1
    }
}

const sortedPosts = computed(() => {
    if (!posts.value.content) return []

    return [...posts.value.content].sort((a, b) => {
        const field = sortField.value
        let aValue = a[field]
        let bValue = b[field]

        if (typeof aValue === 'boolean') {
            aValue = aValue ? 1 : 0
            bValue = bValue ? 1 : 0
        }

        if (aValue < bValue) return -1 * sortOrder.value
        if (aValue > bValue) return 1 * sortOrder.value
        return 0
    })
})

const loadPosts = async () => {
    loading.value = true
    try {
        const response = await BlogPostService.listBlogPosts(
            filters.search,
            currentPage.value,
            10,
            '', // categoryId - vazio para buscar todos
            filters.published === '' ? null : filters.published === 'true',
            filters.featured === '' ? null : filters.featured === 'true'
        )
        posts.value = response
    } catch (error) {
        console.error('Error loading posts:', error)
        toast.add({ severity: 'error', summary: t('blogPost.messages.errorTitle'), detail: t('blogPost.messages.loadError'), life: 3000 })
    } finally {
        loading.value = false
    }
}

const debouncedSearch = debounce(() => {
    currentPage.value = 0
    loadPosts()
}, 300)

const handlePageChange = (page) => {
    currentPage.value = page
    loadPosts()
}

const editPost = (id) => {
    router.push(`/blog/posts/${id}`)
}

const doDeletePost = async (postId) => {
    try {
        await BlogPostService.deleteBlogPost(postId)
        toast.add({ severity: 'success', summary: t('blogPost.messages.successTitle'), detail: t('blogPost.messages.deleteSuccess'), life: 3000 })
        loadPosts()
    } catch (error) {
        console.error('Error deleting post:', error)
        toast.add({ severity: 'error', summary: t('blogPost.messages.errorTitle'), detail: t('blogPost.messages.deleteError'), life: 3000 })
    }
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title
    confirmModalConfirmText.value = t('delete')
    confirmModalCancelText.value = t('cancel')
    confirmModalType.value = 'error'
    confirmCallback.value = onConfirm
    showConfirmModal.value = true
}

const handleConfirm = () => {
    if (confirmCallback.value) {
        confirmCallback.value()
    }
    showConfirmModal.value = false
}

const handleCancel = () => {
    showConfirmModal.value = false
}

const deletePost = (post) => {
    showConfirmDanger(
        t('blogPost.messages.deleteConfirm', { title: post.title }),
        t('blogPost.messages.deleteConfirmTitle'),
        () => doDeletePost(post.id)
    )
}

const formatDate = (dateString) => {
    if (!dateString) return '-'
    return new Date(dateString).toLocaleDateString('pt-BR')
}

// Watchers para filtros
watch(() => filters.published, () => {
    currentPage.value = 0
    loadPosts()
})

watch(() => filters.featured, () => {
    currentPage.value = 0
    loadPosts()
})

onMounted(() => {
    loadPosts()
})
</script>

<style scoped>
.blog-post-list-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: var(--iluria-color-background);
    min-height: 100vh;
}

/* Header */
.list-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--iluria-color-text);
    margin: 0;
    line-height: 1.2;
}

.header-content .page-subtitle {
    font-size: 16px;
    color: var(--iluria-color-text-muted);
    margin: 4px 0 0 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

.action-button {
    display: inline-flex;
    align-items: center;
    padding: 10px 16px;
    border-radius: 8px;
    font-size: 14px;
    font-weight: 500;
    text-decoration: none;
    transition: all 0.2s ease;
}

.action-button.secondary {
    background: var(--iluria-color-container-bg);
    color: var(--iluria-color-text);
    border: 1px solid var(--iluria-color-border);
}

.action-button.secondary:hover {
    background: var(--iluria-color-sidebar-bg);
    border-color: var(--iluria-color-border-hover);
}

/* Main Content */
.list-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: var(--iluria-color-background);
}

/* Integrated Filters */
.filters-section {
    padding-bottom: 20px;
    margin-bottom: 20px;
}

/* Filters Layout */
.filters-layout {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.search-container {
    flex: 0 0 200px; /* narrower fixed width */
    max-width: 200px;
    width: 100%;
    display: flex;
    align-items: center; /* vertically center the input */
}

.search-input input {
    height: 38px; /* ensure same height as dropdowns */
}

/* Make search full-width on small screens */
@media (max-width: 768px) {
    .search-container {
        flex: 1 1 100%;
        max-width: 100%;
    }
}

.search-input {
    flex: 1 1 auto; /* expand to fill */
    width: 100%;
    max-width: none; /* remove fixed cap */
}

.filters-container {
    display: flex;
    gap: 16px;
    flex-wrap: wrap;
}

.filter-select {
    border: transparent !important;
    min-width: 180px;
    flex: 1;
    max-width: 250px;
}

.filter-select :deep(.p-dropdown) {
    border: none !important;
    box-shadow: none !important;
    background: var(--iluria-color-container-bg);
}

.filter-select :deep(.p-dropdown:hover),
.filter-select :deep(.p-dropdown:focus) {
    border: none !important;
    box-shadow: none !important;
}

.filter-select :deep(.p-inputtext) {
    border: none !important;
    box-shadow: none !important;
}


/* Filters with counter */
.filters-with-counter {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: nowrap;
}

.posts-counter {
    display: flex;
    align-items: center;
    background: var(--iluria-color-container-bg);
    border-radius: 8px;
    padding: 8px 12px;
    font-size: 14px;
    font-weight: 500;
    color: var(--iluria-color-text);
}

.counter-text {
    color: var(--iluria-color-text-muted);
}

/* Post title styling */
.post-title {
    font-size: 14px;
    font-weight: 500;
    color: var(--iluria-color-text);
    line-height: 1.4;
}

.post-excerpt {
    font-size: 14px;
    color: var(--iluria-color-text-muted);
    margin-top: 4px;
    line-height: 1.4;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
}

/* Empty state styling */
.empty-state-title {
    font-size: 18px;
    font-weight: 500;
    color: var(--iluria-color-text);
    margin-bottom: 8px;
}

.empty-state-subtitle {
    font-size: 16px;
    color: var(--iluria-color-text-muted);
    margin-bottom: 24px;
}

/* Status badges styling */
.status-badge {
    display: inline-flex;
    align-items: center;
    padding: 4px 8px;
    border-radius: 9999px;
    font-size: 12px;
    font-weight: 500;
}

.status-published {
    background-color: #dcfce7; /* light green */
    color: #166534; /* dark green */
}

.status-draft {
    background-color: var(--iluria-color-container-bg);
    color: var(--iluria-color-text-muted);
    border: 1px solid var(--iluria-color-border);
}

.status-featured {
    background-color: #fef3c7; /* light yellow */
    color: #92400e; /* dark yellow/brown */
}

/* Dark theme overrides for status badges */
:root[data-theme="dark"] .status-published {
    background-color: #14532d; /* dark green */
    color: #bbf7d0; /* light green */
}

:root[data-theme="dark"] .status-featured {
    background-color: #92400e; /* dark yellow */
    color: #fde68a; /* light yellow */
}

/* Centralizar colunas */
:deep(.blog-posts-table .p-datatable-tbody > tr > td.col-medium),
:deep(.blog-posts-table .p-datatable-tbody > tr > td.col-small) {
    text-align: center;
}

:deep(.blog-posts-table .p-datatable-thead > tr > th.col-medium),
:deep(.blog-posts-table .p-datatable-thead > tr > th.col-small) {
    text-align: center;
    justify-content: center;
}

/* Views and date styling */
.views-count {
    text-align: center;
    color: var(--iluria-color-text);
    font-weight: 500;
}

.date-text {
    color: var(--iluria-color-text);
    font-size: 14px;
}

/* Centralizar coluna de visualizações */
.blog-posts-table :deep(.p-datatable-thead th:nth-child(4)),
.blog-posts-table :deep(.p-datatable-tbody td:nth-child(4)) {
    text-align: center;
}

/* Table Column Styles */
:deep(.p-datatable-table) {
    border-collapse: separate;
    border-spacing: 0 4px;
}

:deep(th) {
    background: var(--iluria-color-background) !important;
    color: var(--iluria-color-text-muted) !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    padding: 8px 16px !important;
    border-bottom: 1px solid var(--iluria-color-border) !important;
    text-align: left;
}

:deep(td) {
    background: var(--iluria-color-container-bg);
    padding: 12px 16px !important;
    color: var(--iluria-color-text);
    border-bottom: 1px solid var(--iluria-color-border-light) !important;
    vertical-align: middle;
}

:deep(tr:first-child td:first-child) {
    border-top-left-radius: 8px;
}
:deep(tr:first-child td:last-child) {
    border-top-right-radius: 8px;
}
:deep(tr:last-child td:first-child) {
    border-bottom-left-radius: 8px;
}
:deep(tr:last-child td:last-child) {
    border-bottom-right-radius: 8px;
}
:deep(tr td) {
    border: none;
    border-bottom: 1px solid var(--iluria-color-border-light);
}

.sortable-header {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.sort-icon {
    font-size: 12px;
}

/* Column Widths */
:deep(.col-image) { width: 80px; }
:deep(.col-small) { width: 140px; }
:deep(.col-medium) { width: 160px; }
:deep(.col-large) { width: 220px; }
:deep(.col-date) { width: 150px; }
:deep(.col-actions) { width: 100px; text-align: center; }
:deep(.col-flex) { flex: 1; min-width: 200px; }


/* Responsive */
@media (max-width: 768px) {
    .blog-post-list-container {
        padding: 16px;
    }
    
    .list-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
    }
    
    .header-content {
        text-align: center;
    }
    
    .header-content h1.page-title {
        font-size: 24px;
    }
    
    .header-content .page-subtitle {
        font-size: 15px;
    }
    
    .header-actions {
        justify-content: center;
        flex-wrap: wrap;
    }

    .action-button {
        font-size: 13px;
        padding: 8px 12px;
    }

    /* Filters responsive */
    .filters-section {
        padding-bottom: 16px;
        margin-bottom: 16px;
    }

    .filters-layout {
        gap: 16px;
    }

    .search-input {
        max-width: 100%;
    }

    .filters-container {
        flex-direction: column;
        gap: 12px;
    }

    .filter-select {
        min-width: 100%;
        max-width: 100%;
    }

    .filters-with-counter {
        flex-direction: column;
        align-items: flex-start;
        gap: 12px;
    }

    .posts-counter {
        width: 100%;
        justify-content: center;
    }
}

@media (min-width: 769px) {
    .filters-layout {
        flex-direction: row;
        align-items: center; /* center vertically across breakpoints */
        justify-content: space-between;
        gap: 24px;
    }

    .search-container {
        flex: 1 1 auto;
        margin-right: 24px;
        max-width: none;
    }

    .filters-container {
        display: flex;
        gap: 16px;
        flex-wrap: nowrap;
        flex-shrink: 0;
    }

    .filter-select {
        min-width: 160px;
        max-width: 180px;
    }

    .filters-with-counter {
        display: flex;
        flex-direction: row;
        align-items: center;
        gap: 16px;
    }

    .posts-counter {
        flex-shrink: 0;
    }
}
</style> 