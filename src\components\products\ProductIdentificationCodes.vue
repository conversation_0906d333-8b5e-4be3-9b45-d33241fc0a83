<template>
    <div class="space-y-4">
        <div>
            <IluriaInputText id="sku" name="sku" :label="t('product.skuCode')" v-model="form.sku"
                :formContext="props.formContext?.sku" />
            <IluriaLabel class="label">{{t('product.skuCodeLabel')}}</IluriaLabel>
        </div>
        <div>
            <IluriaInputText id="barCode" name="barCode" :label="t('product.barCode')" v-model="form.barCode"
                :formContext="props.formContext?.barCode" />
        </div>
    </div>
</template>

<script setup>
import { inject } from 'vue';
import { useI18n } from 'vue-i18n';
import Message from 'primevue/message'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';


const { t } = useI18n();

const form = inject('productForm')

const props = defineProps({
    formContext: {
        type: Object,
        default: () => ({})
    }
});
</script>

<style scoped>
.label{
    font-size: 12px;
    font-weight: 200;
    color: var(--iluria-color-text-secondary);
}
</style>