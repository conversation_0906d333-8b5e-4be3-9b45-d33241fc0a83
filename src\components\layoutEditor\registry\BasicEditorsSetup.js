/**
 * Configuração e registro dos editores básicos no EditorRegistry
 * 
 * Este arquivo registra os editores que sempre devem estar disponíveis
 * no PropertyEditor da sidebar.
 */

import { EditorRegistry } from './EditorRegistry.js'

/**
 * Registra todos os editores básicos
 */
export function registerBasicEditors() {
  
  
}

/**
 * Registra editores específicos de componentes
 */
export function registerComponentEditors() {
  
  // Editor de carrossel unificado (combina design + conteúdo)
  EditorRegistry.registerEditor('carousel-config', {
    component: 'CarouselSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/CarouselSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de vídeo
  EditorRegistry.registerEditor('video-config', {
    component: 'VideoSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/VideoSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de header (versão sidebar)
  EditorRegistry.registerEditor('header-config', {
    component: 'HeaderSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/HeaderSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de footer (versão sidebar)
  EditorRegistry.registerEditor('footer-config', {
    component: 'FooterSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/FooterSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de informações da empresa unificado
  EditorRegistry.registerEditor('company-information-config', {
    component: 'CompanyInformationSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/CompanyInformationSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de localização unificado (combina conteúdo + design)
  EditorRegistry.registerEditor('location-config', {
    component: 'LocationSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/LocationSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de statement simplificado
  EditorRegistry.registerEditor('statement-config', {
    component: 'StatementEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/StatementEditor.vue'),
    category: 'components'
  })
  
  // Editor de benefícios de pagamento unificado (combina conteúdo + design)
  EditorRegistry.registerEditor('payment-benefits-config', {
    component: 'PaymentBenefitsSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/PaymentBenefitsSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de avaliações unificado
  EditorRegistry.registerEditor('customer-review-config', {
    component: 'CustomerReviewSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/CustomerReviewSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de ofertas especiais unificado
  EditorRegistry.registerEditor('special-offers-config', {
    component: 'SpecialOffersSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/SpecialOffersSidebarEditor.vue'),
    category: 'components'
  })
  
  // Editor de seleção de produtos unificado
  EditorRegistry.registerEditor('product-selection', {
    component: 'ProductSelectionSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/ProductSelectionSidebarEditor.vue'),
    category: 'ecommerce'
  })
  
  // Editor de estilo de produtos unificado  
  EditorRegistry.registerEditor('product-style', {
    component: 'ProductStyleSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/ProductStyleSidebarEditor.vue'),
    category: 'ecommerce'
  })
  
  // Editor de checkout de produto
  EditorRegistry.registerEditor('product-checkout-config', {
    component: 'ProductCheckoutSidebarEditor',
    loader: () => import('@/components/layoutEditor/Sidebar/editors/ProductCheckoutSidebarEditor.vue'),
    category: 'ecommerce'
  })

  // Editor principal do product-grid (combina seleção + estilo)
  EditorRegistry.registerEditor('product-grid-config', {
    component: 'ProductSelectionSidebarEditor', // Usar o editor de seleção como principal
    loader: () => import('@/components/layoutEditor/Sidebar/editors/ProductSelectionSidebarEditor.vue'),
    category: 'ecommerce'
  })

}

/**
 * Inicializa todos os editores
 */
export function initializeAllEditors() {
  
  registerBasicEditors()
  registerComponentEditors()
    
}

// Auto-inicialização
initializeAllEditors() 