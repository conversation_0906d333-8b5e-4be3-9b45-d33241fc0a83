<template>
  <div class="space-y-6">
    <!-- SEO Attribute Selection -->
   
      <div class="space-y-3">
        <div class="flex items-center gap-4">
          <IluriaLabel class="text-sm font-medium text-[var(--iluria-color-text-primary)] whitespace-nowrap">
            {{ t('seoProductVariation.variationType') }}
          </IluriaLabel>
          <div class="min-w-[200px]">
            <IluriaSelect
              id="seoAttribute"
              v-model="selectedSeoAttribute"
              @update:modelValue="onSeoAttributeChange"
              :options="seoAttributeOptions"
              optionLabel="label"
              optionValue="value"
            />
          </div>
        </div>
        <p class="text-xs text-[var(--iluria-color-text-secondary)]">{{ t('seoProductVariation.description') }}</p>
      </div>


    <!-- SEO Configuration Table -->
    <div v-if="selectedSeoAttribute && attributeValues.length > 0" class="bg-[var(--iluria-color-surface)] rounded-lg shadow-sm border border-[var(--iluria-color-border)]">
      <div class="p-4 border-b border-[var(--iluria-color-border)]">
        <IluriaTitle class="text-md font-medium text-[var(--iluria-color-text-primary)]">
          {{ t('seoProductVariation.productVariationsTitle') }}
        </IluriaTitle>
      </div>

      <div class="overflow-x-auto">
        <table class="w-full">
          <thead class="bg-[var(--iluria-color-surface-hover)] border-b border-[var(--iluria-color-border)]">
            <tr>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ t('seoProductVariation.image') }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ selectedSeoAttribute }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ t('seoProductVariation.metaTitle') }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ t('seoProductVariation.urlSlug') }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ t('seoProductVariation.metaDescription') }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ t('seoProductVariation.status') }}
              </th>
              <th class="px-4 py-3 text-left text-xs font-medium text-[var(--iluria-color-text-secondary)] uppercase tracking-wider">
                {{ t('seoProductVariation.actions') }}
              </th>
            </tr>
          </thead>

          <tbody class="bg-[var(--iluria-color-surface)] divide-y divide-[var(--iluria-color-border)]">
            <tr v-for="value in attributeValues" :key="value" class="hover:bg-[var(--iluria-color-hover)]">
              <!-- Image -->
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="relative">
                  <div class="w-12 h-12 rounded-md border flex items-center justify-center overflow-hidden"
                       :class="getVariationImage(value) ? 'border-gray-200' : 'border-red-300 bg-red-50'">
                    <img 
                      v-if="getVariationImage(value)" 
                      :src="getVariationImage(value)" 
                      :alt="value"
                      class="w-full h-full object-cover"
                    />
                    <div 
                      v-else 
                      class="w-8 h-8 rounded flex items-center justify-center"
                      :style="{ backgroundColor: getColorValue(value) }"
                    >
                      <span class="text-white text-xs">!</span>
                    </div>
                  </div>
                  <!-- Warning icon for non-SEO specific images -->
                  <div 
                    v-if="hasVariationImageButNotSeoImage(value)"
                    class="absolute -top-1 -right-1 w-4 h-4 bg-yellow-500 rounded-full flex items-center justify-center"
                    :title="t('seoProductVariation.imageFromVariationWarning')"
                  >
                    <span class="text-white text-xs">!</span>
                  </div>
                </div>
              </td>
              
              <!-- Attribute Value -->
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div 
                    class="w-4 h-4 rounded-full mr-2 border border-[var(--iluria-color-border)]"
                    :style="{ backgroundColor: getColorValue(value) }"
                  ></div>
                  <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">{{ value }}</span>
                </div>
              </td>
              
              <!-- Meta Title -->
              <td class="px-4 py-4">
                <div class="text-sm text-[var(--iluria-color-text-primary)] max-w-xs">
                  {{ getDisplayValue(value, 'metaTitle') || getInheritedValue(value, 'metaTitle') }}
                </div>
                <div v-if="!getDisplayValue(value, 'metaTitle')" class="text-xs text-[var(--iluria-color-text-secondary)] mt-1">
                  (Gerado automaticamente)
                </div>
              </td>
              
              <!-- URL -->
              <td class="px-4 py-4">
                <div class="text-sm text-[var(--iluria-color-primary)] max-w-xs truncate">
                  {{ getDisplayValue(value, 'urlSlug') || getInheritedValue(value, 'urlSlug') }}
                </div>
                <div v-if="!getDisplayValue(value, 'urlSlug')" class="text-xs text-[var(--iluria-color-text-secondary)] mt-1">
                  (Gerado automaticamente)
                </div>
              </td>
              
              <!-- Meta Description -->
              <td class="px-4 py-4">
                <div class="text-sm text-[var(--iluria-color-text-primary)] max-w-xs">
                  {{ cleanHtmlText(getDisplayValue(value, 'metaDescription') || getInheritedValue(value, 'metaDescription')) }}
                </div>
                <div v-if="!getDisplayValue(value, 'metaDescription')" class="text-xs text-[var(--iluria-color-text-secondary)] mt-1">
                  (Gerado automaticamente)
                </div>
              </td>
              
              <!-- Status -->
              <td class="px-4 py-4 whitespace-nowrap">
                <div class="flex items-center">
                  <div 
                    class="w-2 h-2 rounded-full mr-2"
                    :class="getSeoStatusColor(value)"
                  ></div>
                  <span 
                    class="text-xs font-medium"
                    :class="getSeoStatusTextClass(value)"
                  >
                    {{ getSeoStatus(value) }}
                  </span>
                </div>
                <!-- Warning message for using variation image -->
                <div v-if="hasVariationImageButNotSeoImage(value)" class="text-xs text-yellow-600 mt-1">
                  {{ t('seoProductVariation.usingVariationImage') }}
                </div>
              </td>
              
              <!-- Actions -->
              <td class="px-4 py-4 whitespace-nowrap">
                <IluriaButton 
                  @click="editVariation(value)"
                  color="primary"
                  size="small"
                >
                  {{ t('seoProductVariation.editButton') }}
                </IluriaButton>
              </td>
            </tr>
          </tbody>
        </table>
      </div>
    </div>

    <div v-if="showImageAdvice" class="p-4 border rounded-lg border-[var(--iluria-color-warning)] bg-[var(--iluria-color-warning-bg)]">
      <IluriaLabel class="text-sm text-[var(--iluria-color-warning)] cursor-pointer">
        {{ t('seoProductVariation.imageAdvice') }}
      </IluriaLabel>
    </div>

    <!-- Empty State -->
    <div v-else-if="selectedSeoAttribute && attributeValues.length === 0" class="text-center py-8">
      <p class="text-[var(--iluria-color-text-secondary)]">{{ t('seoProductVariation.noVariationsFound') }}</p>
    </div>

    <!-- SEO Variation Edit Modal -->
    <SeoProductVariationModal
      v-model="editingVariation"
      :product-id="props.productId"
      :attribute="editingVariation?.attribute || ''"
      :attribute-value="editingVariation?.attributeValue || ''"
      :variation-data="editingVariation?.data || {}"
      @save="handleSaveVariation"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaTitle from '@/components/iluria/IluriaTitle.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { useI18n } from 'vue-i18n'
import SeoProductVariationModal from '@/components/products/SeoProductVariationModal.vue'

const { t } = useI18n()

const props = defineProps({
  modelValue: {
    type: String,
    default: ''
  },
  seoVariations: {
    type: Array,
    default: () => []
  },
  productVariations: {
    type: Array,
    default: () => []
  },
  productName: {
    type: String,
    default: ''
  },
  productShortDescription: {
    type: String,
    default: ''
  },
  productId: {
    type: String,
    default: ''
  },
  defaultMetaTitle: {
    type: String,
    default: ''
  },
  defaultMetaDescription: {
    type: String,
    default: ''
  },
  defaultUrlSlug: {
    type: String,
    default: ''
  },
  hasVariationStructureChanged: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue', 'update:seoVariations', 'update:productSeoToNull', 'edit-variation'])

const selectedSeoAttribute = ref(props.modelValue || '')

watch(() => props.modelValue, (newValue) => {
  if (newValue && newValue !== selectedSeoAttribute.value) {
    selectedSeoAttribute.value = newValue
    nextTick(() => {
      initializeSeoConfig()
    })
  }
}, { immediate: true })

const seoConfig = ref({})
const isInitializing = ref(false) 
const showImageAdvice = ref(false)

const editingVariation = ref(null)

const availableAttributes = computed(() => {
  if (!props.productVariations || props.productVariations.length === 0) {
    return []
  }

  const attributeSet = new Set()
  
  props.productVariations.forEach(variation => {
    if (variation.options) {
      Object.keys(variation.options).forEach(attr => {
        attributeSet.add(attr)
      })
    }
  })

  return Array.from(attributeSet).sort()
})

const seoAttributeOptions = computed(() => {
  const options = availableAttributes.value.map(attr => ({
    label: attr,
    value: attr
  }))
  
  if (!selectedSeoAttribute.value || selectedSeoAttribute.value === '') {
    return [
      { label: t('seoProductVariation.selectAttribute'), value: '' },
      ...options
    ]
  }
  
  return options
})

const attributeValues = computed(() => {
  if (!selectedSeoAttribute.value || !props.productVariations) {
    return []
  }

  const valueSet = new Set()
  
  props.productVariations.forEach(variation => {
    if (variation.options && variation.options[selectedSeoAttribute.value]) {
      valueSet.add(variation.options[selectedSeoAttribute.value])
    }
  })

  return Array.from(valueSet).sort()
})

// Computed to show SEO image optimization notice
const showSeoImageOptimizationNotice = computed(() => {
  if (!selectedSeoAttribute.value || attributeValues.value.length === 0) {
    return false
  }
  
  return attributeValues.value.some(value => hasVariationImageButNotSeoImage(value))
})

function cleanHtmlText(text) {
  if (!text) return '';
  
  return text
    .replace(/<[^>]*>/g, '')
    .replace(/&nbsp;/g, ' ')
    .replace(/&amp;/g, '&')
    .replace(/&lt;/g, '<')
    .replace(/&gt;/g, '>')
    .replace(/&quot;/g, '"')
    .replace(/&#39;/g, "'")
    .replace(/\s+/g, ' ')
    .trim();
}

function generateSlug(text) {
  if (!text) return '';
  
  let slug = text
    .toLowerCase()
    .trim()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+$/g, '');
  
  slug = '/' + slug;
  
  return slug;
}

function generateDefaultMetaTitle(value) {
  if (!value) return ''
  
  const baseName = props.productName || props.defaultMetaTitle || ''
  if (baseName) {
    return `${baseName} ${value}`
  }
  
  return `${selectedSeoAttribute.value} ${value}`
}

function generateDefaultMetaDescription(value) {
  if (!value) return ''
  
  const baseDescription = cleanHtmlText(props.productShortDescription || props.defaultMetaDescription || '')
  if (baseDescription) {
    return `${baseDescription} ${value}`
  }
  
  return `${t('seoProductVariation.metaDescriptionFallback')} ${selectedSeoAttribute.value.toLowerCase()} ${value.toLowerCase()}`
}

function generateDefaultSlug(value) {
  if (!value) return '';
  
  const baseName = props.productName || props.defaultMetaTitle || ''
  let baseText = baseName ? `${baseName} ${value}` : `${selectedSeoAttribute.value} ${value}`
  
  return generateSlug(baseText);
}

function getColorValue(value) {
  const colorMap = {
    'Azul': '#3B82F6',
    'Blue': '#3B82F6',
    'Vermelho': '#EF4444', 
    'Red': '#EF4444',
    'Verde': '#10B981',
    'Green': '#10B981',
    'Amarelo': '#F59E0B',
    'Yellow': '#F59E0B',
    'Roxo': '#8B5CF6',
    'Purple': '#8B5CF6',
    'Rosa': '#EC4899',
    'Pink': '#EC4899',
    'Laranja': '#F97316',
    'Orange': '#F97316',
    'Preto': '#1F2937',
    'Black': '#1F2937',
    'Branco': '#F9FAFB',
    'White': '#F9FAFB',
    'Cinza': '#6B7280',
    'Gray': '#6B7280'
  }
  
  return colorMap[value] || '#6B7280'
}

function getVariationImage(value) {
  // 1. Verificar se há imagem SEO customizada no config local
  const config = seoConfig.value[value]
  if (config && typeof config.seoImage === 'string' && config.seoImage !== '' && config.seoImage !== 'REMOVED_BY_USER') {
    return config.seoImage
  }
  
  // 2. Verificar se há imagem SEO no backend
  const seoVariation = props.seoVariations.find(
    seo => seo.attributeValue === value && seo.attribute === selectedSeoAttribute.value
  )
  if (seoVariation && typeof seoVariation.seoImage === 'string' && seoVariation.seoImage !== '' && seoVariation.seoImage !== 'REMOVED_BY_USER') {
    return seoVariation.seoImage
  }
  
  // 3. Usar a primeira foto da variação como fallback
  if (!props.productVariations) return null
  
  const variation = props.productVariations.find(v => 
    v.options && v.options[selectedSeoAttribute.value] === value
  )
  
  if (variation?.photos && variation.photos.length > 0) {
    const firstPhoto = variation.photos[0]
    // Suportar tanto File objects (cache local) quanto URLs do servidor
    if (firstPhoto instanceof File) {
      // Se é um arquivo local, criar URL temporária
      return URL.createObjectURL(firstPhoto)
    } else if (firstPhoto && firstPhoto.url) {
      return firstPhoto.url
    } else if (typeof firstPhoto === 'string') {
      // Caso seja apenas uma string com a URL
      return firstPhoto
    }
  }
  
  return null
}

function getSeoImageForVariation(value) {
  const config = seoConfig.value[value]
  if (config && typeof config.seoImage === 'string' && config.seoImage !== '' && config.seoImage !== 'REMOVED_BY_USER' && config.seoImageIsCustom) {
    return config.seoImage
  }
  const seoVariation = props.seoVariations.find(
    seo => seo.attributeValue === value && seo.attribute === selectedSeoAttribute.value
  )
  if (seoVariation && typeof seoVariation.seoImage === 'string' && seoVariation.seoImage !== '' && seoVariation.seoImage !== 'REMOVED_BY_USER' && seoVariation.seoImageIsCustom) {
    return seoVariation.seoImage
  }
  return null
}

function generateDefaultSeoImage(value) {
  if (!props.productVariations) return null
  
  const variation = props.productVariations.find(v => 
    v.options && v.options[selectedSeoAttribute.value] === value
  )
  
  if (variation?.photos && variation.photos.length > 0) {
    const firstPhoto = variation.photos[0]
    if (firstPhoto && !(firstPhoto instanceof File) && firstPhoto.url) {
      return firstPhoto.url
    }
  }
  
  return null
}

// Function to check if variation has image but no SEO-specific image
function hasVariationImageButNotSeoImage(value) {
  const hasSeoImage = getSeoImageForVariation(value)
  const hasVariationImage = generateDefaultSeoImage(value) || getVariationImageFromFiles(value)
  
  return !hasSeoImage && hasVariationImage
}

// Function to get variation image from uploaded files (including File objects)
function getVariationImageFromFiles(value) {
  if (!props.productVariations) return null
  
  const variation = props.productVariations.find(v => 
    v.options && v.options[selectedSeoAttribute.value] === value
  )
  
  if (variation?.photos && variation.photos.length > 0) {
    return variation.photos[0] // Return any type of photo (File, URL, object)
  }
  
  return null
}

// Function to check if there's ANY image available (SEO or variation)
function hasAnyImageAvailable(value) {
  const seoImage = getSeoImageForVariation(value)
  const variationImage = getVariationImageFromFiles(value)
  
  return !!(seoImage || variationImage)
}

function initializeSeoConfig() {
  if (isInitializing.value) return
  
  if (!selectedSeoAttribute.value || attributeValues.value.length === 0) {
    seoConfig.value = {}
    return
  }
  
  isInitializing.value = true
  
  const newConfig = {}
  const newSeoVariations = []
  
  attributeValues.value.forEach(value => {
    const existingConfig = props.seoVariations.find(
      seo => seo.attributeValue === value && seo.attribute === selectedSeoAttribute.value
    )
    
    const defaultImage = generateDefaultSeoImage(value)
    const seoVariation = {
      attribute: selectedSeoAttribute.value,
      attributeValue: value,
      metaTitle: existingConfig?.metaTitle ?? generateDefaultMetaTitle(value),
      metaDescription: existingConfig?.metaDescription ? cleanHtmlText(existingConfig.metaDescription) : (existingConfig?.metaDescription ?? generateDefaultMetaDescription(value)),
      urlSlug: existingConfig?.urlSlug ?? generateDefaultSlug(value),
      seoImage: existingConfig?.seoImage ?? defaultImage ?? '',
      seoImageIsCustom: existingConfig?.seoImageIsCustom ?? false
    }
    
    newSeoVariations.push(seoVariation)
    
    newConfig[value] = {
      metaTitle: seoVariation.metaTitle,
      metaDescription: seoVariation.metaDescription,
      urlSlug: seoVariation.urlSlug,
      seoImage: seoVariation.seoImage,
      seoImageIsCustom: seoVariation.seoImageIsCustom
    }
  })
  
  seoConfig.value = newConfig
  
  const needsUpdate = selectedSeoAttribute.value && attributeValues.value.length > 0
  
  if (needsUpdate) {
    const filteredVariations = props.seoVariations.filter(
      seo => seo.attribute !== selectedSeoAttribute.value
    )
    
    const updatedSeoVariations = [...filteredVariations, ...newSeoVariations]
    
    const hasChanges = JSON.stringify(updatedSeoVariations) !== JSON.stringify(props.seoVariations)
    
    if (hasChanges) {
      emit('update:seoVariations', updatedSeoVariations)
      emit('update:productSeoToNull')
    }
  }
  
  setTimeout(() => {
    isInitializing.value = false
  }, 10)
}

function getDisplayValue(value, field) {
  const config = seoConfig.value[value]
  return config ? config[field] : ''
}

function getInheritedValue(value, field) {
  const generators = {
    'metaTitle': generateDefaultMetaTitle,
    'metaDescription': generateDefaultMetaDescription,
    'urlSlug': generateDefaultSlug,
    'seoImage': generateDefaultSeoImage
  }
  
  return generators[field] ? generators[field](value) : ''
}

function editVariation(value) {
  const currentConfig = seoConfig.value[value] || {}
  
  const cleanedCurrentConfig = {
    ...currentConfig,
    metaDescription: currentConfig.metaDescription ? cleanHtmlText(currentConfig.metaDescription) : currentConfig.metaDescription
  }
  
  const variationData = {
    attribute: selectedSeoAttribute.value,
    attributeValue: value,
    currentConfig: cleanedCurrentConfig,
    defaultValues: {
      metaTitle: generateDefaultMetaTitle(value),
      metaDescription: generateDefaultMetaDescription(value),
      urlSlug: generateDefaultSlug(value),
      seoImage: generateDefaultSeoImage(value)
    },
    productName: props.productName,
    productId: props.productId
  }
  
  editingVariation.value = {
    attribute: selectedSeoAttribute.value,
    attributeValue: value,
    data: variationData
  }
}

function handleCloseEditModal() {
  editingVariation.value = null
}

function handleSaveVariation(updatedVariation) {
  applySavedVariationData(updatedVariation)
  editingVariation.value = null
}

function getSeoStatus(value) {
  const config = seoConfig.value[value]
  
  const hasMetaTitle = config?.metaTitle && config.metaTitle.trim() !== ''
  const hasMetaDescription = config?.metaDescription && config.metaDescription.trim() !== ''
  const hasUrlSlug = config?.urlSlug && config.urlSlug.trim() !== ''
  
  // Modified: Now considers both SEO-specific images AND variation images
  const hasAnyImage = hasAnyImageAvailable(value)
  
  const allRequiredFieldsFilled = hasMetaTitle && hasMetaDescription && hasUrlSlug && hasAnyImage
  
  return allRequiredFieldsFilled ? t('seoProductVariation.complete') : t('seoProductVariation.incomplete')
}

function getSeoStatusColor(value) {
  const status = getSeoStatus(value)
  return status === t('seoProductVariation.complete') ? 'bg-green-500' : 'bg-red-500'
}

function getSeoStatusTextClass(value) {
  const status = getSeoStatus(value)
  return status === t('seoProductVariation.complete') ? 'text-green-700' : 'text-red-700'
}

function updateSeoVariation(attributeValue, field, value) {
  const currentSeoVariations = [...props.seoVariations]
  const existingIndex = currentSeoVariations.findIndex(
    seo => seo.attributeValue === attributeValue && seo.attribute === selectedSeoAttribute.value
  )
  
  if (existingIndex >= 0) {
    currentSeoVariations[existingIndex][field] = value
  } else {
    currentSeoVariations.push({
      attribute: selectedSeoAttribute.value,
      attributeValue: attributeValue,
      metaTitle: field === 'metaTitle' ? value : null,
      metaDescription: field === 'metaDescription' ? value : null,
      urlSlug: field === 'urlSlug' ? value : null,
      seoImage: field === 'seoImage' ? value : null
    })
  }
  
  emit('update:seoVariations', currentSeoVariations)
  
  if (!seoConfig.value[attributeValue]) {
    seoConfig.value[attributeValue] = {}
  }
  seoConfig.value[attributeValue][field] = value
}

function onSeoAttributeChange() {
  emit('update:modelValue', selectedSeoAttribute.value)
  initializeSeoConfig()
}

function updateSeoImagesFromVariations() {
  if (!selectedSeoAttribute.value || !attributeValues.value.length) return
  
  let hasChanges = false
  const updatedSeoVariations = [...props.seoVariations]
  
  attributeValues.value.forEach(value => {
    const userCustomImage = getSeoImageForVariation(value)
    
    if (userCustomImage) {
      return
    }
    
    const variationPhoto = generateDefaultSeoImage(value)
    
    const existingIndex = updatedSeoVariations.findIndex(
      seo => seo.attributeValue === value && seo.attribute === selectedSeoAttribute.value
    )
    
    if (existingIndex >= 0) {
      const currentSeoImage = updatedSeoVariations[existingIndex].seoImage
      const isCustomImage = updatedSeoVariations[existingIndex].seoImageIsCustom
      
      if (!isCustomImage && currentSeoImage !== variationPhoto) {
        updatedSeoVariations[existingIndex].seoImage = variationPhoto
        updatedSeoVariations[existingIndex].seoImageIsCustom = false
        hasChanges = true
        
        if (seoConfig.value[value]) {
          seoConfig.value[value].seoImage = variationPhoto
          seoConfig.value[value].seoImageIsCustom = false
        }
      }
    }
  })
  
  if (hasChanges) {
    emit('update:seoVariations', updatedSeoVariations)
  }
}

let photoUpdateTimeout = null
watch(() => props.productVariations?.map(v => v.photos), () => {
  if (!selectedSeoAttribute.value || !attributeValues.value.length) return
  
  if (photoUpdateTimeout) {
    clearTimeout(photoUpdateTimeout)
  }
  
  photoUpdateTimeout = setTimeout(() => {
    updateSeoImagesFromVariations()
  }, 100)
}, { deep: true })

watch(() => [selectedSeoAttribute.value, attributeValues.value.length], () => {
  if (selectedSeoAttribute.value && attributeValues.value.length > 0) {
  initializeSeoConfig()
  }
}, { immediate: true })

watch(() => props.hasVariationStructureChanged, (newValue) => {
  if (newValue && selectedSeoAttribute.value && attributeValues.value.length > 0) {
    showImageAdvice.value = true
  } else {
    showImageAdvice.value = false
  }
})

watch(() => props.modelValue, (newValue, oldValue) => {
  if (!newValue && oldValue) {
    selectedSeoAttribute.value = ''
    seoConfig.value = {}
    if (availableAttributes.value.length > 0) {
      nextTick(() => {
        initializeSeoConfig()
      })
    }
  }
  else if (newValue && newValue !== selectedSeoAttribute.value) {
    selectedSeoAttribute.value = newValue
    nextTick(() => {
      initializeSeoConfig()
    })
  }
})

watch(() => props.productVariations, (newVariations, oldVariations) => {
  const oldLength = oldVariations?.length || 0
  const newLength = newVariations?.length || 0
  
  if (newVariations && newLength > 0) {
    if (!selectedSeoAttribute.value || oldLength !== newLength) {
      seoConfig.value = {}
      nextTick(() => {
        initializeSeoConfig()
      })
    }
  }
}, { deep: true })

watch(() => [selectedSeoAttribute.value, props.productVariations?.length], ([newAttribute, newVariationsCount], [oldAttribute, oldVariationsCount]) => {
  if (newAttribute && newVariationsCount > 0 && Object.keys(seoConfig.value).length === 0) {
    nextTick(() => {
      initializeSeoConfig()
    })
  }
  else if (newAttribute && newAttribute !== oldAttribute && newVariationsCount > 0) {
    seoConfig.value = {}
    nextTick(() => {
      initializeSeoConfig()
    })
  }
}, { immediate: false })

onMounted(() => {
  initializeSeoConfig()
})

function applySavedVariationData(updatedVariation) {
  const currentSeoVariations = [...props.seoVariations]
  const existingIndex = currentSeoVariations.findIndex(
    seo => seo.attributeValue === updatedVariation.attributeValue && seo.attribute === updatedVariation.attribute
  )
  
  const cleanedVariation = { 
    ...updatedVariation,
    metaDescription: updatedVariation.metaDescription ? cleanHtmlText(updatedVariation.metaDescription) : updatedVariation.metaDescription
  }
  
  if (cleanedVariation.seoImage === undefined || cleanedVariation.seoImage === '') {
    delete cleanedVariation.seoImage
  }
  
  const isCustomImage = cleanedVariation.seoImage !== undefined && cleanedVariation.seoImage !== generateDefaultSeoImage(updatedVariation.attributeValue)
  
  if (existingIndex >= 0) {
    currentSeoVariations[existingIndex] = {
      ...currentSeoVariations[existingIndex],
      metaTitle: cleanedVariation.metaTitle,
      metaDescription: cleanedVariation.metaDescription,
      urlSlug: cleanedVariation.urlSlug
    }
    if (cleanedVariation.seoImage !== undefined) {
      currentSeoVariations[existingIndex].seoImage = cleanedVariation.seoImage
      currentSeoVariations[existingIndex].seoImageIsCustom = isCustomImage
    } else {
      delete currentSeoVariations[existingIndex].seoImage
      currentSeoVariations[existingIndex].seoImageIsCustom = false
    }
  } else {
    const newVariation = { ...cleanedVariation }
    if (cleanedVariation.seoImage !== undefined) {
      newVariation.seoImageIsCustom = isCustomImage
    } else {
      newVariation.seoImageIsCustom = false
    }
    currentSeoVariations.push(newVariation)
  }
  if (!seoConfig.value[updatedVariation.attributeValue]) {
    seoConfig.value[updatedVariation.attributeValue] = {}
  }
  seoConfig.value[updatedVariation.attributeValue] = {
    metaTitle: cleanedVariation.metaTitle,
    metaDescription: cleanedVariation.metaDescription,
    urlSlug: cleanedVariation.urlSlug
  }
  if (cleanedVariation.seoImage !== undefined) {
    seoConfig.value[updatedVariation.attributeValue].seoImage = cleanedVariation.seoImage
    seoConfig.value[updatedVariation.attributeValue].seoImageIsCustom = isCustomImage
  } else {
    seoConfig.value[updatedVariation.attributeValue].seoImageIsCustom = false
  }
  if (!selectedSeoAttribute.value && updatedVariation.attribute) {
    selectedSeoAttribute.value = updatedVariation.attribute
    emit('update:modelValue', updatedVariation.attribute)
  }
  emit('update:seoVariations', currentSeoVariations)
}

onUnmounted(() => {
  if (photoUpdateTimeout) {
    clearTimeout(photoUpdateTimeout)
  }
})

defineExpose({
  updateSeoVariation,
  initializeSeoConfig
})
</script>
