import "@hugeicons-pro/core-bulk-rounded";
import "@/assets/main.css";
import "cropperjs/dist/cropper.css";

import { createApp } from "vue";
import { createPinia } from "pinia";
import piniaPluginPersistedstate from "pinia-plugin-persistedstate";

import PrimeVue from "primevue/config";
import Tooltip from 'primevue/tooltip';
import InputMask from "primevue/inputmask";
import ConfirmationService from 'primevue/confirmationservice';
import { Form, FormField } from '@primevue/forms';

import Aura from '@primeuix/themes/aura'
import ToastService from 'primevue/toastservice';
import Button from 'primevue/button';

import i18n from "@/includes/i18n.js";

import Index from "@/views/index/index.vue";
import Router from "@/views/index/router.js";

import NavBar from "@/components/layout/NavBar.vue";
import ViewContainer from "@/components/layout/ViewContainer.vue";

import permissionDirective from "@/directives/permission.js";

import "@/config/axios.config";

const pinia = createPinia();
pinia.use(piniaPluginPersistedstate);

const app = createApp(Index);

app.use(PrimeVue, {
  theme: {
    preset: Aura,
    options: {
      darkModeSelector: false,
      cssLayer: {
        name: 'primevue',
        order: 'theme, base, primevue'
    }
    }
  }
});

app.use(pinia);
app.use(Router);
app.use(i18n);
app.use(ToastService);
app.directive('tooltip', Tooltip);
app.use(permissionDirective);
app.use(ConfirmationService);
app.component("ViewContainer", ViewContainer);
app.component("NavBar", NavBar);
app.component("InputMask", InputMask);
app.component("Form", Form);
app.component("FormField", FormField);
app.component("Button", Button);

app.mount("#app");
