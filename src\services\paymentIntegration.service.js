import axios from 'axios';
import { API_URLS } from '../config/api.config';

class PaymentIntegrationService {
    #endpoint;

    constructor() {
        this.#endpoint = `${API_URLS.PAYMENT_INTEGRATION_BASE_URL}`;
    }

    // Mercado Pago
    authMercadoPago(storeId) {
        try {
            return `${this.#endpoint}/mercadopago/auth?storeIdParam=${storeId}`;
        } catch (error) {
            console.error('Error fetching auth mercado pago:', error);
            throw error;
        }
    }

    async disconnectMercadoPago(storeId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/mercadopago/delete?storeId=${storeId}`);
            return response;
        } catch (error) {
            console.error('Error disconnecting mercado pago:', error);
            throw error;
        }
    }

    async getMercadoPagoStatus(storeId) {
        try {
            const response = await axios.get(`${this.#endpoint}/mercadopago/status?storeId=${storeId}`);
            return response.data;
        } catch (error) {
            console.error('Error getting mercado pago status:', error);
            throw error;
        }
    }

    // PagBank
    authPagBank(storeId) {
        try {
            return `${this.#endpoint}/pagbank/auth?storeIdParam=${storeId}`;
        } catch (error) {
            console.error('Error fetching auth pagbank:', error);
            throw error;
        }
    }

    async disconnectPagBank(storeId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/pagbank/delete?storeId=${storeId}`);
            return response;
        } catch (error) {
            console.error('Error disconnecting pagbank:', error);
            throw error;
        }
    }

    async getPagBankStatus(storeId) {
        try {
            const response = await axios.get(`${this.#endpoint}/pagbank/status?storeId=${storeId}`);
            return response.data;
        } catch (error) {
            console.error('Error getting pagbank status:', error);
            throw error;
        }
    }

    // PayPal
    authPayPal(storeId) {
        try {
            return `${this.#endpoint}/paypal/auth?storeIdParam=${storeId}`;
        } catch (error) {
            console.error('Error fetching auth paypal:', error);
            throw error;
        }
    }

    async disconnectPayPal(storeId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/paypal/delete?storeId=${storeId}`);
            return response;
        } catch (error) {
            console.error('Error disconnecting paypal:', error);
            throw error;
        }
    }

    async getPayPalStatus(storeId) {
        try {
            const response = await axios.get(`${this.#endpoint}/paypal/status?storeId=${storeId}`);
            return response.data;
        } catch (error) {
            console.error('Error getting paypal status:', error);
            throw error;
        }
    }

    // Stripe
    authStripe(storeId) {
        try {
            return `${this.#endpoint}/stripe/auth?storeIdParam=${storeId}`;
        } catch (error) {
            console.error('Error fetching auth stripe:', error);
            throw error;
        }
    }

    async disconnectStripe(storeId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/stripe/delete?storeId=${storeId}`);
            return response;
        } catch (error) {
            console.error('Error disconnecting stripe:', error);
            throw error;
        }
    }

    async getStripeStatus(storeId) {
        try {
            const response = await axios.get(`${this.#endpoint}/stripe/status?storeId=${storeId}`);
            return response.data;
        } catch (error) {
            console.error('Error getting stripe status:', error);
            throw error;
        }
    }
}

export default PaymentIntegrationService;