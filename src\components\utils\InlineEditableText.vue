<template>
  <div class="inline-editable">
    <!-- Display mode -->
    <component
      v-if="!isEditing"
      :is="tag"
      :class="[textClass, 'editable-text']"
    >
      {{ modelValue }}
    </component>
    
    <!-- Edit mode -->
    <input
      v-else
      ref="inputRef"
      v-model="editingValue"
      :class="[inputClass, 'editable-input']"
      :placeholder="placeholder"
      :maxlength="maxLength"
      @blur="handleBlur"
      @keydown.enter="save"
      @keydown.escape="cancel"
      @input="validateInput"
    />
  </div>
</template>

<script setup>
import { ref, computed, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

// Props
const props = defineProps({
  modelValue: {
    type: String,
    required: true
  },
  tag: {
    type: String,
    default: 'span'
  },
  textClass: {
    type: String,
    default: ''
  },
  inputClass: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  maxLength: {
    type: Number,
    default: 100
  },
  minLength: {
    type: Number,
    default: 1
  },
  disabled: {
    type: Boolean,
    default: false
  },
  validateOnBlur: {
    type: Boolean,
    default: true
  },
  autoSave: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits([
  'update:modelValue',
  'save',
  'cancel',
  'start-edit',
  'validation-error'
])

// State
const isEditing = ref(false)
const editingValue = ref('')
const inputRef = ref(null)
const hasError = ref(false)
const errorMessage = ref('')

// Computed
const canEdit = computed(() => !props.disabled)

// Methods
const startEditing = async () => {
  if (!canEdit.value) return
  
  isEditing.value = true
  editingValue.value = props.modelValue
  hasError.value = false
  errorMessage.value = ''
  
  emit('start-edit')
  
  // Focus and select text after DOM update
  await nextTick()
  if (inputRef.value) {
    inputRef.value.focus()
    inputRef.value.select()
  }
}

const validateInput = () => {
  // Reset error state
  hasError.value = false
  errorMessage.value = ''

  // Sempre retorna true - sem validações obrigatórias
  return true
}

const save = async () => {
  const trimmedValue = editingValue.value.trim()

  // Se o campo estiver vazio, apenas cancela a edição
  if (!trimmedValue) {
    cancel()
    return
  }

  // If no changes, just cancel
  if (trimmedValue === props.modelValue) {
    cancel()
    return
  }

  try {
    // Emit save event with new value
    emit('save', trimmedValue)

    // Update model value if autoSave is enabled
    if (props.autoSave) {
      emit('update:modelValue', trimmedValue)
    }

    // Exit editing mode
    isEditing.value = false
  } catch (error) {
    console.error('Error saving inline edit:', error)
    emit('validation-error', t('general.saveError'))
    // Keep editing mode active on error
  }
}

const handleBlur = () => {
  if (props.validateOnBlur) {
    save()
  } else {
    cancel()
  }
}

const cancel = () => {
  isEditing.value = false
  editingValue.value = ''
  hasError.value = false
  errorMessage.value = ''
  emit('cancel')
}

// Watch for external value changes
watch(() => props.modelValue, (newValue) => {
  if (!isEditing.value) {
    editingValue.value = newValue
  }
})

// Expose methods for external access
defineExpose({
  startEditing
})
</script>

<style scoped>
.inline-editable {
  position: relative;
  display: inline-block;
  width: 100%;
}

.editable-text {
  cursor: inherit;
  transition: all 0.2s ease;
  border-radius: 4px;
  padding: 2px 4px;
  margin: -2px -4px;
  outline: none;
  position: relative;
  font-size: inherit !important;
  font-weight: inherit !important;
  color: inherit !important;
  line-height: inherit !important;
  letter-spacing: inherit !important;
}

/* Removidos efeitos de hover e focus - edição apenas programática */

/* Ícone de edição removido - edição apenas via botão "Renomear" */

.editable-input {
  width: 100%;
  background: var(--iluria-color-surface);
  border: 2px solid var(--iluria-color-primary);
  border-radius: 6px;
  padding: 4px 8px;
  outline: none;
  font-family: inherit;
  font-size: inherit;
  font-weight: inherit;
  color: inherit;
  line-height: inherit;
  letter-spacing: inherit;
  margin: inherit;
  transition: all 0.2s ease;
  box-shadow: 0 0 0 3px rgba(var(--iluria-color-primary-rgb), 0.1);
}

.editable-input:focus {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px rgba(var(--iluria-color-primary-rgb), 0.2);
}

.editable-input.error {
  border-color: var(--iluria-color-danger);
  box-shadow: 0 0 0 3px rgba(var(--iluria-color-danger-rgb), 0.1);
}

/* CSS para estado desabilitado removido - não mais necessário */
</style>