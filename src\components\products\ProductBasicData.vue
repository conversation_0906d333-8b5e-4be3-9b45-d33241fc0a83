<template>
  <div class="space-y-4">
    <!-- Nome do produto e SKU lado a lado -->
    <!-- <div class="gap-3 flex flex-col md:flex-row w-full"> -->
    <div class="flex-1">
      <IluriaInputText id="name" name="name" :label="labelName" v-model="form.name"
        :formContext="props.formContext?.name" />
    </div>

    <!-- </div> -->

    <!-- URL Slug -->
    <div class="form-group w-full">
      <IluriaInputText 
        id="slug" 
        name="slug" 
        :label="t('seo.slug')" 
        v-model="form.seo.slug" 
        :formContext="props.formContext?.seo?.slug"
        @input="formatSlug"
        prefix="/"
      />
    </div>

    <!-- Descrição do produto -->
    <div class="form-group w-full" v-if="props.description">
      <IluriaEditor id="description" :label="t('description')" height="150px" v-model="form.description"
        :placeholder="t('product.description')" hideToggle />
      <Message v-if="props.formContext?.description?.invalid">
        {{ props.formContext.description.error?.message }}
      </Message>
    </div>

  </div>
</template>

<script setup>
import { inject, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import Message from 'primevue/message'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaEditor from '@/components/editor/IluriaEditor.vue';

const { t } = useI18n();

// Inject the product form data from parent component
const form = inject('productForm');

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  },
  description: {
    type: Boolean,
    default: true
  },
  labelName: {
    type: String,
    default: ''
  }
});

const labelName = computed(() => props.labelName || t('product.name'));

function formatSlug() {
  if (form.seo && form.seo.slug) {
    form.seo.slug = form.seo.slug
      .toLowerCase()
      .replace(/\s+/g, '-')
      .replace(/[^\w\-]+/g, '')
      .replace(/\-\-+/g, '-')
      .replace(/^-+/, '')
      .replace(/-+$/, '')
  }
}

// Define validation rules
const validationRules = {
  name: {},
  sku: {}
};

// Expose validation rules
defineExpose({ validationRules });
</script>
