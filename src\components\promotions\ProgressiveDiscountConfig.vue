<template>
  <div class="mb-6">
    <IluriaRadioGroup
      id="progressive-type"
      v-model="progressiveType"
      :options="[
        { value: 'QUANTITY', label: t('promotions.editor.discountByQuantity') },
        { value: 'ORDER_VALUE', label: t('promotions.editor.discountByOrderValue') }
      ]"
      :label="t('promotions.editor.progressiveDiscountType')"
      direction="horizontal"
      @update:modelValue="setProgressiveType"
      class="mb-6"
    />

    <!-- Progressive Discount Tiers -->
    <div v-if="progressiveType === 'QUANTITY'">
      <div v-for="(tier, index) in progressiveTiers" :key="index" class="flex items-center gap-3 mb-4">
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.minQuantity') }}</IluriaLabel>
          <IluriaInputText
            v-model.number="tier.minQuantity"
            type="number"
            :min="index === 0 ? 1 : progressiveTiers[index-1].minQuantity + 1"
            :step="1"
            :placeholder="t('promotions.editor.productsPlaceholder')"
            suffix=" unidade(s)"
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.discount') }}</IluriaLabel>
          <IluriaInputText
            v-model.number="tier.discountValue"
            type="number"
            :min="0"
            :max="100"
            :step="0.01"
            suffix="%"
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex items-end">
          <IluriaButton
            size="small"
            color="ghost"
            :hugeIcon="Delete02Icon"
            @click="removeTier(index)"
            class="p-2"
            :title="t('promotions.editor.removeTier')"
          />
        </div>
      </div>
    </div>

    <div v-else>
      <div v-for="(tier, index) in progressiveTiers" :key="index" class="flex items-center gap-3 mb-4">
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.minOrderValue') }}</IluriaLabel>
          <IluriaInputText
            v-model.number="tier.minValue"
            type="number"
            :min="index === 0 ? 0.01 : progressiveTiers[index-1].minValue + 0.01"
            :step="0.01"
            prefix="R$ "
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.discount') }}</IluriaLabel>
          <IluriaInputText
            v-model.number="tier.discountValue"
            type="number"
            :min="0"
            :max="100"
            :step="0.01"
            suffix="%"
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex items-end">
          <IluriaButton
            size="small"
            color="ghost"
            :hugeIcon="Delete02Icon"
            @click="removeTier(index)"
            class="p-2"
            :title="t('promotions.editor.removeTier')"
          />
        </div>
      </div>
    </div>

    <!-- Add New Tier Button -->
    <IluriaButton
      v-if="progressiveTiers.length < 5"
      color="primary"
      variant="outline"
      :hugeIcon="PlusSignIcon"
      @click="addTier"
      class="mt-2"
    >
      {{ t('promotions.editor.addProgressiveTier') }}
    </IluriaButton>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { Delete02Icon, PlusSignIcon } from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const progressiveType = ref('QUANTITY');
const progressiveTiers = ref([
  { minQuantity: 1, minOrderValue: 0.01, discountValue: 5 }
]);

const setProgressiveType = (type) => {
  progressiveType.value = type;
  updateModelValue();
};

const addTier = () => {
  if (progressiveTiers.value.length < 5) {
    const lastTier = progressiveTiers.value[progressiveTiers.value.length - 1];
    const newTier = {
      minQuantity: lastTier.minQuantity + 1,
      minOrderValue: lastTier.minOrderValue + 50,
      discountValue: lastTier.discountValue + 5
    };
    progressiveTiers.value.push(newTier);
    updateModelValue();
  }
};

const removeTier = (index) => {
  if (progressiveTiers.value.length > 1) {
    progressiveTiers.value.splice(index, 1);
    updateModelValue();
  }
};

const updateTier = (index) => {
  const tier = progressiveTiers.value[index];
  
  if (progressiveType.value === 'QUANTITY') {
    tier.minQuantity = Math.max(1, tier.minQuantity);
    if (index > 0) {
      tier.minQuantity = Math.max(progressiveTiers.value[index-1].minQuantity + 1, tier.minQuantity);
    }
  } else {
    tier.minOrderValue = Math.max(0.01, tier.minOrderValue);
    if (index > 0) {
      tier.minOrderValue = Math.max(progressiveTiers.value[index-1].minOrderValue + 0.01, tier.minOrderValue);
    }
  }
  
  tier.discountValue = Math.min(100, Math.max(0, tier.discountValue));
  
  updateModelValue();
};

const updateModelValue = () => {
  try {
    isUpdatingFromModelChange = true;
    props.modelValue.progressiveType = progressiveType.value;
    props.modelValue.progressiveTiers = JSON.parse(JSON.stringify(progressiveTiers.value));
    emit('update:modelValue', props.modelValue);
  } finally {
    isUpdatingFromModelChange = false;
  }
};

onMounted(() => {
  if (props.modelValue.progressiveType) {
    progressiveType.value = props.modelValue.progressiveType;
  }
  
  if (props.modelValue.progressiveTiers && props.modelValue.progressiveTiers.length > 0) {
    progressiveTiers.value = JSON.parse(JSON.stringify(props.modelValue.progressiveTiers));
  }
});

let isUpdatingFromModelChange = false;

watch(() => props.modelValue.progressiveType, (newVal) => {
  if (isUpdatingFromModelChange) return;
  
  try {
    isUpdatingFromModelChange = true;
    if (newVal && newVal !== progressiveType.value) {
      progressiveType.value = newVal;
    }
  } finally {
    isUpdatingFromModelChange = false;
  }
}, { deep: true });

watch(() => props.modelValue.progressiveTiers, (newVal) => {
  if (isUpdatingFromModelChange) return;
  
  try {
    isUpdatingFromModelChange = true;
    if (newVal && JSON.stringify(newVal) !== JSON.stringify(progressiveTiers.value)) {
      progressiveTiers.value = JSON.parse(JSON.stringify(newVal));
    }
  } finally {
    isUpdatingFromModelChange = false;
  }
}, { deep: true });
</script>
