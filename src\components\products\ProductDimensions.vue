<template>
  <div class="space-y-4">
    <!-- <PERSON><PERSON><PERSON> e Dimensões da Embalagem -->
    <div class="space-y-4">
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <IluriaInputText
          id="weight"
          name="weight"
          :label="t('product.weight')"
          suffix="g"
          type="number"
          v-model.number="form.weight"
          :formContext="props.formContext?.weight"
        />

        <IluriaInputText
          id="boxLength"
          name="boxLength"
          :label="t('product.boxLength')"
          suffix="cm"
          type="number"
          v-model.number="form.boxLength"
          :formContext="props.formContext?.boxLength"
        />

        <IluriaInputText
          id="boxWidth"
          name="boxWidth"
          :label="t('product.boxWidth')"
          suffix="cm"
          type="number"
          v-model.number="form.boxWidth"
          :formContext="props.formContext?.boxWidth"
        />

        <IluriaInputText
          id="boxDepth"
          name="boxDepth"
          :label="t('product.boxDepth')"
          suffix="cm"
          type="number"
          v-model.number="form.boxDepth"
          :formContext="props.formContext?.boxDepth"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { inject } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

// Inject the product form data from parent component
const form = inject('productForm');

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Define validation rules
const validationRules = {
  weight: {},
  boxLength: {},
  boxWidth: {},
  boxDepth: {}
};

// Expose validation rules
defineExpose({ validationRules });
</script> 