<template>
  <div class="social-media-settings-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="t('socialMedia.title')"
      :subtitle="t('socialMedia.subtitle')"
      :showSave="true"
      :saveText="t('socialMedia.saveButton')"
      @save-click="saveSettings"
    />

    <!-- Main Content -->
    <Form 
      v-if="dataLoaded" 
      v-slot="$form" 
      @submit="saveSettings" 
      :resolver="resolver" 
      :validate-on-submit="true" 
      :validate-on-blur="true" 
      :validate-on-value-update="false" 
      :initial-values="formData" 
      class="form-container"
    >
      
      <!-- Configuração de Redes Sociais -->
      <ViewContainer 
        :title="t('socialMedia.socialConfigTitle')"
        :subtitle="t('socialMedia.socialConfigSubtitle')"
        :icon="Share08Icon"
        iconColor="orange"
      >
        <div class="social-grid">
          <!-- <PERSON><PERSON> da imagem -->
          <div class="image-section">
            <div class="form-field">
              <label class="field-label">{{ t('socialMedia.featuredImage') }}</label>
              <IluriaSimpleImageUpload
                v-model="formData.profileImageUrl"
                accept="image/*"
                :add-button-text="t('socialMedia.addImage')"
                :change-button-text="t('socialMedia.changeImage')"
                :remove-button-text="t('socialMedia.removeImage')"
                :format-hint="t('socialMedia.imageFormats')"
                :prevent-cache="true"
                @change="onImageChange"
                class="image-upload"
              />
            </div>
          </div>

          <!-- Coluna dos campos de texto -->
          <div class="content-section">
            <!-- Título para Redes Sociais -->
            <div class="form-field">
              <IluriaInputText 
                id="socialTitle"
                :label="t('socialMedia.socialTitle')"
                v-model="formData.title" 
                :placeholder="t('socialMedia.socialTitlePlaceholder')"
                :formContext="$form.socialTitle"
              />
              <p class="field-hint">{{ t('socialMedia.titleMaxLength') }}</p>
            </div>

            <!-- Descrição para Redes Sociais -->
            <div class="form-field">
              <IluriaInputText
                id="socialDescription"
                :label="t('socialMedia.socialDescription')"
                v-model="formData.description" 
                :placeholder="t('socialMedia.socialDescriptionPlaceholder')"
                :formContext="$form.socialDescription"
                type="textarea"
                rows="3"
              />
              <p class="field-hint">{{ t('socialMedia.descriptionMaxLength') }}</p>
            </div>
          </div>
        </div>

      </ViewContainer>

      <!-- Configuração de Links -->
      <ViewContainer 
        :title="t('socialLink.configurationTitle')"
        :subtitle="t('socialLink.configurationSubtitle')"
        :icon="Link03Icon"
        iconColor="blue"
      >
        <div class="social-links-grid">
          
          <!-- Instagram -->
          <div class="social-link-item">
            <div class="social-link-content">
              <div class="social-link-header">
                <div class="social-icon instagram">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.40s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </div>
                <div class="social-info">
                  <h3 class="social-title">{{ t('socialLink.instagram.title') }}</h3>
                  <p class="social-description">{{ t('socialLink.instagram.description') }}</p>
                </div>
                <IluriaToggleSwitch 
                  v-model="socialLinks.instagramEnabled" 
                  :color="socialLinks.instagramEnabled ? 'primary' : 'gray'"
                />
              </div>
              <div class="social-input" v-if="socialLinks.instagramEnabled">
                <IluriaInputText
                  v-model="socialLinks.instagramUrl"
                  type="url" 
                  :placeholder="t('socialLink.instagram.placeholder')"
                />
              </div>
            </div>
          </div>

          <!-- Twitter/X -->
          <div class="social-link-item">
            <div class="social-link-content">
              <div class="social-link-header">
                <div class="social-icon twitter">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                  </svg>
                </div>
                <div class="social-info">
                  <h3 class="social-title">{{ t('socialLink.twitter.title') }}</h3>
                  <p class="social-description">{{ t('socialLink.twitter.description') }}</p>
                </div>
                <IluriaToggleSwitch 
                  v-model="socialLinks.twitterEnabled" 
                  :color="socialLinks.twitterEnabled ? 'primary' : 'gray'"
                />
              </div>
              <div class="social-input" v-if="socialLinks.twitterEnabled">
                <IluriaInputText
                  v-model="socialLinks.twitterUrl"
                  type="url" 
                  :placeholder="t('socialLink.twitter.placeholder')"
                />
              </div>
            </div>
          </div>

          <!-- Facebook -->
          <div class="social-link-item">
            <div class="social-link-content">
              <div class="social-link-header">
                <div class="social-icon facebook">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </div>
                <div class="social-info">
                  <h3 class="social-title">{{ t('socialLink.facebook.title') }}</h3>
                  <p class="social-description">{{ t('socialLink.facebook.description') }}</p>
                </div>
                <IluriaToggleSwitch 
                  v-model="socialLinks.facebookEnabled" 
                  :color="socialLinks.facebookEnabled ? 'primary' : 'gray'"
                />
              </div>
              <div class="social-input" v-if="socialLinks.facebookEnabled">
                <IluriaInputText
                  v-model="socialLinks.facebookUrl"
                  type="url" 
                  :placeholder="t('socialLink.facebook.placeholder')"
                />
              </div>
            </div>
          </div>

          <!-- TikTok -->
          <div class="social-link-item">
            <div class="social-link-content">
              <div class="social-link-header">
                <div class="social-icon tiktok">
                  <svg width="20" height="20" fill="currentColor" viewBox="0 0 24 24">
                    <path d="M19.59 6.69a4.83 4.83 0 0 1-3.77-4.25V2h-3.45v13.67a2.89 2.89 0 0 1-5.2 1.74 2.89 2.89 0 0 1 2.31-4.64 2.93 2.93 0 0 1 .88.13V9.4a6.84 6.84 0 0 0-1-.05A6.33 6.33 0 0 0 5 20.1a6.34 6.34 0 0 0 10.86-4.43v-7a8.16 8.16 0 0 0 4.77 1.52v-3.4a4.85 4.85 0 0 1-1-.10z"/>
                  </svg>
                </div>
                <div class="social-info">
                  <h3 class="social-title">{{ t('socialLink.tiktok.title') }}</h3>
                  <p class="social-description">{{ t('socialLink.tiktok.description') }}</p>
                </div>
                <IluriaToggleSwitch 
                  v-model="socialLinks.tiktokEnabled" 
                  :color="socialLinks.tiktokEnabled ? 'primary' : 'gray'"
                />
              </div>
              <div class="social-input" v-if="socialLinks.tiktokEnabled">
                <IluriaInputText
                  v-model="socialLinks.tiktokUrl"
                  type="url" 
                  :placeholder="t('socialLink.tiktok.placeholder')"
                />
              </div>
            </div>
          </div>

        </div>
      </ViewContainer>


    </Form>

    <!-- Loading State -->
    <div v-else class="loading-state">
      <div class="loading-spinner"></div>
      <span>{{ t('loading') }}</span>
    </div>

    <!-- Toast Container -->
    <Toast />
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { z } from 'zod';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import { Form } from '@primevue/forms';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import socialMediaService from '../../services/socialmedia.service';
import socialLinksService from '@/services/socialLinks.service.js';
import { requiredText } from '@/services/validation.service';
import { useToast } from '@/services/toast.service';
import Toast from 'primevue/toast';
import { 
  FloppyDiskIcon,
  Share08Icon,
  Link03Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n();
const dataLoaded = ref(false);
const componentMounted = ref(false);
const isUploading = ref(false);
const toast = useToast();

const resolver = zodResolver(
  z.object({
    title: requiredText(t('socialMedia.socialTitle')),
    description: requiredText(t('socialMedia.socialDescription')),
  })
);

const formData = reactive({
  profileImageUrl: '',
  title: '',
  description: '',
  newImageFile: null
});

const socialLinks = ref({
    instagramUrl: '',
    twitterUrl: '',
    facebookUrl: '',
    tiktokUrl: '',
    instagramEnabled: false,
    twitterEnabled: false,
    facebookEnabled: false,
    tiktokEnabled: false
});

const originalData = ref({});
const originalSocialLinks = ref({});

const onImageChange = (file) => {
  if (!file) {
    // Se estiver removendo a imagem e já tiver uma imagem no servidor
    if (formData.profileImageUrl && formData.profileImageUrl.startsWith('http')) {
      try {
        socialMediaService.deleteImage(formData.profileImageUrl);
      } catch (error) {
        console.error('Erro ao remover a imagem:', error);
        toast.showError(t('socialMedia.deleteError') || 'Erro ao remover a imagem');
      }
    }
    
    formData.profileImageUrl = '';
    formData.newImageFile = null;
    return;
  }
  
  if (file.size > 15 * 1024 * 1024) {
    toast.showError(t('socialMedia.imageTooLarge') || 'A imagem não pode ser maior que 15MB');
    return;
  }
  
  formData.newImageFile = file;
  
  const reader = new FileReader();
  reader.onload = (e) => {
    formData.profileImageUrl = e.target.result;
  };
  reader.readAsDataURL(file);
};

const onImageUploadError = (erro) => {
  if (componentMounted.value) {
    toast.showError(t('socialMedia.uploadError') || 'Erro ao fazer upload da imagem');
  }
};

onMounted(async () => {
  componentMounted.value = true;
  await loadData();
});

const loadData = async () => {
  try {
    // Carregar configurações de mídia social
    const response = await socialMediaService.getSocialMediaSettings();
    if (response) {
      formData.title = response.title || '';
      formData.description = response.description || '';
      formData.profileImageUrl = response.profileImage || '';
    }
    
    // Carregar social links
    await loadSocialLinks();
    
    dataLoaded.value = true;
  } catch (error) {
    console.error('Erro ao carregar configurações de redes sociais:', error);
    if (componentMounted.value) {
      toast.showError(t('socialMedia.loadError') || 'Erro ao carregar configurações');
    }
  }
};

const loadSocialLinks = async () => {
    try {
        const data = await socialLinksService.getSocialLinks();
        socialLinks.value = {
            instagramUrl: data.instagram_url || '',
            twitterUrl: data.twitter_url || '',
            facebookUrl: data.facebook_url || '',
            tiktokUrl: data.tiktok_url || '',
            instagramEnabled: data.instagram_enabled || false,
            twitterEnabled: data.twitter_enabled || false,
            facebookEnabled: data.facebook_enabled || false,
            tiktokEnabled: data.tiktok_enabled || false
        };
        // Salva uma cópia para poder resetar
        originalSocialLinks.value = JSON.parse(JSON.stringify(socialLinks.value));
    } catch (error) {
        if (error.response && error.response.status === 404) {
            // Se não existir ainda, mantém os valores padrão
        } else {
            console.error('Erro ao carregar social links:', error);
            throw error;
        }
    }
};

const saveSettings = async () => {
  if (!componentMounted.value) return;
  
  try {
    if (!formData.title || !formData.description) {
      toast.showError('Título e descrição são obrigatórios');
      return;
    }
    
    isUploading.value = true;
    
    try {
      // Salvar configurações de mídia social
      const data = {
        title: formData.title,
        description: formData.description,
        profileImage: ''
      };

      await socialMediaService.updateSocialMediaSettings(data);
      
      // Salvar social links
      const socialLinksData = {
        instagram_url: socialLinks.value.instagramUrl,
        twitter_url: socialLinks.value.twitterUrl,
        facebook_url: socialLinks.value.facebookUrl,
        tiktok_url: socialLinks.value.tiktokUrl,
        instagram_enabled: socialLinks.value.instagramEnabled,
        twitter_enabled: socialLinks.value.twitterEnabled,
        facebook_enabled: socialLinks.value.facebookEnabled,
        tiktok_enabled: socialLinks.value.tiktokEnabled
      };

      await socialLinksService.saveSocialLinks(socialLinksData);
      
      if (formData.newImageFile) {
        try {
          // Fazemos o upload da imagem separadamente
          const result = await socialMediaService.uploadImage(formData.newImageFile);
          if (result && result.profileImage) {
            // Atualizamos apenas o formData local com a URL completa para exibição
            formData.profileImageUrl = result.profileImage;
            
            toast.showSuccess(t('socialMedia.imageUploadSuccess') || 'Imagem carregada com sucesso');
          }
        } catch (uploadError) {
          console.error('Erro ao fazer upload da imagem:', uploadError);
          toast.showError(t('socialMedia.uploadError') || 'Erro ao fazer upload da imagem');
        }
      }
      
      // Atualiza as cópias originais
      originalSocialLinks.value = JSON.parse(JSON.stringify(socialLinks.value));
      
      toast.showSuccess(t('socialMedia.saveSuccess') || 'Configurações salvas com sucesso');
      formData.newImageFile = null;
      
      await loadData();
    } catch (error) {
      console.error('Erro ao salvar configurações de redes sociais:', error);
      toast.showError(t('socialMedia.saveError') || 'Erro ao salvar configurações');
      throw error;
    }
  } catch (error) {
    console.error('Erro inesperado ao processar as configurações:', error);
    toast.showError(t('socialMedia.saveError') || 'Erro ao processar as configurações');
  } finally {
    isUploading.value = false;
  }
};
</script>

<style scoped>
.social-media-settings-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



/* Form Container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Social Grid */
.social-grid {
  display: grid;
  grid-template-columns: 300px 1fr;
  gap: 32px;
  margin-bottom: 24px;
}

.image-section {
  display: flex;
  flex-direction: column;
}

.content-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Fields */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.field-hint {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.image-upload {
  width: 100%;
}



/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 64px 24px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .social-grid {
    grid-template-columns: 1fr;
    gap: 24px;
  }
  
  .image-section {
    order: 2;
  }
  
  .content-section {
    order: 1;
  }
}

@media (max-width: 768px) {
  .social-media-settings-container {
    padding: 16px;
  }
  

  
  .form-container {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .social-media-settings-container {
    padding: 12px;
  }
  

}

/* Social Links Styles */
.social-links-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.social-link-item {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  background: var(--iluria-color-surface);
  transition: all 0.2s ease;
}

.social-link-item:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--iluria-shadow-md);
}

.social-link-content {
  padding: 20px;
}

.social-link-header {
  display: flex;
  align-items: center;
  gap: 16px;
  min-height: 48px;
}

.social-icon {
  width: 48px;
  height: 48px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
}

.social-icon.instagram {
  background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
  color: white;
}

.social-icon.twitter {
  background: #000000;
  color: white;
}

.social-icon.facebook {
  background: #1877f2;
  color: white;
}

.social-icon.tiktok {
  background: #000000;
  color: white;
}

.social-link-item:hover .social-icon {
  transform: scale(1.05);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
}

.social-info {
  flex: 1;
  min-width: 0;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.social-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
}

.social-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.social-input {
  margin-top: 12px;
  padding-top: 4px;
  animation: slideDown 0.3s ease;
  background: none !important;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}


</style>
