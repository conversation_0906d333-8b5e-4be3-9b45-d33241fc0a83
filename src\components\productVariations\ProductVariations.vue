<template>
    
  <div class="p-4">
    <!-- Variations List -->
    <draggable 
      v-model="variations" 
      :animation="200"
      item-key="id"
      handle=".variation-drag-handle"
      @start="dragStart"
      @end="dragEnd"
      ghost-class="drag-ghost"
      chosen-class="drag-chosen"
    >
      <template #item="{ element: variation, index }">
        <div class="mb-4 px-6 py-5 bg-[var(--iluria-color-surface)] border border-[var(--iluria-color-border)] rounded-lg shadow-sm relative">
          <div class="flex justify-between items-center mb-3">
            <div class="flex items-center space-x-4 w-full">
              <div class="flex-grow">
                <IluriaLabel class="block text-[var(--iluria-color-text-secondary)] mb-1">
                  {{ t('productVariations.variationName') }}
                </IluriaLabel>
                <div class="flex items-center justify-between space-x-2">
                  <div class="flex items-center flex-grow space-x-2">
                    <IluriaInputText
                      :id="`variation-name-${variation.id}`"
                      v-model="variation.name"
                      :placeholder="t('productVariations.variationNamePlaceholder')"
                      class="max-w-[250px]"
                      @keydown.enter.prevent="focusOptionsInput(index)"
                      @input="handleVariationNameChange(index, $event)"
                      :ref="el => { if (el) variationInputs[index] = el }"
                      @focus="activeInput = index"
                      @blur="activeInput = null"
                    />
                    <IluriaButton 
                      @click="removeVariation(index)" 
                      color="danger"
                      size="small"
                    >
                      {{ t('productVariations.deleteVariation') }}
                    </IluriaButton>
                  </div>
                  <div class="flex items-center">
                    <span class="text-[var(--iluria-color-text-secondary)] mr-2">{{ t('productVariations.showInSearch') }}</span>
                    <IluriaToggleSwitch
                      :modelValue="variation.showInSearch"
                      @update:modelValue="(value) => handleToggleSearch(index, value)"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Variation Options -->
          <div class="mt-7 mb-2">
            <IluriaInputTags
              :ref="el => { if (el) variationOptions[index] = el }"
              :id="`variation-options-${variation.id}`"
              v-model="variation.options"
              :label="t('productVariations.options')"
              :placeholder="t('productVariations.addOptionPlaceholder')"
              :draggable="true"
              @update:modelValue="generateCombinations"
            />
          </div>
          <!-- Absolute drag handle icon -->
          <HugeiconsIcon 
            :icon="DragDropVerticalIcon" 
            class="variation-drag-handle cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-primary)] absolute top-4 right-6" 
            :size="18" 
          />
        </div>
      </template>
    </draggable>
    
    <!-- Add Variation Button -->
    <IluriaButton 
      @click="addVariation" 
      color="outline"
      :disabled="!canAddVariation"
      :huge-icon="AddSquareIcon"
      class="rounded-full"
    >
      {{ canAddVariation ? t('productVariations.addVariation') : t('productVariations.maxVariationsReached') }}
    </IluriaButton>
    
    <!-- Group By Section and Variations Table -->
    <template v-if="showTable">
      <!-- Variations Table -->
      <div class="mt-10 pt-6 border-t border-[var(--iluria-color-border)]">
        <VariationsTable
          :variations="variations"
          :combinedVariations="combinedVariations"
          :group-by="groupBy"
          @update:variation="updateVariation"
          @update:groupBy="groupBy = $event"
          @clearAllData="clearAllVariationData"
        />
      </div>
    </template>

    <!-- Modal de Confirmação para Limpar Tudo -->
    <IluriaConfirmationModal
      :is-visible="showClearAllModal"
      :title="t('productVariations.confirmClearAllTitle')"
      :message="t('productVariations.confirmClearAllMessage')"
      :confirm-text="t('productVariations.confirmClearAll')"
      :cancel-text="t('cancel')"
      type="warning"
      @confirm="confirmClearAllData"
      @cancel="showClearAllModal = false"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick, inject } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToast } from '@/services/toast.service';
import { HugeiconsIcon } from '@hugeicons/vue';
import { DragDropVerticalIcon, Cancel01Icon, AddSquareIcon, Delete03Icon } from '@hugeicons-pro/core-stroke-rounded';
import draggable from 'vuedraggable';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaInputTags from '@/components/iluria/form/IluriaInputTags.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import VariationsTable from './VariationsTable.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import { Sortable } from 'sortablejs';
import productsApi from '@/services/product.service';

// Composables
const { t } = useI18n();
const toast = useToast();

// Inject data from parent
const productForm = inject('productForm', ref({}));
const isEditing = inject('isEditing', ref(false));

// Props and Emits
const props = defineProps({
modelValue: {
  type: Array,
  default: () => []
},
productVariations: {
  type: Array,
  default: () => []
}
});

const emit = defineEmits(['update:modelValue', 'update:productVariations']);

// State
const variations = ref([]);
const combinedVariations = ref([]);
const groupBy = ref('');
const variationInputs = ref([]);
const variationOptions = ref([]);
const activeInput = ref(null);
const isMounted = ref(false);
const showClearAllModal = ref(false);

const focusOptionsInput = (index) => {
nextTick(() => {
  const optionsComponent = variationOptions.value[index];
  if (optionsComponent && optionsComponent.$el) {
    const inputElement = optionsComponent.$el.querySelector('input');
    if (inputElement) {
      inputElement.focus();
    }
  }
});
};

onMounted(() => {
if (props.modelValue && props.modelValue.length > 0) {
  variations.value = props.modelValue.map(variation => ({
    ...variation,
    showInSearch: variation.showInSearch ?? false
  }));

  // Garantir que pelo menos uma variação tenha showInSearch=true
  const hasActiveVariation = variations.value.some(v => v.showInSearch);
  if (!hasActiveVariation && variations.value.length > 0) {
    variations.value[0].showInSearch = true;
  }

  // Garantir que apenas uma variação tenha showInSearch=true
  let foundActive = false;
  variations.value = variations.value.map(variation => {
    if (variation.showInSearch && !foundActive) {
      foundActive = true;
      return variation;
    } else {
      return { ...variation, showInSearch: false };
    }
  });

  if (variations.value.length > 0) {
    groupBy.value = variations.value[0].name || '';
  }
}

if (props.productVariations && props.productVariations.length > 0) {
  combinedVariations.value = [...props.productVariations];
} else if (variations.value.length > 0) {
  generateCombinations();
}

isMounted.value = true;

});

// Computed property to check if we can add more variations
const canAddVariation = computed(() => variations.value.length < 3);

// Computed property to check if we have valid variations to show the table
const showTable = computed(() => {
return variations.value.length > 0 && variations.value.some(v => v.name && v.options && v.options.length > 0);
});

// Emit changes to parent (simplified and protected against loops)
const emitChanges = () => {
if (!isMounted.value) return;

emit('update:modelValue', variations.value);
emit('update:productVariations', combinedVariations.value);
};

// Handle variation name change
const handleVariationNameChange = (index, event) => {
const newName = event.target.value;
if (variations.value[index]) {
  variations.value[index].name = newName;
  
  if (groupBy.value === '' || !variations.value.some(v => v.name === groupBy.value)) {
    groupBy.value = newName;
  }
  
  emitChanges();
}
};

// Handle drag events
const dragStart = () => {
isDragging.value = true;
};

const dragEnd = () => {
isDragging.value = false;
if (activeInput.value !== null) {
  nextTick(() => {
    const inputComponent = variationInputs.value[activeInput.value];
    if (inputComponent && inputComponent.$el) {
      const inputElement = inputComponent.$el.querySelector('input') || inputComponent.$el;
      inputElement?.focus();
    }
  });
}
emitChanges();
};

const handleToggleSearch = (index, newValue) => {
  if (!variations.value[index]) return;

  // Se está tentando habilitar uma variação
  if (newValue === true) {
    // Desabilitar todas as outras e habilitar apenas esta
    variations.value = variations.value.map((variation, i) => ({
      ...variation,
      showInSearch: i === index
    }));

    emitChanges();
    return;
  }

  // Se está tentando desabilitar uma variação
  if (newValue === false) {
    const activeCount = variations.value.filter(v => v.showInSearch).length;

    // Se é a única ativa, não permitir desabilitar
    if (activeCount === 1) {
      // Mostrar mensagem de erro usando o padrão Iluria
      toast.showError(t('productVariations.showInSearchRequired'));

      // Forçar o valor de volta para true no próximo tick
      nextTick(() => {
        variations.value[index].showInSearch = true;
      });
      return;
    }

    // Se há mais de uma ativa, permitir desabilitar esta
    variations.value = variations.value.map((variation, i) => ({
      ...variation,
      showInSearch: i === index ? false : variation.showInSearch
    }));

    emitChanges();
  }
};

// Manter a função original para compatibilidade
const toggleSearch = (index, newValue) => {
  handleToggleSearch(index, newValue);
};

// Add a new variation
const addVariation = () => {
if (canAddVariation.value) {
  // Se é a primeira variação, deve ter showInSearch=true
  // Caso contrário, showInSearch=false
  const isFirstVariation = variations.value.length === 0;

  const newVariation = {
    id: Date.now().toString(),
    name: '',
    options: [],
    showInSearch: isFirstVariation
  };

  variations.value.push(newVariation);

  nextTick(() => {
    const inputs = document.querySelectorAll('.variation-name-input');
    if (inputs.length > 0) {
      const lastInput = inputs[inputs.length - 1];
      lastInput.focus();
    }
  });

  emitChanges();
}
};

// Remove a variation
const removeVariation = (index) => {
const removedVariation = variations.value[index];
const wasShowingInSearch = removedVariation.showInSearch;

variations.value.splice(index, 1);

// Se removeu a variação que estava sendo mostrada na busca e ainda há variações restantes,
// ativar a primeira variação restante
if (wasShowingInSearch && variations.value.length > 0) {
  const hasActiveVariation = variations.value.some(v => v.showInSearch);
  if (!hasActiveVariation) {
    variations.value[0].showInSearch = true;
  }
}

if (removedVariation.name === groupBy.value && variations.value.length > 0) {
  const firstValidVariation = variations.value.find(v => v.name && v.options.length > 0);
  groupBy.value = firstValidVariation ? firstValidVariation.name : '';
}

generateCombinations();
emitChanges();
};

// Update variation data
const updateVariation = (id, field, value) => {
  const variationIndex = combinedVariations.value.findIndex(v => v.id === id);
  if (variationIndex !== -1) {
    // Para arrays (como photos), fazer uma cópia profunda para garantir reatividade
    if (Array.isArray(value)) {
      combinedVariations.value[variationIndex][field] = [...value];
    } else {
      combinedVariations.value[variationIndex][field] = value;
    }


    emitChanges();
  } else {
    console.warn(`Variação com ID ${id} não encontrada para atualização`);
  }
};

// Generate all possible combinations of variations
const generateCombinations = () => {
if (variations.value.length === 0) {
  combinedVariations.value = [];
  return;
}

const validVariations = variations.value.filter(v => v.name && v.options && v.options.length > 0);

if (validVariations.length === 0) {
  combinedVariations.value = [];
  return;
}

function cartesianProduct(arrays) {
  if (arrays.length === 0) return [[]];
  if (arrays.length === 1) return arrays[0].map(item => [item]);
  
  const [first, ...rest] = arrays;
  const restProduct = cartesianProduct(rest);
  
  return first.flatMap(item =>
    restProduct.map(combo => [item, ...combo])
  );
}

const optionArrays = validVariations.map(v => v.options);
const combinations = cartesianProduct(optionArrays);

const existingVariationsMap = new Map();
combinedVariations.value.forEach(variation => {
  const key = Object.entries(variation.options)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([k, v]) => `${k}:${v}`)
    .join('|');
  existingVariationsMap.set(key, variation);
});

combinedVariations.value = combinations.map((combo, index) => {
  const options = {};
  validVariations.forEach((variation, i) => {
    options[variation.name] = combo[i];
  });
  
  const key = Object.entries(options)
    .sort(([a], [b]) => a.localeCompare(b))
    .map(([k, v]) => `${k}:${v}`)
    .join('|');
  
  const existingVariation = existingVariationsMap.get(key);
  
  if (existingVariation) {
    return existingVariation;
  }
  
  return {
    id: `variation-${Date.now()}-${index}`,
    options,
    price: '',
    stock: '',
    originalPrice: '',
    costPrice: '',
    weight: '',
    length: '',
    width: '',
    depth: '',
    photos: [],
    priceRanges: [],
    hasPriceRanges: false
  };
});

emitChanges();
};

// Show confirmation modal for clearing all data
const clearAllVariationData = () => {
  if (combinedVariations.value.length === 0) {
    return;
  }
  showClearAllModal.value = true;
};

// Confirm and execute clear all data
const confirmClearAllData = async () => {
  showClearAllModal.value = false;

  const productId = productForm.value?.id;

  // Se estamos editando um produto existente, deletar imagens do backend primeiro
  if (isEditing.value && productId) {
    for (const variation of combinedVariations.value) {
      // Verificar se a variação tem ID válido (variação existente no backend)
      const isExistingVariation = variation.id &&
        typeof variation.id === 'string' &&
        variation.id.match(/^[0-9A-Z]{26}$/);

      if (isExistingVariation && variation.photos && variation.photos.length > 0) {
        // Deletar cada imagem da variação no backend
        for (const photo of variation.photos) {
          if (photo.url && photo.isExisting !== false) {
            try {
              await productsApi.deleteVariationImage(productId, variation.id, photo.url);
            } catch (error) {
              console.error('Erro ao deletar imagem da variação:', error);
              // Continuar mesmo se houver erro em uma imagem específica
            }
          }
        }
      }
    }
  }

  // Limpar todos os dados das variações mas manter a estrutura
  combinedVariations.value.forEach(variation => {
    variation.price = '';
    variation.stock = '';
    variation.originalPrice = '';
    variation.costPrice = '';
    variation.weight = '';
    variation.length = '';
    variation.width = '';
    variation.depth = '';
    variation.photos = [];
    variation.priceRanges = [];
    variation.hasPriceRanges = false;
  });

  // Emitir as mudanças
  emitChanges();

  // Mostrar mensagem de sucesso
  toast.showSuccess(t('productVariations.allDataCleared'));
};

watch(() => props.modelValue, (newVariations, oldVariations) => {
const newLength = newVariations?.length || 0;
const oldLength = oldVariations?.length || 0;

const shouldUpdate = isMounted.value && (
  newLength !== oldLength || 
  (newLength > 0 && variations.value.length === 0) ||
  JSON.stringify(newVariations) !== JSON.stringify(oldVariations)
);

if (shouldUpdate) {
  variations.value = newVariations.map(variation => ({
    ...variation,
    showInSearch: variation.showInSearch ?? false
  }));

  // Garantir que pelo menos uma variação tenha showInSearch=true
  const hasActiveVariation = variations.value.some(v => v.showInSearch);
  if (!hasActiveVariation && variations.value.length > 0) {
    variations.value[0].showInSearch = true;
  }

  // Garantir que apenas uma variação tenha showInSearch=true
  let foundActive = false;
  variations.value = variations.value.map(variation => {
    if (variation.showInSearch && !foundActive) {
      foundActive = true;
      return variation;
    } else {
      return { ...variation, showInSearch: false };
    }
  });

  if (variations.value.length > 0 && !groupBy.value) {
    groupBy.value = variations.value[0].name || '';
  }
}
}, { deep: true, immediate: false });

watch(() => props.productVariations, (newProductVariations, oldProductVariations) => {
const newLength = newProductVariations?.length || 0;
const oldLength = oldProductVariations?.length || 0;

const shouldUpdate = isMounted.value && (
  newLength !== oldLength || 
  (newLength > 0 && combinedVariations.value.length === 0) ||
  JSON.stringify(newProductVariations) !== JSON.stringify(oldProductVariations)
);

if (shouldUpdate) {
  combinedVariations.value = [...newProductVariations];
}
}, { deep: true, immediate: false });

// Only watch for direct changes to regenerate combinations
watch(() => variations.value, () => {
if (isMounted.value) {
  generateCombinations();
}
}, { deep: true });
</script>

<style scoped>
.shadow-custom {
box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
}

.bg-light-pink {
background-color: #fdf2f8;
}

/* Estilos para drag and drop com temas */
:deep(.drag-ghost) {
opacity: 0.6;
background: var(--iluria-color-surface) !important;
border: 1px solid var(--iluria-color-border) !important;
color: var(--iluria-color-text-primary) !important;
}

:deep(.drag-chosen) {
background: var(--iluria-color-surface-hover) !important;
border: 1px solid var(--iluria-color-primary) !important;
}

/* Garantir que todos os elementos dentro do ghost usem as cores do tema */
:deep(.drag-ghost *) {
background: var(--iluria-color-surface) !important;
color: var(--iluria-color-text-primary) !important;
border-color: var(--iluria-color-border) !important;
}

:deep(.drag-ghost svg) {
color: var(--iluria-color-text-tertiary) !important;
}
</style>
