import axios from 'axios';
import { API_URLS } from '../config/api.config';

const endpoint = API_URLS.PRODUCT_QUESTIONS_BASE_URL;

export const questionsApi = {
  findQuestions: (params = {}) => {
    return axios.get(endpoint, { params });
  },

  getKpis: () => {
    return axios.get(`${endpoint}/kpis`);
  },

  getById: (id) => {
    return axios.get(`${endpoint}/${id}`);
  },

  answer: (id, answerData) => {
    return axios.post(`${endpoint}/${id}/answer`, answerData);
  },

  updateAnswer: (id, answerData) => {
    return axios.put(`${endpoint}/${id}/answer`, answerData);
  },

  updateStatus: (id, status, highlighted = null) => {
    const params = new URLSearchParams();
    params.append('status', status);
    if (highlighted !== null) {
      params.append('highlighted', highlighted);
    }
    return axios.put(`${endpoint}/${id}/status?${params.toString()}`);
  },

  delete: (id) => {
    return axios.delete(`${endpoint}/${id}`);
  },

  // Predefined answers
  getPredefinedAnswers: () => {
    return axios.get('http://localhost:8081/api/predefined-answers');
  },

  incrementPredefinedAnswerUsage: (id) => {
    return axios.post(`http://localhost:8081/api/predefined-answers/${id}/increment-usage`);
  },

  deletePredefinedAnswer: (id) => {
    return axios.delete(`http://localhost:8081/api/predefined-answers/${id}`);
  },

  updatePredefinedAnswer: (id, data) => {
    return axios.put(`http://localhost:8081/api/predefined-answers/${id}`, data);
  },

  createPredefinedAnswer: (data) => {
    return axios.post('http://localhost:8081/api/predefined-answers', data);
  },

  getPredefinedAnswerById: (id) => {
    return axios.get(`http://localhost:8081/api/predefined-answers/${id}`);
  }
};

export default questionsApi; 