<template>
  <div class="content-preview-container">
    <!-- Preview Area -->
    <div
      class="content-preview"
      @click="openEditor"
      :class="{ 'empty': isEmpty }"
    >
      <!-- Edit Overlay -->
      <div class="edit-overlay">
        <HugeiconsIcon :icon="Edit04Icon" size="32" />
      </div>
      
      <!-- Content Preview -->
      <div 
        v-if="!isEmpty" 
        class="preview-content"
        v-html="htmlContent"
      ></div>
      
      <!-- Empty State -->
      <div v-else class="empty-state">
        <h3>{{ $t('contentEditor.contentPreview.emptyTitle') }}</h3>
        <p>{{ $t('contentEditor.contentPreview.emptyDescription') }}</p>
      </div>
    </div>

    <!-- Editor Modal -->
    <IluriaModal
      v-model:visible="showEditor"
      :title="$t('contentEditor.contentPreview.editorTitle')"
      :closable="true"
      :dialogStyle="modalStyle"
      @hide="onModalClose"
    >
      <template #default>
        <div class="editor-modal-content">
          <ContentEditor
            ref="contentEditorRef"
            :height="'calc(100vh - 120px)'"
            :placeholder="placeholder"
            :modelValue="editorData"
            @update:modelValue="handleContentChange"
            @initialized="onEditorInitialized"
          />
        </div>
      </template>
    </IluriaModal>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import { Edit04Icon } from '@hugeicons-pro/core-stroke-rounded'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import ContentEditor from '@/components/editor/ContentEditor.vue'

const { t } = useI18n()

// Props
const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      time: Date.now(),
      blocks: [],
      version: "2.28.2"
    })
  },
  placeholder: {
    type: String,
    default: 'Comece a digitar…'
  },
  height: {
    type: String,
    default: '400px'
  }
})

// Emits
const emit = defineEmits(['update:modelValue'])

// Refs
const showEditor = ref(false)
const saving = ref(false)
const contentEditorRef = ref(null)
const editorData = ref({ ...props.modelValue })
const originalData = ref(null)

// Computed
const isEmpty = computed(() => {
  return !editorData.value?.blocks || editorData.value.blocks.length === 0 ||
    editorData.value.blocks.every(block => {
      if (block.type === 'paragraph' && !block.data?.text?.trim()) return true
      if (block.type === 'header' && !block.data?.text?.trim()) return true
      return false
    })
})

const htmlContent = ref('')

// Watch for changes and update HTML content
const updateHtmlContent = async () => {
  if (isEmpty.value) {
    htmlContent.value = ''
    return
  }

  try {
    htmlContent.value = await convertEditorDataToHtml(editorData.value)
  } catch (error) {
    console.error('Error updating HTML content:', error)
    htmlContent.value = '<p>Erro ao renderizar conteúdo</p>'
  }
}

// Watch for editor data changes
watch(editorData, updateHtmlContent, { deep: true, immediate: true })

const modalStyle = computed(() => ({
  width: '95vw',
  height: '95vh',
  maxWidth: '95vw',
  maxHeight: '95vh'
}))

// Methods
const openEditor = () => {
  originalData.value = JSON.parse(JSON.stringify(editorData.value))
  showEditor.value = true
}

const onEditorInitialized = () => {
  // Now that the editor is ready, render the content.
  if (contentEditorRef.value) {
    contentEditorRef.value.render(editorData.value)
  }
}

const handleContentChange = (data) => {
  editorData.value = data
}

const onModalClose = async () => {
  // Auto-save when closing modal
  try {
    // Get final content from editor
    if (contentEditorRef.value) {
      const finalData = await contentEditorRef.value.save()
      editorData.value = finalData
    }

    // Emit the updated data
    emit('update:modelValue', editorData.value)

    originalData.value = null
  } catch (error) {
    console.error('Error saving content:', error)
    // If save fails, restore original data
    if (originalData.value) {
      editorData.value = originalData.value
    }
  }
}

// HTML converter for preview using edjsHTML parser
const convertEditorDataToHtml = async (data) => {
  if (!data?.blocks) return ''

  try {
    // Use the same parser configuration as ContentEditor
    const { default: edjsHTML } = await import('editorjs-html')

    // Configure parser with custom blocks (same as ContentEditor)
    const edjsParser = edjsHTML({
      productGrid: function(block) {
        const cardColor = block.data.cardColor || '#ffffff';
        const buttonColor = block.data.buttonColor || '#1f2937';
        const titleColor = block.data.titleColor || '#1f2937';
        const descriptionColor = block.data.descriptionColor || '#6b7280';
        const ratingColor = block.data.ratingColor || '#f59e0b';
        const titleFontSize = block.data.titleFontSize || '1.125';
        const descriptionFontSize = block.data.descriptionFontSize || '0.875';
        const columns = block.data.columns || 3;
        const layout = block.data.layout || 'grid';

        let html = `<div class="product-grid" style="
          display: ${layout === 'list' ? 'flex' : 'grid'};
          ${layout === 'grid' ? `grid-template-columns: repeat(${columns}, 1fr);` : 'flex-direction: column;'}
          gap: 16px;
          margin: 20px 0;
          padding: 20px;
          background: #f8fafc;
          border-radius: 12px;
          border: 1px solid #e2e8f0;
        ">`;

        if (block.data.products && block.data.products.length > 0) {
          block.data.products.forEach(product => {
            const stars = '★'.repeat(Math.floor(product.rating || 0)) + '☆'.repeat(5 - Math.floor(product.rating || 0));

            html += `
              <div class="product-card" style="
                background: ${cardColor};
                border: 1px solid #e2e8f0;
                border-radius: 8px;
                padding: 16px;
                text-align: center;
                transition: transform 0.2s ease;
              ">
                ${product.image ? `<img src="${product.image}" alt="${product.title}" style="
                  width: 100%;
                  height: 150px;
                  object-fit: cover;
                  border-radius: 6px;
                  margin-bottom: 12px;
                " />` : ''}
                <h3 class="product-title" style="
                  color: ${titleColor};
                  font-size: ${titleFontSize}rem;
                  font-weight: 600;
                  margin: 0 0 8px 0;
                ">${product.title || 'Produto'}</h3>
                <p class="product-description" style="
                  color: ${descriptionColor};
                  font-size: ${descriptionFontSize}rem;
                  margin: 0 0 8px 0;
                  line-height: 1.4;
                ">${product.description || 'Descrição do produto'}</p>
                <div class="product-rating" style="
                  color: ${ratingColor};
                  font-size: 0.875rem;
                  margin: 8px 0;
                ">${stars}</div>
                <button class="product-button" style="
                  background: ${buttonColor};
                  color: white;
                  border: none;
                  padding: 8px 16px;
                  border-radius: 6px;
                  font-size: 0.875rem;
                  cursor: pointer;
                  transition: background-color 0.2s ease;
                ">${product.buttonText || 'Ver Detalhes'}</button>
              </div>
            `;
          });
        } else {
          html += `
            <div style="
              grid-column: 1 / -1;
              text-align: center;
              color: #6b7280;
              font-style: italic;
              padding: 40px;
            ">
              📦 Grid de Produtos<br>
              <small>Configure os produtos no editor</small>
            </div>
          `;
        }

        html += '</div>';
        return html;
      },

      imageText: function(block) {
        const imagePosition = block.data.imagePosition || 'left';
        const title = block.data.title || '';
        const text = block.data.text || '';
        const imageUrl = block.data.imageUrl || '';
        const textColor = block.data.textColor || '#ffffff';
        const backgroundColor = block.data.backgroundColor || '#4a90e2';
        const buttons = block.data.buttons || [];

        const isImageLeft = imagePosition === 'left';

        const buttonsHtml = buttons.map(button => `
          <a href="${button.url}" style="
            display: inline-block;
            background: ${button.backgroundColor || 'transparent'};
            color: ${button.color || textColor};
            border: 1px solid ${button.color || textColor};
            padding: 12px 24px;
            margin-right: 12px;
            margin-bottom: 8px;
            border-radius: 6px;
            text-decoration: none;
            font-weight: 500;
            transition: all 0.3s ease;
          ">${button.text}</a>
        `).join('');

        return `
          <div class="image-text-block" style="
            background-color: ${backgroundColor};
            color: ${textColor};
            padding: 40px;
            border-radius: 12px;
            display: flex;
            align-items: center;
            gap: 40px;
            margin: 20px 0;
            flex-direction: ${isImageLeft ? 'row' : 'row-reverse'};
            min-height: 300px;
          ">
            <div class="image-section" style="flex: 1;">
              ${imageUrl ? `
                <img src="${imageUrl}" alt="${title}" style="
                  width: 100%;
                  height: auto;
                  border-radius: 8px;
                  object-fit: cover;
                  max-height: 400px;
                " />
              ` : `
                <div style="
                  border: 2px dashed rgba(255,255,255,0.3);
                  border-radius: 8px;
                  padding: 40px;
                  text-align: center;
                  color: rgba(255,255,255,0.7);
                  min-height: 200px;
                  display: flex;
                  align-items: center;
                  justify-content: center;
                  flex-direction: column;
                ">
                  <div style="font-size: 48px; margin-bottom: 16px;">📷</div>
                  <div>Imagem não definida</div>
                </div>
              `}
            </div>

            <div class="text-section" style="flex: 1;">
              ${title ? `<h2 style="
                font-size: 28px;
                font-weight: bold;
                margin: 0 0 16px 0;
                color: ${textColor};
              ">${title}</h2>` : ''}

              ${text ? `<p style="
                font-size: 16px;
                line-height: 1.6;
                margin: 0 0 20px 0;
                color: ${textColor};
              ">${text}</p>` : ''}

              ${buttonsHtml ? `<div class="buttons-container">${buttonsHtml}</div>` : ''}
            </div>
                     </div>
         `;
      },

      warning: function(block) {
        const title = block.data.title || '';
        const message = block.data.message || '';
        return `
          <div class="warning-block" style="
            background-color: #fef3cd;
            border: 1px solid #fecaca;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            border-left: 4px solid #f59e0b;
          ">
            ${title ? `<h4 style="margin: 0 0 0.5rem 0; color: #92400e; font-weight: 600;">${title}</h4>` : ''}
            <p style="margin: 0; color: #92400e;">${message}</p>
          </div>
        `;
      },

      attaches: function(block) {
        const file = block.data.file || {};
        const title = block.data.title || file.name || 'Arquivo anexado';
        const url = file.url || '#';
        const size = file.size ? `(${Math.round(file.size / 1024)} KB)` : '';

        return `
          <div class="attaches-block" style="
            border: 1px solid #e5e7eb;
            border-radius: 8px;
            padding: 1rem;
            margin: 1rem 0;
            background-color: #f9fafb;
            display: flex;
            align-items: center;
            gap: 0.75rem;
          ">
            <div style="
              width: 40px;
              height: 40px;
              background-color: #3b82f6;
              border-radius: 6px;
              display: flex;
              align-items: center;
              justify-content: center;
              color: white;
              font-weight: bold;
            ">📎</div>
            <div style="flex-grow: 1;">
              <a href="${url}" style="
                color: #3b82f6;
                text-decoration: none;
                font-weight: 500;
              " download>${title}</a>
              ${size ? `<div style="font-size: 0.875rem; color: #6b7280;">${size}</div>` : ''}
            </div>
          </div>
        `;
      },

      raw: function(block) {
        return block.data.html || '';
      }
    });

    const htmlArray = edjsParser.parse(data)
    return Array.isArray(htmlArray) ? htmlArray.join('') : htmlArray
  } catch (error) {
    console.error('Error converting editor data to HTML:', error)
    return '<p>Erro ao renderizar conteúdo</p>'
  }
}

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && !showEditor.value) {
    editorData.value = { ...newValue }
    updateHtmlContent()
  }
}, { deep: true, immediate: true })
</script>

<style scoped>
/* Modify preview container to not center and allow full width */
.content-preview-container {
  position: relative;
  width: 100%;
  display: block;
  background-color: transparent;
}


/* Expand preview to full width and dynamic height */
.content-preview {
  position: relative;
  width: 100%;
  max-width: 100%;
  height: v-bind(height);
  border: 2px dashed var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  background: var(--iluria-color-container-bg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.content-preview:hover {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-container-bg-hover);
}

.content-preview.empty {
  align-items: center;
  justify-content: center;
}

.edit-overlay {
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 64px;
  height: 64px;
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast);
  border-radius: 50%;
  opacity: 0.7;
  transition: all 0.3s ease;
  z-index: 2;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.content-preview:hover .edit-overlay {
  opacity: 1;
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.25);
}

.preview-content {
  position: relative;
  z-index: 1;
  flex: 1;
  overflow-y: auto;
  font-size: 14px;
  line-height: 1.5;
}

.preview-content :deep(h1),
.preview-content :deep(h2),
.preview-content :deep(h3),
.preview-content :deep(h4),
.preview-content :deep(h5),
.preview-content :deep(h6) {
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  font-weight: 600;
  font-size: 1.1em;
}

.preview-content :deep(p) {
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.preview-content :deep(ul),
.preview-content :deep(ol) {
  margin: 0 0 8px 0;
  padding-left: 16px;
  font-size: 0.9em;
}

.preview-content :deep(blockquote) {
  border-left: 3px solid var(--iluria-color-primary);
  padding-left: 12px;
  margin: 0 0 8px 0;
  font-style: italic;
  color: var(--iluria-color-text-secondary);
  font-size: 0.9em;
}

.preview-content :deep(pre) {
  background: var(--iluria-color-container-bg-secondary);
  padding: 8px;
  border-radius: 4px;
  overflow-x: auto;
  margin: 0 0 8px 0;
  font-size: 0.8em;
}

.preview-content :deep(hr) {
  border: none;
  height: 1px;
  background: var(--iluria-color-border);
  margin: 12px 0;
}

.preview-content :deep(.product-grid) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 8px;
  margin: 8px 0;
}

.preview-content :deep(.product-card) {
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  padding: 8px;
  font-size: 0.7em;
  text-align: center;
}

.preview-content :deep(.product-card img) {
  width: 100%;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
  margin-bottom: 4px;
}

.preview-content :deep(.product-title) {
  font-weight: 600;
  margin: 4px 0 2px 0;
  font-size: 0.9em;
}

.preview-content :deep(.product-description) {
  margin: 2px 0;
  font-size: 0.8em;
  opacity: 0.8;
}

.preview-content :deep(.product-rating) {
  margin: 2px 0;
  font-size: 0.7em;
}

.preview-content :deep(.product-button) {
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 0.7em;
  margin-top: 4px;
}

.empty-state {
  text-align: center;
  color: var(--iluria-color-text-muted);
  padding-top: 150px;
}

.empty-state h3 {
  margin: 16px 0 8px 0;
  color: var(--iluria-color-text);
}

.empty-state p {
  margin: 0;
  font-size: 14px;
}

.editor-modal-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  margin: -1.5rem;
  padding: 0.5rem;
}
</style>
