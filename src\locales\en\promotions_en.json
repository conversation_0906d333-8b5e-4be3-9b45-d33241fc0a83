{"yes": "Yes", "no": "No", "save": "Save", "cancel": "Cancel", "error": "Error", "success": "Success", "update": "Update", "promotionsTitle": "Promotions", "promotionsSubtitle": "Manage and configure promotions to boost your sales", "promotionsListTitle": "Promotions List", "promotionsListSubtitle": "View and manage all active and inactive promotions", "add": "New Promotion", "searchPlaceholder": "Search promotions...", "loadingPromotions": "Loading promotions...", "noPromotions": "No promotions found", "noPromotionsDescription": "You don't have any promotions yet. Create the first promotion to start boosting your sales.", "createFirstPromotion": "Create First Promotion", "nameHeader": "Name", "typeHeader": "Type", "discountValueHeader": "Discount Value", "activeHeader": "Active", "combinePromosHeader": "Combine Promotions", "createdHeader": "Created", "actionsHeader": "Actions", "editPromotion": "Edit promotion", "deletePromotion": "Delete promotion", "viewPromotion": "View promotion", "confirmDeleteTitle": "Confirm deletion", "confirmDeleteMessage": "Are you sure you want to delete the promotion {name}? This action cannot be undone.", "deleteSuccess": "Promotion deleted successfully", "deleteError": "Error deleting promotion", "loadError": "Error loading promotions", "freeShippingValue": "Free Shipping", "types": {"simpleDiscount": "Simple Discount", "progressiveDiscount": "Progressive Discount", "freeShipping": "Free Shipping", "gift": "Gift", "crossSelling": "<PERSON>lling", "firstPurchase": "First Purchase"}, "editor": {"loading": "Loading promotion...", "createTitle": "Create New Promotion", "createSubtitle": "Configure a new promotion to boost your sales", "editTitle": "Edit Promotion", "editSubtitle": "Update promotion settings", "basicDataTitle": "Basic Data", "basicDataSubtitle": "Main promotion information", "promotionName": "Promotion Name", "promotionNamePlaceholder": "Ex: Black Friday 2024", "promotionType": "Promotion Type", "selectPromotionType": "Select promotion type", "selectDiscountType": "Select discount type", "discountConfigTitle": "Discount Configuration", "discountConfigSubtitle": "Define discount type and value", "freeShippingTitle": "Free Shipping Configuration", "freeShippingSubtitle": "Configure conditions for free shipping", "progressiveDiscountTitle": "Progressive Discount Configuration", "progressiveDiscountSubtitle": "Configure discounts that increase with quantity or value", "giftConfigTitle": "Gift Configuration", "giftConfigSubtitle": "Define gifts and their conditions", "crossSellingTitle": "Cross Selling Configuration", "crossSellingSubtitle": "Configure suggested products and triggers", "minimumRequirementsTitle": "Minimum Requirements", "minimumRequirementsSubtitle": "Define minimum conditions to apply the promotion", "generalConditionsTitle": "General Conditions", "generalConditionsSubtitle": "Configure validity period and other conditions", "simpleDiscount": "Simple Discount", "progressiveDiscount": "Progressive Discount", "crossSelling": "<PERSON>lling", "firstPurchase": "First Purchase", "freeShipping": "Free Shipping", "gift": "Gift", "nameRequired": "Promotion name is required", "typeRequired": "Promotion type is required", "createSuccess": "Promotion created successfully", "updateSuccess": "Promotion updated successfully", "saveError": "Error saving promotion", "loadError": "Error loading promotion data", "noMinimumRequired": "No minimum requirements", "minimumOrderValue": "Minimum Order Value", "minimumItemQuantity": "Minimum Item Quantity", "minimumRequirements": "Minimum Requirements", "minimumOrderPlaceholder": "Ex: 100.00", "minimumItemsPlaceholder": "Ex: 5 units", "minQuantity": "Minimum Quantity", "minOrderValue": "Minimum Order Value", "discount": "Discount", "productsPlaceholder": "Ex: 3 units", "progressiveDiscountType": "Progressive Discount Type", "discountByQuantity": "Discount by Quantity", "discountByOrderValue": "Discount by Order Value", "addProgressiveTier": "Add New Tier", "giftType": "Gift Type", "simplifiedGift": "Simplified Gift", "productGift": "Product Gift", "giftDescription": "Gift Description", "product": "Product", "addGift": "Add Gift", "discountToApply": "Discount to Apply", "discountToApplyDescription": "Discount applied to suggested products", "productToReceiveDiscount": "Product to Receive Discount", "highestValueProduct": "Highest Value Product", "lowestValueProduct": "Lowest Value Product", "trigger": "<PERSON><PERSON>", "triggerDescription": "Defines when the promotion will be activated", "allStore": "All Store", "categories": "Categories", "products": "Products", "discountConfiguration": "Discount Configuration", "discountType": "Discount Type", "discountValue": "Discount Value", "discountValuePlaceholder": "Ex: 10", "discountTypeDescription": "Select discount type to configure the value", "percentage": "Percentage", "fixed": "Fixed", "selectMinimumRequirementType": "Select minimum requirement type"}}