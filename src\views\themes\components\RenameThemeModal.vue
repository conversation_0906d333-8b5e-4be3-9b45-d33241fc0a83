<template>
  <IluriaModal
    v-model="isVisible"
    :title="$t('themes.rename')"
    :subtitle="$t('themes.renameDescription')"
    :icon="EditIcon"
    icon-color="blue"
    @save="handleSave"
    @cancel="handleCancel"
    :save-label="isSaving ? $t('themes.renaming') : $t('themes.rename')"
    :cancel-label="$t('cancel')"
    :saving="isSaving"
    :dialog-style="{ 
      width: '500px', 
      maxWidth: '90vw'
    }"
  >
    <div class="rename-form">
      <div class="form-group">
        <label class="form-label">{{ $t('themes.currentName') }}</label>
        <div class="current-name">
          {{ theme?.name }}
        </div>
      </div>
      
      <div class="form-group">
        <label class="form-label">{{ $t('themes.newName') }}</label>
        <IluriaInputText
          v-model="newName"
          :placeholder="$t('themes.enterNewName')"
          :error="nameError"
          @keyup.enter="handleSave"
          @input="validateName"
          autofocus
          required
        />
        <div v-if="nameError" class="error-message">
          {{ nameError }}
        </div>
      </div>
      
      <div class="form-group">
        <div class="info-message">
          <HugeiconsIcon :icon="InformationCircleIcon" class="info-icon" />
          <span>{{ $t('themes.renameInfo') }}</span>
        </div>
      </div>
    </div>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  EditIcon,
  InformationCircleIcon
} from '@hugeicons-pro/core-stroke-standard'

import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'

const { t } = useI18n()

// Props
const props = defineProps({
  theme: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  },
  existingNames: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['update:visible', 'rename', 'cancel'])

// State
const isVisible = ref(false)
const newName = ref('')
const nameError = ref('')
const isSaving = ref(false)

// Computed
const canSave = computed(() => {
  return newName.value.trim() && 
         newName.value.trim() !== props.theme?.name &&
         !nameError.value &&
         !isSaving.value
})

// Methods
const validateName = () => {
  const trimmedName = newName.value.trim()
  
  if (!trimmedName) {
    nameError.value = t('themes.nameRequired')
    return false
  }
  
  if (trimmedName.length < 2) {
    nameError.value = t('themes.nameTooShort')
    return false
  }
  
  if (trimmedName.length > 100) {
    nameError.value = t('themes.nameTooLong')
    return false
  }
  
  if (trimmedName === props.theme?.name) {
    nameError.value = t('themes.nameSameAsCurrent')
    return false
  }
  
  if (props.existingNames.includes(trimmedName)) {
    nameError.value = t('themes.nameAlreadyExists')
    return false
  }
  
  nameError.value = ''
  return true
}

const handleSave = async () => {
  if (!validateName() || !canSave.value) return
  
  isSaving.value = true
  
  try {
    await emit('rename', {
      theme: props.theme,
      newName: newName.value.trim()
    })
    
    handleCancel()
  } catch (error) {
    console.error('Error renaming theme:', error)
    nameError.value = t('themes.renameError')
  } finally {
    isSaving.value = false
  }
}

const handleCancel = () => {
  newName.value = ''
  nameError.value = ''
  isSaving.value = false
  isVisible.value = false
  emit('update:visible', false)
  emit('cancel')
}

const resetForm = () => {
  newName.value = props.theme?.name || ''
  nameError.value = ''
  isSaving.value = false
}

// Watchers
watch(() => props.visible, (visible) => {
  isVisible.value = visible
  if (visible) {
    resetForm()
    nextTick(() => {
      // Focus no input após o modal abrir
      const input = document.querySelector('.rename-form input')
      if (input) {
        input.focus()
        input.select()
      }
    })
  }
})

watch(() => props.theme, () => {
  if (props.visible) {
    resetForm()
  }
})
</script>

<style scoped>
.rename-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.current-name {
  padding: 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

.error-message {
  font-size: 0.75rem;
  color: var(--iluria-color-error);
  margin-top: 0.25rem;
}

.info-message {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 0.75rem;
  background: var(--iluria-color-info-bg, #eff6ff);
  border: 1px solid var(--iluria-color-info-border, #bfdbfe);
  border-radius: 6px;
  font-size: 0.875rem;
  color: var(--iluria-color-info-text, #1e40af);
}

.info-icon {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}
</style>
