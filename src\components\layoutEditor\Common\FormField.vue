<template>
  <div class="form-field">
    <label v-if="label" :for="id" class="field-label">
      <span class="label-text">{{ label }}</span>
      <span v-if="valueDisplay" class="value-display">{{ valueDisplay }}</span>
    </label>
    
    <div class="field-input" :class="type">
      <slot></slot>
    </div>
    
    <span v-if="hint" class="field-hint">{{ hint }}</span>
  </div>
</template>

<script setup>
defineProps({
  id: {
    type: String,
    required: true
  },
  label: {
    type: String,
    default: ''
  },
  valueDisplay: {
    type: String,
    default: ''
  },
  hint: {
    type: String,
    default: ''
  },
  type: {
    type: String,
    default: 'default'
  }
})
</script>

<style scoped>
.form-field {
  margin-bottom: 0.75rem;
}

.field-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.25rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
}

.label-text {
  font-size: 0.875rem;
}

.value-display {
  font-size: 0.875rem;
  color: #94a3b8;
  font-family: monospace;
}

.field-input {
  background: #1a1a1a;
  border-radius: 6px;
  padding: 0.5rem;
}

.field-input.color {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.field-input.range {
  padding: 0.75rem 0.5rem;
}

.field-hint {
  display: block;
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: #94a3b8;
}

/* Tipos específicos de campos */
.field-input.text input,
.field-input.text textarea {
  width: 100%;
  background: var(--iluria-color-input-bg);
  border: 1px solid var(--iluria-color-input-border);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  color: var(--iluria-color-input-text);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.field-input.text input:focus,
.field-input.text textarea:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus);
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring);
}

.field-input.select select {
  width: 100%;
  background: var(--iluria-color-input-bg);
  border: 1px solid var(--iluria-color-input-border);
  border-radius: 0.375rem;
  padding: 0.375rem 0.75rem;
  color: var(--iluria-color-input-text);
  font-size: 0.875rem;
  cursor: pointer;
}

.field-input.select select:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus);
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring);
}

/* Responsividade */
@media (max-width: 768px) {
  .field-label {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.25rem;
  }
  
  .value-display {
    font-size: 0.75rem;
  }
}

.error {
  font-size: 0.75rem;
  color: var(--iluria-color-error);
  margin-top: 0.25rem;
}
</style> 
