<template>
  <div class="alerts-panel">
    <div v-if="loading" class="loading-state">
      <div v-for="i in 3" :key="i" class="alert-skeleton"></div>
    </div>
    
    <div v-else-if="!alerts.length" class="empty-state">
      <div class="empty-icon">
        <CheckmarkCircle01Icon class="icon" />
      </div>
      <h3 class="empty-title">{{ $t('dashboard.noAlerts') }}</h3>
      <p class="empty-description">{{ $t('dashboard.noAlertsDesc') }}</p>
    </div>
    
    <div v-else class="alerts-list">
      <div class="alerts-body">
        <div 
          v-for="alert in alerts" 
          :key="alert.id"
          class="alert-item"
          :class="getAlertClass(alert.type)"
          @click="handleAlertClick(alert)"
        >
          <div class="alert-icon">
            <component :is="getAlertIcon(alert.type)" class="icon" />
          </div>
          <div class="alert-content">
            <div class="alert-header">
              <h4 class="alert-title">{{ alert.title }}</h4>
              <span class="alert-time">{{ formatTime(alert.date) }}</span>
            </div>
            <p class="alert-message">{{ alert.message }}</p>
          </div>
          <div class="alert-actions">
            <button 
              class="action-button dismiss"
              @click.stop="dismissAlert(alert.id)"
              title="Dispensar alerta"
            >
              <CancelSquareIcon class="action-icon" />
            </button>
          </div>
        </div>
      </div>
      
      <div v-if="alerts.length > 3" class="alerts-footer">
        <IluriaButton 
          variant="ghost" 
          color="dark"
          size="small"
          @click="viewAllAlerts"
        >
          Ver todos os alertas
        </IluriaButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import {
  Alert02Icon,
  InformationCircleIcon,
  CheckmarkCircle01Icon,
  CancelCircleIcon,
  Cancel01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const props = defineProps({
  alerts: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const { t } = useI18n()

const emit = defineEmits(['dismiss-alert', 'view-alert'])

const getAlertClass = (type) => {
  const classes = {
    'warning': 'alert-warning',
    'info': 'alert-info',
    'success': 'alert-success',
    'error': 'alert-error'
  }
  return classes[type] || 'alert-info'
}

const getAlertIcon = (type) => {
  const icons = {
    'warning': Alert02Icon,
    'info': InformationCircleIcon,
    'success': CheckmarkCircle01Icon,
    'error': Cancel01Icon
  }
  return icons[type] || InformationCircleIcon
}

const formatTime = (dateString) => {
  const date = new Date(dateString)
  const now = new Date()
  const diffInMinutes = Math.floor((now - date) / (1000 * 60))
  
  if (diffInMinutes < 1) return 'Agora'
  if (diffInMinutes < 60) return `${diffInMinutes}min`
  
  const diffInHours = Math.floor(diffInMinutes / 60)
  if (diffInHours < 24) return `${diffInHours}h`
  
  const diffInDays = Math.floor(diffInHours / 24)
  return `${diffInDays}d`
}

const handleAlertClick = (alert) => {
  emit('view-alert', alert)
}

const dismissAlert = (alertId) => {
  emit('dismiss-alert', alertId)
}

const viewAllAlerts = () => {
  router.push('/notifications')
}
</script>

<style scoped>
.alerts-panel {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.alert-skeleton {
  height: 72px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 16px;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  background: rgba(16, 185, 129, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-icon .icon {
  width: 24px;
  height: 24px;
  color: var(--iluria-color-success);
}

.empty-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.empty-description {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.alerts-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.alerts-body {
  flex: 1;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.alert-item {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  border-radius: 12px;
  border: 1px solid;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.alert-item:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05);
}

.alert-icon {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-icon .icon {
  width: 18px;
  height: 18px;
}

.alert-content {
  flex: 1;
  min-width: 0;
}

.alert-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 4px;
}

.alert-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert-time {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  white-space: nowrap;
  flex-shrink: 0;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert-message {
  font-size: 0.8rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  margin: 0;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.alert-actions {
  display: flex;
  align-items: flex-start;
  gap: 4px;
  margin-top: 2px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.alert-item:hover .alert-actions {
  opacity: 1;
}

.action-button {
  width: 24px;
  height: 24px;
  border: none;
  background: none;
  cursor: pointer;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.2s ease;
}

.action-button:hover {
  background: rgba(0, 0, 0, 0.05);
}

.action-icon {
  width: 14px;
  height: 14px;
  color: #6b7280;
}

/* Alert types */
.alert-warning {
  background: rgba(245, 158, 11, 0.05);
  border-color: rgba(245, 158, 11, 0.2);
}

.alert-warning .alert-icon .icon {
  color: var(--iluria-color-warning);
}

.alert-info {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
}

.alert-info .alert-icon .icon {
  color: var(--iluria-color-info);
}

.alert-success {
  background: rgba(16, 185, 129, 0.05);
  border-color: rgba(16, 185, 129, 0.2);
}

.alert-success .alert-icon .icon {
  color: var(--iluria-color-success);
}

.alert-error {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
}

.alert-error .alert-icon .icon {
  color: var(--iluria-color-error);
}

.alerts-footer {
  padding: 16px 0;
  border-top: 1px solid #e5e7eb;
  display: flex;
  justify-content: center;
}

@media (max-width: 768px) {
  .alert-item {
    padding: 12px;
  }
  
  .alert-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }
  
  .alert-time {
    font-size: 0.7rem;
  }
  
  .alert-message {
    font-size: 0.75rem;
  }
  
  .alert-actions {
    opacity: 1;
    position: absolute;
    top: 12px;
    right: 12px;
  }
  
  .action-button {
    width: 20px;
    height: 20px;
  }
  
  .action-icon {
    width: 12px;
    height: 12px;
  }
}
</style> 
