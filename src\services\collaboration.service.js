import axios from 'axios';
import { API_URLS } from '../config/api.config';

const endpoint = API_URLS.COLLABORATION_BASE_URL;

export const collaborationApi = {
  // Buscar convites recebidos pelo usuário
  getMyInvites: () => {
    return axios.get(`${endpoint}/invitations`);
  },

  // Buscar lojas onde sou colaborador
  getMyCollaborations: () => {
    return axios.get(`${endpoint}/stores`);
  },

  // Aceitar convite de colaboração
  acceptInvite: (inviteId) => {
    return axios.post(`${endpoint}/invitations/accept`, {
      invitationId: inviteId
    });
  },

  // Recusar convite de colaboração
  rejectInvite: (inviteId) => {
    return axios.post(`${endpoint}/invitations/reject`, {
      invitationId: inviteId
    });
  },

  // Sair de uma loja (quando sou colaborador)
  leaveStore: (storeId) => {
    const payload = {
      storeId: storeId
    };
    return axios.post(`${endpoint}/stores/leave`, payload);
  }
};