<template>
  <IluriaModal
    v-model="modalVisible"
    :title="getModalTitle()"
    class="w-full max-w-4xl"
  >
  
    <!-- Modal Body -->
    <div class="p-4 md:p-6" :class="{ 'max-h-[50vh] overflow-y-auto': hasPriceRanges }">

      <!-- Stock and Price Fields (Side by side) -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-6">

        <!-- Unit Price Field -->
        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('productVariations.price') }}</IluriaLabel>

            <IluriaButton
              @click="copyPriceToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
              :title="t('product.copyPriceToAll')"
            />
          </div>

          <IluriaInputText
            v-model="unitPrice"
            type="money"
            prefix="R$"
          />
        </div>

        <!-- Stock Field -->
        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('productVariations.stock') }}</IluriaLabel>

            <IluriaButton
              @click="copyStockToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
              :title="t('product.copyStockToAll')"
            />
          </div>

          <IluriaInputText
            v-model="stock"
            type="number"
            min="0"
            suffix="unidade(s)"
          />
        </div>
      </div>

      <!-- Price Range Toggle -->
      <div class="mb-6">
        <IluriaCheckBox
          v-model="hasPriceRanges"
          :label="t('product.hasPriceRanges')"
        />
      </div>

        <!-- Price Ranges -->
        <div v-if="hasPriceRanges" class="mb-6">
          <PriceRanges
            :priceRanges="priceRanges"
            @addPriceRange="addPriceRange"
            @removeRange="removeRange"
            @updatePriceRanges="priceRanges = $event"
          />
        </div>

      <!-- Additional Fields -->
      <div class="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('productVariations.originalPrice') }}</IluriaLabel>

            <IluriaButton
              @click="copyOriginalPriceToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
              :title="t('product.copyOriginalPriceToAll')"
            />
          </div>

          <IluriaInputText
            v-model="originalPrice"
            type="money"
            prefix="R$"
          />
        </div>

        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('productVariations.costPrice') }}</IluriaLabel>
            <IluriaButton
              @click="copyCostPriceToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
              :title="t('product.copyCostPriceToAll')"
            />
          </div>

          <IluriaInputText
            v-model="costPrice"
            type="money"
            prefix="R$"
          />
        </div>

        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('product.weight') }}</IluriaLabel>

            <IluriaButton
              @click="copyWeightToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
            />
          </div>

          <IluriaInputText
            v-model="weight"
            type="number"
            min="0"
            suffix="g"
          />
        </div>
      </div>

      <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('product.boxLength') }}</IluriaLabel>

            <IluriaButton
              @click="copyLengthToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
            />
          </div>

          <IluriaInputText
            v-model="length"
            type="number"
            min="0"
            suffix="cm"
          />
        </div>

        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('product.boxWidth') }}</IluriaLabel>

            <IluriaButton
              @click="copyWidthToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
            />
          </div>

          <IluriaInputText
            v-model="width"
            type="number"
            min="0"
            suffix="cm"
          />
        </div>

        <div>
          <div class="flex items-center space-x-2 mb-1">
            <IluriaLabel>{{ t('product.boxDepth') }}</IluriaLabel>

            <IluriaButton
              @click="copyDepthToAll"
              color="text-primary"
              size="small"
              :huge-icon="Copy01Icon"
            />
          </div>

          <IluriaInputText
            v-model="depth"
            type="number"
            min="0"
            suffix="cm"
          />
        </div>
      </div>
    </div>

    <template #footer>
      <div class="flex justify-end space-x-2">
        <IluriaButton @click="coppyAllToAll" color="outline">
          {{ t('productVariations.copyAllToAll') }}
        </IluriaButton>

        <IluriaButton @click="save">
          {{ t('productVariations.save') }}
        </IluriaButton>

      </div>
    </template>
  </IluriaModal>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import { Copy01Icon, Delete03Icon, AddSquareIcon } from '@hugeicons-pro/core-bulk-rounded';
import IluriaModal from '@/components/iluria/IluriaModal.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import PriceRanges from '@/components/productVariations/PriceRanges.vue';


const { t } = useI18n();

const props = defineProps({
  isOpen: Boolean,
  variation: Object,
  allVariations: Array,
  groupBy: String
});

const emit = defineEmits(['close', 'save', 'copyStock', 'copyPrice', 'copyOriginalPrice',
'copyCostPrice', 'copyWeight', 'copyLength', 'copyWidth', 'copyDepth', 'copyAllToAll']);
const modalVisible = computed({
  get: () => props.isOpen,
  set: (value) => {
    if (!value) {
      close();
    }
  }
});

const hasPriceRanges = ref(false);
const priceRanges = ref([]);
const stock = ref('');
const unitPrice = ref('');
const originalPrice = ref('');
const costPrice = ref('');
const weight = ref('');
const length = ref('');
const width = ref('');
const depth = ref('');

const getModalTitle = () => {
  const baseTitle = t('productVariations.editVariation');
  if (!props.variation?.options || !props.groupBy) return baseTitle;
  const options = { ...props.variation.options };
  const orderedOptions = [];
  if (props.groupBy in options) {
    orderedOptions.push(options[props.groupBy]);
    delete options[props.groupBy];
  }
  Object.keys(options).forEach(key => {
    orderedOptions.push(options[key]);
  });
  return `${baseTitle} (${orderedOptions.join(' / ')})`;
};

const parseMoneyValue = (value) => {
  if (!value) return 0;
  if (typeof value === 'number') return value;
  // Remove formatação monetária e converte para número
  const cleanValue = String(value).replace(/[R$\s.]/g, '').replace(',', '.');
  return parseFloat(cleanValue) || 0;
};

const formatMoneyValue = (value) => {
  if (!value || value === 0 || value === '0') return '';
  return String(value);
};

watch(() => props.variation, (newVariation) => {
  if (newVariation) {
    hasPriceRanges.value = false;
    priceRanges.value = [
      { quantity: 1, price: 0 }
    ];
    stock.value = newVariation.stock ? String(newVariation.stock) : '';
    unitPrice.value = formatMoneyValue(newVariation.price);
    originalPrice.value = formatMoneyValue(newVariation.originalPrice);
    costPrice.value = formatMoneyValue(newVariation.costPrice);
    weight.value = newVariation.weight ? String(newVariation.weight) : '';
    length.value = newVariation.length ? String(newVariation.length) : '';
    width.value = newVariation.width ? String(newVariation.width) : '';
    depth.value = newVariation.depth ? String(newVariation.depth) : '';
    if (newVariation.priceRanges && newVariation.priceRanges.length > 0) {
      hasPriceRanges.value = true;
      priceRanges.value = [...newVariation.priceRanges];
    } else {
      hasPriceRanges.value = false;
      priceRanges.value = [{ quantity: 1, price: 0 }];
    }
  }
}, { immediate: true });
watch(() => hasPriceRanges.value, (newValue) => {
  if (!newValue) {
    priceRanges.value = [];
  } else if (newValue && priceRanges.value.length === 0) {
    priceRanges.value = [{ quantity: 1, price: parseMoneyValue(unitPrice.value) || 0 }];
  }
});

const addPriceRange = () => {
  priceRanges.value.push({ quantity: 0, price: 0 });
};

const removeRange = (index) => {
  priceRanges.value.splice(index, 1);
};

const copyStockToAll = () => {
  emit('copyStock', stock.value);
};

const copyOriginalPriceToAll = () => {
  emit('copyOriginalPrice', parseMoneyValue(originalPrice.value));
};

const copyCostPriceToAll = () => {
  emit('copyCostPrice', parseMoneyValue(costPrice.value));
};

const copyPriceToAll = () => {
  emit('copyPrice', parseMoneyValue(unitPrice.value));
};

const copyLengthToAll = () => {
  emit('copyLength', length.value);
};

const copyWidthToAll = () => {
  emit('copyWidth', width.value);
};

const copyDepthToAll = () => {
  emit('copyDepth', depth.value);
};

const copyWeightToAll = () => {
  emit('copyWeight', weight.value);
};

const coppyAllToAll = () => {
  const currentValues = {
    price: parseMoneyValue(unitPrice.value),
    stock: stock.value,
    originalPrice: parseMoneyValue(originalPrice.value),
    costPrice: parseMoneyValue(costPrice.value),
    weight: weight.value,
    length: length.value,
    width: width.value,
    depth: depth.value,
    priceRanges: hasPriceRanges.value ? [...priceRanges.value] : [],
    hasPriceRanges: hasPriceRanges.value
  };
  emit('copyAllToAll', currentValues);
};

const close = () => {
  emit('close');
};

const save = () => {
  const data = {
    hasPriceRanges: hasPriceRanges.value,
    priceRanges: priceRanges.value,
    stock: stock.value || 0,
    unitPrice: parseMoneyValue(unitPrice.value),
    originalPrice: parseMoneyValue(originalPrice.value),
    costPrice: parseMoneyValue(costPrice.value),
    weight: weight.value || 0,
    length: length.value || 0,
    width: width.value || 0,
    depth: depth.value || 0
  };
  emit('save', data);
  close();
};
</script>
