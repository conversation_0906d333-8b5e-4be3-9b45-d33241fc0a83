<template>
  <div class="create-card">
    <div class="card-icon-container">
      <div :class="bgColor + ' card-icon-background'">
        <HugeiconsIcon :size="32" :icon="icon" class="card-icon" />
      </div>
    </div>

    <IluriaTitle class="card-title">{{ title }}</IluriaTitle>
    <IluriaText class="card-description">{{ description }}</IluriaText>
  </div>
</template>

<script setup>
import IluriaTitle from "@/components/iluria/IluriaTitle.vue";
import IluriaText from "@/components/iluria/IluriaText.vue";
import {HugeiconsIcon} from '@hugeicons/vue';

defineProps({
  title: String,
  description: String,
  bgColor: String,
  icon: Object
});
</script>

<style scoped>
.create-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 24px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: center;
  min-height: 200px;
  justify-content: center;
}

.create-card:hover {
  transform: translateY(-2px);
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-md);
}

.card-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.card-icon-background {
  width: 64px;
  height: 64px;
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: transform 0.2s ease;
}

.create-card:hover .card-icon-background {
  transform: scale(1.05);
}

.card-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.card-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 8px;
  line-height: 1.3;
}

.card-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  max-width: 250px;
}

/* Tailwind classes mapping for background colors */
.bg-green-400 {
  background-color: #4ade80;
}

.bg-yellow-400 {
  background-color: #facc15;
}

.bg-blue-400 {
  background-color: #60a5fa;
}

.bg-purple-400 {
  background-color: #c084fc;
}

.bg-red-400 {
  background-color: #f87171;
}

/* Responsive */
@media (max-width: 768px) {
  .create-card {
    padding: 24px 20px;
    min-height: 180px;
  }
  
  .card-icon-background {
    width: 56px;
    height: 56px;
  }
  
  .card-title {
    font-size: 16px;
  }
  
  .card-description {
    font-size: 13px;
  }
}

@media (max-width: 480px) {
  .create-card {
    padding: 20px 16px;
    min-height: 160px;
  }
  
  .card-icon-background {
    width: 48px;
    height: 48px;
  }
  
  .card-title {
    font-size: 15px;
  }
}
</style>
