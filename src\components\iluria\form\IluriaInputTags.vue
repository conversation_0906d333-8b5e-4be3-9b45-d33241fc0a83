<template>
  <div>
    <label :for="id" v-if="label" class="mb-2 cursor-pointer block text-sm font-normal">
      {{ label }}
    </label>
    <div class="tags-container">
      <div class="flex flex-wrap gap-2 mb-1">
        <draggable
          v-if="draggable"
          v-model="modelValue"
          item-key="id"
          handle=".tag-drag-handle"
          :animation="150"
          class="flex flex-wrap gap-2"
        >
          <template #item="{ element: tag, index }">
            <span class="tag">
              <HugeiconsIcon :icon="DragDropVerticalIcon" name="drag" class="tag-drag-handle cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-primary)] mr-1" />
              {{ tag }}
              <button class="remove-tag" @click="removeTag(index)">×</button>
            </span>
          </template>
        </draggable>
        
        <template v-else>
          <span v-for="(tag, index) in modelValue" :key="index" class="tag">
            {{ tag }}
            <button class="remove-tag" @click="removeTag(index)">×</button>
          </span>
        </template>
      </div>

      <div class="add-tag flex">
        <InputText
          :id="id"
          :name="name || id"
          v-model="newTag"
          :disabled="disabled"
          :placeholder="placeholder"
          :required="required"
          :unstyled="true"
          @keydown.enter.prevent="addTag"
          @input="handleInput"
          @keydown="handleKeyDown"
          class="w-full block px-3 py-2 rounded-lg text-base sm:text-sm/6 mr-2
            placeholder:text-[var(--iluria-color-input-placeholder)]
            border-2 border-[var(--iluria-color-input-border)]
            bg-[var(--iluria-color-input-bg)]
            focus:border-[var(--iluria-color-input-border-focus)]
            focus:shadow-[0_0_0_2px_var(--iluria-color-focus-ring)]"
          :class="[inputClass,
            formContext?.invalid
              ? 'border-[var(--iluria-color-error)] bg-red-100 focus:border-[var(--iluria-color-error)] focus:shadow-[0_0_0_2px_var(--iluria-color-error)]'
              : '']"
        />
        <IluriaButton  @click="addTag">{{ t('add') }}</IluriaButton>
      </div>
    </div>
    <Message v-if="formContext && formContext?.invalid" severity="error" size="small" variant="simple"
      class="mt-1">
      {{ formContext?.error?.message }} 
    </Message>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { InputText, Message } from 'primevue';
import { useToast } from '../../../services/toast.service';
import { useI18n } from 'vue-i18n';
import draggable from 'vuedraggable';
import { DragDropVerticalIcon } from '@hugeicons-pro/core-bulk-rounded';
import { HugeiconsIcon } from '@hugeicons/vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';

const { t } = useI18n();
const toast = useToast();
const modelValue = defineModel();

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    required: true
  },
  inputClass: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  formContext: {
    type: Object,
    default: null
  },
  draggable: {
    type: Boolean,
    default: true
  }
});

watch(() => modelValue.value, (newVal) => {
  if (!newVal) {
    modelValue.value = [];
  }
}, { immediate: true });

const newTag = ref('');

watch(() => modelValue.value, () => {
  newTag.value = '';
}, { immediate: true });

onMounted(() => {
  newTag.value = '';
});

function addTag() {
  const trimmedTag = newTag.value.trim();
  if (trimmedTag) {
    const tagExists = (modelValue.value || []).some(tag => 
      tag.toLowerCase() === trimmedTag.toLowerCase()
    );
    
    if (!tagExists) {
      const updatedTags = [...(modelValue.value || []), trimmedTag];
      modelValue.value = updatedTags;
    } else {
      toast.showInfo(t('tagSystem.alreadyExists', { tag: trimmedTag }) || `A tag "${trimmedTag}" já existe`);
    }
    
    newTag.value = '';
  }
}

function removeTag(index) {
  const updatedTags = [...(modelValue.value || [])];
  updatedTags.splice(index, 1);
  modelValue.value = updatedTags;
}

const handleInput = (event) => {
  const value = event.target.value;
  
  if (value.includes(',')) {
    const parts = value.split(',');
    
    for (let i = 0; i < parts.length - 1; i++) {
      const tagText = parts[i].trim();
      if (tagText) {
        newTag.value = tagText;
        addTag();
      }
    }
    
    newTag.value = parts[parts.length - 1].trim();
  }
}

function handleKeyDown(event) {
  if (event.key === 'Enter' || event.key === ',') {
    event.preventDefault();
    addTag();
  }
}
</script>

<style scoped>
.tags-container {
  width: 100%;
}

.tag {
  display: inline-flex;
  align-items: center;
  background-color: var(--iluria-color-surface-hover);
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  padding: 4px 8px;
  margin-right: 4px;
  margin-bottom: 4px;
  font-size: 14px;
  color: var(--iluria-color-text-primary);
}

.tag-drag-handle {
  cursor: move;
  font-size: 0.875rem;
}

.remove-tag {
  margin-left: 6px;
  font-size: 16px;
  line-height: 1;
  cursor: pointer;
  color: var(--iluria-color-text-tertiary);
  transition: color 0.2s ease;
}

.remove-tag:hover {
  color: var(--iluria-color-error);
}
</style>
