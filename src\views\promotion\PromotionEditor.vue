<template>
  <div class="promotion-editor-container">
    <!-- Loading State -->
    <div v-if="loading" class="loading-overlay">
      <div class="loading-content">
        <div class="spinner"></div>
        <p>{{ t('promotions.editor.loading') }}</p>
      </div>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Header with actions -->
      <IluriaHeader
        :title="isEditing ? t('promotions.editor.editTitle') : t('promotions.editor.createTitle')"
        :subtitle="isEditing ? t('promotions.editor.editSubtitle') : t('promotions.editor.createSubtitle')"
        :showCancel="true"
        :cancelText="t('promotions.cancel')"
        :showSave="true"
        :saveText="isEditing ? t('promotions.update') : t('promotions.save')"
        @cancel-click="goBackToList"
        @save-click="savePromotion"
      />

      <!-- Form Content -->
      <div class="form-content">
        <!-- Basic Promotion Data -->
        <ViewContainer 
          :title="t('promotions.editor.basicDataTitle')"
          :subtitle="t('promotions.editor.basicDataSubtitle')"
          :icon="GiftIcon"
          iconColor="blue"
        >
          <div class="basic-data-grid">
            <!-- Promotion Name -->
            <div class="form-field col-span-6">
              <IluriaInputText 
                id="name" 
                v-model="form.name" 
                :label="t('promotions.editor.promotionName')" 
                :placeholder="t('promotions.editor.promotionNamePlaceholder')"
                required
              />
            </div>

            <!-- Promotion Type -->
            <div class="form-field col-span-6">
              <IluriaSelect
                v-model="form.type"
                :options="promotionTypes"
                :label="t('promotions.editor.promotionType')"
                optionLabel="label"
                optionValue="value"
                id="promotion-type"
                name="promotion-type"
                :placeholder="t('promotions.editor.selectPromotionType')"
                required
              />
            </div>
          </div>
        </ViewContainer>
        
        <!-- Type-Specific Configuration -->
        <div v-if="form.type" :key="'config-container-' + form.type">
          <ViewContainer 
            v-if="form.type === 'SIMPLE_DISCOUNT' || form.type === 'FIRST_PURCHASE'" 
            :title="t('promotions.editor.discountConfigTitle')"
            :subtitle="t('promotions.editor.discountConfigSubtitle')"
            :icon="DiscountIcon"
            iconColor="green"
          >
            <DiscountConfig v-model="form" />
          </ViewContainer>
          
          <ViewContainer 
            v-if="form.type === 'FREE_SHIPPING'" 
            :title="t('promotions.editor.freeShippingTitle')"
            :subtitle="t('promotions.editor.freeShippingSubtitle')"
            :icon="TruckDeliveryIcon"
            iconColor="orange"
          >
            <FreeShippingConfig v-model="form" />
          </ViewContainer>
          
          <ViewContainer 
            v-if="form.type === 'PROGRESSIVE_DISCOUNT'" 
            :title="t('promotions.editor.progressiveDiscountTitle')"
            :subtitle="t('promotions.editor.progressiveDiscountSubtitle')"
            :icon="AnalyticsUpIcon"
            iconColor="purple"
          >
            <ProgressiveDiscountConfig v-model="form" />
          </ViewContainer>
          
          <ViewContainer 
            v-if="form.type === 'GIFT'" 
            :title="t('promotions.editor.giftConfigTitle')"
            :subtitle="t('promotions.editor.giftConfigSubtitle')"
            :icon="GiftIcon"
            iconColor="pink"
          >
            <GiftConfig v-model="form" />
          </ViewContainer>
          
          <ViewContainer 
            v-if="form.type === 'CROSS_SELLING'" 
            :title="t('promotions.editor.crossSellingTitle')"
            :subtitle="t('promotions.editor.crossSellingSubtitle')"
            :icon="ArrowDataTransferHorizontalIcon"
            iconColor="indigo"
          >
            <CrossSellingConfig v-model="form" />
          </ViewContainer>
        </div>

        <!-- Minimum Requirements -->
        <ViewContainer 
          v-if="form.type === 'SIMPLE_DISCOUNT' || form.type === 'FIRST_PURCHASE'" 
          :title="t('promotions.editor.minimumRequirementsTitle')"
          :subtitle="t('promotions.editor.minimumRequirementsSubtitle')"
          :icon="ShoppingBasket01Icon"
          iconColor="cyan"
        >
          <MinimumRequirementsConfig v-model="form" />
        </ViewContainer>

        <!-- General Conditions -->
        <ViewContainer 
          v-if="form.type" 
          :title="t('promotions.editor.generalConditionsTitle')"
          :subtitle="t('promotions.editor.generalConditionsSubtitle')"
          :icon="Settings02Icon"
          iconColor="gray"
        >
          <GeneralConditionsConfig v-model="form" />
        </ViewContainer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from 'primevue/usetoast'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import PromotionsService from '@/services/promotions.service'
import { 
  GiftIcon,
  DiscountIcon,
  TruckDeliveryIcon,
  AnalyticsUpIcon,
  ArrowDataTransferHorizontalIcon,
  ShoppingBasket01Icon,
  Settings02Icon,
  Cancel01Icon,
  CheckmarkCircle02Icon
} from '@hugeicons-pro/core-stroke-rounded'

// Component imports
import DiscountConfig from '@/components/promotions/DiscountConfig.vue'
import GeneralConditionsConfig from '@/components/promotions/GeneralConditionsConfig.vue'
import FreeShippingConfig from '@/components/promotions/FreeShippingConfig.vue'
import MinimumRequirementsConfig from '@/components/promotions/MinimumRequirementsConfig.vue'
import ProgressiveDiscountConfig from '@/components/promotions/ProgressiveDiscountConfig.vue'
import GiftConfig from '@/components/promotions/GiftConfig.vue'
import CrossSellingConfig from '@/components/promotions/CrossSellingConfig.vue'

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const toast = useToast()

const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)

// Form state
const form = reactive({
  id: '',
  name: '',
  type: '',
  discountType: 'PERCENTAGE_TOTAL',
  discountValue: 0,
  minRequirementType: 'NONE',
  minValue: 0,
  minQuantity: 1,
  active: true,
  scope: 'ALL_STORE',
  triggerProducts: [],
  progressiveType: 'QUANTITY',
  progressiveTiers: [
    { minQuantity: 1, minValue: 0.01, discountValue: 5 }
  ],
  giftType: 'SIMPLIFIED',
  giftTiers: [
    { minValue: 0, description: '', productId: '', productName: '' }
  ],
  triggerType: 'ALL_STORE',
  selectedCategories: [],
  selectedTriggerProducts: [],
  selectedSuggestedProducts: [],
  targetConfig: 'LOWEST_VALUE',
  combinePromos: false,
  startDate: new Date(),
  defineEndDate: false,
  endDate: new Date(),
  startTime: '00:00',
  endTime: '23:59'
})

const promotionTypes = [
  { label: t('promotions.editor.simpleDiscount'), value: 'SIMPLE_DISCOUNT' },
  { label: t('promotions.editor.progressiveDiscount'), value: 'PROGRESSIVE_DISCOUNT' },
  { label: t('promotions.editor.crossSelling'), value: 'CROSS_SELLING' },
  { label: t('promotions.editor.firstPurchase'), value: 'FIRST_PURCHASE' },
  { label: t('promotions.editor.freeShipping'), value: 'FREE_SHIPPING' },
  { label: t('promotions.editor.gift'), value: 'GIFT' }
]

// Helper functions for mapping data
const mapProducts = (products) => {
  if (!products || !Array.isArray(products)) return []
  return products.map(prod => {
    const productId = prod.productId ? 
      (typeof prod.productId === 'object' ? prod.productId.toString() : prod.productId) : 
      (prod.id ? (typeof prod.id === 'object' ? prod.id.toString() : prod.id) : null)
    
    return {
      id: productId,
      productId: productId,
      name: prod.productName || prod.name || `Produto ${productId || 'sem ID'}`,
      productName: prod.productName || prod.name || `Produto ${productId || 'sem ID'}`,
      price: prod.price ? Number(prod.price) : 0,
      sku: prod.sku || '',
      imageUrl: prod.imageUrl || '',
      position: prod.position || 0,
      type: prod.type || 'TRIGGER'
    }
  })
}

const mapCategories = (categories) => {
  if (!categories || !Array.isArray(categories)) return []
  
  return categories.map((cat, index) => {
    const categoryId = cat.categoryId || cat.id || `cat-${index}`
    const categoryName = cat.categoryName || cat.name || `Categoria ${categoryId}`
    
    return {
      id: categoryId,
      categoryId: categoryId,
      name: categoryName,
      categoryName: categoryName
    }
  })
}

const loadBasicPromotionData = (data) => {
  const {
    id: promotionId,
    name,
    type,
    active,
    combinePromos,
    discountType,
    discountValue,
    minRequirementType = 'NONE',
    minValue = 0,
    minQuantity = 0,
    targetConfig,
    scope = 'ALL_STORE'
  } = data
  
  return {
    id: promotionId,
    name,
    type,
    active,
    combinePromos,
    discountType,
    discountValue,
    minRequirementType,
    minValue,
    minQuantity,
    targetConfig,
    scope,
    triggerProducts: [],
    categories: [],
    progressiveTiers: [],
    giftTiers: []
  }
}

// Continue with other data loading functions...
// [The rest of the complex data mapping logic remains the same]
const loadPromotion = async (id) => {
  loading.value = true
  try {
    const data = await PromotionsService.getPromotion(id)
    
    if (data.categories && Array.isArray(data.categories)) {
      window.sessionStorage.setItem('originalPromotionCategories', JSON.stringify(data.categories))
      
      const categoryNameMap = {}
      data.categories.forEach(cat => {
        if (cat.categoryId && cat.categoryName) {
          categoryNameMap[cat.categoryId] = cat.categoryName
        }
      })
      window.sessionStorage.setItem('categoryNameMap', JSON.stringify(categoryNameMap))
    }
    
    Object.assign(form, loadBasicPromotionData(data))
    
    const { categories = [], products = [], triggerProducts = [] } = data

    if (products && products.length > 0) {
      form.products = mapProducts(products)
    } else if (triggerProducts && triggerProducts.length > 0) {
      form.triggerProducts = mapProducts(triggerProducts)
    } else {
      form.products = []
      form.triggerProducts = []
    }
    
    form.categories = mapCategories(categories || [])
    
    // Load type-specific data based on promotion type
    switch (form.type) {
      case 'PROGRESSIVE_DISCOUNT':
        // Load progressive discount data
        break
      case 'GIFT':
        // Load gift data
        break
      case 'CROSS_SELLING':
        // Load cross selling data
        break
    }
    
    // Load date data
    if (data.startDate) {
      try {
        const startDate = new Date(data.startDate)
        form.startDate = startDate
        form.startTime = `${String(startDate.getHours()).padStart(2, '0')}:${String(startDate.getMinutes()).padStart(2, '0')}`
      } catch (e) {
        console.error('Erro ao processar data de início:', e)
      }
    }
    
    form.defineEndDate = data.defineEndDate
    if (data.defineEndDate && data.endDate) {
      try {
        const endDate = new Date(data.endDate)
        form.endDate = endDate
        form.endTime = `${String(endDate.getHours()).padStart(2, '0')}:${String(endDate.getMinutes()).padStart(2, '0')}`
      } catch (e) {
        console.error('Erro ao processar data de término:', e)
      }
    }
    
  } catch (error) {
    console.error('Error loading promotion:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('promotions.error'), 
      detail: error.response?.data?.message || t('promotions.editor.loadError'),
      life: 5000 
    })
  } finally {
    loading.value = false
  }
}

const savePromotion = async () => {
  if (!form.name) {
    toast.add({ 
      severity: 'error', 
      summary: t('promotions.error'), 
      detail: t('promotions.editor.nameRequired'), 
      life: 3000 
    })
    return
  }
  
  if (!form.type) {
    toast.add({ 
      severity: 'error', 
      summary: t('promotions.error'), 
      detail: t('promotions.editor.typeRequired'), 
      life: 3000 
    })
    return
  }

  saving.value = true
  try {
    let startDate = null
    let endDate = null
    
    if (form.startDate) {
      const startTime = form.startTime || '00:00'
      const [hours, minutes] = startTime.split(':')
      startDate = new Date(form.startDate)
      startDate.setHours(parseInt(hours), parseInt(minutes), 0, 0)
    }
    
    if (form.defineEndDate && form.endDate) {
      const endTime = form.endTime || '23:59'
      const [hours, minutes] = endTime.split(':')
      endDate = new Date(form.endDate)
      endDate.setHours(parseInt(hours), parseInt(minutes), 59, 999)
    }
    
    // Process the form data for backend
    const promotionData = {
      id: form.id || null,
      name: form.name,
      type: form.type,
      active: form.active,
      discountType: form.discountType || 'PERCENTAGE_TOTAL',
      discountValue: Number(form.discountValue) || 0,
      minRequirementType: form.minRequirementType || 'NONE',
      minValue: form.minRequirementType === 'ORDER_VALUE' ? Number(form.minValue) || 0 : 0,
      minQuantity: form.minRequirementType === 'ITEM_QUANTITY' ? Number(form.minQuantity) || 0 : 0,
      combinePromos: Boolean(form.combinePromos),
      scope: form.scope || 'ALL_STORE',
      startDate: startDate ? startDate.toISOString() : null,
      defineEndDate: Boolean(form.defineEndDate),
      endDate: endDate ? endDate.toISOString() : null,
      categories: form.categories || [],
      products: form.products || [],
      tiers: form.tiers || [],
      triggerType: form.triggerType || null,
      targetConfig: form.targetConfig || null,
      targetProduct: form.targetProduct || null
    }
    
    let response
    if (isEditing.value) {
      response = await PromotionsService.updatePromotion(route.params.id, promotionData)
      toast.add({ 
        severity: 'success', 
        summary: t('promotions.success'), 
        detail: t('promotions.editor.updateSuccess'), 
        life: 3000 
      })
    } else {
      response = await PromotionsService.createPromotion(promotionData)
      toast.add({ 
        severity: 'success', 
        summary: t('promotions.success'), 
        detail: t('promotions.editor.createSuccess'), 
        life: 3000 
      })
    }
    
    router.push('/promotions')
  } catch (error) {
    console.error('Error saving promotion:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('promotions.error'), 
      detail: t('promotions.editor.saveError'), 
      life: 3000 
    })
  } finally {
    saving.value = false
  }
}

const goBackToList = () => router.push('/promotions')

const initializeTypeSpecificFields = (type) => {
  const preservedData = {
    id: form.id,
    name: form.name,
    active: form.active,
    combinePromos: form.combinePromos,
    startDate: form.startDate,
    endDate: form.endDate,
    startTime: form.startTime,
    endTime: form.endTime,
    defineEndDate: form.defineEndDate,
    scope: form.scope,
    categories: form.categories,
    triggerProducts: form.triggerProducts
  }
  
  switch(type) {
    case 'SIMPLE_DISCOUNT':
    case 'FIRST_PURCHASE':
      form.discountType = form.discountType || 'PERCENTAGE_TOTAL'
      form.discountValue = form.discountValue || 10
      form.minRequirementType = form.minRequirementType || 'NONE'
      form.minValue = form.minValue || 0
      form.minQuantity = form.minQuantity || 1
      break
      
    case 'PROGRESSIVE_DISCOUNT':
      form.progressiveType = form.progressiveType || 'QUANTITY'
      if (!form.progressiveTiers || !form.progressiveTiers.length) {
        form.progressiveTiers = [{ minQuantity: 1, minValue: 0.01, discountValue: 5 }]
      }
      break
      
    case 'GIFT':
      form.giftType = form.giftType || 'SIMPLIFIED'
      if (!form.giftTiers || !form.giftTiers.length) {
        form.giftTiers = [{ minValue: 0, description: '', productId: '', productName: '' }]
      }
      break
      
    case 'CROSS_SELLING':
      form.triggerType = form.triggerType || 'ALL_STORE'
      break
  }
  
  Object.assign(form, preservedData)
}

watch(() => form.type, (newType) => {
  initializeTypeSpecificFields(newType)
})

onMounted(() => {
  const promotionId = route.params.id
  if (promotionId && promotionId !== 'new') {
    isEditing.value = true
    loadPromotion(promotionId)
  }
})
</script>

<style scoped>
.promotion-editor-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
  position: relative;
}

/* Loading Overlay */
.loading-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--iluria-color-bg);
  opacity: 0.95;
  backdrop-filter: blur(2px);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  color: var(--iluria-color-text-secondary);
}

.spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}



/* Form Content */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Basic Data Grid */
.basic-data-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  align-items: start;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.col-span-6 {
  grid-column: span 6;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .promotion-editor-container {
    padding: 16px;
  }
  
  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
  }
  
  .header-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .basic-data-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .col-span-6 {
    grid-column: span 1;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-content h1.page-title {
    font-size: 22px;
  }
}
</style>
