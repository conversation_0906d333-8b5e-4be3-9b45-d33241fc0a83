<template>
  <div class="export-list-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('product.exportList.title')"
      :subtitle="t('product.exportList.subtitle')"
      :showAdd="true"
      :addText="t('product.exportList.newExport')"
      :addIcon="PlusSignIcon"
      :customButtons="headerCustomButtons"
      @add-click="goToNewExport"
      @custom-click="handleCustomButtonClick"
    />

    <!-- Exports Table -->
    <div class="table-wrapper">

      <IluriaDataTable
        :value="exports"
        :loading="loading"
        dataKey="id"
        class="exports-table"
      >
        <!-- Coluna do Nome do Arquivo -->
        <Column field="fileName" :header="t('product.exportList.fileName')" headerClass="col-large" class="col-large">
          <template #body="slotProps">
            <div class="flex items-center">
              <div class="flex-shrink-0 w-8 h-8 rounded-lg flex items-center justify-center mr-3"
                   :class="slotProps.data.fileType === 'CSV' ? 'bg-blue-100' : 'bg-green-100'">
                <HugeiconsIcon
                  v-if="slotProps.data.fileType === 'CSV'"
                  :icon="File01Icon"
                  :size="16"
                  class="text-blue-600"
                />
                <HugeiconsIcon
                  v-else-if="slotProps.data.fileType === 'XLSX'"
                  :icon="Xls01Icon"
                  :size="16"
                  class="text-green-600"
                />
                <HugeiconsIcon
                  v-else
                  :icon="File01Icon"
                  :size="16"
                  class="text-gray-600"
                />
              </div>
              <div class="min-w-0 flex-1">
                <div class="text-sm font-medium text-gray-900 truncate" :title="slotProps.data.fileName || generateFileName(slotProps.data)">
                  {{ slotProps.data.fileName || generateFileName(slotProps.data) }}
                </div>
                <div class="text-xs text-gray-500">
                  {{ slotProps.data.fileType || 'CSV' }}
                </div>
              </div>
            </div>
          </template>
        </Column>

        <!-- Coluna da Data de Criação -->
        <Column field="createdAt" :header="t('product.exportList.createdAt')" headerClass="col-date" class="col-date">
          <template #body="slotProps">
            <span class="text-sm">{{ formatDate(slotProps.data.createdAt) }}</span>
          </template>
        </Column>

        <!-- Coluna do Tamanho do Arquivo -->
        <Column field="fileSize" :header="t('product.exportList.fileSize')" headerClass="col-small" class="col-small">
          <template #body="slotProps">
            <span class="text-sm">{{ formatFileSize(slotProps.data.fileSize) }}</span>
          </template>
        </Column>

        <!-- Coluna do Status -->
        <Column field="status" :header="t('product.exportList.status')" headerClass="col-small" class="col-small">
          <template #body="slotProps">
            <span :class="getStatusClasses(slotProps.data.status)" class="inline-flex px-2 py-1 text-xs font-semibold rounded-full">
              {{ getStatusText(slotProps.data.status) }}
            </span>
          </template>
        </Column>

        <!-- Coluna de Ações -->
        <Column field="actions" :header="t('product.exportList.actions')" headerClass="col-actions" class="col-actions">
          <template #body="slotProps">
            <div class="flex items-center gap-1">
              <IluriaButton
                v-if="canDownload(slotProps.data)"
                @click="downloadExport(slotProps.data)"
                color="primary"
                variant="ghost"
                size="small"
                :hugeIcon="Download01Icon"
                :title="t('product.exportList.download')"
                class="action-btn"
              />

              <IluriaButton
                v-if="canDelete(slotProps.data)"
                @click="confirmDelete(slotProps.data)"
                color="text-danger"
                variant="ghost"
                size="small"
                :hugeIcon="Delete01Icon"
                :title="t('product.exportList.delete')"
                class="action-btn"
              />

              <div v-if="slotProps.data.status === 'IN_PROGRESS'" class="flex items-center justify-center w-8 h-8">
                <div class="animate-spin w-4 h-4 border-2 border-blue-600 border-t-transparent rounded-full"></div>
              </div>
            </div>
          </template>
        </Column>

        <!-- Estado vazio -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <HugeiconsIcon :icon="PackageIcon" :size="48" class="text-gray-400" />
            </div>
            <h3 class="empty-title">{{ t('product.exportList.noExports') }}</h3>
            <p class="empty-description">{{ t('product.exportList.noExportsDescription') }}</p>
            <IluriaButton color="primary" :hugeIcon="PlusSignIcon" @click="goToNewExport" class="mt-4">
              {{ t('product.exportList.createFirstExport') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Estado de carregamento -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('product.exportList.loading') }}</span>
          </div>
        </template>
      </IluriaDataTable>

    </div>

    <!-- Paginação -->
    <div class="pagination-container" v-if="totalPages > 0">
      <IluriaPagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="goToPage"
      />
    </div>

    <!-- Painel de Informações -->
    <div class="info-section">
      <div class="info-header">
        <HugeiconsIcon :icon="InformationCircleIcon" :size="20" class="text-blue-600" />
        <h3 class="info-title">{{ t('product.exportList.infoTitle') }}</h3>
      </div>
      <div class="info-grid">
        <div class="info-card">
          <div class="info-card-icon">
            <HugeiconsIcon :icon="Time04Icon" :size="18" class="text-amber-600" />
          </div>
          <div class="info-card-content">
            <h4 class="info-card-title">{{ t('product.exportList.retentionTitle') }}</h4>
            <p class="info-card-text">{{ t('product.exportList.retentionDescription') }}</p>
          </div>
        </div>

        <div class="info-card">
          <div class="info-card-icon">
            <HugeiconsIcon :icon="Loading03Icon" :size="18" class="text-blue-600" />
          </div>
          <div class="info-card-content">
            <h4 class="info-card-title">{{ t('product.exportList.processingTitle') }}</h4>
            <p class="info-card-text">{{ t('product.exportList.processingDescription') }}</p>
          </div>
        </div>

        <div class="info-card">
          <div class="info-card-icon">
            <HugeiconsIcon :icon="PackageIcon" :size="18" class="text-green-600" />
          </div>
          <div class="info-card-content">
            <h4 class="info-card-title">{{ t('product.exportList.formatsTitle') }}</h4>
            <p class="info-card-text">{{ t('product.exportList.formatsDescription') }}</p>
          </div>
        </div>
      </div>
    </div>

    <!-- Delete Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import ProductExportService from '@/services/productExport.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import Column from 'primevue/column'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  PlusSignIcon,
  Download01Icon,
  Delete01Icon,
  File01Icon,
  Xls01Icon,
  Time04Icon,
  Loading03Icon,
  InformationCircleIcon,
  PackageIcon,
  ArrowLeft01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

// State
const exports = ref([])
const loading = ref(false)
const totalRecords = ref(0)
const currentPage = ref(0)
const pageSize = ref(10)
const refreshInterval = ref(null)

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('')
const confirmModalCancelText = ref('')
const confirmModalType = ref('error')
const confirmCallback = ref(null)

// Custom buttons for header
const headerCustomButtons = ref([
  {
    text: t('product.exportList.backToProducts'),
    icon: ArrowLeft01Icon,
    color: 'dark',
    variant: 'solid',
    action: 'back-to-products',
    class: 'back-to-products-btn'
  }
])

// Handle custom button clicks
const handleCustomButtonClick = (index, button) => {
  if (button.action === 'back-to-products') {
    goToProductsList()
  }
}

// Computed
const totalPages = computed(() => {
  return Math.ceil(totalRecords.value / pageSize.value)
})

// Methods
const loadExports = async (page = 0) => {
  loading.value = true
  try {
    const response = await ProductExportService.getExports(page, 10)
    exports.value = response.content
    totalRecords.value = response.totalElements
    currentPage.value = page
  } catch (error) {
    toast.showError(t('product.exportList.loadError'))
  } finally {
    loading.value = false
  }
}

const goToPage = (page) => {
  if (page !== currentPage.value) {
    loadExports(page)
  }
}

const goToNewExport = () => {
  router.push('/products/export')
}

const goToProductsList = () => {
  router.push('/products')
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const formatFileSize = (bytes) => {
  return ProductExportService.formatFileSize(bytes)
}

const getStatusText = (status) => {
  return ProductExportService.getStatusText(status)
}

const getStatusClasses = (status) => {
  const classMap = {
    'PENDING': 'bg-yellow-100 text-yellow-800',
    'IN_PROGRESS': 'bg-blue-100 text-blue-800',
    'COMPLETED': 'bg-green-100 text-green-800',
    'FAILED': 'bg-red-100 text-red-800'
  }
  return classMap[status] || 'bg-gray-100 text-gray-800'
}

const generateFileName = (exportJob) => {
  const timestamp = new Date(exportJob.createdAt).toISOString().replace(/[:.]/g, '-')
  return `products_${timestamp}.${exportJob.fileType.toLowerCase()}`
}

const canDownload = (exportJob) => {
  return ProductExportService.canDownload(exportJob)
}

const canDelete = (exportJob) => {
  return ProductExportService.canDelete(exportJob)
}

const downloadExport = async (exportJob) => {
  try {
    const fileName = exportJob.fileName || generateFileName(exportJob)
    await ProductExportService.downloadExportFile(exportJob.id, fileName)

    toast.showSuccess(t('product.exportList.downloadStarted'))
  } catch (error) {
    toast.showError(t('product.exportList.downloadError'))
  }
}

const confirmDelete = (exportJob) => {
  confirmModalMessage.value = t('product.exportList.deleteMessage')
  confirmModalTitle.value = t('product.exportList.deleteTitle')
  confirmModalConfirmText.value = t('product.exportList.deleteConfirm')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = () => deleteExport(exportJob.id)
  showConfirmModal.value = true
}

// Modal control functions
const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const deleteExport = async (exportId) => {
  try {
    await ProductExportService.deleteExport(exportId)
    toast.showSuccess(t('product.exportList.deleteSuccess'))
    // Reload the list
    await loadExports(currentPage.value)
  } catch (error) {
    toast.showError(t('product.exportList.deleteError'))
  }
}

// Auto-refresh for in-progress exports
const startAutoRefresh = () => {
  refreshInterval.value = setInterval(() => {
    const hasInProgress = exports.value.some(e => e.status === 'IN_PROGRESS')
    if (hasInProgress) {
      loadExports(currentPage.value)
    }
  }, 5000) // Refresh every 5 seconds
}

const stopAutoRefresh = () => {
  if (refreshInterval.value) {
    clearInterval(refreshInterval.value)
    refreshInterval.value = null
  }
}

// Lifecycle
onMounted(() => {
  loadExports()
  startAutoRefresh()
})

onUnmounted(() => {
  stopAutoRefresh()
})
</script>

<style scoped>
.export-list-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Table Styles */
.table-wrapper {
  background: var(--iluria-color-container-bg);
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
  overflow: hidden;
}

.table-header {
  background: var(--iluria-color-table-header-bg);
  border-bottom: 1px solid var(--iluria-color-border);
}

.table-header-cell {
  padding: 12px 24px;
  text-align: left;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.table-body {
  background: var(--iluria-color-container-bg);
}

.table-row {
  border-bottom: 1px solid var(--iluria-color-border);
}

.table-cell {
  padding: 16px 24px;
  white-space: nowrap;
  color: var(--iluria-color-text-primary);
}

.bg-white {
  background: var(--iluria-color-container-bg) !important;
}

th, td {
  white-space: nowrap;
}

@media (max-width: 1024px) {
  .export-list-container {
    padding: 12px;
  }
  .table-container {
    padding: 8px;
  }
  .page-header {
    flex-direction: column;
    gap: 16px;
    padding-bottom: 12px;
  }
  }
  
@media (max-width: 768px) {
  .export-list-container {
    padding: 4px;
  }
  .table-container {
    padding: 0;
    border-radius: 0;
    box-shadow: none;
  }
  .page-header {
    flex-direction: column;
    gap: 8px;
    padding-bottom: 8px;
  }
  table {
    font-size: 13px;
  }
  th, td {
    padding-left: 8px !important;
    padding-right: 8px !important;
  }
}

/* Ajuste para responsividade horizontal */
.table-container .overflow-x-auto {
  overflow-x: auto;
}

/* Ajuste para hover e zebra */
tbody tr:hover {
  background: var(--iluria-color-hover) !important;
}

/* Ajuste para botões de ação */
.flex.items-center.justify-end.gap-2 > * {
  min-width: 32px;
}

/* Ajuste para badge de status */
.inline-flex.px-2.py-1.text-xs.font-semibold.rounded-full {
  min-width: 80px;
  justify-content: center;
}

/* Info panel styles removidos - ViewContainer já aplica */
.info-list {
  margin: 0;
  padding-left: 20px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}
.info-list li {
  margin-bottom: 6px;
}

/* Info Content Styles */
.info-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.info-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px 16px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  transition: all 0.2s ease;
}

.info-item:hover {
  background: var(--iluria-color-surface-tertiary);
  border-color: var(--iluria-color-border-hover);
}

.info-item-icon {
  width: 20px;
  height: 20px;
  color: var(--iluria-color-primary);
  flex-shrink: 0;
}

.info-item-text {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  line-height: 1.4;
}

/* Info Section Styles */
.info-section {
  margin-top: 24px;
  padding: 24px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
}

.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 20px;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.info-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.info-card {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  padding: 16px;
  background: var(--iluria-color-surface-primary);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.info-card-icon {
  flex-shrink: 0;
  width: 36px;
  height: 36px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
}

.info-card-content {
  flex: 1;
  min-width: 0;
}

.info-card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
}

.info-card-text {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
}

.empty-title {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
  text-align: center;
}

.empty-description {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 16px 0;
  transition: color 0.3s ease;
  text-align: center;
  line-height: 1.5;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  transition: background-color 0.3s ease;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination Container */
.pagination-container {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}



/* Column Styles */
.exports-table :deep(.col-large) {
  width: 30%;
}

.exports-table :deep(.col-date) {
  width: 20%;
}

.exports-table :deep(.col-small) {
  width: 10%;
}

.exports-table :deep(.col-actions) {
  width: 20%;
  text-align: left;
}

/* Estilo específico para o botão "Voltar para Listagem de Produtos" */
.back-to-products-btn {
  background: var(--iluria-color-button-dark-bg) !important;
  color: var(--iluria-color-button-dark-fg) !important;
  border-color: var(--iluria-color-button-dark-bg) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.3) !important;
  transition: all 0.2s ease !important;
}

.back-to-products-btn:hover {
  background: var(--iluria-color-button-dark-bg-hover) !important;
  border-color: var(--iluria-color-button-dark-bg-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.4) !important;
}

/* Estilo específico para tema Dracula */
.theme-dracula .back-to-products-btn {
  background: #6272a4 !important;
  color: #f8f8f2 !important;
  border-color: #6272a4 !important;
  box-shadow: 0 2px 6px rgba(98, 114, 164, 0.3) !important;
}

.theme-dracula .back-to-products-btn:hover {
  background: #bd93f9 !important;
  border-color: #bd93f9 !important;
  color: #282a36 !important;
  box-shadow: 0 4px 12px rgba(189, 147, 249, 0.4) !important;
}

/* Estilos para outros temas escuros */
.theme-dark .back-to-products-btn,
.theme-nord .back-to-products-btn {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.3) !important;
}

.theme-dark .back-to-products-btn:hover,
.theme-nord .back-to-products-btn:hover {
  background: var(--iluria-color-primary) !important;
  border-color: var(--iluria-color-primary) !important;
  color: var(--iluria-color-primary-contrast) !important;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.4) !important;
}
</style>