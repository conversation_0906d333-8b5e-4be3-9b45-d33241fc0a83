/**
 * Sistema de Configurações de Componentes
 * 
 * Este arquivo carrega todas as configurações de componentes
 * e as organiza por tipo, categoria e prioridade.
 */

// Importações diretas dos configs (mais confiável que glob)
import videoConfig from './video.config.js'
import carouselConfig from './carousel.config.js'
import headerConfig from './header.config.js'
import productGridConfig from './product-grid.config.js'
import customerReviewConfig from './customer-review.config.js'
import specialOffersConfig from './special-offers.config.js'
import footerConfig from './footer.config.js'
import companyInformationConfig from './company-information.config.js'
import locationConfig from './location.config.js'
import paymentBenefitsConfig from './payment-benefits.config.js'
import statementConfig from './statement.config.js'
import productCheckoutConfig from './product-checkout.config.js'



// Array com todas as configurações
const allConfigsList = [
  videoConfig,
  carouselConfig,
  headerConfig,
  productGridConfig,
  customerReviewConfig,
  specialOffersConfig,
  footerConfig,
  companyInformationConfig,
  locationConfig,
  paymentBenefitsConfig,
  statementConfig,
  productCheckoutConfig
]

// Objeto para armazenar as configurações organizadas
const configurations = {
  byType: {},
  byCategory: {},
  byPriority: [],
  all: []
}



// Processamento e organização das configurações
allConfigsList.forEach((config, index) => {

  if (!config || !config.name || !config.type) {

    return
  }

  // Adicionar ao array geral
  configurations.all.push(config)
  
  // Organizar por tipo
  configurations.byType[config.type] = config
  
  // Organizar por categoria
  const category = config.category || 'other'
  if (!configurations.byCategory[category]) {
    configurations.byCategory[category] = []
  }
  configurations.byCategory[category].push(config)
  
  // Adicionar à lista de prioridades
  configurations.byPriority.push({
    priority: config.priority || 50,
    config
  })
  

})

// Ordenar por prioridade (maior prioridade primeiro)
configurations.byPriority.sort((a, b) => b.priority - a.priority)



// Se não há configurações, mostrar erro detalhado
if (configurations.all.length === 0) {

}

// Teste imediato do sistema


/**
 * Buscar configuração por tipo
 * @param {string} type - Tipo do componente
 * @returns {Object|null} Configuração do componente
 */
export function getConfigByType(type) {
  const config = configurations.byType[type] || null

  return config
}

/**
 * Buscar configurações por categoria
 * @param {string} category - Categoria dos componentes
 * @returns {Array} Array de configurações
 */
export function getConfigsByCategory(category) {
  const configs = configurations.byCategory[category] || []
  return configs
}

/**
 * ✅ NOVO: Buscar configurações filtradas por tipo de layout
 * @param {string} layoutType - Tipo do layout (static, product, collection, system)
 * @returns {Array} Array de configurações compatíveis com o layout
 */
export function getConfigsByLayoutType(layoutType) {
  if (!layoutType) return configurations.all
  
  return configurations.all.filter(config => {
    // Se o componente não especifica layoutTypes, está disponível para todos
    if (!config.layoutTypes || !Array.isArray(config.layoutTypes)) {
      return true
    }
    
    // Se especifica layoutTypes, verificar se o layout atual está incluído
    return config.layoutTypes.includes(layoutType)
  })
}

/**
 * Buscar configuração por elemento
 * @param {HTMLElement} element - Elemento do DOM
 * @returns {Object|null} Configuração do componente
 */
export function getConfigByElement(element) {
  if (!element) return null
  
  // Buscar por data-component primeiro
  const dataComponent = element.getAttribute('data-component')
  if (dataComponent) {
    const config = configurations.byType[dataComponent]
    if (config) {
      return config
    }
  }
  
  // Buscar por seletores em ordem de prioridade
  for (const { config } of configurations.byPriority) {
    if (config.selectors) {
      for (const selector of config.selectors) {
        try {
          if (element.matches(selector)) {
            return config
          }
        } catch (e) {
          // Seletor inválido, ignorar
          continue
        }
      }
    }
  }
  
  return null
}

/**
 * Obter todas as configurações ordenadas por prioridade
 * @returns {Array} Array de configurações ordenadas
 */
export function getAllConfigs() {

  return configurations.byPriority.map(item => item.config)
}

/**
 * Obter todas as configurações por categoria
 * @returns {Object} Objeto com configurações organizadas por categoria
 */
export function getConfigsByCategories() {

  return configurations.byCategory
}

/**
 * Obter estatísticas das configurações carregadas
 * @returns {Object} Estatísticas
 */
export function getConfigStats() {
  return {
    total: configurations.all.length,
    categories: Object.keys(configurations.byCategory).length,
    types: Object.keys(configurations.byType),
    categoriesData: Object.entries(configurations.byCategory).map(([category, configs]) => ({
      name: category,
      count: configs.length,
      components: configs.map(c => c.name)
    }))
  }
}



// Exportação padrão com todas as configurações
export default {
  getConfigByType,
  getConfigsByCategory,
  getConfigByElement,
  getAllConfigs,
  getConfigsByCategories,
  getConfigStats,
  getConfigsByLayoutType,
  configurations
} 