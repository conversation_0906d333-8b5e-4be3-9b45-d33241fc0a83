<template>
  <Teleport to="body">
    <div
      v-if="isVisible"
      class="confirmation-overlay"
      @click.self="onCancel"
    >
      <div class="confirmation-modal" :class="themeClasses" @click.stop>
        <!-- Close Button -->
        <button @click="onCancel" class="close-button" type="button" aria-label="Fechar">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
          </svg>
        </button>
        
        <div class="confirmation-icon" :class="iconConfig.containerClass">
          <HugeiconsIcon :icon="iconConfig.component" :size="40" />
        </div>
        
        <h3>{{ displayTitle }}</h3>
        <p>{{ displayMessage }}</p>
        
        <div v-if="$slots['extra-content']" class="extra-content">
          <slot name="extra-content"></slot>
        </div>
        
        <div class="confirmation-buttons">
          <IluriaButton
            @click="onCancel" 
            variant="outline"
            color="secondary"
            class="min-w-[100px]"
          >
            {{ displayCancelText }}
          </IluriaButton>
          <IluriaButton
            @click="onConfirm" 
            :color="buttonConfig.color"
            class="min-w-[100px]"
          >
            {{ displayConfirmText }}
          </IluriaButton>
        </div>
      </div>
    </div>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTheme } from '@/composables/useTheme'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Alert01Icon,
  CancelCircleIcon,
  InformationCircleIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const { currentThemeId } = useTheme()

const props = defineProps({
  isVisible: {
    type: Boolean,
    default: false
  },
  title: {
    type: String,
    default: ''
  },
  message: {
    type: String,
    default: ''
  },
  confirmText: {
    type: String,
    default: ''
  },
  cancelText: {
    type: String,
    default: ''
  },
  isDestructive: {
    type: Boolean,
    default: true
  },
  type: {
    type: String,
    default: 'error', // 'error', 'warning', 'info'
    validator: (value) => ['error', 'warning', 'info'].includes(value)
  }
})

const emit = defineEmits(['confirm', 'cancel'])

// Componentes de ícones usando HugeIcons

// Configurações por tipo
const typeConfigs = {
  error: {
    icon: CancelCircleIcon,
    containerClass: 'icon-error',
    buttonColor: 'danger'
  },
  warning: {
    icon: Alert01Icon,
    containerClass: 'icon-warning',
    buttonColor: 'warning'
  },
  info: {
    icon: InformationCircleIcon,
    containerClass: 'icon-info',
    buttonColor: 'primary'
  }
}

// Determinar tipo baseado em isDestructive para compatibilidade
const modalType = computed(() => {
  if (props.type !== 'error') {
    return props.type
  }
  return props.isDestructive ? 'error' : 'info'
})

const iconConfig = computed(() => ({
  component: typeConfigs[modalType.value].icon,
  containerClass: typeConfigs[modalType.value].containerClass
}))

const buttonConfig = computed(() => ({
  color: typeConfigs[modalType.value].buttonColor
}))

// Classes de tema baseadas no tema atual
const themeClasses = computed(() => ({
  [`theme-${currentThemeId.value}`]: true
}))

// Computed properties for default translations
const displayTitle = computed(() => 
  props.title || t('layoutEditor.confirmDelete.title')
)

const displayMessage = computed(() => 
  props.message || t('layoutEditor.confirmDelete.message')
)

const displayConfirmText = computed(() => 
  props.confirmText || t('layoutEditor.confirmDelete.confirm')
)

const displayCancelText = computed(() => 
  props.cancelText || t('layoutEditor.confirmDelete.cancel')
)

const onConfirm = () => {
  emit('confirm')
}

const onCancel = () => {
  emit('cancel')
}
</script>

<style scoped>
.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(4px);
}

.confirmation-modal {
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  position: relative;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: var(--iluria-shadow-lg);
  animation: modalSlideIn 0.3s ease-out;
}

/* Estilos universais usando variáveis CSS do sistema de temas */
.confirmation-modal h3 {
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.75rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.confirmation-modal p {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 2rem 0;
  line-height: 1.5;
}

.close-button {
  position: absolute;
  top: 1rem;
  right: 1rem;
  background: none;
  border: none;
  padding: 0.5rem;
  cursor: pointer;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-muted);
}

.close-button:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-secondary);
}

.close-button:focus {
  outline: 2px solid var(--iluria-color-primary);
  outline-offset: 2px;
}

.confirmation-icon {
  width: 64px;
  height: 64px;
  margin: 0 auto 1rem auto;
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-error {
  color: var(--iluria-color-error);
  background: var(--iluria-color-surface);
  border: 2px solid var(--iluria-color-border);
}

.icon-warning {
  color: var(--iluria-color-warning);
  background: var(--iluria-color-surface);
  border: 2px solid var(--iluria-color-border);
}

.icon-info {
  color: var(--iluria-color-info);
  background: var(--iluria-color-surface);
  border: 2px solid var(--iluria-color-border);
}

/* Icon size is now controlled by the HugeiconsIcon component size prop */

.extra-content {
  margin: -1rem 0 2rem 0;
  padding: 1rem;
  border-radius: 8px;
  text-align: left;
  font-size: 0.875rem;
  max-height: 150px;
  overflow-y: auto;
  background: var(--iluria-color-background);
  color: var(--iluria-color-text-secondary);
  border: 1px solid var(--iluria-color-border);
}

.confirmation-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@media (max-width: 768px) {
  .confirmation-modal {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}
</style>
