<template>
    <div class="questions-container">
      <!-- Header -->
      <IluriaHeader
        :title="$t('questions.title')"
        :subtitle="$t('questions.subtitle')"
        :showAdd="true"
        addText="Gerenciar Respostas Predefinidas"
        @add-click="goToPredefinedAnswers"
      />

      <!-- KPIs -->
      <div class="kpi-grid">
        <div v-for="kpi in kpis" :key="kpi.name" class="kpi-card">
          <div class="kpi-header">
            <p class="kpi-label">{{ kpi.name }}</p>
            <span class="kpi-icon" :class="kpi.iconBgColor">
               <div v-html="kpi.icon" class="kpi-icon-svg" :class="kpi.iconColor"></div>
            </span>
          </div>
          <p class="kpi-value">{{ kpi.value }}</p>
          <p class="kpi-change">
            <span :class="kpi.changeType === 'increase' ? 'kpi-change-positive' : 'kpi-change-negative'">
              {{ kpi.change }}
            </span>
            nas últimas 24h
          </p>
        </div>
      </div>

      <!-- Filters and Actions -->
      <div class="filters-container">
        <div class="filters-grid">
          <IluriaCategoryDropdown 
            :categories="categories"
            v-model="selectedCategory"
            :placeholder="$t('questions.filters.allCategories')"
            :show-all-option="true"
            :all-option-text="$t('questions.filters.allCategories')"
            :all-option-value="null"
          />
          <IluriaInputText 
            v-model="searchProduct"
            :placeholder="$t('questions.filters.searchProduct')" 
          />
          <IluriaSelect 
            :placeholder="$t('questions.status.all')" 
            :options="statusOptions"
            optionLabel="label"
            optionValue="value"
            v-model="selectedStatus"
          />
          <IluriaInputText 
            v-model="selectedDate"
            type="date" 
            :placeholder="$t('questions.filters.date')" 
          />
          <IluriaInputText 
            v-model="searchQuestion"
            :placeholder="$t('questions.filters.searchQuestion')" 
          />
        </div>
      </div>

      <!-- Questions Table -->
      <div class="table-wrapper">
        <IluriaDataTable :value="filteredQuestions" :columns="columns" 
          selectionMode="multiple" v-model:selection="selectedQuestions"
          class="product-table iluria-data-table">

          <template #column-product="{ data }">
            <div class="product-cell">
              <img 
                :src="data.product.imageUrl || generateProductAvatar(data.product.name)" 
                :alt="data.product.name" 
                class="product-image"
                @error="(event) => handleImageError(event, data.product.name, 'product')"
              >
              <div class="product-info">
                <div class="product-name">{{ data.product.name }}</div>
                <div class="product-category">{{ data.product.categoryName }}</div>
              </div>
            </div>
          </template>

          <template #column-question="{ data }">
            <div class="question-cell">
              <p class="question-text" :title="data.questionText">
                {{ data.questionText.length > 10 ? data.questionText.substring(0, 10) + '...' : data.questionText }}
              </p>
            </div>
          </template>

          <template #column-customer="{ data }">
            <div class="customer-name">{{ data.customer.name }}</div>
          </template>

          <template #column-date="{ data }">
            <div class="question-date">{{ formatDate(data.createdAt) }}</div>
          </template>

          <template #column-status="{ data }">
            <div class="status-cell">
                <span class="status-badge" :class="getStatusConfig(data).badgeClass">
                    <div v-html="getStatusConfig(data).icon" class="status-icon" :class="getStatusConfig(data).iconClass"></div>
                </span>
            </div>
          </template>

          <template #column-actions="{ data }">
            <div class="action-buttons">
              <!-- Botão Responder/Editar -->
              <IluriaButton
                @click="goToResponsePage(data.id)"
                :title="data.status.toLowerCase() === 'answered' ? 'Editar resposta' : 'Responder'"
                size="small"
                :color="data.status.toLowerCase() === 'answered' ? 'primary' : 'success'"
                :hugeIcon="data.status.toLowerCase() === 'answered' ? PencilEdit01Icon : MailReply01Icon"
              >
                {{ data.status.toLowerCase() === 'answered' ? 'Editar' : 'Responder' }}
              </IluriaButton>
              
              <!-- Botão Destacar (só para perguntas respondidas) -->
              <IluriaButton
                v-if="data.status.toLowerCase() === 'answered'"
                @click="confirmToggleHighlight(data)"
                :title="data.isHighlighted ? 'Remover destaque' : 'Destacar'"
                size="small"
                :color="data.isHighlighted ? 'warning' : 'text-secondary'"
                :hugeIcon="StarIcon"
              >
                {{ data.isHighlighted ? 'Destacada' : 'Destacar' }}
              </IluriaButton>
              
              <!-- Botão Ocultar/Mostrar -->
              <IluriaButton
                @click="confirmHideQuestion(data)"
                :title="data.status.toLowerCase() === 'hidden' ? 'Mostrar pergunta' : 'Ocultar pergunta'"
                size="small"
                color="text-secondary"
                :hugeIcon="data.status.toLowerCase() === 'hidden' ? ViewIcon : ViewOffIcon"
              >
                {{ data.status.toLowerCase() === 'hidden' ? 'Mostrar' : 'Ocultar' }}
              </IluriaButton>
              
              <!-- Botão Excluir -->
              <IluriaButton
                @click="confirmDeleteQuestion(data)"
                title="Excluir pergunta"
                size="small"
                color="danger"
                :hugeIcon="Delete01Icon"
              >
                Excluir
              </IluriaButton>
            </div>
          </template>

        </IluriaDataTable>
      </div>

      <!-- Pagination -->
      <div class="pagination-container" v-if="totalPages >= 1">
        <IluriaPagination 
          :current-page="currentPage"
          :total-pages="totalPages"
          @go-to-page="changePage"
        />
      </div>
    </div>

    <!-- Modal de Confirmação de Exclusão -->
    <IluriaConfirmationModal
      :is-visible="showDeleteModal"
      :title="`Excluir Pergunta`"
      :message="`Tem certeza que deseja excluir esta pergunta? Esta ação não pode ser desfeita.`"
      confirm-text="Excluir"
      cancel-text="Cancelar"
      type="error"
      @confirm="deleteQuestion"
      @cancel="showDeleteModal = false"
    />

    <!-- Modal de Confirmação de Ocultar/Mostrar -->
    <IluriaConfirmationModal
      :is-visible="showHideModal"
      :title="selectedQuestionForAction?.status?.toLowerCase() === 'hidden' ? 'Mostrar Pergunta' : 'Ocultar Pergunta'"
      :message="selectedQuestionForAction?.status?.toLowerCase() === 'hidden'
        ? 'Tem certeza que deseja mostrar esta pergunta novamente?'
        : 'Tem certeza que deseja ocultar esta pergunta?'"
      :confirm-text="selectedQuestionForAction?.status?.toLowerCase() === 'hidden' ? 'Mostrar' : 'Ocultar'"
      cancel-text="Cancelar"
      type="info"
      @confirm="toggleQuestionVisibility"
      @cancel="showHideModal = false"
    />

    <!-- Modal de Confirmação de Destacar/Remover Destaque -->
    <IluriaConfirmationModal
      :is-visible="showHighlightModal"
      :title="selectedQuestionForAction?.isHighlighted ? 'Remover Destaque' : 'Destacar Pergunta'"
      :message="selectedQuestionForAction?.isHighlighted
        ? 'Tem certeza que deseja remover o destaque desta pergunta?'
        : 'Tem certeza que deseja destacar esta pergunta? Perguntas destacadas aparecem em posição de destaque para os clientes.'"
      :confirm-text="selectedQuestionForAction?.isHighlighted ? 'Remover Destaque' : 'Destacar'"
      cancel-text="Cancelar"
      type="warning"
      @confirm="toggleHighlight"
      @cancel="showHighlightModal = false"
    />
</template>

<script setup>
import { ref, watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { useRouter } from 'vue-router';
import IluriaCategoryDropdown from '@/components/iluria/CategoryDropdown.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import IluriaPagination from '@/components/iluria/IluriaPagination.vue';
import { PENDING, ANSWERED, HIDDEN } from './questionStatus';
import questionsApi from '@/services/question.service'; 
import { useToast } from '@/services/toast.service';
import { generateProductAvatar, handleImageError } from '@/utils/avatarUtils';
import { categoryService } from '@/services/category.service';
import { useTheme } from '@/composables/useTheme';
import { 
  DocumentAttachmentIcon,
  FilterIcon,
  BubbleChatIcon,
  PencilEdit01Icon,
  MailReply01Icon,
  StarIcon,
  ViewIcon,
  ViewOffIcon,
  Delete01Icon
} from '@hugeicons-pro/core-stroke-rounded';

const router = useRouter();
const { t } = useI18n();

// Initialize theme system
const { initTheme } = useTheme();
initTheme();

const categories = ref([]);
const selectedCategory = ref(null);
const selectedStatus = ref('all');
const selectedSort = ref('newest');
const selectedQuestions = ref([]);
const searchProduct = ref('');
const searchQuestion = ref('');
const selectedDate = ref('');

const eyeSlashIcon = `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M3.98 8.223A10.477 10.477 0 001.934 12C3.226 16.338 7.244 19.5 12 19.5c.993 0 1.953-.138 2.863-.395M6.228 6.228A10.45 10.45 0 0112 4.5c4.756 0 8.774 3.162 10.065 7.498a10.523 10.523 0 01-4.293 5.774M6.228 6.228L3 3m3.228 3.228l3.65 3.65m7.894 7.894L21 21m-3.228-3.228l-3.65-3.65m0 0a3 3 0 10-4.243-4.243m4.243 4.243l-4.243-4.243" /></svg>`;

const statusConfig = {
    pending: { 
        icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>`, 
        badgeClass: 'bg-orange-100',
        iconClass: 'text-orange-600'
    },
    answered: { 
        icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>`, 
        badgeClass: 'bg-green-100',
        iconClass: 'text-green-600'
    },
    hidden: { 
        icon: eyeSlashIcon,
        badgeClass: 'bg-gray-100',
        iconClass: 'text-gray-500'
     },
    highlighted: { 
        icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>`, 
        badgeClass: 'bg-yellow-100',
        iconClass: 'text-yellow-600'
    }
};

const statusOptions = ref([
  { label: t('questions.status.all'), value: 'all' },
  { label: t('questions.status.pending'), value: 'pending' },
  { label: t('questions.status.answered'), value: 'answered' },
  { label: t('questions.status.highlighted'), value: 'highlighted' },
  { label: t('questions.status.hidden'), value: 'hidden' }
]);

const sortOptions = ref([
  { label: t('questions.sort.newest'), value: 'newest' },
  { label: t('questions.sort.oldest'), value: 'oldest' }
]);

const kpis = ref([
  {
    name: t('questions.kpis.pending'),
    value: '0',
    change: '+0',
    changeType: 'increase',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><circle cx="12" cy="12" r="10"></circle><polyline points="12 6 12 12 16 14"></polyline></svg>`,
    iconBgColor: 'bg-orange-100',
    iconColor: 'text-orange-600'
  },
  {
    name: t('questions.kpis.answered'),
    value: '0',
    change: '+0',
    changeType: 'increase',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polyline points="20 6 9 17 4 12"></polyline></svg>`,
    iconBgColor: 'bg-green-100',
    iconColor: 'text-green-600'
  },
  {
    name: t('questions.kpis.highlighted'),
    value: '0',
    change: '+0',
    changeType: 'increase',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><polygon points="12 2 15.09 8.26 22 9.27 17 14.14 18.18 21.02 12 17.77 5.82 21.02 7 14.14 2 9.27 8.91 8.26 12 2"></polygon></svg>`,
    iconBgColor: 'bg-yellow-100',
    iconColor: 'text-yellow-600'
  },
  {
    name: t('questions.kpis.hidden'),
    value: '0',
    change: '+0',
    changeType: 'increase',
    icon: eyeSlashIcon,
    iconBgColor: 'bg-gray-100',
    iconColor: 'text-gray-500'
  },
  {
    name: t('questions.kpis.total'),
    value: '0',
    change: '+0',
    changeType: 'increase',
    icon: `<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><path d="M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2"></path><circle cx="9" cy="7" r="4"></circle><path d="M22 21v-2a4 4 0 0 0-3-3.87"></path><path d="M16 3.13a4 4 0 0 1 0 7.75"></path></svg>`,
    iconBgColor: 'bg-blue-100',
    iconColor: 'text-blue-600'
  }
]);
const questions = ref([]);
const loading = ref(false);
const currentPage = ref(0);
const totalPages = ref(1);
const itemsPerPage = ref(10);
const pagination = ref({
  page: 0,
  size: 10,
  totalElements: 0,
  totalPages: 0,
});
const filters = ref({
  searchTerm: '',
});

// Modal states
const showDeleteModal = ref(false);
const showHideModal = ref(false);
const showHighlightModal = ref(false);
const selectedQuestionForAction = ref(null);

// All questions from backend (before frontend filtering)
const allQuestions = ref([]);

// Computed for filtered and paginated questions
const filteredQuestions = computed(() => {
  let filtered = allQuestions.value;
  
  // Apply frontend filters
  // 1. Status filter
  if (selectedStatus.value && selectedStatus.value !== 'all') {
    if (selectedStatus.value === 'highlighted') {
      filtered = filtered.filter(question => 
        question.status.toLowerCase() === 'answered' && question.isHighlighted === true
      );
    } else {
      filtered = filtered.filter(question => 
        question.status.toLowerCase() === selectedStatus.value.toLowerCase()
      );
    }
  } else {
    // For "all", exclude hidden questions
    filtered = filtered.filter(question => 
      question.status.toLowerCase() !== 'hidden'
    );
  }

  // 2. Category filter
  if (selectedCategory.value && selectedCategory.value !== null) {
    filtered = filtered.filter(question => 
      question.product.category === selectedCategory.value
    );
  }

  // 3. Product filter
  if (searchProduct.value && searchProduct.value.trim()) {
    const searchTerm = searchProduct.value.toLowerCase().trim();
    filtered = filtered.filter(question => 
      question.product.name.toLowerCase().includes(searchTerm) ||
      question.product.sku.toLowerCase().includes(searchTerm)
    );
  }

  // 4. Question text filter
  if (searchQuestion.value && searchQuestion.value.trim()) {
    const searchTerm = searchQuestion.value.toLowerCase().trim();
    filtered = filtered.filter(question => 
      question.questionText.toLowerCase().includes(searchTerm)
    );
  }

  // 5. Date filter
  if (selectedDate.value) {
    const filterDate = new Date(selectedDate.value);
    const startOfDay = new Date(filterDate.getFullYear(), filterDate.getMonth(), filterDate.getDate());
    const endOfDay = new Date(filterDate.getFullYear(), filterDate.getMonth(), filterDate.getDate(), 23, 59, 59);
    
    filtered = filtered.filter(question => {
      const questionDate = new Date(question.createdAt);
      return questionDate >= startOfDay && questionDate <= endOfDay;
    });
  }

  // Apply sorting
  filtered.sort((a, b) => {
    const dateA = new Date(a.createdAt);
    const dateB = new Date(b.createdAt);
    
    if (selectedSort.value === 'oldest') {
      return dateA - dateB;
    } else {
      return dateB - dateA;
    }
  });

  // Calculate total pages based on filtered results
  totalPages.value = Math.ceil(filtered.length / itemsPerPage.value);
  
  // Ensure at least 1 page when we have data structure
  if (totalPages.value === 0 && allQuestions.value !== null) {
    totalPages.value = 1;
  }
  
  // Return paginated results
  const start = currentPage.value * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filtered.slice(start, end);
});

const columns = ref([
  { field: 'selection', header: '' },
  { field: 'product', header: t('questions.table.product') },
  { field: 'question', header: t('questions.table.question') },
  { field: 'customer', header: t('questions.table.customer') },
  { field: 'date', header: t('questions.table.date') },
  { field: 'status', header: t('questions.table.status') },
  { field: 'actions', header: t('questions.table.actions') },
]);

const fetchKpis = async () => {
  try {
    const { data } = await questionsApi.getKpis();
    
    // Função para formatar mudança com sinal correto
    const formatChange = (value) => {
      const num = value || 0;
      return num >= 0 ? `+${num}` : `${num}`;
    };
    
    // Buscar TODAS as perguntas highlighted para contagem correta
    let highlightedCount = 0;
    try {
      const highlightedResponse = await questionsApi.findQuestions({
        status: 'ANSWERED',
        highlighted: true,
        size: 1000 // Buscar mais para ter certeza de pegar todas
      });
      highlightedCount = highlightedResponse.data.totalElements || 0;
    } catch (highlightedError) {
      console.warn('Erro ao buscar destacadas, usando contagem local:', highlightedError);
      // Fallback: contar das perguntas locais se a API falhar
      highlightedCount = questions.value.filter(q => 
        q.status.toLowerCase() === 'answered' && q.isHighlighted === true
      ).length;
    }
    
    // Atualizar os valores dos KPIs com os dados da API
    // 0 - Aguardando Resposta
    kpis.value[0].value = data.pendingCount?.toString() || '0';
    kpis.value[0].change = formatChange(data.pendingCount24h);
    kpis.value[0].changeType = (data.pendingCount24h || 0) >= 0 ? 'increase' : 'decrease';
    
    // 1 - Respondidas
    kpis.value[1].value = data.answeredCount?.toString() || '0';
    kpis.value[1].change = formatChange(data.answeredCount24h);
    kpis.value[1].changeType = (data.answeredCount24h || 0) >= 0 ? 'increase' : 'decrease';
    
    // 2 - Destacadas (calculado via API específica)
    kpis.value[2].value = highlightedCount.toString();
    kpis.value[2].change = formatChange(data.highlightedCount24h);
    kpis.value[2].changeType = (data.highlightedCount24h || 0) >= 0 ? 'increase' : 'decrease';
    
    // 3 - Ocultas
    kpis.value[3].value = data.hiddenCount?.toString() || '0';
    kpis.value[3].change = formatChange(data.hiddenCount24h);
    kpis.value[3].changeType = (data.hiddenCount24h || 0) >= 0 ? 'increase' : 'decrease';
    
    // 4 - Total
    kpis.value[4].value = data.totalCount?.toString() || '0';
    kpis.value[4].change = formatChange(data.totalCount24h);
    kpis.value[4].changeType = (data.totalCount24h || 0) >= 0 ? 'increase' : 'decrease';
  } catch (error) {
    console.error('Erro ao carregar KPIs:', error);
    // Manter valores padrão se a API falhar
  }
};

const fetchQuestions = async () => {
  loading.value = true;
  try {
    let allQuestionsData = [];
    let page = 0;
    let totalPages = 1;
    const pageSize = 100; // Reasonable page size
    
    // Fetch all pages
    do {
      const params = {
        page: page,
        size: pageSize,
        sort: 'createdAt,desc',
      };

      const { data } = await questionsApi.findQuestions(params);
      
      if (data.content) {
        allQuestionsData = [...allQuestionsData, ...data.content];
      }
      
      totalPages = data.totalPages || 1;
      page++;
      
    } while (page < totalPages);
    
    // Store all questions for frontend filtering
    allQuestions.value = allQuestionsData;
    
    // Reset pagination when data changes
    currentPage.value = 0;
    
  } catch (error) {
    console.error('Erro ao carregar perguntas:', error);
    allQuestions.value = [];
    // Don't reset totalPages to 0, keep it at 1 to show pagination
    currentPage.value = 0;
  } finally {
    loading.value = false;
  }
};

const changePage = (page) => {
  currentPage.value = page;
};

// Reset to first page when filters change
const resetPage = () => {
  currentPage.value = 0;
};

const viewQuestion = (questionId) => {
  router.push({ name: 'QuestionResponse', params: { id: questionId } });
};

const goToResponsePage = (questionId) => {
  router.push({ name: 'question-reply', params: { id: questionId } });
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Funções dos botões
const confirmDeleteQuestion = (question) => {
  selectedQuestionForAction.value = question;
  showDeleteModal.value = true;
};

const deleteQuestion = async () => {
  if (!selectedQuestionForAction.value) return;
  
  try {
    await questionsApi.delete(selectedQuestionForAction.value.id);
    showDeleteModal.value = false;
    selectedQuestionForAction.value = null;
    await fetchQuestions();
    fetchKpis();
  } catch (error) {
    console.error('Erro ao excluir pergunta:', error);
  }
};

const confirmHideQuestion = (question) => {
  selectedQuestionForAction.value = question;
  showHideModal.value = true;
};

const toggleQuestionVisibility = async () => {
  if (!selectedQuestionForAction.value) return;
  
  const question = selectedQuestionForAction.value;
  let newStatus;
  
  if (question.status.toLowerCase() === 'hidden') {
    // Se está oculta, voltar ao status original
    // Se tem respostas, voltar para ANSWERED, senão PENDING
    newStatus = (question.answers && question.answers.length > 0) ? 'ANSWERED' : 'PENDING';
  } else {
    // Se não está oculta, ocultar
    newStatus = 'HIDDEN';
  }
  
  try {
    // Preservar o isHighlighted quando des-ocultar
    await questionsApi.updateStatus(question.id, newStatus, question.isHighlighted);
    const message = newStatus === 'HIDDEN' 
      ? t('questions.messages.questionHidden') 
      : t('questions.messages.questionShown');
    showHideModal.value = false;
    selectedQuestionForAction.value = null;
    await fetchQuestions();
    fetchKpis();
  } catch (error) {
    console.error('Erro ao atualizar status:', error);
  }
};

const confirmToggleHighlight = (question) => {
  selectedQuestionForAction.value = question;
  showHighlightModal.value = true;
};

const toggleHighlight = async () => {
  if (!selectedQuestionForAction.value) return;
  
  const question = selectedQuestionForAction.value;
  try {
    const newHighlightStatus = !question.isHighlighted;
    await questionsApi.updateStatus(question.id, question.status, newHighlightStatus);
    showHighlightModal.value = false;
    selectedQuestionForAction.value = null;
    await fetchQuestions();
    fetchKpis();
  } catch (error) {
    console.error('Erro ao atualizar destaque:', error);
  }
};

const getStatusConfig = (question) => {
  // Se a pergunta está destacada, mostrar o ícone de estrela
  if (question.isHighlighted) {
    return statusConfig.highlighted;
  }
  
  // Caso contrário, mostrar o status normal
  return statusConfig[question.status.toLowerCase()];
};

const goToPredefinedAnswers = () => {
  router.push({ name: 'predefined-answers' });
};

const fetchCategories = async () => {
  try {
    const data = await categoryService.fetchCategories();
    categories.value = data || [];
  } catch (error) {
    console.error('Erro ao carregar categorias:', error);
    categories.value = [];
  }
};

onMounted(async () => {
  await fetchCategories();
  await fetchQuestions();
  fetchKpis();
});

// Watch for filter changes and reset page
watch(selectedSort, () => {
  resetPage();
});

watch(selectedStatus, () => {
  resetPage();
});

watch(selectedCategory, () => {
  resetPage();
});

watch(searchProduct, () => {
  resetPage();
});

watch(searchQuestion, () => {
  resetPage();
});

watch(selectedDate, () => {
  resetPage();
});

// Watch for data changes to update KPIs
watch(allQuestions, () => {
  fetchKpis();
}, { deep: true });
</script>

<style scoped>
.questions-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* KPI Grid */
.kpi-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 32px;
}

.kpi-card {
  padding: 20px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-sm);
  transition: all 0.3s ease;
}

.kpi-card:hover {
  box-shadow: var(--iluria-shadow-md);
  transform: translateY(-2px);
}

.kpi-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 12px;
}

.kpi-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin: 0;
}

.kpi-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 8px;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.kpi-icon.bg-orange-100 {
  background: rgba(251, 146, 60, 0.1);
}

.kpi-icon.bg-green-100 {
  background: rgba(34, 197, 94, 0.1);
}

.kpi-icon.bg-yellow-100 {
  background: rgba(250, 204, 21, 0.1);
}

.kpi-icon.bg-gray-100 {
  background: rgba(107, 114, 128, 0.1);
}

.kpi-icon.bg-blue-100 {
  background: rgba(59, 130, 246, 0.1);
}

.kpi-icon-svg {
  width: 20px;
  height: 20px;
}

.kpi-icon-svg.text-orange-600 {
  color: #ea580c;
}

.kpi-icon-svg.text-green-600 {
  color: #16a34a;
}

.kpi-icon-svg.text-yellow-600 {
  color: #ca8a04;
}

.kpi-icon-svg.text-gray-500 {
  color: #6b7280;
}

.kpi-icon-svg.text-blue-600 {
  color: #2563eb;
}

.kpi-value {
  font-size: 24px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.kpi-change {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.kpi-change-positive {
  color: #16a34a;
  font-weight: 500;
}

.kpi-change-negative {
  color: #dc2626;
  font-weight: 500;
}

/* Filters */
.filters-container {
  background: var(--iluria-color-surface);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
  margin-bottom: 24px;
}

.filters-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

/* Table */
.table-wrapper {
  margin-bottom: 24px;
}

:deep(.product-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  transition: all 0.3s ease;
}

:deep(.product-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px 24px;
  transition: all 0.3s ease;
}

:deep(.product-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
  transition: background-color 0.2s ease;
}

:deep(.product-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.product-table .p-datatable-tbody > tr:hover > td) {
  background: var(--iluria-color-hover) !important;
}

:deep(.product-table .p-datatable-tbody > tr > td) {
  padding: 16px 24px;
  border: none;
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  transition: all 0.3s ease;
}

.sort-select {
  min-width: 180px;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* Product Cell */
.product-cell {
  display: flex;
  align-items: center;
  gap: 12px;
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 6px;
  object-fit: cover;
  border: 1px solid var(--iluria-color-border);
}

.product-info {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.product-category {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Question Cell */
.question-cell {
  max-width: 200px;
}

.question-text {
  color: var(--iluria-color-text);
  font-size: 14px;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  margin: 0;
}

/* Customer */
.customer-name {
  color: var(--iluria-color-text);
  font-size: 14px;
  font-weight: 500;
}

/* Date */
.question-date {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
}

/* Status */
.status-cell {
  display: flex;
  justify-content: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  transition: all 0.3s ease;
}

.status-badge.bg-orange-100 {
  background: rgba(251, 146, 60, 0.1);
}

.status-badge.bg-green-100 {
  background: rgba(34, 197, 94, 0.1);
}

.status-badge.bg-gray-100 {
  background: rgba(107, 114, 128, 0.1);
}

.status-badge.bg-yellow-100 {
  background: rgba(250, 204, 21, 0.1);
}

.status-icon {
  width: 16px;
  height: 16px;
}

.status-icon.text-orange-600 {
  color: #ea580c;
}

.status-icon.text-green-600 {
  color: #16a34a;
}

.status-icon.text-gray-500 {
  color: #6b7280;
}

.status-icon.text-yellow-600 {
  color: #ca8a04;
}

/* Actions */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  flex-wrap: wrap;
}

/* Responsive */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
    gap: 16px;
  }
  
  .filters-grid {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .questions-container {
    padding: 16px;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 15px;
  }
  
  .kpi-grid {
    grid-template-columns: repeat(auto-fit, minmax(160px, 1fr));
    gap: 12px;
  }
  
  .kpi-card {
    padding: 16px;
  }
  
  .kpi-value {
    font-size: 20px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .product-cell {
    gap: 8px;
  }
  
  .product-image {
    width: 32px;
    height: 32px;
  }
  
  .product-name {
    font-size: 13px;
  }
  
  .product-category {
    font-size: 11px;
  }
}

@media (max-width: 480px) {
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
  }
  
  .kpi-grid {
    grid-template-columns: 1fr;
  }
  
  .action-buttons {
    gap: 2px;
  }
}
</style> 
