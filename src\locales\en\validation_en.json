{"required": "This field is required", "fieldRequired": "{field} is required", "maxLength": "{field} must be at most {length} characters", "invalidNumber": "{field} must be a valid number", "nonNegative": "{field} must be greater than or equal to 0", "lessThan": "{field} must be less than {value}", "greaterThan": "{field} must be greater than {value}", "invalidEmail": "{field} must be a valid email address", "invalidUrl": "{field} must be a valid URL", "idRequired": "The ID field is required", "fullAddressRequired": "The Full Address field is required", "zipCodeRequired": "The Postal Code is required", "zipCodeInvalid": "The Postal Code is invalid", "phoneRequired": "The Mobile field is required", "phoneInvalid": "The Mobile number is invalid", "emailRequired": "The Email field is required", "emailInvalid": "The Email is invalid", "nameRequired": "The Name field is required", "urlRequired": "The E-commerce URL is required", "urlInvalid": "The E-commerce URL is invalid", "urlIluriaRequired": "The Custom URL is required", "urlIluriaInvalid": "The Custom URL is invalid", "invalidAddress": "Invalid address", "invalidTaxId": "Invalid Tax ID", "emailInvalidFormat": "<PERSON><PERSON><PERSON>", "nameUnavailable": "Name not available", "emailUnavailable": "Email not available", "customerNotFound": "Customer not found", "errorFetchingCustomer": "Error fetching customer", "errorFetchingEmail": "Error fetching email", "fixedValueRequired": "The fixed value is required when the option is activated", "percentageRequired": "The percentage is required when the option is activated", "isRequired": "is Required", "minGreaterThanMax": "The minimum quantity must be less than or equal to the maximum quantity", "passwordsDontMatch": "Passwords don't match", "invalidDate": "Invalid date", "dateMustBeFuture": "Date must be in the future", "mustBePositive": "Must be a positive value", "maxPercentage": "Maximum value is 100%"}