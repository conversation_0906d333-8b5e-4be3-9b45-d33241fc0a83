/**
 * Configuração do Componente de Benefícios de Pagamento - REFATORADO
 * Design system unificado, interface modernizada e editor simplificado
 */

export default {
  type: 'payment-benefits',
  name: 'Benefícios de Pagamento',
  description: 'Componente moderno para exibir vantagens e benefícios relacionados a pagamento e entrega',
  category: 'ecommerce',
  priority: 70,
  icon: 'credit-card',
  
  html: `<div 
    data-component="payment-benefits" 
    data-element-type="paymentBenefits"
    class="iluria-payment-benefits">
    
    <div class="benefits-container">
      <header class="benefits-header">
        <h2 class="benefits-title">Por que escolher nossa loja?</h2>
        <p class="benefits-subtitle">Benefícios exclusivos para você</p>
      </header>
      
      <div class="benefits-grid" data-columns="3" data-text-align="center">
        <article class="benefit-card" data-benefit-id="free-shipping">
          <div class="benefit-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="1" y="3" width="15" height="13"/>
              <path d="M16 8h4l3 3v5h-7V8zM16 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM7 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
            </svg>
          </div>
          <div class="benefit-content">
            <h3 class="benefit-title">Frete Grátis</h3>
            <p class="benefit-description">Entrega gratuita para compras acima de R$ 99 em todo o Brasil</p>
          </div>
        </article>
        
        <article class="benefit-card benefit-highlighted" data-benefit-id="secure-payment">
          <div class="benefit-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
              <path d="M9 12l2 2 4-4"/>
            </svg>
          </div>
          <div class="benefit-content">
            <h3 class="benefit-title">Pagamento 100% Seguro</h3>
            <p class="benefit-description">Suas informações protegidas com criptografia SSL de última geração</p>
          </div>
          <div class="benefit-badge">Popular</div>
        </article>
        
        <article class="benefit-card" data-benefit-id="money-back">
          <div class="benefit-icon">
            <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
              <path d="M3 3v5h5M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/>
              <path d="M16 16h5v5"/>
            </svg>
          </div>
          <div class="benefit-content">
            <h3 class="benefit-title">Garantia de Reembolso</h3>
            <p class="benefit-description">30 dias para devolução com reembolso total sem complicações</p>
          </div>
        </article>
      </div>
    </div>
    
    <style>
      .iluria-payment-benefits {
        --benefits-primary-color: #3b82f6;
        --benefits-secondary-color: #1e40af;
        --benefits-accent-color: #dbeafe;
        --benefits-text-color: #1f2937;
        
        padding: clamp(2rem, 5vw, 4rem) 1rem;
        background: #ffffff;
        border-radius: 16px;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
        position: relative;
        overflow: hidden;
      }
      
      .benefits-container {
        max-width: 1200px;
        margin: 0 auto;
        position: relative;
        z-index: 2;
      }
      
      .benefits-header {
        text-align: center;
        margin-bottom: clamp(2rem, 4vw, 3rem);
      }
      
      .benefits-title {
        font-size: clamp(1.75rem, 4vw, 2.5rem);
        font-weight: 700;
        color: var(--benefits-text-color);
        margin: 0 0 0.5rem 0;
        line-height: 1.2;
        letter-spacing: -0.025em;
      }
      
      .benefits-subtitle {
        font-size: clamp(1rem, 2.5vw, 1.25rem);
        color: #6b7280;
        margin: 0;
        font-weight: 400;
        line-height: 1.5;
      }
      
      .benefits-grid {
        display: grid;
        gap: clamp(1rem, 3vw, 2rem);
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      }
      
      .benefits-grid[data-columns="1"] {
        grid-template-columns: 1fr;
        max-width: 600px;
        margin: 0 auto;
      }
      
      .benefits-grid[data-columns="2"] {
        grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      }
      
      .benefits-grid[data-columns="3"] {
        grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
      }
      
      .benefits-grid[data-columns="4"] {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
      }
      
      .benefit-card {
        background: #ffffff;
        border: 2px solid #f1f5f9;
        border-radius: 16px;
        padding: clamp(1.5rem, 3vw, 2rem);
        transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
        position: relative;
        cursor: pointer;
        overflow: hidden;
        text-align: center;
      }
      
      .benefit-card::before {
        content: '';
        position: absolute;
        top: 0;
        left: 0;
        right: 0;
        height: 4px;
        background: linear-gradient(90deg, var(--benefits-primary-color), var(--benefits-secondary-color));
        transform: scaleX(0);
        transition: transform 0.4s ease;
      }
      
      .benefit-card:hover::before {
        transform: scaleX(1);
      }
      
      .benefit-card:hover {
        border-color: var(--benefits-accent-color);
        box-shadow: 0 20px 40px -12px rgba(59, 130, 246, 0.15), 0 8px 16px -4px rgba(59, 130, 246, 0.1);
        transform: translateY(-8px);
      }
      
      .benefit-highlighted {
        background: linear-gradient(135deg, var(--benefits-accent-color) 0%, #ffffff 100%);
        border-color: var(--benefits-primary-color);
        transform: scale(1.02);
      }
      
      .benefit-highlighted::before {
        transform: scaleX(1);
      }
      
      .benefit-badge {
        position: absolute;
        top: 1rem;
        right: 1rem;
        background: var(--benefits-primary-color);
        color: white;
        padding: 0.25rem 0.75rem;
        border-radius: 20px;
        font-size: 0.75rem;
        font-weight: 600;
        text-transform: uppercase;
        letter-spacing: 0.5px;
      }
      
      .benefit-icon {
        width: 64px;
        height: 64px;
        border-radius: 16px;
        display: flex;
        align-items: center;
        justify-content: center;
        margin: 0 auto 1.5rem auto;
        color: white;
        background: var(--benefits-primary-color);
        box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
        transition: all 0.4s ease;
      }
      
      .benefit-title {
        font-size: 1.25rem;
        font-weight: 600;
        color: var(--benefits-text-color);
        margin: 0 0 0.75rem 0;
        line-height: 1.3;
        letter-spacing: -0.01em;
      }
      
      .benefit-description {
        font-size: 0.95rem;
        color: #6b7280;
        line-height: 1.6;
        margin: 0;
      }
      
      @media (max-width: 768px) {
        .iluria-payment-benefits {
          padding: 2rem 1rem;
        }
        
        .benefits-grid {
          grid-template-columns: 1fr !important;
          gap: 1.5rem;
        }
        
        .benefit-card {
          padding: 1.5rem;
        }
        
        .benefit-icon {
          width: 56px;
          height: 56px;
          margin-bottom: 1rem;
        }
        
        .benefit-title {
          font-size: 1.125rem;
        }
        
        .benefit-description {
          font-size: 0.875rem;
        }
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'payment-benefits-editor',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M11 4H4a2 2 0 0 0-2 2v14c0 1.1.9 2 2 2h14a2 2 0 0 0 2-2v-7"/>
        <path d="M18.5 2.5a2.12 2.12 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
      </svg>`,
      title: 'Editar Benefícios',
      description: 'Configurar conteúdo e aparência dos benefícios',
      condition: (element) => {
        return element?.getAttribute('data-component') === 'payment-benefits'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'payment-benefits-config',
      component: 'PaymentBenefitsSidebarEditor',
      path: '@/components/layoutEditor/Sidebar/editors/PaymentBenefitsSidebarEditor.vue'
    }
  ],
  
  computedCheckers: {
    isPaymentBenefitsComponent: (element) => {
      return element?.getAttribute('data-component') === 'payment-benefits'
    },
    
    hasValidBenefits: (element) => {
      const benefits = element?.getAttribute('data-benefits')
      try {
        const parsed = JSON.parse(benefits || '[]')
        return Array.isArray(parsed) && parsed.length > 0
      } catch {
        return false
      }
    },
    
    isModernLayout: (element) => {
      const cardStyle = element?.getAttribute('data-card-style')
      return ['modern', 'minimal', 'outlined'].includes(cardStyle)
    }
  },
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true,
    copyable: true,
    exportable: true
  },
  
  // Configurações padrão para novos componentes
  defaults: {
    title: "Por que escolher nossa loja?",
    subtitle: "Benefícios exclusivos para você",
    layout: "grid",
    columns: 3,
    showIcons: true,
    iconStyle: "filled",
    showSubtitle: true,
    textAlign: "center",
    cardStyle: "modern",
    colorScheme: "blue",
    spacing: "comfortable",
    animation: "fade-up"
  },
  
  // Validações
  validation: {
    required: ['title', 'benefits'],
    benefits: {
      minLength: 1,
      maxLength: 8,
      requiredFields: ['title', 'description', 'icon']
    }
  },
  
  // Metadata para analytics e SEO
  metadata: {
    version: '2.0.0',
    lastUpdated: new Date().toISOString(),
    author: 'Iluria Team',
    tags: ['ecommerce', 'benefits', 'payment', 'trust'],
    analytics: {
      trackViews: true,
      trackInteractions: true
    }
  }
} 