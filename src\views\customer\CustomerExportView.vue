<template>
  <div class="customer-export-container">
    <!-- Header com ações -->
    <IluriaHeader
      :title="t('customer.export.title')"
      :subtitle="t('customer.export.subtitle')"
      :customButtons="headerCustomButtons"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="t('customer.export.startExport')"
      :saveIcon="Download01Icon"
      @custom-click="handleCustomButtonClick"
      @cancel-click="goBack"
      @save-click="openConfirm"
    />

    <!-- Export Form -->
    <div class="export-form-container space-y-6">
      <!-- Seção de Dados Básicos -->
      <ViewContainer
        :title="t('customer.export.basicData')"
        :subtitle="t('customer.export.basicDataDescription')"
        :icon="File01Icon"
        icon-color="blue"
      >
        <!-- Formato do Arquivo -->
        <div>
          <label class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-4">{{ t('customer.export.fileFormat') }}</label>
          <div class="format-options">
            <button
              @click="selectedFormat = 'CSV'"
              class="format-option"
              :class="{ 'active': selectedFormat === 'CSV' }"
            >
              <div class="format-option-icon">
                <HugeiconsIcon :icon="File01Icon" class="w-6 h-6" />
              </div>
              <div class="format-option-content">
                <span class="format-name">CSV</span>
                <span class="format-description">{{ t('customer.export.csvDescription') }}</span>
              </div>
              <div class="format-check" v-if="selectedFormat === 'CSV'">
                <HugeiconsIcon :icon="CheckmarkCircle02Icon" class="w-5 h-5" />
              </div>
            </button>

            <button
              @click="selectedFormat = 'XLSX'"
              class="format-option"
              :class="{ 'active': selectedFormat === 'XLSX' }"
            >
              <div class="format-option-icon">
                <HugeiconsIcon :icon="Xls01Icon" class="w-6 h-6" />
              </div>
              <div class="format-option-content">
                <span class="format-name">Excel (XLSX)</span>
                <span class="format-description">{{ t('customer.export.xlsxDescription') }}</span>
              </div>
              <div class="format-check" v-if="selectedFormat === 'XLSX'">
                <HugeiconsIcon :icon="CheckmarkCircle02Icon" class="w-5 h-5" />
              </div>
            </button>
          </div>
        </div>
      </ViewContainer>

      <!-- Seção de Seleção de Campos -->
      <ViewContainer
        :title="t('customer.export.fieldSelection')"
        :subtitle="t('customer.export.fieldSelectionDescription')"
        :icon="CheckmarkSquare03Icon"
        icon-color="purple"
      >
        <div class="space-y-6">
          <!-- Selecionar todos -->
          <div class="mb-6">
            <IluriaCheckbox
              v-model="selectAllFields"
              @change="toggleAllFields"
              :label="t('customer.export.selectAllFields')"
              class="font-medium text-base"
            />
            <p class="text-sm text-[var(--iluria-color-text-secondary)] mt-2 ml-6">
              {{ t('customer.export.selectAllDescription') }}
            </p>
          </div>

          <!-- Grid de Campos Simples -->
          <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-x-8 gap-y-4">
            <!-- Campos Básicos -->
            <div v-for="field in availableFields.basic" :key="field">
              <IluriaCheckbox
                :id="`field-${field}`"
                v-model="fieldSelections[field]"
                :label="getFieldDisplayName(field)"
                @change="updateSelectedFields"
                class="text-sm"
              />
            </div>

            <!-- Campos de Endereço -->
            <div v-for="field in availableFields.addresses" :key="field">
              <IluriaCheckbox
                :id="`field-${field}`"
                v-model="fieldSelections[field]"
                :label="getFieldDisplayName(field)"
                @change="updateSelectedFields"
                class="text-sm"
              />
            </div>
          </div>

          <!-- Contador de Campos Selecionados -->
          <div class="mt-6 pt-4 border-t border-[var(--iluria-color-border)]">
            <div class="flex items-center justify-between text-sm">
              <span class="text-[var(--iluria-color-text-secondary)]">
                {{ t('customer.export.selectedFieldsCount') }}:
              </span>
              <span class="font-medium text-[var(--iluria-color-text-primary)]">
                {{ selectedFields.length }} {{ t('customer.export.fields') }}
              </span>
            </div>
          </div>
        </div>
      </ViewContainer>

      <!-- Resumo da Exportação -->
      <ViewContainer
        :title="t('customer.export.summary')"
        :subtitle="t('customer.export.summaryDescription')"
        :icon="CheckmarkCircle02Icon"
        icon-color="green"
      >
        <div class="space-y-4">
          <div class="flex justify-between items-center py-3 border-b border-[var(--iluria-color-border)]">
            <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">{{ t('customer.export.format') }}:</span>
            <span class="text-sm text-[var(--iluria-color-text-primary)] font-semibold bg-[var(--iluria-color-surface-secondary)] px-3 py-1 rounded-full">{{ selectedFormat }}</span>
          </div>
          <div class="flex justify-between items-center py-3 border-b border-[var(--iluria-color-border)]">
            <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">{{ t('customer.export.totalCustomers') }}:</span>
            <span class="text-sm font-semibold bg-green-100 text-green-800 px-3 py-1 rounded-full">
              <span v-if="loadingCustomerCount" class="animate-pulse">...</span>
              <span v-else>{{ totalCustomers.toLocaleString() }} {{ t('customer.export.customers') }}</span>
            </span>
          </div>
          <div class="flex justify-between items-center py-3">
            <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">{{ t('customer.export.selectedFieldsCount') }}:</span>
            <span class="text-sm font-semibold bg-blue-100 text-blue-800 px-3 py-1 rounded-full">{{ selectedFields.length }} {{ t('customer.export.fields') }}</span>
          </div>
        </div>
      </ViewContainer>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import CustomerExportService from '@/services/customerExport.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaCheckbox from '@/components/iluria/form/IluriaCheckbox.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  LeftToRightListBulletIcon,
  Cancel01Icon,
  Download01Icon,
  CheckmarkSquare03Icon,
  CheckmarkCircle02Icon,
  File01Icon,
  Xls01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

// Form state
const selectedFormat = ref('CSV')
const selectedFields = ref([])
const selectAllFields = ref(false)
const availableFields = ref({ basic: [], addresses: [] })
const fieldSelections = ref({})
const exporting = ref(false)

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const totalCustomers = ref(0)
const loadingCustomerCount = ref(false)

// Custom buttons for header
const headerCustomButtons = ref([
  {
    text: t('customer.export.viewExports'),
    icon: LeftToRightListBulletIcon,
    color: 'secondary',
    variant: 'outline',
    action: 'view-exports'
  }
])

// Handle custom button clicks
const handleCustomButtonClick = (index, button) => {
  if (button.action === 'view-exports') {
    goToExportsList()
  }
}

// Computed
const canExport = computed(() => {
  return selectedFields.value.length > 0 && selectedFormat.value
})

// Methods
const loadCustomerCount = async () => {
  try {
    loadingCustomerCount.value = true
    const count = await CustomerExportService.getCustomerCount()
    totalCustomers.value = count
  } catch (error) {
    console.error('Error loading customer count:', error)
    totalCustomers.value = 0
  } finally {
    loadingCustomerCount.value = false
  }
}

const loadAvailableFields = async () => {
  try {
    const fields = await CustomerExportService.getAvailableFields()

    // Ensure we have the correct structure
    if (fields && fields.basic && fields.addresses) {
      availableFields.value = fields

      // Initialize field selections
      const allFields = [...fields.basic, ...fields.addresses]
      fieldSelections.value = {}
      allFields.forEach(field => {
        fieldSelections.value[field] = false
      })

      // Select some basic fields by default
      if (fields.basic.includes('name')) fieldSelections.value['name'] = true
      if (fields.basic.includes('email')) fieldSelections.value['email'] = true
      if (fields.basic.includes('creationDate')) fieldSelections.value['creationDate'] = true

      updateSelectedFields()
      selectAllFields.value = false
    } else {
      throw new Error('Invalid fields structure received from API')
    }
  } catch (error) {
    console.error('Error loading available fields:', error)
    toast.showError(t('customer.export.exportError'))
  }
}

const updateSelectedFields = () => {
  selectedFields.value = Object.keys(fieldSelections.value).filter(field => fieldSelections.value[field])
}

const toggleAllFields = () => {
  if (!availableFields.value.basic || !availableFields.value.addresses) {
    return
  }

  const allFields = [...availableFields.value.basic, ...availableFields.value.addresses]
  allFields.forEach(field => {
    fieldSelections.value[field] = selectAllFields.value
  })
  updateSelectedFields()
}

const getFieldDisplayName = (fieldName) => {
  return CustomerExportService.getFieldDisplayName(fieldName)
}

const startExport = async () => {
  if (!selectedFields.value.length) {
    toast.showError(t('customer.export.noFieldsSelected'))
    return
  }

  try {
    exporting.value = true

    await CustomerExportService.createExport(
      selectedFormat.value,
      selectedFields.value
    )

    toast.showSuccess(
      t('customer.export.exportStartedMessage'),
      {
        title: t('customer.export.exportStarted'),
        duration: 5000
      }
    )

    // Navegar para lista de exportações
    router.push('/customers/exports')

  } catch (error) {
    console.error('Error starting export:', error)
    toast.showError(t('customer.export.exportError'))
  } finally {
    exporting.value = false
  }
}

const goBack = () => {
  router.push('/customer-list')
}

const goToExportsList = () => {
  router.push('/customers/exports')
}

// Modal control functions
const showConfirm = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('confirm')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'info'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const openConfirm = () => {
  showConfirm(
    t('customer.export.confirmMessage'),
    t('customer.export.confirmTitle'),
    startExport
  )
}

// Lifecycle
onMounted(() => {
  loadAvailableFields()
  loadCustomerCount()
})
</script>

<style scoped>
.customer-export-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Main Content - ViewContainer já aplica o espaçamento necessário */

.option-selected {
  background: var(--iluria-color-option-selected-bg, #e0e7ff) !important;
  border-color: var(--iluria-color-option-selected-border, #6366f1) !important;
  color: var(--iluria-color-option-selected-text, #3730a3) !important;
  box-shadow: 0 0 0 2px var(--iluria-color-option-selected-border, #6366f1);
}
.option-unselected {
  background: transparent;
  border-color: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
}
.option-selected h3,
.option-selected p {
  color: var(--iluria-color-option-selected-text, #3730a3) !important;
}
.option-unselected h3,
.option-unselected p {
  color: var(--iluria-color-text-primary);
}

.info-panel {
  display: flex;
  align-items: flex-start;
  background: var(--iluria-color-info-bg, #e0f2fe);
  border: 1px solid var(--iluria-color-info-border, #bae6fd);
  border-radius: 12px;
  padding: 20px;
  margin-top: 32px;
  gap: 16px;
}
.info-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--iluria-color-info-icon-bg, #bae6fd);
  border-radius: 8px;
}
.info-icon-svg {
  color: var(--iluria-color-info-icon, #0284c7);
}
.info-content {
  flex: 1;
}
.info-title {
  font-size: 16px;
  font-weight: 600;
  margin: 0 0 8px 0;
  color: var(--iluria-color-info-title, #0369a1);
}
.info-list {
  margin: 0;
  padding-left: 20px;
  color: var(--iluria-color-info-text, #0c4a6e);
  font-size: 14px;
}
.info-list li {
  margin-bottom: 6px;
}

/* Format Options - Seguindo padrão do ThemeSelector */
.format-options {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.format-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  position: relative;
  outline: none;
}

.format-option:hover:not(.active) {
  background: rgba(156, 163, 175, 0.15);
  transform: translateX(2px);
}

.format-option.active {
  background: rgba(156, 163, 175, 0.25);
  color: var(--iluria-color-text-primary);
  transform: scale(1.02);
  box-shadow: 0 0 0 1px rgba(156, 163, 175, 0.3);
}

.format-option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(156, 163, 175, 0.1);
  flex-shrink: 0;
  transition: all 0.2s ease;
  overflow: hidden;
  color: var(--iluria-color-text-secondary);
}

.format-option:hover .format-option-icon {
  transform: scale(1.15) rotate(5deg);
}

.format-option.active .format-option-icon {
  background: rgba(156, 163, 175, 0.2);
  transform: scale(1.1);
}

.format-option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.format-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.format-description {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.3;
}

.format-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(156, 163, 175, 0.2);
  color: var(--iluria-color-primary);
  flex-shrink: 0;
}
</style>
