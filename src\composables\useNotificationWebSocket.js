import { ref, onUnmounted, watch } from 'vue'
import { Client } from '@stomp/stompjs'
import SockJS from 'sockjs-client'
import { useAuthStore } from '@/stores/auth.store'

// Polyfill for global if not defined
if (typeof global === 'undefined') {
  window.global = window
}

/**
 * Composable para gerenciar conexão WebSocket de notificações em tempo real.
 * Baseado no padrão do useStoreInitializationProgress.js
 */
export function useNotificationWebSocket() {
  const authStore = useAuthStore()
  
  const isConnected = ref(false)
  const newNotification = ref(null)
  const isConnecting = ref(false)
  const error = ref(null)
  const connectionAttempts = ref(0)
  const maxRetries = 5
  
  let stompClient = null
  let subscription = null

  const connect = () => {
    if (stompClient && stompClient.connected) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        isConnecting.value = true
        connectionAttempts.value++

        // Use modern STOMP client with SockJS - mesmo padrão do useStoreInitializationProgress
        stompClient = new Client({
          webSocketFactory: () => {
            return new SockJS('http://localhost:8081/ws/stomp')
          },
          connectHeaders: {},
          debug: (str) => {
            // Debug desabilitado em produção
            if (process.env.NODE_ENV === 'development') {
            }
          },
          reconnectDelay: 5000,
          heartbeatIncoming: 4000,
          heartbeatOutgoing: 4000,
          onConnect: (frame) => {
            isConnected.value = true
            isConnecting.value = false
            connectionAttempts.value = 0
            error.value = null
            resolve()
          },
          onStompError: (frame) => {
            console.error('❌ STOMP Error:', frame)
            isConnected.value = false
            isConnecting.value = false
            const errorMessage = frame.headers['message'] || 'STOMP connection error'
            error.value = errorMessage
            reject(new Error(errorMessage))
          },
          onWebSocketError: (wsError) => {
            console.error('❌ WebSocket Error:', wsError)
            isConnected.value = false
            isConnecting.value = false
            const errorMessage = `WebSocket error: ${wsError.message || wsError}`
            error.value = errorMessage
            reject(new Error(errorMessage))
          },
          onWebSocketClose: (event) => {
            console.warn('⚠️ WebSocket Closed:', event)
            isConnected.value = false
            isConnecting.value = false
          }
        })

        stompClient.activate()
      } catch (err) {
        isConnecting.value = false
        reject(err)
      }
    })
  }

  const subscribe = (userId) => {
    if (!stompClient || !stompClient.connected) {
      console.warn('⚠️ Cannot subscribe: WebSocket not connected')
      return
    }

    const destination = `/topic/notifications/${userId}`
    
    subscription = stompClient.subscribe(destination, (message) => {
      try {
        const data = JSON.parse(message.body)

        // Validar se os dados necessários existem
        if (!data || typeof data !== 'object') {
          console.warn('Invalid notification data received:', data)
          return
        }

        // Estrutura esperada da notificação (baseada no NotificationDTO)
        const notification = {
          id: data.id,
          type: data.notificationType || data.type, // Backend usa 'notificationType'
          title: data.title || 'New notification',
          content: data.content || '',
          actionUrl: data.actionUrl,
          isRead: data.isRead || false,
          createdAt: data.createdAt || new Date().toISOString(),
          metadata: data.metadata || {}
        }

        newNotification.value = notification

      } catch (err) {
        const errorMessage = typeof err === 'string' ? err : (err.message || 'Erro ao processar notificação')
        error.value = errorMessage
      }
    })
  }

  const validateAuthentication = () => {
    if (!authStore.userLoggedIn) {
      return { valid: false, error: 'User not logged in' }
    }

    const userInfo = authStore.getTokenInfo('user')
    if (!userInfo?.userId) {
      return { valid: false, error: 'User ID not found in token' }
    }

    return { valid: true, userId: userInfo.userId }
  }

  const startListening = async () => {
    try {
      const authValidation = validateAuthentication()
      if (!authValidation.valid) {
        console.warn('⚠️ Cannot start notifications WebSocket:', authValidation.error)
        return
      }

      const userId = authValidation.userId
      
      await connect()
      subscribe(userId)
            
    } catch (err) {
      console.error('❌ Erro ao conectar WebSocket de notificações:', err)
      const errorMessage = typeof err === 'string' ? err : (err.message || 'Erro desconhecido')
      error.value = `Erro ao conectar com o servidor de notificações: ${errorMessage}`

      // Não tentar reconectar se não estiver autenticado
      const authValidation = validateAuthentication()
      if (!authValidation.valid) {
        console.warn('⚠️ Stopping WebSocket reconnection: User not authenticated')
        return
      }

      // Tentar novamente após delay progressivo (máximo 5 tentativas)
      if (connectionAttempts.value < maxRetries) {
        const delay = Math.min(3000 * Math.pow(2, connectionAttempts.value - 1), 30000)
        setTimeout(() => {
          startListening()
        }, delay)
      } else {
        console.error('❌ Max WebSocket connection attempts reached for notifications')
      }
    }
  }

  const disconnect = () => {
    
    if (subscription) {
      subscription.unsubscribe()
      subscription = null
    }

    if (stompClient && stompClient.connected) {
      stompClient.deactivate()
    }

    isConnected.value = false
    isConnecting.value = false
    connectionAttempts.value = 0
  }

  const reset = () => {
    newNotification.value = null
    error.value = null
  }

  const markNotificationAsProcessed = () => {
    newNotification.value = null
  }

  // Watch para mudanças no estado de login
  watch(() => authStore.userLoggedIn, (isLoggedIn) => {
    if (isLoggedIn) {
      // Aguardar um pouco para o token estar pronto
      setTimeout(() => {
        startListening()
      }, 1000)
    } else {
      disconnect()
      reset()
    }
  })

  // Cleanup on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    // Estado da conexão
    isConnected,
    isConnecting,
    error,
    connectionAttempts,
    
    // Dados das notificações
    newNotification,
    
    // Métodos de controle
    startListening,
    disconnect,
    reset,
    markNotificationAsProcessed
  }
}