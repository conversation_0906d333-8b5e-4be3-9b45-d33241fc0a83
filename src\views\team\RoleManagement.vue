<template>
    <div class="role-management-container">
        <!-- Header Section -->
        <IluriaHeader
            title="Configuração de Cargos"
            subtitle="Gerencie cargos e suas permissões na loja"
            :showSearch="false"
            :showAdd="true"
            addText="Criar Cargo"
            :addIcon="UserAdd01Icon"
            :customButtons="headerCustomButtons"
            @add-click="openCreateRoleModal"
            @custom-click="handleCustomButtonClick"
        />

        <!-- Main Content -->
        <div class="table-wrapper">
            <IluriaDataTable
                :value="storeRoles"
                :columns="roleTableColumns"
                :loading="loading"
                dataKey="id"
                class="role-table iluria-data-table"
            >
                <!-- Column Headers -->
                <template #header-name>
                    <span class="column-header">Nome do Cargo</span>
                </template>
                <template #header-description>
                    <span class="column-header">Descrição</span>
                </template>
                <template #header-permissions>
                    <span class="column-header">Permissões</span>
                </template>
                <template #header-actions>
                    <span class="column-header">Ações</span>
                </template>

                <!-- Column Templates -->
                <template #column-name="{ data }">
                    <div class="role-name-cell">
                        <h3 class="role-name">{{ data.name }}</h3>
                        <span v-if="data.isSystemRole" class="system-badge">
                            <HugeiconsIcon :icon="Shield01Icon" size="12" />
                            Sistema
                        </span>
                    </div>
                </template>

                <template #column-description="{ data }">
                    <span class="role-description">{{ data.description || 'Sem descrição' }}</span>
                </template>

                <template #column-permissions="{ data }">
                    <div class="permissions-preview">
                        <span class="permission-count">{{ data.permissions?.length || 0 }} permissões</span>
                        <IluriaButton
                            color="text-primary"
                            size="small"
                            variant="ghost"
                            @click="viewPermissions(data)"
                        >
                            Ver detalhes
                        </IluriaButton>
                    </div>
                </template>

                <template #column-actions="{ data }">
                    <div v-if="!(data.systemRole || data.isSystemRole)" class="action-buttons">
                        <IluriaButton
                            color="text-primary"
                            size="small"
                            :hugeIcon="PencilEdit01Icon"
                            @click.prevent="editRole(data)"
                            title="Editar cargo"
                        />
                        <IluriaButton
                            color="text-danger"
                            size="small"
                            :hugeIcon="Delete01Icon"
                            @click.prevent="confirmDeleteRole(data)"
                            title="Excluir cargo"
                        />
                    </div>
                </template>
                
                <!-- Empty State -->
                <template #empty>
                    <div class="empty-state">
                        <div class="empty-icon">
                            <HugeiconsIcon :icon="UserGroup02Icon" size="48" :strokeWidth="1.5" />
                        </div>
                        <h3 class="empty-title">Nenhum cargo encontrado</h3>
                        <p class="empty-description">Comece criando cargos personalizados para sua equipe</p>
                        <IluriaButton @click="openCreateRoleModal" :hugeIcon="UserAdd01Icon" class="mt-4">
                            Criar Cargo
                        </IluriaButton>
                    </div>
                </template>

                <!-- Loading State -->
                <template #loading>
                    <div class="loading-state">
                        <div class="loading-spinner"></div>
                        <span>Carregando cargos...</span>
                    </div>
                </template>
            </IluriaDataTable>
        </div>

        <!-- Create/Edit Role Modal -->
        <IluriaModal
            v-model="showRoleModal"
            :title="isEditing ? 'Editar Cargo' : 'Criar Cargo'"
            :subtitle="isEditing ? 'Modifique as informações do cargo' : 'Configure um novo cargo para sua equipe'"
            :icon="UserAdd01Icon"
            saveLabel="Salvar"
            :dialogStyle="{ width: '90vw', maxWidth: '1200px' }"
            @save="saveRole"
            @cancel="closeRoleModal"
        >
            <div class="role-modal-content">
                <!-- Basic Info -->
                <div class="role-basic-info">
                    <div class="form-row">
                        <div class="form-field">
                            <label class="form-label" for="roleName">Nome do Cargo *</label>
                            <input
                                id="roleName"
                                v-model="currentRole.name"
                                type="text"
                                class="form-input"
                                placeholder="Ex: Designer, Marketeiro"
                                :disabled="saving"
                                maxlength="100"
                            />
                        </div>
                        <div class="form-field">
                            <label class="form-label" for="roleDescription">Descrição</label>
                            <input
                                id="roleDescription"
                                v-model="currentRole.description"
                                type="text"
                                class="form-input"
                                placeholder="Descrição do cargo (opcional)"
                                :disabled="saving"
                                maxlength="255"
                            />
                        </div>
                    </div>
                </div>

                <!-- Permissions Section -->
                <div class="permissions-section">
                    <h3 class="section-title">Permissões</h3>
                    <p class="section-description">Selecione as permissões que este cargo terá na loja</p>

                    <div class="permissions-grid">
                        <div 
                            v-for="category in permissionCategories" 
                            :key="category.name"
                            class="permission-category"
                        >
                            <div class="category-header">
                                <h4 class="category-title">{{ category.label }}</h4>
                                <IluriaButton
                                    size="small"
                                    variant="ghost"
                                    color="text-primary"
                                    @click="toggleCategory(category)"
                                >
                                    {{ isCategorySelected(category) ? 'Desmarcar todos' : 'Selecionar todos' }}
                                </IluriaButton>
                            </div>
                            
                            <div class="permissions-list">
                                <div 
                                    v-for="permission in category.permissions" 
                                    :key="permission.code"
                                    class="permission-item"
                                >
                                    <IluriaCheckBox
                                        :modelValue="isPermissionSelected(permission)"
                                        @update:modelValue="togglePermission(permission)"
                                        :disabled="saving"
                                    />
                                    <div class="permission-info">
                                        <span class="permission-name">{{ permission.description }}</span>
                                        <span class="permission-code">{{ permission.code }}</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </IluriaModal>

        <!-- View Permissions Modal -->
        <IluriaModal
            v-model="showPermissionsModal"
            :title="`Permissões: ${selectedRole?.name}`"
            subtitle="Visualizar todas as permissões deste cargo"
            :icon="Shield01Icon"
            :showSave="false"
            :showFooter="false"
            :dialogStyle="{ width: '80vw', maxWidth: '800px' }"
        >
            <div class="view-permissions-content">
                <div 
                    v-for="category in getSelectedPermissionsByCategory()" 
                    :key="category.name"
                    class="permission-category-view"
                >
                    <h4 class="category-title-view">{{ category.label }}</h4>
                    <div class="permissions-list-view">
                        <div 
                            v-for="permission in category.permissions" 
                            :key="permission.code"
                            class="permission-item-view"
                        >
                            <HugeiconsIcon :icon="CheckmarkCircle02Icon" size="16" class="check-icon" />
                            <span class="permission-description">{{ permission.description }}</span>
                        </div>
                    </div>
                </div>
                
                <div v-if="!selectedRole?.permissions?.length" class="no-permissions">
                    <p>Este cargo não possui permissões configuradas.</p>
                </div>
            </div>
        </IluriaModal>

        <!-- Confirmation Dialog -->
        <IluriaConfirmationModal 
            :isVisible="showConfirmDialog"
            :title="confirmData.title" 
            :message="confirmData.message"
            :type="confirmData.type || 'error'"
            @confirm="handleConfirm"
            @cancel="showConfirmDialog = false"
        />
    </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import {
    UserAdd01Icon,
    PencilEdit01Icon,
    Delete01Icon,
    UserGroup02Icon,
    Shield01Icon,
    CheckmarkCircle02Icon,
    ArrowLeft01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { HugeiconsIcon } from '@hugeicons/vue'
import { useToast } from '@/services/toast.service'
import { useRouter } from 'vue-router'
import { roleApi } from '@/services/role.service'
import { useStoreStore } from '@/stores/store.store'

const router = useRouter()
const toast = useToast()
const storeStore = useStoreStore()

// Confirmation dialog state
const showConfirmDialog = ref(false)
const confirmData = ref({ title: '', message: '', type: 'error', onConfirm: null })

// Reactive data
const storeRoles = ref([])
const loading = ref(true)
const saving = ref(false)

// Modal states
const showRoleModal = ref(false)
const showPermissionsModal = ref(false)
const isEditing = ref(false)
const selectedRole = ref(null)

// Current role being edited/created
const currentRole = ref({
    name: '',
    description: '',
    permissions: []
})

// Available permissions
const availablePermissions = ref([])

// Navigation
const goBack = () => {
    router.push('/team')
}

// Header custom buttons
const headerCustomButtons = ref([
    {
        text: 'Voltar',
        color: 'secondary',
        variant: 'outline',
        icon: ArrowLeft01Icon,
        onClick: goBack
    }
])

// Table columns configuration
const roleTableColumns = computed(() => [
    { field: 'name', headerClass: 'col-name', class: 'col-name' },
    { field: 'description', headerClass: 'col-description', class: 'col-description' },
    { field: 'permissions', headerClass: 'col-permissions', class: 'col-permissions' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
])

// Permission categories for organization
const permissionCategories = computed(() => {
    if (!availablePermissions.value || availablePermissions.value.length === 0) {
        return []
    }
    
    const categories = [
        {
            name: 'store',
            label: 'Gerenciamento da Loja',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('store.'))
        },
        {
            name: 'product',
            label: 'Produtos',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('product.'))
        },
        {
            name: 'order',
            label: 'Pedidos',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('order.'))
        },
        {
            name: 'customer',
            label: 'Clientes',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('customer.'))
        },
        {
            name: 'financial',
            label: 'Financeiro',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('financial.'))
        },
        {
            name: 'promotion',
            label: 'Promoções',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('promotion.'))
        },
        {
            name: 'layout',
            label: 'Layout e Design',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('layout.'))
        },
        {
            name: 'file',
            label: 'Arquivos',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('file.'))
        },
        {
            name: 'analytics',
            label: 'Analytics',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('analytics.') || p?.code?.startsWith('reports.'))
        },
        {
            name: 'team',
            label: 'Equipe',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('team.'))
        },
        {
            name: 'payment',
            label: 'Pagamento',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('payment.'))
        },
        {
            name: 'shipping',
            label: 'Frete',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('shipping.'))
        },
        {
            name: 'notification',
            label: 'Notificações da Loja',
            permissions: availablePermissions.value.filter(p => p?.code?.startsWith('notification.'))
        }
    ]
    
    return categories
})

// Methods
const loadStoreRoles = async () => {
    loading.value = true
    try {
        const storeId = storeStore.selectedStore?.id
        if (!storeId) {
            return
        }
        
        const response = await roleApi.getStoreRoles(storeId)
        // Normalize systemRole field for compatibility and sort system roles first
        const roles = (response.data || []).map(role => ({
            ...role,
            isSystemRole: role.systemRole || role.isSystemRole || false
        })).sort((a, b) => {
            // System roles first, then alphabetical by name
            if (a.isSystemRole && !b.isSystemRole) return -1
            if (!a.isSystemRole && b.isSystemRole) return 1
            return a.name.localeCompare(b.name)
        })
        storeRoles.value = roles
    } catch (error) {
        console.error('Error loading store roles:', error)
        toast.showError('Erro ao carregar cargos da loja')
    } finally {
        loading.value = false
    }
}

const loadAvailablePermissions = async () => {
    try {
        const storeId = storeStore.selectedStore?.id
        if (!storeId) return
        
        const response = await roleApi.getAvailablePermissions(storeId)
        availablePermissions.value = response.data || []
    } catch (error) {
        console.error('Error loading permissions:', error)
        toast.showError('Erro ao carregar permissões disponíveis')
    }
}

// Modal methods
const openCreateRoleModal = () => {
    currentRole.value = {
        name: '',
        description: '',
        permissions: []
    }
    isEditing.value = false
    showRoleModal.value = true
}

const editRole = (role) => {
    
    // Backend sends permissions as array of codes: ["store.view", "product.create", ...]
    // Convert codes to {code, description} objects by finding them in availablePermissions
    const permissionsArray = Array.isArray(role.permissions) ? role.permissions : []
    
    
    const selectedPermissions = permissionsArray
        .map(permissionCode => {
            const found = availablePermissions.value.find(p => p.code === permissionCode)
            return found
        })
        .filter(Boolean) // Remove any undefined values
    
    
    currentRole.value = {
        id: role.id,
        name: role.name,
        description: role.description || '',
        permissions: selectedPermissions
    }
    
    
    isEditing.value = true
    showRoleModal.value = true
}

const closeRoleModal = () => {
    showRoleModal.value = false
    currentRole.value = {
        name: '',
        description: '',
        permissions: []
    }
    isEditing.value = false
}

const saveRole = async () => {
    if (!validateRole()) return
    
    saving.value = true
    try {
        const storeId = storeStore.selectedStore?.id
        if (!storeId) return
        
        // Backend expects an array of permission codes like ["product.view", "product.edit"]
        const permissionsToSend = currentRole.value.permissions.map(p => p.code)
        
        const roleData = {
            name: currentRole.value.name,
            description: currentRole.value.description,
            permissions: permissionsToSend
        }
        
        
        if (isEditing.value) {
            await roleApi.updateStoreRole(storeId, currentRole.value.id, roleData)
            toast.showSuccess('Cargo atualizado com sucesso')
        } else {
            await roleApi.createStoreRole(storeId, roleData)
            toast.showSuccess('Cargo criado com sucesso')
        }
        
        closeRoleModal()
        loadStoreRoles()
    } catch (error) {
        console.error('Error saving role:', error)
        console.error('Error response:', error.response)
        toast.showError(error.response?.data?.message || 'Erro ao salvar cargo')
    } finally {
        saving.value = false
    }
}

const validateRole = () => {
    if (!currentRole.value.name?.trim()) {
        toast.showError('Nome do cargo é obrigatório')
        return false
    }
    
    if (!currentRole.value.permissions?.length) {
        toast.showError('Selecione pelo menos uma permissão')
        return false
    }
    
    return true
}

const confirmDeleteRole = (role) => {
    confirmData.value = {
        title: 'Excluir Cargo',
        message: `Tem certeza que deseja excluir o cargo "${role.name}"?`,
        type: 'error',
        onConfirm: () => deleteRole(role.id)
    }
    showConfirmDialog.value = true
}

const deleteRole = async (roleId) => {
    try {
        const storeId = storeStore.selectedStore?.id
        if (!storeId) return
        
        await roleApi.deleteStoreRole(storeId, roleId)
        toast.showSuccess('Cargo excluído com sucesso')
        loadStoreRoles()
    } catch (error) {
        console.error('Error deleting role:', error)
        toast.showError('Erro ao excluir cargo')
    }
}

// Permission handling
const isPermissionSelected = (permission) => {
    const isSelected = currentRole.value.permissions.some(p => p.code === permission.code)
    return isSelected
}

const togglePermission = (permission) => {
    const index = currentRole.value.permissions.findIndex(p => p.code === permission.code)
    
    if (index > -1) {
        currentRole.value.permissions.splice(index, 1)
    } else {
        currentRole.value.permissions.push(permission)
    }
}

const isCategorySelected = (category) => {
    return category.permissions.length > 0 && 
           category.permissions.every(p => isPermissionSelected(p))
}

const toggleCategory = (category) => {
    const allSelected = isCategorySelected(category)
    
    category.permissions.forEach(permission => {
        const index = currentRole.value.permissions.findIndex(p => p.code === permission.code)
        
        if (allSelected && index > -1) {
            // Remove permission
            currentRole.value.permissions.splice(index, 1)
        } else if (!allSelected && index === -1) {
            // Add permission
            currentRole.value.permissions.push(permission)
        }
    })
}

// View permissions
const viewPermissions = (role) => {
    selectedRole.value = role
    showPermissionsModal.value = true
}

const getSelectedPermissionsByCategory = () => {
    if (!selectedRole.value?.permissions?.length) {
        return []
    }
    
    // selectedRole.value.permissions is already an array of permission codes from backend
    const rolePermissionCodes = selectedRole.value.permissions
    
    const result = permissionCategories.value
        .map(category => ({
            ...category,
            permissions: category.permissions.filter(p => {
                return rolePermissionCodes.includes(p.code)
            })
        }))
        .filter(category => category.permissions.length > 0)
    
    return result
}

// Handle custom button clicks
const handleCustomButtonClick = (_index, button) => {
    if (button.onClick && typeof button.onClick === 'function') {
        button.onClick()
    }
}

// Confirmation dialog handler
const handleConfirm = () => {
    if (confirmData.value.onConfirm) {
        confirmData.value.onConfirm()
    }
    showConfirmDialog.value = false
}

// Lifecycle
onMounted(() => {
    loadAvailablePermissions()
    loadStoreRoles()
})
</script>

<style scoped>
.role-management-container {
    padding: 24px;
    max-width: 1400px;
    margin: 0 auto;
}

/* Table Styles */
.table-wrapper {
    margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-name) { width: 200px; text-align: left; }
:deep(.col-description) { width: auto; text-align: left; }
:deep(.col-permissions) { width: 180px; text-align: center; }
:deep(.col-actions) { width: 120px; text-align: center; }

/* General table styling - reuse from TeamManagement */
:deep(.role-table) {
    border-radius: 8px;
    overflow: hidden;
    box-shadow: var(--iluria-shadow-sm);
    background: var(--iluria-color-surface) !important;
    border: 1px solid var(--iluria-color-border) !important;
}

:deep(.role-table .p-datatable-table) {
    table-layout: auto;
    width: 100%;
}

:deep(.role-table .p-datatable-thead > tr > th) {
    background: var(--iluria-color-sidebar-bg) !important;
    color: var(--iluria-color-text-primary) !important;
    font-weight: 600;
    font-size: 12px;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border-bottom: 1px solid var(--iluria-color-border) !important;
    padding: 16px;
    text-align: center !important;
}

:deep(.role-table .p-datatable-tbody > tr) {
    border-bottom: 1px solid var(--iluria-color-border) !important;
    background: var(--iluria-color-surface) !important;
}

:deep(.role-table .p-datatable-tbody > tr:hover) {
    background: var(--iluria-color-hover) !important;
}

:deep(.role-table .p-datatable-tbody > tr > td) {
    padding: 16px;
    border: none;
    border-bottom: 1px solid var(--iluria-color-border);
    vertical-align: middle;
    background: inherit !important;
    color: var(--iluria-color-text) !important;
    font-size: 14px;
    text-align: center;
}

:deep(.role-table .p-datatable-tbody > tr > td.col-name),
:deep(.role-table .p-datatable-tbody > tr > td.col-description) {
    text-align: left;
}

/* Column header style */
:deep(.role-table th .column-header) {
    display: block;
    width: 100%;
    text-align: center !important;
    cursor: default;
    user-select: none;
}

/* Role name cell */
.role-name-cell {
    display: flex;
    align-items: center;
    gap: 8px;
    justify-content: flex-start;
}

.role-name {
    font-size: 14px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0;
    text-align: left;
}

.system-badge {
    display: inline-flex;
    align-items: center;
    gap: 4px;
    font-size: 10px;
    font-weight: 600;
    padding: 4px 8px;
    border-radius: 12px;
    background: linear-gradient(135deg, var(--iluria-color-warning-100), var(--iluria-color-warning-50));
    color: var(--iluria-color-warning-800);
    text-transform: uppercase;
    letter-spacing: 0.05em;
    border: 1px solid var(--iluria-color-warning-200);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.role-description {
    font-size: 13px;
    color: var(--iluria-color-text-secondary);
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
    display: block;
    max-width: 300px;
    text-align: left;
}

/* Permissions preview */
.permissions-preview {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 4px;
}

.permission-count {
    font-size: 13px;
    font-weight: 500;
    color: var(--iluria-color-text-primary);
}

/* Action buttons */
.action-buttons {
    display: flex;
    gap: 4px;
    justify-content: center;
}

/* Empty and Loading states - reuse from TeamManagement */
.empty-state {
    text-align: center;
    padding: 48px 24px;
    background: var(--iluria-color-surface);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
}

.empty-icon {
    color: var(--iluria-color-text-muted);
    margin-bottom: 16px;
    display: flex;
    justify-content: center;
    align-items: center;
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
    text-align: center;
}

.empty-description {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0 0 16px 0;
    text-align: center;
    line-height: 1.5;
    max-width: 400px;
}

.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 32px;
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
    background: var(--iluria-color-surface);
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid #e5e7eb;
    border-top: 2px solid #3b82f6;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Modal Styles */
.role-modal-content {
    padding: 24px 0;
}

.role-basic-info {
    margin-bottom: 32px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 16px;
}

.form-field {
    display: flex;
    flex-direction: column;
}

.form-label {
    font-size: 14px;
    font-weight: 500;
    color: var(--iluria-color-text-primary);
    margin-bottom: 6px;
}

.form-input {
    padding: 12px;
    border: 1px solid var(--iluria-color-border);
    border-radius: 8px;
    font-size: 14px;
    color: var(--iluria-color-text-primary);
    background: var(--iluria-color-surface);
    transition: border-color 0.2s ease;
}

.form-input:focus {
    outline: none;
    border-color: var(--iluria-color-primary);
    box-shadow: 0 0 0 3px var(--iluria-color-primary-50);
}

.form-input:disabled {
    background: var(--iluria-color-disabled);
    color: var(--iluria-color-text-muted);
}

/* Permissions Section */
.permissions-section {
    border-top: 1px solid var(--iluria-color-border);
    padding-top: 24px;
}

.section-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 6px 0;
}

.section-description {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0 0 24px 0;
}

.permissions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
    gap: 24px;
}

.permission-category {
    border: 1px solid var(--iluria-color-border);
    border-radius: 8px;
    padding: 16px;
}

.category-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 12px;
    padding-bottom: 8px;
    border-bottom: 1px solid var(--iluria-color-border-light);
}

.category-title {
    font-size: 15px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0;
}

.permissions-list {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.permission-item {
    display: flex;
    align-items: flex-start;
    gap: 8px;
    padding: 4px 0;
}

.permission-info {
    display: flex;
    flex-direction: column;
    flex: 1;
}

.permission-name {
    font-size: 13px;
    color: var(--iluria-color-text-primary);
    line-height: 1.3;
}

.permission-code {
    font-size: 11px;
    color: var(--iluria-color-text-muted);
    font-family: monospace;
    margin-top: 2px;
}

/* View Permissions Modal */
.view-permissions-content {
    padding: 0;
}

.permission-category-view {
    margin-bottom: 24px;
}

.category-title-view {
    font-size: 16px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 12px 0;
    padding-bottom: 6px;
    border-bottom: 1px solid var(--iluria-color-border);
}

.permissions-list-view {
    display: flex;
    flex-direction: column;
    gap: 6px;
}

.permission-item-view {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 4px 0;
}

.check-icon {
    color: var(--iluria-color-success);
    flex-shrink: 0;
}

.permission-description {
    font-size: 13px;
    color: var(--iluria-color-text-primary);
}

.no-permissions {
    text-align: center;
    padding: 32px;
    color: var(--iluria-color-text-muted);
}

/* Responsive */
@media (max-width: 768px) {
    .role-management-container {
        padding: 16px;
    }
    
    .form-row {
        grid-template-columns: 1fr;
    }
    
    .permissions-grid {
        grid-template-columns: 1fr;
    }
    
    .action-buttons {
        flex-direction: column;
        gap: 2px;
    }
}
</style>
