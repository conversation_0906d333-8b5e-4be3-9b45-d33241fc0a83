import axios from 'axios';
import { API_URLS } from '../config/api.config';

const handleServiceError = (methodName, error) => {
  const errorInfo = {
    method: `FileManagerService.${methodName}`,
    message: error.message,
    ...(error.response ? {
      status: error.response.status,
      data: error.response.data
    } : {}),
    ...(error.request && !error.response ? { noResponse: true } : {})
  };

  console.error('FileManagerService Error:', errorInfo);
  throw error;
};

class FileManagerService {
  #endpoint;

  constructor() {
    this.#endpoint = `${API_URLS.FILE_MANAGER_BASE_URL}`;
  }

  getEndpoint() {
    return this.#endpoint;
  }

  async getFileTree(environment) {
    try {
      const response = await axios.get(`${this.#endpoint}`, {
        params: { environment }
      });

      return response.data;
    } catch (error) {
      return handleServiceError('getFileTree', error);
    }
  }

  async getById(environment, parentFolderId) {
    try {
      const params = { environment };

      if (parentFolderId !== null && parentFolderId !== undefined && parentFolderId !== '') {
        params.parentFolderId = parentFolderId;
      }

      const response = await axios.get(this.#endpoint, { params });
      return response.data;
    } catch (error) {
      return handleServiceError('getById', error);
    }
  }


  async createFolder(folderData) {
    try {
      const config = {
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const response = await axios.post(`${this.#endpoint}/create-folder`, folderData, config);
      return response.data;
    } catch (error) {
      return handleServiceError('createFolder', error);
    }
  }

  async createFile(fileData) {
    try {
      const config = {
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const response = await axios.post(`${this.#endpoint}/create-file`, fileData, config);
      return response.data;
    } catch (error) {
      return handleServiceError('createFile', error);
    }
  }

  async uploadFile(file, parentFolderId, environment, relativePath = '', progressConfig = {}) {
    try {
      const formData = new FormData();
      formData.append('file', file);

      if (parentFolderId) {
        formData.append('parentFolderId', parentFolderId);
      } else {
        formData.append('parentFolderId', '');
      }

      if (relativePath) {
        const directoryPath = relativePath.split('/').slice(0, -1).join('/');
        if (directoryPath) {
          formData.append('relativePath', directoryPath);
        }
      }

      const env = environment?.toLowerCase() === 'production' ? 'prod' : 'dev';
      formData.append('environment', env);

      const config = {
        headers: {
          'Content-Type': 'multipart/form-data',
        },
        onUploadProgress: progressConfig.onUploadProgress
      };

      const response = await axios.post(`${this.#endpoint}/upload`, formData, config);
      
      return response.data;
    } catch (error) {
      error.fileName = file.name;
      return handleServiceError('uploadFile', error);
    }
  }

  async update(id, data) {
    try {
      const config = {
        headers: {
          'Content-Type': 'application/json'
        }
      };

      const response = await axios.put(`${this.#endpoint}/${id}`, data, config);
      return response.data;
    } catch (error) {
      return handleServiceError('update', error);
    }
  }

  async delete(id, type) {
    try {
      const response = await axios.delete(`${this.#endpoint}/${id}`, {
        params: { type }
      });
      return response.data;
    } catch (error) {
      return handleServiceError('delete', error);
    }
  }

  async deleteFolder(folderId) {
    try {
      const response = await axios.delete(`${this.#endpoint}/${folderId}`);
      return response.data;
    } catch (error) {
      return handleServiceError('deleteFolder', error);
    }
  }

  async downloadFileById(id, environment) {
    try {
      const response = await axios.get(`${this.#endpoint}/download-by-id/${id}?environment=${environment}`, {
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      return handleServiceError('downloadFileById', error);
    }
  }

  async downloadFolderById(id, environment) {
    try {
      const response = await axios.get(`${this.#endpoint}/download-by-id/${id}?environment=${environment}`, {
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      return handleServiceError('downloadFolderById', error);
    }
  }

  async downloadRootFiles(environment) {
    try {
      const response = await axios.get(`${this.#endpoint}/download-root-files?environment=${environment}`, {
        responseType: 'blob'
      });
      return response;
    } catch (error) {
      return handleServiceError('downloadRootFiles', error);
    }
  }

}
export default new FileManagerService();
