<template>
    <div :class="{ 'flex flex-col gap-2': props.labelPosition === 'vertical', 'flex items-center gap-2': props.labelPosition === 'horizontal' }">
        <span class="flex items-center gap-2">
            <IluriaLabel v-if="label" :for="id">{{ label }}</IluriaLabel> 
            <IluriaTooltip v-if="tooltip" :text="tooltip"></IluriaTooltip>
        </span>
        
        <!-- Custom Select Button Implementation -->
        <div 
            class="iluria-select-button-group" 
            :class="{ 'vertical': direction === 'vertical' }"
            :key="`select-group-${selectedValue}`"
        >
            <button
                v-for="option in options"
                :key="`option-${getOptionValue(option)}-${selectedValue}`"
                type="button"
                class="select-button-option"
                :class="{ 'active': selectedValue === getOptionValue(option) }"
                @click="selectOption(option)"
                :disabled="getOptionDisabled(option)"
            >
                {{ getOptionLabel(option) }}
            </button>
        </div>
    </div>
</template>

<script setup>
import { computed, watch, ref, nextTick, onMounted } from 'vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaTooltip from '@/components/iluria/IluriaTooltip.vue';
import { useTheme } from '@/composables/useTheme';

const { initTheme } = useTheme();

// Inicializar o tema
initTheme();

const model = defineModel()

// Ref local para forçar reatividade visual
const localValue = ref(model.value);

// Sincronizar ref local com model
watch(() => model.value, (newValue) => {
   
    localValue.value = newValue;
}, { immediate: true });

// Sincronizar mudanças locais de volta para o model
watch(localValue, (newValue) => {
    
    model.value = newValue;
    
    // Force uma atualização visual
    nextTick(() => {
        
    });
});

// Inicialização correta do valor padrão
onMounted(() => {
    // Se model.value for undefined/null, mas há um valor padrão nas opções, use-o
    if ((model.value === undefined || model.value === null) && props.options.length > 0) {
        const firstOption = props.options[0];
        const firstValue = getOptionValue(firstOption);
       
        localValue.value = firstValue;
    } else {
        // Garantir que localValue reflita o model atual
        localValue.value = model.value;
    }
});

const props = defineProps({
    options: {
        type: Array,
        required: true
    },
    label: {
        type: String,
        required: true
    },
    labelPosition: {
        type: String,
        default: 'vertical'
    },
    direction: {
        type: String,
        default: 'horizontal', 
        validator: (value) => ['horizontal', 'vertical'].includes(value)
    },
    id: {
        type: String,
        required: false
    },
    name: {
        type: String,
        default: null
    },
    optionLabel: {
        type: String,
        default: 'label'
    },
    optionValue: {
        type: String,
        default: 'value'
    },
    optionDisabled: {
        type: String,
        default: 'disabled'
    },
    tooltip: {
        type: String
    },
    allowEmpty: {
        type: Boolean,
        default: false
    }      
});

// Helper functions para acessar propriedades das opções
const getOptionLabel = (option) => {
    if (typeof option === 'string') return option;
    return option[props.optionLabel] || option.label || option;
};

const getOptionValue = (option) => {
    if (typeof option === 'string') return option;
    return option[props.optionValue] || option.value || option;
};

const getOptionDisabled = (option) => {
    if (typeof option === 'string') return false;
    return option[props.optionDisabled] || option.disabled || false;
};

// Verifica se uma opção está selecionada
const isSelected = (option) => {
    const optionValue = getOptionValue(option);
    const isSelected = localValue.value === optionValue;
    
    // Log apenas quando necessário para debug
    if (isSelected) {
        
    }
    
    return isSelected;
};

// Computed para forçar reatividade na seleção
const selectedValue = computed(() => localValue.value);

// Watcher para debug da reatividade
watch(selectedValue, (newValue, oldValue) => {
    
}, { immediate: true });

// Seleciona uma opção
const selectOption = (option) => {
    const optionValue = getOptionValue(option);
    
    
    
    // Se a opção já está selecionada e allowEmpty é true, deseleciona
    if (props.allowEmpty && localValue.value === optionValue) {
        localValue.value = null;
        
    } else {
        // Sempre seleciona a nova opção (comportamento padrão)
        localValue.value = optionValue;
            
    }
};
</script>

<style scoped>
.iluria-select-button-group {
    display: flex;
    gap: 0;
    border: 1px solid var(--iluria-color-border);
    border-radius: 8px;
    padding: 4px;
    width: fit-content;
    box-shadow: var(--iluria-shadow-sm);
    background-color: var(--iluria-color-container-bg);
    transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.iluria-select-button-group.vertical {
    flex-direction: column;
}

.select-button-option {
    border-radius: 6px;
    border: none;
    font-weight: 500;
    transition: all 0.15s ease;
    white-space: nowrap;
    flex-shrink: 0;
    font-size: 0.875rem;
    padding: 8px 16px;
    cursor: pointer;
    background: transparent;
    color: var(--iluria-color-text-primary);
    position: relative;
    
    /* DaisyUI Button Press Effect */
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
    
    /* Prevent text selection */
    user-select: none;
    -webkit-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
}

.select-button-option:focus {
    outline: none;
    box-shadow: 0 0 0 2px var(--iluria-color-focus-ring);
}

/* Estado inativo (ghost) */
.select-button-option:not(.active) {
    color: var(--iluria-color-text-secondary);
    background: transparent;
}

.select-button-option:not(.active):hover:not(:disabled) {
    background: var(--iluria-color-hover);
    color: var(--iluria-color-text-primary);
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

/* Estado ativo */
.select-button-option.active {
    background: var(--iluria-color-primary);
    color: var(--iluria-color-primary-contrast);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* REMOVIDO: Efeito de pressionar para botões de seleção */
.select-button-option:active:not(:disabled) {
    transform: none;
    box-shadow: 0 1px 2px rgba(0, 0, 0, 0.05);
}

.select-button-option.active:hover:not(:disabled) {
    background: var(--iluria-color-primary-hover);
    color: var(--iluria-color-primary-contrast);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* Estado desabilitado */
.select-button-option:disabled {
    opacity: 0.5;
    cursor: not-allowed;
    pointer-events: none;
}

/* Responsividade */
@media (max-width: 768px) {
    .iluria-select-button-group:not(.vertical) {
        width: 100%;
    }
    
    .select-button-option {
        flex: 1;
        justify-content: center;
        text-align: center;
    }
}

/* Variações específicas para diferentes temas */
:deep(.theme-dark) .iluria-select-button-group {
    border-color: var(--iluria-color-border);
    background-color: var(--iluria-color-container-bg);
}

:deep(.theme-blue) .select-button-option.active {
    background: var(--iluria-color-primary);
    color: var(--iluria-color-primary-contrast);
}

:deep(.theme-green) .select-button-option.active {
    background: var(--iluria-color-primary);
    color: var(--iluria-color-primary-contrast);
}

:deep(.theme-purple) .select-button-option.active {
    background: var(--iluria-color-primary);
    color: var(--iluria-color-primary-contrast);
}

/* Força refresh para elementos que possam ter cache */
.force-refresh .select-button-option {
    background-color: var(--iluria-color-container-bg) !important;
    color: var(--iluria-color-text-primary) !important;
}

.force-refresh .select-button-option.active {
    background-color: var(--iluria-color-primary) !important;
    color: var(--iluria-color-primary-contrast) !important;
}
</style>
