<template>
  <div class="editor-tabs">
    <!-- Navegação por abas -->
    <div class="tab-navigation iluria-scrollbar-horizontal">
      <button 
        v-for="tab in tabs"
        :key="tab.value"
        :class="['tab-button', { active: modelValue === tab.value }]"
        @click="$emit('update:modelValue', tab.value)"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> das abas -->
    <div class="tab-content">
      <slot />
    </div>
  </div>
</template>

<script setup>
defineProps({
  modelValue: {
    type: String,
    required: true
  },
  tabs: {
    type: Array,
    required: true,
    validator: (tabs) => {
      return tabs.every(tab => 
        typeof tab === 'object' && 
        'value' in tab && 
        'label' in tab
      )
    }
  }
})

defineEmits(['update:modelValue'])
</script>

<style scoped>
.editor-tabs {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--iluria-color-container-bg);
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.tab-button {
  padding: 12px 20px;
  background: none;
  border: none;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  border-bottom: 2px solid transparent;
  transition: all 0.2s;
  font-size: 14px;
  white-space: nowrap;
  flex-shrink: 0;
  min-width: fit-content;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-7);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 1.5rem;
  background: var(--iluria-color-container-bg);
}

/* Scrollbar Vertical */
.tab-content::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track {
  background: var(--iluria-color-sidebar-bg);
}

.tab-content::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border-hover);
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-muted);
}

</style> 
