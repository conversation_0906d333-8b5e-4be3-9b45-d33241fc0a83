<template>
  <div>
    <h2>Validation Example</h2>
    <Form v-slot="$form" :resolver="resolver" @submit.prevent="submitForm" :validate-on-blur="true">
      <div class="form-group">
        <IluriaInputText 
          id="name" 
          name="name" 
          :label="t('product.name')" 
          v-model="form.name" 
          :formContext="$form.name" 
        />
      </div>

      <div class="form-group">
        <IluriaInputText 
          id="price" 
          name="price" 
          type="number" 
          :label="t('product.price')" 
          v-model.number="form.price" 
          :formContext="$form.price" 
          required 
        />
      </div>

      <div class="form-group">
        <IluriaInputText 
          id="email" 
          name="email" 
          :label="t('login.email')" 
          v-model="form.email" 
          :formContext="$form.email" 
        />
      </div>

      <div class="form-group">
        <IluriaInputText 
          id="website" 
          name="website" 
          :label="t('website')" 
          v-model="form.website" 
          :formContext="$form.website" 
        />
      </div>

      <button type="submit">{{ t('save') }}</button>
    </Form>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useI18n } from 'vue-i18n';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Form } from 'vee-validate';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import { 
  requiredText, 
  requiredNonNegativeNumber, 
  emailValidator, 
  urlValidator 
} from '@/services/validation.service';

const { t } = useI18n();

const form = ref({
  name: '',
  price: null,
  email: '',
  website: ''
});

// Create validation schema using the validation service
const resolver = zodResolver(
  z.object({
    // Required text field with default max length of 255
    name: requiredText(t('product.name')),
    
    // Required number field that must be non-negative
    price: requiredNonNegativeNumber(t('product.price')),
    
    // Required email field with custom error message
    email: emailValidator(t('login.email'), {
      invalidMessage: t('validation.customEmailError', { field: t('login.email') })
    }),
    
    // Optional URL field
    website: urlValidator(t('website'), { required: false })
  })
);

const submitForm = () => {

};
</script>

<style scoped>
.form-group {
  margin-bottom: 1rem;
}
</style>
