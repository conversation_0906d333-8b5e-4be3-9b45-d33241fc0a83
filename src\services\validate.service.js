// validate.service.js

// Função para validar CNPJ
export function validaCNPJ(cnpj) {
    const b = [6, 5, 4, 3, 2, 9, 8, 7, 6, 5, 4, 3, 2];
    const c = String(cnpj).replace(/[^\d]/g, '');

    if (c.length !== 14) return false;

    if (/0{14}/.test(c)) return false;

    let i, n;
    for (i = 0, n = 0; i < 12; n += c[i] * b[++i]);
    if (c[12] != (((n %= 11) < 2) ? 0 : 11 - n)) return false;

    for (i = 0, n = 0; i <= 12; n += c[i] * b[i++]);
    return c[13] == (((n %= 11) < 2) ? 0 : 11 - n);
}

// Função para validar CPF
export function validaCPF(cpf) {
    let Soma = 0;
    let Resto;
    const strCPF = String(cpf).replace(/[^\d]/g, '');

    if (strCPF.length !== 11) return false;

    if ([ '00000000000', '11111111111', '22222222222', '33333333333', '44444444444', 
          '55555555555', '66666666666', '77777777777', '88888888888', '99999999999'
    ].indexOf(strCPF) !== -1) return false;

    for (let i = 1; i <= 9; i++)
        Soma = Soma + parseInt(strCPF.substring(i - 1, i)) * (11 - i);

    Resto = (Soma * 10) % 11;
    if ((Resto === 10) || (Resto === 11)) Resto = 0;

    if (Resto != parseInt(strCPF.substring(9, 10))) return false;

    Soma = 0;

    for (let i = 1; i <= 10; i++)
        Soma = Soma + parseInt(strCPF.substring(i - 1, i)) * (12 - i);

    Resto = (Soma * 10) % 11;
    if ((Resto === 10) || (Resto === 11)) Resto = 0;

    return Resto === parseInt(strCPF.substring(10, 11));
}