<template>
  <button 
    class="action-button" 
    :class="[variant, size]"
    :type="type"
    :disabled="disabled"
    @click="$emit('click')"
  >
    <slot name="icon"></slot>
    <slot></slot>
  </button>
</template>

<script setup>
defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'danger', 'ghost'].includes(value)
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  type: {
    type: String,
    default: 'button'
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

defineEmits(['click'])
</script>

<style scoped>
.action-button {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  font-weight: 500;
  border-radius: 6px;
  transition: all 0.15s ease;
  cursor: pointer;
  white-space: nowrap;
  font-size: 0.875rem;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* Variantes */
.primary {
  background: #3b82f6;
  color: white;
  border: none;
}

.primary:hover {
  background: #2563eb;
}

.secondary {
  background: transparent;
  color: #94a3b8;
  border: 1px solid #3d3d3d;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.secondary:hover {
  background: #2d2d2d;
  color: #f8fafc;
}

.danger {
  background: #ef4444;
  color: white;
  border: none;
}

.danger:hover {
  background: #dc2626;
}

.ghost {
  background: transparent;
  color: #94a3b8;
  border: none;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.ghost:hover {
  background: #2d2d2d;
  color: #f8fafc;
}

/* Tamanhos */
.small {
  padding: 0.375rem 0.75rem;
  font-size: 0.75rem;
}

.medium {
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
}

.large {
  padding: 0.625rem 1.25rem;
  font-size: 1rem;
}

.action-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  box-shadow: none;
}

.action-button:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Responsividade */
@media (max-width: 768px) {
  .action-button {
    width: 100%;
  }
}
</style> 
