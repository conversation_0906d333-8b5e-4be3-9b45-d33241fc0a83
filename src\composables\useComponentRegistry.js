import { ref } from 'vue'
import ConfigManager, { 
  getConfigByType, 
  getConfigByElement, 
  getAllConfigs,
  getConfigsByCategories 
} from '@/components/layoutEditor/configs/index.js'

/**
 * Sistema Unificado de Registro de Componentes - ARQUITETURA NOVA
 * 
 * MIGRAÇÃO COMPLETA PARA O SISTEMA DE CONFIGURAÇÕES CENTRALIZADAS:
 * - Componentes definidos em: src/components/layoutEditor/configs/*.config.js
 * - Auto-importação automática via import.meta.glob()
 * - Eliminação de código duplicado e registros manuais
 * - Sistema totalmente automatizado e padronizado
 * 
 * ELIMINA DUPLICAÇÕES:
 * - Não precisa mais de arquivos individuais em components/layoutEditor/components/
 * - <PERSON><PERSON> as configurações centralizadas em configs/
 * - Sistema único de verdade
 */

// Cache para otimização
const registryCache = ref(new Map())
const toolbarHandlers = ref(new Map())
const propertyEditors = ref(new Map()) 

/**
 * Inicializar sistema com as configurações centralizadas
 */
function initializeRegistry() {

  
  const configs = getAllConfigs()
  
  configs.forEach(config => {
    // Cache da configuração
    registryCache.value.set(config.type, config)
    
    // Registrar ações da toolbar
    if (config.toolbarActions) {
      config.toolbarActions.forEach(action => {
    if (!toolbarHandlers.value.has(action.type)) {
      toolbarHandlers.value.set(action.type, [])
    }
    toolbarHandlers.value.get(action.type).push({
          componentType: config.type,
      ...action
    })
  })
    }
  
    // Registrar editores de propriedades
    if (config.propertyEditors) {
      config.propertyEditors.forEach(editor => {
    propertyEditors.value.set(editor.type, {
          componentType: config.type,
      ...editor
    })
      })
    }
  })
  


}

// Inicializar automaticamente
initializeRegistry()

/**
 * Registra componente (mantido para compatibilidade, mas usa configurações centralizadas)
 */
export function registerComponent(config) {

  return config
}

/**
 * Obtém configuração de componente pelo tipo
 */
export function getComponentConfig(type) {
  return getConfigByType(type)
}

/**
 * Obtém todos os componentes disponíveis
 */
export function getAllComponents() {
  return getAllConfigs()
}

/**
 * Detecta componente a partir de elemento do DOM
 */
export function detectComponentFromElement(element) {
  if (!element) return null
  
  // Usar o sistema centralizado de detecção
  const config = getConfigByElement(element)
  if (config) {
    return {
      type: config.type,
      config,
      element
    }
  }
  
  return null
}

/**
 * Processa elemento para o canvas (modo edição)
 */
export function processElementForCanvas(element, config = null) {
  if (!element) return element
  
  const componentConfig = config || getConfigByElement(element)
  if (!componentConfig) return element
  
  // Aplicar estilos inline baseados na configuração
  if (componentConfig.html) {
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = componentConfig.html
    const templateElement = tempDiv.firstElementChild
    
    if (templateElement && templateElement.style.cssText) {
      element.style.cssText = templateElement.style.cssText
    }
  }
  
  // Preservar atributos específicos
  if (componentConfig.attributes?.preserve) {
    componentConfig.attributes.preserve.forEach(attr => {
      if (!element.getAttribute(attr) && componentConfig.attributes.defaults?.[attr]) {
        element.setAttribute(attr, componentConfig.attributes.defaults[attr])
      }
    })
  }
  
  return element
}

/**
 * Gera HTML limpo para salvamento
 */
export function generateCleanHtmlCode(element, config) {
  return processElementForSaving(element, null, config)
}

/**
 * Processa elemento para salvamento
 */
export function processElementForSaving(element, container, config = null) {
  if (!element) return element
  
  const componentConfig = config || getConfigByElement(element)
  const clonedElement = element.cloneNode(true)
  
  if (componentConfig) {
    // Aplicar limpeza específica do componente
    cleanupElement(clonedElement, componentConfig)
  } else {
    // Limpeza padrão
    cleanupEditingAttributes(clonedElement)
  }
  
  return clonedElement
}

/**
 * Limpa elemento usando configuração específica
 */
function cleanupElement(element, config) {
  // Remover atributos de edição
  cleanupEditingAttributes(element)
  
  // Limpar estilos de edição (manter estilos funcionais)
  if (config.cleanup?.removeEditingStyles !== false) {
    const currentStyle = element.style.cssText
    const stylesToPreserve = config.cleanup?.preserveStyles || []
    
    // Limpar estilos de seleção específicos
    element.style.cssText = currentStyle
      .split(';')
      .filter(style => {
        const property = style.split(':')[0]?.trim()
        if (!property) return false
        
        // Remover estilos de seleção
        if (property.includes('box-shadow') && style.includes('rgba(59,130,246')) {
          return false
        }
        
        // Preservar estilos específicos
        return stylesToPreserve.length === 0 || stylesToPreserve.some(preserve => 
          property.includes(preserve)
        )
      })
      .join(';')
  }
  
  // Limpar elementos filhos
  if (config.cleanup?.childSelectors) {
    config.cleanup.childSelectors.forEach(selector => {
      const childElements = element.querySelectorAll(selector)
      childElements.forEach(child => {
        cleanupEditingAttributes(child)
      })
    })
  }
  
  // Elementos especiais (como botões que precisam manter display: inline-block)
  if (config.cleanup?.specialElements) {
    Object.entries(config.cleanup.specialElements).forEach(([selector, rules]) => {
      const specialElements = element.querySelectorAll(selector)
      specialElements.forEach(specialEl => {
        if (rules.preserveInlineStyles) {
          // Já preservado pelos preserveStyles
        }
        if (rules.enforceDisplay) {
          specialEl.style.display = rules.enforceDisplay
        }
      })
    })
  }
}

/**
 * Remove atributos de edição padrão
 */
function cleanupEditingAttributes(element) {
  const editingAttributes = [
    'draggable', 'contenteditable', 'data-draggable', 'data-editable',
    'data-component-selected', 'data-component-hover', 'data-resizable'
  ]
  
  editingAttributes.forEach(attr => {
    element.removeAttribute(attr)
  })
  
  // Limpar outline e borders de edição do style
  if (element.style) {
    element.style.outline = ''
    element.style.border = element.style.border?.replace(/2px solid rgba\(59,130,246,[^)]+\)/, '') || ''
  }
}

/**
 * Determina se deve preservar draggable
 */
export function shouldPreserveDraggable(element) {
  return false // Sistema novo não usa draggable
}

/**
 * Gera seletor para componente
 */
export function generateSelectorForComponent(config) {
  if (config.selectors && config.selectors.length > 0) {
    return config.selectors[0]
  }
  return `[data-component="${config.type}"]`
}

/**
 * Obtém todos os seletores de componentes
 */
export function getAllComponentSelectors() {
  const configs = getAllConfigs()
  const selectors = []
  
  configs.forEach(config => {
    if (config.selectors) {
      selectors.push(...config.selectors)
    }
  })
  
  // Retorna os seletores como string separada por vírgula
  const selectorString = selectors.join(', ')

  return selectorString
}

/**
 * Auto-inicializa componentes no documento
 */
export function autoInitializeComponents(document) {
  const configs = getAllConfigs()
  
  configs.forEach(config => {
    if (config.initialization?.autoInit && config.initialization?.scriptLoader) {
      const elements = document.querySelectorAll(generateSelectorForComponent(config))
    elements.forEach(element => {
      try {
          const initFunction = window[config.initialization.scriptLoader]
          if (typeof initFunction === 'function') {
            initFunction(element, document)
        }
      } catch (error) {
          console.warn(`Erro ao inicializar ${config.name}:`, error)
      }
    })
  }
  })
}

/**
 * Re-inicializa componente específico
 */
export function reinitializeComponent(element) {
  const config = getConfigByElement(element)
  if (config && config.initialization?.scriptLoader) {
    try {
      const initFunction = window[config.initialization.scriptLoader]
      if (typeof initFunction === 'function') {
        initFunction(element, document)
      }
  } catch (error) {
      console.warn(`Erro ao re-inicializar ${config.name}:`, error)
    }
  }
}

/**
 * Inicializa todos os componentes do sistema
 */
export function initializeAllSystemComponents() {
  autoInitializeComponents(document)
}

// === FUNÇÕES DE INICIALIZAÇÃO DE COMPONENTES ESPECÍFICOS (mantidas para compatibilidade) ===

export function initializeVideoComponent(element, document) {
  const config = getConfigByType('video')
  if (!config) return
  
  const videoData = loadVideoData(element)
  setupVideoComponent(element, videoData, document)
}

function loadVideoData(element) {
  return {
    title: element.getAttribute('data-video-title') || 'Título do vídeo',
    description: element.getAttribute('data-video-description') || 'Descrição do vídeo',
    buttonText: element.getAttribute('data-button-text') || 'Saiba mais',
    buttonUrl: element.getAttribute('data-button-url') || '#',
    buttonEnabled: element.getAttribute('data-button-enabled') === 'true',
    videoUrl: element.getAttribute('data-video-url') || '',
    videoType: element.getAttribute('data-video-type') || 'youtube',
    posterImage: element.getAttribute('data-video-poster') || '',
    autoplay: element.getAttribute('data-video-autoplay') === 'true',
    loop: element.getAttribute('data-video-loop') === 'true',
    muted: element.getAttribute('data-video-muted') === 'true',
    controls: element.getAttribute('data-video-controls') !== 'false',
    aspectRatio: element.getAttribute('data-video-aspect-ratio') || '16:9',
      layout: element.getAttribute('data-layout') || 'horizontal',
    videoPosition: element.getAttribute('data-video-position') || 'left',
    backgroundColor: element.getAttribute('data-bg-color') || '#ffffff',
    textColor: element.getAttribute('data-text-color') || '#000000',
    buttonColor: element.getAttribute('data-button-color') || '#000000',
    buttonTextColor: element.getAttribute('data-button-text-color') || '#ffffff',
    paddingTop: parseInt(element.getAttribute('data-padding-top')) || 60,
    paddingBottom: parseInt(element.getAttribute('data-padding-bottom')) || 60,
    borderRadius: parseInt(element.getAttribute('data-border-radius')) || 12
  }
}

function setupVideoComponent(element, config, document) {
  const html = generateVideoHTML(config)
  element.innerHTML = html
  initializeVideoComponentFeatures(element, config)
}

function generateVideoHTML(config) {
  const containerStyles = `
        max-width: 1200px;
        margin: 0 auto;
    padding: 0 2rem;
  `
  
  const contentStyles = `
        display: flex;
    gap: 2rem; 
        align-items: center;
    ${config.layout === 'vertical' ? 'flex-direction: column; text-align: center;' : ''}
    ${config.videoPosition === 'right' && config.layout === 'horizontal' ? 'flex-direction: row-reverse;' : ''}
  `
    
    return `
    <div class="video-container" style="${containerStyles}">
      <div class="video-content" style="${contentStyles}">
        ${generateVideoComponentSection(config)}
        ${generateVideoContentSection(config)}
        </div>
      </div>
    `
}

function generateVideoComponentSection(config) {
  const sectionStyles = `flex: 1;`
  const wrapperStyles = `
    aspect-ratio: ${config.aspectRatio}; 
    border-radius: 8px; 
    overflow: hidden; 
    background: rgba(0,0,0,0.1);
  `
  
  let videoContent = ''
  
  if (config.videoUrl) {
    const videoId = extractVideoComponentId(config.videoUrl, config.videoType)
    
    switch (config.videoType) {
      case 'youtube':
        videoContent = generateYouTubeComponentEmbed(videoId, config)
        break
      case 'vimeo':
        videoContent = generateVimeoComponentEmbed(videoId, config)
        break
      case 'direct':
        videoContent = generateDirectVideoComponent(config.videoUrl, config)
        break
      default:
        videoContent = generatePlaceholderVideoComponent(config)
    }
  } else {
    videoContent = generatePlaceholderVideoComponent(config)
  }

  return `
    <div class="video-section" style="${sectionStyles}">
      <div class="video-wrapper" style="${wrapperStyles}">
        ${videoContent}
          </div>
          </div>
  `
}

function generateYouTubeComponentEmbed(videoId, config) {
  if (!videoId) return generatePlaceholderVideoComponent(config)
  
  const autoplay = config.autoplay ? '&autoplay=1' : ''
  const loop = config.loop ? `&loop=1&playlist=${videoId}` : ''
  const muted = config.muted ? '&mute=1' : ''
  const controls = config.controls ? '' : '&controls=0'
  
  return `
    <iframe
      class="video-iframe"
      src="https://www.youtube.com/embed/${videoId}?rel=0${autoplay}${loop}${muted}${controls}"
      style="width: 100%; height: 100%; border: none;"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen>
    </iframe>
  `
}

function generateVimeoComponentEmbed(videoId, config) {
  if (!videoId) return generatePlaceholderVideoComponent(config)
  
  const autoplay = config.autoplay ? '&autoplay=1' : ''
  const loop = config.loop ? '&loop=1' : ''
  const muted = config.muted ? '&muted=1' : ''

  return `
    <iframe
      class="video-iframe"
      src="https://player.vimeo.com/video/${videoId}?title=0&byline=0&portrait=0${autoplay}${loop}${muted}"
      style="width: 100%; height: 100%; border: none;"
      allow="autoplay; fullscreen; picture-in-picture"
      allowfullscreen>
    </iframe>
  `
}

function generateDirectVideoComponent(url, config) {
  const controlsAttr = config.controls ? 'controls' : ''
  const autoplayAttr = config.autoplay ? 'autoplay' : ''
  const loopAttr = config.loop ? 'loop' : ''
  const mutedAttr = config.muted ? 'muted' : ''
  
  return `
    <video
      class="video-element"
      style="width: 100%; height: 100%; object-fit: cover;"
      ${controlsAttr}
      ${autoplayAttr}
      ${loopAttr}
      ${mutedAttr}
      ${config.posterImage ? `poster="${config.posterImage}"` : ''}>
      <source src="${url}" type="video/mp4">
      <p>Seu navegador não suporta vídeos HTML5.</p>
    </video>
  `
}

function generatePlaceholderVideoComponent(config) {
  return `
    <div class="video-placeholder" style="
        width: 100%;
      height: 100%; 
        display: flex;
        align-items: center;
        justify-content: center;
        color: ${config.textColor};
    ">
      <div class="placeholder-content" style="text-align: center; opacity: 0.7;">
        <svg class="placeholder-icon" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin: 0 auto 10px;">
          <polygon points="5,3 19,12 5,21"/>
        </svg>
        <p class="placeholder-text" style="margin: 0; font-size: 14px;">Configure o vídeo para visualizar</p>
    </div>
    </div>
  `
}

function generateVideoContentSection(config) {
  const sectionStyles = `flex: 1; padding: 1rem;`
  const titleStyles = `
    font-size: 2rem; 
      font-weight: bold;
    margin: 0 0 1rem; 
      color: ${config.textColor};
    line-height: 1.2;
  `
  const descStyles = `
    font-size: 1rem; 
    line-height: 1.6; 
    margin: 0 0 1.5rem; 
      color: ${config.textColor};
    opacity: 0.8;
  `
  const buttonStyles = `
    background-color: ${config.buttonColor}; 
    color: ${config.buttonTextColor}; 
    padding: 12px 30px; 
    border: none; 
    border-radius: 6px; 
    font-size: 1rem; 
    font-weight: 600; 
      text-decoration: none;
    display: inline-block; 
    cursor: pointer; 
    transition: all 0.3s ease;
  `
  
  return `
    <div class="content-section" style="${sectionStyles}">
      <h2 class="video-title" style="${titleStyles}">${config.title}</h2>
      <p class="video-description" style="${descStyles}">${config.description}</p>
      ${config.buttonEnabled ? `
        <a href="${config.buttonUrl}" class="video-button" style="${buttonStyles}">
      ${config.buttonText}
    </a>
      ` : ''}
    </div>
  `
}

function initializeVideoComponentFeatures(element, config) {
  // Implementar funcionalidades interativas do vídeo se necessário
}

function extractVideoComponentId(url, type) {
  switch (type) {
    case 'youtube':
      return extractYouTubeVideoId(url)
    case 'vimeo':
      return extractVimeoVideoId(url)
    default:
      return null
  }
}

function extractYouTubeVideoId(url) {
  const regExp = /^.*(youtu.be\/|v\/|u\/\w\/|embed\/|watch\?v=|&v=)([^#&?]*).*/
  const match = url.match(regExp)
  return (match && match[2].length === 11) ? match[2] : null
}

function extractVimeoVideoId(url) {
  const regExp = /(?:vimeo)\.com.*(?:videos|video|channels|)\/([\d]+)/i
  const match = url.match(regExp)
  return match ? match[1] : null
}

// === FUNÇÕES DO SISTEMA ORIGINAL (mantidas para compatibilidade) ===

export function getToolbarActionsForElement(element) {
  const actions = []
  
  for (const [actionType, handlers] of toolbarHandlers.value.entries()) {
    for (const handler of handlers) {
      if (handler.condition && handler.condition(element)) {
          actions.push({
            type: actionType,
          title: handler.title,
          icon: handler.icon,
          handler: handler.handler
          })
        }
      }
  }

  return actions
}

export function getComponentLibrary() {
  const categories = getConfigsByCategories()
  const library = {}
  
  Object.entries(categories).forEach(([category, configs]) => {
    library[category] = {
      title: category.charAt(0).toUpperCase() + category.slice(1),
      components: configs.map(config => ({
        name: config.name,
        type: config.type,
        description: config.description,
        icon: config.icon,
        html: config.html
      }))
    }
  })

  return library
}

export function getComponentCategories() {
  return getConfigsByCategories()
}

export function getPropertyEditor(editorType) {
  return propertyEditors.value.get(editorType)
}

export function getComputedCheckers(componentType) {
  // Implementar se necessário
  return []
}

export function clearRegistry() {
  registryCache.value.clear()
  toolbarHandlers.value.clear()
  propertyEditors.value.clear()
}

export function useComponentRegistry() {
  return {
    registerComponent,
    getComponentConfig,
    getAllComponents,
    detectComponentFromElement,
    processElementForCanvas,
    processElementForSaving,
    autoInitializeComponents,
    reinitializeComponent,
    getToolbarActionsForElement,
    getComponentLibrary,
    getPropertyEditor,
    clearRegistry
  }
} 