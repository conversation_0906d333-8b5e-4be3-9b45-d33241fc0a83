{"title": "Combined Products", "subtitle": "Manage your combined products", "searchPlaceholder": "Search combined products...", "actions": {"add": "Add Product", "edit": "Edit", "delete": "Delete"}, "table": {"headers": {"image": "Image", "name": "Product Name", "status": "Status", "actions": "Actions"}}, "status": {"active": "Active", "inactive": "Inactive"}, "empty": {"message": "No combined products found.", "action": "Create your first combined product!"}, "loading": "Loading combined products...", "edit": {"titleCreate": "Create Combined Product", "titleEdit": "Edit Combined Product", "sections": {"basicInfo": "Basic Information", "basicInfoDescription": "Configure name, description and product status", "status": "Status", "images": "Images", "imagesDescription": "Upload and configure stickers on images"}, "sidebar": {"quickInfo": "Quick Information", "quickInfoDescription": "Summary of product under construction", "totalImages": "Total Images", "totalStickers": "Total Stickers", "linkedProducts": "Linked Products", "limits": "Limits", "limitsDescription": "Track product limits"}, "fields": {"productName": "Product Name *", "productNamePlaceholder": "Enter product name...", "description": "Description", "descriptionPlaceholder": "Describe this product...", "productStatus": "Product Status", "statusPlaceholder": "Select a status"}, "statusOptions": {"active": "Visible", "inactive": "Invisible"}, "preview": {"title": "Preview of item {index}", "dragHint": "Grab it here:", "stickerHint": "New sticker starts at center. <PERSON><PERSON> to position.", "removeHint": "Drag to edge to remove. Double click to choose product."}, "removeZone": {"title": "Drag here to delete"}, "tooltips": {"withProduct": "{name} - ${price}\nDrag to move, drag to edge to remove, double click to change product.", "withoutProduct": "Sticker {index}: Drag to move or drag to edge to remove. Double click to choose product.", "newSticker": "New sticker: starts at image center. Drag to position or drag to edge to remove."}, "actions": {"create": "Create Product", "save": "Save Changes", "cancel": "Cancel"}, "loading": "Loading...", "validation": {"nameRequired": "Product name is required", "imageRequired": "At least one image is required", "maxImages": "Maximum of 5 images allowed", "productRequired": "Add at least one product to a sticker to create the combined product"}, "saving": {"info": "Saving: {productCount} products in {stickerCount} stickers"}}, "messages": {"limitReached": "Limit of {max} combined products reached", "deleteConfirm": "Are you sure you want to delete the combined product \"{name}\"?", "deleteSuccess": "Product \"{name}\" deleted successfully", "deleteError": "Error deleting combined product", "deleteNotFound": "Product not found", "deleteNoPermission": "No permission to delete this product", "deleteServerError": "Server error. Please try again later", "deleteNetworkError": "Connection error. Check your internet", "invalidProduct": "Invalid product for deletion", "loadError": "Error loading combined products", "stickerRemoved": "Sticker removed successfully!", "createSuccess": "Combined product created successfully", "updateSuccess": "Combined product updated successfully", "createError": "Error creating combined product", "updateError": "Error updating combined product", "loadSuccess": "Product loaded successfully", "loadEditError": "Error loading combined product", "savedLocally": "Product saved locally. Will sync when possible."}}