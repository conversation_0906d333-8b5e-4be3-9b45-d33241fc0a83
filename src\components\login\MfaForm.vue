<template>
    <form @submit.prevent="submitMfaCode" class="space-y-4">
      <div class="welcome-back-message text-center mb-5">
        <p>{{ $t("login.welcome") }}</p>
        <p v-if="props.mfaType === 'EMAIL_VERIFICATION'">
          {{ $t("login.mfaEmailSent") }} <strong>{{ email }}</strong>. {{ $t("login.checkYourInbox") }}
        </p>
        <p v-else-if="props.mfaType === 'TOTP'">
          {{ $t("login.totpRequired") }}
        </p>
      </div>
  
      <label for="mfaCode" class="block text-sm font-medium text-gray-700">
        {{ props.mfaType === 'TOTP' ? $t("login.totpCode") : $t("login.mfaCode") }}:
      </label>
      <div class="mfa-code-input flex justify-between space-x-2">
        <input
          type="text"
          v-for="(digit, index) in 6"
          :key="index"
          v-model="mfaDigits[index]"
          maxlength="1"
          @input="focusNext(index)"
          @keydown.delete="focusPrevious(index)"
          @paste="handlePaste"
          class="mfa-digit w-10 h-12 text-center text-lg border border-black rounded-md "
        />
      </div>
  
      <div class="remember-mfa flex items-start space-x-2">
        <IluriaCheckBox type="checkbox" id="rememberMfa" v-model="rememberMfa" class="mr-2 custom-checkbox" />
        <label for="rememberMfa" class="text-sm text-gray-700 hover:text-blue-600 cursor-pointer">
          {{ $t("login.rememberMfa") }}
        </label>
      </div>
  
      <div class="flex space-x-2">
        <IluriaButton type="submit" class="w-full">{{ $t("login.submitMfa") }}</IluriaButton>
        <IluriaButton 
          v-if="props.mfaType === 'EMAIL_VERIFICATION'"
          size="small" 
          type="button" 
          @click="resendEmail" 
          :disabled="resendDisabled" 
          class="w-full" 
          :class="{ 'opacity-50 cursor-not-allowed': resendDisabled }"
        >
          {{ resendButtonText }}
        </IluriaButton>
      </div>
    </form>
  </template>
  
  <script setup>
  import { useRouter } from 'vue-router';
  import { ref, computed } from 'vue';
  import { useAuthStore } from '@/stores/auth.store';
  import authService from "@/services/auth.service";
  import { useI18n } from 'vue-i18n';
  import IluriaButton from '@/components/iluria/IluriaButton.vue';
  import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';
  import { useToast } from '@/services/toast.service';
  const { addToast } = useToast();
  const props = defineProps({
    email: String,
    mfaType: {
      type: String,
      default: 'EMAIL_VERIFICATION' // 'EMAIL_VERIFICATION' | 'TOTP'
    }
  });
  
  const router = useRouter();
  const authStore = useAuthStore();
  const mfaDigits = ref(Array(6).fill(''));
  const rememberMfa = ref(false);
  const resendDisabled = ref(false);
  const resendTimeout = ref(30);
  const { t } = useI18n();
  
  const resendButtonText = computed(() => {
    return resendDisabled.value ? t("login.resendIn", { seconds: resendTimeout.value }) : t("login.resend");
  });
  
  const focusNext = (index) => {
    if (index < 5 && mfaDigits.value[index]) {
      document.querySelectorAll('.mfa-digit')[index + 1].focus();
    }
  };
  
  const focusPrevious = (index) => {
    if (index > 0 && !mfaDigits.value[index]) {
      document.querySelectorAll('.mfa-digit')[index - 1].focus();
    }
  };
  
  const handlePaste = (event) => {
  event.preventDefault();
  
  const pasteData = event.clipboardData.getData('text').trim();
  if (pasteData.length === 6 && /^[0-9]+$/.test(pasteData)) {
    mfaDigits.value = pasteData.split('');

    setTimeout(() => {
      document.querySelectorAll('.mfa-digit')[5].focus();
    }, 10);
  }
};

const submitMfaCode = async () => {
  const mfaCode = mfaDigits.value.join('');
  try {
    const response = await authStore.verifyMfaCode(mfaCode, props.email, rememberMfa.value);

    if (response && response.jwt) {
      addToast(t('login.welcome'), 'success');
      router.push({ name: 'StoreSelection' });
    } else {
      addToast(t('login.genericError'), 'error');
    }
  } catch (error) {

    if (error.response?.status === 401) {
      addToast(t('login.invalidCode'), 'error');
    } else if (error.message === 'Network Error') {
      addToast(t('login.networkError'), 'error');
    } else {
      addToast(t('login.genericError'), 'error');
    }
  }
};
  
const resendEmail = async () => {
  resendDisabled.value = true;
  resendTimeout.value = 30;
  const interval = setInterval(() => {
    resendTimeout.value -= 1;
    if (resendTimeout.value <= 0) {
      clearInterval(interval);
      resendDisabled.value = false;
    }
  }, 1000);

  try {
    await authService.resendMfaCode(props.email);
    addToast(t('login.mfaResentSuccess'), 'success');
  } catch (error) {
    console.error("Erro ao reenviar o código MFA:", error);
    addToast(t('login.mfaResentError'), 'error');
  }
};

  </script>

  <style scoped>
   .welcome-back-message {
   color: black;
  }
  
  .mfa-digit {
    width: 40px;
    height: 40px;
    font-size: 1.5rem;
    text-align: center;
    border-radius: 5px;
    margin-bottom: 10px;
    color: black !important
  }
  
</style>
  
