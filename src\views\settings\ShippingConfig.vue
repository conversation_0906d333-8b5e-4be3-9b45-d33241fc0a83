<template>
  <div class="shipping-config-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="t('shippingConfig.title')"
      :subtitle="t('shippingConfig.description')"
      :showSave="true"
      :saveText="t('shippingConfig.saveButton')"
      @save-click="handleSave"
    />

    <!-- Main Content -->
    <Form 
      id="shipping-form"
      v-slot="$form" 
      @submit="salvarConfiguracoes"   
      :resolver="formResolver"  
      :validate-on-blur="false" 
      :validate-on-value-update="true"
      :validate-on-submit="false"
      class="form-container"
    >
      
      <!-- Configurações de Acréscimo -->
      <ViewContainer 
        :title="t('shippingConfig.increaseShippingTitle')"
        :subtitle="t('shippingConfig.increaseShippingSubtitle')"
        :icon="AnalyticsUpIcon"
        iconColor="blue"
      >
        <IncreaseShipping
          ref="increaseShippingRef"
          v-model:increaseShippingValue="formValues.increaseShippingValue"
          v-model:increaseShippingPercentage="formValues.increaseShippingPercentage"
          :formContext="$form"
        />
      </ViewContainer>
      
      <!-- Grid para seções lado a lado -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <!-- Retirar na Loja -->
        <ViewContainer 
          :title="t('shippingConfig.pickupTitle')"
          :subtitle="t('shippingConfig.pickupSubtitle')"
          :icon="StoreIcon"
          iconColor="orange"
        >
          <PickupShipping
            ref="pickupShippingRef"
            v-model:pickupDescription="formValues.pickupDescription"
            v-model:pickupEnabled="formValues.pickupEnabled"
            :formContext="$form"
          />
        </ViewContainer>

        <!-- Restrições por CEP -->
        <ViewContainer 
          :title="t('shippingConfig.zipCodeRestriction.title')" 
          :subtitle="t('shippingConfig.zipCodeRestriction.subtitle')"
          :icon="LocationIcon"
          iconColor="purple"
        >
          <ZipCodeRestriction
            ref="zipCodeRestrictionRef"
            v-model:enabled="formValues.zipCodeRestrictionEnabled"
            v-model:ranges="formValues.zipCodeRestrictionRanges"
            :formContext="$form"
          />
        </ViewContainer>
      </div>

      <!-- Integração com Correios -->
      <ViewContainer 
        :title="t('shippingConfig.correiosTitle')"
        :subtitle="t('shippingConfig.correiosSubtitle')"
        :icon="DeliveryTruck01Icon"
        iconColor="blue"
      >
        <CorreiosIntegration
          ref="correiosIntegrationRef"
          v-model:offerSedex="formValues.offerSedex"
          v-model:offerPac="formValues.offerPac"
          v-model:includeInsurance="formValues.includeInsurance"
          v-model:showDeliveryTime="formValues.showDeliveryTime"
          v-model:addFixedValue="formValues.addFixedValue"
          v-model:fixedValueAmount="formValues.fixedValueAmount"
          v-model:addPercentageValue="formValues.addPercentageValue"
          v-model:percentageAmount="formValues.percentageAmount"
          v-model:hasContract="formValues.hasContract"
          v-model:correiosOfferSedex="formValues.correiosOfferSedex"
          v-model:correiosOfferPac="formValues.correiosOfferPac"
          v-model:correiosIncludeInsurance="formValues.correiosIncludeInsurance"
          v-model:correiosShowDeliveryTime="formValues.correiosShowDeliveryTime"
          v-model:correiosAddFixedValue="formValues.correiosAddFixedValue"
          v-model:correiosFixedValueAmount="formValues.correiosFixedValueAmount"
          v-model:correiosAddPercentageValue="formValues.correiosAddPercentageValue"
          v-model:correiosPercentageAmount="formValues.correiosPercentageAmount"
          v-model:correiosHasContract="formValues.correiosHasContract"
          v-model:correiosUser="formValues.correiosUser"
          v-model:correiosApiKey="formValues.correiosApiKey"
          :formContext="$form"
        />
      </ViewContainer>
      
      <!-- Entrega Personalizada -->
      <ViewContainer 
        :title="t('shippingConfig.localShippingTitle')"
        :subtitle="t('shippingConfig.localShippingSubtitle')"
        :icon="TruckDeliveryIcon"
        iconColor="green"
      >
        <LocalShipping
          ref="localShippingRef"
          v-model:localShippingEnabled="formValues.localShippingEnabled"
          v-model:localShippingDescription="formValues.localShippingDescription"
          v-model:localShippingDiscountType="formValues.localShippingDiscountType"
          v-model:localShippingMinimumValueDiscount="formValues.localShippingMinimumValueDiscount"
          v-model:localShippingPercentageDiscount="formValues.localShippingPercentageDiscount"
          :formContext="$form"
        />
      </ViewContainer>
    </Form>
  </div>
</template>

<script setup>
import { ref, computed, onBeforeMount } from 'vue';
import { useI18n } from 'vue-i18n';
import { z } from 'zod';
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { Form } from '@primevue/forms'
import shippingService from '@/services/shipping.service';
import { useToast } from '@/services/toast.service';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IncreaseShipping from '@/components/shippingConfig/IncreaseShipping.vue';
import PickupShipping from '@/components/shippingConfig/PickupShipping.vue';
import ZipCodeRestriction from '@/components/shippingConfig/ZipCodeRestriction.vue';
import LocalShipping from '@/components/shippingConfig/LocalShipping.vue';
import CorreiosIntegration from '@/components/shippingConfig/CorreiosIntegration.vue';
import { 
  AnalyticsUpIcon,
  StoreIcon, 
  TruckDeliveryIcon,
  LocationIcon,
  FloppyDiskIcon,
  DeliveryTruck01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n();
const toast = useToast();

const saving = ref(false);
const formInitialValues = ref({});
const formValues = ref({});

const increaseShippingRef = ref(null);
const pickupShippingRef = ref(null);
const zipCodeRestrictionRef = ref(null);
const localShippingRanges = ref([]);
const localShippingRef = ref(null);
const correiosIntegrationRef = ref(null);

const formResolver = computed(() => {
  const increaseShippingRules = increaseShippingRef.value?.validationRules || {};
  const pickupShippingRules = pickupShippingRef.value?.validationRules || {};
  const zipCodeRestrictionRules = zipCodeRestrictionRef.value?.validationRules || {};
  const localShippingRules = localShippingRef.value?.validationRules || {};
  
  return zodResolver(
    z.object({
      ...increaseShippingRules,
      ...pickupShippingRules,
      ...zipCodeRestrictionRules,
      ...localShippingRules
    })
  );
});

const handleSave = () => {
  // Trigger form submission
  const form = document.getElementById('shipping-form');
  if (form) {
    form.requestSubmit();
  }
}

const salvarConfiguracoes = async (e) => {
  saving.value = true;

  try {
    if (formValues.value.zipCodeRestrictionEnabled && zipCodeRestrictionRef.value?.hasEmptyFields) {
      toast.showWarning(t('shippingConfig.zipCodeRestriction.fieldsRequired'));
      return;
    }

    localShippingRef.value?.validateAndSetDiscountType?.()


    const result = await shippingService.updateShippingConfig(formValues.value);
    formValues.value = {
      ...formValues.value,
      ...result
    };
    
    toast.showSuccess(t('shippingConfig.savedSuccess'));
    
    if (result) {
      if (result.enableLocalShipping) {
        try {
          const ranges = await shippingService.getLocalShippingRanges();
          localShippingRanges.value = ranges;
        } catch (error) {
          console.error('Error loading shipping ranges:', error);
        }
      }
    }
  } catch (error) {
    console.error('Error saving shipping settings:', error);
    toast.showError(t('shippingConfig.savedError'));
  } finally {
    saving.value = false;
  }
};

onBeforeMount(async () => {
  try {
    const data = await shippingService.getShippingConfig();
    formValues.value = {
      ...data,
      pickupEnabled: data.pickupEnabled ?? false,
      pickupDescription: data.pickupDescription ?? '',
      correiosOfferSedex: data.correiosOfferSedex ?? false,
      correiosOfferPac: data.correiosOfferPac ?? false,
      correiosIncludeInsurance: data.correiosIncludeInsurance ?? false,
      correiosShowDeliveryTime: data.correiosShowDeliveryTime ?? false,
      correiosAddFixedValue: data.correiosAddFixedValue ?? false,
      correiosAddPercentageValue: data.correiosAddPercentageValue ?? false,
      correiosHasContract: data.correiosHasContract ?? false,
      correiosFixedValueAmount: data.correiosFixedValueAmount ?? 0,
      correiosPercentageAmount: data.correiosPercentageAmount ?? 0,
      correiosUser: data.correiosUser ?? '',
      correiosApiKey: data.correiosApiKey ?? '',
      localShippingDiscountType: data.localShippingDiscountType ?? 'NO_DISCOUNT'
    };
    
    if (data.enableLocalShipping) {
      try {
        const ranges = await shippingService.getLocalShippingRanges();
        localShippingRanges.value = ranges;
      } catch (error) {
        console.error('Error loading shipping ranges:', error);
      }
    }
  } catch (error) {
    console.error('Error loading shipping settings:', error);
    
    toast.showError(t('shippingConfig.loadError'));
  }
});
</script>

<style scoped>
.shipping-config-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.grid {
  display: grid;
}

.grid-cols-1 {
  grid-template-columns: repeat(1, minmax(0, 1fr));
}

.gap-6 {
  gap: 24px;
}

@media (min-width: 1024px) {
  .lg\:grid-cols-2 {
    grid-template-columns: repeat(2, minmax(0, 1fr));
  }
}

/* Responsive */
@media (max-width: 768px) {
  .shipping-config-container {
    padding: 16px;
  }
  

  
  .form-container {
    gap: 20px;
  }
}

@media (max-width: 640px) {

}

@media (max-width: 480px) {
  .shipping-config-container {
    padding: 12px;
  }
  

  
  .form-container {
    gap: 16px;
  }
}

:deep(.p-form-invalid) {
  border-color: var(--iluria-color-error) !important;
}

:deep(.p-form-error) {
  color: var(--iluria-color-error);
  font-size: 12px;
  margin-top: 4px;
}
</style>
