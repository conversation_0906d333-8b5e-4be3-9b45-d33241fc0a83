<template>
  <IluriaModal
    :modelValue="true"
    :title="$t('emailSettings.confirmTitle')"
    @update:modelValue="$emit('cancel')"
  >
    <div class="verification-content">
      <div class="verification-info">
        <p class="info-text">
          {{ $t('emailSettings.confirmInfoText', { email: newEmail }) }}
        </p>
        
        <div class="email-info">
          <div class="email-row">
            <span class="email-label">{{ $t('emailSettings.oldEmailLabel') }}:</span>
            <span class="email-value">{{ oldEmail }}</span>
          </div>
          <div class="email-row">
            <span class="email-label">{{ $t('emailSettings.newEmailLabel') }}:</span>
            <span class="email-value">{{ newEmail }}</span>
          </div>
        </div>
      </div>

      <form @submit.prevent="handleSubmit" class="verification-form">
        <div class="form-field">
          <label class="code-label">{{ $t('emailSettings.codeLabel') }}</label>
          <div class="code-inputs">
            <input
              v-for="(digit, index) in 6"
              :key="index"
              ref="codeInputs"
              v-model="verificationDigits[index]"
              type="text"
              maxlength="1"
              class="code-digit"
              @input="handleDigitInput(index)"
              @keydown="handleKeyDown(index, $event)"
              @paste="handlePaste"
              autocomplete="off"
              inputmode="numeric"
              pattern="[0-9]"
            />
          </div>
        </div>
      </form>

      <div class="resend-section">
        <p class="resend-text">
          {{ $t('emailSettings.resendText') }}
        </p>
        <IluriaButton
          variant="ghost"
          color="primary"
          size="small"
          @click="handleResend"
          :disabled="isVerifying || resendCooldown > 0"
        >
          {{ resendCooldown > 0 
            ? $t('emailSettings.resendCooldown', { seconds: resendCooldown })
            : $t('emailSettings.resendButton') 
          }}
        </IluriaButton>
      </div>

      <div class="warning-section">
        <HugeiconsIcon
          :icon="Alert02Icon"
          size="16"
          :strokeWidth="1.5"
          class="warning-icon"
        />
        <p class="warning-text">
          {{ $t('emailSettings.warningText') }}
        </p>
      </div>
    </div>
    
    <template #footer>
      <div class="modal-footer">
        <IluriaButton
          @click="handleSubmit"
          variant="solid"
          color="primary"
          :disabled="!canSubmit"
          :loading="isVerifying"
          :hugeIcon="CheckmarkCircle02Icon"
        >
          {{ $t('emailSettings.verifyButton') }}
        </IluriaButton>
      </div>
    </template>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import { CheckmarkCircle02Icon, Alert02Icon } from '@hugeicons-pro/core-stroke-standard'

// Props
const props = defineProps({
  oldEmail: {
    type: String,
    required: true
  },
  newEmail: {
    type: String,
    required: true
  },
  changeToken: {
    type: String,
    required: true
  },
  isVerifying: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['verified', 'cancel', 'resend'])

// Composables
const { t } = useI18n()

// State
const verificationCode = ref('')
const verificationDigits = ref(Array(6).fill(''))
const codeInputs = ref([])
const resendCooldown = ref(0)
let cooldownInterval = null

// Computed
const canSubmit = computed(() => {
  return verificationCode.value.length === 6 && !props.isVerifying
})

// Methods
const handleDigitInput = (index) => {
  // Permitir apenas números
  const value = verificationDigits.value[index]
  if (value && !/^\d$/.test(value)) {
    verificationDigits.value[index] = ''
    return
  }
  
  // Atualizar código completo
  updateVerificationCode()
  
  // Mover para próximo campo se digitou
  if (value && index < 5) {
    codeInputs.value[index + 1]?.focus()
  }
}

const handleKeyDown = (index, event) => {
  // Backspace - mover para campo anterior se vazio
  if (event.key === 'Backspace' && !verificationDigits.value[index] && index > 0) {
    event.preventDefault()
    codeInputs.value[index - 1]?.focus()
  }
  
  // Setas para navegação
  if (event.key === 'ArrowLeft' && index > 0) {
    event.preventDefault()
    codeInputs.value[index - 1]?.focus()
  }
  if (event.key === 'ArrowRight' && index < 5) {
    event.preventDefault()
    codeInputs.value[index + 1]?.focus()
  }
}

const handlePaste = (event) => {
  event.preventDefault()
  const pasteData = event.clipboardData.getData('text').trim()
  
  // Verificar se é um código de 6 dígitos
  if (/^\d{6}$/.test(pasteData)) {
    verificationDigits.value = pasteData.split('')
    updateVerificationCode()
    // Focar no último campo
    codeInputs.value[5]?.focus()
  }
}

const updateVerificationCode = () => {
  verificationCode.value = verificationDigits.value.join('')
}

const handleSubmit = () => {
  if (!canSubmit.value) return
  
  emit('verified', {
    verificationCode: verificationCode.value
  })
}

const handleResend = () => {
  if (resendCooldown.value > 0) return
  
  emit('resend')
  startResendCooldown()
}

const startResendCooldown = () => {
  resendCooldown.value = 60 // 60 segundos de cooldown
  
  cooldownInterval = setInterval(() => {
    resendCooldown.value--
    if (resendCooldown.value <= 0) {
      clearInterval(cooldownInterval)
      cooldownInterval = null
    }
  }, 1000)
}

// Lifecycle
onMounted(() => {
  // Focar no primeiro campo de código
  setTimeout(() => {
    codeInputs.value[0]?.focus()
  }, 100)
})

onUnmounted(() => {
  if (cooldownInterval) {
    clearInterval(cooldownInterval)
  }
})
</script>

<style scoped>
.verification-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.verification-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.info-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.5;
}

.email-info {
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.email-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  font-size: 14px;
}

.email-label {
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.email-value {
  color: var(--iluria-color-text-primary);
  font-family: monospace;
}

.verification-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.code-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  text-align: center;
  margin-bottom: 0.5rem;
}

.code-inputs {
  display: flex;
  justify-content: center;
  gap: 0.5rem;
}

.code-digit {
  width: 48px;
  height: 56px;
  text-align: center;
  font-size: 24px;
  font-weight: 600;
  border: 2px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-surface-primary);
  color: var(--iluria-color-text-primary);
  transition: all 0.2s ease;
  outline: none;
}

.code-digit:focus {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px rgba(var(--iluria-color-primary-rgb), 0.1);
}

.code-digit:hover {
  border-color: var(--iluria-color-primary-light);
}

/* Remove spin buttons do input number */
.code-digit::-webkit-inner-spin-button,
.code-digit::-webkit-outer-spin-button {
  -webkit-appearance: none;
  margin: 0;
}

.modal-footer {
  display: flex;
  justify-content: center;
  padding: 0;
}

.resend-section {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  padding: 1rem 0;
  border-top: 1px solid var(--iluria-color-border);
}

.resend-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
}

.warning-section {
  display: flex;
  align-items: flex-start;
  gap: 0.5rem;
  padding: 1rem;
  background: var(--iluria-color-warning-light);
  border: 1px solid var(--iluria-color-warning);
  border-radius: 8px;
}

.warning-icon {
  color: var(--iluria-color-warning);
  flex-shrink: 0;
  margin-top: 2px;
}

.warning-text {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  margin: 0;
}

/* Responsive */
@media (max-width: 640px) {
  .modal-footer button {
    width: 100%;
  }
  
  .code-digit {
    width: 40px;
    height: 48px;
    font-size: 20px;
  }
  
  .code-inputs {
    gap: 0.375rem;
  }
}
</style>