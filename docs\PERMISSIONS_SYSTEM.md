# Sistema de Permissões Frontend

Sistema completo de gerenciamento de permissões no frontend, integrado com o sistema de roles do backend.

## 🎯 Visão Geral

O sistema implementa controle de acesso granular baseado em permissões, garantindo que:
- ✅ **Rotas protegidas**: Usuários só acessam páginas que têm permissão
- ✅ **UI filtrada**: NavBar e componentes mostram apenas opções permitidas
- ✅ **Controle granular**: Elementos individuais podem ser protegidos
- ✅ **Segurança**: Validação no backend + proteção no frontend

## 📁 Estrutura do Sistema

```
src/
├── constants/
│   └── permissions.js              # Enum de permissões
├── composables/
│   └── usePermissions.js           # Lógica principal
├── components/
│   └── common/
│       └── PermissionGuard.vue     # Componente de proteção
├── directives/
│   └── permission.js               # Diretiva v-permission
├── components/errors/
│   └── NoPermission.vue           # Página 403
├── locales/
│   ├── pt-br/errors_pt.json
│   └── en/errors_en.json          # Traduções de erro
└── examples/
    └── PermissionSystemExample.vue # Exemplos de uso
```

## 🚀 Formas de Uso

### 1. Componente PermissionGuard

Protege seções inteiras da UI:

```vue
<template>
  <!-- Permissão única -->
  <PermissionGuard :required-permissions="['product.create']">
    <IluriaButton>Criar Produto</IluriaButton>
  </PermissionGuard>

  <!-- Múltiplas permissões (AND) -->
  <PermissionGuard :required-permissions="['product.edit', 'product.view']">
    <ProductEditForm />
  </PermissionGuard>

  <!-- Qualquer permissão (OR) -->
  <PermissionGuard :required-permissions="['order.view', 'customer.view']" any-permission>
    <DashboardStats />
  </PermissionGuard>

  <!-- Com fallback -->
  <PermissionGuard :required-permissions="['team.edit']" show-fallback>
    <TeamManagement />
    <template #fallback>
      <p>Você não tem permissão para gerenciar a equipe.</p>
    </template>
  </PermissionGuard>
</template>
```

### 2. Diretiva v-permission

Controla elementos individuais:

```vue
<template>
  <!-- Oculta elemento -->
  <button v-permission="'product.create'">Criar Produto</button>

  <!-- Desabilita elemento -->
  <button v-permission:disable="'product.delete'">Excluir</button>

  <!-- Operação OR -->
  <div v-permission:any="['order.view', 'customer.view']">Dashboard</div>

  <!-- Array de permissões (AND) -->
  <div v-permission="['product.edit', 'product.view']">Editar Produto</div>
</template>
```

### 3. Composable usePermissions

Lógica personalizada em JavaScript:

```vue
<script setup>
import { useGlobalPermissions } from '@/composables/usePermissions';
import { PERMISSIONS } from '@/constants/permissions';

const { hasPermission, hasAllPermissions, hasAnyPermission } = useGlobalPermissions();

// Verificações simples
const canCreateProduct = computed(() => hasPermission(PERMISSIONS.PRODUCT_CREATE));
const canManageOrders = computed(() => hasAnyPermission([
  PERMISSIONS.ORDER_VIEW,
  PERMISSIONS.ORDER_EDIT
]));

// Filtrar listas
const availableActions = computed(() => {
  return allActions.filter(action => hasPermission(action.requiredPermission));
});
</script>
```

### 4. Guards de Rota

Proteção automática no router:

```javascript
// Em router.js, adicione meta.requiredPermissions
{
  path: "products/new",
  component: NewProductView,
  meta: { requiredPermissions: [PERMISSIONS.PRODUCT_CREATE] }
}
```

## 🔧 Configuração

### 1. Definir Permissões das Rotas

```javascript
// router.js
import { PERMISSIONS } from '@/constants/permissions';

const routes = [
  {
    path: "products",
    component: ProductListView,
    meta: { requiredPermissions: [PERMISSIONS.PRODUCT_VIEW] }
  },
  {
    path: "products/new",
    component: NewProductView,
    meta: { requiredPermissions: [PERMISSIONS.PRODUCT_CREATE] }
  }
];
```

### 2. Configurar Items do Menu

```javascript
// NavBar.vue
const navigation = [
  {
    name: "Produtos",
    href: "/products",
    requiredPermissions: [PERMISSIONS.PRODUCT_VIEW]
  }
];
```

### 3. Backend - Endpoint de Permissões

O sistema espera um endpoint que retorne as permissões do usuário atual:

```
GET /api/v1/stores/{storeId}/users/current/permissions
```

Resposta esperada:
```json
[
  "product.create",
  "product.view", 
  "order.view"
]
```

## 📋 Lista de Permissões Disponíveis

### Loja
- `store.admin` - Administração completa
- `store.settings` - Configurações da loja
- `store.view` - Visualizar informações

### Produtos
- `product.create` - Criar produtos
- `product.edit` - Editar produtos
- `product.delete` - Excluir produtos
- `product.view` - Visualizar produtos
- `product.category.manage` - Gerenciar categorias

### Pedidos
- `order.view` - Visualizar pedidos
- `order.edit` - Editar pedidos
- `order.cancel` - Cancelar pedidos
- `order.export` - Exportar pedidos
- `order.status.change` - Alterar status

### Clientes
- `customer.view` - Visualizar clientes
- `customer.edit` - Editar clientes
- `customer.delete` - Excluir clientes
- `customer.export` - Exportar dados

### Equipe
- `team.view` - Visualizar equipe
- `team.invite` - Convidar membros
- `team.edit` - Editar equipe
- `team.remove` - Remover membros

### Financeiro
- `financial.view` - Visualizar dados
- `financial.reports` - Relatórios
- `financial.export` - Exportar dados

### Marketing
- `promotion.create` - Criar promoções
- `promotion.edit` - Editar promoções
- `promotion.delete` - Excluir promoções
- `promotion.view` - Visualizar promoções

### Layout
- `layout.edit` - Editar layout
- `layout.publish` - Publicar alterações
- `layout.template.manage` - Gerenciar templates

### Arquivos
- `file.upload` - Upload de arquivos
- `file.delete` - Excluir arquivos
- `file.manage` - Gerenciar arquivos

### Analytics
- `analytics.view` - Visualizar analytics
- `reports.generate` - Gerar relatórios
- `reports.export` - Exportar relatórios

### Pagamento
- `payment.settings` - Configurar pagamentos
- `payment.view` - Visualizar configurações

### Frete
- `shipping.settings` - Configurar frete
- `shipping.view` - Visualizar configurações

## 🔍 Como Funciona

### 1. Carregamento de Permissões
1. Usuário faz login e recebe JWT com `storeRoleId`
2. Sistema busca permissões do role na API
3. Permissões são armazenadas em cache reativo
4. UI é atualizada automaticamente

### 2. Validação de Rotas
1. Router intercepta navegação
2. Verifica `meta.requiredPermissions` da rota
3. Consulta permissões do usuário
4. Permite acesso ou redireciona para página 403

### 3. Filtragem de UI
1. NavBar filtra itens baseado em permissões
2. Componentes usam PermissionGuard
3. Diretivas ocultam/desabilitam elementos
4. UI sempre sincronizada com permissões

## 🛡️ Segurança

### Frontend (UX)
- ✅ UI responsiva às permissões
- ✅ Navegação filtrada
- ✅ Elementos protegidos
- ✅ Feedback claro ao usuário

### Backend (Segurança Real)
- ✅ Validação obrigatória com `@RequirePermission`
- ✅ Interceptor automático
- ✅ Retorno 403 com detalhes
- ✅ Consulta dinâmica ao banco

> **Importante**: O frontend só melhora a UX. A segurança real está no backend!

## 🧪 Testando o Sistema

1. **Acesse a página de exemplos**: `/examples/permission-system`
2. **Teste diferentes permissões**: Modifique as permissões do usuário no backend
3. **Verifique comportamentos**:
   - Itens do menu aparecem/desaparecem
   - Rotas redirecionam para 403
   - Botões ficam desabilitados
   - Seções são ocultadas

## 🔧 Manutenção

### Adicionar Nova Permissão
1. **Backend**: Adicionar no enum `Permission.java`
2. **Frontend**: Adicionar em `constants/permissions.js`
3. **Usar**: Aplicar nos componentes/rotas conforme necessário

### Debugging
- **Console**: Logs automáticos de carregamento de permissões
- **Vue DevTools**: Estado das permissões em tempo real
- **Network**: Verificar chamadas da API de permissões

### Performance
- **Cache**: Permissões em cache reativo
- **Lazy Loading**: Carregamento sob demanda
- **Fallback**: Graceful degradation em caso de erro