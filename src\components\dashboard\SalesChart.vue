<template>
  <div class="sales-chart" ref="chartContainer">
    <div v-if="loading" class="chart-loading">
      <div class="loading-skeleton"></div>
    </div>
    <div v-else class="chart-container">
      <div class="chart-header">
        <div class="chart-stats">
          <div class="stat-item">
            <span class="stat-label">{{ $t('dashboard.totalRevenue') }}</span>
            <span class="stat-value">R$ {{ totalRevenue }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ $t('dashboard.averageDaily') }}</span>
            <span class="stat-value">R$ {{ averageDaily }}</span>
          </div>
        </div>
      </div>
      
      <div class="chart-wrapper" ref="chartWrapper">
        <svg :width="chartDimensions.width" :height="chartDimensions.height" class="chart-svg">
          <!-- Grid lines -->
          <defs>
            <pattern id="grid" :width="gridSize" :height="gridSize" patternUnits="userSpaceOnUse">
              <path :d="`M ${gridSize} 0 L 0 0 0 ${gridSize}`" fill="none" stroke="#f3f4f6" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#grid)" />
          
          <!-- Chart line -->
          <path
            :d="chartPath"
            fill="none"
            stroke="#10b981"
            :stroke-width="lineWidth"
            stroke-linecap="round"
            stroke-linejoin="round"
          />
          
          <!-- Area under the line -->
          <path
            :d="areaPath"
            fill="url(#gradient)"
            opacity="0.3"
          />
          
          <!-- Gradient definition -->
          <defs>
            <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style="stop-color:#10b981;stop-opacity:0.8" />
              <stop offset="100%" style="stop-color:#10b981;stop-opacity:0.1" />
            </linearGradient>
          </defs>
          
          <!-- Data points -->
          <circle
            v-for="(point, index) in dataPoints"
            :key="index"
            :cx="point.x"
            :cy="point.y"
            :r="pointRadius"
            fill="#10b981"
            stroke="#ffffff"
            stroke-width="2"
            class="data-point"
            @mouseover="showTooltip($event, point)"
            @mouseleave="hideTooltip"
          />
        </svg>
        
        <!-- X-axis labels -->
        <div class="x-axis">
          <span
            v-for="(point, index) in dataPoints"
            :key="index"
            class="x-label"
            :class="{ 'x-label-small': isMobile }"
            :style="{ left: point.x + 'px' }"
          >
            {{ formatDate(point.date) }}
          </span>
        </div>
      </div>
      
      <!-- Tooltip -->
      <div
        v-if="tooltip.show"
        class="chart-tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div class="tooltip-content">
          <div class="tooltip-date">{{ tooltip.date }}</div>
          <div class="tooltip-value">R$ {{ tooltip.value }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()

// Refs para elementos DOM
const chartContainer = ref(null)
const chartWrapper = ref(null)

// Estado responsivo
const containerDimensions = ref({ width: 600, height: 300 })
const isMobile = ref(false)

// Dimensões calculadas
const chartDimensions = computed(() => {
  const minWidth = 300
  const minHeight = 200
  const maxWidth = 1200
  const maxHeight = 400
  
  let width = Math.max(minWidth, Math.min(maxWidth, containerDimensions.value.width))
  let height = Math.max(minHeight, Math.min(maxHeight, containerDimensions.value.height))
  
  // Ajustar proporção para mobile
  if (isMobile.value) {
    height = Math.min(height, 250)
  }
  
  return { width, height }
})

const padding = computed(() => {
  const base = { top: 20, right: 20, bottom: 40, left: 20 }
  
  if (isMobile.value) {
    return { top: 15, right: 15, bottom: 35, left: 15 }
  }
  
  return base
})

const gridSize = computed(() => isMobile.value ? 30 : 40)
const lineWidth = computed(() => isMobile.value ? 2 : 3)
const pointRadius = computed(() => isMobile.value ? 3 : 4)

const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  date: '',
  value: ''
})

const totalRevenue = computed(() => {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(props.data.reduce((sum, item) => sum + item.value, 0))
})

const averageDaily = computed(() => {
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  const average = total / props.data.length
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(average)
})

const dataPoints = computed(() => {
  if (!props.data.length) return []
  
  const maxValue = Math.max(...props.data.map(d => d.value))
  const minValue = Math.min(...props.data.map(d => d.value))
  const valueRange = maxValue - minValue || 1 // Evitar divisão por zero
  
  const chartInnerWidth = chartDimensions.value.width - padding.value.left - padding.value.right
  const chartInnerHeight = chartDimensions.value.height - padding.value.top - padding.value.bottom
  
  return props.data.map((item, index) => {
    const x = padding.value.left + (index / Math.max(1, props.data.length - 1)) * chartInnerWidth
    const y = padding.value.top + (1 - (item.value - minValue) / valueRange) * chartInnerHeight
    
    return {
      x,
      y,
      date: item.date,
      value: item.value,
      formattedValue: new Intl.NumberFormat('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      }).format(item.value)
    }
  })
})

const chartPath = computed(() => {
  if (!dataPoints.value.length) return ''
  
  const path = dataPoints.value.reduce((acc, point, index) => {
    const command = index === 0 ? 'M' : 'L'
    return acc + `${command} ${point.x} ${point.y} `
  }, '')
  
  return path.trim()
})

const areaPath = computed(() => {
  if (!dataPoints.value.length) return ''
  
  const linePath = chartPath.value
  const firstPoint = dataPoints.value[0]
  const lastPoint = dataPoints.value[dataPoints.value.length - 1]
  const bottomY = chartDimensions.value.height - padding.value.bottom
  
  return `${linePath} L ${lastPoint.x} ${bottomY} L ${firstPoint.x} ${bottomY} Z`
})

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })
}

const showTooltip = (event, point) => {
  const tooltipWidth = 110
  const tooltipHeight = 10
  
  // Usar as coordenadas do ponto do gráfico (já calculadas em coordenadas SVG)
  let x = point.x - tooltipWidth / 2
  let y = point.y - tooltipHeight - 10
  
  // Ajustar posição para não sair da tela horizontalmente
  const margin = 15
  if (x < margin) x = margin
  if (x + tooltipWidth > chartDimensions.value.width - margin) {
    x = chartDimensions.value.width - tooltipWidth - margin
  }
  
  // Ajustar posição vertical se não houver espaço acima
  if (y < margin) {
    y = point.y + 20 // Posicionar abaixo do ponto
  }
  
  tooltip.value = {
    show: true,
    x,
    y,
    date: formatDate(point.date),
    value: point.formattedValue
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}

const updateDimensions = () => {
  if (!chartContainer.value) return
  
  const rect = chartContainer.value.getBoundingClientRect()
  containerDimensions.value = {
    width: rect.width,
    height: rect.height || 300
  }
  
  isMobile.value = window.innerWidth < 768
}

let resizeObserver = null

onMounted(async () => {
  await nextTick()
  updateDimensions()
  
  // ResizeObserver para detectar mudanças de tamanho
  if (window.ResizeObserver && chartContainer.value) {
    resizeObserver = new ResizeObserver(updateDimensions)
    resizeObserver.observe(chartContainer.value)
  }
  
  // Fallback para window resize
  window.addEventListener('resize', updateDimensions)
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  window.removeEventListener('resize', updateDimensions)
})
</script>

<style scoped>
.sales-chart {
  height: 100%;
  width: 100%;
  position: relative;
  min-height: 200px;
}

.chart-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-skeleton {
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.chart-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.chart-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 1.125rem;
  color: #111827;
  font-weight: 600;
}

.chart-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  
  min-height: 200px;
}

.chart-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.data-point {
  cursor: pointer;
  transition: all 0.2s ease;
}

.data-point:hover {
  filter: drop-shadow(0 2px 4px rgba(16, 185, 129, 0.3));
}

.x-axis {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  height: 20px;
}

.x-label {
  position: absolute;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.x-label-small {
  font-size: 0.7rem;
}

.chart-tooltip {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

.tooltip-content {
  background: #111827;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  white-space: nowrap;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #111827;
}

.tooltip-date {
  font-weight: 500;
  margin-bottom: 2px;
}

.tooltip-value {
  font-weight: 600;
  color: #10b981;
}

@media (max-width: 768px) {
  .chart-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-value {
    font-size: 1rem;
  }
  
  .chart-header {
    margin-bottom: 15px;
  }
  
  .tooltip-content {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .chart-stats {
    gap: 12px;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
  
  .stat-value {
    font-size: 0.95rem;
  }
  
  .chart-header {
    margin-bottom: 12px;
  }
}
</style> 
