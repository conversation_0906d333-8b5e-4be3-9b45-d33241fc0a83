import axios from 'axios';
import { API_URLS } from '../config/api.config';

export const OrderService = {
  async listOrders(filter = '', page = 0, size = 10) {
    try {
      const response = await axios.get(`${API_URLS.ORDER_BASE_URL}`, {
        params: { filter, page, size }
      });
      
      return {
        content: response.data.content || [],
        totalPages: response.data.totalPages || 0,
        totalElements: response.data.totalElements || 0,
        number: response.data.number || 0,
        size: response.data.size || 10
      };
    } catch (error) {
      console.error('Erro ao listar pedidos:', error);
      throw error;
    }
  },

  async getOrder(orderId) {
    try {
      const response = await axios.get(`${API_URLS.ORDER_BASE_URL}/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter pedido:', error);
      throw error;
    }
  },

  async createOrder(orderData) {
    try {
      const response = await axios.post(`${API_URLS.ORDER_BASE_URL}`, orderData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar pedido:', error);
      throw error;
    }
  },

  async updateOrder(orderId, orderData) {
    try {
      const response = await axios.put(`${API_URLS.ORDER_BASE_URL}/${orderId}`, orderData);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar pedido:', error);
      throw error;
    }
  },

  async deleteOrder(orderId) {
    try {
      const response = await axios.delete(`${API_URLS.ORDER_BASE_URL}/${orderId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir pedido:', error);
      throw error;
    }
  },

  prepareOrderData(orderForm) {
    return {
      id: orderForm.id,
      orderNumber: orderForm.orderNumber,
      customerId: orderForm.customerId,
      shippingStatus: orderForm.shippingStatus,
      paymentStatus: orderForm.paymentStatus,
      productsValue: parseFloat(orderForm.productsValue) || 0,
      productsDiscountValue: parseFloat(orderForm.productsDiscountValue) || 0,
      productsDiscountDescription: orderForm.productsDiscountDescription || '',
      shippingType: orderForm.shippingType || '',
      shippingValue: parseFloat(orderForm.shippingValue) || 0,
      shippingDescription: orderForm.shippingDescription || '',
      shippingDiscount: parseFloat(orderForm.shippingDiscount) || 0,
      shippingDiscountDescription: orderForm.shippingDiscountDescription || '',
      trackingCode: orderForm.trackingCode || '',
      shippingExternalId: orderForm.shippingExternalId || '',
      shippingExternalProtocol: orderForm.shippingExternalProtocol || '',
      deliveryTime: parseInt(orderForm.deliveryTime) || 1,
      annotation: orderForm.annotation || '',
      addressStreet: orderForm.addressStreet || '',
      addressZipCode: orderForm.addressZipCode || '',
      addressNumber: orderForm.addressNumber || '',
      addressDistrict: orderForm.addressDistrict || '',
      addressCity: orderForm.addressCity || '',
      addressState: orderForm.addressState || '',
      addressCountry: orderForm.addressCountry || 'Brasil',
      addressComplement: orderForm.addressComplement || '',
      customerName: orderForm.customerName || '',
      customerPhone: orderForm.customerPhone || '',
      customerEmail: orderForm.customerEmail || '',
      customerDocument: orderForm.customerDocument || '',
      receiverName: orderForm.receiverName || '',
      sourceDevice: orderForm.sourceDevice || 'WEB',
      items: (orderForm.items || []).map(item => ({
        id: item.id,
        productId: item.productId,
        name: item.name,
        sku: item.sku || '',
        price: parseFloat(item.price) || 0,
        originalPrice: parseFloat(item.originalPrice) || parseFloat(item.price) || 0,
        quantity: parseInt(item.quantity) || 1,
        variationTitle: item.variationTitle || '',
        variationId: item.variationId === undefined ? '' : item.variationId,
        shippingTime: parseInt(item.shippingTime) || 0,
        productType: item.productType || 'PHYSICAL'
      }))
    };
  },
};

export default OrderService;