import axios from 'axios';
import { API_URLS } from '../config/api.config';
import deviceDetection from '@/utils/deviceDetection';
import geolocationService from '@/services/geolocation.service';

class UserSessionService {
    #sessionsEndpoint = API_URLS.USER_SESSIONS_BASE_URL;
    #currentDeviceFingerprint = null;

    /**
     * Obtém o device fingerprint atual (com cache)
     * @returns {string}
     */
    getCurrentDeviceFingerprint() {
        if (!this.#currentDeviceFingerprint) {
            this.#currentDeviceFingerprint = deviceDetection.generateDeviceFingerprint();
        }
        return this.#currentDeviceFingerprint;
    }

    /**
     * Sanitiza valor de header para evitar caracteres problemáticos
     * @private
     * @param {any} value
     * @param {boolean} isFingerprint - Se true, usa sanitização mais permissiva para fingerprints
     * @returns {string}
     */
    #sanitizeHeaderValue(value, isFingerprint = false) {
        if (!value) return '';
        
        const stringValue = String(value);
        
        if (isFingerprint) {
            // Para fingerprints, apenas remove caracteres de controle e quebras de linha
            // Mantém caracteres especiais que podem fazer parte do hash
            return stringValue
                .replace(/[\r\n\t]/g, '') // Remove quebras de linha e tabs
                .replace(/[\x00-\x1F\x7F-\x9F]/g, '') // Remove caracteres de controle
                .trim();
        } else {
            // Para outros valores, remove caracteres não-ASCII
            return stringValue
                .replace(/[^\x20-\x7E]/g, '') // Remove caracteres fora do range ASCII printável
                .trim();
        }
    }

    /**
     * Gera headers de sessão para requisições
     * @private
     * @returns {Object}
     */
    #getSessionHeaders() {
        const deviceFingerprint = this.getCurrentDeviceFingerprint();
        const sessionInfo = deviceDetection.getSessionIdentificationInfo();
        
        
        return {
            'X-Device-Fingerprint': this.#sanitizeHeaderValue(deviceFingerprint, true),
            'X-Device-Info': this.#sanitizeHeaderValue(sessionInfo.deviceInfo),
            'X-User-Agent': this.#sanitizeHeaderValue(sessionInfo.userAgent),
            'X-Screen-Resolution': this.#sanitizeHeaderValue(sessionInfo.screenResolution),
            'X-Timezone': this.#sanitizeHeaderValue(sessionInfo.timezone),
            'X-Request-Timestamp': this.#sanitizeHeaderValue(sessionInfo.timestamp)
        };
    }

    /**
     * Obtém todas as sessões ativas do usuário atual
     * @returns {Promise<Array>} Lista de sessões ativas
     */
    async getActiveSessions() {
        try {
            const headers = this.#getSessionHeaders();
            const response = await axios.get(`${this.#sessionsEndpoint}/active`, { headers });

            // Marcar sessão atual baseada no device fingerprint
            const sessions = response.data || [];
            const currentFingerprint = this.getCurrentDeviceFingerprint();

            const sessionsWithCurrent = sessions.map(session => ({
                ...session,
                isCurrent: session.deviceFingerprint === currentFingerprint
            }));

            // Enriquece com dados de geolocalização
            const enrichedSessions = await this.enrichSessionsWithGeolocation(sessionsWithCurrent);

            return enrichedSessions;
        } catch (error) {
            console.error('Erro ao buscar sessões ativas:', error);
            throw this.#handleApiError(error);
        }
    }

    /**
     * Termina uma sessão específica
     * @param {string} sessionId - ID da sessão a ser terminada
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async terminateSession(sessionId) {
        try {
            const headers = this.#getSessionHeaders();
            await axios.delete(`${this.#sessionsEndpoint}/${sessionId}`, { headers });
            return true;
        } catch (error) {
            console.error('Erro ao terminar sessão:', error);
            throw this.#handleApiError(error);
        }
    }

    /**
     * Termina todas as outras sessões (mantém apenas a atual)
     * @returns {Promise<boolean>} Sucesso da operação
     */
    async terminateAllOtherSessions() {
        try {
            const headers = this.#getSessionHeaders();
            await axios.delete(`${this.#sessionsEndpoint}/terminate-others`, { headers });
            return true;
        } catch (error) {
            console.error('Erro ao terminar outras sessões:', error);
            throw this.#handleApiError(error);
        }
    }

    /**
     * Atualiza a atividade da sessão atual
     * @returns {Promise<Object>} Resultado da operação com validação de sessão
     */
    async updateSessionActivity() {
        try {
            const headers = this.#getSessionHeaders();
            const response = await axios.post(`${this.#sessionsEndpoint}/activity`, {}, { headers });
            
            // Retorna informações sobre validade da sessão
            return {
                success: true,
                sessionValid: response.data?.sessionValid !== false,
                lastActivity: response.data?.lastActivity || new Date().toISOString()
            };
        } catch (error) {
            console.error('Erro ao atualizar atividade da sessão:', error);
            
            // Se erro 401, sessão foi invalidada remotamente
            if (error.response?.status === 401) {
                return {
                    success: false,
                    sessionValid: false,
                    error: 'Sessão invalidada remotamente'
                };
            }
            
            // Para outros erros, assume que sessão ainda é válida
            return {
                success: false,
                sessionValid: true,
                error: error.message
            };
        }
    }

    /**
     * Obtém informações do dispositivo atual
     * @returns {Object} Informações completas do dispositivo e navegador
     */
    getDeviceInfo() {
        return deviceDetection.getDeviceInfo();
    }

    /**
     * Obtém informações de localização baseada em timezone
     * @returns {Object} Informações de localização
     */
    getLocationInfo() {
        return deviceDetection.getLocationInfo();
    }

    /**
     * Obtém informações completas de identificação de sessão
     * @returns {Object} Informações completas para identificação
     */
    getSessionIdentificationInfo() {
        return deviceDetection.getSessionIdentificationInfo();
    }

    /**
     * Verifica se uma sessão específica é a sessão atual
     * @param {Object} session - Dados da sessão
     * @returns {boolean}
     */
    isCurrentSession(session) {
        const currentFingerprint = this.getCurrentDeviceFingerprint();
        return session.deviceFingerprint === currentFingerprint;
    }

    /**
     * Verifica se o dispositivo atual pode terminar uma sessão
     * (previne que um usuário termine a própria sessão atual acidentalmente)
     * @param {string} sessionId - ID da sessão
     * @param {Object} sessionData - Dados da sessão (opcional)
     * @returns {boolean}
     */
    canTerminateSession(sessionId, sessionData = null) {
        // Se temos dados da sessão, verifica se é sessão atual
        if (sessionData) {
            return !this.isCurrentSession(sessionData);
        }
        
        // Se não tem dados, permite (validação será feita no backend)
        return true;
    }

    /**
     * Formata informações da sessão para exibição consistente
     * @param {Object} session - Dados da sessão
     * @returns {Object} Sessão formatada
     */
    formatSessionForDisplay(session) {
        const deviceInfo = this.#parseDeviceInfo(session.deviceInfo || session.device);
        const browserInfo = this.#parseBrowserInfo(session.browserInfo || session.browser);

        // Prioriza locationInfo (dados enriquecidos) sobre location (dados brutos)
        const locationData = session.locationInfo || session.location;
        const locationInfo = this.#parseLocationInfo(locationData);

        return {
            ...session,
            isCurrent: this.isCurrentSession(session),
            deviceIcon: this.#getDeviceIcon(deviceInfo),
            locationDisplay: locationInfo.display,
            lastActivityFormatted: this.formatRelativeTime(session.lastActivity),
            browserDisplay: browserInfo.display,
            deviceDisplay: deviceInfo.display,
            isMobile: deviceInfo.isMobile,
            isDesktop: deviceInfo.isDesktop,
            isTablet: deviceInfo.isTablet
        };
    }

    /**
     * Enriquece sessões com dados de geolocalização
     * @param {Array} sessions - Lista de sessões
     * @returns {Promise<Array>} Sessões enriquecidas com geolocalização
     */
    async enrichSessionsWithGeolocation(sessions) {
        const enrichedSessions = await Promise.all(
            sessions.map(async (session) => {
                try {
                    // Se já tem locationInfo detalhado, não precisa buscar
                    if (session.locationInfo && typeof session.locationInfo === 'object' && session.locationInfo.country) {
                        return session;
                    }

                    // Extrai IP da sessão
                    const ip = this.#extractIPFromSession(session);
                    if (!ip) {
                        return session;
                    }

                    // Busca dados de geolocalização
                    const geoData = await geolocationService.getLocationByIP(ip);

                    // Atualiza a sessão com dados de geolocalização
                    return {
                        ...session,
                        locationInfo: geoData,
                        ipAddress: ip
                    };
                } catch (error) {
                    console.warn(`Erro ao obter geolocalização para sessão ${session.sessionId}:`, error.message);
                    return session;
                }
            })
        );

        return enrichedSessions;
    }

    /**
     * Extrai IP de uma sessão
     * @private
     * @param {Object} session
     * @returns {string|null}
     */
    #extractIPFromSession(session) {
        // Tenta várias propriedades onde o IP pode estar
        return session.ipAddress ||
               session.ip ||
               session.clientIP ||
               session.remoteAddress ||
               (session.locationInfo && typeof session.locationInfo === 'string' ? session.locationInfo : null);
    }

    /**
     * Faz parsing das informações do dispositivo
     * @private
     * @param {string|Object} deviceInfo - Informações do dispositivo
     * @returns {Object} Informações formatadas do dispositivo
     */
    #parseDeviceInfo(deviceInfo) {
        let deviceString = '';
        let deviceData = {};

        // Se é um JSON string, tenta fazer parse
        if (typeof deviceInfo === 'string') {
            try {
                if (deviceInfo.startsWith('{') || deviceInfo.startsWith('[')) {
                    deviceData = JSON.parse(deviceInfo);
                    deviceString = [
                        deviceData.device || '',
                        deviceData.userAgent || '',
                        deviceData.platform || '',
                        deviceData.os || ''
                    ].join(' ').toLowerCase();
                } else {
                    deviceString = deviceInfo.toLowerCase();
                }
            } catch (e) {
                deviceString = deviceInfo.toLowerCase();
            }
        } else if (typeof deviceInfo === 'object' && deviceInfo !== null) {
            deviceData = deviceInfo;
            deviceString = [
                deviceData.device || '',
                deviceData.userAgent || '',
                deviceData.platform || '',
                deviceData.os || ''
            ].join(' ').toLowerCase();
        }

        const isMobile = deviceString.includes('iphone') ||
                        deviceString.includes('android') ||
                        deviceString.includes('mobile') ||
                        deviceString.includes('phone');

        const isTablet = deviceString.includes('ipad') ||
                        deviceString.includes('tablet');

        const isDesktop = !isMobile && !isTablet;

        // Gera display amigável
        let display = 'Dispositivo desconhecido';
        if (deviceData.device && deviceData.device !== 'Desktop') {
            display = deviceData.device;
        } else if (deviceData.platform) {
            display = deviceData.platform;
        } else if (deviceString) {
            // Extrai informações básicas do user agent
            if (deviceString.includes('windows')) display = 'Windows PC';
            else if (deviceString.includes('mac')) display = 'Mac';
            else if (deviceString.includes('linux')) display = 'Linux';
            else if (deviceString.includes('android')) display = 'Android';
            else if (deviceString.includes('iphone')) display = 'iPhone';
            else if (deviceString.includes('ipad')) display = 'iPad';
            else if (isDesktop) display = 'Desktop';
        }

        return {
            raw: deviceInfo,
            display,
            isMobile,
            isTablet,
            isDesktop,
            deviceString
        };
    }

    /**
     * Faz parsing das informações do navegador
     * @private
     * @param {string|Object} browserInfo - Informações do navegador
     * @returns {Object} Informações formatadas do navegador
     */
    #parseBrowserInfo(browserInfo) {
        let browserData = {};
        let browserString = '';

        if (typeof browserInfo === 'string') {
            try {
                if (browserInfo.startsWith('{') || browserInfo.startsWith('[')) {
                    browserData = JSON.parse(browserInfo);
                    browserString = [
                        browserData.name || '',
                        browserData.version || '',
                        browserData.userAgent || ''
                    ].join(' ').toLowerCase();
                } else {
                    browserString = browserInfo.toLowerCase();
                }
            } catch (e) {
                browserString = browserInfo.toLowerCase();
            }
        } else if (typeof browserInfo === 'object' && browserInfo !== null) {
            browserData = browserInfo;
            browserString = [
                browserData.name || '',
                browserData.version || '',
                browserData.userAgent || ''
            ].join(' ').toLowerCase();
        }

        // Gera display amigável
        let display = 'Navegador desconhecido';
        if (browserData.name) {
            display = browserData.name;
            if (browserData.version) {
                display += ` ${browserData.version}`;
            }
        } else if (browserString) {
            // Detecta navegador pelo user agent
            if (browserString.includes('chrome')) display = 'Chrome';
            else if (browserString.includes('firefox')) display = 'Firefox';
            else if (browserString.includes('safari')) display = 'Safari';
            else if (browserString.includes('edge')) display = 'Edge';
            else if (browserString.includes('opera')) display = 'Opera';
        }

        return {
            raw: browserInfo,
            display
        };
    }

    /**
     * Faz parsing das informações de localização
     * @private
     * @param {string|Object} locationInfo - Informações de localização
     * @returns {Object} Informações formatadas de localização
     */
    #parseLocationInfo(locationInfo) {
        let locationData = {};

        if (typeof locationInfo === 'string') {
            try {
                if (locationInfo.startsWith('{') || locationInfo.startsWith('[')) {
                    locationData = JSON.parse(locationInfo);
                } else {
                    return { raw: locationInfo, display: locationInfo || 'Localização desconhecida' };
                }
            } catch (e) {
                return { raw: locationInfo, display: locationInfo || 'Localização desconhecida' };
            }
        } else if (typeof locationInfo === 'object' && locationInfo !== null) {
            locationData = locationInfo;
        }

        // Gera display amigável
        let display = 'Localização desconhecida';

        // Se já tem um display formatado (ex: do serviço de geolocalização)
        if (locationData.display) {
            display = locationData.display;
        } else if (locationData.city && locationData.country) {
            // Para dados do ipinfo.io, formata como "Cidade, Região, País"
            if (locationData.region && locationData.region !== locationData.city) {
                display = `${locationData.city}, ${locationData.region}, ${locationData.country}`;
            } else {
                display = `${locationData.city}, ${locationData.country}`;
            }
        } else if (locationData.country) {
            display = locationData.country;
        } else if (locationData.city) {
            display = locationData.city;
        }

        return {
            raw: locationInfo,
            display
        };
    }

    /**
     * Determina o ícone apropriado para o tipo de dispositivo
     * @private
     * @param {Object} deviceInfo - Informações processadas do dispositivo
     * @returns {string} Nome do ícone
     */
    #getDeviceIcon(deviceInfo) {
        if (deviceInfo.isMobile) {
            return 'SmartPhone02Icon';
        }

        if (deviceInfo.isTablet) {
            return 'TabletIcon';
        }

        return 'ComputerIcon';
    }

    /**
     * Formata data/hora relativa para exibição
     * @param {string|Date} date - Data a ser formatada
     * @returns {string} Data formatada
     */
    formatRelativeTime(date) {
        const now = new Date();
        const targetDate = new Date(date);
        const diffMs = now - targetDate;
        
        const minutes = Math.floor(diffMs / (1000 * 60));
        const hours = Math.floor(diffMs / (1000 * 60 * 60));
        const days = Math.floor(diffMs / (1000 * 60 * 60 * 24));

        if (minutes < 1) return 'Agora mesmo';
        if (minutes < 60) return `${minutes} min atrás`;
        if (hours < 24) return `${hours}h atrás`;
        return `${days} dias atrás`;
    }


    /**
     * Trata erros da API
     * @private
     */
    #handleApiError(error) {
        if (error.response) {
            const { status, data } = error.response;
            
            switch (status) {
                case 401:
                    return new Error('Sessão expirada. Faça login novamente.');
                case 403:
                    return new Error('Você não tem permissão para realizar esta ação.');
                case 404:
                    return new Error('Sessão não encontrada.');
                case 500:
                    return new Error('Erro interno do servidor. Tente novamente.');
                default:
                    return new Error(data?.message || `Erro ${status}: ${error.message}`);
            }
        }
        
        if (error.request) {
            return new Error('Erro de conexão. Verifique sua internet.');
        }
        
        return new Error(error.message || 'Erro desconhecido');
    }
}

export default new UserSessionService();