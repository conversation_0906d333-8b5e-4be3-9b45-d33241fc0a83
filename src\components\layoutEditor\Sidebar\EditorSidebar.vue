<template>
  <!-- Sidebar principal -->
  <div 
    class="editor-sidebar"
    :class="{ 
      'is-visible': isVisible, 
      'is-hidden': isHidden,
      'is-hovering': isHovering 
    }"
    :style="{ 
      display: isVisible ? 'flex' : 'none',
      width: `${sidebarWidth}px`,
      transform: isHidden ? `translateX(-${sidebarWidth - visibleBorderWidth}px)` : 'translateX(0)'
    }"
    @mouseenter="handleSidebarEnter"
    @mouseleave="handleSidebarLeave"
  >
    <!-- Header da Sidebar -->
    <div class="sidebar-header">
      <!-- Bot<PERSON> de voltar (só aparece quando não está na tela inicial) -->
      <button 
        v-if="currentView !== 'initial'"
        class="back-button"
        @click="goToInitial"
        :title="$t('layoutEditor.backToMenu')"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 12H5M12 19l-7-7 7-7"/>
        </svg>
      </button>
      
      <!-- Título dinâmico -->
      <div class="header-title">
        <h3 class="sidebar-title">{{ currentTitle }}</h3>
        <p v-if="currentSubtitle" class="sidebar-subtitle">{{ currentSubtitle }}</p>
      </div>
      
      <!-- Botão de Fechar -->
      <button 
        class="close-button" 
        @click="hideSidebar"
        :title="$t('layoutEditor.sidebar.closeSidebar')"
      >
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M18 6L6 18M6 6l12 12"/>
        </svg>
      </button>
    </div>

    <!-- Conteúdo da Sidebar -->
    <div class="sidebar-content">
      <!-- View: Página Inicial -->
      <SidebarInitial 
        v-if="currentView === 'initial'"
        :iframeDocument="iframeDocument"
        @navigate-to="navigateTo"
        @component-select="handleComponentSelect"
        @section-select="handleSectionSelect"
        @section-highlight="handleSectionHighlight"
        @page-settings-updated="handlePageSettingsUpdated"
      />

      <!-- View: Menu Principal (Home) -->
      <SidebarHome 
        v-if="currentView === 'home'"
        ref="sidebarHomeRef"
        :iframeDocument="iframeDocument"
        @navigate-to="navigateTo"
        @component-select="handleComponentSelect"
        @section-select="handleSectionSelect"
        @section-highlight="handleSectionHighlight"
        @section-deleted="handleSectionDeleted"
        @section-will-delete="handleSectionWillDelete"
      />

      <!-- View: Editor Específico -->
      <SidebarEditor 
        v-else-if="currentView === 'editor'"
        :selectedElement="selectedElement"
        :editorType="currentEditorType"
        @element-updated="handleElementUpdated"
        @back="goToHome"
      />

      <!-- View: Configurações da Página -->
      <SidebarPageSettings
        v-else-if="currentView === 'page-settings'"
        @settings-updated="handlePageSettingsUpdated"
        @back="goToHome"
      />

      <!-- View: Adicionar Componente -->
      <SidebarAddComponent
        v-else-if="currentView === 'add-component'"
        :layoutType="layoutType"
        @component-select="handleComponentSelect"
        @back="goToHome"
      />
    </div>
  </div>
</template>

<script setup>
import { computed, watch, ref } from 'vue'
import { useI18n } from 'vue-i18n'
import SidebarInitial from './views/SidebarInitial.vue'
import SidebarHome from './views/SidebarHome.vue'
import SidebarEditor from './views/SidebarEditor.vue'
import SidebarPageSettings from './views/SidebarPageSettings.vue'
import SidebarAddComponent from './views/SidebarAddComponent.vue'

const { t } = useI18n()

const props = defineProps({
  selectedElement: {
    type: Object,
    default: null
  },
  isVisible: {
    type: Boolean,
    default: false
  },
  isHidden: {
    type: Boolean,
    default: false
  },
  activeTab: {
    type: String,
    default: 'home'
  },
  sidebarWidth: {
    type: Number,
    default: 400
  },
  iframeDocument: {
    type: Object,
    default: null
  },
  layoutType: {
    type: String,
    default: null
  }
})

// Usar estado controlado pelo pai em vez de estado interno
const isHovering = ref(false)

// Computed para acessar props no template
const isHidden = computed(() => props.isHidden)

// Largura da bordinha visível quando escondida
const visibleBorderWidth = 8

const emit = defineEmits(['tab-change', 'close', 'element-updated', 'component-select', 'page-settings-updated', 'section-select', 'section-highlight', 'section-deleted', 'section-will-delete', 'sidebar-shown', 'sidebar-hidden'])

// Estado da navegação
const currentView = ref('initial') // 'initial', 'home', 'editor', 'page-settings', 'add-component'
const currentEditorType = ref(null)

// Títulos dinâmicos baseados na view atual
const currentTitle = computed(() => {
  switch (currentView.value) {
    case 'initial':
      return t('layoutEditor.sidebar.layoutEditor')
    case 'home':
      return t('layoutEditor.sidebar.pageSections')
    case 'editor':  
      return getEditorTitle(currentEditorType.value)
    case 'page-settings':
      return t('layoutEditor.sidebar.pageConfiguration')
    case 'add-component':
      return t('layoutEditor.sidebar.addComponent')
    default:
      return t('layoutEditor.sidebar.layoutEditor')
  }
})

const currentSubtitle = computed(() => {
  switch (currentView.value) {
    case 'initial':
      return 'Home'
    case 'home':
      return 'Clique em uma seção para personalizá-la'
    case 'editor':
      return getEditorSubtitle(currentEditorType.value)
    case 'page-settings':
      return t('layoutEditor.sidebar.pageConfigurationDesc')
    case 'add-component':
      return 'Escolha um componente para adicionar à página'
    default:
      return null
  }
})

// Detectar tipo de editor baseado no elemento selecionado
watch(() => props.selectedElement, (newElement) => {

  
  if (newElement && currentView.value !== 'home') {
    // Detectar qual editor deve ser usado
    currentEditorType.value = detectEditorType(newElement)

  } else if (!newElement && currentView.value === 'editor') {
    // Se elemento foi desmarcado e estamos no editor, voltar ao home
    goToHome()
  }
}, { immediate: true })

// Detecta qual tipo de editor usar baseado no elemento
const detectEditorType = (element) => {
  if (!element) return null
  

  
  // Verificar data-component primeiro
  const dataComponent = element.getAttribute('data-component')
  if (dataComponent) {
    const editorType = `${dataComponent}-config`

    return editorType
  }
  
  // Verificar data-element-type
  const elementType = element.getAttribute('data-element-type')
  if (elementType) {
    const editorType = `${elementType}-config`

    return editorType
  }
  
  // Fallback baseado na tag
  const tagName = element.tagName.toLowerCase()
  const typeMap = {
    'img': 'image',
    'a': 'link',
    'h1': 'text', 'h2': 'text', 'h3': 'text', 'h4': 'text', 'h5': 'text', 'h6': 'text',
    'p': 'text', 'span': 'text'
  }
  
  const fallbackType = typeMap[tagName] || 'spacing'

  return fallbackType
}

// Navegação
const navigateTo = (destination, data = {}) => {
  
  switch (destination) {
    case 'editor':
      currentView.value = 'editor'
      if (data.editorType) {
        currentEditorType.value = data.editorType
      }
      break
      
    case 'page-settings':
      currentView.value = 'page-settings'
      break
      
    case 'add-component':
      currentView.value = 'add-component'
      break
      
    case 'home':
      currentView.value = 'home'
      break
      
    case 'initial':
    default:
      goToInitial()
      break
  }
}

const goToInitial = () => {
  currentView.value = 'initial'
  currentEditorType.value = null
  emit('tab-change', 'initial')
}

const goToHome = () => {
  currentView.value = 'home'
  currentEditorType.value = null
  emit('tab-change', 'home')
}

// Funções de controle da sidebar escondida
const hideSidebar = () => {
  emit('sidebar-hidden')
}

const showSidebar = () => {
  emit('sidebar-shown')
}

const handleSidebarEnter = () => {
  if (props.isHidden) {
    // Quando hover na sidebar escondida, abrir permanentemente
    showSidebar()
  }
}

const handleSidebarLeave = () => {
  // Não fazer nada quando sair da sidebar
  // Ela só fecha com o botão fechar
}

// Handlers
const handleComponentSelect = (componentData) => {
  
  emit('component-select', componentData)
}

const handleElementUpdated = (data) => {
  
  emit('element-updated', data)
}

const handlePageSettingsUpdated = (settings) => {
  
  emit('page-settings-updated', settings)
}

const handleSectionSelect = (section) => {
  
  emit('section-select', section)
}

const handleSectionHighlight = (data) => {
      
  emit('section-highlight', data)
}

const handleSectionDeleted = (section) => {
  
  emit('section-deleted', section)
}

const handleSectionWillDelete = (section) => {
  
  emit('section-will-delete', section)
}

// Funções auxiliares para títulos
const getEditorTitle = (editorType) => {
  const titleMap = {
    'header-config': t('layoutEditor.sidebar.headerEditor'),
    'footer-config': t('layoutEditor.sidebar.footerEditor'),
    'carousel-config': t('layoutEditor.sidebar.carouselEditor'),
    'video-config': t('layoutEditor.sidebar.videoEditor'),
    'company-information-config': t('layoutEditor.sidebar.companyInfoEditor'),
    'location-config': t('layoutEditor.sidebar.locationEditor'),
    'statement-config': t('layoutEditor.sidebar.statementEditor'),
    'payment-benefits-config': t('layoutEditor.sidebar.paymentBenefitsEditor'),
    'customer-review-config': t('layoutEditor.sidebar.customerReviewEditor'),
    'special-offers-config': t('layoutEditor.sidebar.specialOffersEditor'),
    'product-selection': t('layoutEditor.sidebar.productSelection'),
    'product-style': t('layoutEditor.sidebar.productStyle'),
    'spacing': t('layoutEditor.sidebar.spacing'),
    'border': t('layoutEditor.sidebar.border'),
    'animation': t('layoutEditor.sidebar.animation'),
    'transform': t('layoutEditor.sidebar.transform'),
    'filter': t('layoutEditor.sidebar.filter'),
    'text': t('layoutEditor.sidebar.text'),
    'image': t('layoutEditor.sidebar.image'),
    'link': t('layoutEditor.sidebar.link')
  }
  
  return titleMap[editorType] || t('layoutEditor.sidebar.elementEditor')
}

const getEditorSubtitle = (editorType) => {
  if (!editorType) return null
  
  if (props.selectedElement) {
    const elementType = props.selectedElement.tagName.toLowerCase()
    const dataComponent = props.selectedElement.getAttribute('data-component')
    
    if (dataComponent) {
      return t('layoutEditor.sidebar.editingComponent', { component: dataComponent })
    }
    
    return t('layoutEditor.sidebar.editingElement', { element: elementType })
  }
  
  return t('layoutEditor.sidebar.customizeProperties')
}

// Observar mudanças na propriedade activeTab (para compatibilidade externa)
watch(() => props.activeTab, (newTab) => {
  if (newTab === 'properties' && props.selectedElement) {
    navigateTo('editor')
  } else if (newTab === 'page') {
    navigateTo('page-settings')
  } else if (newTab === 'home') {
    navigateTo('home')
  } else {
    navigateTo('initial')
  }
})

// Quando um elemento é selecionado externamente, navegar para o editor
watch(() => props.selectedElement, (newElement) => {
  if (newElement && (currentView.value === 'home' || currentView.value === 'initial')) {
    navigateTo('editor')
    // Se sidebar estiver escondida, mostrar automaticamente
    if (props.isHidden) {
      showSidebar()
    }
  }
})

// Referências para os componentes filhos
const sidebarHomeRef = ref(null)

// Função para refresh das seções
const refreshSections = () => {
  if (sidebarHomeRef.value && sidebarHomeRef.value.refreshSections) {
    sidebarHomeRef.value.refreshSections()
  }
}

// Expor funções para controle externo
defineExpose({
  hideSidebar,
  showSidebar,
  refreshSections,
  isHidden: computed(() => props.isHidden),
  isHovering
})
</script>

<style scoped>
/* Sidebar principal */
.editor-sidebar {
  position: fixed;
  top: 64px; /* Altura do header */
  left: 0;
  bottom: 0;
  background: var(--iluria-color-surface);
  border-right: 1px solid var(--iluria-color-border);
  z-index: 1000;
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  display: flex;
  flex-direction: column;
  box-shadow: var(--iluria-shadow-lg);
  overflow: hidden;
}

.editor-sidebar.is-visible {
  transform: translateX(0);
}

.editor-sidebar.is-hidden {
  box-shadow: none;
  cursor: pointer;
}

.editor-sidebar.is-hidden::after {
  content: '';
  position: absolute;
  top: 50%;
  right: 2px;
  transform: translateY(-50%);
  width: 2px;
  height: 40px;
  background: var(--iluria-color-primary);
  border-radius: 1px;
  opacity: 0.6;
  transition: opacity 0.2s ease;
}

.editor-sidebar.is-hidden:hover::after {
  opacity: 1;
}

.editor-sidebar.is-hovering {
  box-shadow: var(--iluria-shadow-xl);
  z-index: 1001;
}

/* Header da Sidebar */
.sidebar-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  flex-shrink: 0;
  min-height: 80px;
}

.back-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: transparent;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.back-button:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
  border-color: var(--iluria-color-primary-border);
}

.header-title {
  flex: 1;
  min-width: 0;
}

.sidebar-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.sidebar-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
}

.close-button:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}



/* Conteúdo da Sidebar */
.sidebar-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Responsividade */
@media (max-width: 768px) {
  .editor-sidebar {
    width: 100vw !important;
    max-width: 350px;
  }
  
  .sidebar-header {
    padding: 0.75rem;
    min-height: 70px;
  }
  
  .sidebar-title {
    font-size: 1rem;
  }
  
  .sidebar-subtitle {
    font-size: 0.8rem;
  }
  
  .back-button {
    width: 32px;
    height: 32px;
  }
}

@media (max-width: 480px) {
  .editor-sidebar {
    max-width: 100vw;
  }
}
</style> 
