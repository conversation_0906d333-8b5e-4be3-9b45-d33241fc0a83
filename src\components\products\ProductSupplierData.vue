<template>
  <div class="space-y-4 p-4">
    <!-- Nome do fornecedor -->
    <div class="mb-4">
      <IluriaInputText
        id="supplierName"
        name="supplierName"
        :label="t('product.supplierName')"
        v-model="form.supplierName"
        class="w-full md:w-1/2"
      />
    </div>
    
    <!-- Link para o produto ou fornecedor -->
    <div class="mb-4">
      <IluriaInputText
        id="supplierLink"
        name="supplierLink"
        :label="t('product.supplierLink')"
        v-model="form.supplierLink"
        class="w-full md:w-1/2"
      />
    </div>
    
    <!-- Anotações -->
    <div class="mb-4">
      <div class="mb-2">
        <label for="supplierNotes" class="block text-sm font-medium text-gray-700">{{ t('product.supplierNotes') }}</label>
      </div>
      <textarea
        id="supplierNotes"
        v-model="form.supplierNotes"
        class="w-full p-2 border border-gray-300 rounded-md"
        rows="4"
      ></textarea>
    </div>
  </div>
</template>

<script setup>
import { inject } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

// Inject the product form data from parent component
const form = inject('productForm');

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Define validation rules - campos opcionais sem validação
const validationRules = {
  supplierName: {},
  supplierLink: {},
  supplierNotes: {}
};

// Expose validation rules
defineExpose({ validationRules });
</script>
