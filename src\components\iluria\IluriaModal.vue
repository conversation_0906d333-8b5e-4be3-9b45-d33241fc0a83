<template>
  <Dialog 
    v-model:visible="visible" 
    :modal="true"
    :dismissableMask="dismissableMask"
    :draggable="draggable"
    :header="showHeader && !$slots.header && !title ? null : ''"
    class="iluria-modal border-0"
    :style="dialogInlineStyle"
    :pt="{
      root: 'iluria-modal-root',
      header: {
        style: {
          background: 'var(--iluria-color-sidebar-bg)',
          borderBottom: '1px solid var(--iluria-color-border)',
          color: 'var(--iluria-color-text-primary)',
          padding: '1.25rem 1.5rem',
          fontWeight: '600',
          borderRadius: '0.75rem 0.75rem 0 0'
        }
      },
      content: Object.assign({ 
        padding: '2rem 1.5rem 1.5rem 1.5rem',
        background: 'var(--iluria-color-container-bg)',
        color: 'var(--iluria-color-text-primary)'
      }, contentStyle),
      footer: {
        style: {
          background: 'var(--iluria-color-container-bg)',
          borderTop: '1px solid var(--iluria-color-border)',
          padding: '1rem 1.5rem 1.5rem 1.5rem',
          borderRadius: '0 0 0.75rem 0.75rem'
        }
      },
      mask: {
        style: {
          backgroundColor: 'rgba(0, 0, 0, 0.6)',
          backdropFilter: 'blur(4px)'
        }
      },
      transition: {
        enterFromClass: 'opacity-0 scale-75',
        enterActiveClass: 'transition-all duration-300 ease-out',
        leaveActiveClass: 'transition-all duration-200 ease-in',
        leaveToClass: 'opacity-0 scale-75'
      }
    }"
  >
    <!-- Header slot with fallback to title prop -->
    <template #header v-if="$slots.header">
      <slot name="header"></slot>
    </template>
    <template #header v-else-if="showHeader && title">
      <div class="flex items-center gap-3">
        <!-- Icon -->
        <div v-if="icon" class="flex-shrink-0">
          <div class="w-8 h-8 rounded-full flex items-center justify-center" style="background: var(--iluria-color-container-bg);">
            <HugeiconsIcon :icon="icon" size="18" :class="iconColorClass" />
          </div>
        </div>
        <!-- Title and Subtitle -->
        <div class="flex-1 min-w-0">
          <div class="text-lg font-semibold" style="color: var(--iluria-color-text-primary);">{{ title }}</div>
          <div v-if="subtitle" class="text-sm mt-1" style="color: var(--iluria-color-text-secondary);">{{ subtitle }}</div>
        </div>
      </div>
    </template>
    <div>
      <p>&nbsp;&nbsp;&nbsp;&nbsp;</p>
    </div>
    <!-- Default content slot -->
    <slot></slot>

    <!-- Footer slot with fallback to default save/cancel buttons -->
    <template #footer v-if="showFooter && ($slots.footer || saveLabel || cancelLabel)">
      <div class="flex justify-end gap-2">
        <slot name="footer">
          <IluriaButton
            v-if="cancelLabel"
            @click="handleCancel"
            variant="outline"
            class="min-w-[100px]"
          >
            {{ cancelLabel || t('cancel') }}
          </IluriaButton>
          <IluriaButton
            v-if="saveLabel"
            @click="handleSave"
            class="min-w-[100px]"
          >
            {{ saveLabel || t('save') }}
          </IluriaButton>
        </slot>
      </div>
    </template>
  </Dialog>
</template>

<script setup>
import Dialog from 'primevue/dialog';
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';

const { t } = useI18n();

const props = defineProps({
  /** Controls whether the modal is shown */
  modelValue: {
    type: Boolean,
    default: false
  },
  /** Modal title (used when header slot is not provided) */
  title: {
    type: String,
    default: ''
  },
  /** Modal subtitle */
  subtitle: {
    type: String,
    default: ''
  },
  /** Icon component for the header */
  icon: {
    type: Object,
    default: null
  },
  /** Icon color */
  iconColor: {
    type: String,
    default: 'blue'
  },
  /** Label for the save button */
  saveLabel: {
    type: String
  },
  /** Label for the cancel button */
  cancelLabel: {
    type: String
  },
  /** Whether to show the footer with action buttons */
  showFooter: {
    type: Boolean,
    default: true
  },
  /** Whether to show the header */
  showHeader: {
    type: Boolean,
    default: true
  },
  /** Whether clicking outside closes the modal */
  dismissableMask: {
    type: Boolean,
    default: true
  },
  /** Whether the modal can be dragged by its header */
  draggable: {
    type: Boolean,
    default: false
  },
  /** Custom style for the content */
  contentStyle: {
    type: Object,
    default: () => ({})
  },
  /** Custom style for the Dialog root (ex: width, maxWidth) */
  dialogStyle: {
    type: Object,
    default: () => ({})
  }
});

const emit = defineEmits(['update:modelValue', 'save', 'cancel']);

// Computed property to control dialog visibility with two-way binding
const visible = computed({
  get: () => props.modelValue,
  set: (value) => {
    emit('update:modelValue', value);
  }
});

// Computed para aplicar estilo no Dialog
const dialogInlineStyle = computed(() => props.dialogStyle || {})

// Computed para cor do ícone
const iconColorClass = computed(() => {
  const colorMap = {
    blue: 'text-[var(--iluria-color-primary)]',
    green: 'text-green-600',
    red: 'text-red-600',
    yellow: 'text-yellow-600',
    purple: 'text-purple-600',
    orange: 'text-orange-600',
    gray: 'text-[var(--iluria-color-text-secondary)]'
  };
  return colorMap[props.iconColor] || 'text-[var(--iluria-color-primary)]';
})

// Handle save button click
const handleSave = () => {
  emit('save');
};

// Handle cancel button click
const handleCancel = () => {
  emit('cancel');
  // If no cancel handler is provided, close the modal
  if (!emit.hasOwnProperty('cancel') || emit.getListeners('cancel').length === 0) {
    visible.value = false;
  }
};
</script>

<style>
/* Modal background - highest specificity */
.iluria-modal :deep(.p-dialog),
.iluria-modal :deep(.p-dialog-content),
.iluria-modal :deep(.p-dialog) * {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
}

.iluria-modal :deep(.p-dialog) {
  border: 1px solid var(--iluria-color-border) !important;
  border-radius: 0.75rem !important;
  box-shadow: var(--iluria-shadow-lg) !important;
  color: var(--iluria-color-text-primary) !important;
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
}

/* Force all child elements to inherit theme colors */
.iluria-modal :deep(.p-dialog *) {
  border-color: var(--iluria-color-border) !important;
}

/* Override any white or default borders */
.iluria-modal :deep(.p-dialog),
.iluria-modal :deep(.p-dialog-header),
.iluria-modal :deep(.p-dialog-content),
.iluria-modal :deep(.p-dialog-footer),
.iluria-modal :deep(.p-component) {
  border-color: var(--iluria-color-border) !important;
}

.iluria-modal :deep(.p-dialog-header) {
  padding: 1.25rem 1.5rem !important;
  font-weight: 600 !important;
  background: var(--iluria-color-sidebar-bg) !important;
  background-color: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  border-radius: 0.75rem 0.75rem 0 0 !important;
}

.iluria-modal :deep(.p-dialog-content) {
  padding: 2rem 1.5rem 1.5rem 1.5rem !important;
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
}

.iluria-modal :deep(.p-dialog-footer) {
  padding: 1rem 1.5rem 1.5rem 1.5rem !important;
  border-top: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
  border-radius: 0 0 0.75rem 0.75rem !important;
}

.iluria-modal :deep(.p-dialog-header-icon) {
  color: var(--iluria-color-text-secondary) !important;
  transition: all 0.2s ease !important;
}

.iluria-modal :deep(.p-dialog-header-icon:hover) {
  color: var(--iluria-color-text-primary) !important;
  background: var(--iluria-color-hover) !important;
}

.iluria-modal :deep(.p-dialog-mask) {
  background-color: rgba(0, 0, 0, 0.6) !important;
  backdrop-filter: blur(4px) !important;
}

/* Force background on all child elements */
.iluria-modal :deep(*) {
  background-color: inherit !important;
  border-color: var(--iluria-color-border) !important;
}

/* Override specific white backgrounds */
.iluria-modal :deep(.bg-white),
.iluria-modal :deep([style*="background-color: white"]),
.iluria-modal :deep([style*="background-color: #ffffff"]),
.iluria-modal :deep([style*="background-color:#ffffff"]),
.iluria-modal :deep([style*="background: white"]),
.iluria-modal :deep([style*="background: #ffffff"]) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
}

/* Override any remaining default styles */
.iluria-modal :deep(.p-inputtext),
.iluria-modal :deep(.p-dropdown),
.iluria-modal :deep(.p-button),
.iluria-modal :deep(.p-datatable),
.iluria-modal :deep(.p-datatable-wrapper),
.iluria-modal :deep(table),
.iluria-modal :deep(thead),
.iluria-modal :deep(tbody),
.iluria-modal :deep(tr),
.iluria-modal :deep(td),
.iluria-modal :deep(th) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
  border-color: var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
}

/* Final fallback for any missed elements */
.product-selector-modal :deep(.p-dialog),
.product-selector-modal :deep(.p-dialog-content),
.product-selector-modal :deep(.p-dialog-header),
.product-selector-modal :deep(.p-dialog-footer) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
  border-color: var(--iluria-color-border) !important;
}

/* Force PrimeVue Dialog to use theme colors */
.iluria-modal-root {
  background: var(--iluria-color-container-bg) !important;
  border: 1px solid var(--iluria-color-border) !important;
  border-radius: 0.75rem !important;
  box-shadow: var(--iluria-shadow-lg) !important;
}

/* Override PrimeVue's default white styles at the highest level */
:deep(.p-dialog.iluria-modal-root) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
}

:deep(.p-dialog.iluria-modal-root .p-dialog-header) {
  background: var(--iluria-color-sidebar-bg) !important;
  background-color: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
}

:deep(.p-dialog.iluria-modal-root .p-dialog-content) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  padding: 2rem 1.5rem 1.5rem 1.5rem !important;
}

:deep(.p-dialog.iluria-modal-root .p-dialog-footer) {
  background: var(--iluria-color-container-bg) !important;
  background-color: var(--iluria-color-container-bg) !important;
  border-top: 1px solid var(--iluria-color-border) !important;
}

/* Add text utility overrides for better theme contrast */
.iluria-modal :deep(.text-gray-300),
.iluria-modal :deep(.text-gray-400),
.iluria-modal :deep(.text-gray-500),
.iluria-modal :deep(.text-gray-600),
.iluria-modal :deep(.text-gray-700) {
  color: var(--iluria-color-text-primary) !important;
}
</style>
