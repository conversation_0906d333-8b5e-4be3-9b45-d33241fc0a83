<template>
  <div class="theme-action-menu">
    <IluriaButton
      ref="triggerRef"
      variant="ghost"
      size="small"
      @click="toggleMenu"
      :hugeIcon="MoreVerticalIcon"
      class="menu-trigger"
    />
    
    <Teleport to="body">
      <Transition name="menu">
        <div v-if="isMenuOpen" ref="menuRef" class="action-menu" @click.stop>
        <div class="menu-header" v-if="isCurrent">
          <HugeiconsIcon :icon="CheckmarkSquare02Icon" class="current-icon" />
          <span>{{ $t('themes.currentTheme') }}</span>
        </div>
        
        <div class="menu-section">
          <button
            class="menu-item"
            @click="handleAction('preview')"
          >
            <HugeiconsIcon :icon="EyeIcon" class="menu-icon" />
            <span>{{ $t('themes.preview.title') }}</span>
          </button>

          <button
            v-if="!isCurrent"
            class="menu-item"
            @click="handleAction('activate')"
          >
            <HugeiconsIcon :icon="PlayIcon" class="menu-icon" />
            <span>{{ $t('themes.activate') }}</span>
          </button>

          <button
            class="menu-item"
            @click="handleAction('rename')"
            :disabled="!canEdit"
          >
            <HugeiconsIcon :icon="EditIcon" class="menu-icon" />
            <span>{{ $t('themes.rename') }}</span>
          </button>

          <button
            class="menu-item"
            @click="handleAction('change-colors')"
            :disabled="!canEdit"
          >
            <HugeiconsIcon :icon="PaintBoardIcon" class="menu-icon" />
            <span>{{ $t('themes.changeColors') }}</span>
          </button>
        </div>

        <div class="menu-section">
          <button
            class="menu-item"
            @click="handleAction('duplicate')"
          >
            <HugeiconsIcon :icon="CopyIcon" class="menu-icon" />
            <span>{{ $t('themes.duplicate') }}</span>
          </button>
        </div>
        


        <div class="menu-section" v-if="canDelete">
          <button
            class="menu-item menu-item--danger"
            @click="handleAction('delete')"
          >
            <HugeiconsIcon :icon="Delete02Icon" class="menu-icon" />
            <span>{{ $t('themes.delete') }}</span>
          </button>
        </div>
        </div>
      </Transition>

      <!-- Backdrop -->
      <div
        v-if="isMenuOpen"
        class="menu-backdrop"
        @click="closeMenu"
      ></div>
    </Teleport>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  MoreVerticalIcon,
  CheckmarkSquare02Icon,
  EyeIcon,
  PlayIcon,
  EditIcon,
  CopyIcon,
  Delete02Icon,
  PaintBoardIcon
} from '@hugeicons-pro/core-stroke-standard'

import IluriaButton from '@/components/iluria/IluriaButton.vue'

// Props
const props = defineProps({
  theme: {
    type: Object,
    required: true
  },
  isCurrent: {
    type: Boolean,
    default: false
  },
  templateStatus: {
    type: Object,
    default: null
  },
  showAISuggestions: {
    type: Boolean,
    default: true
  },
  editableTextRef: {
    type: Object,
    default: null
  }
})

// Emits
const emit = defineEmits([
  'preview',
  'activate',
  'rename',
  'duplicate',
  'change-colors',
  'delete',
  'action'
])

// State
const isMenuOpen = ref(false)
const menuRef = ref(null)
const triggerRef = ref(null)

// Computed
const canEdit = computed(() => {
 // Só pode editar temas customizados ou não padrão
  return !props.theme.isDefault || (props.theme.storeId && props.theme.storeId !== null)
})

const canDelete = computed(() => {
  // Só pode deletar temas customizados e que não sejam o tema atual
  return !props.isCurrent && (!props.theme.isDefault || (props.theme.storeId && props.theme.storeId !== null))
})



// Methods
const toggleMenu = async (event) => {
  // Prevenir propagação para evitar conflitos
  if (event) {
    event.stopPropagation()
  }

  isMenuOpen.value = !isMenuOpen.value

  if (isMenuOpen.value) {
    await nextTick()
    positionMenu()
  }
}

const positionMenu = () => {
  if (!triggerRef.value || !menuRef.value) return

  const trigger = triggerRef.value.$el || triggerRef.value
  const menu = menuRef.value
  const triggerRect = trigger.getBoundingClientRect()
  const menuRect = menu.getBoundingClientRect()
  const viewport = {
    width: window.innerWidth,
    height: window.innerHeight
  }

  // Posição inicial: abaixo do botão, alinhado à direita
  let top = triggerRect.bottom + 8
  let left = triggerRect.right - menuRect.width

  // Verificar se o menu sai da tela pela direita
  if (left < 16) {
    left = triggerRect.left
  }

  // Verificar se ainda sai pela direita após ajuste
  if (left + menuRect.width > viewport.width - 16) {
    left = viewport.width - menuRect.width - 16
  }

  // Verificar se o menu sai da tela por baixo
  if (top + menuRect.height > viewport.height - 16) {
    top = triggerRect.top - menuRect.height - 8
  }

  // Verificar se o menu sai da tela por cima após ajuste
  if (top < 16) {
    top = 16
  }

  // Garantir que o menu não sobreponha o trigger
  if (top < triggerRect.bottom && top + menuRect.height > triggerRect.top) {
    if (triggerRect.top - menuRect.height - 8 > 16) {
      top = triggerRect.top - menuRect.height - 8
    } else {
      top = triggerRect.bottom + 8
    }
  }

  // Aplicar posicionamento com !important para garantir prioridade
  menu.style.setProperty('top', `${top}px`, 'important')
  menu.style.setProperty('left', `${left}px`, 'important')
  menu.style.setProperty('position', 'fixed', 'important')
  menu.style.setProperty('z-index', '99999', 'important')
}

const closeMenu = () => {
  isMenuOpen.value = false
}

const handleAction = (action) => {
  if (action === 'rename' && props.editableTextRef) {
    // Ativar edição inline diretamente
    props.editableTextRef.startEditing()
    closeMenu()
    return
  }

  const actionData = {
    type: action,
    theme: props.theme,
    templateStatus: props.templateStatus
  }

  // Emitir apenas evento genérico para o pai
  emit('action', actionData)

  // Fechar menu
  closeMenu()
}

// Close menu on outside click
const handleOutsideClick = (event) => {
  if (!isMenuOpen.value) return

  // Verificar se o clique foi dentro do componente
  const menuComponent = event.target.closest('.theme-action-menu')
  if (!menuComponent) {
    closeMenu()
    return
  }

  // Se clicou no trigger, não fechar (deixar o toggleMenu lidar)
  const trigger = triggerRef.value?.$el || triggerRef.value
  if (trigger && trigger.contains(event.target)) {
    return
  }

  // Se clicou no menu, não fechar
  const menu = menuRef.value
  if (menu && menu.contains(event.target)) {
    return
  }

  // Se chegou aqui, clicou fora - fechar
  closeMenu()
}

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleOutsideClick)
})

onUnmounted(() => {
  document.removeEventListener('click', handleOutsideClick)
})
</script>

<style scoped>
.theme-action-menu {
  position: relative;
  display: inline-block;
  z-index: 1;
}

/* Garantir que o menu tenha prioridade máxima */
.theme-action-menu .action-menu {
  z-index: 99999 !important;
}

.theme-action-menu .menu-backdrop {
  z-index: 99998 !important;
}

.menu-trigger {
  border-radius: 50%;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-menu {
  position: fixed;
  z-index: 99999;
  min-width: 220px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  box-shadow: var(--iluria-shadow-lg);
  padding: 0.5rem 0;
  pointer-events: all;
  user-select: none;
}

.menu-backdrop {
  position: fixed;
  inset: 0;
  z-index: 99998;
  background: transparent;
  pointer-events: all;
}

.menu-header {
  padding: 0.75rem 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  margin-bottom: 0.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.current-icon {
  width: 1rem;
  height: 1rem;
  color: var(--iluria-color-success);
}

.ai-header {
  border-bottom: none;
  margin-bottom: 0;
  opacity: 0.7;
}

.ai-icon {
  width: 1rem;
  height: 1rem;
  color: var(--iluria-color-primary);
}

.beta-badge {
  background: var(--iluria-color-warning-light);
  color: var(--iluria-color-warning);
  padding: 0.125rem 0.5rem;
  border-radius: 10px;
  font-size: 0.625rem;
  font-weight: 600;
  margin-left: auto;
}

.menu-section {
  padding: 0.25rem 0;
}

.menu-section:not(:last-child) {
  border-bottom: 1px solid var(--iluria-color-border);
}

.ai-section {
  background: rgba(var(--iluria-color-primary-rgb), 0.02);
}

.template-section {
  background: rgba(var(--iluria-color-info-rgb), 0.02);
  border-left: 3px solid var(--iluria-color-info);
}

.template-icon {
  width: 1rem;
  height: 1rem;
  color: var(--iluria-color-info);
}

.menu-divider {
  height: 1px;
  background: var(--iluria-color-border);
  margin: 0.5rem 0;
}

.menu-item {
  width: 100%;
  padding: 0.75rem 1rem;
  background: none;
  border: none;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.menu-item:hover:not(:disabled) {
  background: var(--iluria-color-hover);
}

.menu-item:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.menu-item--disabled {
  opacity: 0.4;
  cursor: not-allowed;
}

.menu-item--danger {
  color: var(--iluria-color-danger);
}

.menu-item--danger:hover {
  background: var(--iluria-color-danger-light);
}

.menu-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
}

/* Transitions */
.menu-enter-active,
.menu-leave-active {
  transition: all 0.2s ease;
}

.menu-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.menu-leave-to {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

/* Responsive */
@media (max-width: 768px) {
  .action-menu {
    position: fixed;
    top: auto;
    bottom: 1rem;
    left: 1rem;
    right: 1rem;
    border-radius: 12px;
    max-width: none;
  }
  
  .menu-item {
    padding: 1rem;
    font-size: 1rem;
  }
  
  .menu-icon {
    width: 1.5rem;
    height: 1.5rem;
  }
}
</style>