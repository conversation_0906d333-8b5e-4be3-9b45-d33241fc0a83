import axios from 'axios';
import { API_URLS } from '../config/api.config';

class SettingsService {
    #endpointUrlRedirect;

    constructor() {
      this.#endpointUrlRedirect = `${API_URLS.SETTINGS_BASE_URL}/url-redirect`;
    }

    async getUrlRedirectList(filter, page, size) {
        try {
            const response = await axios.get(this.#endpointUrlRedirect, {
                params: { filter, page, size }
            });
            
            return {
                content: response.data.content || [],  
                totalPages: response.data.totalPages || 0  
            };
        } catch (error) {
            console.error('Error fetching URL redirects:', error);
            throw error;
        }
    }

    async createUrlRedirect(urlRedirectData) {
        try {
            const response = await axios.post(this.#endpointUrlRedirect, urlRedirectData);
            return response.data;
        } catch (error) {
            console.error('Error creating URL redirect:', error);
            throw error;
        }
    }

    async getUrlRedirect(id) {
        try {
            const response = await axios.get(`${this.#endpointUrlRedirect}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching URL redirect:', error);
            throw error;
        }
    }

    async updateUrlRedirect(id, urlRedirectData) {
        try {
            const response = await axios.put(`${this.#endpointUrlRedirect}/${id}`, urlRedirectData);
            return response.data;
        } catch (error) {
            console.error('Error updating URL redirect:', error);
            throw error;
        }
    }

    async deleteUrlRedirect(id) {
        try {
            const response = await axios.delete(`${this.#endpointUrlRedirect}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting URL redirect:', error);
            throw error;
        }
    }

}

export default new SettingsService();