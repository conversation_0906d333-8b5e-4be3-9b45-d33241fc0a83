import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useToastStore = defineStore('toast', () => {
  // Estado dos toasts
  const toasts = ref([])
  const nextId = ref(1)

  // Função para gerar ID único
  const generateId = () => {
    return nextId.value++
  }

  // Função para adicionar toast
  const addToast = (options) => {
    const toast = {
      id: generateId(),
      type: options.type || 'info',
      message: options.message || '',
      title: options.title || '',
      duration: options.duration !== undefined ? options.duration : 4000,
      action: options.action || null,
      persistent: options.persistent || false,
      dismissible: options.dismissible !== undefined ? options.dismissible : true,
      createdAt: Date.now()
    }

    toasts.value.push(toast)

    // Limitar o número máximo de toasts visíveis (opcional)
    if (toasts.value.length > 5) {
      toasts.value.shift() // Remove o mais antigo
    }

    return toast.id
  }

  // Função para remover toast
  const removeToast = (id) => {
    const index = toasts.value.findIndex(toast => toast.id === id)
    if (index > -1) {
      toasts.value.splice(index, 1)
    }
  }

  // Função para limpar todos os toasts
  const clearAllToasts = () => {
    toasts.value = []
  }

  // Função para encontrar um toast por ID
  const findToast = (id) => {
    return toasts.value.find(toast => toast.id === id)
  }

  // Função para atualizar um toast existente
  const updateToast = (id, updates) => {
    const toast = findToast(id)
    if (toast) {
      Object.assign(toast, updates)
    }
  }

  return {
    // Estado
    toasts,
    
    // Ações
    addToast,
    removeToast,
    clearAllToasts,
    findToast,
    updateToast
  }
}) 