import { ref, computed, watch, onMounted } from 'vue'

// Definição dos temas disponíveis
const THEMES = {
  light: {
    id: 'light',
    colors: {
      // Cores principais
      '--iluria-color-primary': '#1f2937',
      '--iluria-color-primary-hover': '#111827',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#6b7280',
      '--iluria-color-secondary-hover': '#4b5563',
      
      // Backgrounds
      '--iluria-color-body-bg': '#f9fafb',
      '--iluria-color-background': '#f9fafb',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#f3f4f6',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(0, 0, 0, 0.05)',
      '--iluria-color-navbar-active': 'rgba(0, 0, 0, 0.1)',
      '--iluria-color-navbar-active-hover': 'rgba(0, 0, 0, 0.15)',
      
      // Textos
      '--iluria-color-body-fg': '#1f2937',
      '--iluria-color-text': '#1f2937',
      '--iluria-color-navbar-fg': '#374151',
      '--iluria-color-text-primary': '#1f2937',
      '--iluria-color-text-secondary': '#6b7280',
      '--iluria-color-text-muted': '#9ca3af',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(0, 0, 0, 0.05)',
      '--iluria-color-focus-ring': 'rgba(31, 41, 55, 0.1)',
      
      // Bordas
      '--iluria-color-border': '#e5e7eb',
      '--iluria-color-border-hover': '#d1d5db',
      '--iluria-color-border-focus': '#1f2937',
      
      // Estados
      '--iluria-color-success': '#10b981',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#1f2937',
      
      // Botões específicos - tema claro
      '--iluria-color-button-primary-bg': '#1f2937',
      '--iluria-color-button-primary-bg-hover': '#111827',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#6b7280',
      '--iluria-color-button-secondary-bg-hover': '#4b5563',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#10b981',
      '--iluria-color-button-confirm-bg-hover': '#059669',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#1f2937',
      '--iluria-color-button-dark-bg-hover': '#111827',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#1f2937',
      
      // Inputs específicos - tema claro
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#e5e7eb',
      '--iluria-color-input-border-focus': '#1f2937',
      '--iluria-color-input-text': '#1f2937',
      '--iluria-color-input-placeholder': '#9ca3af',
      
      // Switches e controles específicos - tema claro
      '--iluria-color-switch-bg': '#e5e7eb',
      '--iluria-color-switch-bg-active': '#1f2937',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#d1d5db',
      '--iluria-color-switch-border-active': '#1f2937',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#1f2937',
      '--iluria-color-checkbox-border': '#d1d5db',
      '--iluria-color-checkbox-border-active': '#1f2937',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#e5e7eb',
      '--iluria-color-select-border-focus': '#1f2937',
      '--iluria-color-select-option-hover': '#f3f4f6',
      '--iluria-color-select-option-selected': '#f9fafb',
      
      // Logo específico - tema claro
      '--iluria-logo-text-color': '#1f2937',
      '--iluria-logo-accent-color': '#ec3526',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(31, 41, 55, 0.05)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(31, 41, 55, 0.1)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(31, 41, 55, 0.1)',
    }
  },
  
  dark: {
    id: 'dark',
    colors: {
      // Cores principais - mais focado no preto
      '--iluria-color-primary': '#ffffff',
      '--iluria-color-primary-hover': '#f3f4f6',
      '--iluria-color-primary-contrast': '#000000',
      '--iluria-color-secondary': '#6b7280',
      '--iluria-color-secondary-hover': '#9ca3af',
      
      // Backgrounds - tons de preto/cinza escuro
      '--iluria-color-body-bg': '#0a0a0a',
      '--iluria-color-background': '#0a0a0a',
      '--iluria-color-surface': '#1a1a1a',
      '--iluria-color-navbar-bg': '#111111',
      '--iluria-color-sidebar-bg': '#151515',
      '--iluria-color-container-bg': '#1a1a1a',
      '--iluria-color-card-bg': '#222222',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(255, 255, 255, 0.1)',
      '--iluria-color-navbar-active': 'rgba(255, 255, 255, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(255, 255, 255, 0.3)',
      
      // Textos - contrastes altos para acessibilidade
      '--iluria-color-body-fg': '#ffffff',
      '--iluria-color-text': '#ffffff',
      '--iluria-color-navbar-fg': '#ffffff',
      '--iluria-color-text-primary': '#ffffff',
      '--iluria-color-text-secondary': '#d1d5db',
      '--iluria-color-text-muted': '#9ca3af',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(255, 255, 255, 0.03)',
      '--iluria-color-focus-ring': 'rgba(156, 163, 175, 0.3)',
      
      // Bordas - tons de cinza escuro
      '--iluria-color-border': '#333333',
      '--iluria-color-border-hover': '#444444',
      '--iluria-color-border-focus': '#ffffff',
      
      // Estados - cores vibrantes para contraste
      '--iluria-color-success': '#22c55e',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#9ca3af',
      
      // Botões específicos - tema escuro
      '--iluria-color-button-primary-bg': '#ffffff',
      '--iluria-color-button-primary-bg-hover': '#f3f4f6',
      '--iluria-color-button-primary-fg': '#000000',
      
      '--iluria-color-button-secondary-bg': '#6b7280',
      '--iluria-color-button-secondary-bg-hover': '#9ca3af',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#22c55e',
      '--iluria-color-button-confirm-bg-hover': '#16a34a',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#444444',
      '--iluria-color-button-dark-bg-hover': '#333333',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#ffffff',
      
      // Inputs específicos - tema escuro
      '--iluria-color-input-bg': '#222222',
      '--iluria-color-input-border': '#333333',
      '--iluria-color-input-border-focus': '#9ca3af',
      '--iluria-color-input-text': '#ffffff',
      '--iluria-color-input-placeholder': '#9ca3af',
      
      // Switches e controles específicos - tema escuro (cinza destaque)
      '--iluria-color-switch-bg': '#333333',
      '--iluria-color-switch-bg-active': '#9ca3af',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#444444',
      '--iluria-color-switch-border-active': '#9ca3af',
      
      '--iluria-color-checkbox-bg': '#222222',
      '--iluria-color-checkbox-bg-active': '#9ca3af',
      '--iluria-color-checkbox-border': '#333333',
      '--iluria-color-checkbox-border-active': '#9ca3af',
      '--iluria-color-checkbox-check': '#000000',
      
      '--iluria-color-select-bg': '#222222',
      '--iluria-color-select-border': '#333333',
      '--iluria-color-select-border-focus': '#9ca3af',
      '--iluria-color-select-option-hover': '#333333',
      '--iluria-color-select-option-selected': '#444444',
      
      // Logo específico - tema escuro
      '--iluria-logo-text-color': '#ffffff',
      '--iluria-logo-accent-color': '#ec3526',
      
      // Shadows - mais intensas para tema escuro
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.8)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.8)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.9)',
    }
  },
  
  cupcake: {
    id: 'cupcake',
    colors: {
      // Cores principais - inspirado no tema cupcake do DaisyUI
      '--iluria-color-primary': '#ec4899',
      '--iluria-color-primary-hover': '#db2777',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#a855f7',
      '--iluria-color-secondary-hover': '#9333ea',
      
      // Backgrounds - tons pastéis doces
      '--iluria-color-body-bg': '#fef7f0',
      '--iluria-color-background': '#fef7f0',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#fdf2f8',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(236, 72, 153, 0.08)',
      '--iluria-color-navbar-active': 'rgba(236, 72, 153, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(236, 72, 153, 0.2)',
      
      // Textos
      '--iluria-color-body-fg': '#3f172f',
      '--iluria-color-text': '#3f172f',
      '--iluria-color-navbar-fg': '#3f172f',
      '--iluria-color-text-primary': '#3f172f',
      '--iluria-color-text-secondary': '#831843',
      '--iluria-color-text-muted': '#be185d',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(236, 72, 153, 0.05)',
      '--iluria-color-focus-ring': 'rgba(236, 72, 153, 0.1)',
      
      // Bordas - tons doces
      '--iluria-color-border': '#fce7f3',
      '--iluria-color-border-hover': '#fbcfe8',
      '--iluria-color-border-focus': '#ec4899',
      
      // Estados
      '--iluria-color-success': '#22c55e',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#a855f7',
      
      // Botões específicos - tema cupcake doce
      '--iluria-color-button-primary-bg': '#ec4899',
      '--iluria-color-button-primary-bg-hover': '#db2777',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#a855f7',
      '--iluria-color-button-secondary-bg-hover': '#9333ea',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#22c55e',
      '--iluria-color-button-confirm-bg-hover': '#16a34a',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#be185d',
      '--iluria-color-button-dark-bg-hover': '#9d174d',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#3f172f',
      
      // Inputs específicos - tema cupcake doce
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#fce7f3',
      '--iluria-color-input-border-focus': '#ec4899',
      '--iluria-color-input-text': '#3f172f',
      '--iluria-color-input-placeholder': '#be185d',
      
      // Switches e controles específicos - tema cupcake doce
      '--iluria-color-switch-bg': '#fce7f3',
      '--iluria-color-switch-bg-active': '#ec4899',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#fbcfe8',
      '--iluria-color-switch-border-active': '#ec4899',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#ec4899',
      '--iluria-color-checkbox-border': '#fbcfe8',
      '--iluria-color-checkbox-border-active': '#ec4899',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#fce7f3',
      '--iluria-color-select-border-focus': '#ec4899',
      '--iluria-color-select-option-hover': '#fdf2f8',
      '--iluria-color-select-option-selected': '#fce7f3',
      
      // Logo específico - tema cupcake doce
      '--iluria-logo-text-color': '#3f172f',
      '--iluria-logo-accent-color': '#ec4899',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(236, 72, 153, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(236, 72, 153, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(236, 72, 153, 0.2)',
    }
  },

  bumblebee: {
    id: 'bumblebee',
    colors: {
      // Cores principais - inspirado no tema bumblebee do DaisyUI
      '--iluria-color-primary': '#f59e0b',
      '--iluria-color-primary-hover': '#d97706',
      '--iluria-color-primary-contrast': '#000000',
      '--iluria-color-secondary': '#1f2937',
      '--iluria-color-secondary-hover': '#111827',
      
      // Backgrounds - tons amarelos suaves
      '--iluria-color-body-bg': '#fffbeb',
      '--iluria-color-background': '#fffbeb',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#f59e0b',
      '--iluria-color-sidebar-bg': '#fef3c7',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(0, 0, 0, 0.1)',
      '--iluria-color-navbar-active': 'rgba(0, 0, 0, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(0, 0, 0, 0.3)',
      
      // Textos
      '--iluria-color-body-fg': '#1f2937',
      '--iluria-color-text': '#1f2937',
      '--iluria-color-navbar-fg': '#000000',
      '--iluria-color-text-primary': '#1f2937',
      '--iluria-color-text-secondary': '#374151',
      '--iluria-color-text-muted': '#6b7280',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(245, 158, 11, 0.1)',
      '--iluria-color-focus-ring': 'rgba(245, 158, 11, 0.2)',
      
      // Bordas - tons amarelos
      '--iluria-color-border': '#fed7aa',
      '--iluria-color-border-hover': '#fdba74',
      '--iluria-color-border-focus': '#f59e0b',
      
      // Estados
      '--iluria-color-success': '#10b981',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#3b82f6',
      
      // Botões específicos - tema bumblebee
      '--iluria-color-button-primary-bg': '#f59e0b',
      '--iluria-color-button-primary-bg-hover': '#d97706',
      '--iluria-color-button-primary-fg': '#000000',
      
      '--iluria-color-button-secondary-bg': '#1f2937',
      '--iluria-color-button-secondary-bg-hover': '#111827',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#10b981',
      '--iluria-color-button-confirm-bg-hover': '#059669',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#1f2937',
      '--iluria-color-button-dark-bg-hover': '#111827',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#1f2937',
      
      // Inputs específicos - tema bumblebee
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#fed7aa',
      '--iluria-color-input-border-focus': '#f59e0b',
      '--iluria-color-input-text': '#1f2937',
      '--iluria-color-input-placeholder': '#6b7280',
      
      // Switches e controles específicos - tema bumblebee
      '--iluria-color-switch-bg': '#fed7aa',
      '--iluria-color-switch-bg-active': '#f59e0b',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#fdba74',
      '--iluria-color-switch-border-active': '#f59e0b',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#f59e0b',
      '--iluria-color-checkbox-border': '#fdba74',
      '--iluria-color-checkbox-border-active': '#f59e0b',
      '--iluria-color-checkbox-check': '#000000',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#fed7aa',
      '--iluria-color-select-border-focus': '#f59e0b',
      '--iluria-color-select-option-hover': '#fef3c7',
      '--iluria-color-select-option-selected': '#fed7aa',
      
      // Logo específico - tema bumblebee
      '--iluria-logo-text-color': '#1f2937',
      '--iluria-logo-accent-color': '#d97706',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(245, 158, 11, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(245, 158, 11, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(245, 158, 11, 0.2)',
    }
  },

  emerald: {
    id: 'emerald',
    colors: {
      // Cores principais - inspirado no tema emerald do DaisyUI
      '--iluria-color-primary': '#10b981',
      '--iluria-color-primary-hover': '#059669',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#6b7280',
      '--iluria-color-secondary-hover': '#4b5563',
      
      // Backgrounds - tons verdes suaves
      '--iluria-color-body-bg': '#ecfdf5',
      '--iluria-color-background': '#ecfdf5',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#d1fae5',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(16, 185, 129, 0.08)',
      '--iluria-color-navbar-active': 'rgba(16, 185, 129, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(16, 185, 129, 0.2)',
      
      // Textos
      '--iluria-color-body-fg': '#064e3b',
      '--iluria-color-text': '#064e3b',
      '--iluria-color-navbar-fg': '#064e3b',
      '--iluria-color-text-primary': '#064e3b',
      '--iluria-color-text-secondary': '#065f46',
      '--iluria-color-text-muted': '#047857',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(16, 185, 129, 0.05)',
      '--iluria-color-focus-ring': 'rgba(16, 185, 129, 0.1)',
      
      // Bordas - tons verdes
      '--iluria-color-border': '#bbf7d0',
      '--iluria-color-border-hover': '#86efac',
      '--iluria-color-border-focus': '#10b981',
      
      // Estados
      '--iluria-color-success': '#10b981',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#06b6d4',
      
      // Botões específicos - tema emerald
      '--iluria-color-button-primary-bg': '#10b981',
      '--iluria-color-button-primary-bg-hover': '#059669',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#6b7280',
      '--iluria-color-button-secondary-bg-hover': '#4b5563',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#22c55e',
      '--iluria-color-button-confirm-bg-hover': '#16a34a',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#064e3b',
      '--iluria-color-button-dark-bg-hover': '#022c22',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#064e3b',
      
      // Inputs específicos - tema emerald
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#bbf7d0',
      '--iluria-color-input-border-focus': '#10b981',
      '--iluria-color-input-text': '#064e3b',
      '--iluria-color-input-placeholder': '#047857',
      
      // Switches e controles específicos - tema emerald
      '--iluria-color-switch-bg': '#bbf7d0',
      '--iluria-color-switch-bg-active': '#10b981',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#86efac',
      '--iluria-color-switch-border-active': '#10b981',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#10b981',
      '--iluria-color-checkbox-border': '#86efac',
      '--iluria-color-checkbox-border-active': '#10b981',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#bbf7d0',
      '--iluria-color-select-border-focus': '#10b981',
      '--iluria-color-select-option-hover': '#d1fae5',
      '--iluria-color-select-option-selected': '#bbf7d0',
      
      // Logo específico - tema emerald
      '--iluria-logo-text-color': '#064e3b',
      '--iluria-logo-accent-color': '#10b981',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(16, 185, 129, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(16, 185, 129, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(16, 185, 129, 0.2)',
    }
  },

  corporate: {
    id: 'corporate',
    colors: {
      // Cores principais - inspirado no tema corporate do DaisyUI
      '--iluria-color-primary': '#2563eb',
      '--iluria-color-primary-hover': '#1d4ed8',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#64748b',
      '--iluria-color-secondary-hover': '#475569',
      
      // Backgrounds - tons corporativos limpos
      '--iluria-color-body-bg': '#f8fafc',
      '--iluria-color-background': '#f8fafc',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#f1f5f9',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(37, 99, 235, 0.08)',
      '--iluria-color-navbar-active': 'rgba(37, 99, 235, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(37, 99, 235, 0.2)',
      
      // Textos
      '--iluria-color-body-fg': '#0f172a',
      '--iluria-color-text': '#0f172a',
      '--iluria-color-navbar-fg': '#0f172a',
      '--iluria-color-text-primary': '#0f172a',
      '--iluria-color-text-secondary': '#334155',
      '--iluria-color-text-muted': '#64748b',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(37, 99, 235, 0.05)',
      '--iluria-color-focus-ring': 'rgba(37, 99, 235, 0.1)',
      
      // Bordas - tons corporativos
      '--iluria-color-border': '#e2e8f0',
      '--iluria-color-border-hover': '#cbd5e1',
      '--iluria-color-border-focus': '#2563eb',
      
      // Estados
      '--iluria-color-success': '#059669',
      '--iluria-color-warning': '#d97706',
      '--iluria-color-error': '#dc2626',
      '--iluria-color-info': '#0284c7',
      
      // Botões específicos - tema corporate
      '--iluria-color-button-primary-bg': '#2563eb',
      '--iluria-color-button-primary-bg-hover': '#1d4ed8',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#64748b',
      '--iluria-color-button-secondary-bg-hover': '#475569',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#059669',
      '--iluria-color-button-confirm-bg-hover': '#047857',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#dc2626',
      '--iluria-color-button-danger-bg-hover': '#b91c1c',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#0f172a',
      '--iluria-color-button-dark-bg-hover': '#020617',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#0f172a',
      
      // Inputs específicos - tema corporate
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#e2e8f0',
      '--iluria-color-input-border-focus': '#2563eb',
      '--iluria-color-input-text': '#0f172a',
      '--iluria-color-input-placeholder': '#64748b',
      
      // Switches e controles específicos - tema corporate
      '--iluria-color-switch-bg': '#e2e8f0',
      '--iluria-color-switch-bg-active': '#2563eb',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#cbd5e1',
      '--iluria-color-switch-border-active': '#2563eb',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#2563eb',
      '--iluria-color-checkbox-border': '#cbd5e1',
      '--iluria-color-checkbox-border-active': '#2563eb',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#e2e8f0',
      '--iluria-color-select-border-focus': '#2563eb',
      '--iluria-color-select-option-hover': '#f1f5f9',
      '--iluria-color-select-option-selected': '#e2e8f0',
      
      // Logo específico - tema corporate
      '--iluria-logo-text-color': '#0f172a',
      '--iluria-logo-accent-color': '#2563eb',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(37, 99, 235, 0.05)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(37, 99, 235, 0.1)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(37, 99, 235, 0.15)',
    }
  },

  garden: {
    id: 'garden',
    colors: {
      // Cores principais - inspirado no tema garden do DaisyUI
      '--iluria-color-primary': '#16a34a',
      '--iluria-color-primary-hover': '#15803d',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#ca8a04',
      '--iluria-color-secondary-hover': '#a16207',
      
      // Backgrounds - tons de jardim natural
      '--iluria-color-body-bg': '#f0fdf4',
      '--iluria-color-background': '#f0fdf4',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#dcfce7',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(22, 163, 74, 0.08)',
      '--iluria-color-navbar-active': 'rgba(22, 163, 74, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(22, 163, 74, 0.2)',
      
      // Textos
      '--iluria-color-body-fg': '#14532d',
      '--iluria-color-text': '#14532d',
      '--iluria-color-navbar-fg': '#14532d',
      '--iluria-color-text-primary': '#14532d',
      '--iluria-color-text-secondary': '#166534',
      '--iluria-color-text-muted': '#15803d',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(22, 163, 74, 0.05)',
      '--iluria-color-focus-ring': 'rgba(22, 163, 74, 0.1)',
      
      // Bordas - tons de jardim
      '--iluria-color-border': '#bbf7d0',
      '--iluria-color-border-hover': '#86efac',
      '--iluria-color-border-focus': '#16a34a',
      
      // Estados
      '--iluria-color-success': '#16a34a',
      '--iluria-color-warning': '#ca8a04',
      '--iluria-color-error': '#dc2626',
      '--iluria-color-info': '#0891b2',
      
      // Botões específicos - tema garden
      '--iluria-color-button-primary-bg': '#16a34a',
      '--iluria-color-button-primary-bg-hover': '#15803d',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#ca8a04',
      '--iluria-color-button-secondary-bg-hover': '#a16207',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#22c55e',
      '--iluria-color-button-confirm-bg-hover': '#16a34a',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#dc2626',
      '--iluria-color-button-danger-bg-hover': '#b91c1c',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#14532d',
      '--iluria-color-button-dark-bg-hover': '#052e16',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#14532d',
      
      // Inputs específicos - tema garden
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#bbf7d0',
      '--iluria-color-input-border-focus': '#16a34a',
      '--iluria-color-input-text': '#14532d',
      '--iluria-color-input-placeholder': '#15803d',
      
      // Switches e controles específicos - tema garden
      '--iluria-color-switch-bg': '#bbf7d0',
      '--iluria-color-switch-bg-active': '#16a34a',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#86efac',
      '--iluria-color-switch-border-active': '#16a34a',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#16a34a',
      '--iluria-color-checkbox-border': '#86efac',
      '--iluria-color-checkbox-border-active': '#16a34a',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#bbf7d0',
      '--iluria-color-select-border-focus': '#16a34a',
      '--iluria-color-select-option-hover': '#dcfce7',
      '--iluria-color-select-option-selected': '#bbf7d0',
      
      // Logo específico - tema garden
      '--iluria-logo-text-color': '#14532d',
      '--iluria-logo-accent-color': '#16a34a',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(22, 163, 74, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(22, 163, 74, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(22, 163, 74, 0.2)',
    }
  },

  lofi: {
    id: 'lofi',
    colors: {
      // Cores principais - inspirado no tema lofi do DaisyUI
      '--iluria-color-primary': '#6b7280',
      '--iluria-color-primary-hover': '#4b5563',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#d1d5db',
      '--iluria-color-secondary-hover': '#9ca3af',
      
      // Backgrounds - tons relaxantes e muted
      '--iluria-color-body-bg': '#f9fafb',
      '--iluria-color-background': '#f9fafb',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#f3f4f6',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(107, 114, 128, 0.08)',
      '--iluria-color-navbar-active': 'rgba(107, 114, 128, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(107, 114, 128, 0.2)',
      
      // Textos - tons suaves
      '--iluria-color-body-fg': '#374151',
      '--iluria-color-text': '#374151',
      '--iluria-color-navbar-fg': '#374151',
      '--iluria-color-text-primary': '#374151',
      '--iluria-color-text-secondary': '#6b7280',
      '--iluria-color-text-muted': '#9ca3af',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(107, 114, 128, 0.05)',
      '--iluria-color-focus-ring': 'rgba(107, 114, 128, 0.1)',
      
      // Bordas - tons neutros
      '--iluria-color-border': '#e5e7eb',
      '--iluria-color-border-hover': '#d1d5db',
      '--iluria-color-border-focus': '#6b7280',
      
      // Estados
      '--iluria-color-success': '#10b981',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#6b7280',
      
      // Botões específicos - tema lofi
      '--iluria-color-button-primary-bg': '#6b7280',
      '--iluria-color-button-primary-bg-hover': '#4b5563',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#d1d5db',
      '--iluria-color-button-secondary-bg-hover': '#9ca3af',
      '--iluria-color-button-secondary-fg': '#374151',
      
      '--iluria-color-button-confirm-bg': '#10b981',
      '--iluria-color-button-confirm-bg-hover': '#059669',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#374151',
      '--iluria-color-button-dark-bg-hover': '#1f2937',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#374151',
      
      // Inputs específicos - tema lofi
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#e5e7eb',
      '--iluria-color-input-border-focus': '#6b7280',
      '--iluria-color-input-text': '#374151',
      '--iluria-color-input-placeholder': '#9ca3af',
      
      // Switches e controles específicos - tema lofi
      '--iluria-color-switch-bg': '#e5e7eb',
      '--iluria-color-switch-bg-active': '#6b7280',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#d1d5db',
      '--iluria-color-switch-border-active': '#6b7280',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#6b7280',
      '--iluria-color-checkbox-border': '#d1d5db',
      '--iluria-color-checkbox-border-active': '#6b7280',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#e5e7eb',
      '--iluria-color-select-border-focus': '#6b7280',
      '--iluria-color-select-option-hover': '#f3f4f6',
      '--iluria-color-select-option-selected': '#e5e7eb',
      
      // Logo específico - tema lofi
      '--iluria-logo-text-color': '#374151',
      '--iluria-logo-accent-color': '#6b7280',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(107, 114, 128, 0.05)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(107, 114, 128, 0.1)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(107, 114, 128, 0.1)',
    }
  },

  dracula: {
    id: 'dracula',
    colors: {
      // Cores principais - inspirado no tema dracula do DaisyUI
      '--iluria-color-primary': '#ff79c6',
      '--iluria-color-primary-hover': '#ff6bc5',
      '--iluria-color-primary-contrast': '#282a36',
      '--iluria-color-secondary': '#bd93f9',
      '--iluria-color-secondary-hover': '#a777f7',
      
      // Backgrounds - tons góticos escuros
      '--iluria-color-body-bg': '#282a36',
      '--iluria-color-background': '#282a36',
      '--iluria-color-surface': '#44475a',
      '--iluria-color-navbar-bg': '#6272a4',
      '--iluria-color-sidebar-bg': '#373844',
      '--iluria-color-container-bg': '#44475a',
      '--iluria-color-card-bg': '#44475a',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(255, 121, 198, 0.1)',
      '--iluria-color-navbar-active': 'rgba(255, 121, 198, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(255, 121, 198, 0.3)',
      
      // Textos - tons claros para contraste
      '--iluria-color-body-fg': '#f8f8f2',
      '--iluria-color-text': '#f8f8f2',
      '--iluria-color-navbar-fg': '#f8f8f2',
      '--iluria-color-text-primary': '#f8f8f2',
      '--iluria-color-text-secondary': '#f1fa8c',
      '--iluria-color-text-muted': '#6272a4',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(255, 121, 198, 0.05)',
      '--iluria-color-focus-ring': 'rgba(255, 121, 198, 0.2)',
      
      // Bordas - tons roxos
      '--iluria-color-border': '#6272a4',
      '--iluria-color-border-hover': '#bd93f9',
      '--iluria-color-border-focus': '#ff79c6',
      
      // Estados
      '--iluria-color-success': '#50fa7b',
      '--iluria-color-warning': '#f1fa8c',
      '--iluria-color-error': '#ff5555',
      '--iluria-color-info': '#8be9fd',
      
      // Botões específicos - tema dracula
      '--iluria-color-button-primary-bg': '#ff79c6',
      '--iluria-color-button-primary-bg-hover': '#ff6bc5',
      '--iluria-color-button-primary-fg': '#282a36',
      
      '--iluria-color-button-secondary-bg': '#bd93f9',
      '--iluria-color-button-secondary-bg-hover': '#a777f7',
      '--iluria-color-button-secondary-fg': '#282a36',
      
      '--iluria-color-button-confirm-bg': '#50fa7b',
      '--iluria-color-button-confirm-bg-hover': '#2dd55b',
      '--iluria-color-button-confirm-fg': '#282a36',
      
      '--iluria-color-button-danger-bg': '#ff5555',
      '--iluria-color-button-danger-bg-hover': '#ff4444',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#44475a',
      '--iluria-color-button-dark-bg-hover': '#373844',
      '--iluria-color-button-dark-fg': '#f8f8f2',
      
      '--iluria-color-button-text-primary': '#282a36',
      
      // Inputs específicos - tema dracula
      '--iluria-color-input-bg': '#44475a',
      '--iluria-color-input-border': '#6272a4',
      '--iluria-color-input-border-focus': '#ff79c6',
      '--iluria-color-input-text': '#f8f8f2',
      '--iluria-color-input-placeholder': '#6272a4',
      
      // Switches e controles específicos - tema dracula
      '--iluria-color-switch-bg': '#6272a4',
      '--iluria-color-switch-bg-active': '#ff79c6',
      '--iluria-color-switch-handle': '#f8f8f2',
      '--iluria-color-switch-border': '#bd93f9',
      '--iluria-color-switch-border-active': '#ff79c6',
      
      '--iluria-color-checkbox-bg': '#44475a',
      '--iluria-color-checkbox-bg-active': '#ff79c6',
      '--iluria-color-checkbox-border': '#6272a4',
      '--iluria-color-checkbox-border-active': '#ff79c6',
      '--iluria-color-checkbox-check': '#282a36',
      
      '--iluria-color-select-bg': '#44475a',
      '--iluria-color-select-border': '#6272a4',
      '--iluria-color-select-border-focus': '#ff79c6',
      '--iluria-color-select-option-hover': '#373844',
      '--iluria-color-select-option-selected': '#6272a4',
      
      // Logo específico - tema dracula
      '--iluria-logo-text-color': '#f8f8f2',
      '--iluria-logo-accent-color': '#ff79c6',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.8)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.8)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.9)',
    }
  },

  pastel: {
    id: 'pastel',
    colors: {
      // Cores principais - inspirado no tema pastel do DaisyUI
      '--iluria-color-primary': '#d8b4fe',
      '--iluria-color-primary-hover': '#c084fc',
      '--iluria-color-primary-contrast': '#581c87',
      '--iluria-color-secondary': '#fda4af',
      '--iluria-color-secondary-hover': '#fb7185',
      
      // Backgrounds - tons pastéis muito suaves
      '--iluria-color-body-bg': '#fefcff',
      '--iluria-color-background': '#fefcff',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#faf5ff',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(216, 180, 254, 0.1)',
      '--iluria-color-navbar-active': 'rgba(216, 180, 254, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(216, 180, 254, 0.3)',
      
      // Textos - suaves mas legíveis
      '--iluria-color-body-fg': '#581c87',
      '--iluria-color-text': '#581c87',
      '--iluria-color-navbar-fg': '#581c87',
      '--iluria-color-text-primary': '#581c87',
      '--iluria-color-text-secondary': '#7c3aed',
      '--iluria-color-text-muted': '#a855f7',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(216, 180, 254, 0.08)',
      '--iluria-color-focus-ring': 'rgba(216, 180, 254, 0.15)',
      
      // Bordas - tons pastéis claros
      '--iluria-color-border': '#f3e8ff',
      '--iluria-color-border-hover': '#e9d5ff',
      '--iluria-color-border-focus': '#d8b4fe',
      
      // Estados
      '--iluria-color-success': '#86efac',
      '--iluria-color-warning': '#fcd34d',
      '--iluria-color-error': '#fca5a5',
      '--iluria-color-info': '#93c5fd',
      
      // Botões específicos - tema pastel
      '--iluria-color-button-primary-bg': '#d8b4fe',
      '--iluria-color-button-primary-bg-hover': '#c084fc',
      '--iluria-color-button-primary-fg': '#581c87',
      
      '--iluria-color-button-secondary-bg': '#fda4af',
      '--iluria-color-button-secondary-bg-hover': '#fb7185',
      '--iluria-color-button-secondary-fg': '#881337',
      
      '--iluria-color-button-confirm-bg': '#86efac',
      '--iluria-color-button-confirm-bg-hover': '#4ade80',
      '--iluria-color-button-confirm-fg': '#14532d',
      
      '--iluria-color-button-danger-bg': '#fca5a5',
      '--iluria-color-button-danger-bg-hover': '#f87171',
      '--iluria-color-button-danger-fg': '#7f1d1d',
      
      '--iluria-color-button-dark-bg': '#581c87',
      '--iluria-color-button-dark-bg-hover': '#3b0764',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#581c87',
      
      // Inputs específicos - tema pastel
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#f3e8ff',
      '--iluria-color-input-border-focus': '#d8b4fe',
      '--iluria-color-input-text': '#581c87',
      '--iluria-color-input-placeholder': '#a855f7',
      
      // Switches e controles específicos - tema pastel
      '--iluria-color-switch-bg': '#f3e8ff',
      '--iluria-color-switch-bg-active': '#d8b4fe',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#e9d5ff',
      '--iluria-color-switch-border-active': '#d8b4fe',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#d8b4fe',
      '--iluria-color-checkbox-border': '#e9d5ff',
      '--iluria-color-checkbox-border-active': '#d8b4fe',
      '--iluria-color-checkbox-check': '#581c87',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#f3e8ff',
      '--iluria-color-select-border-focus': '#d8b4fe',
      '--iluria-color-select-option-hover': '#faf5ff',
      '--iluria-color-select-option-selected': '#f3e8ff',
      
      // Logo específico - tema pastel
      '--iluria-logo-text-color': '#581c87',
      '--iluria-logo-accent-color': '#d8b4fe',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(216, 180, 254, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(216, 180, 254, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(216, 180, 254, 0.2)',
    }
  },

  night: {
    id: 'night',
    colors: {
      // Cores principais - inspirado no tema night do DaisyUI
      '--iluria-color-primary': '#38bdf8',
      '--iluria-color-primary-hover': '#0ea5e9',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#a78bfa',
      '--iluria-color-secondary-hover': '#8b5cf6',
      
      // Backgrounds - tons noturnos profundos
      '--iluria-color-body-bg': '#0f172a',
      '--iluria-color-background': '#0f172a',
      '--iluria-color-surface': '#1e293b',
      '--iluria-color-navbar-bg': '#1e293b',
      '--iluria-color-sidebar-bg': '#1a202c',
      '--iluria-color-container-bg': '#1e293b',
      '--iluria-color-card-bg': '#334155',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(56, 189, 248, 0.1)',
      '--iluria-color-navbar-active': 'rgba(56, 189, 248, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(56, 189, 248, 0.3)',
      
      // Textos - claros para tema noturno
      '--iluria-color-body-fg': '#f1f5f9',
      '--iluria-color-text': '#f1f5f9',
      '--iluria-color-navbar-fg': '#f1f5f9',
      '--iluria-color-text-primary': '#f1f5f9',
      '--iluria-color-text-secondary': '#cbd5e1',
      '--iluria-color-text-muted': '#94a3b8',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(56, 189, 248, 0.05)',
      '--iluria-color-focus-ring': 'rgba(56, 189, 248, 0.2)',
      
      // Bordas - tons azuis escuros
      '--iluria-color-border': '#475569',
      '--iluria-color-border-hover': '#64748b',
      '--iluria-color-border-focus': '#38bdf8',
      
      // Estados
      '--iluria-color-success': '#34d399',
      '--iluria-color-warning': '#fbbf24',
      '--iluria-color-error': '#f87171',
      '--iluria-color-info': '#60a5fa',
      
      // Botões específicos - tema night
      '--iluria-color-button-primary-bg': '#38bdf8',
      '--iluria-color-button-primary-bg-hover': '#0ea5e9',
      '--iluria-color-button-primary-fg': '#0f172a',
      
      '--iluria-color-button-secondary-bg': '#a78bfa',
      '--iluria-color-button-secondary-bg-hover': '#8b5cf6',
      '--iluria-color-button-secondary-fg': '#1e1b4b',
      
      '--iluria-color-button-confirm-bg': '#34d399',
      '--iluria-color-button-confirm-bg-hover': '#10b981',
      '--iluria-color-button-confirm-fg': '#064e3b',
      
      '--iluria-color-button-danger-bg': '#f87171',
      '--iluria-color-button-danger-bg-hover': '#ef4444',
      '--iluria-color-button-danger-fg': '#7f1d1d',
      
      '--iluria-color-button-dark-bg': '#475569',
      '--iluria-color-button-dark-bg-hover': '#334155',
      '--iluria-color-button-dark-fg': '#f1f5f9',
      
      '--iluria-color-button-text-primary': '#374151',
      
      // Inputs específicos - tema night
      '--iluria-color-input-bg': '#334155',
      '--iluria-color-input-border': '#475569',
      '--iluria-color-input-border-focus': '#38bdf8',
      '--iluria-color-input-text': '#f1f5f9',
      '--iluria-color-input-placeholder': '#94a3b8',
      
      // Switches e controles específicos - tema night
      '--iluria-color-switch-bg': '#475569',
      '--iluria-color-switch-bg-active': '#38bdf8',
      '--iluria-color-switch-handle': '#f1f5f9',
      '--iluria-color-switch-border': '#64748b',
      '--iluria-color-switch-border-active': '#38bdf8',
      
      '--iluria-color-checkbox-bg': '#334155',
      '--iluria-color-checkbox-bg-active': '#38bdf8',
      '--iluria-color-checkbox-border': '#475569',
      '--iluria-color-checkbox-border-active': '#38bdf8',
      '--iluria-color-checkbox-check': '#0f172a',
      
      '--iluria-color-select-bg': '#334155',
      '--iluria-color-select-border': '#475569',
      '--iluria-color-select-border-focus': '#38bdf8',
      '--iluria-color-select-option-hover': '#1e293b',
      '--iluria-color-select-option-selected': '#475569',
      
      // Logo específico - tema night
      '--iluria-logo-text-color': '#f1f5f9',
      '--iluria-logo-accent-color': '#38bdf8',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.9)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.9)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.95)',
    }
  },

  winter: {
    id: 'winter',
    colors: {
      // Cores principais - inspirado no tema winter do DaisyUI
      '--iluria-color-primary': '#0ea5e9',
      '--iluria-color-primary-hover': '#0284c7',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#64748b',
      '--iluria-color-secondary-hover': '#475569',
      
      // Backgrounds - tons invernais frios
      '--iluria-color-body-bg': '#f8fafc',
      '--iluria-color-background': '#f8fafc',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#f1f5f9',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(14, 165, 233, 0.08)',
      '--iluria-color-navbar-active': 'rgba(14, 165, 233, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(14, 165, 233, 0.2)',
      
      // Textos - tons frios
      '--iluria-color-body-fg': '#0f172a',
      '--iluria-color-text': '#0f172a',
      '--iluria-color-navbar-fg': '#0f172a',
      '--iluria-color-text-primary': '#0f172a',
      '--iluria-color-text-secondary': '#1e293b',
      '--iluria-color-text-muted': '#475569',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(14, 165, 233, 0.05)',
      '--iluria-color-focus-ring': 'rgba(14, 165, 233, 0.1)',
      
      // Bordas - tons azuis gelados
      '--iluria-color-border': '#e2e8f0',
      '--iluria-color-border-hover': '#cbd5e1',
      '--iluria-color-border-focus': '#0ea5e9',
      
      // Estados
      '--iluria-color-success': '#10b981',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#06b6d4',
      
      // Botões específicos - tema winter
      '--iluria-color-button-primary-bg': '#0ea5e9',
      '--iluria-color-button-primary-bg-hover': '#0284c7',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#64748b',
      '--iluria-color-button-secondary-bg-hover': '#475569',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#10b981',
      '--iluria-color-button-confirm-bg-hover': '#059669',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#0f172a',
      '--iluria-color-button-dark-bg-hover': '#020617',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#0f172a',
      
      // Inputs específicos - tema winter
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#e2e8f0',
      '--iluria-color-input-border-focus': '#0ea5e9',
      '--iluria-color-input-text': '#0f172a',
      '--iluria-color-input-placeholder': '#475569',
      
      // Switches e controles específicos - tema winter
      '--iluria-color-switch-bg': '#e2e8f0',
      '--iluria-color-switch-bg-active': '#0ea5e9',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#cbd5e1',
      '--iluria-color-switch-border-active': '#0ea5e9',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#0ea5e9',
      '--iluria-color-checkbox-border': '#cbd5e1',
      '--iluria-color-checkbox-border-active': '#0ea5e9',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#e2e8f0',
      '--iluria-color-select-border-focus': '#0ea5e9',
      '--iluria-color-select-option-hover': '#f1f5f9',
      '--iluria-color-select-option-selected': '#e2e8f0',
      
      // Logo específico - tema winter
      '--iluria-logo-text-color': '#0f172a',
      '--iluria-logo-accent-color': '#0ea5e9',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(14, 165, 233, 0.05)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(14, 165, 233, 0.1)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(14, 165, 233, 0.15)',
    }
  },

  sunset: {
    id: 'sunset',
    colors: {
      // Cores principais - inspirado no tema sunset do DaisyUI
      '--iluria-color-primary': '#f97316',
      '--iluria-color-primary-hover': '#ea580c',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#f59e0b',
      '--iluria-color-secondary-hover': '#d97706',
      
      // Backgrounds - tons de pôr do sol
      '--iluria-color-body-bg': '#fff7ed',
      '--iluria-color-background': '#fff7ed',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#fed7aa',
      '--iluria-color-sidebar-bg': '#ffedd5',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(249, 115, 22, 0.1)',
      '--iluria-color-navbar-active': 'rgba(249, 115, 22, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(249, 115, 22, 0.3)',
      
      // Textos
      '--iluria-color-body-fg': '#9a3412',
      '--iluria-color-text': '#9a3412',
      '--iluria-color-navbar-fg': '#9a3412',
      '--iluria-color-text-primary': '#9a3412',
      '--iluria-color-text-secondary': '#c2410c',
      '--iluria-color-text-muted': '#ea580c',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(249, 115, 22, 0.08)',
      '--iluria-color-focus-ring': 'rgba(249, 115, 22, 0.15)',
      
      // Bordas - tons quentes
      '--iluria-color-border': '#fed7aa',
      '--iluria-color-border-hover': '#fdba74',
      '--iluria-color-border-focus': '#f97316',
      
      // Estados
      '--iluria-color-success': '#22c55e',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#06b6d4',
      
      // Botões específicos - tema sunset
      '--iluria-color-button-primary-bg': '#f97316',
      '--iluria-color-button-primary-bg-hover': '#ea580c',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#f59e0b',
      '--iluria-color-button-secondary-bg-hover': '#d97706',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#22c55e',
      '--iluria-color-button-confirm-bg-hover': '#16a34a',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#9a3412',
      '--iluria-color-button-dark-bg-hover': '#7c2d12',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#9a3412',
      
      // Inputs específicos - tema sunset
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#fed7aa',
      '--iluria-color-input-border-focus': '#f97316',
      '--iluria-color-input-text': '#9a3412',
      '--iluria-color-input-placeholder': '#ea580c',
      
      // Switches e controles específicos - tema sunset
      '--iluria-color-switch-bg': '#fed7aa',
      '--iluria-color-switch-bg-active': '#f97316',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#fdba74',
      '--iluria-color-switch-border-active': '#f97316',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#f97316',
      '--iluria-color-checkbox-border': '#fdba74',
      '--iluria-color-checkbox-border-active': '#f97316',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#fed7aa',
      '--iluria-color-select-border-focus': '#f97316',
      '--iluria-color-select-option-hover': '#ffedd5',
      '--iluria-color-select-option-selected': '#fed7aa',
      
      // Logo específico - tema sunset
      '--iluria-logo-text-color': '#9a3412',
      '--iluria-logo-accent-color': '#f97316',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(249, 115, 22, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(249, 115, 22, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(249, 115, 22, 0.2)',
    }
  },

  nord: {
    id: 'nord',
    colors: {
      // Cores principais - inspirado no tema nord do DaisyUI
      '--iluria-color-primary': '#5e81ac',
      '--iluria-color-primary-hover': '#4c566a',
      '--iluria-color-primary-contrast': '#eceff4',
      '--iluria-color-secondary': '#81a1c1',
      '--iluria-color-secondary-hover': '#5e81ac',
      
      // Backgrounds - tons nórdicos frios
      '--iluria-color-body-bg': '#eceff4',
      '--iluria-color-background': '#eceff4',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#e5e9f0',
      '--iluria-color-sidebar-bg': '#d8dee9',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(94, 129, 172, 0.1)',
      '--iluria-color-navbar-active': 'rgba(94, 129, 172, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(94, 129, 172, 0.3)',
      
      // Textos - tons nórdicos escuros
      '--iluria-color-body-fg': '#2e3440',
      '--iluria-color-text': '#2e3440',
      '--iluria-color-navbar-fg': '#2e3440',
      '--iluria-color-text-primary': '#2e3440',
      '--iluria-color-text-secondary': '#3b4252',
      '--iluria-color-text-muted': '#434c5e',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(94, 129, 172, 0.08)',
      '--iluria-color-focus-ring': 'rgba(94, 129, 172, 0.15)',
      
      // Bordas - tons nórdicos
      '--iluria-color-border': '#d8dee9',
      '--iluria-color-border-hover': '#4c566a',
      '--iluria-color-border-focus': '#5e81ac',
      
      // Estados
      '--iluria-color-success': '#a3be8c',
      '--iluria-color-warning': '#ebcb8b',
      '--iluria-color-error': '#bf616a',
      '--iluria-color-info': '#88c0d0',
      
      // Botões específicos - tema nord
      '--iluria-color-button-primary-bg': '#5e81ac',
      '--iluria-color-button-primary-bg-hover': '#4c566a',
      '--iluria-color-button-primary-fg': '#eceff4',
      
      '--iluria-color-button-secondary-bg': '#81a1c1',
      '--iluria-color-button-secondary-bg-hover': '#5e81ac',
      '--iluria-color-button-secondary-fg': '#2e3440',
      
      '--iluria-color-button-confirm-bg': '#a3be8c',
      '--iluria-color-button-confirm-bg-hover': '#8fbf7f',
      '--iluria-color-button-confirm-fg': '#2e3440',
      
      '--iluria-color-button-danger-bg': '#bf616a',
      '--iluria-color-button-danger-bg-hover': '#a54247',
      '--iluria-color-button-danger-fg': '#eceff4',
      
      '--iluria-color-button-dark-bg': '#2e3440',
      '--iluria-color-button-dark-bg-hover': '#3b4252',
      '--iluria-color-button-dark-fg': '#eceff4',
      
      '--iluria-color-button-text-primary': '#2e3440',
      
      // Inputs específicos - tema nord
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#d8dee9',
      '--iluria-color-input-border-focus': '#5e81ac',
      '--iluria-color-input-text': '#2e3440',
      '--iluria-color-input-placeholder': '#434c5e',
      
      // Switches e controles específicos - tema nord
      '--iluria-color-switch-bg': '#d8dee9',
      '--iluria-color-switch-bg-active': '#5e81ac',
      '--iluria-color-switch-handle': '#eceff4',
      '--iluria-color-switch-border': '#4c566a',
      '--iluria-color-switch-border-active': '#5e81ac',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#5e81ac',
      '--iluria-color-checkbox-border': '#d8dee9',
      '--iluria-color-checkbox-border-active': '#5e81ac',
      '--iluria-color-checkbox-check': '#eceff4',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#d8dee9',
      '--iluria-color-select-border-focus': '#5e81ac',
      '--iluria-color-select-option-hover': '#e5e9f0',
      '--iluria-color-select-option-selected': '#d8dee9',
      
      // Logo específico - tema nord
      '--iluria-logo-text-color': '#2e3440',
      '--iluria-logo-accent-color': '#5e81ac',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(94, 129, 172, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(94, 129, 172, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(94, 129, 172, 0.2)',
    }
  },

  dim: {
    id: 'dim',
    colors: {
      // Cores principais - inspirado no tema dim do DaisyUI
      '--iluria-color-primary': '#9ca3af',
      '--iluria-color-primary-hover': '#6b7280',
      '--iluria-color-primary-contrast': '#111827',
      '--iluria-color-secondary': '#4b5563',
      '--iluria-color-secondary-hover': '#374151',
      
      // Backgrounds - tons escuros suaves
      '--iluria-color-body-bg': '#1f2937',
      '--iluria-color-background': '#1f2937',
      '--iluria-color-surface': '#374151',
      '--iluria-color-navbar-bg': '#374151',
      '--iluria-color-sidebar-bg': '#2d3748',
      '--iluria-color-container-bg': '#374151',
      '--iluria-color-card-bg': '#4b5563',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(156, 163, 175, 0.1)',
      '--iluria-color-navbar-active': 'rgba(156, 163, 175, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(156, 163, 175, 0.3)',
      
      // Textos - claros mas suaves
      '--iluria-color-body-fg': '#f9fafb',
      '--iluria-color-text': '#f9fafb',
      '--iluria-color-navbar-fg': '#f9fafb',
      '--iluria-color-text-primary': '#f9fafb',
      '--iluria-color-text-secondary': '#d1d5db',
      '--iluria-color-text-muted': '#9ca3af',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(156, 163, 175, 0.05)',
      '--iluria-color-focus-ring': 'rgba(156, 163, 175, 0.2)',
      
      // Bordas - tons médios
      '--iluria-color-border': '#6b7280',
      '--iluria-color-border-hover': '#9ca3af',
      '--iluria-color-border-focus': '#9ca3af',
      
      // Estados
      '--iluria-color-success': '#34d399',
      '--iluria-color-warning': '#fbbf24',
      '--iluria-color-error': '#f87171',
      '--iluria-color-info': '#60a5fa',
      
      // Botões específicos - tema dim
      '--iluria-color-button-primary-bg': '#9ca3af',
      '--iluria-color-button-primary-bg-hover': '#6b7280',
      '--iluria-color-button-primary-fg': '#111827',
      
      '--iluria-color-button-secondary-bg': '#4b5563',
      '--iluria-color-button-secondary-bg-hover': '#374151',
      '--iluria-color-button-secondary-fg': '#f9fafb',
      
      '--iluria-color-button-confirm-bg': '#34d399',
      '--iluria-color-button-confirm-bg-hover': '#10b981',
      '--iluria-color-button-confirm-fg': '#064e3b',
      
      '--iluria-color-button-danger-bg': '#f87171',
      '--iluria-color-button-danger-bg-hover': '#ef4444',
      '--iluria-color-button-danger-fg': '#7f1d1d',
      
      '--iluria-color-button-dark-bg': '#6b7280',
      '--iluria-color-button-dark-bg-hover': '#4b5563',
      '--iluria-color-button-dark-fg': '#f9fafb',
      
      '--iluria-color-button-text-primary': '#374151',
      
      // Inputs específicos - tema dim
      '--iluria-color-input-bg': '#4b5563',
      '--iluria-color-input-border': '#6b7280',
      '--iluria-color-input-border-focus': '#9ca3af',
      '--iluria-color-input-text': '#f9fafb',
      '--iluria-color-input-placeholder': '#9ca3af',
      
      // Switches e controles específicos - tema dim
      '--iluria-color-switch-bg': '#6b7280',
      '--iluria-color-switch-bg-active': '#9ca3af',
      '--iluria-color-switch-handle': '#f9fafb',
      '--iluria-color-switch-border': '#4b5563',
      '--iluria-color-switch-border-active': '#9ca3af',
      
      '--iluria-color-checkbox-bg': '#4b5563',
      '--iluria-color-checkbox-bg-active': '#9ca3af',
      '--iluria-color-checkbox-border': '#6b7280',
      '--iluria-color-checkbox-border-active': '#9ca3af',
      '--iluria-color-checkbox-check': '#111827',
      
      '--iluria-color-select-bg': '#4b5563',
      '--iluria-color-select-border': '#6b7280',
      '--iluria-color-select-border-focus': '#9ca3af',
      '--iluria-color-select-option-hover': '#374151',
      '--iluria-color-select-option-selected': '#6b7280',
      
      // Logo específico - tema dim
      '--iluria-logo-text-color': '#f9fafb',
      '--iluria-logo-accent-color': '#9ca3af',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 0.6)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 0.6)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 0.7)',
    }
  },

  abyss: {
    id: 'abyss',
    colors: {
      // Cores principais - inspirado no tema abyss do DaisyUI
      '--iluria-color-primary': '#3b82f6',
      '--iluria-color-primary-hover': '#2563eb',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#6366f1',
      '--iluria-color-secondary-hover': '#4f46e5',
      
      // Backgrounds - tons abissais profundos
      '--iluria-color-body-bg': '#000000',
      '--iluria-color-background': '#000000',
      '--iluria-color-surface': '#111111',
      '--iluria-color-navbar-bg': '#111111',
      '--iluria-color-sidebar-bg': '#0a0a0a',
      '--iluria-color-container-bg': '#111111',
      '--iluria-color-card-bg': '#1a1a1a',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(59, 130, 246, 0.1)',
      '--iluria-color-navbar-active': 'rgba(59, 130, 246, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(59, 130, 246, 0.3)',
      
      // Textos - claros para máximo contraste
      '--iluria-color-body-fg': '#ffffff',
      '--iluria-color-text': '#ffffff',
      '--iluria-color-navbar-fg': '#ffffff',
      '--iluria-color-text-primary': '#ffffff',
      '--iluria-color-text-secondary': '#e5e7eb',
      '--iluria-color-text-muted': '#9ca3af',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(59, 130, 246, 0.05)',
      '--iluria-color-focus-ring': 'rgba(59, 130, 246, 0.3)',
      
      // Bordas - tons muito escuros
      '--iluria-color-border': '#333333',
      '--iluria-color-border-hover': '#444444',
      '--iluria-color-border-focus': '#3b82f6',
      
      // Estados
      '--iluria-color-success': '#10b981',
      '--iluria-color-warning': '#f59e0b',
      '--iluria-color-error': '#ef4444',
      '--iluria-color-info': '#06b6d4',
      
      // Botões específicos - tema abyss
      '--iluria-color-button-primary-bg': '#3b82f6',
      '--iluria-color-button-primary-bg-hover': '#2563eb',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#6366f1',
      '--iluria-color-button-secondary-bg-hover': '#4f46e5',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#10b981',
      '--iluria-color-button-confirm-bg-hover': '#059669',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#ef4444',
      '--iluria-color-button-danger-bg-hover': '#dc2626',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#333333',
      '--iluria-color-button-dark-bg-hover': '#222222',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#06b6d4',
      
      // Inputs específicos - tema abyss
      '--iluria-color-input-bg': '#1a1a1a',
      '--iluria-color-input-border': '#333333',
      '--iluria-color-input-border-focus': '#3b82f6',
      '--iluria-color-input-text': '#ffffff',
      '--iluria-color-input-placeholder': '#9ca3af',
      
      // Switches e controles específicos - tema abyss
      '--iluria-color-switch-bg': '#333333',
      '--iluria-color-switch-bg-active': '#3b82f6',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#444444',
      '--iluria-color-switch-border-active': '#3b82f6',
      
      '--iluria-color-checkbox-bg': '#1a1a1a',
      '--iluria-color-checkbox-bg-active': '#3b82f6',
      '--iluria-color-checkbox-border': '#333333',
      '--iluria-color-checkbox-border-active': '#3b82f6',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#1a1a1a',
      '--iluria-color-select-border': '#333333',
      '--iluria-color-select-border-focus': '#3b82f6',
      '--iluria-color-select-option-hover': '#111111',
      '--iluria-color-select-option-selected': '#333333',
      
      // Logo específico - tema abyss
      '--iluria-logo-text-color': '#ffffff',
      '--iluria-logo-accent-color': '#3b82f6',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(0, 0, 0, 1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(0, 0, 0, 1)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(0, 0, 0, 1)',
    }
  },

  silk: {
    id: 'silk',
    colors: {
      // Cores principais - inspirado no tema silk do DaisyUI
      '--iluria-color-primary': '#be185d',
      '--iluria-color-primary-hover': '#9d174d',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#6b7280',
      '--iluria-color-secondary-hover': '#4b5563',
      
      // Backgrounds - tons sedosos suaves
      '--iluria-color-body-bg': '#fefbff',
      '--iluria-color-background': '#fefbff',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#fdf4ff',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(190, 24, 93, 0.08)',
      '--iluria-color-navbar-active': 'rgba(190, 24, 93, 0.15)',
      '--iluria-color-navbar-active-hover': 'rgba(190, 24, 93, 0.2)',
      
      // Textos - tons profundos mas suaves
      '--iluria-color-body-fg': '#4a044e',
      '--iluria-color-text': '#4a044e',
      '--iluria-color-navbar-fg': '#4a044e',
      '--iluria-color-text-primary': '#4a044e',
      '--iluria-color-text-secondary': '#701a75',
      '--iluria-color-text-muted': '#9d174d',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(190, 24, 93, 0.05)',
      '--iluria-color-focus-ring': 'rgba(190, 24, 93, 0.1)',
      
      // Bordas - tons sedosos
      '--iluria-color-border': '#f5d0fe',
      '--iluria-color-border-hover': '#f0abfc',
      '--iluria-color-border-focus': '#be185d',
      
      // Estados
      '--iluria-color-success': '#16a34a',
      '--iluria-color-warning': '#ca8a04',
      '--iluria-color-error': '#dc2626',
      '--iluria-color-info': '#0891b2',
      
      // Botões específicos - tema silk
      '--iluria-color-button-primary-bg': '#be185d',
      '--iluria-color-button-primary-bg-hover': '#9d174d',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#6b7280',
      '--iluria-color-button-secondary-bg-hover': '#4b5563',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#16a34a',
      '--iluria-color-button-confirm-bg-hover': '#15803d',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#dc2626',
      '--iluria-color-button-danger-bg-hover': '#b91c1c',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#4a044e',
      '--iluria-color-button-dark-bg-hover': '#2e1065',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#3f172f',
      
      // Inputs específicos - tema silk
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#f5d0fe',
      '--iluria-color-input-border-focus': '#be185d',
      '--iluria-color-input-text': '#4a044e',
      '--iluria-color-input-placeholder': '#9d174d',
      
      // Switches e controles específicos - tema silk
      '--iluria-color-switch-bg': '#f5d0fe',
      '--iluria-color-switch-bg-active': '#be185d',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#f0abfc',
      '--iluria-color-switch-border-active': '#be185d',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#be185d',
      '--iluria-color-checkbox-border': '#f0abfc',
      '--iluria-color-checkbox-border-active': '#be185d',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#f5d0fe',
      '--iluria-color-select-border-focus': '#be185d',
      '--iluria-color-select-option-hover': '#fdf4ff',
      '--iluria-color-select-option-selected': '#f5d0fe',
      
      // Logo específico - tema silk
      '--iluria-logo-text-color': '#4a044e',
      '--iluria-logo-accent-color': '#be185d',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(190, 24, 93, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(190, 24, 93, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(190, 24, 93, 0.2)',
    }
  },

  fantasy: {
    id: 'fantasy',
    colors: {
      // Cores principais - inspirado no tema fantasy do DaisyUI
      '--iluria-color-primary': '#7c3aed',
      '--iluria-color-primary-hover': '#6d28d9',
      '--iluria-color-primary-contrast': '#ffffff',
      '--iluria-color-secondary': '#ec4899',
      '--iluria-color-secondary-hover': '#db2777',
      
      // Backgrounds - tons mágicos
      '--iluria-color-body-bg': '#faf5ff',
      '--iluria-color-background': '#faf5ff',
      '--iluria-color-surface': '#ffffff',
      '--iluria-color-navbar-bg': '#ffffff',
      '--iluria-color-sidebar-bg': '#faf5ff',
      '--iluria-color-container-bg': '#ffffff',
      '--iluria-color-card-bg': '#ffffff',
      
      // Navbar estados específicos
      '--iluria-color-navbar-hover': 'rgba(124, 58, 237, 0.1)',
      '--iluria-color-navbar-active': 'rgba(124, 58, 237, 0.2)',
      '--iluria-color-navbar-active-hover': 'rgba(124, 58, 237, 0.3)',
      
      // Textos - tons mágicos profundos
      '--iluria-color-body-fg': '#3c1361',
      '--iluria-color-text': '#3c1361',
      '--iluria-color-navbar-fg': '#3c1361',
      '--iluria-color-text-primary': '#3c1361',
      '--iluria-color-text-secondary': '#581c87',
      '--iluria-color-text-muted': '#7c3aed',
      
      // Estados interativos
      '--iluria-color-hover': 'rgba(124, 58, 237, 0.05)',
      '--iluria-color-focus-ring': 'rgba(124, 58, 237, 0.1)',
      
      // Bordas - tons mágicos
      '--iluria-color-border': '#f3e8ff',
      '--iluria-color-border-hover': '#ddd6fe',
      '--iluria-color-border-focus': '#7c3aed',
      
      // Estados
      '--iluria-color-success': '#16a34a',
      '--iluria-color-warning': '#ca8a04',
      '--iluria-color-error': '#dc2626',
      '--iluria-color-info': '#0891b2',
      
      // Botões específicos - tema fantasy
      '--iluria-color-button-primary-bg': '#7c3aed',
      '--iluria-color-button-primary-bg-hover': '#6d28d9',
      '--iluria-color-button-primary-fg': '#ffffff',
      
      '--iluria-color-button-secondary-bg': '#ec4899',
      '--iluria-color-button-secondary-bg-hover': '#db2777',
      '--iluria-color-button-secondary-fg': '#ffffff',
      
      '--iluria-color-button-confirm-bg': '#16a34a',
      '--iluria-color-button-confirm-bg-hover': '#15803d',
      '--iluria-color-button-confirm-fg': '#ffffff',
      
      '--iluria-color-button-danger-bg': '#dc2626',
      '--iluria-color-button-danger-bg-hover': '#b91c1c',
      '--iluria-color-button-danger-fg': '#ffffff',
      
      '--iluria-color-button-dark-bg': '#3c1361',
      '--iluria-color-button-dark-bg-hover': '#2e1065',
      '--iluria-color-button-dark-fg': '#ffffff',
      
      '--iluria-color-button-text-primary': '#3c1361',
      
      // Inputs específicos - tema fantasy
      '--iluria-color-input-bg': '#ffffff',
      '--iluria-color-input-border': '#f3e8ff',
      '--iluria-color-input-border-focus': '#7c3aed',
      '--iluria-color-input-text': '#3c1361',
      '--iluria-color-input-placeholder': '#9d174d',
      
      // Switches e controles específicos - tema fantasy
      '--iluria-color-switch-bg': '#f3e8ff',
      '--iluria-color-switch-bg-active': '#7c3aed',
      '--iluria-color-switch-handle': '#ffffff',
      '--iluria-color-switch-border': '#ddd6fe',
      '--iluria-color-switch-border-active': '#7c3aed',
      
      '--iluria-color-checkbox-bg': '#ffffff',
      '--iluria-color-checkbox-bg-active': '#7c3aed',
      '--iluria-color-checkbox-border': '#ddd6fe',
      '--iluria-color-checkbox-border-active': '#7c3aed',
      '--iluria-color-checkbox-check': '#ffffff',
      
      '--iluria-color-select-bg': '#ffffff',
      '--iluria-color-select-border': '#f3e8ff',
      '--iluria-color-select-border-focus': '#7c3aed',
      '--iluria-color-select-option-hover': '#faf5ff',
      '--iluria-color-select-option-selected': '#f3e8ff',
      
      // Logo específico - tema fantasy
      '--iluria-logo-text-color': '#3c1361',
      '--iluria-logo-accent-color': '#7c3aed',
      
      // Shadows
      '--iluria-shadow-sm': '0 1px 2px 0 rgba(124, 58, 237, 0.1)',
      '--iluria-shadow-md': '0 4px 6px -1px rgba(124, 58, 237, 0.15)',
      '--iluria-shadow-lg': '0 10px 15px -3px rgba(124, 58, 237, 0.2)',
    }
  }
}

// Estado global do tema
const currentThemeId = ref('light')
const isTransitioning = ref(false)

export function useTheme() {
  // Computed para o tema atual
  const currentTheme = computed(() => THEMES[currentThemeId.value] || THEMES.light)
  
  // Lista de temas disponíveis
  const availableThemes = computed(() => Object.values(THEMES))
  
  // Função para aplicar tema
  const applyTheme = (themeId) => {
    const theme = THEMES[themeId]
    if (!theme) return
    
    isTransitioning.value = true
    
    // Aplicar CSS variables no :root
    const root = document.documentElement
    Object.entries(theme.colors).forEach(([property, value]) => {
      root.style.setProperty(property, value)
    })
    
    // Adicionar classe do tema ao body
    document.body.className = document.body.className
      .replace(/theme-\w+/g, '')
      .trim()
    document.body.classList.add(`theme-${themeId}`)
    
    // Forçar refresh de todos os inputs e elementos
    forceRefreshElements()
    
    // Salvar preferência
    localStorage.setItem('iluria-theme', themeId)
    
    // Finalizar transição
    setTimeout(() => {
      isTransitioning.value = false
    }, 300)
  }
  
  // Função para forçar refresh dos elementos
  const forceRefreshElements = () => {
    // Força todos os inputs a re-renderizar suas CSS variables
    const inputs = document.querySelectorAll('input, textarea, select, .p-inputtext, .p-inputnumber-input, .p-inputmask')
    inputs.forEach(input => {
      // Força recálculo das CSS variables
      const currentStyle = input.style.cssText
      input.style.cssText = currentStyle + '; --force-refresh: 1;'
      
      // Remove após um frame para forçar re-render
      requestAnimationFrame(() => {
        input.style.removeProperty('--force-refresh')
      })
    })
    
    // Força re-render dos botões IluriaButton
    const buttons = document.querySelectorAll('.btn, button')
    buttons.forEach(button => {
      // Força recálculo das CSS variables
      const currentStyle = button.style.cssText
      button.style.cssText = currentStyle + '; --force-refresh: 1;'
      
      // Remove após um frame para forçar re-render
      requestAnimationFrame(() => {
        button.style.removeProperty('--force-refresh')
      })
    })
    
    // Força re-render do navbar
    const navbar = document.querySelector('.navbar-themed')
    if (navbar) {
      navbar.style.cssText = navbar.style.cssText + '; --force-refresh: 1;'
      requestAnimationFrame(() => {
        navbar.style.removeProperty('--force-refresh')
      })
    }
  }
  
  // Função para mudar tema
  const setTheme = (themeId) => {
    if (currentThemeId.value === themeId) return
    currentThemeId.value = themeId
    applyTheme(themeId)
  }
  
  // Função para toggle entre claro/escuro
  const toggleTheme = () => {
    const newTheme = currentThemeId.value === 'light' ? 'dark' : 'light'
    setTheme(newTheme)
  }
  
  // Função para detectar preferência do sistema
  const getSystemTheme = () => {
    if (typeof window !== 'undefined' && window.matchMedia) {
      return window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' : 'light'
    }
    return 'light'
  }
  
  // Função para carregar tema salvo
  const loadSavedTheme = () => {
    const saved = localStorage.getItem('iluria-theme')
    if (saved && THEMES[saved]) {
      return saved
    }
    return getSystemTheme()
  }
  
  // Inicializar tema
  const initTheme = () => {
    const themeId = loadSavedTheme()
    currentThemeId.value = themeId
    applyTheme(themeId)
  }
  
  // Watch para mudanças no tema
  watch(currentThemeId, (newThemeId) => {
    applyTheme(newThemeId)
  })
  
  // Retornar interface pública
  return {
    // Estado
    currentTheme,
    currentThemeId: computed(() => currentThemeId.value),
    availableThemes,
    isTransitioning,
    
    // Métodos
    setTheme,
    toggleTheme,
    initTheme,
    
    // Utils
    getSystemTheme
  }
}

// Instância global para uso em componentes que não usam composition API
let globalThemeInstance = null

export function getGlobalTheme() {
  if (!globalThemeInstance) {
    globalThemeInstance = useTheme()
  }
  return globalThemeInstance
} 