<template>
  <div class="iluria-color-picker">
    <IluriaLabel v-if="label" :for="colorPickerId" class="mb-2">
      {{ label }}
    </IluriaLabel>
    
    <div class="color-picker-container">
      <!-- Botão de preview que abre o picker -->
      <button
        type="button"
        @click="togglePicker"
        class="color-preview-button"
        :style="{ backgroundColor: currentColor }"
        :title="`Cor atual: ${currentColor}`"
        :disabled="disabled"
      >
        <div class="color-preview-content">
          <svg v-if="currentColor === 'transparent'" 
               class="transparent-icon" 
               viewBox="0 0 24 24" 
               fill="none" 
               stroke="currentColor">
            <path d="M18 6L6 18M6 6l12 12"/>
          </svg>
        </div>

      </button>

      <!-- Picker dropdown -->
      <div v-show="isPickerOpen" :class="['color-picker-dropdown', { 'is-dragging': isDragging }]" ref="pickerDropdown">
        
        <div class="picker-main-content">
          <!-- Canvas de seleção -->
          <div class="color-canvas-container">
            <canvas
              ref="colorCanvas"
              :class="['color-canvas', { dragging: isDragging && dragType === 'color' }]"
              width="180"
              height="140"
              @mousedown="startColorSelection"
              
            />
            <div 
              :class="['color-selector-dot', { dragging: isDragging && dragType === 'color' }]"
              :style="{ left: selectorPosition.x + 'px', top: selectorPosition.y + 'px' }"
              @mousedown="startDotDrag"
            />
          </div>

          <!-- Barras laterais -->
          <div class="side-bars">
            <!-- Barra de matiz -->
            <div class="hue-bar-container">
              <canvas
                ref="hueCanvas"
                class="hue-bar vertical"
                width="12"
                height="140"
                @mousedown="startHueSelection"
                @mousemove="updateHueSelection"
                @mouseup="endHueSelection"
              />
              <div 
                class="hue-selector vertical"
                :style="{ top: huePosition + 'px' }"
              />
            </div>

            <!-- Barra de opacidade -->
            <div class="opacity-bar-container">
              <canvas
                ref="opacityCanvas"
                class="opacity-bar vertical"
                width="12"
                height="140"
                @mousedown="startOpacitySelection"
                @mousemove="updateOpacitySelection"
                @mouseup="endOpacitySelection"
              />
              <div 
                class="opacity-selector vertical"
                :style="{ top: opacityPosition + 'px' }"
              />
            </div>
          </div>
        </div>

        <!-- Input com botão de modo integrado -->
        <div class="color-input-section">
          <div class="input-with-mode">
            <!-- Input HEX -->
            <div v-if="inputMode === 'hex'" class="input-container">
              <input
                v-model="hexValue"
                type="text"
                class="color-input main-input"
                placeholder="#000000"
                @input="handleHexInput"
              />
              <button
                type="button"
                @click="toggleInputMode"
                @mousedown.stop
                class="mode-toggle-button"
              >
                HEX
              </button>
            </div>

            <!-- Inputs RGB -->
            <div v-else class="input-container rgb-container">
              <div class="rgb-inputs">
                <input
                  v-model.number="rgbValue.r"
                  type="number"
                  min="0"
                  max="255"
                  class="color-input rgb-input"
                  @input="handleRgbInput"
                  placeholder="R"
                />
                <input
                  v-model.number="rgbValue.g"
                  type="number"
                  min="0"
                  max="255"
                  class="color-input rgb-input"
                  @input="handleRgbInput"
                  placeholder="G"
                />
                <input
                  v-model.number="rgbValue.b"
                  type="number"
                  min="0"
                  max="255"
                  class="color-input rgb-input"
                  @input="handleRgbInput"
                  placeholder="B"
                />
              </div>
              <button
                type="button"
                @click="toggleInputMode"
                @mousedown.stop
                class="mode-toggle-button"
              >
                RGB
              </button>
            </div>
          </div>
        </div>

        <!-- Cores rápidas do tema -->
        <div class="quick-colors">
          <div class="quick-colors-label">Cores do tema:</div>
          <div class="quick-colors-grid">
            <button
              v-for="themeColor in themeColors"
              :key="themeColor.name"
              type="button"
              class="quick-color-button"
              :style="{ backgroundColor: themeColor.value }"
              @click="selectColor(themeColor.value)"
              :title="themeColor.name"
            />
          </div>
        </div>

      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick, watch } from 'vue'
import IluriaLabel from '../IluriaLabel.vue'

const props = defineProps({
  modelValue: {
    type: String,
    default: '#000000'
  },
  label: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  }
})

const emit = defineEmits(['update:modelValue'])

// Refs
const colorCanvas = ref(null)
const hueCanvas = ref(null)
const opacityCanvas = ref(null)
const pickerDropdown = ref(null)

// Estado
const isPickerOpen = ref(false)
const inputMode = ref('hex')
const isDragging = ref(false)
const dragType = ref('')

// Valores de cor
const hue = ref(0)
const saturation = ref(100)
const lightness = ref(50)
const opacity = ref(1)

// Posições dos seletores
const selectorPosition = ref({ x: 180, y: 0 })
const huePosition = ref(0)
const opacityPosition = ref(0)

// ID único
const colorPickerId = `color-picker-${Math.random().toString(36).substring(2, 11)}`

// Paleta de cores otimizada (16 cores para caber perfeitamente em 2 linhas)
const themeColors = ref([
  { name: 'Branco', value: '#ffffff' },
  { name: 'Cinza Claro', value: '#f3f4f6' },
  { name: 'Cinza Médio', value: '#9ca3af' },
  { name: 'Cinza Escuro', value: '#374151' },
  { name: 'Preto', value: '#000000' },
  { name: 'Azul', value: '#3b82f6' },
  { name: 'Azul Escuro', value: '#1e40af' },
  { name: 'Verde', value: '#10b981' },
  { name: 'Verde Escuro', value: '#047857' },
  { name: 'Amarelo', value: '#f59e0b' },
  { name: 'Laranja', value: '#ea580c' },
  { name: 'Vermelho', value: '#ef4444' },
  { name: 'Vermelho Escuro', value: '#dc2626' },
  { name: 'Rosa', value: '#ec4899' },
  { name: 'Roxo', value: '#a855f7' },
  { name: 'Ciano', value: '#06b6d4' }
])

// Valores computados
const currentColor = computed(() => {
  if (props.modelValue === 'transparent') return 'transparent'
  return props.modelValue
})

const hexValue = computed({
  get() {
    if (props.modelValue === 'transparent') return 'transparent'
    
    // Se for RGBA, extrair apenas a parte RGB e converter para HEX
    if (props.modelValue.startsWith('rgba(')) {
      const rgbaMatch = props.modelValue.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/)
      if (rgbaMatch) {
        const r = parseInt(rgbaMatch[1])
        const g = parseInt(rgbaMatch[2])
        const b = parseInt(rgbaMatch[3])
        return rgbToHex(r, g, b)
      }
    }
    
    return props.modelValue
  },
  set(value) {
    if (value === 'transparent') {
      emit('update:modelValue', value)
    } else if (isValidHex(value)) {
      // Se há opacidade, manter a opacidade com a nova cor
      if (opacity.value < 1) {
        const rgb = hexToRgb(value)
        if (rgb) {
          const rgba = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity.value})`
          emit('update:modelValue', rgba)
        }
      } else {
        emit('update:modelValue', value)
      }
    }
  }
})

const rgbValue = computed({
  get() {
    if (props.modelValue === 'transparent') return { r: 0, g: 0, b: 0 }
    
    // Se for RGBA, extrair os valores RGB
    if (props.modelValue.startsWith('rgba(')) {
      const rgbaMatch = props.modelValue.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*[\d.]+\)/)
      if (rgbaMatch) {
        return {
          r: parseInt(rgbaMatch[1]),
          g: parseInt(rgbaMatch[2]),
          b: parseInt(rgbaMatch[3])
        }
      }
    }
    
    // Se for HEX, converter para RGB
    const rgb = hexToRgb(props.modelValue)
    return rgb || { r: 0, g: 0, b: 0 }
  },
  set(value) {
    // Se há opacidade, criar RGBA
    if (opacity.value < 1) {
      const rgba = `rgba(${value.r}, ${value.g}, ${value.b}, ${opacity.value})`
      emit('update:modelValue', rgba)
    } else {
      // Senão, criar HEX
      const hex = rgbToHex(value.r, value.g, value.b)
      emit('update:modelValue', hex)
    }
  }
})

// Sistema global para gerenciar color pickers
const globalColorPickerManager = {
  instances: new Set(),
  
  register(instance) {
    this.instances.add(instance)
  },
  
  unregister(instance) {
    this.instances.delete(instance)
  },
  
  closeAll(except = null) {
    this.instances.forEach(instance => {
      if (instance !== except) {
        instance.close()
      }
    })
  }
}

// Métodos principais
const togglePicker = (e) => {
  if (e) {
    e.preventDefault()
    e.stopPropagation()
  }
  
  if (!props.disabled) {
    if (isPickerOpen.value) {
      closePicker()
    } else {
      // Fecha todos os outros color pickers antes de abrir este
      globalColorPickerManager.closeAll(pickerInstance)
      
      isPickerOpen.value = true
      
      nextTick(() => {
        initializeCanvases()
        updateFromCurrentColor()
      })
    }
  }
}

const closePicker = () => {
  isPickerOpen.value = false
}

const selectColor = (color) => {
  emit('update:modelValue', color)
  if (color !== 'transparent') {
    updateFromCurrentColor()
  }
}

const toggleInputMode = (e) => {
  e.preventDefault()
  e.stopPropagation()
  inputMode.value = inputMode.value === 'hex' ? 'rgb' : 'hex'
}

// Inicialização dos canvas
const initializeCanvases = () => {
  drawColorCanvas()
  drawHueBar()
  drawOpacityBar()
}

const drawColorCanvas = () => {
  const canvas = colorCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height
  
  // Limpa o canvas
  ctx.clearRect(0, 0, width, height)
  
  // CORREÇÃO: Gradiente com ajuste para corrigir problema na borda direita
  
  // 1. Base: Gradiente horizontal de saturação (branco para cor pura)
  // Usar width-1 para garantir que a cor pura apareça na borda direita
  const satGradient = ctx.createLinearGradient(0, 0, width - 1, 0)
  satGradient.addColorStop(0, '#ffffff')
  satGradient.addColorStop(1, `hsl(${hue.value}, 100%, 50%)`)
  
  ctx.fillStyle = satGradient
  ctx.fillRect(0, 0, width, height)
  
  // 2. Sobreposição: Gradiente vertical de luminosidade (transparente para preto)
  const lightGradient = ctx.createLinearGradient(0, 0, 0, height - 1)
  lightGradient.addColorStop(0, 'rgba(0,0,0,0)')
  lightGradient.addColorStop(1, 'rgba(0,0,0,1)')
  
  ctx.fillStyle = lightGradient
  ctx.fillRect(0, 0, width, height)
  
  // 3. CORREÇÃO ESPECÍFICA: Desenhar linha vertical na borda direita com a cor pura
  const pureColor = `hsl(${hue.value}, 100%, 50%)`
  
  // Criar gradiente vertical apenas para a última coluna
  const rightEdgeGradient = ctx.createLinearGradient(0, 0, 0, height)
  rightEdgeGradient.addColorStop(0, `hsl(${hue.value}, 100%, 100%)`) // Branco com matiz
  rightEdgeGradient.addColorStop(0.5, pureColor) // Cor pura no meio
  rightEdgeGradient.addColorStop(1, `hsl(${hue.value}, 100%, 0%)`) // Preto com matiz
  
  ctx.fillStyle = rightEdgeGradient
  ctx.fillRect(width - 1, 0, 1, height) // Última coluna
  
  // 4. Aplicar suavização para melhor qualidade visual
  ctx.imageSmoothingEnabled = true
  ctx.imageSmoothingQuality = 'high'
}

const drawHueBar = () => {
  const canvas = hueCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height
  
  // Gradiente vertical para barra de matiz
  const gradient = ctx.createLinearGradient(0, 0, 0, height)
  
  for (let i = 0; i <= 360; i += 60) {
    gradient.addColorStop(i / 360, `hsl(${i}, 100%, 50%)`)
  }
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
}

const drawOpacityBar = () => {
  const canvas = opacityCanvas.value
  if (!canvas) return
  
  const ctx = canvas.getContext('2d')
  const width = canvas.width
  const height = canvas.height
  
  // Fundo xadrez para transparência
  const checkSize = 6
  for (let x = 0; x < width; x += checkSize) {
    for (let y = 0; y < height; y += checkSize) {
      ctx.fillStyle = ((x / checkSize) + (y / checkSize)) % 2 ? '#ffffff' : '#cccccc'
      ctx.fillRect(x, y, checkSize, checkSize)
    }
  }
  
  // Gradiente vertical de opacidade
  const gradient = ctx.createLinearGradient(0, 0, 0, height)
  const currentHsl = `hsl(${hue.value}, ${saturation.value}%, ${lightness.value}%)`
  gradient.addColorStop(0, currentHsl)
  gradient.addColorStop(1, 'transparent')
  
  ctx.fillStyle = gradient
  ctx.fillRect(0, 0, width, height)
}

// Sistema de drag otimizado para máxima velocidade

const startDrag = (e, type) => {
  e.preventDefault()
  e.stopPropagation()
  
  isDragging.value = true
  dragType.value = type
  
  // Atualiza posição imediatamente
  updatePosition(e)
  
  // Adiciona listeners com passive: false para controle total
  document.addEventListener('mousemove', handleMouseMove, { passive: false, capture: true })
  document.addEventListener('mouseup', handleMouseUp, { passive: false, capture: true })
  document.addEventListener('selectstart', preventDefault, { passive: false })
  document.addEventListener('dragstart', preventDefault, { passive: false })
  
  // Melhora performance visual
  document.body.style.userSelect = 'none'
  document.body.style.pointerEvents = 'none'
  if (pickerDropdown.value) {
    pickerDropdown.value.style.pointerEvents = 'auto'
  }
}

const handleMouseMove = (e) => {
  if (!isDragging.value) return
  
  e.preventDefault()
  e.stopPropagation()
  
  // Sem throttling para máxima responsividade
  
  // Atualização direta sem requestAnimationFrame para máxima velocidade
  updatePosition(e)
}

const handleMouseUp = (e) => {
  if (!isDragging.value) return
  
  e.preventDefault()
  e.stopPropagation()
  
  // Sem animações para cancelar
  
  isDragging.value = false
  dragType.value = ''
  
  // Remove todos os listeners
  document.removeEventListener('mousemove', handleMouseMove, { capture: true })
  document.removeEventListener('mouseup', handleMouseUp, { capture: true })
  document.removeEventListener('selectstart', preventDefault)
  document.removeEventListener('dragstart', preventDefault)
  
  // Restaura estilos
  document.body.style.userSelect = ''
  document.body.style.pointerEvents = ''
}

const preventDefault = (e) => {
  e.preventDefault()
  return false
}

const updatePosition = (e) => {
  if (dragType.value === 'color') {
    updateColorPosition(e)
  } else if (dragType.value === 'hue') {
    updateHuePosition(e)
  } else if (dragType.value === 'opacity') {
    updateOpacityPosition(e)
  }
}

// Handlers específicos otimizados
const startColorSelection = (e) => {
  if (e.target === colorCanvas.value) {
    startDrag(e, 'color')
  }
}

const startDotDrag = (e) => {
  startDrag(e, 'color')
}



const updateColorPosition = (e) => {
  if (!colorCanvas.value) return
  
  const rect = colorCanvas.value.getBoundingClientRect()
  const x = Math.max(0, Math.min(180, e.clientX - rect.left))
  const y = Math.max(0, Math.min(140, e.clientY - rect.top))
  
  // NOVA ABORDAGEM: Amostra a cor real do canvas na posição clicada
  const ctx = colorCanvas.value.getContext('2d')
  const imageData = ctx.getImageData(Math.floor(x), Math.floor(y), 1, 1)
  const pixel = imageData.data
  
  // Extrai RGB da posição exata
  const sampledRgb = {
    r: pixel[0],
    g: pixel[1],
    b: pixel[2]
  }
  
  // Converte RGB amostrado para HSL
  const sampledHsl = rgbToHsl(sampledRgb.r, sampledRgb.g, sampledRgb.b)
  
  // Atualiza posição visual
  selectorPosition.value.x = x
  selectorPosition.value.y = y
  
  // Usa os valores HSL amostrados (mais precisos)
  saturation.value = sampledHsl.s
  lightness.value = sampledHsl.l
  // Mantém o hue atual (não muda no canvas principal)
  
  // Gera a cor final usando os valores precisos
  updateColorFromHsl()
}

const startHueSelection = (e) => {
  startDrag(e, 'hue')
}

const updateHuePosition = (e) => {
  if (!hueCanvas.value) return
  
  const rect = hueCanvas.value.getBoundingClientRect()
  const y = Math.max(0, Math.min(140, e.clientY - rect.top))
  
  huePosition.value = y
  hue.value = (y / 140) * 360
  
  drawColorCanvas()
  drawOpacityBar()
  updateColorFromHsl()
}

const startOpacitySelection = (e) => {
  startDrag(e, 'opacity')
}

const updateOpacityPosition = (e) => {
  if (!opacityCanvas.value) return
  
  const rect = opacityCanvas.value.getBoundingClientRect()
  const y = Math.max(0, Math.min(140, e.clientY - rect.top))
  
  opacityPosition.value = y
  opacity.value = 1 - (y / 140) // Invertido porque opacidade máxima fica no topo
  
  updateColorFromHsl()
}



// Handlers de input
const handleHexInput = (e) => {
  const value = e.target.value
  if (isValidHex(value)) {
    // Se há opacidade, manter a opacidade com a nova cor
    if (opacity.value < 1) {
      const rgb = hexToRgb(value)
      if (rgb) {
        const rgba = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity.value})`
        emit('update:modelValue', rgba)
      }
    } else {
      emit('update:modelValue', value)
    }
    updateFromCurrentColor()
  }
}

const handleRgbInput = () => {
  // Se há opacidade, criar RGBA
  if (opacity.value < 1) {
    const rgba = `rgba(${rgbValue.value.r}, ${rgbValue.value.g}, ${rgbValue.value.b}, ${opacity.value})`
    emit('update:modelValue', rgba)
  } else {
    // Senão, criar HEX
    const hex = rgbToHex(rgbValue.value.r, rgbValue.value.g, rgbValue.value.b)
    emit('update:modelValue', hex)
  }
  updateFromCurrentColor()
}

const handleOpacityInput = () => {
  updateColorFromHsl()
}

// Utilitários
const updateColorFromHsl = () => {
  const rgb = hslToRgb(hue.value, saturation.value, lightness.value)
  const hex = rgbToHex(rgb.r, rgb.g, rgb.b)
  
  if (opacity.value < 1) {
    const rgba = `rgba(${rgb.r}, ${rgb.g}, ${rgb.b}, ${opacity.value})`
    emit('update:modelValue', rgba)
  } else {
    emit('update:modelValue', hex)
  }
}

const updateFromCurrentColor = () => {
  if (props.modelValue === 'transparent') {
    opacity.value = 0
    opacityPosition.value = 140 // Transparência total no fundo
    return
  }
  
  let rgb = null
  let currentOpacity = 1
  
  // Se for RGBA, extrair RGB e opacidade
  if (props.modelValue.startsWith('rgba(')) {
    const rgbaMatch = props.modelValue.match(/rgba\((\d+),\s*(\d+),\s*(\d+),\s*([\d.]+)\)/)
    if (rgbaMatch) {
      rgb = {
        r: parseInt(rgbaMatch[1]),
        g: parseInt(rgbaMatch[2]),
        b: parseInt(rgbaMatch[3])
      }
      currentOpacity = parseFloat(rgbaMatch[4])
    }
  } else {
    // Se for HEX, converter para RGB
    rgb = hexToRgb(props.modelValue)
    currentOpacity = 1
  }
  
  if (rgb) {
    const hsl = rgbToHsl(rgb.r, rgb.g, rgb.b)
    hue.value = hsl.h
    saturation.value = hsl.s
    lightness.value = hsl.l
    opacity.value = currentOpacity
    

    
    // NOVA ABORDAGEM: Encontra a posição exata no canvas que corresponde à cor
    const targetRgb = rgb
    let bestMatch = { x: 90, y: 70, distance: Infinity } // Posição padrão no centro
    
    // Busca por amostragem para encontrar a posição mais próxima
    const ctx = colorCanvas.value?.getContext('2d')
    if (ctx) {
      // Amostra pontos no canvas para encontrar a melhor correspondência
      for (let y = 0; y < 140; y += 2) { // Passo de 2 para performance
        for (let x = 0; x < 180; x += 2) {
          try {
            const imageData = ctx.getImageData(x, y, 1, 1)
            const pixel = imageData.data
            
            // Calcula distância euclidiana no espaço RGB
            const distance = Math.sqrt(
              Math.pow(pixel[0] - targetRgb.r, 2) +
              Math.pow(pixel[1] - targetRgb.g, 2) +
              Math.pow(pixel[2] - targetRgb.b, 2)
            )
            
            if (distance < bestMatch.distance) {
              bestMatch = { x, y, distance }
            }
          } catch (e) {
            // Ignora erros de acesso ao pixel
          }
        }
      }
    }
    
    selectorPosition.value = {
      x: bestMatch.x,
      y: bestMatch.y
    }
    

    
    huePosition.value = (hue.value / 360) * 140
    opacityPosition.value = (1 - opacity.value) * 140 // Opacidade: 100% = topo, 0% = fundo
    
    nextTick(() => {
      drawColorCanvas()
      drawOpacityBar()
    })
  }
}

// Conversores de cor
const isValidHex = (hex) => {
  return /^#(?:[0-9a-f]{3}){1,2}$/i.test(hex)
}

const hexToRgb = (hex) => {
  if (!hex || hex === 'transparent') return null
  
  const result = /^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(hex)
  return result ? {
    r: parseInt(result[1], 16),
    g: parseInt(result[2], 16),
    b: parseInt(result[3], 16)
  } : null
}

const rgbToHex = (r, g, b) => {
  return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
}

const hslToRgb = (h, s, l) => {
  h /= 360
  s /= 100
  l /= 100
  
  const c = (1 - Math.abs(2 * l - 1)) * s
  const x = c * (1 - Math.abs((h * 6) % 2 - 1))
  const m = l - c / 2
  
  let r, g, b
  
  if (h < 1/6) [r, g, b] = [c, x, 0]
  else if (h < 2/6) [r, g, b] = [x, c, 0]
  else if (h < 3/6) [r, g, b] = [0, c, x]
  else if (h < 4/6) [r, g, b] = [0, x, c]
  else if (h < 5/6) [r, g, b] = [x, 0, c]
  else [r, g, b] = [c, 0, x]
  
  return {
    r: Math.round((r + m) * 255),
    g: Math.round((g + m) * 255),
    b: Math.round((b + m) * 255)
  }
}

const rgbToHsl = (r, g, b) => {
  r /= 255
  g /= 255
  b /= 255
  
  const max = Math.max(r, g, b)
  const min = Math.min(r, g, b)
  const diff = max - min
  const sum = max + min
  
  let h, s, l = sum / 2
  
  if (diff === 0) {
    h = s = 0
  } else {
    s = l > 0.5 ? diff / (2 - sum) : diff / sum
    
    switch (max) {
      case r: h = (g - b) / diff + (g < b ? 6 : 0); break
      case g: h = (b - r) / diff + 2; break
      case b: h = (r - g) / diff + 4; break
    }
    h /= 6
  }
  
  return {
    h: Math.round(h * 360),
    s: Math.round(s * 100),
    l: Math.round(l * 100)
  }
}

// Instância do picker para o sistema global
const pickerInstance = {
  close: closePicker
}

// Event listeners melhorado
const handleClickOutside = (e) => {
  if (isPickerOpen.value && pickerDropdown.value) {
    // Verifica se o clique foi dentro do dropdown
    if (pickerDropdown.value.contains(e.target)) {
      return
    }
    
    // Verifica se o clique foi no botão de preview deste picker
    const colorPickerContainer = pickerDropdown.value.closest('.iluria-color-picker')
    if (colorPickerContainer && colorPickerContainer.contains(e.target)) {
      return
    }
    
    // Se chegou aqui, fecha o picker
    closePicker()
  }
}

onMounted(() => {
  // Registra esta instância no sistema global
  globalColorPickerManager.register(pickerInstance)
  
  document.addEventListener('click', handleClickOutside, true) // Capture phase
})

onUnmounted(() => {
  // Remove esta instância do sistema global
  globalColorPickerManager.unregister(pickerInstance)
  
  document.removeEventListener('click', handleClickOutside, true)
  
  // Limpa listeners de drag se ainda estiverem ativos
  document.removeEventListener('mousemove', handleMouseMove, { capture: true })
  document.removeEventListener('mouseup', handleMouseUp, { capture: true })
  document.removeEventListener('selectstart', preventDefault)
  document.removeEventListener('dragstart', preventDefault)
  
  // Restaura estilos do body
  document.body.style.userSelect = ''
  document.body.style.pointerEvents = ''
})

// Watch para mudanças externas
watch(() => props.modelValue, () => {
  if (isPickerOpen.value) {
    updateFromCurrentColor()
  }
})
</script>

<style scoped>
.iluria-color-picker {
  position: relative;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.color-picker-container {
  position: relative;
}

.color-preview-button {
  width: 40px;
  height: 40px;
  border: 2px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
  background: var(--iluria-color-surface);
}

.color-preview-button:hover:not(:disabled) {
  border-color: var(--iluria-color-border-hover);
  transform: scale(1.05);
}

.color-preview-button:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.color-preview-content {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.transparent-icon {
  width: 20px;
  height: 20px;
  color: var(--iluria-color-text-secondary);
}

.color-picker-dropdown {
  position: absolute;
  top: 100%;
  left: 0;
  z-index: 9999;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 1rem;
  box-shadow: 0 10px 25px rgba(0, 0, 0, 0.15);
  min-width: 280px;
  margin-top: 0.5rem;
}

.color-picker-dropdown.is-dragging {
  /* Otimizações durante drag */
  will-change: contents;
  pointer-events: auto;
}

.color-picker-dropdown.is-dragging * {
  pointer-events: none;
  transition: none !important;
}

.color-picker-dropdown.is-dragging .color-canvas,
.color-picker-dropdown.is-dragging .hue-bar,
.color-picker-dropdown.is-dragging .opacity-bar,
.color-picker-dropdown.is-dragging .color-selector-dot,
.color-picker-dropdown.is-dragging .hue-selector,
.color-picker-dropdown.is-dragging .opacity-selector {
  pointer-events: auto;
}

.picker-main-content {
  display: flex;
  gap: 0.75rem;
  margin-bottom: 1rem;
}

.color-canvas-container {
  position: relative;
}

.color-canvas {
  width: 180px;
  height: 140px;
  border-radius: 8px;
  cursor: crosshair;
  border: 1px solid var(--iluria-color-border);
  touch-action: none;
  user-select: none;
}

.color-canvas:active,
.color-canvas.dragging {
  cursor: grabbing;
}

.color-selector-dot {
  position: absolute;
  width: 16px;
  height: 16px;
  border: 3px solid white;
  border-radius: 50%;
  cursor: grab;
  transform: translate(-50%, -50%);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.3), 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.1s ease;
  z-index: 10;
}

.color-selector-dot:hover {
  transform: translate(-50%, -50%) scale(1.1);
  box-shadow: 0 0 0 1px rgba(0, 0, 0, 0.4), 0 4px 8px rgba(0, 0, 0, 0.3);
}

.color-selector-dot:active,
.color-selector-dot.dragging {
  cursor: grabbing;
  transform: translate(-50%, -50%) scale(0.95);
}

.side-bars {
  display: flex;
  gap: 0.5rem;
}

.hue-bar-container,
.opacity-bar-container {
  position: relative;
}

.hue-bar.vertical,
.opacity-bar.vertical {
  width: 12px;
  height: 140px;
  border-radius: 6px;
  cursor: pointer;
  border: 1px solid var(--iluria-color-border);
  touch-action: none;
  user-select: none;
}

.hue-selector.vertical,
.opacity-selector.vertical {
  position: absolute;
  left: -2px;
  width: 16px;
  height: 4px;
  background: white;
  border: 1px solid rgba(0, 0, 0, 0.3);
  border-radius: 2px;
  pointer-events: none;
  transform: translateY(-50%);
}

.color-input-section {
  margin-bottom: 1rem;
}

.input-with-mode {
  display: flex;
  flex-direction: column;
}

.input-container {
  position: relative;
  display: flex;
  align-items: center;
}

.rgb-container {
  gap: 0.5rem;
  padding-right: 3.5rem; /* Espaço para o botão */
}

.rgb-inputs {
  display: flex;
  gap: 0.25rem;
  flex: 1;
}

.mode-toggle-button {
  position: absolute;
  right: 1px;
  top: 1px;
  bottom: 1px;
  padding: 0 0.75rem;
  background: var(--iluria-color-button-primary-bg);
  color: var(--iluria-color-button-primary-fg);
  border: none;
  border-radius: 0 3px 3px 0;
  cursor: pointer;
  font-size: 0.75rem;
  font-weight: 500;
  transition: all 0.2s;
  z-index: 2;
  min-width: 3rem;
}

.mode-toggle-button:hover {
  background: var(--iluria-color-button-primary-bg-hover);
}

.color-input {
  padding: 0.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  background: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
  font-size: 0.875rem;
  transition: all 0.2s;
}

.color-input:focus {
  outline: none;
  border-color: var(--iluria-color-button-primary-bg);
}

.main-input {
  width: 100%;
  text-align: center;
  padding-right: 3.5rem; /* Espaço para o botão */
}

.rgb-input {
  text-align: center;
  flex: 1;
  min-width: 0;
  padding: 0.5rem 0.25rem;
}



.quick-colors {
  margin-bottom: 1rem;
}

.quick-colors-label {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 0.5rem;
}

.quick-colors-grid {
  display: grid;
  grid-template-columns: repeat(8, 1fr);
  gap: 0.25rem;
  overflow: hidden;
}

.quick-color-button {
  width: 24px;
  height: 24px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.quick-color-button:hover {
  transform: scale(1.1);
  border-color: var(--iluria-color-border-hover);
}



/* Padrão xadrez para transparência */
.color-preview-button[style*="transparent"] {
  background-image: 
    linear-gradient(45deg, #ccc 25%, transparent 25%), 
    linear-gradient(-45deg, #ccc 25%, transparent 25%), 
    linear-gradient(45deg, transparent 75%, #ccc 75%), 
    linear-gradient(-45deg, transparent 75%, #ccc 75%);
  background-size: 8px 8px;
  background-position: 0 0, 0 4px, 4px -4px, -4px 0px;
}

.debug-indicator {
  position: absolute;
  top: -5px;
  right: -5px;
  background: red;
  color: white;
  font-size: 8px;
  padding: 2px 4px;
  border-radius: 3px;
  pointer-events: none;
}

.debug-info {
  background: #f0f0f0;
  padding: 0.25rem;
  margin-bottom: 0.5rem;
  border-radius: 4px;
  text-align: center;
}
</style> 
