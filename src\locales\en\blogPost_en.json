{"title": "Blog Post Management", "subtitle": "Manage all your blog posts", "new": "New Post", "edit": "Edit Post", "delete": "Delete Post", "search": "Search posts...", "searchByTitle": "Search by title or content", "noData": "No posts found", "loading": "Loading posts...", "actions": "Actions", "itemsPerPage": "Posts per page", "totalItems": "Total posts", "page": "Page", "of": "of", "showing": "Showing", "to": "to", "results": "results", "titleView": "Blog Posts", "form": {"title": "Title", "titlePlaceholder": "Enter post title", "titleRequired": "Title is required", "titleMaxLength": "Title must be at most 255 characters", "slug": "Slug (URL)", "slugPlaceholder": "Enter post slug", "slugRequired": "Slug is required", "slugMaxLength": "Slug must be at most 255 characters", "slugHelp": "Friendly URL for the post. Leave blank to generate automatically.", "category": "Category", "categoryPlaceholder": "Select a category", "categoryOptional": "Category is optional", "content": "Content", "contentPlaceholder": "Enter post content...", "contentRequired": "Content is required", "excerpt": "Excerpt", "excerptPlaceholder": "Enter post excerpt...", "excerptHelp": "Short text that appears in post listings", "excerptMaxLength": "Excerpt must be at most 500 characters", "featuredImage": "Featured Image", "featuredImagePlaceholder": "Featured image URL", "featuredImageHelp": "URL of the post's main image", "published": "Published", "publishedHelp": "Check to publish the post on the blog", "featured": "Featured Post", "featuredHelp": "Check to feature this post", "publishedAt": "Publication Date", "publishedAtHelp": "Date and time when the post was published", "viewCount": "Views", "viewCountHelp": "Number of post views", "basicData": "Basic Data", "basicDataDescription": "Main post information", "seoConfig": "SEO Configuration", "seoConfigDescription": "Search engine optimization", "metaTitle": "Meta Title", "metaTitlePlaceholder": "Enter meta title", "metaTitleHelp": "Title that appears in search results (recommended: 50-60 characters)", "metaTitleMaxLength": "Meta title must be at most 255 characters", "metaDescription": "Meta Description", "metaDescriptionPlaceholder": "Enter meta description", "metaDescriptionHelp": "Description that appears in search results (recommended: 150-160 characters)", "metaDescriptionMaxLength": "Meta description must be at most 500 characters", "googlePreview": "Google Preview", "googlePreviewTitle": "How your post will appear on Google", "googlePreviewSubtitle": "See how your post will appear in search results", "googlePreviewUrl": "yoursite.com/blog/post-slug", "googlePreviewDefaultTitle": "Post Title - Your Site", "googlePreviewDefaultDescription": "Post description that will appear in Google search results...", "imageUpload": "Image Upload", "imageUploadDescription": "Main post image", "save": "Save", "cancel": "Cancel", "saving": "Saving...", "saved": "Post saved successfully!", "backToPosts": "Back to Posts", "selectImage": "Select Image", "changeImage": "Change Image", "removeImage": "Remove Image", "imageFormat": "PNG, JPG up to 2MB", "settingsTitle": "Post Settings", "settingsSubtitle": "Set publication settings", "characters": "characters"}, "table": {"image": "Image", "title": "Title", "category": "Category", "status": "Status", "featured": "Featured", "views": "Views", "publishedAt": "Published at", "createdAt": "Created at", "updatedAt": "Updated at", "actions": "Actions"}, "status": {"published": "Published", "draft": "Draft", "featured": "Featured", "normal": "Normal"}, "filters": {"all": "All", "published": "Published", "draft": "Drafts", "featured": "Featured", "category": "Category", "status": "Status"}, "messages": {"createSuccess": "Post created successfully!", "updateSuccess": "Post updated successfully!", "deleteSuccess": "Post deleted successfully!", "deleteConfirm": "Are you sure you want to delete this post?", "deleteConfirmTitle": "Delete Post", "loadError": "Error loading posts", "saveError": "Error saving post", "deleteError": "Error deleting post", "slugExists": "This slug is already in use", "invalidData": "Invalid data", "required": "Required field", "maxLength": "Exceeds maximum length", "titleRequired": "Title is required", "contentRequired": "Content is required", "loadCategoriesError": "Error loading categories", "loadPostError": "Error loading post", "imageUploadSuccess": "Image uploaded successfully!", "imageUploadError": "Post saved, but error uploading image", "metaTitleMaxChars": "Meta title must be at most 60 characters", "metaDescriptionMaxChars": "Meta description must be at most 160 characters", "successTitle": "Success", "errorTitle": "Error", "warningTitle": "Warning"}, "placeholders": {"noImage": "No image", "noCategory": "No category", "noContent": "No content", "noExcerpt": "No excerpt"}, "stats": {"total": "Total Posts", "published": "Published Posts", "drafts": "Drafts", "featured": "Featured Posts", "categories": "Categories", "views": "Total Views"}, "blogDashboard": {"title": "Blog Management", "subtitle": "Manage your blog posts and categories", "posts": {"title": "Blog Posts", "description": "Manage all your blog posts", "viewAll": "View all posts", "createNew": "Create new post"}, "categories": {"title": "Blog Categories", "description": "Organize posts into categories", "viewAll": "View all categories", "createNew": "Create new category"}}}