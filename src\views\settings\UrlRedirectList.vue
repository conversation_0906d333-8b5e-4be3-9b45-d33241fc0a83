<template>
  <div class="page-container">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>{{ t('loading') }}</p>
    </div>

    <!-- Page Content -->
    <div v-else>
      <!-- Page Header -->
      <IluriaHeader
        :title="t('urlRedirect.title')"
        :subtitle="t('urlRedirect.subtitle', 'Gerencie os redirecionamentos de URLs da sua loja')"
        :showSearch="true"
        :showAdd="true"
        :addText="t('urlRedirect.new')"
        searchPlaceholder="Search..."
        @search="handleSearch"
        @add-click="newUrlRedirect"
      />

      <!-- Data Table -->
      <div class="table-wrapper">
        <IluriaDataTable 
          :value="sortedUrlRedirectList" 
          :columns="mainTableColumns"
          :loading="loading"
          :emptyMessage="t('urlRedirect.noData', 'Nenhum redirecionamento encontrado')"
          class="redirects-table"
        >
            <template #header-url>
                <span class="column-header" data-sortable="true" @click="toggleSort('url')">
                    {{ t('urlRedirect.url', 'URL') }}
                    <span v-if="sortField === 'url'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                </span>
            </template>
            <template #header-redirectUrl>
                <span class="column-header" data-sortable="true" @click="toggleSort('redirectUrl')">
                    {{ t('urlRedirect.redirectUrl', 'URL de Redirecionamento') }}
                    <span v-if="sortField === 'redirectUrl'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                </span>
            </template>
            <template #header-active>
                <span class="column-header" data-sortable="true" @click="toggleSort('active')">
                    {{ t('active', 'Ativo') }}
                    <span v-if="sortField === 'active'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                </span>
            </template>
            <template #header-actions>
                <span class="column-header">{{ t('actions', 'Ações') }}</span>
            </template>
            
            <template #column-url="{ data }">
                <div class="url-cell">
                    <span class="url-path">{{ data.url }}</span>
                </div>
            </template>
          
            <template #column-redirectUrl="{ data }">
                <div class="url-cell">
                    <span class="redirect-path">{{ data.redirectUrl }}</span>
                </div>
            </template>
          
            <template #column-active="{ data }">
                <div class="status-cell">
                    <span :class="['status-badge', data.active ? 'status-active' : 'status-inactive']">
                        {{ data.active ? t('active', 'Ativo') : t('inactive', 'Inativo') }}
                    </span>
                </div>
            </template>
          
            <template #column-actions="{ data }">
                <div class="actions-cell">
                    <IluriaButton 
                        color="text-primary" 
                        size="small" 
                        :hugeIcon="PencilEdit01Icon" 
                        @click="editUrlRedirect(data)"
                        :title="t('edit', 'Editar')"
                    />
                    <IluriaButton 
                        color="text-danger" 
                        size="small" 
                        :hugeIcon="Delete01Icon" 
                        @click="confirmDelete(data)"
                        :title="t('delete', 'Excluir')"
                    />
                </div>
            </template>
        </IluriaDataTable>
      </div>

      <!-- Pagination -->
      <div v-if="totalPages > 1" class="pagination-container">
        <IluriaPagination 
          :current-page="currentPage"
          :total-pages="totalPages"
          @go-to-page="changePage"
        />
      </div>
    </div>

    <IluriaConfirmationModal 
      :isVisible="showConfirmDialog"
      :title="confirmData.title" 
      :message="confirmData.message"
      :type="confirmData.type || 'error'"
      @confirm="handleConfirm"
      @cancel="showConfirmDialog = false"
    />
  </div>
</template>

<script setup>

import { onMounted, ref, computed } from 'vue'
import settingsService from '@/services/settings.service'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import { Delete01Icon, PencilEdit01Icon, Add01Icon } from '@hugeicons-pro/core-bulk-rounded'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'

const router = useRouter()

const { t } = useI18n()

const urlRedirectList = ref([])
const loading = ref(false)

// Confirmation dialog state
const showConfirmDialog = ref(false)
const confirmData = ref({ title: '', message: '', type: 'error', onConfirm: null })

const filter = ref('');
const currentPage = ref(0);
const totalPages = ref(0);
const sortField = ref(null);
const sortOrder = ref(null);

const toggleSort = (field) => {
    if (sortField.value === field) {
        sortOrder.value *= -1;
    } else {
        sortField.value = field;
        sortOrder.value = 1;
    }
};

const sortedUrlRedirectList = computed(() => {
    if (!sortField.value) {
        return urlRedirectList.value;
    }
    return [...urlRedirectList.value].sort((a, b) => {
        const valA = a[sortField.value];
        const valB = b[sortField.value];
        if (typeof valA === 'boolean') {
            return (valA === valB ? 0 : valA ? -1 : 1) * sortOrder.value;
        }
        if (valA < valB) return -1 * sortOrder.value;
        if (valA > valB) return 1 * sortOrder.value;
        return 0;
    });
});

const mainTableColumns = [
    { field: 'url', headerClass: 'col-flex', class: 'col-flex' },
    { field: 'redirectUrl', headerClass: 'col-flex', class: 'col-flex' },
    { field: 'active', headerClass: 'col-small', class: 'col-small' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
];

onMounted(() => {
    loadUrlRedirectList()
})

async function loadUrlRedirectList() {
    loading.value = true;
    try {
        const response = await settingsService.getUrlRedirectList(filter.value, currentPage.value, 5);
        urlRedirectList.value = response.content;
        totalPages.value = response.totalPages;
    } catch (error) {
        console.error('Error loading URL redirects:', error);
    } finally {
        loading.value = false;
    }
}

function editUrlRedirect(urlRedirect) {
    router.push(`/settings/url-redirect/${urlRedirect.id}`)
}

function newUrlRedirect() {
    router.push('/settings/url-redirect/new')
}

function confirmDelete(urlRedirect) {
    confirmData.value = {
        title: t('urlRedirect.confirmDeleteTitle', 'Confirmar Exclusão'),
        message: `${t('urlRedirect.confirmDeleteMessage')} ${urlRedirect.url}?`,
        type: 'error',
        onConfirm: () => deleteUrlRedirect(urlRedirect.id)
    }
    showConfirmDialog.value = true
}

function deleteUrlRedirect(id) {
    settingsService.deleteUrlRedirect(id)
        .then(() => {
            loadUrlRedirectList();
        })
        .catch((error) => {
            console.error('Error deleting URL redirect:', error);
        });
}

let searchTimeout = null
function filterSearch() {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
        currentPage.value = 0 // Reseta a página ao fazer nova busca
        loadUrlRedirectList()
    }, 400)
}

const handleSearch = (searchValue) => {
    filter.value = searchValue
    filterSearch()
}

// Confirmation dialog handler
const handleConfirm = () => {
    if (confirmData.value.onConfirm) {
        confirmData.value.onConfirm()
    }
    showConfirmDialog.value = false
}

function changePage(page) {
    currentPage.value = page
    loadUrlRedirectList()
}

</script>

<style scoped>
.page-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  color: var(--iluria-color-text-secondary);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid var(--iluria-color-border);
  border-top: 4px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-container {
  min-width: 280px;
}

.search-input {
  width: 100%;
}

/* Table */
.table-wrapper {
  border-radius: 12px;
  overflow: hidden;
}

/* Column width definitions */
:deep(.col-flex) { width: auto; text-align: left; }
:deep(.col-small) { width: 120px; text-align: center; }
:deep(.col-actions) { width: 120px; text-align: center; }

/* General table styling */
:deep(.redirects-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.redirects-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.redirects-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.redirects-table .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.redirects-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.redirects-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.redirects-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.redirects-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}

/* Column header style */
:deep(.redirects-table th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.redirects-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

.url-cell {
  text-align: left;
}

.status-cell {
  text-align: center;
}

.actions-cell {
  display: flex;
  justify-content: center;
  gap: 8px;
}

.status-badge {
    padding: 6px 12px;
    border-radius: 16px;
    font-weight: 500;
    font-size: 12px;
    display: inline-block;
}

.status-active {
    background-color: var(--iluria-color-success-bg);
    color: var(--iluria-color-success);
    border: 1px solid var(--iluria-color-success);
}

.status-inactive {
    background-color: var(--iluria-color-error-bg);
    color: var(--iluria-color-error);
    border: 1px solid var(--iluria-color-error);
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
  padding-top: 20px;
  border-top: 1px solid var(--iluria-color-border);
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-subtitle {
    font-size: 14px;
  }
  
  .header-actions {
    width: 100%;
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .search-container {
    min-width: auto;
  }
}

@media (max-width: 480px) {
  .page-title {
    font-size: 22px;
  }
  
  .actions-cell {
    flex-direction: column;
    gap: 4px;
  }
}
</style>
