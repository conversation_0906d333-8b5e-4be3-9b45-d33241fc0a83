<template>
  <div class="textarea-container">
    <textarea
      :id="id"
      :name="name"
      :value="modelValue"
      @input="$emit('update:modelValue', $event.target.value)"
      :placeholder="placeholder"
      :rows="rows || 4"
      :disabled="disabled"
      :class="[
        'w-full px-3 py-2 border rounded-md text-gray-700',
        'focus:outline-none focus:ring-2 focus:ring-blue-500',
        disabled ? 'bg-gray-100 cursor-not-allowed' : '',
        formContext && formContext.errors ? 'border-red-500' : 'border-gray-300'
      ]"
    ></textarea>
    <div v-if="formContext && formContext.errors" class="text-red-500 text-sm mt-1">
      {{ formContext.errors }}
    </div>
  </div>
</template>

<script setup>
defineProps({
  id: {
    type: String,
    default: ''
  },
  name: {
    type: String,
    default: ''
  },
  modelValue: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  rows: {
    type: Number,
    default: 4
  },
  disabled: {
    type: Boolean,
    default: false
  },
  formContext: {
    type: Object,
    default: null
  }
});

defineEmits(['update:modelValue']);
</script>

<style scoped>
.textarea-container {
  margin-bottom: 0.5rem;
}
</style>
