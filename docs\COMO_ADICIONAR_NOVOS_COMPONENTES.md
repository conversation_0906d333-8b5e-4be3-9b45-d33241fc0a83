# Como Adicionar Novos Componentes ao LayoutEditor

Este guia explica como criar novos componentes funcionais para o LayoutEditor do sistema Iluria usando o **Sistema Centralizado de Configurações**.

## 📋 Índice

1. [Visão Geral do Sistema Centralizado](#visão-geral-do-sistema-centralizado)
2. [Arquitetura do Sistema](#arquitetura-do-sistema)
3. [Criando um Novo Componente](#criando-um-novo-componente)
4. [Criando Editores de Propriedades](#criando-editores-de-propriedades)
5. [Sistema de Scripts e Loaders](#sistema-de-scripts-e-loaders)
6. [Exemplo Completo: Header](#exemplo-completo-header)
7. [Checklist e Verificação](#checklist-e-verificação)

## 🚀 Visão Geral do Sistema Centralizado

O LayoutEditor agora utiliza um **sistema completamente centralizado** baseado em arquivos de configuração:

- **📁 Configs Centralizadas**: Cada componente tem um arquivo `*.config.js` 
- **🔄 Auto-Carregamento**: Sistema importa automaticamente todas as configs
- **🎛️ Editores Específicos**: Cada componente pode ter múltiplos editores
- **📦 Scripts Dinâmicos**: Sistema de loaders para componentes com JavaScript
- **🎯 Zero Configuração Manual**: Não precisa editar arquivos centrais

### Estrutura do Sistema Atual

```
src/
├── components/layoutEditor/
│   ├── configs/                          ← ⭐ CONFIGURAÇÕES CENTRALIZADAS
│   │   ├── index.js                      ← Sistema de auto-carregamento
│   │   ├── header.config.js              ← Config do Header
│   │   ├── video.config.js               ← Config do Video
│   │   ├── carousel.config.js            ← Config do Carousel
│   │   └── [SeuComponente].config.js     ← Sua nova config
│   │
│   ├── FloatingToolBar/property-editors/ ← Editores de propriedades
│   │   ├── HeaderEditor.vue              ← Editor do Header
│   │   ├── VideoConfigEditor.vue         ← Editor do Video
│   │   └── [SeuComponente]Editor.vue     ← Seu novo editor
│   │
│   ├── scripts/                          ← Scripts para componentes dinâmicos
│   │   ├── ProductGridLoader.vue         ← Loader para grid de produtos
│   │   ├── CarouselLoader.vue            ← Loader para carousel
│   │   └── [SeuComponente]Loader.vue     ← Seu novo loader (opcional)
│   │
│   └── ComponentMenu.vue                 ← Menu gerado automaticamente
│
├── views/layoutEditor/composables/
│   └── useComponentLibrary.js            ← Conecta configs ao menu
│
└── composables/
    └── useComponentRegistry.js           ← Sistema de registro automático
```

### 🎯 Fluxo Simplificado

1. **Criar arquivo de config** → `src/components/layoutEditor/configs/meu-componente.config.js`
2. **Criar editor de propriedades** → `src/components/layoutEditor/FloatingToolBar/property-editors/MeuComponenteEditor.vue`
3. **Criar loader** *(se precisar de JavaScript)* → `src/components/layoutEditor/scripts/MeuComponenteLoader.vue`
4. **Pronto!** → Componente aparece automaticamente no menu e é totalmente funcional

## 🏗️ Arquitetura do Sistema

### Como Funciona o Carregamento

1. **`configs/index.js`** carrega automaticamente todos os arquivos `*.config.js`
2. **`useComponentLibrary.js`** consome as configs e gera as categorias do menu
3. **`ComponentMenu.vue`** exibe os componentes organizados por categoria
4. **`PropertyPanel.vue`** carrega automaticamente editores baseados no tipo
5. **Sistema de Scripts** injeta JavaScript quando necessário

### Fluxo de Detecção e Edição

```
Usuário clica em elemento → Sistema detecta tipo via configs → Carrega toolbar actions → 
Usuário clica em editor → PropertyPanel carrega editor específico → Editor modifica elemento
```

## 🎨 Criando um Novo Componente

### 1. Arquivo de Configuração

Crie `src/components/layoutEditor/configs/meu-componente.config.js`:

```javascript
export default {
  // === IDENTIFICAÇÃO ===
  name: 'Meu Componente',
  type: 'meu-componente',
  dataComponent: 'meu-componente',        // Usado em data-component
  elementType: 'meuComponente',           // Usado em data-element-type  
  className: 'iluria-meu-componente',     // Classe CSS principal
  selectors: [                            // Seletores para detectar o componente
    '[data-component="meu-componente"]',
    '.iluria-meu-componente'
  ],
  
  // === CATEGORIZAÇÃO ===
  category: 'content',                    // layout, media, content, marketing, ecommerce
  description: 'Componente personalizado para exibir conteúdo especial',
  priority: 80,                           // Prioridade no menu (maior = aparece antes)
  icon: 'custom',                         // Ícone no menu
  
  // === HTML TEMPLATE ===
  html: `<div
    data-component="meu-componente"
    data-element-type="meuComponente"
    data-titulo="Título Padrão"
    data-conteudo="Conteúdo padrão do componente"
    data-cor-fundo="#ffffff"
    data-cor-texto="#000000"
    data-ativo="true"
    class="iluria-meu-componente"
    style="width: 100%; padding: 2rem; background-color: #ffffff; color: #000000;">
    
    <div class="meu-componente-container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
      <div class="meu-componente-content" style="text-align: center;">
        <h2 class="meu-componente-titulo" style="margin: 0 0 1rem 0; font-size: 2rem; font-weight: bold;">
          Título Padrão
        </h2>
        <p class="meu-componente-texto" style="margin: 0; font-size: 1.125rem; line-height: 1.6;">
          Conteúdo padrão do componente
        </p>
      </div>
    </div>
  </div>`,
  
  // === TOOLBAR ACTIONS ===
  toolbarActions: [
    {
      type: 'meu-componente-config',
      title: 'Configurar Meu Componente',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="12" cy="12" r="3"/>
        <path d="M12 1v6m0 6v6m-6-6h6m6 0h-6"/>
      </svg>`,
      condition: (element) => {
        return element?.getAttribute('data-component') === 'meu-componente' ||
               element?.closest('[data-component="meu-componente"]')
      }
    }
  ],
  
  // === PROPERTY EDITORS ===
  propertyEditors: [
    {
      type: 'meu-componente-config',
      component: 'MeuComponenteEditor',
      path: '@/components/layoutEditor/FloatingToolBar/property-editors/MeuComponenteEditor.vue'
    }
  ],
  
  // === ATRIBUTOS PARA PRESERVAR ===
  attributes: {
    preserve: [
      'data-component', 'data-element-type',
      'data-titulo', 'data-conteudo', 
      'data-cor-fundo', 'data-cor-texto', 'data-ativo'
    ],
    defaults: {
      'data-titulo': 'Título Padrão',
      'data-conteudo': 'Conteúdo padrão',
      'data-cor-fundo': '#ffffff',
      'data-cor-texto': '#000000',
      'data-ativo': 'true'
    }
  },
  
  // === INICIALIZAÇÃO ===
  initialization: {
    autoInit: true,                       // Inicialização automática
    reinitOnChange: true,                 // Re-inicializar quando mudar
    scriptLoader: 'initializeMeuComponente' // Nome da função de inicialização (opcional)
  },
  
  // === LIMPEZA PARA SALVAR ===
  cleanup: {
    removeEditingStyles: false,           // Manter estilos funcionais
    preserveStyles: [                     // Estilos a preservar
      'background-color', 'color', 'padding', 'margin', 'width', 'text-align',
      'font-size', 'font-weight', 'line-height', 'max-width'
    ],
    childSelectors: [                     // Elementos filhos protegidos
      '.meu-componente-container',
      '.meu-componente-content', 
      '.meu-componente-titulo',
      '.meu-componente-texto'
    ]
  },
  
  // === DETECÇÃO ===
  detection: {
    priority: 80                          // Prioridade na detecção
  }
}
```

### 2. Padrão de Largura OBRIGATÓRIO

**⚠️ IMPORTANTE**: Todo componente deve seguir este padrão:

```css
/* ✅ CORRETO - Elemento principal usa 100% da largura */
[data-component="meu-componente"] {
  width: 100% !important;
  margin: 1rem 0 !important;  /* NÃO usar margin: X auto */
}

/* ✅ CORRETO - Container interno limita e centraliza */
.meu-componente-container {
  max-width: 1200px !important;
  margin: 0 auto !important;
  padding: 0 2rem !important;
  width: 100% !important;
}
```

**❌ O que NÃO fazer:**
```css
/* ❌ ERRADO - Não usar max-width no elemento principal */
[data-component="meu-componente"] {
  max-width: 1200px !important;
  margin: 0 auto !important;
}
```

## 🎛️ Criando Editores de Propriedades

### Editor Completo

Crie `src/components/layoutEditor/FloatingToolBar/property-editors/MeuComponenteEditor.vue`:

```vue
<template>
  <FloatingWindow
    :title="$t('layoutEditor.meuComponenteEditor')"
    :width="500"
    :min-height="400"
    :position="position"
    :visible="true"
    @close="$emit('close')"
  >
    <div class="editor-container">
      
      <!-- Navegação por Abas -->
      <div class="tab-navigation">
        <button 
          :class="['tab-button', { active: activeTab === 'content' }]"
          @click="activeTab = 'content'"
        >
          {{ $t('layoutEditor.content') }}
        </button>
        <button 
          :class="['tab-button', { active: activeTab === 'design' }]"
          @click="activeTab = 'design'"
        >
          {{ $t('layoutEditor.design') }}
        </button>
      </div>

      <!-- Conteúdo das Abas -->
      <div class="tab-content">
        
        <!-- Aba Conteúdo -->
        <div v-if="activeTab === 'content'" class="content-tab">
          <div class="section">
            <h3 class="section-title">{{ $t('layoutEditor.content') }}</h3>
            
            <div class="form-group">
              <IluriaLabel>{{ $t('layoutEditor.titulo') }}</IluriaLabel>
              <IluriaInputText
                v-model="config.titulo"
                :placeholder="$t('layoutEditor.enterTitulo')"
                @update:modelValue="updateComponent"
              />
            </div>

            <div class="form-group">
              <IluriaLabel>{{ $t('layoutEditor.conteudo') }}</IluriaLabel>
              <textarea
                v-model="config.conteudo"
                :placeholder="$t('layoutEditor.enterConteudo')"
                @input="updateComponent"
                class="iluria-textarea"
                rows="4"
              />
            </div>

            <div class="form-group">
              <label class="checkbox-label">
                <input 
                  type="checkbox" 
                  v-model="config.ativo"
                  @change="updateComponent"
                />
                {{ $t('layoutEditor.componenteAtivo') }}
              </label>
            </div>
          </div>
        </div>

        <!-- Aba Design -->
        <div v-if="activeTab === 'design'" class="design-tab">
          <div class="section">
            <h3 class="section-title">{{ $t('layoutEditor.colors') }}</h3>
            
            <div class="form-group">
              <IluriaLabel>{{ $t('layoutEditor.corFundo') }}</IluriaLabel>
              <div class="color-input-group">
                <input 
                  type="color" 
                  v-model="config.corFundo"
                  @input="updateComponent"
                  class="color-picker"
                />
                <IluriaInputText
                  v-model="config.corFundo"
                  @update:modelValue="updateComponent"
                  class="color-text-input"
                />
              </div>
            </div>

            <div class="form-group">
              <IluriaLabel>{{ $t('layoutEditor.corTexto') }}</IluriaLabel>
              <div class="color-input-group">
                <input 
                  type="color" 
                  v-model="config.corTexto"
                  @input="updateComponent"
                  class="color-picker"
                />
                <IluriaInputText
                  v-model="config.corTexto"
                  @update:modelValue="updateComponent"
                  class="color-text-input"
                />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Footer com Ações -->
      <template #footer>
        <div class="editor-actions">
          <IluriaButton color="outline" @click="$emit('close')">
            {{ $t('layoutEditor.cancel') }}
          </IluriaButton>
          <IluriaButton color="primary" @click="save">
            {{ $t('layoutEditor.save') }}
          </IluriaButton>
        </div>
      </template>
    </div>
  </FloatingWindow>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import FloatingWindow from '../Common/FloatingWindow.vue'
import IluriaLabel from '@/components/iluria/form/IluriaLabel.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true },
  position: { type: Object, required: true }
})

const emit = defineEmits(['close', 'save', 'data-changed'])

// Estado do editor
const activeTab = ref('content')

// Estado da configuração
const config = ref({
  titulo: '',
  conteudo: '',
  corFundo: '#ffffff',
  corTexto: '#000000',
  ativo: true
})

// Carrega configuração atual do elemento
const loadElementConfig = () => {
  if (!props.element) return

  try {
    config.value = {
      titulo: props.element.getAttribute('data-titulo') || '',
      conteudo: props.element.getAttribute('data-conteudo') || '',
      corFundo: props.element.getAttribute('data-cor-fundo') || '#ffffff',
      corTexto: props.element.getAttribute('data-cor-texto') || '#000000',
      ativo: props.element.getAttribute('data-ativo') === 'true'
    }
  } catch (error) {
    console.error('Erro ao carregar configuração:', error)
  }
}

// Atualiza o componente em tempo real
const updateComponent = () => {
  if (!props.element) return

  try {
    // Atualiza atributos data-*
    props.element.setAttribute('data-titulo', config.value.titulo)
    props.element.setAttribute('data-conteudo', config.value.conteudo)
    props.element.setAttribute('data-cor-fundo', config.value.corFundo)
    props.element.setAttribute('data-cor-texto', config.value.corTexto)
    props.element.setAttribute('data-ativo', config.value.ativo.toString())

    // Atualiza estilos visuais
    props.element.style.backgroundColor = config.value.corFundo
    props.element.style.color = config.value.corTexto

    // Atualiza conteúdo dos elementos filhos
    const tituloElement = props.element.querySelector('.meu-componente-titulo')
    if (tituloElement) {
      tituloElement.textContent = config.value.titulo
    }

    const textoElement = props.element.querySelector('.meu-componente-texto')
    if (textoElement) {
      textoElement.textContent = config.value.conteudo
    }

    // Emite evento de mudança
    emit('data-changed', config.value)
  } catch (error) {
    console.error('Erro ao atualizar componente:', error)
  }
}

// Salva e fecha
const save = () => {
  updateComponent()
  emit('save')
  emit('close')
}

// Inicialização
onMounted(() => {
  loadElementConfig()
})
</script>

<style scoped>
.editor-container {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.tab-navigation {
  display: flex;
  border-bottom: 1px solid #3d3d3d;
  background: #2d2d2d;
}

.tab-button {
  background: none;
  border: none;
  color: #94a3b8;
  padding: 1rem 1.5rem;
  cursor: pointer;
  font-weight: 500;
  transition: all 0.2s ease;
}

.tab-button.active {
  color: #60a5fa;
  background: #1a1a1a;
  border-bottom: 2px solid #60a5fa;
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.content-tab, .design-tab {
  padding: 1.5rem;
}

.section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: #f8fafc;
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid #3d3d3d;
}

.form-group {
  margin-bottom: 1.5rem;
}

.iluria-textarea {
  width: 100%;
  padding: 0.75rem;
  border-radius: 6px;
  background: #2d2d2d;
  border: 1px solid #3d3d3d;
  color: #f8fafc;
  font-size: 0.875rem;
  resize: vertical;
  min-height: 100px;
}

.iluria-textarea:focus {
  border-color: #60a5fa;
  outline: none;
}

.color-input-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.color-picker {
  width: 50px;
  height: 40px;
  padding: 0;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.color-text-input {
  flex: 1;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #f8fafc;
  cursor: pointer;
}

.checkbox-label input[type="checkbox"] {
  width: 1.125rem;
  height: 1.125rem;
}

.editor-actions {
  display: flex;
  justify-content: flex-end;
  gap: 0.75rem;
  padding: 1rem 1.5rem;
  border-top: 1px solid #3d3d3d;
  background: #2d2d2d;
}
</style>
```

## 📦 Sistema de Scripts e Loaders

### Quando Usar Loaders

Use loaders quando seu componente precisar de:
- **APIs externas** (carregar dados dinâmicos)
- **JavaScript avançado** (interações, animações)
- **Bibliotecas externas** (charts, mapas, players)

### Exemplo de Loader

Crie `src/components/layoutEditor/scripts/MeuComponenteLoader.vue`:

```vue
<template>
  <!-- Este componente não renderiza nada, apenas fornece scripts -->
</template>

<script>
/**
 * Sistema de injeção de script para Meu Componente
 */
export function injectMeuComponenteScript(doc, authToken = '') {
 

  if (!doc || !doc.querySelector) {
    console.error('❌ [MeuComponente] Documento inválido')
    return
  }

  // Verificar se há componentes na página
  const components = doc.querySelectorAll('[data-component="meu-componente"]')
  if (components.length === 0) {
   
    return
  }

  // Remover script anterior se existir
  const existingScript = doc.querySelector('#meu-componente-script')
  if (existingScript) {
    existingScript.remove()
  }

  try {
    // Criar e injetar script
    const script = doc.createElement('script')
    script.id = 'meu-componente-script'
    
    script.textContent = `
      (function() {
        'use strict';
        
        
        // Função principal de inicialização
        window.initializeMeuComponente = function(element, document) {
          if (!element || element.hasAttribute('data-initialized')) return;
          
          
          // Configuração do componente
          const titulo = element.getAttribute('data-titulo') || 'Título Padrão';
          const conteudo = element.getAttribute('data-conteudo') || 'Conteúdo padrão';
          const ativo = element.getAttribute('data-ativo') === 'true';
          
          if (!ativo) {
            element.style.opacity = '0.5';
            element.style.pointerEvents = 'none';
          }
          
          // Adicionar interatividade (exemplo)
          const container = element.querySelector('.meu-componente-content');
          if (container) {
            container.addEventListener('click', function() {
            });
          }
          
          element.setAttribute('data-initialized', 'true');
        };
        
        // Auto-inicialização de todos os elementos
        window.initAllMeuComponente = function() {
          const elements = document.querySelectorAll('[data-component="meu-componente"]');
          elements.forEach(element => {
            if (!element.id) {
              element.id = 'meu-componente-' + Date.now() + '-' + Math.random().toString(36).substr(2, 9);
            }
            window.initializeMeuComponente(element, document);
          });
        };
        
        // Chama inicialização automática
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', window.initAllMeuComponente);
        } else {
          window.initAllMeuComponente();
        }
      })();
    `;
    
    if (doc.head) {
      doc.head.appendChild(script);
    }
  } catch (error) {
    console.error('❌ [MeuComponente] Erro ao injetar script:', error);
  }
}
</script>
```

### Registrando o Loader

Se criou um loader, registre-o no sistema:

```javascript
// Em src/composables/useComponentScriptInjection.js
import { injectMeuComponenteScript } from '@/components/layoutEditor/scripts/MeuComponenteLoader.vue'

// Adicione na função initializeComponentScriptSystem()
registerAutoDetectComponent(
  'meu-componente',
  [
    '[data-component="meu-componente"]',
    '.iluria-meu-componente'
  ],
  injectMeuComponenteScript,
  false // requiresAuth - true se usar APIs que precisam de autenticação
)
```

## 🏆 Exemplo Completo: Header

O componente Header serve como exemplo completo de implementação:

### 1. Configuração (`header.config.js`)

```javascript
export default {
  name: 'Header',
  type: 'header',
  category: 'layout',
  description: 'Cabeçalho da página com logotipo, menu de navegação e elementos funcionais',
  priority: 95,
  
  html: `<header data-component="header" class="iluria-header">
    <!-- HTML complexo com estrutura completa -->
  </header>`,
  
  toolbarActions: [{
    type: 'header-config',
    title: 'Configurar Header',
    condition: (element) => element?.getAttribute('data-component') === 'header'
  }],
  
  propertyEditors: [{
    type: 'header-config',
    component: 'HeaderConfigEditor'
  }]
}
```

### 2. Editor Avançado (`HeaderEditor.vue`)

- ✅ **Sistema de abas** (Conteúdo, Design, Serviços)
- ✅ **Menu dinâmico** com adição/remoção de itens
- ✅ **Configurações de design** (cores, layout)
- ✅ **Formulários complexos** com validação
- ✅ **Preview em tempo real**

### 3. Funcionamento Automático

- ✅ **Aparece automaticamente** no menu de componentes
- ✅ **Toolbar actions** carregam quando header é selecionado
- ✅ **Editor abre** automaticamente quando clica em "Configurar Header"
- ✅ **Mudanças refletem** imediatamente no layout

## ✅ Checklist de Implementação

### Para Cada Novo Componente:

- [ ] **1. Config**: Criar `*.config.js` em `/configs/` com todas as propriedades
- [ ] **2. Editor**: Criar `*Editor.vue` em `/property-editors/`
- [ ] **3. Loader** *(se necessário)*: Criar `*Loader.vue` em `/scripts/` e registrar
- [ ] **4. Traduções**: Adicionar chaves em `locales/pt-br/layoutEditor_pt.json`
- [ ] **5. Padrão de Largura**: Seguir estrutura com container interno de `max-width: 1200px`
- [ ] **6. Atributos**: Definir corretamente `preserve` e `defaults` na config
- [ ] **7. Seletores**: Configurar `selectors` para detecção automática

### ✅ Checklist de Verificação:

- [ ] **Componente aparece no menu** (categoria correta)
- [ ] **Componente pode ser adicionado** ao layout
- [ ] **Componente é selecionável** após adição
- [ ] **Toolbar actions aparecem** quando selecionado
- [ ] **Editor abre** quando clica na ação
- [ ] **Mudanças refletem** imediatamente no layout
- [ ] **Componente mantém funcionalidade** após salvar
- [ ] **Scripts carregam** automaticamente (se aplicável)
- [ ] **Largura respeita** os limites da página
- [ ] **Elementos internos** não são selecionáveis individualmente

## 🚨 Problemas Comuns e Soluções

### ❌ "Componente não aparece no menu"
**Solução**: 
1. Verificar se arquivo `.config.js` foi criado em `/configs/`
2. Verificar se `name`, `type` e `category` estão definidos
3. Limpar cache do navegador

### ❌ "Editor não abre ao clicar na toolbar"
**Solução**:
1. Verificar se `type` em `toolbarActions` corresponde ao `propertyEditors`
2. Verificar se caminho do editor em `path` está correto
3. Verificar se `condition` na toolbar action está funcionando

### ❌ "Mudanças no editor não aparecem em tempo real"
**Solução**: Implementar `updateComponent()` que modifica diretamente o elemento DOM

### ❌ "Scripts não carregam"
**Solução**: Verificar se loader foi registrado corretamente em `useComponentScriptInjection.js`

### ❌ "Largura do componente não segue o padrão"
**Solução**: Usar estrutura com elemento principal `width: 100%` e container interno `max-width: 1200px`

## 📋 Resumo das Vantagens

### ✅ Sistema Atual (Centralizado):

- ✅ **Um arquivo por componente**: Toda configuração em um local
- ✅ **Auto-carregamento**: Sistema detecta automaticamente novos componentes  
- ✅ **Zero configuração manual**: Não precisa editar arquivos centrais
- ✅ **Editores específicos**: Cada componente pode ter múltiplos editores
- ✅ **Scripts automáticos**: Loaders injetados automaticamente
- ✅ **Organização clara**: Estrutura de pastas bem definida
- ✅ **Fácil manutenção**: Cada componente é autocontido

---

🚀 **Agora você tem tudo o que precisa para criar componentes usando o Sistema Centralizado!**

Para dúvidas, consulte componentes existentes como `Header`, `Video`, `Carousel` e `ProductGrid` que já estão implementados no novo sistema.