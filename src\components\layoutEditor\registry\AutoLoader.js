import { useComponentRegistry } from '@/composables/useComponentRegistry.js'

// Obtém instância do registry unificado
const { registerComponent: registerUnifiedComponent } = useComponentRegistry()

/**
 * Sistema de auto-descoberta para componentes e editores
 * ATUALIZADO PARA USAR O NOVO SISTEMA DE CONFIGURAÇÕES
 */
class AutoLoader {
  constructor() {
    this.loadedModules = new Set()
    this.isInitialized = false
  }

  /**
   * Carrega automaticamente todos os componentes disponíveis
   * NOVO: Agora usa o sistema de configs automático
   */
  async loadAllComponents() {
    try {
        
      // O novo sistema já carrega automaticamente via import.meta.glob
      // em src/components/layoutEditor/configs/index.js
      // Apenas precisamos garantir que o sistema foi inicializado
      
      await this.ensureConfigSystemLoaded()
      
      this.isInitialized = true
      
      return true
    } catch (error) {
      console.error('❌ [AutoLoader] Erro ao carregar componentes:', error)
      return false
    }
  }

  /**
   * Garante que o sistema de configurações foi carregado
   */
  async ensureConfigSystemLoaded() {
    try {
      // Importa dinamicamente o sistema de configs para forçar inicialização
      const configSystem = await import('@/components/layoutEditor/configs/index.js')
      
      // Verifica se há configurações carregadas
      const stats = configSystem.getConfigStats()
      
      if (stats.total === 0) {
      }
      
      return true
    } catch (error) {
      console.error('❌ [AutoLoader] Erro ao carregar sistema de configs:', error)
      throw error
    }
  }



  /**
   * Registra um componente manualmente
   * MANTIDO: Para registros dinâmicos se necessário
   */
  registerComponent(config) {
    registerUnifiedComponent(config)
    this.loadedModules.add(config.type)
  }

  /**
   * Obtém lista de componentes carregados
   */
  getLoadedComponents() {
    // Agora busca do sistema de configs
    try {
      const configSystem = import('@/components/layoutEditor/configs/index.js')
      return configSystem.then(module => {
        const stats = module.getConfigStats()
        return stats.types
      })
    } catch {
      return Array.from(this.loadedModules)
    }
  }

  /**
   * Verifica se um componente específico foi carregado
   */
  isComponentLoaded(componentType) {
    try {
      const configSystem = import('@/components/layoutEditor/configs/index.js')
      return configSystem.then(module => {
        const config = module.getConfigByType(componentType)
        return !!config
      })
    } catch {
      return this.loadedModules.has(componentType)
    }
  }

  /**
   * Recarrega todos os componentes
   */
  async reload() {
    this.loadedModules.clear()
    this.isInitialized = false
    
    // Força reload do sistema de configs
    try {
      // Limpa o cache do módulo se possível
      if (window.__vite_reload_modules) {
        await window.__vite_reload_modules('@/components/layoutEditor/configs/index.js')
      }
    } catch (error) {
      console.warn('Não foi possível limpar cache do sistema de configs:', error)
    }
    
    await this.loadAllComponents()
  }

  /**
   * Verifica se o sistema foi inicializado
   */
  getInitializationStatus() {
    return {
      isInitialized: this.isInitialized,
      systemType: 'configs',
      message: this.isInitialized ? 'Sistema de configs ativo' : 'Sistema não inicializado'
    }
  }
}

// Instância singleton do autoloader
export const autoLoader = new AutoLoader()

// Função utilitária para inicializar o sistema
export async function initializeComponentSystem() {
  try {
    
    // Usar o autoLoader singleton para carregar todos os componentes
    const success = await autoLoader.loadAllComponents()
    
    if (success) {

    } else {
      console.error('❌ [AutoLoader] Falha na inicialização')
    }

    return success
  } catch (error) {
    console.error('❌ [AutoLoader] Erro ao inicializar sistema de componentes:', error)
    return false
  }
}

// Função para registrar componente individual
export const registerComponent = (config) => {
  autoLoader.registerComponent(config)
} 