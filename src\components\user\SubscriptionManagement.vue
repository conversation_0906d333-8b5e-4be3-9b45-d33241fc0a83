<template>
  <div class="subscription-management">
    <div class="current-plan-info">
      <h4>{{ subscription?.planName }}</h4>
      <p class="plan-description">{{ subscription?.description }}</p>
      
      <div class="plan-details">
        <div class="detail-row">
          <span class="label">{{ t('userSettings.subscriptions.status') }}:</span>
          <span 
            class="status-badge"
            :class="`status-${subscription?.status.toLowerCase()}`"
          >
            {{ t(`userSettings.subscriptions.statuses.${subscription?.status.toLowerCase()}`) }}
          </span>
        </div>
        
        <div class="detail-row">
          <span class="label">{{ t('userSettings.subscriptions.nextBilling') }}:</span>
          <span class="value">{{ formatDate(subscription?.nextBillingDate) }}</span>
        </div>
        
        <div class="detail-row">
          <span class="label">{{ t('userSettings.subscriptions.amount') }}:</span>
          <span class="value">{{ formatCurrency(subscription?.price) }}</span>
        </div>
      </div>
    </div>

    <div class="management-options">
      <div class="option-section">
        <h5>{{ t('userSettings.subscriptions.paymentMethod') }}</h5>
        <div class="payment-method-info">
          <div class="current-method">
            <img 
              :src="getCardBrandIcon(subscription?.paymentMethod?.brand)" 
              :alt="subscription?.paymentMethod?.brand"
              class="card-icon"
            />
            <span>{{ subscription?.paymentMethod?.brand }} **** {{ subscription?.paymentMethod?.lastFour }}</span>
          </div>
          <IluriaButton
            @click="showChangePaymentModal = true"
            variant="outline"
            size="sm"
          >
            {{ t('userSettings.subscriptions.changePaymentMethod') }}
          </IluriaButton>
        </div>
      </div>

      <div class="option-section">
        <h5>{{ t('userSettings.subscriptions.billingAddress') }}</h5>
        <div class="billing-address-info">
          <div v-if="billingAddress" class="address-display">
            <p>{{ billingAddress.street }}, {{ billingAddress.number }}</p>
            <p v-if="billingAddress.complement">{{ billingAddress.complement }}</p>
            <p>{{ billingAddress.city }}, {{ billingAddress.state }} - {{ billingAddress.zipCode }}</p>
          </div>
          <div v-else class="no-address">
            <p>{{ t('userSettings.subscriptions.noBillingAddress') }}</p>
          </div>
          <IluriaButton
            @click="showAddressModal = true"
            variant="outline"
            size="sm"
          >
            {{ billingAddress ? t('common.edit') : t('common.add') }}
          </IluriaButton>
        </div>
      </div>

      <div class="option-section">
        <h5>{{ t('userSettings.subscriptions.notifications') }}</h5>
        <div class="notification-settings">
          <div class="notification-item">
            <div class="notification-info">
              <span class="notification-label">{{ t('userSettings.subscriptions.billingReminders') }}</span>
              <small class="notification-description">
                {{ t('userSettings.subscriptions.billingRemindersDesc') }}
              </small>
            </div>
            <IluriaToggleSwitch
              v-model="notificationSettings.billingReminders"
              @change="updateNotificationSettings"
            />
          </div>
          
          <div class="notification-item">
            <div class="notification-info">
              <span class="notification-label">{{ t('userSettings.subscriptions.renewalNotices') }}</span>
              <small class="notification-description">
                {{ t('userSettings.subscriptions.renewalNoticesDesc') }}
              </small>
            </div>
            <IluriaToggleSwitch
              v-model="notificationSettings.renewalNotices"
              @change="updateNotificationSettings"
            />
          </div>
          
          <div class="notification-item">
            <div class="notification-info">
              <span class="notification-label">{{ t('userSettings.subscriptions.planUpdates') }}</span>
              <small class="notification-description">
                {{ t('userSettings.subscriptions.planUpdatesDesc') }}
              </small>
            </div>
            <IluriaToggleSwitch
              v-model="notificationSettings.planUpdates"
              @change="updateNotificationSettings"
            />
          </div>
        </div>
      </div>

      <div class="option-section danger-section">
        <h5>{{ t('userSettings.subscriptions.dangerZone') }}</h5>
        <div class="danger-actions">
          <div class="danger-item">
            <div class="danger-info">
              <span class="danger-label">{{ t('userSettings.subscriptions.pauseSubscription') }}</span>
              <small class="danger-description">
                {{ t('userSettings.subscriptions.pauseSubscriptionDesc') }}
              </small>
            </div>
            <IluriaButton
              @click="pauseSubscription"
              variant="outline"
              severity="warning"
              size="sm"
            >
              {{ t('userSettings.subscriptions.pause') }}
            </IluriaButton>
          </div>
          
          <div class="danger-item">
            <div class="danger-info">
              <span class="danger-label">{{ t('userSettings.subscriptions.cancelSubscription') }}</span>
              <small class="danger-description">
                {{ t('userSettings.subscriptions.cancelSubscriptionDesc') }}
              </small>
            </div>
            <IluriaButton
              @click="confirmCancelSubscription"
              variant="outline"
              severity="danger"
              size="sm"
            >
              {{ t('userSettings.subscriptions.cancel') }}
            </IluriaButton>
          </div>
        </div>
      </div>
    </div>

    <div class="form-actions">
      <IluriaButton
        @click="$emit('cancel')"
        variant="ghost"
      >
        {{ t('common.close') }}
      </IluriaButton>
    </div>

    <!-- Modal para trocar método de pagamento -->
    <IluriaModal
      v-model:visible="showChangePaymentModal"
      :title="t('userSettings.subscriptions.changePaymentMethod')"
      size="md"
    >
      <ChangePaymentMethodForm
        :currentMethod="subscription?.paymentMethod"
        @method-changed="onPaymentMethodChanged"
        @cancel="showChangePaymentModal = false"
      />
    </IluriaModal>

    <!-- Modal para endereço de cobrança -->
    <IluriaModal
      v-model:visible="showAddressModal"
      :title="t('userSettings.subscriptions.billingAddress')"
      size="md"
    >
      <BillingAddressForm
        :address="billingAddress"
        @address-saved="onAddressSaved"
        @cancel="showAddressModal = false"
      />
    </IluriaModal>

    <!-- Confirmação de cancelamento -->
    <IluriaConfirmationModal
      :is-visible="showCancelConfirm"
      :title="t('userSettings.subscriptions.cancelTitle')"
      :message="t('userSettings.subscriptions.cancelMessage')"
      type="error"
      :confirm-text="t('userSettings.subscriptions.confirmCancel')"
      :cancel-text="t('cancel')"
      @confirm="cancelSubscription"
      @cancel="showCancelConfirm = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import ChangePaymentMethodForm from './ChangePaymentMethodForm.vue'
import BillingAddressForm from './BillingAddressForm.vue'

// Composables
const { t } = useI18n()
const { showSuccess, showError } = useToast()

// Props
const props = defineProps({
  subscription: {
    type: Object,
    required: true
  }
})

// Emits
const emit = defineEmits(['updated', 'cancel'])

// State
const billingAddress = ref(null)
const notificationSettings = ref({
  billingReminders: true,
  renewalNotices: true,
  planUpdates: false
})
const showChangePaymentModal = ref(false)
const showAddressModal = ref(false)
const showCancelConfirm = ref(false)

// Methods
const getCardBrandIcon = (brand) => {
  const icons = {
    visa: '/assets/img/cards/visa.png',
    mastercard: '/assets/img/cards/mastercard.png',
    amex: '/assets/img/cards/amex.png',
    elo: '/assets/img/cards/elo.png'
  }
  return icons[brand?.toLowerCase()] || '/assets/img/cards/generic.png'
}

const formatDate = (date) => {
  return new Date(date).toLocaleDateString()
}

const formatCurrency = (amount) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(amount)
}

const loadBillingAddress = async () => {
  try {
    // TODO: Implementar chamada para API
    billingAddress.value = null
  } catch (error) {
    console.error('Erro ao carregar endereço:', error)
  }
}

const loadNotificationSettings = async () => {
  try {
    // TODO: Implementar chamada para API
  } catch (error) {
    console.error('Erro ao carregar configurações:', error)
  }
}

const updateNotificationSettings = async () => {
  try {
    // TODO: Implementar chamada para API
    showSuccess(t('userSettings.subscriptions.success.notificationsUpdated'))
  } catch (error) {
    showError(t('userSettings.subscriptions.errors.updateNotifications'))
  }
}

const pauseSubscription = async () => {
  try {
    // TODO: Implementar pausa da assinatura
    showSuccess(t('userSettings.subscriptions.success.paused'))
    emit('updated')
  } catch (error) {
    showError(t('userSettings.subscriptions.errors.pause'))
  }
}

const confirmCancelSubscription = () => {
  showCancelConfirm.value = true
}

const cancelSubscription = async () => {
  try {
    // TODO: Implementar cancelamento
    showSuccess(t('userSettings.subscriptions.success.cancelled'))
    emit('updated')
  } catch (error) {
    showError(t('userSettings.subscriptions.errors.cancel'))
  }
}

const onPaymentMethodChanged = () => {
  showChangePaymentModal.value = false
  showSuccess(t('userSettings.subscriptions.success.paymentMethodChanged'))
  emit('updated')
}

const onAddressSaved = () => {
  showAddressModal.value = false
  loadBillingAddress()
  showSuccess(t('userSettings.subscriptions.success.addressSaved'))
}

// Lifecycle
onMounted(() => {
  loadBillingAddress()
  loadNotificationSettings()
})
</script>

<style scoped>
.subscription-management {
  padding: 1rem 0;
}

.current-plan-info {
  margin-bottom: 2rem;
  padding: 1.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
}

.current-plan-info h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1.25rem;
  color: var(--iluria-color-text-primary);
}

.plan-description {
  margin: 0 0 1rem 0;
  color: var(--iluria-color-text-muted);
}

.plan-details {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.detail-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.label {
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
}

.value {
  color: var(--iluria-color-text-primary);
}

.status-badge {
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-active {
  background: var(--iluria-color-success-light);
  color: var(--iluria-color-success);
}

.management-options {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.option-section {
  padding: 1.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-bg);
}

.option-section h5 {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  color: var(--iluria-color-text-primary);
}

.payment-method-info,
.billing-address-info {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.current-method {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.card-icon {
  height: 20px;
}

.address-display p {
  margin: 0 0 0.25rem 0;
  color: var(--iluria-color-text-secondary);
}

.no-address p {
  margin: 0;
  color: var(--iluria-color-text-muted);
  font-style: italic;
}

.notification-settings {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.notification-info {
  flex: 1;
}

.notification-label {
  display: block;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 0.25rem;
}

.notification-description {
  color: var(--iluria-color-text-muted);
  font-size: 0.875rem;
}

.danger-section {
  border-color: var(--iluria-color-danger-light);
  background: var(--iluria-color-danger-light);
}

.danger-section h5 {
  color: var(--iluria-color-danger);
}

.danger-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.danger-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 1rem;
}

.danger-info {
  flex: 1;
}

.danger-label {
  display: block;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 0.25rem;
}

.danger-description {
  color: var(--iluria-color-text-muted);
  font-size: 0.875rem;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
}

@media (max-width: 768px) {
  .payment-method-info,
  .billing-address-info,
  .notification-item,
  .danger-item {
    flex-direction: column;
    align-items: stretch;
    gap: 0.75rem;
  }
  
  .current-method {
    justify-content: center;
  }
}
</style>
