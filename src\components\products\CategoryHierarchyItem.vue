<template>
  <div class="space-y-3">
    <!-- Header da categoria -->
    <div 
      @click="$emit('toggle-collapse', category.id)"
      class="flex items-center justify-between p-3 rounded-lg cursor-pointer transition-all duration-200"
      :style="{ 
        marginLeft: `${level * 0.5}rem`,
        background: level === 1 ? 'var(--iluria-color-sidebar-bg)' : 
                   level === 2 ? 'var(--iluria-color-container-bg)' : 
                   'var(--iluria-color-surface)',
        boxShadow: 'var(--iluria-shadow-sm)'
      }"
      @mouseenter="$event.target.style.boxShadow = 'var(--iluria-shadow-md)'"
      @mouseleave="$event.target.style.boxShadow = 'var(--iluria-shadow-sm)'"
    >
      <div class="flex items-center space-x-3">
        <div 
          :class="[
            'flex items-center justify-center rounded-lg',
            level === 1 ? 'w-8 h-8 bg-[var(--color-primary)]/10' :
            level === 2 ? 'w-7 h-7 bg-[var(--color-primary)]/15' :
            'w-6 h-6 bg-[var(--color-accent)]/15'
          ]"
        >
          <svg 
            :class="[
              'text-[var(--color-primary)]',
              level === 1 ? 'w-4 h-4' :
              level === 2 ? 'w-3.5 h-3.5' :
              'w-3 h-3'
            ]" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
          </svg>
        </div>
        <div>
          <h4 
            :class="[
              'font-medium text-[var(--color-text)]',
              level === 1 ? 'text-base' :
              level === 2 ? 'text-sm' :
              'text-xs'
            ]"
          >
            {{ category.name }}
          </h4>
          <div 
            :class="[
              'text-[var(--color-text-light)] flex items-center space-x-2',
              level === 1 ? 'text-xs' :
              level === 2 ? 'text-xs' :
              'text-xs'
            ]"
          >
            <span>{{ category.directAttributes.length }} atributo(s)</span>
            <span v-if="category.children && category.children.length > 0" class="flex items-center space-x-1">
              <span class="text-[var(--color-text-lighter)]">•</span>
              <span>{{ category.children.length }} sub</span>
            </span>
          </div>
        </div>
      </div>
      
      <!-- Ícone de collapse -->
      <svg 
        :class="[
          'text-[var(--color-text-light)] transition-transform duration-200',
          level === 1 ? 'w-4 h-4' : 'w-3.5 h-3.5',
          collapsedCategories[category.id] ? 'transform rotate-180' : ''
        ]" 
        fill="none" 
        stroke="currentColor" 
        viewBox="0 0 24 24"
      >
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
      </svg>
    </div>
    
    <!-- Conteúdo da categoria -->
    <div v-show="!collapsedCategories[category.id]" class="space-y-3">
      <!-- Atributos diretos da categoria -->
      <div v-if="category.directAttributes.length > 0" :style="{ marginLeft: `${(level + 1) * 0.5}rem` }" class="space-y-3">
        <div
          v-for="attribute in category.directAttributes"
          :key="attribute.id"
          class="rounded-lg transition-all duration-200"
          style="background: var(--iluria-color-container-bg); box-shadow: var(--iluria-shadow-sm);"
          @mouseenter="$event.target.style.boxShadow = 'var(--iluria-shadow-md)'"
          @mouseleave="$event.target.style.boxShadow = 'var(--iluria-shadow-sm)'"
        >
          <div 
            :class="[
              level === 1 ? 'p-4' :
              level === 2 ? 'p-3' :
              'p-2'
            ]"
          >
            <div class="flex items-center justify-between">
              <div class="flex items-center space-x-3">
                <!-- Ícone do atributo -->
                <div 
                  :class="[
                    'flex items-center justify-center rounded-lg',
                    level === 1 ? 'w-10 h-10 bg-[var(--color-primary)]/10' :
                    level === 2 ? 'w-8 h-8 bg-[var(--color-accent)]/10' :
                    'w-6 h-6 bg-[var(--color-warning)]/10'
                  ]"
                >
                  <svg 
                    :class="[
                      level === 1 ? 'w-5 h-5 text-[var(--color-primary)]' :
                      level === 2 ? 'w-4 h-4 text-[var(--color-accent)]' :
                      'w-3 h-3 text-[var(--color-warning)]'
                    ]" 
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                  </svg>
                </div>
                
                <div>
                  <h5 
                    :class="[
                      'font-medium text-[var(--color-text)]',
                      level === 1 ? 'text-base' :
                      level === 2 ? 'text-sm' :
                      'text-xs'
                    ]"
                  >
                    {{ attribute.name }}
                  </h5>
                  <div class="flex items-center space-x-3 mt-1">
                    <span 
                      :class="[
                        'text-[var(--color-text-light)]',
                        level === 1 ? 'text-xs' :
                        level === 2 ? 'text-xs' :
                        'text-xs'
                      ]"
                    >
                      {{ attribute.valuesCount || 0 }} valor(es)
                    </span>
                    <span class="text-xs text-[var(--color-text-lighter)]">•</span>
                    <span 
                      :class="[
                        'text-[var(--color-text-light)]',
                        level === 1 ? 'text-xs' :
                        level === 2 ? 'text-xs' :
                        'text-xs'
                      ]"
                    >
                      {{ attribute.usageCount || 0 }} uso(s)
                    </span>
                  </div>
                </div>
              </div>

              <div class="flex items-center space-x-2">
                <!-- Badge de status -->
                <span 
                  :class="[
                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                    attribute.active ? 'bg-[var(--color-success)]/10 text-[var(--color-success)]' : 'bg-[var(--color-text-light)]/10 text-[var(--color-text-light)]'
                  ]"
                >
                  {{ attribute.active ? 'Ativo' : 'Inativo' }}
                </span>

                <!-- Botões de ação -->
                <button
                  @click="$emit('edit-attribute', attribute)"
                  class="p-1.5 text-[var(--color-text-light)] hover:text-[var(--color-primary)] rounded-full hover:bg-[var(--color-primary)]/10"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
                  </svg>
                </button>
                
                <button
                  @click="$emit('delete-attribute', attribute)"
                  class="p-1.5 text-[var(--color-text-light)] hover:text-[var(--color-danger)] rounded-full hover:bg-[var(--color-danger)]/10"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 7l-.867 12.142A2 2 0 0116.138 21H7.862a2 2 0 01-1.995-1.858L5 7m5 4v6m4-6v6m1-10V4a1 1 0 00-1-1h-4a1 1 0 00-1 1v3M4 7h16"/>
                  </svg>
                </button>
              </div>
            </div>

            <!-- Valores do atributo -->
            <div v-if="attribute.values && attribute.values.length > 0" class="mt-3">
              <p class="text-xs font-medium text-[var(--color-text)] mb-2">Valores:</p>
              <div class="flex flex-wrap gap-1">
                <span
                  v-for="value in attribute.values.slice(0, level === 1 ? 8 : level === 2 ? 6 : 4)"
                  :key="value.id"
                  :class="[
                    'inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium',
                    level === 1 ? 'bg-[var(--color-primary)]/10 text-[var(--color-primary)]' :
                    level === 2 ? 'bg-[var(--color-accent)]/10 text-[var(--color-accent)]' :
                    'bg-[var(--color-warning)]/10 text-[var(--color-warning)]'
                  ]"
                >
                  {{ value.value }}
                  <span v-if="value.usageCount" class="ml-1">({{ value.usageCount }})</span>
                </span>
                <span
                  v-if="attribute.values.length > (level === 1 ? 8 : level === 2 ? 6 : 4)"
                  class="inline-flex items-center px-2 py-0.5 rounded-full text-xs font-medium bg-[var(--color-text-light)]/10 text-[var(--color-text-light)]"
                >
                  +{{ attribute.values.length - (level === 1 ? 8 : level === 2 ? 6 : 4) }} mais
                </span>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Subcategorias recursivas -->
      <div v-if="category.children && category.children.length > 0">
        <CategoryHierarchyItem 
          v-for="subcategory in category.children" 
          :key="subcategory.id"
          :category="subcategory"
          :level="level + 1"
          :collapsed-categories="collapsedCategories"
          @toggle-collapse="$emit('toggle-collapse', $event)"
          @edit-attribute="$emit('edit-attribute', $event)"
          @delete-attribute="$emit('delete-attribute', $event)"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 1
  },
  collapsedCategories: {
    type: Object,
    required: true
  }
});

defineEmits(['toggle-collapse', 'edit-attribute', 'delete-attribute']);
</script> 
