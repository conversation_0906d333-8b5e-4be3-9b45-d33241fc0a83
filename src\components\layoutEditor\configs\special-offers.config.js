export default {
  name: 'Special Offers',
  type: 'special-offers',
  dataComponent: 'special-offers',
  elementType: 'special-offers',
  className: 'iluria-special-offers',
  selectors: ['[data-component="special-offers"]', '.iluria-special-offers'],
  category: 'marketing',
  description: 'Seção de ofertas especiais com destaque visual e call-to-action',
  priority: 80,
  icon: 'local_offer',
  
  html: `<div data-component="special-offers" 
              data-element-type="special-offers" 
              class="iluria-special-offers"
              data-title="Ofertas Especiais"
              data-subtitle="Aproveite nossas promoções exclusivas com descontos imperdíveis"
              data-offers='[{"id":"offer-1","title":"Super Desconto","description":"Aproveite 50% OFF em todos os produtos selecionados","badge":"50% OFF","discount":{"value":50,"type":"percentage"},"originalPrice":199.90,"finalPrice":99.90,"linkUrl":"","linkText":"Aproveitar oferta","active":true},{"id":"offer-2","title":"Frete Grátis","description":"Compre acima de R$ 99 e ganhe frete grátis","badge":"FRETE GRÁTIS","discount":{"value":0,"type":"percentage"},"originalPrice":0,"finalPrice":0,"linkUrl":"","linkText":"Ver produtos","active":true}]'
              data-columns="3"
              data-spacing="24"
              data-card-padding="20"
              data-border-radius="12"
              data-section-background="#ffffff"
              data-card-background="#f8f9fa"
              data-title-color="#1a1a1a"
              data-text-color="#666666"
              data-accent-color="#2563eb"
              data-badge-color="#ef4444"
              data-section-title-font-size="40"
              data-card-title-font-size="24"
              data-price-font-size="20"
              data-badge-font-size="12"
              style="width: 100%; background: #ffffff; padding: 4rem 2rem; position: relative;">
    
    <div class="special-offers-container" style="max-width: 1200px; margin: 0 auto;">
      <div class="special-offers-header" style="text-align: center; margin-bottom: 3rem;">
        <h2 class="special-offers-title" style="font-size: 40px; font-weight: bold; margin: 0 0 1rem; color: #1a1a1a; line-height: 1.2;">Ofertas Especiais</h2>
        <p class="special-offers-subtitle" style="font-size: 1.1rem; margin: 0; color: #666666; line-height: 1.4; max-width: 600px; margin: 0 auto;">Aproveite nossas promoções exclusivas com descontos imperdíveis</p>
      </div>
      
      <div class="offers-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 24px; grid-auto-rows: max-content;">
        <div class="offer-item" data-offer-id="offer-1" style="background: #f8f9fa; border-radius: 12px; padding: 20px; position: relative; transition: all 0.3s ease; border: 1px solid #e9ecef; display: flex; flex-direction: column; height: 100%;">
          <div class="offer-badge" style="position: absolute; top: -8px; right: 16px; background: #ef4444; color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; z-index: 2;">
            50% OFF
          </div>
          <div class="offer-content" style="display: flex; flex-direction: column; flex: 1;">
            <h3 class="offer-title" style="font-size: 24px; font-weight: 700; margin: 0 0 0.5rem; color: #1a1a1a; line-height: 1.3;">
              Super Desconto
            </h3>
            <p class="offer-description" style="color: #666666; margin: 0 0 1rem; line-height: 1.4; font-size: 0.95rem; flex: 1;">
              Aproveite 50% OFF em todos os produtos selecionados
            </p>
            <div class="offer-pricing" style="margin: 1rem 0;">
              <span class="original-price" style="text-decoration: line-through; color: #999; font-size: 0.9rem; margin-right: 0.5rem;">
                R$ 199,90
              </span>
              <span class="final-price" style="color: #2563eb; font-size: 20px; font-weight: 700;">
                R$ 99,90
              </span>
            </div>
            <a href="#" class="offer-button" style="background: #2563eb; color: white; padding: 12px 24px; border: none; border-radius: 8px; font-size: 0.9rem; font-weight: 600; text-decoration: none; display: inline-block; transition: all 0.3s ease; width: 100%; text-align: center; box-sizing: border-box; margin-top: auto;">
              Aproveitar oferta
            </a>
          </div>
        </div>
        
        <div class="offer-item" data-offer-id="offer-2" style="background: #f8f9fa; border-radius: 12px; padding: 20px; position: relative; transition: all 0.3s ease; border: 1px solid #e9ecef; display: flex; flex-direction: column; height: 100%;">
          <div class="offer-badge" style="position: absolute; top: -8px; right: 16px; background: #ef4444; color: white; padding: 6px 12px; border-radius: 20px; font-size: 12px; font-weight: 600; z-index: 2;">FRETE GRÁTIS</div>
          <div class="offer-content" style="display: flex; flex-direction: column; flex: 1;">
            <h3 class="offer-title" style="font-size: 24px; font-weight: 700; margin: 0 0 0.5rem; color: #1a1a1a; line-height: 1.3;">Frete Grátis</h3>
            <p class="offer-description" style="color: #666666; margin: 0 0 1rem; line-height: 1.4; font-size: 0.95rem; flex: 1;">
              Compre acima de R$ 99 e ganhe frete grátis
            </p>
            <div class="offer-pricing" style="margin: 1rem 0; min-height: 28px;">
              <span class="offer-highlight" style="color: #2563eb; font-size: 16px; font-weight: 600;">Compras acima de R$ 99</span>
            </div>
            <a href="#" class="offer-button" style="background: #2563eb; color: white; padding: 12px 24px; border: none; border-radius: 8px; font-size: 0.9rem; font-weight: 600; text-decoration: none; display: inline-block; transition: all 0.3s ease; width: 100%; text-align: center; box-sizing: border-box; margin-top: auto;">
              Ver produtos
            </a>
          </div>
        </div>
      </div>
    </div>
    
    <style>
      .iluria-special-offers .offers-grid {
        align-items: stretch;
        grid-auto-rows: max-content;
      }
      
      .iluria-special-offers .offer-item {
        min-height: 280px;
        box-sizing: border-box;
      }
      

      
      @media (max-width: 992px) {
        .iluria-special-offers .offers-grid { 
          grid-template-columns: repeat(2, 1fr) !important; 
        }
      }
      
      @media (max-width: 768px) {
        .iluria-special-offers .offers-grid { 
          grid-template-columns: 1fr !important; 
          gap: 1rem !important;
        }
        .iluria-special-offers .special-offers-title { 
          font-size: 32px !important; 
        }
        .iluria-special-offers { 
          padding: 2rem 1rem !important; 
        }
        .iluria-special-offers .offer-item {
          min-height: auto;
        }
      }
      
      .iluria-special-offers .offer-item:hover {
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0,0,0,0.1);
      }
      
      .iluria-special-offers .offer-button:hover {
        background: #1d4ed8 !important;
        transform: translateY(-1px);
      }
      
      .iluria-special-offers {
        margin: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
      }
      
      .component-container:has(.iluria-special-offers) .element-content {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      .element-content:has(.iluria-special-offers) {
        padding: 0 !important;
        margin: 0 !important;
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'special-offers-config',
      title: 'Configurar Ofertas',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><circle cx="12" cy="12" r="3"/><path d="m12 1 1.735 3.47 3.465.686L19.196 7.5 19.5 9.5l-3.032-1.735L12 1z"/></svg>`,
      condition: (element) => element?.getAttribute('data-component') === 'special-offers'
    }
  ],
  
  initialization: { autoInit: true, scriptLoader: 'initializeSpecialOffersComponent' },
  cleanup: { preserveStyles: ['background', 'color', 'padding', 'margin', 'border-radius', 'position', 'overflow', 'display', 'flex-direction'] },
  detection: { priority: 65 }
} 