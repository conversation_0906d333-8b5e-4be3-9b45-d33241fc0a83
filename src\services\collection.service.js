import axios from 'axios';
import API_URLS from '@/config/api.config';

class CollectionService {
    #endpoint;

    constructor() {
        this.#endpoint = `${API_URLS.COLLECTION_BASE_URL}`;
    }

    async getAllCollections(page, size) {
        try {
            const response = await axios.get(`${this.#endpoint}`, {
                params: { page, size }
            });
            return {
                content: response.data.content || [],
                totalPages: response.data.totalPages || 0
            };
        } catch (error) {
            console.error('Error fetching collections:', error);
            throw error;
        }
    }

    async getCollection(id) {
        try {
            const response = await axios.get(`${this.#endpoint}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching collection:', error);
            throw error;
        }
    }

    async createCollection(collectionData) {
        try {
            const response = await axios.post(`${this.#endpoint}`, collectionData);
            return response.data;
        } catch (error) {
            console.error('Error creating collection:', error);
            throw error;
        }
    }

    async updateCollection(id, collectionData) {
        try {
            const response = await axios.put(`${this.#endpoint}/${id}`, collectionData);
            return response.data;
        } catch (error) {
            console.error('Error updating collection:', error);
            throw error;
        }
    }

    async deleteCollection(id) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting collection:', error);
            throw error;
        }
    }
}

export default new CollectionService();