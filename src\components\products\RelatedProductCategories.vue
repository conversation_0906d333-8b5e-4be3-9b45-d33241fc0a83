<template>
  <div class="related-category-container">
    <!-- Search Input -->
    <div class="relative mb-4">
      <input 
        type="search" 
        v-model="searchQuery"
        class="block w-full p-2 text-sm rounded-lg bg-white"
        placeholder="Buscar categorias..."
        :disabled="isLoading"
      />
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center p-4">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-500"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="text-red-500 p-2 text-center">
      Erro ao carregar categorias
    </div>
    
    <!-- Categories List -->
    <div v-else class="categories-list">
      <div v-if="filteredCategories.length === 0" class="text-center p-4 text-gray-500">
        Nenhuma categoria encontrada
      </div>
      <div v-else v-for="category in filteredCategories" :key="`related-${category.id}`" class="text-left">
        <RelatedCategoryItem 
          :category="category" 
          :toggleExpand="toggleExpand"
          :toggleCheck="toggleCheck"
          :keyPrefix="'related'"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import RelatedCategoryItem from '@/components/products/RelatedCategoryItem.vue';
import { categoryService } from '@/services/category.service';

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue']);

const searchQuery = ref('');
const isLoading = ref(false);
const error = ref(null);
const categories = ref([]);

const filterCategories = (items, query) => {
  if (!query) return items;

  return items.map(category => {
    const matchesSearch = category.name.toLowerCase().includes(query.toLowerCase());
    
    let filteredChildren = [];
    if (category.children && category.children.length > 0) {
      filteredChildren = filterCategories(category.children, query);
    }

    if (matchesSearch || filteredChildren.length > 0) {
      return {
        ...category,
        children: filteredChildren,
        expanded: filteredChildren.length > 0 ? true : category.expanded
      };
    }
    
    return null;
  }).filter(Boolean);
};

const filteredCategories = computed(() => {
  return filterCategories(categories.value, searchQuery.value);
});

const toggleExpand = (category) => {
  category.expanded = !category.expanded;
};

const toggleCheck = (category) => {
  category.checked = !category.checked;
  
  if (!category.checked && category.children && category.children.length > 0) {
    propagateToChildren(category);
  }
  
  if (category.checked) {
    checkParents(category);
  }
  
  const selectedCategories = getSelectedCategories(categories.value);
  emit('update:modelValue', selectedCategories);
};

const propagateToChildren = (category) => {
  if (!category.children || category.children.length === 0) return;
  
  category.children.forEach(child => {
    child.checked = false;
    propagateToChildren(child);
  });
};

const findParentCategory = (categoryId, categories) => {
  for (const category of categories) {
    if (category.children) {
      for (const child of category.children) {
        if (child.id === categoryId) {
          return category;
        }
        const found = findParentCategory(categoryId, [child]);
        if (found) return found;
      }
    }
  }
  return null;
};

const checkParents = (category) => {
  let parent = findParentCategory(category.id, categories.value);
  while (parent) {
    parent.checked = true;
    parent = findParentCategory(parent.id, categories.value);
  }
};

const getSelectedCategories = (items) => {
  let selected = [];
  
  for (const item of items) {
    if (item.checked) {
      selected.push(item.id);
    }
    if (item.children && item.children.length > 0) {
      selected = selected.concat(getSelectedCategories(item.children));
    }
  }
  
  return selected;
};

const fetchCategories = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    const data = await categoryService.fetchCategories();
    categories.value = formatCategories(data);
    initializeCheckedState();
  } catch (err) {
    console.error('Erro ao carregar categorias relacionadas', err);
    error.value = 'Erro ao carregar categorias';
  } finally {
    isLoading.value = false;
  }
};

const formatCategories = (backendCategories) => {
  const formatCategory = (category) => {
    return {
      id: category.id,
      name: category.title,
      checked: false,
      expanded: false,
      children: category.children ? category.children.map(child => formatCategory(child)) : []
    };
  };

  return backendCategories.map(category => formatCategory(category));
};

const initializeCheckedState = () => {
  if (!categories.value.length || !props.modelValue.length) {
    return;
  }
  
  const setChecked = (items, selectedIds) => {
    for (const item of items) {
      if (selectedIds.includes(item.id)) {
        item.checked = true;
      }
      if (item.children && item.children.length > 0) {
        setChecked(item.children, selectedIds);
      }
    }
  };
  
  setChecked(categories.value, props.modelValue);
};

onMounted(() => {
  fetchCategories();
});

watch(() => props.modelValue, (newValue, oldValue) => {
  initializeCheckedState();
}, { immediate: true });
</script>

<style scoped>
.related-category-container {
  min-height: 200px;
  max-height: 300px;
}

.categories-list {
  overflow-y: auto;
  max-height: 250px;
}
</style> 
