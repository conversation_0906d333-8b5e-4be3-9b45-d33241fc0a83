{"title": "Measurement Tables", "subtitle": "Manage measurement tables for products like clothing and footwear", "searchPlaceholder": "Search measurement tables...", "noTables": "No measurement tables found", "noTablesDescription": "Create your first measurement table to get started", "noImage": "No image", "name": "Name", "namePlaceholder": "Enter table name", "type": "Type", "typeImage": "Image", "typeStructured": "Structured", "typeIMAGE": "Image", "typeSTRUCTURED": "Structured", "targetGender": "Target Audience", "genderUnissex": "Unisex", "genderFeminino": "Female", "genderMasculino": "Male", "genderInfantil": "Children", "genderUNISSEX": "Unisex", "genderFEMININO": "Female", "genderMASCULINO": "Male", "genderINFANTIL": "Children", "image": "Image", "imageDescription": "Upload an image showing product measurements", "addImage": "Add image", "changeImage": "Change image", "removeImage": "Remove image", "imageFormats": "PNG, JPG, GIF up to 15MB", "imageTooLarge": "Image cannot be larger than 15MB", "uploadImage": "Click to upload", "orDragDrop": "or drag and drop", "replaceImage": "Replace image", "imageUploadSuccess": "Image uploaded successfully", "imageUploadError": "Error uploading image", "imageDeleteSuccess": "Image removed successfully", "imageDeleteError": "Error removing image", "columns": "Measurement Columns", "addColumn": "Add Column", "columnLabel": "Measurement Name", "columnLabelPlaceholder": "Ex: <PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>", "unit": "Unit", "unitPlaceholder": "cm", "columnImage": "Icon", "columnsCount": "Measurements", "rows": "Sizes", "addRow": "<PERSON><PERSON>", "sizeLabel": "Size", "sizeLabelPlaceholder": "<PERSON>, <PERSON>, <PERSON>, 36, 38...", "sizesCount": "Sizes", "valuePlaceholder": "0", "preview": "Preview", "previewEmpty": "Configure columns and rows to see preview", "visualization": "Visualization", "size": "Size", "createNew": "New Table", "createSubtitle": "Create a new measurement table", "editTable": "Edit Table", "editSubtitle": "Edit table information", "createdSuccess": "Table created successfully", "updatedSuccess": "Table updated successfully", "errorLoading": "Error loading table", "errorCreating": "Error creating table", "errorUpdating": "Error updating table", "errorDeleting": "Error deleting table", "deleteTitle": "Confirm deletion", "deleteMessage": "Are you sure you want to delete table \"{name}\"?", "deletedSuccess": "Table deleted successfully", "selectTable": "Select measurement table", "selectTablePlaceholder": "Choose a table...", "noImageSelected": "No image selected", "typeRequired": "Type is required", "genderRequired": "Gender is required", "nameRequired": "Name is required", "saveSuccess": "Table saved successfully", "saveError": "Error saving table", "columnTagsLabel": "Measurement columns", "columnTagsPlaceholder": "Enter column name (ex: <PERSON><PERSON>, W<PERSON>t, Hip)", "columnTagsHint": "Enter column name and press Enter or comma to add. Drag to reorder.", "sizeTagsLabel": "Available sizes", "sizeTagsPlaceholder": "Enter sizes (ex: S, M, L or 36, 38, 40)", "sizeTagsHint": "Enter sizes and press Enter or comma. Use buttons below for common ranges.", "quickSizeRanges": "Ready size ranges:", "sizeRangePPtoGG": "PP to GG", "sizeRangeXStoXL": "XS to XL", "sizeRangeXStoXXL": "XS to XXL", "sizeRangeNumeric": "36 to 48", "sizeRangeInfantil": "2 to 16", "sizeRangeBaby": "Baby (NB-24M)", "customRange": "Custom Range", "rangeStart": "Start", "rangeEnd": "End", "generateRange": "Generate", "customRangeHint": "Generates sizes by 2 (ex: 100, 102, 104...)", "measurementGrid": "Measurement Table", "selectUnit": "Unit", "sizes": "Sizes", "measurementPlaceholder": "Ex: 85-90, <PERSON>, <PERSON>", "imagePreviewTitle": "Measurement Preview", "imagePreviewDescription": "Product image. Hover over a table column to see where each measurement is taken", "hoverColumnHint": "Hover over columns to see where each measurement is taken", "uploadColumnImage": "Upload Column Image", "columnImageDescription": "Upload an image that explains where this measurement is taken", "columnImageHelp": "This image will be shown when user hovers over this column", "addColumnImage": "Add image", "changeColumnImage": "Change image", "removeColumnImage": "Remove image", "columnImageSaved": "Column image saved successfully", "createFooterNote": "Create a new measurement table", "createHelp": {"description": "Create a new measurement table with the structured type or the image type", "title": "Create a new measurement table", "subtitle": "Create a new measurement table", "image": "Create a new measurement table", "imageDescription": "Create a new measurement table", "imageHelp": "Create a new measurement table", "addColumn": "Create a new measurement table"}}