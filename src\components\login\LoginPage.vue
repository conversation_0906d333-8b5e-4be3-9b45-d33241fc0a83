<template>
  <div class="flex min-h-screen">
    <div class="left-column flex grow basis-xl items-center justify-center px-1">
      <div class="left-column-inner-container">
        <div class="text-center mb-6">
          <img src="@/assets/img/logo-iluria.svg" alt="Logo Iluria" class="logo-iluria" />
        </div>

        <slot></slot>
      </div>
    </div>
    <div class="hidden md:w-full md:block right-column"></div>
  </div>
</template>

<script setup>
// Using script setup for consistency with other components
</script>

<style scoped>
.left-column {
  background-color: white;
}

.right-column {
  background-color: #d6c3ae;
  background-image: url("@/assets/img/login/login-splash-4.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 75%;
}

.left-column-inner-container {
  margin: 25px auto;
  max-width: 350px;
  width: 100%;
}

.logo-iluria {
  display: block;
  margin: 0 auto;
  max-width: 200px;
  height: auto;
}

/* Animação de hover no logo */
.logo-iluria {
  transition: transform 0.2s ease;
}

.logo-iluria:hover {
  transform: scale(1.1);
}
</style>
