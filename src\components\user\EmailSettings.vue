<template>
  <div class="email-content">
    <!-- Alterar Email -->
    <ViewContainer
      :title="$t('emailSettings.title')"
      :subtitle="$t('emailSettings.subtitle')"
      :icon="MailAtSign01Icon"
      iconColor="blue"
    >
      <form @submit.prevent="changeEmail" class="email-form">
        <div class="current-email-display">
          <label class="email-label">{{ $t('emailSettings.currentEmailLabel') }}</label>
          <div class="email-info">
            <span class="email-text">{{ currentEmail }}</span>
            <span class="verified-badge">
              <HugeiconsIcon
                :icon="CheckmarkCircle02Icon"
                size="16"
                :strokeWidth="1.5"
                class="verified-icon"
              />
              Verificado
            </span>
          </div>
        </div>

        <div class="form-field">
          <IluriaInputText
            v-model="formData.newEmail"
            type="email"
            :label="$t('emailSettings.newEmailLabel')"
            :placeholder="$t('emailSettings.newEmailPlaceholder')"
            autocomplete="off"
            name="new-email-field"
          />
          <div class="field-error-container">
            <p v-if="emailError" class="field-error">
              {{ emailError }}
            </p>
          </div>
        </div>

        <div class="form-field">
          <IluriaInputText
            v-model="formData.currentPassword"
            type="password"
            :label="$t('emailSettings.currentPasswordLabel')"
            :placeholder="$t('emailSettings.currentPasswordPlaceholder')"
            autocomplete="current-password"
          />
        </div>

        <div class="form-actions">
          <IluriaButton
            type="submit"
            variant="solid"
            color="primary"
            :disabled="!canSubmit"
            :loading="isChanging"
            :hugeIcon="MailAtSign01Icon"
          >
            {{ $t('emailSettings.submitButton') }}
          </IluriaButton>
        </div>
      </form>
    </ViewContainer>

    <!-- MFA Verification Modal -->
    <MfaVerificationModal
      v-model:visible="showMfaModal"
      :mfa-type="userMfaType"
      :user-email="authStore.userEmail"
      :loading="isMfaVerifying"
      operation="email-change"
      @verified="handleMfaVerified"
      @resend-email="handleMfaResend"
    />

    <!-- New Email Verification Modal -->
    <NewEmailVerificationModal
      v-if="showNewEmailModal"
      :old-email="currentEmail"
      :new-email="formData.newEmail"
      :change-token="changeToken"
      :is-verifying="isVerifyingNewEmail"
      @verified="handleNewEmailVerified"
      @cancel="handleNewEmailCancel"
      @resend="handleNewEmailResend"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, nextTick } from 'vue'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import MfaVerificationModal from '@/components/modals/MfaVerificationModal.vue'
import NewEmailVerificationModal from '@/components/user/NewEmailVerificationModal.vue'
import authService from '@/services/auth.service'
import { HugeiconsIcon } from '@hugeicons/vue'
import { MailAtSign01Icon, CheckmarkCircle02Icon } from '@hugeicons-pro/core-stroke-standard'

// Composables
const authStore = useAuthStore()
const toast = useToast()
const { t } = useI18n()

// State
const currentEmail = ref('')
const formData = ref({
  newEmail: '',
  currentPassword: ''
})
const emailError = ref('')
const isChanging = ref(false)

// MFA State
const showMfaModal = ref(false)
const isMfaVerifying = ref(false)
const userMfaType = ref('EMAIL_VERIFICATION')
const pendingEmailData = ref(null)

// New Email Verification State
const showNewEmailModal = ref(false)
const isVerifyingNewEmail = ref(false)
const changeToken = ref('')

// Computed
const canSubmit = computed(() => {
  return formData.value.newEmail !== '' &&
         formData.value.currentPassword !== '' &&
         formData.value.newEmail !== currentEmail.value &&
         validateEmail(formData.value.newEmail)
})

// Methods
const validateEmail = (email) => {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

const changeEmail = async () => {
  if (!canSubmit.value) return
  
  // Verificar se usuário está logado
  if (!authStore.userLoggedIn || !authStore.userEmail) {
    toast.addToast(t('emailSettings.notLoggedIn'), 'error')
    return
  }
  
  // Validar formato do email
  if (!validateEmail(formData.value.newEmail)) {
    emailError.value = t('emailSettings.invalidEmail')
    return
  }
  
  // Validar se o email é diferente do atual
  if (formData.value.newEmail === currentEmail.value) {
    emailError.value = t('emailSettings.sameEmail')
    return
  }
  
  emailError.value = ''
  isChanging.value = true
  
  try {
    // PRIMEIRO: Validar senha atual
    const passwordValidation = await authStore.validateCurrentPassword(formData.value.currentPassword)
    
    if (!passwordValidation.valid) {
      toast.addToast(passwordValidation.message || t('emailSettings.invalidPassword'), 'error')
      isChanging.value = false
      return
    }
    
    // SEGUNDO: Tentar alterar o email (backend determinará se precisa MFA)
    const response = await authStore.changeEmail(
      formData.value.currentPassword,
      formData.value.newEmail
    )
    
    // Se requer MFA
    if (response.requiresMfa) {
      // Armazenar dados do email para usar após a verificação MFA
      pendingEmailData.value = {
        currentPassword: formData.value.currentPassword,
        newEmail: formData.value.newEmail
      }
      
      // Verificar o tipo de MFA do usuário
      const mfaStatus = await authStore.getUserMfaStatus()
      userMfaType.value = mfaStatus.type
      
      // Se for EMAIL_VERIFICATION, gerar e enviar código inicial
      if (mfaStatus.type === 'EMAIL_VERIFICATION') {
        try {
          await authService.resendMfaCode(authStore.userEmail)
          toast.addToast(t('emailSettings.codeSent'), 'info')
        } catch (error) {
          toast.addToast(t('emailSettings.mfaSendError'), 'error')
          isChanging.value = false
          return
        }
      }
      
      showMfaModal.value = true
      isChanging.value = false
      return
    }
    
    if (response.success) {
      changeToken.value = response.changeToken
      showNewEmailModal.value = true
      toast.addToast(response.message || t('emailSettings.verificationSent'), 'info')
    } else {
      toast.addToast(response.message || t('emailSettings.changeError'), 'error')
    }
    
  } catch (error) {
    const errorMessage = error.message || t('emailSettings.unexpectedError')
    toast.addToast(errorMessage, 'error')
  } finally {
    isChanging.value = false
  }
}

// MFA Handlers
const handleMfaVerified = async (data) => {
  if (!pendingEmailData.value) {
    toast.addToast(t('emailSettings.noEmailData'), 'error')
    return
  }
  
  isMfaVerifying.value = true
  
  try {
    // Chamar o mesmo endpoint mas agora com o código MFA
    const response = await authStore.changeEmail(
      pendingEmailData.value.currentPassword,
      pendingEmailData.value.newEmail,
      data.code
    )
    
    if (response.success) {
      changeToken.value = response.changeToken
      
      // Fechar o modal MFA primeiro
      showMfaModal.value = false
      
      // Aguardar o próximo tick do Vue antes de abrir o novo modal
      await nextTick()
      
      // Agora abrir o modal de verificação do novo email
      showNewEmailModal.value = true
      
      toast.addToast(response.message || t('emailSettings.verificationSent'), 'info')
    } else {
      toast.addToast(response.message || t('emailSettings.changeError'), 'error')
    }
  } catch (error) {
    toast.addToast(error.message || t('emailSettings.mfaVerificationError'), 'error')
  } finally {
    isMfaVerifying.value = false
  }
}

// Watch for MFA modal close
watch(showMfaModal, (newValue, oldValue) => {
  // Só limpar os dados se não estivermos abrindo o modal do novo email
  if (oldValue === true && newValue === false && pendingEmailData.value && !showNewEmailModal.value) {
    // Aguardar um pequeno delay para garantir que não estamos no meio da transição
    setTimeout(() => {
      if (!showNewEmailModal.value) {
        pendingEmailData.value = null
        toast.addToast(t('emailSettings.cancelled'), 'info')
      }
    }, 100)
  }
})

const handleMfaResend = async () => {
  try {
    await authService.resendMfaCode(authStore.userEmail)
    toast.addToast(t('emailSettings.codeResent'), 'success')
  } catch (error) {
    toast.addToast(t('emailSettings.mfaResendError'), 'error')
  }
}

// New Email Verification Handlers
const handleNewEmailVerified = async (data) => {
  isVerifyingNewEmail.value = true
  
  try {
    const response = await authStore.verifyNewEmail(
      changeToken.value,
      data.verificationCode,
      formData.value.newEmail
    )
    
    if (response.success) {
      toast.addToast(response.message || t('emailSettings.emailChanged'), 'success')
      
      // Reset form e modal
      formData.value.currentPassword = ''
      formData.value.newEmail = ''
      showNewEmailModal.value = false
      pendingEmailData.value = null
      changeToken.value = ''
      
      // Atualizar email exibido
      currentEmail.value = authStore.userEmail
      
      // Notificar sobre invalidação das outras sessões
      toast.addToast(t('emailSettings.sessionsInvalidated'), 'info')
    } else {
      toast.addToast(response.message || t('emailSettings.verificationError'), 'error')
    }
  } catch (error) {
    toast.addToast(error.message || t('emailSettings.verificationError'), 'error')
  } finally {
    isVerifyingNewEmail.value = false
  }
}

const handleNewEmailCancel = async () => {
  showNewEmailModal.value = false
  
  // Cancel the email change request in the backend
  if (changeToken.value) {
    try {
      await authStore.cancelEmailChange()
      toast.addToast(t('emailSettings.verificationCancelled'), 'info')
    } catch (error) {
      console.error('Error cancelling email change:', error)
    }
  }
  
  changeToken.value = ''
  formData.value.newEmail = ''
}

const handleNewEmailResend = async () => {
  try {
    const response = await authStore.resendEmailVerification()
    
    if (response.success) {
      toast.addToast(response.message || t('emailSettings.codeResent'), 'success')
    } else {
      toast.addToast(response.message || t('emailSettings.resendError'), 'error')
    }
  } catch (error) {
    toast.addToast(error.message || t('emailSettings.resendError'), 'error')
  }
}

// Lifecycle
onMounted(async () => {
  if (authStore.userLoggedIn && authStore.userEmail) {
    currentEmail.value = authStore.userEmail
    
    // Check for pending email change request
    try {
      const pendingRequest = await authStore.checkPendingEmailChange()
      
      if (pendingRequest.hasPendingRequest) {
        // Restore state from pending request
        formData.value.newEmail = pendingRequest.newEmail
        changeToken.value = pendingRequest.changeToken
        
        // Open the verification modal automatically
        showNewEmailModal.value = true
        
        toast.addToast(t('emailSettings.pendingRequestFound'), 'info')
      }
    } catch (error) {
      console.error('Error checking pending email change:', error)
    }
  }
})

</script>

<style scoped>
.email-content {
  width: 100%;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.current-email-display {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.email-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.email-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem 1rem;
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
}

.email-text {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  font-family: monospace;
  flex: 1;
}

.verified-badge {
  display: flex;
  align-items: center;
  gap: 0.25rem;
  padding: 0.25rem 0.5rem;
  background: var(--iluria-color-success-light);
  color: var(--iluria-color-success-dark);
  border-radius: 4px;
  font-size: 12px;
  font-weight: 500;
}

.verified-icon {
  color: var(--iluria-color-success);
}

.email-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.field-error-container {
  min-height: 20px;
}

.field-error {
  font-size: 12px;
  color: var(--iluria-color-error);
  margin: 0;
  padding: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .form-actions {
    justify-content: stretch;
  }

  .form-actions button {
    width: 100%;
  }
}
</style>