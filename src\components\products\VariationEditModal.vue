<template>
  <IluriaModal v-model="visible" :title="t('product.editVariations')" @save="save">
    <Form v-slot="$modalForm" @submit.prevent="save" :validate-on-blur="false" :validate-on-value-update="false" :initial-values="editingData">
      <div class="space-y-4">
        <div class="grid grid-cols-2 gap-4">
          <div>
            <IluriaInputText
              id="price"
              :label="t('price')"
              v-model="editingData.price"
              type="money"
              prefix="R$"
              :placeholder="getValueRangePlaceholder('price')"
            />
          </div>

          <div>
            <IluriaInputText 
              id="stockQuantity"
              :label="t('stock')"
              v-model="editingData.stockQuantity"
              type="number" 
              :placeholder="getValueRangePlaceholder('stockQuantity')"
            />
          </div>
        </div>

        <div>
          <IluriaInputText
            id="originalPrice"
            :label="t('product.originalPrice')"
            v-model="editingData.originalPrice"
            type="money"
            prefix="R$"
            :placeholder="getValueRangePlaceholder('originalPrice')"
            inputClass="pl-8"
          />
        </div>

        <div>
          <IluriaInputText
            id="costPrice"
            :label="t('product.costPrice') + (modelValue && modelValue.isGroupEdit && modelValue.costPrice === undefined ? ' (' + t('product.differentValuesInGroup') + ')' : '')"
            v-model="editingData.costPrice"
            type="money"
            prefix="R$"
            :placeholder="getValueRangePlaceholder('costPrice')"
            inputClass="pl-8"
          />
        </div>

        <div>
          <IluriaInputText 
            id="weight"
            :label="t('product.weight')"
            v-model="editingData.weight"
            type="number" 
            :placeholder="getValueRangePlaceholder('weight')"
          />
        </div>

        <div class="grid grid-cols-3 gap-4">
          <div>
            <IluriaInputText 
              id="boxLength"
              :label="t('product.boxLength')"
              v-model="editingData.boxLength"
              type="number" 
              :placeholder="getValueRangePlaceholder('boxLength')"
            />
          </div>
          <div>
            <IluriaInputText 
              id="boxWidth"
              :label="t('product.boxWidth')"
              v-model="editingData.boxWidth"
              type="number" 
              :placeholder="getValueRangePlaceholder('boxWidth')"
            />
          </div>
          <div>
            <IluriaInputText 
              id="boxDepth"
              :label="t('product.boxDepth')"
              v-model="editingData.boxDepth"
              type="number" 
              :placeholder="getValueRangePlaceholder('boxDepth')"
            />
          </div>
        </div>
      </div>
    </Form>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { Form } from '@primevue/forms';
import IluriaModal from '@/components/iluria/IluriaModal.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';


const { t } = useI18n();

// Use defineModel for two-way binding
const modelValue = defineModel();

// Computed property to control dialog visibility
const visible = computed({
  get: () => !!modelValue.value,
  set: (value) => {
    if (!value) {
      modelValue.value = null;
    }
  }
});

const emit = defineEmits(['save']);

const editingData = ref(null);

watch(() => modelValue.value, (newValue) => {
  if (newValue) {
    editingData.value = JSON.parse(JSON.stringify(newValue));
  }
}, { immediate: true });

// Função para calcular o placeholder com base na faixa de valores
const getValueRangePlaceholder = (field) => {
  if (!modelValue.value || !modelValue.value.isGroupEdit || modelValue.value[field] !== undefined) {
    // Se não for edição em grupo ou se o valor não for undefined, retorna o placeholder padrão
    return field.includes('Price') ? '0.00' : '0';
  }

  // Se for edição em grupo e o valor for undefined, calcula a faixa de valores
  if (modelValue.value.groupItems && modelValue.value.groupItems.length > 0) {
    // Filtra os itens que têm valores definidos para o campo
    const validValues = modelValue.value.groupItems
      .filter(item => item[field] !== undefined && item[field] !== null)
      .map(item => parseFloat(item[field]));

    if (validValues.length > 0) {
      const min = Math.min(...validValues);
      const max = Math.max(...validValues);

      // Se min e max forem iguais, retorna apenas um valor
      if (min === max) {
        return field.includes('Price') ? min.toFixed(2) : min.toString();
      }

      // Caso contrário, retorna a faixa de valores
      return `${field.includes('Price') ? min.toFixed(2) : min} - ${field.includes('Price') ? max.toFixed(2) : max}`;
    }
  }

  // Se não houver valores válidos, retorna o placeholder padrão
  return '';
};

const save = () => {
  try {
    // Se a validação passar, emitir o evento de salvar
    emit('save', JSON.parse(JSON.stringify(editingData.value)));
  } catch (error) {
    
  }
};
</script>

<style scoped>
.price-input {
  position: relative;
}

.currency {
  position: absolute;
  left: 8px;
  top: 12px;
  z-index: 10;
  font-size: 14px;
  color: #666;
}
</style>
