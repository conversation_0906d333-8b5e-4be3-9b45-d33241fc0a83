
<template>
  <div class="product-editor-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="isEditing ? t('product.editProduct') : t('product.newProduct')"
      :subtitle="isEditing ? t('product.editProductSubtitleText') : t('product.newProductSubtitleText')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="isEditing ? t('update') : t('save')"
      @cancel-click="router.back()"
      @save-click="saveProduct"
    />

    <!-- Category Menu -->
    <div class="category-menu relative flex items-center justify-center" ref="categoryMenuRef">
      <!-- Slider animado que se move entre os botões -->
      <div 
        class="category-slider" 
        ref="categorySliderRef"
        :style="sliderStyle"
      ></div>
      
      <IluriaButton 
        ref="basicButtonRef"
        @click="activeCategory = 'basic'"
        :hugeIcon="DocumentValidationIcon"
        :color="'ghost'"
        :variant="'ghost'"
        size="medium"
        :title="t('product.basicDataTab')"
        class="category-menu-button"
        :class="{ 'active': activeCategory === 'basic' }"
      >
        <span class="category-label">{{ t('product.basicDataTab') }}</span>
      </IluriaButton>
      
      <!-- Nova aba de Atributos & Filtros - só aparece quando categoria está selecionada -->
      <Transition name="tab-slide" appear>
        <IluriaButton 
          ref="attributesButtonRef"
          v-if="form.categoryId && form.categoryName"
          @click="activeCategory = 'attributes'"
          :hugeIcon="FilterIcon"
          :color="'ghost'"
          :variant="'ghost'"
          size="medium"
          :title="t('product.attributesTab')"
          class="category-menu-button"
          :class="{ 'active': activeCategory === 'attributes' }"
        >
          <span class="category-label">{{ t('product.attributesTab') }}</span>
        </IluriaButton>
      </Transition>
      
      <Transition name="tab-slide" appear>
        <IluriaButton 
          ref="variationsButtonRef"
          v-if="showVariationsTab"
          @click="activeCategory = 'variations'"
          :hugeIcon="ArrowExpandIcon"
          :color="'ghost'"
          :variant="'ghost'"
          size="medium"
          :title="t('product.variationsTab')"
          class="category-menu-button"
          :class="{ 'active': activeCategory === 'variations' }"
        >
          <span class="category-label">{{ t('product.variationsTab') }}</span>
        </IluriaButton>
      </Transition>

      <Transition name="tab-slide" appear>
        <IluriaButton 
          ref="pricingButtonRef"
          v-if="showPricingTab"
          @click="activeCategory = 'pricing'"
          :hugeIcon="DollarSquareIcon"
          :color="'ghost'"
          :variant="'ghost'"
          size="medium"
          :title="t('product.pricingTab')"
          class="category-menu-button"
          :class="{ 'active': activeCategory === 'pricing' }"
        >
          <span class="category-label">{{ t('product.pricingTab') }}</span>
        </IluriaButton>
      </Transition>
      
      <Transition name="tab-slide" appear>
        <IluriaButton 
          ref="mediaButtonRef"
          v-if="showMediaTab"
          @click="activeCategory = 'media'"
          :hugeIcon="Image02Icon"
          :color="'ghost'"
          :variant="'ghost'"
          size="medium"
          :title="t('product.mediaTab')"
          class="category-menu-button"
          :class="{ 'active': activeCategory === 'media' }"
        >
          <span class="category-label">{{ t('product.mediaTab') }}</span>
        </IluriaButton>
      </Transition>
      
      <Transition name="tab-slide" appear>
        <IluriaButton 
          ref="shippingButtonRef"
          v-if="form.type !== 'DIGITAL'"
          @click="activeCategory = 'shipping'"
          :hugeIcon="TruckDeliveryIcon"
          :color="'ghost'"
          :variant="'ghost'"
          size="medium"
          :title="t('product.shippingTab')"
          class="category-menu-button"
          :class="{ 'active': activeCategory === 'shipping' }"
        >
          <span class="category-label">{{ t('product.shippingTab') }}</span>
        </IluriaButton>
      </Transition>
      
      <IluriaButton 
        ref="settingsButtonRef"
        @click="activeCategory = 'settings'"
        :hugeIcon="Settings02Icon"
        :color="'ghost'"
        :variant="'ghost'"
        size="medium"
        :title="t('product.settingsTab')"
        class="category-menu-button"
        :class="{ 'active': activeCategory === 'settings' }"
      >
        <span class="category-label">{{ t('product.settingsTab') }}</span>
      </IluriaButton>
    </div>

    <!-- Main Content -->
    <Form v-slot="$form" :resolver="resolver" @submit.prevent="saveProduct" 
      :validate-on-blur="true" 
      :validate-on-value-update="true"
      :validate-on-submit="true">
      
      <!-- Basic Data Category -->
      <div class="editor-content" v-show="activeCategory === 'basic'">
        <ViewContainer 
          :title="t('product.productData')"
          :icon="DocumentValidationIcon"
          iconColor="blue"
        >
          <ProductBasicData
            ref="productBasicDataRef"
            :formContext="$form"
          />
        </ViewContainer>

        <ViewContainer
          :title="t('product.identificationCodes')"
          :icon="BarCode01Icon"
          iconColor="gray"
          >
          <ProductIdentificationCodes
            ref="productIdentificationCodesRef"
            :formContext="$form" 
          />
        </ViewContainer>
        
        <!-- Grid container for Category and Product Settings (50/50) -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <ViewContainer 
            :title="t('category.title')"
            :icon="TagIcon"
            iconColor="orange"
          >
            <ProductCategories 
              v-model="form.categoryId" 
              :selectedIds="form.categoryIds"
              @update:categoryName="form.categoryName = $event"
              @update:categoryIds="form.categoryIds = $event"
            />
          </ViewContainer>
          
          <ViewContainer
            :title="t('product.settings')"
            :icon="Settings02Icon"
            iconColor="gray"
          >
            <ProductSettings
              ref="productSettingsRef"
              :type="form.type"
              :hasVariation="form.hasVariation"
              :status="form.status"
              :highlight="form.highlight"
              :newTag="form.newTag"
              @update:type="form.type = $event"
              @update:hasVariation="form.hasVariation = $event"
              @update:status="form.status = $event"
              @update:highlight="form.highlight = $event"
              @update:newTag="form.newTag = $event"
              :formContext="$form"
            />
          </ViewContainer>
        </div>

        <ViewContainer 
          v-if="!form.hasVariation"
          :title="t('product.seoConfiguration')"
          :subtitle="t('product.seoOptimizationSubtitle')"
          :icon="DocumentValidationIcon"
          iconColor="blue"
        >
          <IluriaSeoForm
            :form="form"
          />
        </ViewContainer>
      </div>

      <!-- Attributes Category -->
      <div class="editor-content" v-show="activeCategory === 'attributes'">
        <ViewContainer 
          :title="t('product.attributes.title')"
          :icon="FilterIcon"
          iconColor="purple"
        >
          <!-- Indicador da categoria selecionada -->
          <div class="category-indicator">
            <div class="category-indicator-content">
              <span class="category-indicator-label">Categoria selecionada:</span>
              <span class="category-indicator-name">{{ form.categoryName }}</span>
            </div>
          </div>
          
          <ProductAttributes v-model="form.attributes" />
        </ViewContainer>

        
      </div>

      <!-- Price & Stock Category -->
      <div class="editor-content" v-show="activeCategory === 'pricing'">
        <ViewContainer
          :title="t('product.priceConfiguration')"
          :subtitle="t('product.priceConfigurationSubtitle')"
          :icon="DollarSquareIcon"
          iconColor="green"
        >
          <ProductPricing
            ref="productPricingRef"
            :formContext="$form"
            @update:hasPriceRange="form.hasPriceRange = $event"
          />
        </ViewContainer>

        <ViewContainer 
          :title="t('product.stockControlTitle')"
          :subtitle="t('product.stockControlSubtitle')"
          :icon="Analytics02Icon"
          iconColor="blue"
        >
          <ProductStock
            ref="productStockRef"
            :formContext="$form"
          />
        </ViewContainer>
      </div>

      <!-- Media Category -->
      <div class="editor-content" v-show="activeCategory === 'media'">
        <ViewContainer 
          :title="t('product.images')"
          :icon="Image02Icon"
          iconColor="indigo"
        >
          <ProductPhotoUpload 
            v-model="form.photos" 
            :max-files="10"
            :product-id="productId || form.id"
            :is-editing="isEditing"
            @change="handlePhotoChange"
          />
        </ViewContainer>
      </div>

      <!-- Shipping Category -->
      <div class="editor-content" v-show="activeCategory === 'shipping'">
        <!-- Dimensões e Peso (primeira linha, sozinho) -->
        <ViewContainer
          :title="t('product.dimensionsAndWeight')"
          :subtitle="t('product.dimensionsAndWeightSubtitle')"
          :icon="RulerIcon"
          iconColor="purple"
        >
          <ProductDimensions
            ref="productDimensionsRef"
            :formContext="$form"
          />
        </ViewContainer>

        <!-- Frete e Embalar para presente (segunda linha, lado a lado) -->
        <div class="grid grid-cols-1 lg:grid-cols-2 gap-5 shipping-gift-grid">
          <ViewContainer 
            :title="t('product.shipping.title')"
            :icon="TruckDeliveryIcon"
            iconColor="orange"
          >

            <ProductShipping
                ref="productShippingRef"
                v-model:shippingFree="form.shippingFree"
                v-model:shippingFixed="form.shippingFixed"
                v-model:shippingFixedValue="form.shippingFixedValue"
                v-model:shippingFixedCombinedValue="form.shippingFixedCombinedValue"
                v-model:shippingFixedMaxUnities="form.shippingFixedMaxUnities"
                v-model:boxLengthValue="form.boxLengthValue"
                v-model:boxWidthValue="form.boxWidthValue"
                v-model:boxHeightValue="form.boxHeightValue"
                v-model:weightValue="form.weightValue"
                :hasVariation="form.hasVariation"
                :formContext="$form"
              />
         

          </ViewContainer>



          <ViewContainer 
            :title="t('product.giftPackaging')"
            :icon="GiftIcon"
            iconColor="pink"
          >
            <ProductGiftPackaging
              v-model:giftPackaging="form.giftPackaging"
              v-model:giftPackagingPrice="form.giftPackagingPrice"
              v-model:giftPackagingType="form.giftPackagingType"
              :shippingFree="form.shippingFree"
            />
          </ViewContainer>
        </div>
      </div>

      <!-- Settings Category -->
      <div class="editor-content" v-show="activeCategory === 'settings'">
        
        <ViewContainer 
          :title="t('product.quantityLimit')"
          :icon="LimitationIcon"
          iconColor="red"
        >
          <ProductQuantityLimit
            ref="productQuantityLimitRef"
            v-model:minQuantityLimit="form.minQuantityLimit"
            v-model:maxQuantityLimit="form.maxQuantityLimit"
            :formContext="$form"
          />
        </ViewContainer>



        <ViewContainer 
          :title="t('measurementTable.title')" 
          :subtitle="t('measurementTable.subtitle')"
          :icon="TableIcon"
          iconColor="purple"
        >
          <MeasurementTableSelector
            v-model="form.measurementTableId"
            :label="t('measurementTable.selectTable')"
            :product-images="productImages"
          />
        </ViewContainer>

        <ViewContainer 
          :title="t('product.customFields')" 
          :subtitle="t('product.customFieldsSubtitle')"
          :icon="Settings02Icon"
          iconColor="orange"
        >
          <ProductCustomizations 
            v-model="form.customizations"
            ref="productCustomizationsRef"
          />
        </ViewContainer>
        
        <ViewContainer 
          :title="t('product.supplierData')" 
          :subtitle="t('product.supplierDataSubtitle')"
          :icon="PackageIcon"
          iconColor="indigo"
        >
          <div class="supplier-section">
            <div class="form-field">
              <label for="supplierName" class="field-label">
                {{ t('product.supplierName') }}
              </label>
              <input
                type="text"
                id="supplierName"
                v-model="form.supplierName"
                class="field-input"
                :placeholder="t('product.supplierNamePlaceholder')"
              />
            </div>
            
            <div class="form-field">
              <label for="supplierLink" class="field-label">
                {{ t('product.supplierLink') }}
              </label>
              <input
                type="url"
                id="supplierLink"
                v-model="form.supplierLink"
                class="field-input"
                :placeholder="t('product.supplierLinkPlaceholder')"
              />
            </div>
            
            <div class="form-field">
              <label for="supplierNotes" class="field-label">
                {{ t('product.supplierNotes') }}
              </label>
              <textarea
                id="supplierNotes"
                v-model="form.supplierNotes"
                class="field-textarea"
                rows="3"
                :placeholder="t('product.supplierNotesPlaceholder')"
              ></textarea>
            </div>
          </div>
        </ViewContainer>
      </div>

      <!-- Variations Category -->
      <div class="editor-content" v-show="activeCategory === 'variations'">
        <!-- Variations (Full Width) -->
        <ViewContainer 
          :title="t('product.variations')"
          class="full-width-container"
          :icon="ArrowExpandIcon"
          iconColor="green"
        >
          <ProductVariations 
            v-model="form.variations" 
            v-model:productVariations="form.productVariations"
            v-model:boxLengthValue="form.boxLengthValue"
            v-model:boxWidthValue="form.boxWidthValue"
            v-model:boxHeightValue="form.boxHeightValue"
            v-model:weightValue="form.weightValue"
            :shippingFree="form.shippingFree"
            :shippingFixed="form.shippingFixed"
            :formContext="$form"
          />
        </ViewContainer>

        <ViewContainer 
          v-if="hasVariationAdded"
          :title="t('seoProductVariation.title')"
          :icon="DocumentValidationIcon"
          iconColor="blue"
        >
          <SeoProductVariation
            v-model="form.seoAttribute"
            v-model:seoVariations="form.seoVariations"
            :productVariations="form.productVariations"
            :productName="form.name"
            :productShortDescription="form.description"
            :productId="productId"
            :hasVariationStructureChanged="hasVariationStructureChanged"
          />
        </ViewContainer>
      </div>
    </Form>
  </div>
</template>

<script setup>
import { ref, onMounted, provide, watch, computed, nextTick, onUnmounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from '@/services/toast.service';
import ProductBasicData from '@/components/products/ProductBasicData.vue'
import ProductPricing from '@/components/products/ProductPricing.vue'
import ProductStock from '@/components/products/ProductStock.vue'
import ProductDimensions from '@/components/products/ProductDimensions.vue'
import ProductSettings from '@/components/products/ProductSettings.vue'
import ProductVariations from '@/components/productVariations/ProductVariations.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import ProductCategories from '@/components/products/ProductCategories.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import ProductGiftPackaging from '@/components/products/ProductGiftPackaging.vue'
import ProductQuantityLimit from '@/components/products/ProductQuantityLimit.vue'
import ProductPhotoUpload from '@/components/products/ProductPhotoUpload.vue'
import ProductShipping from '@/components/products/ProductShipping.vue'
import ProductCustomizations from '@/components/products/ProductCustomizations.vue'
import ProductAttributes from '@/components/products/ProductAttributes.vue'
import MeasurementTableSelector from '@/components/measurementTables/MeasurementTableSelector.vue'
import SeoProductVariation from '@/components/products/SeoProductVariation.vue'
import IluriaSeoForm from '@/components/iluria/form/IluriaSeoForm.vue'

import { attributesApi } from '@/services/attributes.service'
import { productsApi } from '@/services/product.service'
import { categoryService } from '@/services/category.service'
import { useI18n } from 'vue-i18n'
import { 
  FloppyDiskIcon,
  DocumentValidationIcon,
  DollarSquareIcon,
  Image02Icon,
  TruckDeliveryIcon,
  Settings02Icon,
  PackageIcon,
  TagIcon,
  FilterIcon,
  GiftIcon,
  LimitationIcon,
  TableIcon,
  Analytics02Icon,
  RulerIcon,
  ArrowExpandIcon,
  BarCode01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { Form } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { z } from 'zod'
import { useTheme } from '@/composables/useTheme'
import { 
  requiredText, 
  optionalNonNegativeNumber, 
  requiredNonNegativeNumber 
} from '@/services/validation.service'
import ProductIdentificationCodes from '../../components/products/ProductIdentificationCodes.vue';


const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const toast = useToast()

// Initialize theme system
const { initTheme } = useTheme()
initTheme()
const saving = ref(false)
const loading = ref(false)
const productBasicDataRef = ref(null)
const productIdentificationCodesRef = ref(null)
const productPricingRef = ref(null)
const productStockRef = ref(null)
const productDimensionsRef = ref(null)
const productSettingsRef = ref(null)
const productQuantityLimitRef = ref(null)
const originalVariationStructure = ref(null)
const hasVariationStructureChanged = ref(false)
const productCustomizationsRef = ref(null);

// Computed properties para garantir reatividade das abas
const showVariationsTab = computed(() => {
  const result = form.value.hasVariation === true;
  return result;
});

const showPricingTab = computed(() => {
  const result = form.value.hasVariation === false;
  return result;
});

const showMediaTab = computed(() => {
  const result = form.value.hasVariation === false; 
  return result;
});

// Category menu state
const activeCategory = ref('basic');

// Refs para o slider animado
const categoryMenuRef = ref(null);
const categorySliderRef = ref(null);
const basicButtonRef = ref(null);
const attributesButtonRef = ref(null);
const variationsButtonRef = ref(null);
const pricingButtonRef = ref(null);
const mediaButtonRef = ref(null);
const shippingButtonRef = ref(null);
const settingsButtonRef = ref(null);

// Estado do slider
const sliderStyle = ref({
  transform: 'translateX(0px)',
  width: '0px',
  opacity: 0
});

const isEditing = computed(() => !!route.params.id);
const productId = computed(() => route.params.id || form.value.id); 

const form = ref({
  name: '',
  description: '',
  sku: '',
  barCode: '',
  price: '',
  originalPrice: '',
  costPrice: '',
  profitMargin: '',
  stockQuantityTotal: '',
  weight: '',
  boxLength: '',
  boxWidth: '',
  boxDepth: '',
  categoryId: null,
  categoryName: null,
  categoryIds: [],
  giftPackaging: false,
  giftPackagingPrice: '',
  giftPackagingType: '',
  minQuantityLimit: 1,
  maxQuantityLimit: null,
  variations: [],
  productVariations: [],
  photos: [],
  highlight: false,
  newTag: false,
  hasVariation: false,
  type: 'PHYSICAL',
  status: 'ACTIVE',
  supplierName: '',
  supplierLink: '',
  supplierNotes: '',
  shippingFree: 'NO',
  shippingFixed: 'NO',
  shippingFixedValue: '',
  shippingFixedCombinedValue: '',
  shippingFixedMaxUnities: '',
  boxLengthValue: '',
  boxWidthValue: '',
  boxHeightValue: '',
  weightValue: '',
  customizations: [],
  hasCustomizations: false,
  relatedProducts: [],
  measurementTableId: null,
  metaTitle: '',
  metaDescription: '',
  // urlSlug: '',
  seoImage: '',
  seoAttribute: '',
  seoVariations: [],
  attributes: [],
  seo: {
    metaTitle: '',
    metaDescription: '',
    // slug: ''
  }
});

provide('productForm', form);
provide('isEditing', isEditing);
provide('hasVariationStructureChanged', hasVariationStructureChanged);

const productImageObjectUrls = ref([])
const productImages = computed(() => {
  // Limpa URLs antigas
  productImageObjectUrls.value.forEach(url => URL.revokeObjectURL(url))
  productImageObjectUrls.value = []
  const images = []
  // Produto sem variação
  if (!form.value.hasVariation && form.value.photos && form.value.photos.length > 0) {
    const firstPhoto = form.value.photos.find(photo => photo.position === 0) || form.value.photos[0]
    if (firstPhoto) {
      if (firstPhoto.url) {
        images.push(firstPhoto.url)
      } else if (firstPhoto instanceof File) {
        const objectUrl = URL.createObjectURL(firstPhoto)
        productImageObjectUrls.value.push(objectUrl)
        images.push(objectUrl)
      }
    }
  }
  // Produto com variação
  if (form.value.hasVariation && form.value.productVariations && form.value.productVariations.length > 0) {
    const variationWithPhotos = form.value.productVariations.find(variation => variation.photos && variation.photos.length > 0)
    if (variationWithPhotos) {
      const firstPhoto = variationWithPhotos.photos.find(photo => photo.position === 0) || variationWithPhotos.photos[0]
      if (firstPhoto) {
        if (firstPhoto.url) {
          images.push(firstPhoto.url)
        } else if (firstPhoto instanceof File) {
          const objectUrl = URL.createObjectURL(firstPhoto)
          productImageObjectUrls.value.push(objectUrl)
          images.push(objectUrl)
        }
      }
    }
  }
  return images
})

const hasVariationAdded = computed(() => {
  return form.value.variations.length > 0
});

const resolver = zodResolver(
  z.object({
    name: requiredText(t('product.name')),
    price: requiredNonNegativeNumber(t('product.price')).refine((price, ctx) => {
      if (form.value.hasVariation) {
        return true;
      }
      
      const costPrice = parseFloat(form.value.costPrice) || 0;
      
      if (price > 0 && costPrice > 0 && price <= costPrice) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t('product.negativeMarginError'),
        });
        return false;
      }
      
      return true;
    }),
    originalPrice: optionalNonNegativeNumber(t('product.originalPrice')),
    costPrice: optionalNonNegativeNumber(t('product.costPrice')).refine((costPrice, ctx) => {
      if (form.value.hasVariation) {
        return true;
      }
      
      const price = parseFloat(form.value.price) || 0;
      
      if (price > 0 && costPrice > 0 && price <= costPrice) {
        ctx.addIssue({
          code: z.ZodIssueCode.custom,
          message: t('product.negativeMarginError'),
        });
        return false;
      }
      
      return true;
    }),
    stockQuantityTotal: optionalNonNegativeNumber(t('product.stockQuantity')),
    weight: optionalNonNegativeNumber(t('product.weight'), { minValue: 0.01 }),
    boxLength: optionalNonNegativeNumber(t('product.boxLength'), { minValue: 0.01 }),
    boxWidth: optionalNonNegativeNumber(t('product.boxWidth'), { minValue: 0.01 }),
    boxDepth: optionalNonNegativeNumber(t('product.boxDepth'), { minValue: 0.01 })
  })
);

// Function to find category name by ID
async function findCategoryNameById(categoryId) {
  if (!categoryId) return null;
  
  try {
    const categories = await categoryService.fetchCategories();
    
    function searchInCategories(categoriesArray, id) {
      if (!categoriesArray || !Array.isArray(categoriesArray)) return null;
      
      for (const category of categoriesArray) {
        if (category.id && category.id.toString() === id.toString()) {
          return category.title || category.name || '';
        }
        
        if (category.children && Array.isArray(category.children)) {
          const found = searchInCategories(category.children, id);
          if (found) return found;
        }
      }
      
      return null;
    }
    
    return searchInCategories(categories, categoryId);
  } catch (error) {
    
    return null;
  }
}

async function loadProduct() {
  if (isEditing.value && productId.value) {
    try {
      loading.value = true;
      const product = await productsApi.getById(productId.value);
      
      await nextTick();
      
      const shippingState = determineShippingState(product)

      Object.assign(form.value, {
        ...product,
        ...shippingState,
        shippingFixedValue: (product.shippingFixedPrice !== null && product.shippingFixedPrice !== undefined) ? String(product.shippingFixedPrice) : '',
        shippingFixedCombinedValue: product.shippingFixedCombinedPrice ? String(product.shippingFixedCombinedPrice) : '',
        shippingFixedMaxUnities: product.shippingFixedMaxUnities ? String(product.shippingFixedMaxUnities) : '',
        boxLengthValue: product.boxLength ? String(product.boxLength) : '',
        boxWidthValue: product.boxWidth ? String(product.boxWidth) : '',
        boxHeightValue: product.boxDepth ? String(product.boxDepth) : '',
        weightValue: product.weight ? String(product.weight) : '',
        photos: product.photos ? product.photos.sort((a, b) => (a.position || 0) - (b.position || 0)) : [],
        customizations: product.customizations || [],
        hasCustomizations: !!(product.customizations && product.customizations.length > 0),
        hasPriceRange: product.hasPriceRange || false,
        priceRanges: product.priceRanges || [],
        attributes: [],
        measurementTableId: product.measurementTableId || null,  // Garantir que o measurementTableId seja preservado
        seo: {
          metaTitle: product.metaTitle || '',
          metaDescription: stripHtml(product.metaDescription) || '',
          slug: product.urlSlug || ''
        }
      })
      


;

      if (!form.value.categoryId && Array.isArray(form.value.categoryIds) && form.value.categoryIds.length > 0) {
        form.value.categoryId = form.value.categoryIds[form.value.categoryIds.length - 1];
      }
      
      if (form.value.categoryId && !form.value.categoryName) {
        const categoryName = await findCategoryNameById(form.value.categoryId);
        if (categoryName) {
          form.value.categoryName = categoryName;
        }
      }
      
      if (!form.value.hasOwnProperty('giftPackaging')) {
        form.value.giftPackaging = !!(form.value.giftPackagingPrice || form.value.giftPackagingType);
      }
      
      if (product.variations && product.variations.length > 0) {
        form.value.hasVariation = true;
        const processedVariations = processVariationsForForm(product.variations);
        form.value.productVariations = processedVariations.productVariations;
        form.value.variations = processedVariations.variations;

        // Aplicar informações de showInSearch do seoAttribute se existir
        if (product.seoAttribute) {
          try {
            const seoData = JSON.parse(product.seoAttribute);
            if (seoData.showInSearchConfig) {

              form.value.variations = form.value.variations.map(variation => ({
                ...variation,
                showInSearch: seoData.showInSearchConfig[variation.name] || false
              }));

              // Restaurar o seoAttribute original
              form.value.seoAttribute = seoData.originalSeoAttribute || '';
            }
          } catch (e) {
            // Se não conseguir fazer parse, é um seoAttribute normal
          }
        }

        if (isEditing.value) {
          hasAutoSelectedSeoAttribute.value = true;
        }
      }

      try {
        const prodAttrs = await attributesApi.getProductAttributes(product.id);
        if (Array.isArray(prodAttrs)) {
          const grouped = {};
          prodAttrs.forEach(item => {
            const isFilter = item.isFilter ?? item.filter ?? false;
            if (!grouped[item.attributeId]) {
              grouped[item.attributeId] = { attributeId: item.attributeId, name: item.attribute?.name || '', values: [], useAsFilter: isFilter };
            }
            if (item.attribute && item.attribute.name) grouped[item.attributeId].name = item.attribute.name;
            if (Array.isArray(item.attributeValues)) {
              item.attributeValues.forEach(v => {
                if (!grouped[item.attributeId].values.includes(v)) grouped[item.attributeId].values.push(v);
              });
            }
            if (isFilter) grouped[item.attributeId].useAsFilter = true;
          });
          form.value.attributes = Object.values(grouped);
        }
      } catch (attrErr) {
          
      }

      if (!form.value.hasVariation) {
        await nextTick();
        generateBasicProductSeo();
      }
    } catch (error) {
      toast.showError(t('product.errorLoadingProduct'));
      router.back();
    } finally {
      loading.value = false;
    }
  }
}

function processVariationsForForm(variations) {
  const attributeMap = new Map();
  variations.forEach(variation => {
    if (variation.attributes) {
      Object.entries(variation.attributes).forEach(([key, value]) => {
        const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1);
        if (!attributeMap.has(capitalizedKey)) {
          attributeMap.set(capitalizedKey, new Set());
        }
        attributeMap.get(capitalizedKey).add(value);
      });
    }
  });
  
  const variationsStructure = Array.from(attributeMap.entries()).map(([name, optionsSet]) => ({
    id: Date.now().toString() + Math.random(),
    name,
    options: Array.from(optionsSet),
    showInSearch: true
  }));
  
  if (isEditing.value && !originalVariationStructure.value) {
    originalVariationStructure.value = variationsStructure.map(v => ({
      name: v.name,
      options: [...v.options].sort()
    }));
  }
  
  const productVariations = variations.map(variation => {
    const options = {};
    if (variation.attributes) {
      Object.entries(variation.attributes).forEach(([key, value]) => {
        const capitalizedKey = key.charAt(0).toUpperCase() + key.slice(1);
        options[capitalizedKey] = value;
      });
    }
    
    return {
      id: variation.id,
      options,
      price: variation.price?.toString() || '',
      stock: variation.stockQuantity?.toString() || '',
      originalPrice: variation.originalPrice?.toString() || '',
      costPrice: variation.costPrice?.toString() || '',
      profitMargin: variation.profitMargin?.toString() || '',
      weight: variation.weight?.toString() || '',
      length: variation.boxLength?.toString() || '',
      width: variation.boxWidth?.toString() || '',
      depth: variation.boxDepth?.toString() || '',
      photos: variation.photos ? variation.photos
        .sort((a, b) => (a.position || 0) - (b.position || 0))
        .map(photo => ({ ...photo, isExisting: true })) : [],
      priceRanges: variation.priceRanges || [],
      hasPriceRanges: !!(variation.priceRanges && variation.priceRanges.length > 0)
    };
  });
  
  return { variations: variationsStructure, productVariations };
}

function generateProductVariationsFromStructure(variationsStructure) {
  if (!variationsStructure || variationsStructure.length === 0) {
    return [];
  }

  const validVariations = variationsStructure.filter(v => 
    v && v.name && v.options && Array.isArray(v.options) && v.options.length > 0
  );

  if (validVariations.length === 0) {
    return [];
  }

  function generateCombinations(variations) {
    if (variations.length === 0) return [{}];
    
    const [first, ...rest] = variations;
    const restCombinations = generateCombinations(rest);
    const combinations = [];
    
    for (const option of first.options) {
      for (const restCombination of restCombinations) {
        combinations.push({
          [first.name]: option,
          ...restCombination
        });
      }
    }
    
    return combinations;
  }

  const combinations = generateCombinations(validVariations);
  
  return combinations.map((options, index) => ({
    id: `temp-${Date.now()}-${index}`,
    options,
    price: '',
    stock: '',
    originalPrice: '',
    costPrice: '',
    weight: '',
    length: '',
    width: '',
    depth: '',
    photos: [],
    priceRanges: [],
    hasPriceRanges: false
  }));
}

let variationStructureTimeout = null;
watch(() => form.value.variations, () => {
  clearTimeout(variationStructureTimeout);
  variationStructureTimeout = setTimeout(() => {
    if (isEditing.value) {
        const currentStructure = form.value.variations.map(v => ({ name: v.name, options: [...v.options].sort() }));
        const originalStructureStr = JSON.stringify(originalVariationStructure.value?.sort((a, b) => a.name.localeCompare(b.name)));
        const currentStructureStr = JSON.stringify(currentStructure.sort((a, b) => a.name.localeCompare(b.name)));
        hasVariationStructureChanged.value = originalStructureStr !== currentStructureStr;
    }
  }, 300);
}, { deep: true });

// Debug watcher para hasVariation
watch(() => form.value.hasVariation, (newValue, oldValue) => {

}, { immediate: true });

// Watcher melhorado para hasVariation que respeita a escolha do usuário
watch(() => form.value.hasVariation, (newValue, oldValue) => {
  // Se o usuário mudou explicitamente de "Com variação" para "Simples"
  if (oldValue === true && newValue === false) {
    if (form.value.variations && form.value.variations.length > 0) {
      preservedVariations.value = JSON.parse(JSON.stringify(form.value.variations));
    }
    
    // Limpar variações quando usuário escolhe "Simples"
    form.value.variations = [];
    form.value.productVariations = [];
    form.value.photos = [];

    form.value.seoAttribute = '';
    form.value.seoVariations = [];
    
    hasAutoSelectedSeoAttribute.value = false;
    
    if (isEditing.value) {
      hasVariationStructureChanged.value = true;
    }
    
    // Switch to basic tab when disabling variations
    if (activeCategory.value === 'variations') {
      activeCategory.value = 'basic';
    }
    
    handleProductSeoToNull();
  }
  // Se o usuário mudou de "Simples" para "Com variação"
  else if (oldValue === false && newValue === true) {
    form.value.photos = [];

    form.value.seoAttribute = '';
    
    // Switch to variations tab when enabling variations
    if (activeCategory.value === 'pricing' || activeCategory.value === 'media') {
      activeCategory.value = 'variations';
    }
    handleProductSeoToNull();
    
    if (isEditing.value) {
      hasVariationStructureChanged.value = true;
    }
    
    hasAutoSelectedSeoAttribute.value = false;
    
    nextTick(() => {
      if (preservedVariations.value && preservedVariations.value.length > 0) {
        form.value.variations = JSON.parse(JSON.stringify(preservedVariations.value));
        
        const generatedProductVariations = generateProductVariationsFromStructure(form.value.variations);
        if (generatedProductVariations.length > 0) {
          form.value.productVariations = generatedProductVariations;
        }
        
        const firstVariation = form.value.variations[0];
        if (firstVariation && firstVariation.name) {
          form.value.seoAttribute = firstVariation.name;
          hasAutoSelectedSeoAttribute.value = true;
        }
        
        preservedVariations.value = null;
      }
      else if (form.value.variations && form.value.variations.length > 0) {
        const generatedProductVariations = generateProductVariationsFromStructure(form.value.variations);
        if (generatedProductVariations.length > 0) {
          form.value.productVariations = generatedProductVariations;
        }
        
        const firstVariation = form.value.variations[0];
        if (firstVariation && firstVariation.name) {
          form.value.seoAttribute = firstVariation.name;
          hasAutoSelectedSeoAttribute.value = true;
        }
      }
    });
  }
  // Qualquer mudança durante edição
  else if (isEditing.value && oldValue !== newValue) {
    hasVariationStructureChanged.value = true;
  }

  // Atualizar SEO básico quando necessário
  if (!newValue) {
    nextTick(() => {
      generateBasicProductSeo();
    });
  }
});

// Watch for category changes to redirect when needed
watch(() => activeCategory.value, (newCategory) => {
  if (showVariationsTab.value) {
    if (newCategory === 'pricing' || newCategory === 'media') {
      activeCategory.value = 'variations';
    }
  }
});

// Watch for category selection/deselection to handle attributes tab
watch(() => form.value.categoryId, (newCategoryId, oldCategoryId) => {
  // Se a categoria foi desmarcada e o usuário estava na aba de atributos
  if (!newCategoryId && oldCategoryId && activeCategory.value === 'attributes') {
    activeCategory.value = 'basic';
  }
});

const hasAutoSelectedSeoAttribute = ref(false);
const preservedVariations = ref(null);

watch(() => form.value.variations, (newVariations, oldVariations) => {
  if (loading.value) {
    return;
  }
  
  const oldLength = oldVariations?.length || 0;
  const newLength = newVariations?.length || 0;
  
  if (form.value.hasVariation && newLength > 0) {
    const hasNoProductVariations = form.value.productVariations.length === 0;
    const structureChanged = (oldLength !== newLength) || 
      JSON.stringify(newVariations) !== JSON.stringify(oldVariations);
    
    const hasValidProductVariations = form.value.productVariations.length > 0 && 
      form.value.productVariations.every(pv => pv.options && Object.keys(pv.options).length > 0);
    
    const shouldRecreateProductVariations = hasNoProductVariations || 
      (structureChanged && !hasValidProductVariations);
    
    if (shouldRecreateProductVariations) {
      const generatedProductVariations = generateProductVariationsFromStructure(newVariations);
      if (generatedProductVariations.length > 0) {
        form.value.productVariations = generatedProductVariations;
      }
    }
  
    const shouldAutoSelect = (
      (oldLength === 0 && newLength === 1 && !hasAutoSelectedSeoAttribute.value) ||
      (!form.value.seoAttribute || form.value.seoAttribute.trim() === '')
    );
    
    if (shouldAutoSelect) {
      const firstVariation = newVariations[0];
      if (firstVariation && firstVariation.name) {
        nextTick(() => {
          form.value.seoAttribute = firstVariation.name;
          hasAutoSelectedSeoAttribute.value = true;
        });
      }
    }
  }
  
  if (newLength === 0) {
    hasAutoSelectedSeoAttribute.value = false;
    form.value.seoAttribute = '';
    form.value.productVariations = [];
  }
}, { deep: true });

// Watch para atualizar posição do slider
watch(() => activeCategory.value, () => {
  updateSliderPosition();
});

// Watch para mudanças que afetam quais botões estão visíveis
watch(() => form.value.hasVariation, () => {
  nextTick(() => updateSliderPosition());
});

watch(() => form.value.categoryId, () => {
  nextTick(() => updateSliderPosition());
});

watch(() => form.value.type, () => {
  nextTick(() => updateSliderPosition());
});

// Listener para redimensionamento
const handleResize = () => {
  updateSliderPosition();
};

onMounted(() => {
  loadProduct();
  nextTick(() => {
    updateSliderPosition();
    // Force refresh elements to apply theme
    forceRefreshElements();
  });
  window.addEventListener('resize', handleResize);
});

// Force refresh elements for theme application
function forceRefreshElements() {
  // Force refresh of navigation elements
  const navElements = document.querySelectorAll('.category-menu, .category-menu-button, .category-slider');
  navElements.forEach(element => {
    element.style.display = 'none';
    requestAnimationFrame(() => {
      element.style.display = '';
    });
  });
}

onUnmounted(() => {
  clearTimeout(variationStructureTimeout);
  productImageObjectUrls.value.forEach(url => URL.revokeObjectURL(url))
  productImageObjectUrls.value = []
  window.removeEventListener('resize', handleResize);
  
  preservedVariations.value = null;
});


function generateSlug(text, productId) {
  if (!text) return '';
  
  let slug = text
    .toLowerCase()
    .trim()
    .normalize('NFD')
    .replace(/[\u0300-\u036f]/g, '')
    .replace(/[^a-z0-9\s-]/g, '')
    .replace(/\s+/g, '-')
    .replace(/-+/g, '-')
    .replace(/^-+$/g, '');
  
  return slug;
}

function stripHtml(html) {
  if (!html) return "";
  const temporalDivElement = document.createElement("div");
  temporalDivElement.innerHTML = html;
  let text = temporalDivElement.textContent || temporalDivElement.innerText || "";
  return text.replace(/\s+/g, ' ').trim();
}

function validateSeoVariations() {
  if (form.value.hasVariation && form.value.seoAttribute && form.value.seoAttribute.trim() !== '') {
    if (!form.value.seoVariations || form.value.seoVariations.length === 0) {
      toast.showError(t('seoProductVariation.validation.allFieldsRequired'));
      return false;
    }
    
    const invalidVariations = form.value.seoVariations.filter(variation => {
      return !variation.metaTitle || 
             variation.metaTitle.trim() === '' ||
             !variation.metaDescription || 
             variation.metaDescription.trim() === '' ||
             !variation.urlSlug || 
             variation.urlSlug.trim() === '';
    });
    
    if (invalidVariations.length > 0) {
      const invalidValues = invalidVariations.map(v => v.attributeValue).join(', ');
      toast.showError(t('seoProductVariation.validation.allFieldsRequired') + `. Variações incompletas: ${invalidValues}`);
      return false;
    }
  }
  
  return true;
}

function generateBasicProductSeo() {
  if (form.value.hasVariation) {
    return;
  }

  if (form.value.name && form.value.name.trim()) {
    form.value.metaTitle = form.value.name.trim();
    form.value.seo.metaTitle = form.value.name.trim();
  }

  if (form.value.description && form.value.description.trim()) {
    const cleanDescription = stripHtml(form.value.description);
    form.value.metaDescription = cleanDescription;
    form.value.seo.metaDescription = cleanDescription;
  }

  if (form.value.name && form.value.name.trim()) {
    const currentProductId = productId.value || form.value.id;
    const generatedSlug = generateSlug(form.value.name.trim(), currentProductId);
    form.value.urlSlug = generatedSlug;
    form.value.seo.slug = generatedSlug;
  }

  updateBasicProductSeoImage();
}

function regenerateSlugWithId(savedProductId) {
  if (!form.value.hasVariation && form.value.name && form.value.name.trim()) {
    const currentSlug = form.value.urlSlug || '';

    const hasIdInSlug = /\/[^\/]+-[a-zA-Z0-9]+$/.test(currentSlug);
    
    if (!hasIdInSlug && savedProductId) {
      const newSlug = generateSlug(form.value.name.trim(), savedProductId);
      form.value.urlSlug = newSlug;
      form.value.seo.slug = newSlug;
    }
  }
}

function updateBasicProductSeoImage() {
  if (form.value.hasVariation) {
    return;
  }

  let seoImage = '';
  
  if (form.value.photos && form.value.photos.length > 0) {
    const primaryPhoto = form.value.photos.find(photo => photo.position === 0);
    let photoToUse = primaryPhoto;
    
    if (!photoToUse) {
      photoToUse = form.value.photos[0];
    }
    
    if (photoToUse) {
      if (photoToUse instanceof File) {
        seoImage = URL.createObjectURL(photoToUse);
      } else if (photoToUse.url) {
        seoImage = photoToUse.url;
      }
    }
  }

  form.value.seoImage = seoImage;
}

const saveProduct = async () => {
  try {
    saving.value = true

    if (!form.value.hasVariation) {
      if (productPricingRef.value?.hasNegativeMargin?.value) {
        toast.showError(t('product.negativeMarginError'));
        saving.value = false;
        return;
      }
      
      const price = parseFloat(form.value.price) || 0;
      const costPrice = parseFloat(form.value.costPrice) || 0;
      
      if (price > 0 && costPrice > 0 && price <= costPrice) {
        toast.showError(t('product.negativeMarginError'));
        saving.value = false;
        return;
      }
    }

    if (!validateSeoVariations()) {
      saving.value = false
      return
    }
    
    if (form.value.hasVariation && form.value.productVariations) {
      const invalidVariations = []
      
      form.value.productVariations.forEach((variation, index) => {
        const price = parseFloat(variation.price) || 0
        const costPrice = parseFloat(variation.costPrice) || 0
        
        if (price > 0 && costPrice > 0 && price <= costPrice) {
          const variantName = Object.values(variation.options || {}).join(' / ')
          invalidVariations.push(variantName || `Variação ${index + 1}`)
        }
      })
      
      if (invalidVariations.length > 0) {
        const message = t('product.negativeMarginError') + '. Variações: ' + invalidVariations.join(', ')
        toast.showError(message)
        saving.value = false
        return
      }
    }
    
    const productData = { ...form.value }
    
    prepareGiftPackaging(productData)
    configureShipping(productData)
    await processCustomizations(productData)
    
    const variationsToSend = processVariations(productData)
    const dtoData = buildProductDTO(productData, variationsToSend)
    
    const response = await saveProductData(dtoData)
    
    // Save attributes after product is saved
    const savedProductId = response.data?.id || productId.value
    if (savedProductId) {
      const attrPayload = (form.value.attributes || [])
        .filter(a => a.attributeId && a.values && a.values.length)
        .map(attr => ({
          attributeId: attr.attributeId,
          attributeValues: attr.values,
          filter: !!attr.useAsFilter
        }));
      await attributesApi.saveProductAttributes(savedProductId, attrPayload);
    }
    
    await handlePostSave(response, productData)
    
  } catch (error) {
    handleSaveError(error)
  } finally {
    saving.value = false
  }
}

function prepareGiftPackaging(productData) {
  if (!productData.giftPackaging) {
    productData.giftPackagingPrice = null
    productData.giftPackagingType = null
  }
}

function configureShipping(productData) {
  if (productData.shippingFree === 'YES') {
    setFreeShipping(productData)
  } else if (productData.shippingFixed === 'YES') {
    setFixedShipping(productData)
  } else {
    setCalculatedShipping(productData)
  }
}

function setFreeShipping(productData) {
  productData.shippingFixed = 'NO'
  productData.shippingFixedPrice = 0.00
  productData.shippingFixedCombinedPrice = null
  productData.shippingFixedMaxUnities = null

  // Limpar campos de formulário de frete
  productData.shippingFixedValue = '0'
  productData.shippingFixedCombinedValue = ''
  productData.shippingFixedMaxUnities = ''

  // Não limpar dimensões - elas devem ser sempre preservadas independentemente do tipo de frete
}

function setFixedShipping(productData) {
  productData.shippingFixedPrice = productData.shippingFixedValue || null
  productData.shippingFixedCombinedPrice = productData.shippingFixedCombinedValue || null
  productData.shippingFixedMaxUnities = productData.shippingFixedMaxUnities || null
  // Não limpar dimensões quando frete é fixo - elas podem ser necessárias para outros cálculos
}

function setCalculatedShipping(productData) {
  productData.shippingFixedPrice = null
  productData.shippingFixedCombinedPrice = null
  productData.shippingFixedMaxUnities = null

  // Limpar campos de formulário de frete
  productData.shippingFixedValue = ''
  productData.shippingFixedCombinedValue = ''
  productData.shippingFixedMaxUnities = ''

  if (productData.hasVariation) {
    clearDimensions(productData)
  }
}

function clearDimensions(productData) {
  // Limpar campos de dimensões do formulário
  productData.boxLengthValue = ''
  productData.boxWidthValue = ''
  productData.boxHeightValue = ''
  productData.weightValue = ''

  // Limpar campos de dimensões do DTO
  productData.boxLength = null
  productData.boxWidth = null
  productData.boxDepth = null
  productData.weight = null
}

async function processCustomizations(productData) {
  if (!shouldProcessCustomizations(productData)) return
  
  productData.customizations = productData.customizations.map(transformCustomization)
}

function shouldProcessCustomizations(productData) {
  return productData.hasCustomizations && 
         productData.customizations && 
         productData.customizations.length > 0
}

function transformCustomization(customization) {
  const transformed = createBaseCustomization(customization)
  
  setCustomizationPrice(customization, transformed)
  setCustomizationCharacterLimit(customization, transformed)
  setCustomizationOptions(customization, transformed)
  
  return transformed
}

function createBaseCustomization(customization) {
  return {
    type: customization.type,
    title: customization.title,
    description: customization.description || '',
    required: customization.required || false,
    options: []
  }
}

function setCustomizationPrice(customization, transformed) {
  if (customization.type === 'MULTIPLE_CHOICE') return
  
  const hasValidPrice = hasValidAdditionalPrice(customization.additionalPrice)
  if (hasValidPrice) {
    transformed.additionalPrice = parseFloatSafe(customization.additionalPrice)
  }
}

function setCustomizationCharacterLimit(customization, transformed) {
  if (customization.type !== 'TEXT') return
  
  if (hasValidValue(customization.characterLimit)) {
    transformed.characterLimit = parseInt(customization.characterLimit)
  }
}

function setCustomizationOptions(customization, transformed) {
  if (!customization.options?.length) return
  
  transformed.options = customization.options.map(transformOption)
}

function transformOption(option, optIndex) {
  const transformed = createBaseOption(option, optIndex)
  
  setOptionPrice(option, transformed)
  setNestedCustomizations(option, transformed)
  
  return transformed
}

function createBaseOption(option, optIndex) {
  return {
    label: option.label || `Opção ${optIndex + 1}`,
    additionalPrice: 0.00,
    position: optIndex + 1,
    nestedCustomizations: []
  }
}

function setOptionPrice(option, transformed) {
  if (hasValidValue(option.additionalPrice)) {
    transformed.additionalPrice = parseFloatSafe(option.additionalPrice)
  }
}

function setNestedCustomizations(option, transformed) {
  if (!option.nestedCustomizations?.length) return
  
  transformed.nestedCustomizations = option.nestedCustomizations.map(transformNestedCustomization)
}

function transformNestedCustomization(nested, nestedIndex) {
  const transformed = createBaseNestedCustomization(nested, nestedIndex)
  
  setNestedCustomizationPrice(nested, transformed)
  setNestedCharacterLimit(nested, transformed)
  setNestedOptions(nested, transformed)
  
  return transformed
}

function createBaseNestedCustomization(nested, nestedIndex) {
  return {
    type: nested.type,
    title: nested.title,
    description: nested.description || '',
    required: nested.required || false,
    position: nestedIndex + 1,
    options: []
  }
}

function setNestedCustomizationPrice(nested, transformed) {
  if (nested.type === 'MULTIPLE_CHOICE') return
  
  const hasValidPrice = hasValidAdditionalPrice(nested.additionalPrice)
  if (hasValidPrice) {
    transformed.additionalPrice = parseFloatSafe(nested.additionalPrice)
  }
}

function setNestedCharacterLimit(nested, transformed) {
  if (nested.type !== 'TEXT') return
  
  if (hasValidValue(nested.characterLimit)) {
    transformed.characterLimit = parseInt(nested.characterLimit)
  }
}

function setNestedOptions(nested, transformed) {
  if (nested.type !== 'MULTIPLE_CHOICE' || !nested.options?.length) return
  
  transformed.options = nested.options.map(transformNestedOption)
}

function transformNestedOption(nestedOption, nestedOptIndex) {
  const transformed = {
    label: nestedOption.label || `Opção ${nestedOptIndex + 1}`,
    additionalPrice: 0.00,
    position: nestedOptIndex + 1
  }
  
  if (hasValidValue(nestedOption.additionalPrice)) {
    transformed.additionalPrice = parseFloatSafe(nestedOption.additionalPrice)
  }
  
  return transformed
}

function hasValidAdditionalPrice(price) {
  return price !== null && 
         price !== undefined && 
         price !== '' && 
         price !== 0
}

function hasValidValue(value) {
  return value !== null && 
         value !== undefined && 
         value !== ''
}

function parseFloatSafe(value) {
  return typeof value === 'number' ? value : parseFloat(value)
}

function processVariations(productData) {
  if (!hasValidVariations(productData)) {
    return []
  }
  
  const variationsToSend = productData.productVariations
    .filter(hasValidOptions)
    .map(mapVariation)
  
  if (variationsToSend.length === 0) {
    productData.hasVariation = false
  }
  
  return variationsToSend
}

function hasValidVariations(productData) {
  return !!(productData.hasVariation && productData.productVariations?.length)
}

function hasValidOptions(variation) {
  return variation.options && Object.keys(variation.options).length > 0
}

function mapVariation(variation) {
  const mappedVariation = {
    attributes: buildAttributes(variation.options),
    ...buildVariationPricing(variation),
    ...buildVariationDimensions(variation)
  }
  
  addVariationPhotos(variation, mappedVariation)
  addVariationId(variation, mappedVariation)
  
  return mappedVariation
}

function buildAttributes(options) {
  const attributes = {}
  if (options) {
    Object.keys(options).forEach(key => {
      attributes[key.toLowerCase()] = options[key]
    })
  }
  return attributes
}

function buildVariationPricing(variation) {
  return {
    price: parseFloatSafe(variation.price) || 0,
    originalPrice: parseFloatSafe(variation.originalPrice),
    costPrice: parseFloatSafe(variation.costPrice),
    profitMargin: parseFloatSafe(variation.profitMargin),
    stockQuantity: (variation.stock && variation.stock !== '') ? parseInt(variation.stock) : 0
  }
}

function buildVariationDimensions(variation) {
  return {
    weight: parseFloatSafe(variation.weight),
    boxDepth: parseFloatSafe(variation.depth),
    boxLength: parseFloatSafe(variation.length),
    boxWidth: parseFloatSafe(variation.width)
  }
}

function addVariationPhotos(variation, mappedVariation) {
  if (variation.photos?.length > 0) {
    mappedVariation.photos = variation.photos
      .filter(photo => !(photo instanceof File))
      .map((photo, index) => ({ ...photo, position: index }))
  }
}

function addVariationId(variation, mappedVariation) {
  if (isValidExistingVariation(variation)) {
    mappedVariation.id = variation.id
  }
}

function isValidExistingVariation(variation) {
  return isEditing.value && 
         variation.id && 
         typeof variation.id === 'string' && 
         variation.id.match(/^[0-9A-Z]{26}$/)
}

function calculateStockTotal(productData, variationsToSend) {
  if (productData.hasVariation && variationsToSend.length > 0) {
    return variationsToSend.reduce((total, variation) => {
      return total + (variation.stockQuantity || 0)
    }, 0)
  }
  return 0
}

function buildProductDTO(productData, variationsToSend) {
  const stockQuantityTotal = calculateStockTotal(productData, variationsToSend)
  
  const dtoData = buildBaseDTOData(productData, variationsToSend)
  
  // Enviar apenas o caminho completo das categorias (array de IDs)
  if (Array.isArray(productData.categoryIds) && productData.categoryIds.length > 0) {
    dtoData.categoryIds = productData.categoryIds;
  }
  
  // Garantir que o campo legado "categoryId" não seja enviado ao backend
  if (dtoData.hasOwnProperty('categoryId')) {
    delete dtoData.categoryId;
  }
  
  if (shouldUseSimpleProductData(productData, variationsToSend)) {
    addSimpleProductData(productData, dtoData)
  } else {
    dtoData.stockQuantityTotal = stockQuantityTotal
  }
  
  return dtoData
}

function buildBaseDTOData(productData, variationsToSend) {
  return {
    ...buildBasicProductInfo(productData),
    ...buildProductFlags(productData),
    ...buildGiftPackagingInfo(productData),
    ...buildQuantityLimits(productData),
    ...buildSupplierInfo(productData),
    ...buildVariationInfo(productData, variationsToSend),
    ...buildCustomizationInfo(productData),
    ...buildRelatedProductsInfo(productData),
    ...buildShippingInfo(productData),
    ...buildDimensionsInfo(productData)
  }
}

function buildBasicProductInfo(productData) {
  // Preparar seoAttribute com configurações de showInSearch
  let seoAttribute = productData.seoAttribute || ''

  // Se há variações, salvar configurações de showInSearch no seoAttribute
  if (productData.hasVariation && Array.isArray(productData.variations) && productData.variations.length > 0) {
    const showInSearchConfig = {}
    productData.variations.forEach(variation => {
      showInSearchConfig[variation.name] = variation.showInSearch || false
    })

    // Criar um objeto com o seoAttribute original e as configurações
    const seoData = {
      originalSeoAttribute: seoAttribute,
      showInSearchConfig: showInSearchConfig
    }

    seoAttribute = JSON.stringify(seoData)
  }

  return {
    name: productData.name || '',
    sku: productData.sku || null,
    barCode: productData.barCode || null,
    description: productData.description || null,
    shortDescription: productData.shortDescription || '',
    status: productData.status || 'DRAFT',
    type: productData.type || 'PHYSICAL',
    metaTitle: productData.metaTitle || '',
    metaDescription: productData.metaDescription || '',
    urlSlug: productData.urlSlug || '',
    seoImage: productData.seoImage || '',
    seoAttribute: seoAttribute,
    seoVariations: productData.seoVariations || []
  }
}

function buildProductFlags(productData) {
  return {
    highlight: !!productData.highlight,
    newTag: !!productData.newTag,
    giftPackaging: !!productData.giftPackaging
  }
}

function buildGiftPackagingInfo(productData) {
  return {
    giftPackagingPrice: parseFloatOrNull(productData.giftPackagingPrice),
    giftPackagingType: productData.giftPackagingType || null
  }
}

function buildQuantityLimits(productData) {
  return {
    minQuantityLimit: parseIntOrDefault(productData.minQuantityLimit, 1),
    maxQuantityLimit: parseIntOrNull(productData.maxQuantityLimit)
  }
}

function buildShippingInfo(productData) {
  // Determinar o valor correto do shippingFixedPrice baseado no estado
  let shippingFixedPrice = null

  if (productData.shippingFree === 'YES') {
    shippingFixedPrice = 0.00  // Frete grátis = 0
  } else if (productData.shippingFixed === 'YES') {
    shippingFixedPrice = parseFloatOrNull(productData.shippingFixedValue)
  }

  const shippingInfo = {
    shippingFixedPrice: shippingFixedPrice,
    shippingFixedCombinedPrice: parseFloatOrNull(productData.shippingFixedCombinedValue),
    shippingFixedMaxUnities: parseIntOrNull(productData.shippingFixedMaxUnities)
  }


  return shippingInfo
}

function buildDimensionsInfo(productData) {
  return {
    weight: parseFloatOrNull(productData.weightValue || productData.weight),
    boxDepth: parseFloatOrNull(productData.boxHeightValue || productData.boxDepth),
    boxLength: parseFloatOrNull(productData.boxLengthValue || productData.boxLength),
    boxWidth: parseFloatOrNull(productData.boxWidthValue || productData.boxWidth)
  }
}

function determineShippingState(product) {
  // Verificar se é exatamente 0 (frete grátis)
  if (product.shippingFixedPrice === 0 || product.shippingFixedPrice === "0" || product.shippingFixedPrice === 0.0) {
    return {
      shippingFree: 'YES',
      shippingFixed: 'NO'
    }
  }

  // Verificar se é maior que 0 (frete fixo)
  if (product.shippingFixedPrice && Number(product.shippingFixedPrice) > 0) {
    return {
      shippingFree: 'NO',
      shippingFixed: 'YES'
    }
  }

  // Caso contrário (null ou undefined), é frete calculado
  return {
    shippingFree: 'NO',
    shippingFixed: 'NO'
  }
}

function buildSupplierInfo(productData) {
  return {
    supplierName: productData.supplierName || null,
    supplierLink: productData.supplierLink || null,
    supplierNotes: productData.supplierNotes || null
  }
}

function buildVariationInfo(productData, variationsToSend) {
  return {
    hasVariation: !!productData.hasVariation,
    variations: Array.isArray(variationsToSend) ? variationsToSend : []
  }
}

function buildCustomizationInfo(productData) {
  return {
    hasCustomizations: !!(productData.customizations && productData.customizations.length > 0),
    customizations: productData.customizations || []
  }
}

function buildRelatedProductsInfo(productData) {
  return {
    selectedProductIds: productData.selectedProductIds || null,
    selectedCategoryIds: productData.selectedCategoryIds || null,
    selectedCollectionIds: productData.selectedCollectionIds || null,
    sortOrder: productData.sortOrder || null,
    manualModeEnabled: productData.manualModeEnabled || false,
    categoryModeEnabled: productData.categoryModeEnabled || false,
    collectionModeEnabled: productData.collectionModeEnabled || false
  }
}

function shouldUseSimpleProductData(productData, variationsToSend) {
  return !productData.hasVariation || variationsToSend.length === 0
}

function addSimpleProductData(productData, dtoData) {
  addSimpleProductPrices(productData, dtoData)
  addSimpleProductDimensions(productData, dtoData)
  addSimpleProductPhotos(productData, dtoData)
}

function addSimpleProductPrices(productData, dtoData) {
  dtoData.price = parseFloatOrDefault(productData.price, 0)
  dtoData.originalPrice = parseFloatOrNull(productData.originalPrice)
  dtoData.costPrice = parseFloatOrNull(productData.costPrice)
  dtoData.profitMargin = parseFloatOrNull(productData.profitMargin)
  dtoData.stockQuantityTotal = parseIntOrDefault(productData.stockQuantityTotal, 0)
}

function addSimpleProductDimensions(productData, dtoData) {
  dtoData.weight = parseFloatOrNull(productData.weight)
  dtoData.boxDepth = parseFloatOrNull(productData.boxDepth)
  dtoData.boxLength = parseFloatOrNull(productData.boxLength)
  dtoData.boxWidth = parseFloatOrNull(productData.boxWidth)
}

function addSimpleProductPhotos(productData, dtoData) {
  if (productData.photos && productData.photos.length > 0) {
    dtoData.photos = productData.photos
      .filter(photo => !(photo instanceof File))
      .map((photo, index) => ({
        ...photo,
        position: index
      }))
  }
}

function parseFloatOrNull(value) {
  return value && value !== '' ? parseFloat(value) : null
}

function parseFloatOrNullAllowZero(value) {
  return (value !== null && value !== undefined && value !== '') ? parseFloat(value) : null
}

function parseFloatOrDefault(value, defaultValue) {
  return value && value !== '' ? parseFloat(value) : defaultValue
}

function parseIntOrNull(value) {
  return value && value !== '' ? parseInt(value) : null
}

function parseIntOrDefault(value, defaultValue) {
  return value && value !== '' ? parseInt(value) : defaultValue
}

async function saveProductData(dtoData) {
  if (isEditing.value) {
    const response = await productsApi.update(productId.value, dtoData)
    toast.showSuccess(t('product.productUpdated'))
    return response
  } else {
    const response = await productsApi.create(dtoData)
    if (response.data && response.data.id) {
      form.value.id = response.data.id
    }
    toast.showSuccess(t('product.productCreated'))
    return response
  }
}

async function handlePostSave(response, productData) {
  const savedProductId = response.data?.id || productId.value
  
  if (response.data?.id && !isEditing.value) {
    regenerateSlugWithId(savedProductId);
    
    try {
      const updatedData = {
        urlSlug: form.value.urlSlug
      };
      await productsApi.update(savedProductId, updatedData);
    } catch (error) {
      console.warn('Erro ao atualizar slug do produto:', error);
    }
  }
  
  const hasImagesToUpload = checkForImagesToUpload(productData)
  
  if (hasImagesToUpload && savedProductId) {
    uploadImagesInBackground(savedProductId, productData)
  }
  
  await router.push('/products')
}

function handleSaveError(error) {
  console.error('Erro ao salvar produto:', error)
  
  const errorMessage = isEditing.value ? t('product.errorUpdatingProduct') : t('product.anErrorOccurred')
  toast.showError(errorMessage)
}

async function uploadImages(files) {
  let uploadedCount = 0
  let errorCount = 0
  
  for (const file of files) {
    try {
      await productsApi.uploadImage(productId.value, file)
      uploadedCount++
    } catch (error) {
      console.error('Error uploading image:', error)
      errorCount++
    }
  }
  
  if (errorCount > 0) {
    const errorMessage = isEditing.value ? t('product.errorUpdatingProduct') : t('product.anErrorOccurred')
    toast.showError(errorMessage)
  }
}

watch(() => form.value.type, (newType) => {
  if (newType === 'GIFT') {
    form.value.hasVariation = false;
  }
  
  // Se mudar para DIGITAL e estiver na aba de shipping, muda para basic
  if (newType === 'DIGITAL' && activeCategory.value === 'shipping') {
    activeCategory.value = 'basic';
  }
});

// ... existing code ...

function handlePhotoChange(photos) {
  form.value.photos = photos;
}

watch(() => form.value.name, (newName) => {
  if (!form.value.hasVariation && newName && newName.trim()) {
    form.value.metaTitle = newName.trim();
    const currentProductId = productId.value || form.value.id;
    const generatedSlug = generateSlug(newName.trim(), currentProductId);
    form.value.urlSlug = generatedSlug;
    
    form.value.seo.metaTitle = newName.trim();
    form.value.seo.slug = generatedSlug;
  }
});

watch(() => form.value.description, (newDescription) => {
  if (!form.value.hasVariation && newDescription && newDescription.trim()) {
    const cleanDescription = stripHtml(newDescription);
    form.value.metaDescription = cleanDescription;
    form.value.seo.metaDescription = cleanDescription;
  }
});

watch(() => form.value.photos, () => {
  if (!form.value.hasVariation) {
    updateBasicProductSeoImage();
  }
}, { deep: true });

watch(() => form.value.seo.metaTitle, (newValue) => {
  if (!form.value.hasVariation) {
    form.value.metaTitle = newValue || '';
  }
});

watch(() => form.value.seo.metaDescription, (newValue) => {
  if (!form.value.hasVariation) {
    form.value.metaDescription = newValue || '';
  }
});

watch(() => form.value.seo.slug, (newValue) => {
  if (!form.value.hasVariation) {
    form.value.urlSlug = newValue || '';
  }
});



const uploadImagesInBackground = async (productId, originalProductData) => {
    try {
        const productData = { ...form.value };
        
    
        
        let savedProductId = productId.value;
       
        
        // Buscar o produto salvo para obter os IDs corretos das variações
        const savedProduct = await productsApi.getById(productId)
        
        const uploadTasks = [];

        if (!originalProductData.hasVariation) {
            // Upload de imagens do produto simples
            const productImages = (originalProductData.photos || []).filter(p => p instanceof File);
            
            if (productImages.length > 0) {
                uploadTasks.push(productsApi.uploadProductImages(productId, productImages));
            }
        } else {
            // Mapear variações originais para variações salvas usando atributos
            const variationImageBatches = [];
            
            (originalProductData.productVariations || []).forEach((originalVariation) => {
                const imagesToUpload = (originalVariation.photos || []).filter(p => p instanceof File);
                
                if (imagesToUpload.length === 0) {
                    return;
                }
                
                // Encontrar variação correspondente no produto salvo
                const matchingVariation = findMatchingVariation(originalVariation, savedProduct.variations || []);
                
                if (matchingVariation && matchingVariation.id) {
                    variationImageBatches.push({
                        variationId: matchingVariation.id,
                        images: imagesToUpload
                    });
                } else {
                    console.warn('Não foi possível encontrar variação correspondente para:', originalVariation.options);
                }
            });
            
            // Criar tasks de upload para cada batch de variação
            variationImageBatches.forEach(batch => {
                uploadTasks.push(productsApi.uploadVariationImages(productId, batch.variationId, batch.images));
            });
        }
        
        if (uploadTasks.length > 0) {
            toast.showInfo(t('product.uploadingImages'));
            
            try {
                await Promise.all(uploadTasks);
                toast.showSuccess(t('product.imagesUploadedSuccessfully'));
            } catch (uploadError) {
                console.error('Falha no upload das imagens:', uploadError);
                toast.showError(t('product.errorUploadingImages'));
            }
        }
        
    } catch (error) {
        console.error('Erro no upload das imagens:', error);
        toast.showError(t('product.errorUploadingImages'));
    }
};

// Função para encontrar a variação correspondente baseada nos atributos
const findMatchingVariation = (originalVariation, savedVariations) => {
    if (!originalVariation.options || !savedVariations || savedVariations.length === 0) {
        return null;
    }
    
    const result = savedVariations.find(savedVariation => {
        if (!savedVariation.attributes) {
            return false;
        }
        
        // Comparar atributos (converter chaves para lowercase para comparação)
        const originalAttrs = {};
        Object.keys(originalVariation.options).forEach(key => {
            originalAttrs[key.toLowerCase()] = String(originalVariation.options[key]).trim();
        });
        
        const savedAttrs = {};
        Object.keys(savedVariation.attributes).forEach(key => {
            savedAttrs[key.toLowerCase()] = String(savedVariation.attributes[key]).trim();
        });
        
        // Verificar se todos os atributos batem
        const originalKeys = Object.keys(originalAttrs).sort();
        const savedKeys = Object.keys(savedAttrs).sort();
        
        if (originalKeys.length !== savedKeys.length) {
            return false;
        }
        
        const matches = originalKeys.every(key => 
            savedKeys.includes(key) && originalAttrs[key] === savedAttrs[key]
        );
        
        return matches;
    });
    
    return result;
};

const checkForImagesToUpload = (productData) => {
    if (!productData.hasVariation) {
        return (productData.photos || []).some(p => p instanceof File);
    }
    return (productData.productVariations || []).some(v => (v.photos || []).some(p => p instanceof File));
};

// Função para calcular posição do slider
const updateSliderPosition = async () => {
  await nextTick();
  
  // Aguarda um pouco mais para garantir que as transições terminaram
  setTimeout(async () => {
    await nextTick();
    
    if (!categoryMenuRef.value) return;
    
    const buttonRefMap = {
      basic: basicButtonRef,
      attributes: attributesButtonRef,
      variations: variationsButtonRef,
      pricing: pricingButtonRef,
      media: mediaButtonRef,
      shipping: shippingButtonRef,
      settings: settingsButtonRef
    };
    
    const activeButtonRef = buttonRefMap[activeCategory.value];
    if (!activeButtonRef?.value?.$el) return;
    
    try {
      const menuRect = categoryMenuRef.value.getBoundingClientRect();
      const buttonRect = activeButtonRef.value.$el.getBoundingClientRect();
      
      // Verifica se os elementos têm dimensões válidas
      if (buttonRect.width === 0 || menuRect.width === 0) {
        // Tenta novamente em 100ms se as dimensões não estão prontas
        setTimeout(() => updateSliderPosition(), 100);
        return;
      }
      
      // Cálculo preciso para cobrir exatamente a área do botão (como o hover)
      const isMobile = window.innerWidth <= 768;
      const menuPadding = isMobile ? 6 : 4; // Padding do menu container ajustado para mobile
      const buttonGap = isMobile ? 5 : 5; // Gap entre botões (mesmo valor para mobile e desktop)
      
      // Posição e largura exatas do botão
      const buttonOffsetFromMenu = buttonRect.left - menuRect.left;
      let width = buttonRect.width;
      let exactOffsetX;
      
      if (isMobile) {
        const mobileSliderPadding = 5;
        width = buttonRect.width - (mobileSliderPadding * 2);
        const buttonCenter = buttonOffsetFromMenu + (buttonRect.width / 2);
        const sliderStart = buttonCenter - (width / 2);
        exactOffsetX = sliderStart - menuPadding - 1.3;
      } else {
        width = buttonRect.width;
        const buttonCenter = buttonOffsetFromMenu + (buttonRect.width / 2);
        const sliderCenter = buttonCenter - (width / 2);
        exactOffsetX = sliderCenter - 4.5;
      }
      
      const finalWidth = width;
      const finalOffsetX = exactOffsetX;
      
      sliderStyle.value = {
        transform: `translateX(${Math.round(finalOffsetX)}px)`,
        width: `${Math.round(finalWidth)}px`,
        opacity: 1
      };
    } catch (error) {
      console.warn('Erro ao calcular posição do slider:', error);
      // Tenta novamente em caso de erro
      setTimeout(() => updateSliderPosition(), 100);
    }
  }, 50);
};


function handleProductSeoToNull() {
  if (form.value.hasVariation) {
    form.value.metaTitle = null;
    form.value.metaDescription = null;
    form.value.urlSlug = null;
    form.value.seoImage = null;
    
    form.value.seo.metaTitle = '';
    form.value.seo.metaDescription = '';
    form.value.seo.slug = '';
  }
}

onMounted(() => {
  loadProduct();

  if (!isEditing.value) {
    nextTick(() => {
      generateBasicProductSeo();
    });
  }
});

</script>

<style scoped>
.product-editor-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
}

/* Header */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-muted);
  margin: 4px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Category Menu */
.category-menu {
  display: flex;
  gap: 5px;
  margin-bottom: 32px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 4px;
  overflow: hidden; /* Previne scrollbar */
  justify-content: center;
  width: fit-content;
  margin-left: auto;
  margin-right: auto;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  position: relative;
  max-width: 100vw;
  box-sizing: border-box;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Slider animado */
.category-slider {
  position: absolute;
  top: 4px;
  left: 4px;
  height: calc(100% - 8px);
  background: var(--iluria-color-primary);
  border-radius: 8px;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
  z-index: 1;
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.15), 0 1px 2px 0 rgba(0, 0, 0, 0.1);
  will-change: transform, width;
  max-width: calc(100% - 8px);
}

/* Animações para abas dinâmicas */
.tab-slide-enter-active {
  transition: all 0.5s cubic-bezier(0.25, 0.46, 0.45, 0.94);
  overflow: hidden;
  will-change: transform, opacity, width, max-width;
}

.tab-slide-leave-active {
  transition: all 0.4s cubic-bezier(0.55, 0.06, 0.68, 0.19);
  overflow: hidden;
  will-change: transform, opacity, width, max-width;
}

.tab-slide-enter-from {
  opacity: 0;
  transform: scaleX(0.1);
  max-width: 0;
  min-width: 0;
  width: 0;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
  border-left-width: 0;
  border-right-width: 0;
}

.tab-slide-leave-to {
  opacity: 0;
  transform: scaleX(0.1);
  max-width: 0;
  min-width: 0;
  width: 0;
  padding-left: 0;
  padding-right: 0;
  margin-left: 0;
  margin-right: 0;
  border-left-width: 0;
  border-right-width: 0;
}

.tab-slide-enter-to,
.tab-slide-leave-from {
  opacity: 1;
  transform: scaleX(1);
  max-width: 200px;
  min-width: auto;
  width: auto;
  will-change: auto;
}

/* Animação específica para ícones e textos dentro das abas */
.category-menu-button {
  border-radius: 8px !important;
  border: none !important;
  font-weight: 500 !important;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1) !important;
  white-space: nowrap;
  flex-shrink: 0;
  transform-origin: left center;
  position: relative;
  backface-visibility: hidden;
  -webkit-font-smoothing: antialiased;
  z-index: 2;
  background: transparent !important;
  color: var(--iluria-color-text) !important;
  box-sizing: border-box;
  padding: 12px 16px !important;
}

.category-menu-button:focus {
  outline: none !important;
  box-shadow: none !important;
}

/* Todos os botões ghost por padrão */
.category-menu-button.btn-ghost {
  color: var(--iluria-color-text-muted) !important;
  background: transparent !important;
}

.category-menu-button.btn-ghost:hover:not(:disabled) {
  background: var(--iluria-color-hover) !important;
  color: var(--iluria-color-text) !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Botão ativo - usa cor contraste para destacar sobre o slider */
.category-menu-button.active {
  color: var(--iluria-color-primary-contrast) !important;
  background: transparent !important;
}

.category-menu-button.active:hover:not(:disabled) {
  color: var(--iluria-color-primary-contrast) !important;
  background: transparent !important;
  transform: none !important;
  box-shadow: none !important;
}

/* Força a cor dos ícones */
.category-menu-button svg {
  fill: currentColor !important;
  stroke: currentColor !important;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-origin: center;
  will-change: transform, opacity;
}

.category-menu-button svg path {
  fill: currentColor !important;
  stroke: currentColor !important;
}

/* Força todos os elementos internos dos ícones */
.category-menu-button svg * {
  fill: inherit !important;
  stroke: inherit !important;
}

.category-label {
  font-size: 14px;
  font-weight: 500;
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1) !important;
  transform-origin: left center;
  will-change: transform, opacity;
}

/* Animação escalonada para conteúdo interno */
.tab-slide-enter-active .category-menu-button svg {
  transition-delay: 0.15s;
}

.tab-slide-enter-active .category-label {
  transition-delay: 0.2s;
}

.tab-slide-leave-active .category-menu-button svg {
  transition-delay: 0s;
}

.tab-slide-leave-active .category-label {
  transition-delay: 0s;
}

/* Reset will-change após animação */
.tab-slide-enter-active:not(.tab-slide-enter-from),
.tab-slide-leave-active:not(.tab-slide-leave-to) {
  will-change: auto;
}

/* Main Content */
.editor-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--iluria-color-background);
}

/* Grid para Frete e Gift Packaging - permite alturas independentes */
.shipping-gift-grid {
  align-items: start;
}

.shipping-gift-grid > * {
  height: auto;
  min-height: auto;
}

/* Category Indicator */
.category-indicator {
  margin-bottom: 20px;
  padding: 12px 16px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.category-indicator-content {
  display: flex;
  align-items: center;
  gap: 8px;
  justify-content: center;
}

.category-indicator-label {
  font-size: 14px;
  color: var(--iluria-color-text-muted);
  font-weight: 500;
}

.category-indicator-name {
  font-size: 14px;
  color: var(--iluria-color-text);
  font-weight: 600;
  background: var(--iluria-color-surface);
  padding: 4px 12px;
  border-radius: 6px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

/* Supplier Section */
.supplier-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text);
}

.field-input,
.field-textarea {
  padding: 12px 16px;
  border: 1px solid var(--iluria-color-input-border);
  border-radius: 8px;
  font-size: 14px;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
}

.field-input:focus,
.field-textarea:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring);
}

.field-textarea {
  resize: vertical;
  min-height: 60px;
}

.field-input::placeholder,
.field-textarea::placeholder {
  color: var(--iluria-color-input-placeholder);
}

/* Responsive */
@media (max-width: 900px) {
  .category-menu {
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    justify-content: flex-start;
    gap: 5px;
  }
  
  .category-menu-button {
    padding: 10px 16px !important;
    font-size: 13px !important;
  }
}

@media (max-width: 600px) {
  .category-menu {
    justify-content: space-between;
    padding: 6px;
    gap: 5px;
  }
  
  .category-menu-button {
    flex: 1;
    max-width: none;
    padding: 14px 8px !important;
  }
}

@media (max-width: 768px) {
  .product-editor-container {
    padding: 16px;
  }
  
  .editor-header {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    gap: 12px;
  }
  
  .header-content {
    flex: 1;
    min-width: 0;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 15px;
  }
  
  .header-actions {
    flex-direction: row;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-start;
  }
  
  .header-actions .btn {
    min-width: 75px;
    font-size: 13px;
    padding: 8px 12px;
  }
}

@media (max-width: 480px) {
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
  }
  
  .header-actions {
    gap: 6px;
  }
  
  .header-actions .btn {
    min-width: 65px;
    font-size: 12px;
    padding: 6px 8px;
  }
}

@media (max-width: 768px) {
  .category-menu {
    gap: 5px;
    padding: 6px;
    width: 100%;
    margin-left: 0;
    margin-right: 0;
    justify-content: flex-start;
    overflow: hidden; /* Previne scrollbar no mobile também */
    display: grid;
    grid-auto-flow: column;
    grid-auto-columns: 1fr;
  }
  
  /* Slider mobile */
  .category-slider {
     top: 6px;
     left: 6px;
     height: calc(100% - 12px);
     border-radius: 6px;
     max-width: calc(100% - 12px);
  }
  
  .category-menu-button {
     padding: 12px !important;
      font-size: 13px !important;
     min-width: 48px !important;
      justify-content: center !important;
     flex-shrink: 0;
    width: 100%;
  }
  
    /* PERFECT ICON CENTERING - This is the key fix */
    .category-menu-button svg{
    width: 22px !important;
    height: 22px !important;
    position: absolute !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    margin: 0 !important;
    flex-shrink: 0;
  }
  
  .category-label {
    display: none;
  }

   /* Ajuste das animações para mobile - mais rápidas e suaves */
    .tab-slide-enter-active {
      transition: all 0.4s cubic-bezier(0.25, 0.46, 0.45, 0.94);
    }
    
    .tab-slide-leave-active {
      transition: all 0.3s cubic-bezier(0.55, 0.06, 0.68, 0.19);
    }
    
    .tab-slide-enter-to,
    .tab-slide-leave-from {
      max-width: 60px;
    }
    
    /* Remove delays no mobile para animação mais rápida */
    .tab-slide-enter-active .category-menu-button svg {
      transition-delay: 0.1s;
    }
    
    .tab-slide-enter-active .category-label {
      transition-delay: 0s; /* Não há label no mobile */
    }
    
    /* Responsivo para o indicador de categoria */
    .category-indicator-content {
      flex-direction: column;
      gap: 4px;
      text-align: center;
    }
    
    .category-indicator-label {
      font-size: 13px;
    }
    
    .category-indicator-name {
      font-size: 13px;
      padding: 3px 10px;
    }
}

/* Form validation styles */
:deep(.p-form-invalid) {
  border-color: #ef4444 !important;
}

:deep(.p-form-error) {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}

/* Special case for variations - full width */
.full-width-container {
  width: 100%;
}

@media (max-width: 768px) {
  .category-menu-button {
    display: flex !important;
    align-items: center !important;
    justify-content: center !important;

    gap: 0 !important;
  }

  .category-menu-button .button-icon {
    margin: 0 !important;
  }

  .category-menu-button .category-label {
    display: none !important;
  }
}
</style> 

