<script setup>
import { RouterView } from 'vue-router';
import ToastContainer from '@/components/ToastContainer.vue';
import { onMounted } from 'vue';
import { useTheme } from '@/composables/useTheme';
import { useFont } from '@/composables/useFont';

// Inicializar sistema de temas e fontes
const { initTheme } = useTheme();
const { initFont } = useFont();

onMounted(() => {
  initTheme();
  initFont();
});
</script>

<template>
  <RouterView />
  <ToastContainer />
</template>
