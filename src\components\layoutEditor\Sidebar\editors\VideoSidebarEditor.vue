<template>
  <div class="video-sidebar-editor">
    <!-- Tabs -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- <PERSON><PERSON> -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        
        <!-- Títu<PERSON> e Descrição -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.content.title') }}</h3>
        
          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.content.videoTitle') }}</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.title"
              :placeholder="$t('videoEditor.content.videoTitlePlaceholder')"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.content.description') }}</IluriaLabel>
            <IluriaTextarea
              v-model="localConfig.description"
              :placeholder="$t('videoEditor.content.descriptionPlaceholder')"
              rows="3"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>

        <!-- Configurações do Botão -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.content.buttonSettings') }}</h3>
          
          <div class="form-group">
            <IluriaCheckBox
              v-model="localConfig.buttonEnabled"
              :label="$t('videoEditor.content.enableButton')"
              @update:modelValue="updateConfig"
            />
          </div>

          <div v-if="localConfig.buttonEnabled" class="form-group">
            <IluriaLabel>{{ $t('videoEditor.content.buttonText') }}</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.buttonText"
              :placeholder="$t('videoEditor.content.buttonTextPlaceholder')"
              @update:modelValue="updateConfig"
            />
          </div>

          <div v-if="localConfig.buttonEnabled" class="form-group">
            <IluriaLabel>{{ $t('videoEditor.content.buttonUrl') }}</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.buttonUrl"
              :placeholder="$t('videoEditor.content.buttonUrlPlaceholder')"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>
      </div>

      <!-- Aba Vídeo -->
      <div v-if="activeTab === 'video'" class="tab-panel">
        
        <!-- Configurações de Vídeo -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.video.title') }}</h3>
      
          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.video.videoUrl') }}</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.videoUrl"
              :placeholder="$t('videoEditor.video.videoUrlPlaceholder')"
              @update:modelValue="updateConfig"
            />
            <small class="form-hint">{{ $t('videoEditor.video.videoUrlHint') }}</small>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.video.videoType') }}</IluriaLabel>
            <IluriaSelect
              v-model="localConfig.videoType"
              :options="[
                { value: 'youtube', label: 'YouTube' },
                { value: 'vimeo', label: 'Vimeo' },
                { value: 'direct', label: $t('videoEditor.video.directVideo') }
              ]"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.video.aspectRatio') }}</IluriaLabel>
            <IluriaSelect
              v-model="localConfig.videoAspectRatio"
              :options="[
                { value: '16:9', label: '16:9 (Widescreen)' },
                { value: '4:3', label: '4:3 (Standard)' },
                { value: '1:1', label: '1:1 (Square)' },
                { value: '21:9', label: '21:9 (Ultrawide)' }
              ]"
              @update:modelValue="updateConfig"
            />
          </div>

          <div v-if="localConfig.videoType === 'direct'" class="form-group">
            <IluriaLabel>{{ $t('videoEditor.video.videoPoster') }}</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.videoPoster"
              :placeholder="$t('videoEditor.video.videoPosterPlaceholder')"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>

        <!-- Opções de Reprodução -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.video.playbackOptions') }}</h3>
          
          <div class="form-group">
            <IluriaCheckBox
              v-model="localConfig.videoAutoplay"
              :label="$t('videoEditor.video.autoplay')"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaCheckBox
              v-model="localConfig.videoLoop"
              :label="$t('videoEditor.video.loop')"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaCheckBox
              v-model="localConfig.videoMuted"
              :label="$t('videoEditor.video.muted')"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaCheckBox
              v-model="localConfig.videoControls"
              :label="$t('videoEditor.video.showControls')"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>
      </div>

      <!-- Aba Design -->
      <div v-if="activeTab === 'design'" class="tab-panel">
        
        <!-- Layout -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.design.layout') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.design.layoutType') }}</IluriaLabel>
            <div class="layout-options">
              <button
                v-for="layout in layoutOptions"
                :key="layout.value"
                :class="['layout-btn', { active: localConfig.layout === layout.value }]"
                @click="setLayout(layout.value)"
              >
                <div class="layout-preview" :class="layout.value">
                  <div class="preview-video"></div>
                  <div class="preview-content"></div>
                </div>
                <span class="layout-name">{{ $t(layout.label) }}</span>
              </button>
            </div>
          </div>

          <div v-if="localConfig.layout === 'horizontal'" class="form-group">
            <IluriaLabel>{{ $t('videoEditor.design.videoPosition') }}</IluriaLabel>
            <IluriaSelect
              v-model="localConfig.videoPosition"
              :options="[
                { value: 'left', label: $t('videoEditor.design.positionLeft') },
                { value: 'right', label: $t('videoEditor.design.positionRight') }
              ]"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>

        <!-- Cores -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.colors.title') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.colors.backgroundColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="localConfig.backgroundColor"
                @update:modelValue="updateConfig"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.colors.textColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="localConfig.textColor"
                @update:modelValue="updateConfig"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.colors.buttonColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="localConfig.buttonColor"
                @update:modelValue="updateConfig"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.colors.buttonTextColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="localConfig.buttonTextColor"
                @update:modelValue="updateConfig"
              />
            </div>
          </div>
        </div>

        <!-- Espaçamento -->
        <div class="section">
          <h3 class="section-title">{{ $t('videoEditor.spacing.title') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.spacing.paddingTop') }}</IluriaLabel>
            <IluriaRange
              v-model="localConfig.paddingTop"
              :min="0"
              :max="120"
              :step="10"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.spacing.paddingBottom') }}</IluriaLabel>
            <IluriaRange
              v-model="localConfig.paddingBottom"
              :min="0"
              :max="120"
              :step="10"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('videoEditor.spacing.borderRadius') }}</IluriaLabel>
            <IluriaRange
              v-model="localConfig.borderRadius"
              :min="0"
              :max="50"
              :step="2"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'
import IluriaTextarea from '@/components/Textarea.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed'])

// Tabs
const tabs = [
  { value: 'content', label: t('layoutEditor.content') },
  { value: 'video', label: t('layoutEditor.video') },
  { value: 'design', label: t('layoutEditor.design') }
]

// Estado das abas
const activeTab = ref('content')

// Configuração local do vídeo
const localConfig = ref({
  title: '',
  description: '',
  buttonText: '',
  buttonUrl: '',
  buttonEnabled: true,
  videoUrl: '',
  videoType: 'youtube',
  videoPoster: '',
  videoAutoplay: false,
  videoLoop: false,
  videoMuted: false,
  videoControls: true,
  layout: 'horizontal',
  videoPosition: 'left',
  backgroundColor: '#2c3e50',
  textColor: '#ffffff',
  buttonColor: '#3498db',
  buttonTextColor: '#ffffff',
  paddingTop: 60,
  paddingBottom: 60,
  borderRadius: 12,
  videoAspectRatio: '16:9'
})

// Opções de layout
const layoutOptions = ref([
  {
    value: 'horizontal',
    label: 'videoEditor.design.horizontal'
  },
  {
    value: 'vertical',
    label: 'videoEditor.design.vertical'
  }
])

// Carrega configuração do elemento
const loadConfig = () => {
  if (!props.element) return
  
  localConfig.value = {
    title: props.element.getAttribute('data-video-title') || 'Principais itens básicos essenciais',
    description: props.element.getAttribute('data-video-description') || 'Não sabe por onde começar para criar um guarda-roupa cápsula? Comece pelo básico.',
    buttonText: props.element.getAttribute('data-button-text') || 'Compre agora',
    buttonUrl: props.element.getAttribute('data-button-url') || '#',
    buttonEnabled: props.element.getAttribute('data-button-enabled') !== 'false',
    videoUrl: props.element.getAttribute('data-video-url') || '',
    videoType: props.element.getAttribute('data-video-type') || 'youtube',
    videoPoster: props.element.getAttribute('data-video-poster') || '',
    videoAutoplay: props.element.getAttribute('data-video-autoplay') === 'true',
    videoLoop: props.element.getAttribute('data-video-loop') === 'true',
    videoMuted: props.element.getAttribute('data-video-muted') === 'true',
    videoControls: props.element.getAttribute('data-video-controls') !== 'false',
    layout: props.element.getAttribute('data-layout') || 'horizontal',
    videoPosition: props.element.getAttribute('data-video-position') || 'left',
    backgroundColor: props.element.getAttribute('data-bg-color') || '#2c3e50',
    textColor: props.element.getAttribute('data-text-color') || '#ffffff',
    buttonColor: props.element.getAttribute('data-button-color') || '#3498db',
    buttonTextColor: props.element.getAttribute('data-button-text-color') || '#ffffff',
    paddingTop: parseInt(props.element.getAttribute('data-padding-top') || '60'),
    paddingBottom: parseInt(props.element.getAttribute('data-padding-bottom') || '60'),
    borderRadius: parseInt(props.element.getAttribute('data-border-radius') || '12'),
    videoAspectRatio: props.element.getAttribute('data-video-aspect-ratio') || '16:9'
  }
}

// Atualiza configuração
const updateConfig = () => {
  if (!props.element) {
    return
  }
  
  // Atualiza atributos do elemento
  props.element.setAttribute('data-video-title', localConfig.value.title)
  props.element.setAttribute('data-video-description', localConfig.value.description)
  props.element.setAttribute('data-button-text', localConfig.value.buttonText)
  props.element.setAttribute('data-button-url', localConfig.value.buttonUrl)
  props.element.setAttribute('data-button-enabled', localConfig.value.buttonEnabled.toString())
  props.element.setAttribute('data-video-url', localConfig.value.videoUrl)
  props.element.setAttribute('data-video-type', localConfig.value.videoType)
  props.element.setAttribute('data-video-poster', localConfig.value.videoPoster)
  props.element.setAttribute('data-video-autoplay', localConfig.value.videoAutoplay.toString())
  props.element.setAttribute('data-video-loop', localConfig.value.videoLoop.toString())
  props.element.setAttribute('data-video-muted', localConfig.value.videoMuted.toString())
  props.element.setAttribute('data-video-controls', localConfig.value.videoControls.toString())
  props.element.setAttribute('data-layout', localConfig.value.layout)
  props.element.setAttribute('data-video-position', localConfig.value.videoPosition)
  props.element.setAttribute('data-bg-color', localConfig.value.backgroundColor)
  props.element.setAttribute('data-text-color', localConfig.value.textColor)
  props.element.setAttribute('data-button-color', localConfig.value.buttonColor)
  props.element.setAttribute('data-button-text-color', localConfig.value.buttonTextColor)
  props.element.setAttribute('data-padding-top', localConfig.value.paddingTop.toString())
  props.element.setAttribute('data-padding-bottom', localConfig.value.paddingBottom.toString())
  props.element.setAttribute('data-border-radius', localConfig.value.borderRadius.toString())
  props.element.setAttribute('data-video-aspect-ratio', localConfig.value.videoAspectRatio)
  
  // Reinicializa o componente
  reinitializeComponent()
  
  emit('data-changed')
}

// Define layout
const setLayout = (layout) => {
  localConfig.value.layout = layout
  if (layout === 'vertical') {
    localConfig.value.videoPosition = 'top'
  }
  updateConfig()
}

// Reinicializa componente
const reinitializeComponent = () => {
  if (!props.element) {
    return
  }
  
  try {
    // Remove marcação de inicializado para forçar re-processamento
    props.element.removeAttribute('data-initialized')
    props.element.removeAttribute('data-video-processed')
    
    // Chama o inicializador global que funciona para outros componentes
    if (window.VideoComponent && window.VideoComponent.initializeVideo) {
      window.VideoComponent.initializeVideo(props.element)
    } else {
      // Regenera HTML completo baseado nos novos atributos
      const videoData = loadVideoData()
      const videoHTML = generateVideoHTML(videoData)
      
      // Atualiza conteúdo
      props.element.innerHTML = videoHTML
    }
    
  } catch (error) {
    // Silently handle error
  }
}

// Função para carregar dados atualizados do elemento
const loadVideoData = () => {
  return {
    title: localConfig.value.title,
    description: localConfig.value.description,
    buttonText: localConfig.value.buttonText,
    buttonUrl: localConfig.value.buttonUrl,
    buttonEnabled: localConfig.value.buttonEnabled,
    videoUrl: localConfig.value.videoUrl,
    videoType: localConfig.value.videoType,
    videoPoster: localConfig.value.videoPoster,
    videoAutoplay: localConfig.value.videoAutoplay,
    videoLoop: localConfig.value.videoLoop,
    videoMuted: localConfig.value.videoMuted,
    videoControls: localConfig.value.videoControls,
    layout: localConfig.value.layout,
    videoPosition: localConfig.value.videoPosition,
    backgroundColor: localConfig.value.backgroundColor,
    textColor: localConfig.value.textColor,
    buttonColor: localConfig.value.buttonColor,
    buttonTextColor: localConfig.value.buttonTextColor,
    paddingTop: localConfig.value.paddingTop,
    paddingBottom: localConfig.value.paddingBottom,
    borderRadius: localConfig.value.borderRadius,
    videoAspectRatio: localConfig.value.videoAspectRatio
  }
}

// Gera HTML do vídeo (versão simplificada)
const generateVideoHTML = (data) => {
  return `
    <div class="video-section">
      <div class="container">
        <div class="video-content ${data.layout}">
          <div class="video-wrapper">
            <div class="video-embed">
              <!-- Video content here -->
            </div>
          </div>
          <div class="content-wrapper">
            <h2 class="video-title">${data.title}</h2>
            <p class="video-description">${data.description}</p>
            ${data.buttonEnabled ? `<a href="${data.buttonUrl}" class="video-button">${data.buttonText}</a>` : ''}
          </div>
        </div>
      </div>
    </div>
  `
}

// Lifecycle
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.video-sidebar-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Tabs */
.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  overflow-x: auto;
  flex-shrink: 0;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-bg);
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
}

.section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.form-group {
  margin-bottom: 1rem;
}

.color-input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.form-hint {
  display: block;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin-top: 0.25rem;
}

/* Layout Options */
.layout-options {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 0.75rem;
}

.layout-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.layout-btn:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary-border);
}

.layout-btn.active {
  background: var(--iluria-color-primary-bg);
  border-color: var(--iluria-color-primary);
}

.layout-preview {
  width: 60px;
  height: 40px;
  background: var(--iluria-color-surface);
  border-radius: 4px;
  position: relative;
  overflow: hidden;
}

.layout-preview.horizontal {
  display: flex;
}

.layout-preview.vertical {
  display: flex;
  flex-direction: column;
}

.preview-video {
  background: var(--iluria-color-primary);
  opacity: 0.7;
}

.preview-content {
  background: var(--iluria-color-text-secondary);
  opacity: 0.3;
}

.layout-preview.horizontal .preview-video {
  width: 50%;
  height: 100%;
}

.layout-preview.horizontal .preview-content {
  width: 50%;
  height: 100%;
}

.layout-preview.vertical .preview-video {
  width: 100%;
  height: 60%;
}

.layout-preview.vertical .preview-content {
  width: 100%;
  height: 40%;
}

.layout-name {
  font-size: 0.75rem;
  font-weight: 500;
  text-align: center;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-panel {
    padding: 0.75rem;
  }
  
  .layout-options {
    grid-template-columns: 1fr;
  }
}
</style> 