<template>
  <IluriaModal
    :model-value="isVisible"
    @update:model-value="isVisible = $event"
    :dialog-style="{ width: '95vw', height: '90vh', maxWidth: 'none', maxHeight: 'none' }"
    :content-style="{ padding: '0', height: 'calc(90vh - 120px)' }"
    :show-footer="false"
    @close="handleClose"
  >
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-3">
          <h2 class="text-lg font-semibold text-[var(--iluria-color-text-primary)]">
            {{ $t('themes.preview.title') }}
          </h2>
          <span class="text-sm text-[var(--iluria-color-text-secondary)]">
            {{ theme.name }}
          </span>
        </div>

        <div class="preview-header-actions">




        <!-- Actions -->
        <div class="preview-actions">
          <IluriaButton
            variant="outline"
            @click="refreshPreview"
            :hugeIcon="RefreshIcon"
            :loading="isRefreshing"
          >
            {{ $t('themes.refresh') }}
          </IluriaButton>

          <IluriaButton
            variant="solid"
            color="primary"
            @click="selectTheme"
            :loading="isSelecting"
          >
            {{ $t('themes.preview.selectTheme') }}
          </IluriaButton>
        </div>
        </div>
      </div>
    </template>

    <div class="preview-container">
      <!-- Theme info sidebar -->
      <div class="theme-sidebar">
        <div class="theme-info">
          <h3 class="theme-name">{{ theme.name }}</h3>
          <p class="theme-description">{{ theme.description }}</p>
          
          <!-- Tags -->
          <div class="theme-tags" v-if="theme.tags">
            <span 
              v-for="tag in theme.tags"
              :key="tag"
              class="tag"
            >
              {{ $t(`themes.tags.${tag}`, tag) }}
            </span>
          </div>
          
          <!-- Color palette -->
          <div class="color-section" v-if="theme.colors">
            <h4 class="section-title">{{ $t('themes.colorPalette') }}</h4>
            <div class="color-grid">
              <div 
                v-for="(color, name) in theme.colors"
                :key="name"
                class="color-item"
              >
                <div 
                  class="color-swatch"
                  :style="{ backgroundColor: color }"
                ></div>
                <div class="color-info">
                  <span class="color-name">{{ $t(`themes.${name}Color`, name) }}</span>
                  <span class="color-value">{{ color }}</span>
                </div>
              </div>
            </div>
          </div>
          
          <!-- Theme details -->
          <div class="details-section">
            <h4 class="section-title">{{ $t('themes.details') }}</h4>
            <div class="detail-item">
              <span class="detail-label">{{ $t('themes.layout') }}:</span>
              <span class="detail-value">{{ $t(`themes.layout${theme.layout === 'container' ? 'Container' : 'FullWidth'}`) }}</span>
            </div>
            <div class="detail-item">
              <span class="detail-label">{{ $t('themes.author') }}:</span>
              <span class="detail-value">{{ theme.author || 'Iluria' }}</span>
            </div>
            <div class="detail-item" v-if="theme.version">
              <span class="detail-label">{{ $t('themes.version') }}:</span>
              <span class="detail-value">v{{ theme.version }}</span>
            </div>
          </div>
          

        </div>
      </div>
      
      <!-- Preview area -->
      <div class="preview-area" :class="`view-mode-${viewMode}`">
        <!-- Dual view (Desktop + Mobile) -->
        <div class="dual-preview">
          <!-- Desktop preview -->
          <div class="desktop-container">
            <div class="device-label">
              <HugeiconsIcon :icon="ComputerIcon" />
              <span>{{ $t('themes.preview.desktop') }}</span>
            </div>
            <div class="desktop-frame">
              <div class="browser-chrome">
                <div class="browser-controls">
                  <div class="browser-dots">
                    <span class="dot red"></span>
                    <span class="dot yellow"></span>
                    <span class="dot green"></span>
                  </div>
                  <div class="browser-url">
                    <span>{{ theme.name.toLowerCase().replace(/\s+/g, '-') }}.iluria.store</span>
                  </div>
                </div>
              </div>
              <iframe
                :src="getDevicePreviewUrl(theme, 'desktop')"
                class="preview-iframe desktop-iframe"
                title="Desktop theme preview"
                @load="onPreviewLoad"
                @error="onPreviewError"
                sandbox="allow-scripts allow-same-origin allow-forms"
              ></iframe>

              <!-- Loading overlay for desktop -->
              <div v-if="isLoading" class="preview-loading desktop-loading">
                <div class="loading-spinner"></div>
                <p>{{ $t('themes.loadingPreview') }}</p>
              </div>
            </div>
          </div>

          <!-- Mobile preview -->
          <div class="mobile-container">
            <div class="device-label">
              <HugeiconsIcon :icon="SmartPhone02Icon" />
              <span>{{ $t('themes.preview.mobile') }}</span>
            </div>
            <div class="mobile-frame">
              <div class="mobile-chrome">
                <div class="mobile-notch"></div>
                <div class="mobile-status-bar">
                  <span class="time">9:41</span>
                  <div class="mobile-indicators">
                    <span class="signal">●●●</span>
                    <span class="wifi">📶</span>
                    <span class="battery">🔋</span>
                  </div>
                </div>
              </div>
              <iframe
                :src="getDevicePreviewUrl(theme, 'mobile')"
                class="preview-iframe mobile-iframe"
                title="Mobile theme preview"
                @load="onPreviewLoad"
                @error="onPreviewError"
                sandbox="allow-scripts allow-same-origin allow-forms"
              ></iframe>

              <!-- Loading overlay for mobile -->
              <div v-if="isLoading" class="preview-loading mobile-loading">
                <div class="loading-spinner"></div>
                <p>{{ $t('themes.loadingPreview') }}</p>
              </div>
            </div>
          </div>
        </div>

        <!-- Preview navigation -->
        <div class="preview-navigation">
          <fieldset class="nav-controls">
            <legend class="sr-only">Preview navigation</legend>
            <IluriaButton
              variant="ghost"
              color="dark"
              size="small"
              @click="navigatePreview('back')"
              :hugeIcon="ArrowLeftIcon"
              :disabled="!canGoBack"
              :aria-label="$t('themes.back')"
            >
              {{ $t('themes.back') }}
            </IluriaButton>

            <IluriaButton
              variant="ghost"
              color="dark"
              size="small"
              @click="navigatePreview('forward')"
              :hugeIcon="ArrowRightIcon"
              :disabled="!canGoForward"
              :aria-label="$t('themes.forward')"
            >
              {{ $t('themes.forward') }}
            </IluriaButton>

            <IluriaButton
              variant="ghost"
              color="dark"
              size="small"
              @click="navigatePreview('home')"
              :hugeIcon="HomeIcon"
              :aria-label="$t('themes.home')"
            >
              {{ $t('themes.home') }}
            </IluriaButton>
          </fieldset>
          
          <div class="url-bar">
            <input
              v-model="currentUrl"
              class="url-input"
              readonly
              :placeholder="$t('themes.previewUrl')"
            />
          </div>
        </div>
      </div>
    </div>
  </IluriaModal>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  RefreshIcon,
  ArrowLeftIcon,
  ArrowRightIcon,
  HomeIcon,
  ComputerIcon,
  SmartPhone02Icon
} from '@hugeicons-pro/core-stroke-standard'

import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { useThemePreview } from '@/composables/useThemePreview'

const { t } = useI18n()

// Composables
const { getThemePreviewUrl, getDevicePreviewUrl } = useThemePreview()

// Props
const props = defineProps({
  theme: {
    type: Object,
    required: true,
    validator: (theme) => {
      return theme && theme.id && theme.name
    }
  },
  visible: {
    type: Boolean,
    default: true
  }
})

// Emits
const emit = defineEmits(['close', 'select', 'update:visible'])

// State
const isVisible = ref(props.visible)
const currentDevice = ref('desktop')
const viewMode = ref('dual') // 'dual' or 'single'
const isLoading = ref(true)
const isRefreshing = ref(false)
const isSelecting = ref(false)
const currentUrl = ref('')
const canGoBack = ref(false)
const canGoForward = ref(false)



// Methods




const refreshPreview = async () => {
  isRefreshing.value = true
  isLoading.value = true
  
  // Simular refresh
  await new Promise(resolve => setTimeout(resolve, 1000))
  
  // Force reload iframe
  const iframe = document.querySelector('.preview-iframe')
  if (iframe) {
    const currentSrc = iframe.src
    iframe.src = 'about:blank'
    iframe.src = currentSrc
  }
  
  isRefreshing.value = false
}

const selectTheme = async () => {
  isSelecting.value = true
  
  try {
    emit('select', props.theme)
    // Fechar modal após seleção
    setTimeout(() => {
      handleClose()
    }, 1000)
  } finally {
    isSelecting.value = false
  }
}

const handleClose = () => {
  // Reset all states before closing
  isLoading.value = true
  isRefreshing.value = false
  isSelecting.value = false
  canGoBack.value = false
  canGoForward.value = false
  currentUrl.value = ''

  isVisible.value = false
  emit('update:visible', false)
  emit('close')
}

const onPreviewLoad = () => {
  isLoading.value = false
  // Simular atualização da URL
  if (props.theme && props.theme.name) {
    currentUrl.value = `${props.theme.name.toLowerCase().replace(/\s+/g, '-')}.iluria.store`
  }
}

const onPreviewError = () => {
  isLoading.value = false
  console.error('Failed to load preview for theme:', props.theme?.name)
}

const navigatePreview = (direction) => {
  // Mock navigation
  switch (direction) {
    case 'back':
      canGoBack.value = false
      canGoForward.value = true
      break
    case 'forward':
      canGoBack.value = true
      canGoForward.value = false
      break
    case 'home':
      canGoBack.value = false
      canGoForward.value = false
      currentUrl.value = `${props.theme.name.toLowerCase().replace(/\s+/g, '-')}.iluria.store`
      break
  }
}

// Watch device changes
watch(currentDevice, () => {
  isLoading.value = true
})

// Watch view mode changes
watch(viewMode, () => {
  // View mode changed
})

// Watch para mudanças na visibilidade
watch(() => props.visible, (newVisible) => {
  isVisible.value = newVisible
  if (newVisible) {
    // Reset loading state when modal opens
    isLoading.value = true
    // Reset other states
    isRefreshing.value = false
    isSelecting.value = false
    canGoBack.value = false
    canGoForward.value = false
  }
}, { immediate: true })

// Watch para mudanças no tema
watch(() => props.theme, (newTheme) => {
  if (newTheme && getThemePreviewUrl) {
    // Verificar tipo de URL quando o tema muda
    try {
      getThemePreviewUrl(newTheme)
    } catch (error) {
      console.error('Erro ao processar novo tema:', error)
    }
  }
}, { immediate: false })

// Lifecycle
onMounted(() => {
  
  // Simular carregamento inicial mais rápido
  setTimeout(() => {
    isLoading.value = false
    onPreviewLoad()
  }, 800)
})
</script>

<style scoped>
.preview-header-actions {
  display: flex;
  align-items: center;
  gap: 2rem;
}



.preview-actions {
  display: flex;
  gap: 1rem;
  padding-right: 1rem;
}

.preview-container {
  display: grid;
  grid-template-columns: 300px 1fr;
  height: calc(90vh - 120px);
  gap: 2rem;
  overflow: hidden;
}

/* Theme sidebar */
.theme-sidebar {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 1.5rem;
  overflow-y: auto;
  max-height: 100%;
  scrollbar-width: thin;
  scrollbar-color: rgba(var(--iluria-color-primary-rgb), 0.3) transparent;
}

.theme-sidebar::-webkit-scrollbar {
  width: 6px;
}

.theme-sidebar::-webkit-scrollbar-track {
  background: transparent;
}

.theme-sidebar::-webkit-scrollbar-thumb {
  background: rgba(var(--iluria-color-primary-rgb), 0.3);
  border-radius: 3px;
}

.theme-sidebar::-webkit-scrollbar-thumb:hover {
  background: rgba(var(--iluria-color-primary-rgb), 0.5);
}

.theme-info {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.theme-name {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.theme-description {
  color: var(--iluria-color-text-secondary);
  line-height: 1.5;
}

.theme-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
}

.section-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.beta-badge {
  background: var(--iluria-color-warning-light);
  color: var(--iluria-color-warning);
  padding: 0.125rem 0.5rem;
  border-radius: 10px;
  font-size: 0.625rem;
  font-weight: 600;
  margin-left: auto;
}

.color-grid {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.color-swatch {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  flex-shrink: 0;
}

.color-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.color-name {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
}

.color-value {
  font-family: monospace;
  color: var(--iluria-color-text-secondary);
  font-size: 0.75rem;
}

.detail-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 0;
  border-bottom: 1px solid var(--iluria-color-border);
}

.detail-item:last-child {
  border-bottom: none;
}

.detail-label {
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
}

.detail-value {
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  font-size: 0.875rem;
}

/* AI Suggestions */
.ai-section {
  background: rgba(var(--iluria-color-primary-rgb), 0.02);
  border-radius: 8px;
  padding: 1rem;
}

.ai-suggestions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.ai-suggestion {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--iluria-color-surface);
  border-radius: 6px;
  opacity: 0.7;
}

.suggestion-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--iluria-color-primary);
  flex-shrink: 0;
}

.suggestion-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.suggestion-title {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
}

.suggestion-desc {
  color: var(--iluria-color-text-secondary);
  font-size: 0.75rem;
}

.suggestion-action {
  flex-shrink: 0;
}

/* Preview area */
.preview-area {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  flex: 1;
  min-height: 0;
}

/* Dual Preview Layout */
.dual-preview {
  display: flex;
  gap: 2.5rem;
  height: 100%;
  padding: 2rem;
  background: linear-gradient(135deg, var(--iluria-color-surface) 0%, rgba(var(--iluria-color-primary-rgb), 0.03) 100%);
  border: 1px solid rgba(var(--iluria-color-primary-rgb), 0.08);
  border-radius: 16px;
  position: relative;
  overflow: hidden;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.02),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
}

.dual-preview::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(circle at 20% 80%, rgba(var(--iluria-color-primary-rgb), 0.08) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(var(--iluria-color-secondary-rgb), 0.08) 0%, transparent 50%);
  pointer-events: none;
}

.desktop-container {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  position: relative;
  z-index: 1;
}

.mobile-container {
  flex: 0 0 auto;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  width: 280px;
  position: relative;
  z-index: 1;
  align-items: center;
}

.device-label {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  padding: 0.5rem 0;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.device-label svg {
  width: 20px;
  height: 20px;
  color: var(--iluria-color-primary);
}

/* Desktop Frame */
.desktop-frame {
  flex: 1;
  background: #ffffff;
  border-radius: 16px;
  overflow: hidden;
  box-shadow:
    0 20px 25px -5px rgba(0, 0, 0, 0.1),
    0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid rgba(0, 0, 0, 0.05);
  transition: all 0.3s ease;
  position: relative;
}

.desktop-frame:hover {
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.15),
    0 20px 25px -5px rgba(0, 0, 0, 0.1);
  transform: translateY(-2px);
}

.browser-chrome {
  background: linear-gradient(180deg, #f7f8fa 0%, #e9ecef 100%);
  padding: 1rem 1.25rem;
  border-bottom: 1px solid #dee2e6;
  position: relative;
}

.browser-chrome::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.8) 50%, transparent 100%);
}

.browser-controls {
  display: flex;
  align-items: center;
  gap: 1.25rem;
}

.browser-dots {
  display: flex;
  gap: 0.5rem;
}

.dot {
  width: 14px;
  height: 14px;
  border-radius: 50%;
  box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
  transition: all 0.2s ease;
  cursor: pointer;
}

.dot:hover {
  transform: scale(1.1);
}

.dot.red {
  background: linear-gradient(135deg, #ff6b6b 0%, #ff5252 100%);
}
.dot.yellow {
  background: linear-gradient(135deg, #ffd93d 0%, #ffb300 100%);
}
.dot.green {
  background: linear-gradient(135deg, #51cf66 0%, #40c057 100%);
}

.browser-url {
  flex: 1;
  background: #ffffff;
  padding: 0.75rem 1.25rem;
  border-radius: 24px;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  border: 1px solid #e9ecef;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05);
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  transition: all 0.2s ease;
}

.browser-url:hover {
  border-color: var(--iluria-color-primary-light);
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.05), 0 0 0 3px rgba(var(--iluria-color-primary-rgb), 0.1);
}

.desktop-iframe {
  width: 100%;
  height: calc(100% - 75px);
  border: none;
  background: #ffffff;
}

/* Mobile Frame */
.mobile-frame {
  flex: 1;
  background: linear-gradient(145deg, #2d3748 0%, #1a202c 100%);
  border-radius: 36px;
  padding: 14px;
  box-shadow:
    0 25px 50px -12px rgba(0, 0, 0, 0.4),
    0 0 0 1px rgba(255, 255, 255, 0.05);
  max-width: 320px;
  width: 100%;
  position: relative;
  transition: all 0.3s ease;
  overflow: visible;
  z-index: 2;
}

.mobile-frame:hover {
  transform: translateY(-4px);
  box-shadow:
    0 35px 60px -12px rgba(0, 0, 0, 0.5),
    0 0 0 1px rgba(255, 255, 255, 0.1);
}

.mobile-frame::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  border-radius: 36px;
  padding: 1px;
  background: linear-gradient(145deg, rgba(255,255,255,0.1) 0%, rgba(255,255,255,0.05) 100%);
  -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  -webkit-mask-composite: exclude;
  mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
  mask-composite: exclude;
}

.mobile-chrome {
  background: linear-gradient(180deg, #2d3748 0%, #1a202c 100%);
  border-radius: 28px 28px 0 0;
  position: relative;
  overflow: hidden;
  height: 56px;
  width: 100%;
}

.mobile-notch {
  width: 140px;
  height: 28px;
  background: #000000;
  border-radius: 0 0 16px 16px;
  margin: 0 auto;
  position: relative;
  z-index: 2;
  box-shadow: inset 0 -2px 4px rgba(0, 0, 0, 0.3);
}

.mobile-status-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.5rem 1.75rem;
  color: #ffffff;
  font-size: 0.8rem;
  font-weight: 600;
  margin-top: -18px;
  padding-top: 1.25rem;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
}

.mobile-indicators {
  display: flex;
  gap: 0.375rem;
  align-items: center;
  font-size: 0.75rem;
}

.mobile-iframe {
  width: 166% !important;
  height: calc((100% - 61px) / 0.6) !important;
  border: none;
  border-radius: 0 0 28px 28px;
  background: #ffffff;
  box-shadow: inset 0 1px 3px rgba(0, 0, 0, 0.1);
  position: relative;
  overflow: auto;
  transform: scale(0.6);
  transform-origin: top left;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .dual-preview {
    flex-direction: column;
    gap: 2rem;
    padding: 1.5rem;
    align-items: center;
  }

  .mobile-container {
    max-width: 350px;
    order: -1;
  }

  .mobile-frame {
    max-width: 300px;
  }

  .desktop-container {
    order: 1;
    width: 100%;
  }
}

@media (max-width: 768px) {
  .preview-header-actions {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }

  .view-mode-selector,
  .device-selector {
    justify-content: center;
  }

  .dual-preview {
    padding: 1rem;
    gap: 1.5rem;
  }

  .mobile-container {
    max-width: 320px;
  }

  .mobile-frame {
    max-width: 280px;
  }
}



.preview-device {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--iluria-color-surface) 0%, rgba(var(--iluria-color-primary-rgb), 0.03) 100%);
  border: 1px solid rgba(var(--iluria-color-primary-rgb), 0.08);
  border-radius: 16px;
  position: relative;
  box-shadow:
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.02);
}

.device-frame {
  position: relative;
  background: #333;
  border-radius: 20px;
  padding: 1rem;
}

.device-tablet .device-frame {
  width: 768px;
  height: 1024px;
  max-width: 90%;
  max-height: 90%;
}

.device-mobile .device-frame {
  width: 375px;
  height: 812px;
  max-width: 90%;
  max-height: 90%;
  border-radius: 25px;
}

.device-screen {
  width: 100%;
  height: 100%;
  background: white;
  border-radius: inherit;
  position: relative;
}

.desktop-preview {
  width: 100%;
  height: 100%;
  position: relative;
}

.mock-preview {
  width: 100%;
  height: 100%;
  overflow-y: auto;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
}

.mock-header {
  padding: 1rem 2rem;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
}

.mock-logo {
  font-size: 1.5rem;
  font-weight: bold;
}

.mock-nav {
  display: flex;
  gap: 2rem;
}

.mock-nav span {
  cursor: pointer;
  opacity: 0.9;
  transition: opacity 0.2s;
}

.mock-nav span:hover {
  opacity: 1;
}

.mock-content {
  padding: 0;
}

.mock-hero {
  padding: 4rem 2rem;
  text-align: center;
}

.mock-hero h1 {
  font-size: 2.5rem;
  font-weight: bold;
  margin-bottom: 1rem;
}

.mock-hero p {
  font-size: 1.2rem;
  margin-bottom: 2rem;
  opacity: 0.8;
}

.mock-button {
  padding: 0.75rem 2rem;
  border: none;
  border-radius: 6px;
  color: white;
  font-weight: 600;
  cursor: pointer;
  transition: transform 0.2s;
}

.mock-button:hover {
  transform: translateY(-1px);
}

.mock-features {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 2rem;
  padding: 4rem 2rem;
}

.mock-feature {
  text-align: center;
  padding: 2rem;
}

.mock-icon {
  width: 4rem;
  height: 4rem;
  border-radius: 50%;
  margin: 0 auto 1rem;
  opacity: 0.1;
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
  overflow: auto;
  -webkit-overflow-scrolling: touch;
}

.preview-loading {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: rgba(255, 255, 255, 0.95);
  backdrop-filter: blur(4px);
  gap: 1rem;
  z-index: 10;
  border-radius: inherit;
}

.desktop-loading {
  border-radius: 0 0 12px 12px;
}

.mobile-loading {
  border-radius: 0 0 20px 20px;
}

.loading-spinner {
  width: 2.5rem;
  height: 2.5rem;
  border: 3px solid rgba(var(--iluria-color-primary-rgb), 0.2);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.preview-loading p {
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  margin: 0;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Preview navigation */
.preview-navigation {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.nav-controls {
  display: flex;
  gap: 0.5rem;
  border: none;
  margin: 0;
  padding: 0;
}

.nav-controls legend {
  position: absolute;
  width: 1px;
  height: 1px;
  padding: 0;
  margin: -1px;
  overflow: hidden;
  clip: rect(0, 0, 0, 0);
  white-space: nowrap;
  border: 0;
}

.url-bar {
  flex: 1;
}

.url-input {
  width: 100%;
  padding: 0.5rem 1rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  background: var(--iluria-color-background);
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .preview-container {
    grid-template-columns: 1fr;
    gap: 1rem;
  }

  .theme-sidebar {
    order: 2;
    max-height: 300px;
  }

  .preview-area {
    order: 1;
  }

  /* Stack dual preview vertically on tablets */
  .dual-preview {
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
    align-items: center;
  }

  .mobile-container {
    max-width: 300px;
    flex: 0 0 auto;
  }

  .mobile-frame {
    max-width: 280px;
    margin: 0 auto;
  }

  .desktop-container {
    width: 100%;
    max-width: 800px;
  }
}

@media (max-width: 768px) {
  .preview-header-actions {
    flex-direction: column;
    gap: 1rem;
  }

  .view-mode-selector,
  .device-selector {
    width: 100%;
    justify-content: center;
  }

  .preview-actions {
    width: 100%;
    justify-content: center;
  }

  .preview-navigation {
    flex-direction: column;
    gap: 1rem;
  }

  .nav-controls {
    width: 100%;
    justify-content: center;
  }
}

@media (max-width: 480px) {
  .mobile-container {
    max-width: 280px;
    min-width: 260px;
  }

  .mobile-frame {
    max-width: 240px;
  }

  .dual-preview {
    padding: 0.75rem;
    gap: 1rem;
  }
}
</style>