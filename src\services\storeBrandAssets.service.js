import axios from 'axios';
import { API_URLS } from '../config/api.config';

class StoreBrandAssetsService {
  #endpoint;

  constructor() {
    this.#endpoint = `${API_URLS.STORE_BASE_URL}/brand-assets`;
  }
  
  getEndpoint() {
    return this.#endpoint;
  }
  
  async getStoreBrandAssets() {
    try {
      const response = await axios.get(`${this.#endpoint}`);
      return response.data;
    } catch (error) {
      if (error.response && error.response.status === 404) {
        return this.#getDefaultSettings();
      }
      
      console.error('Erro ao buscar configurações de marca:', error);
      return this.#getDefaultSettings();
    }
  }

  #getDefaultSettings() {
    return {
      id: null,
      storeId: null,
      logoUrl: null,
      faviconUrl: null,
      createdAt: null,
      updatedAt: null
    };
  }
  
  /**
   * Faz o upload de uma imagem de logo
   * @param {File} file - Arquivo de imagem para upload
   * @returns {Promise<Object>} - Configurações atualizadas
   * @throws {Error} - Com mensagem específica para cada tipo de erro
   */
  async uploadLogo(file) {
    try {
      if (!file || !file.type?.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem válida');
      }
      
      // Verificação de tamanho no frontend (2MB para logo)
      const MAX_SIZE_MB = 2;
      const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;
      
      if (file.size > MAX_SIZE_BYTES) {
        throw new Error(`A imagem não pode ser maior que ${MAX_SIZE_MB}MB`);
      }
      
      // Validar tipos permitidos para logo
      const allowedTypes = ['image/png', 'image/jpeg', 'image/jpg', 'image/svg+xml'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Formato inválido. Use PNG, JPG ou SVG para o logo.');
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      await axios.post(`${this.#endpoint}/logo`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      const updatedSettings = await this.getStoreBrandAssets();
      return updatedSettings;
    } catch (error) {
      console.error('Erro ao fazer upload do logo:', error);
      
      if (error?.response) {
        if (error.response.status === 413) {
          throw new Error('A imagem é muito grande. O tamanho máximo permitido é 2MB.');
        } else if (error.response.data?.message) {
          throw new Error(error.response.data.message);
        } else if (error.response.status === 400) {
          throw new Error('Formato de imagem inválido ou dados corrompidos.');
        } else if (error.response.status === 500) {
          throw new Error('Erro no servidor ao processar a imagem. Tente novamente mais tarde.');
        }
      } else if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        throw new Error('Erro de conexão com o servidor. Verifique sua conexão de internet ou tente novamente mais tarde.');
      }
      
      throw error;
    }
  }
  
  /**
   * Faz o upload de um favicon
   * @param {File} file - Arquivo de favicon para upload
   * @returns {Promise<Object>} - Configurações atualizadas
   * @throws {Error} - Com mensagem específica para cada tipo de erro
   */
  async uploadFavicon(file) {
    try {
      if (!file) {
        throw new Error('Nenhum arquivo selecionado');
      }
      
      // Verificação de tamanho no frontend (1MB para favicon)
      const MAX_SIZE_MB = 1;
      const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;
      
      if (file.size > MAX_SIZE_BYTES) {
        throw new Error(`O favicon não pode ser maior que ${MAX_SIZE_MB}MB`);
      }
      
      // Validar tipos permitidos para favicon
      const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/jpeg', 'image/jpg'];
      if (!allowedTypes.includes(file.type)) {
        throw new Error('Formato inválido. Use ICO, PNG ou JPG para o favicon.');
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      await axios.post(`${this.#endpoint}/favicon`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      const updatedSettings = await this.getStoreBrandAssets();
      return updatedSettings;
    } catch (error) {
      console.error('Erro ao fazer upload do favicon:', error);
      
      if (error?.response) {
        if (error.response.status === 413) {
          throw new Error('O favicon é muito grande. O tamanho máximo permitido é 1MB.');
        } else if (error.response.data?.message) {
          throw new Error(error.response.data.message);
        } else if (error.response.status === 400) {
          throw new Error('Formato de favicon inválido ou dados corrompidos.');
        } else if (error.response.status === 500) {
          throw new Error('Erro no servidor ao processar o favicon. Tente novamente mais tarde.');
        }
      } else if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        throw new Error('Erro de conexão com o servidor. Verifique sua conexão de internet ou tente novamente mais tarde.');
      }
      
      throw error;
    }
  }
  
  /**
   * Remove o logo
   * @returns {Promise<void>}
   */
  async deleteLogo() {
    try {
      await axios.delete(`${this.#endpoint}/logo`);
    } catch (error) {
      console.error('Erro ao remover o logo:', error);
      if (error.response && error.response.status === 404) {
        return; // Logo já não existe
      }
      throw error;
    }
  }
  
  /**
   * Remove o favicon
   * @returns {Promise<void>}
   */
  async deleteFavicon() {
    try {
      await axios.delete(`${this.#endpoint}/favicon`);
    } catch (error) {
      console.error('Erro ao remover o favicon:', error);
      if (error.response && error.response.status === 404) {
        return; // Favicon já não existe
      }
      throw error;
    }
  }
}

export default new StoreBrandAssetsService(); 