<template>
  <div ref="container" :style="containerStyle"></div>
</template>

<script setup>
import { ref, watch, onMounted, onBeforeUnmount, computed } from 'vue'
import { useTheme } from '@/composables/useTheme'

function cleanHtml(html) {
  if (!html) return '';
  
  let cleaned = String(html)
    .replace(/&nbsp;/gi, ' ')
    
  return cleaned;
}

function formatHtml(html) {
  if (!html) return '';
  
  try {
    let formattedHtml = cleanHtml(html);
    
    if (monaco && monaco.editor) {
      const model = monaco.editor.createModel(formattedHtml, 'html');
      
      try {
        if (monaco.editor.getEditors().length > 0) {
          const action = monaco.editor.getEditors()[0].getAction('editor.action.formatDocument');
          if (action) {
            action.run();
            formattedHtml = model.getValue();
          }
        }
      } catch (formatError) {
        console.warn('Erro ao formatar com ação:', formatError);
      }
      
      model.dispose();
    }
    
    return formattedHtml;
  } catch (error) {
    console.warn('Erro na formatação HTML:', error);
    return cleanHtml(html);
  }
}

const props = defineProps({
  modelValue: { type: String, default: '' },
  language: { type: String, default: 'html' },
  height: { type: [String, Number], default: '350px' }
})
const emit = defineEmits(['update:modelValue'])

const { currentTheme } = useTheme()

const container = ref(null)
let editor = null
const containerStyle = computed(() => ({ 
  height: typeof props.height === 'number' ? props.height + 'px' : props.height 
}))

// Lista de temas escuros
const darkThemes = ['dark', 'dracula', 'night', 'dim', 'abyss']

// Computed para determinar se o tema atual é escuro
const isDarkTheme = computed(() => {
  const themeId = currentTheme.value?.id?.toLowerCase() || ''
  return darkThemes.includes(themeId)
})

// Computed para o tema do Monaco
const monacoTheme = computed(() => {
  const theme = isDarkTheme.value ? 'vs-dark' : 'vs'
  return theme
})

let monaco = null

window.MonacoEnvironment = {
  getWorkerUrl: function (moduleId, label) {
    const code = `
      self.MonacoEnvironment = { baseUrl: '.' };
      self.onmessage = function(e) { self.postMessage({ type: 'response' }); };
    `;
    const blob = new Blob([code], { type: 'application/javascript' });
    return URL.createObjectURL(blob);
  }
};

async function loadMonaco() {
  if (!monaco) {
    monaco = await import('monaco-editor')
    
    monaco.languages.registerDocumentFormattingEditProvider('html', {
      provideDocumentFormattingEdits: function(model) {
        let text = cleanHtml(model.getValue())
          .replace(/\n\s*\n/g, '\n')
          .trim();
        
        if (!text) return [];

        const indent = ' '.repeat(2);
        const linebreak = '\n';
        
        let formatted = '';
        let indent_level = 0;
        let in_tag = false;
        let in_content = false;
        let skip_formatting = false;
        let pre_tag_level = -1;
        
        text = text
          .replace(/\r\n/g, '\n')
          .replace(/\r/g, '\n')
          .replace(/\n+/g, '\n')
          .replace(/\n\s+/g, '\n');
        
        for (let i = 0; i < text.length; i++) {
          let c = text.charAt(i);
          
          if (c === '<') {
            const nextFew = text.substring(i, i+5).toLowerCase();
            if (nextFew.startsWith('<pre') || nextFew.startsWith('<code')) {
              skip_formatting = true;
              pre_tag_level = indent_level;
            } else if (nextFew.startsWith('</pre') || nextFew.startsWith('</code')) {
              skip_formatting = false;
            }
          }
          
          if (c === '<' && text.substring(i, i+4) !== '<!--' && !skip_formatting) {
            in_tag = true;
            if (text.charAt(i+1) === '/') {
              indent_level = Math.max(0, indent_level - 1);
            }
            if (in_content) {
              formatted += linebreak;
              for (let j = 0; j < indent_level; j++) formatted += indent;
            }
            in_content = false;
          } else if (c === '>' && in_tag && !skip_formatting) {
            in_tag = false;
            formatted += c;
            
            const is_self_closing = text.charAt(i-1) === '/' || text.substring(i-2, i).match(/\/>/) ||
                                  text.substring(i-3, i) === '-->' || text.substring(i-2, i) === '?>'
                                  
            const next_is_tag = text.charAt(i+1) === '<';
            const next_is_closing = next_is_tag && text.charAt(i+2) === '/';
            
            if (!is_self_closing) {
              if (!next_is_tag || next_is_closing) {
                formatted += linebreak;
                if (!next_is_tag) {
                  in_content = true;
                  for (let j = 0; j < indent_level; j++) formatted += indent;
                }
              }
              
              if (!next_is_tag) {
                indent_level++;
              }
            }
            continue;
          } else if (c === '\n' && !in_tag && !in_content) {
            continue;
          }
          
          formatted += c;
        }
        
        formatted = cleanHtml(formatted);
        
        return [{
          range: model.getFullModelRange(),
          text: formatted
        }];
      }
    });
  }
}

onMounted(async () => {
  try {
    await loadMonaco()
    
    if (monaco.languages && monaco.languages.html) {
      monaco.languages.html.htmlDefaults.setOptions({
        format: {
          tabSize: 2,
          insertSpaces: true,
          wrapLineLength: 120,
          unformatted: 'code,pre',
          contentUnformatted: 'pre,code,textarea',
          indentInnerHtml: true,
          preserveNewLines: true,
          maxPreserveNewLines: 2,
          indentHandlebars: false,
          endWithNewline: false,
          extraLiners: 'head, body, /html',
          wrapAttributes: 'auto',
          templating: ['auto'],
          unformattedContentDelimiter: '',
        }
      });
    }
    
    if (monaco.languages && monaco.languages.typescript) {
      monaco.languages.typescript.javascriptDefaults.setDiagnosticsOptions({ 
        noSemanticValidation: true, 
        noSyntaxValidation: true 
      });
    }
    
    const defaultOptions = {
      value: props.modelValue,
      language: props.language,
      fontSize: 15,
      automaticLayout: true,
      padding: { top: 8 },
      theme: monacoTheme.value,
      lineNumbersMinChars: 2,
      glyphMargin: false,
      wordWrap: 'on',
      minimap: { enabled: true },
      scrollBeyondLastLine: false,
      roundedSelection: true,
      autoClosingBrackets: 'always',
      matchBrackets: 'always',
      tabSize: 2,
      renderWhitespace: 'boundary',
      quickSuggestions: true,
      suggestOnTriggerCharacters: true,
      formatOnPaste: true,
      formatOnType: true
    };

    let initialFormattedValue = '';
    if (props.modelValue) {
      try {
        initialFormattedValue = cleanHtml(props.modelValue);
      } catch (error) {
        console.warn('Erro ao formatar valor inicial:', error);
        initialFormattedValue = props.modelValue || '';
      }
    }

    editor = monaco.editor.create(container.value, {
      ...defaultOptions,
      value: initialFormattedValue
    });
    
    // Forçar aplicação do tema após criação do editor
    setTimeout(() => {
      if (editor && monaco) {
        try {
          monaco.editor.setTheme(monacoTheme.value);
        } catch (error) {
          console.error('Monaco Editor - Erro ao forçar tema:', error)
        }
      }
    }, 100);
    
    let initialFormatDone = false;
    
    setTimeout(() => {
      if (!initialFormatDone) {
        try {
          const currentValue = editor.getValue();
          const cleanedValue = cleanHtml(currentValue);
          
          if (cleanedValue !== currentValue) {
            editor.setValue(cleanedValue);
            
            const action = editor.getAction('editor.action.formatDocument');
            if (action) {
              action.run();
            }
          }
          initialFormatDone = true;
        } catch (error) {
          console.warn('Erro ao aplicar formatação inicial:', error);
        }
      }
    }, 300);

    editor.onDidChangeModelContent(() => {  
      emit('update:modelValue', editor.getValue());
    });

    // Watcher para mudanças no tema
    watch(
      () => monacoTheme.value,
      (newTheme, oldTheme) => {
        if (editor && monaco) {
          try {
            monaco.editor.setTheme(newTheme); 
          } catch (error) {
            console.error('Monaco Editor - Erro ao aplicar tema:', error)
          }
        }
      },
      { immediate: true }
    );

    // Watcher para mudanças no modelValue
    watch(
      () => props.modelValue,
      (val) => {
        if (editor && editor.getValue() !== val) {
          const cleanedVal = val ? cleanHtml(val) : '';
          editor.setValue(cleanedVal);
        }
      }
    )
  } catch (error) {
    console.error('Erro ao inicializar o Monaco Editor:', error)
  }
})

onBeforeUnmount(() => {
  if (editor) {
    editor.dispose()
    editor = null
  }
})
</script>

<style scoped>
</style>
