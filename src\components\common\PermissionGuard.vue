<template>
  <div v-if="hasAccess">
    <slot />
  </div>
  <div v-else-if="showFallback && $slots.fallback">
    <slot name="fallback" />
  </div>
  <div v-else-if="showFallback && fallbackMessage" class="permission-denied-message">
    <div class="text-center py-8">
      <HugeiconsIcon :icon="SecurityCheckIcon" size="48" class="mx-auto mb-4 text-gray-400" />
      <p class="text-gray-600">{{ fallbackMessage }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useGlobalPermissions } from '@/composables/usePermissions';
import { HugeiconsIcon } from '@hugeicons/vue';
import { SecurityCheckIcon } from '@hugeicons-pro/core-stroke-standard';

/**
 * Componente para proteger seções da UI baseado em permissões
 * 
 * Exemplos de uso:
 * 
 * <!-- Permissão única -->
 * <PermissionGuard :required-permissions="['product.create']">
 *   <IluriaButton>Criar Produto</IluriaButton>
 * </PermissionGuard>
 * 
 * <!-- Múltiplas permissões (AND por padrão) -->
 * <PermissionGuard :required-permissions="['product.edit', 'product.view']">
 *   <ProductEditForm />
 * </PermissionGuard>
 * 
 * <!-- Qualquer permissão (OR) -->
 * <PermissionGuard :required-permissions="['order.view', 'customer.view']" any-permission>
 *   <DashboardStats />
 * </PermissionGuard>
 * 
 * <!-- Com fallback customizado -->
 * <PermissionGuard :required-permissions="['team.edit']" show-fallback>
 *   <TeamManagement />
 *   <template #fallback>
 *     <p>Você não tem permissão para gerenciar a equipe.</p>
 *   </template>
 * </PermissionGuard>
 */

const props = defineProps({
  /**
   * Permissões necessárias para exibir o conteúdo
   * Pode ser string única ou array de strings
   */
  requiredPermissions: {
    type: [String, Array],
    required: true
  },
  
  /**
   * Se true, usa operação OR (qualquer permissão)
   * Se false, usa operação AND (todas as permissões)
   */
  anyPermission: {
    type: Boolean,
    default: false
  },
  
  /**
   * Se deve exibir fallback quando não tem permissão
   */
  showFallback: {
    type: Boolean,
    default: false
  },
  
  /**
   * Mensagem padrão quando não tem permissão (se não usar slot fallback)
   */
  fallbackMessage: {
    type: String,
    default: 'Você não tem permissão para acessar este conteúdo.'
  },
  
  /**
   * Se deve ignorar o estado de carregamento das permissões
   * Por padrão, aguarda carregar as permissões antes de decidir
   */
  ignoreLoading: {
    type: Boolean,
    default: false
  }
});

const { hasPermission, hasAllPermissions, hasAnyPermission, permissionsLoaded } = useGlobalPermissions();

/**
 * Normaliza as permissões para array
 */
const normalizedPermissions = computed(() => {
  if (!props.requiredPermissions) return [];
  
  return Array.isArray(props.requiredPermissions) 
    ? props.requiredPermissions 
    : [props.requiredPermissions];
});

/**
 * Verifica se o usuário tem acesso baseado nas permissões
 */
const hasAccess = computed(() => {
  // Se ainda está carregando permissões e não deve ignorar loading, não exibe
  if (!props.ignoreLoading && !permissionsLoaded.value) {
    return false;
  }
  
  const permissions = normalizedPermissions.value;
  
  // Se não há permissões especificadas, permite acesso
  if (permissions.length === 0) {
    return true;
  }
  
  // Aplica lógica AND ou OR conforme configurado
  if (props.anyPermission) {
    return hasAnyPermission(permissions);
  } else {
    return hasAllPermissions(permissions);
  }
});

// Expõe informações para debugging
defineExpose({
  hasAccess,
  requiredPermissions: normalizedPermissions,
  permissionsLoaded
});
</script>

<style scoped>
.permission-denied-message {
  border-radius: 0.5rem;
  border: 1px solid #e5e7eb;
  background-color: #f9fafb;
  padding: 1rem;
}
</style>