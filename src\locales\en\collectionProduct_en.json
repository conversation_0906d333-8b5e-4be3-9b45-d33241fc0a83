{"list": {"title": "Product Collections", "subtitle": "Manage your product collections", "collections": "Collections", "collectionsSubtitle": "List of all your collections", "new": "Create collection", "name": "Name", "productsLength": "Products", "condition": "Condition", "noCollections": "No collections found", "noCollectionsFound": "No collections found", "noCollectionsDescription": "Start by creating your first collection", "loadError": "Error loading collections", "deleteSuccess": "Collection deleted successfully", "deleteError": "Error deleting collection", "confirmDeleteMessage": "Are you sure you want to delete this collection: "}, "form": {"title": "Collection manager", "editTitle": "Edit collection", "subtitle": "Configure your collection data", "basicData": "Basic data", "basicDataSubtitle": "Basic collection information", "name": "Collection name", "description": "Description", "type": "Type of collection", "manual": "Manual", "manualDescription": "Add products to this collection manually", "smart": "Intelligent", "smartDescription": "Products that meet certain conditions defined by you will enter this collection", "saveError": "Error saving collection", "saveSuccess": "Collection saved successfully", "loadError": "Error loading collection", "seo": {"title": "Search engine listing", "previewTitle": "Page title (preview)"}}}