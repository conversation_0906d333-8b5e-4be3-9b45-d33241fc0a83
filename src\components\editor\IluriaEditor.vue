<template>
  <div class="iluria-editor-wrapper">
    <div v-if="label" class="mb-2">
      <label :for="id" class="editor-label">{{ label }}</label>
    </div>

    <!-- Editor Visual (Quill) -->
    <div class="visual-editor-container">
      <Editor
        :id="id"
        v-model="content"
        :editor-style="editorStyle"
        :placeholder="placeholder"
        class="themed-editor"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import Editor from 'primevue/editor'

const props = defineProps({
  id: { type: String, default: 'iluria-editor' },
  label: { type: String, default: '' },
  placeholder: { type: String, default: '' },
  height: { type: [String, Number], default: '350px' }
})

const content = defineModel({
  type: String,
  default: ''
})

const editorStyle = computed(() => ({
  height: typeof props.height === 'number' ? props.height + 'px' : props.height
}))
</script>

<style scoped>
.iluria-editor-wrapper {
  /* Remove background to make wrapper transparent */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.editor-label {
  color: var(--iluria-color-text);
  font-size: 0.875rem;
  font-weight: 500;
  transition: color 0.3s ease;
}



.visual-editor-container {
  border-radius: 8px;
  overflow: hidden;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.visual-editor-container:focus-within {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring);
}



/* Estilos do Editor Visual (Quill) */
:deep(.themed-editor) {
  background: var(--iluria-color-surface) !important;
  border: none !important;
  border-radius: 8px !important;
  color: var(--iluria-color-text) !important;
}

:deep(.themed-editor .ql-toolbar) {
  background: var(--iluria-color-surface) !important;
  border: none !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text) !important;
}

:deep(.themed-editor .ql-toolbar .ql-stroke) {
  stroke: var(--iluria-color-text) !important;
}

:deep(.themed-editor .ql-toolbar .ql-fill) {
  fill: var(--iluria-color-text) !important;
}

:deep(.themed-editor .ql-toolbar .ql-picker-label) {
  color: var(--iluria-color-text) !important;
}

:deep(.themed-editor .ql-toolbar .ql-picker-options) {
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  border-radius: 6px !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

:deep(.themed-editor .ql-toolbar .ql-picker-item) {
  color: var(--iluria-color-text) !important;
}

:deep(.themed-editor .ql-toolbar .ql-picker-item:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.themed-editor .ql-container) {
  background: var(--iluria-color-surface) !important;
  border: none !important;
  color: var(--iluria-color-text) !important;
  font-family: inherit !important;
}

:deep(.themed-editor .ql-editor) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  padding: 16px !important;
  min-height: 200px !important;
}

:deep(.themed-editor .ql-editor.ql-blank::before) {
  color: var(--iluria-color-input-placeholder) !important;
  font-style: italic !important;
}

:deep(.themed-editor .ql-editor:focus) {
  outline: none !important;
}

/* Botões da toolbar ativos */
:deep(.themed-editor .ql-toolbar button:hover) {
  background: var(--iluria-color-hover) !important;
  border-radius: 4px !important;
}

:deep(.themed-editor .ql-toolbar button.ql-active) {
  background: var(--iluria-color-primary) !important;
  color: var(--iluria-color-primary-contrast) !important;
  border-radius: 4px !important;
}

:deep(.themed-editor .ql-toolbar button.ql-active .ql-stroke) {
  stroke: var(--iluria-color-primary-contrast) !important;
}

:deep(.themed-editor .ql-toolbar button.ql-active .ql-fill) {
  fill: var(--iluria-color-primary-contrast) !important;
}



/* Tooltip customizations */
:deep(.ql-tooltip) {
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  border-radius: 6px !important;
  color: var(--iluria-color-text) !important;
  box-shadow: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05) !important;
}

:deep(.ql-tooltip input[type=text]) {
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text) !important;
  border-radius: 4px !important;
  padding: 6px 8px !important;
}

:deep(.ql-tooltip input[type=text]:focus) {
  border-color: var(--iluria-color-primary) !important;
  outline: none !important;
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
}

:deep(.ql-tooltip a.ql-action) {
  color: var(--iluria-color-primary) !important;
}

:deep(.ql-tooltip a.ql-action:hover) {
  color: var(--iluria-color-primary-hover) !important;
}

/* Tema escuro específico */
.theme-dark :deep(.themed-editor .ql-snow.ql-toolbar button:hover .ql-stroke) {
  stroke: var(--iluria-color-text) !important;
}

.theme-dark :deep(.themed-editor .ql-snow.ql-toolbar button:hover .ql-fill) {
  fill: var(--iluria-color-text) !important;
}

/* Responsividade */
@media (max-width: 768px) {
  :deep(.themed-editor .ql-toolbar) {
    padding: 8px !important;
  }
  
  :deep(.themed-editor .ql-toolbar .ql-formats) {
    margin-right: 8px !important;
  }
  
  :deep(.themed-editor .ql-editor) {
    padding: 12px !important;
    min-height: 150px !important;
  }
}
</style>
