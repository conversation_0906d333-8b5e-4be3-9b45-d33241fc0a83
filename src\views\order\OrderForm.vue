<template>
  <div class="order-form-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="isViewMode ? t('order.viewOrder') : isEditMode ? t('order.editOrder') : t('order.newOrder')"
      :subtitle="isViewMode ? t('order.viewOrderSubtitle') : isEditMode ? t('order.updateOrderSubtitle') : t('order.createOrderSubtitle')"
      :customButtons="getHeaderButtons()"
      @custom-click="handleCustomButtonClick"
    />

    <Form @submit="saveOrder">
      <div class="form-layout">
        <!-- Main Form Section -->
        <div class="form-content">
          <!-- Customer Information -->
          <ViewContainer 
            :title="t('order.customerDataTitle')"
            :subtitle="t('order.customerDataSubtitle')"
            :icon="UserMultiple02Icon"
            iconColor="blue"
          >
            <div class="customer-section">
              <CustomerSelect
                v-model="selectedCustomer"
                :label="t('order.selectCustomer')"
                :placeholder="t('order.searchCustomer')"
                @select="onCustomerSelect"
              />
            </div>
          </ViewContainer>

          <!-- Shipping Address -->
          <ViewContainer 
            :title="t('order.shippingAddress')"
            :subtitle="t('order.shippingAddressSubtitle')"
            :icon="Location01Icon"
            iconColor="green"
          >
            <div class="address-section">
              <div v-if="!selectedCustomer" class="no-customer-selected">
                <div class="no-customer-icon">
                  <HugeiconsIcon :icon="UserMultiple02Icon" class="w-8 h-8" />
                </div>
                <p class="no-customer-text">{{ t('order.selectCustomerFirst') }}</p>
              </div>
              
              <div v-if="selectedCustomer" class="mb-4">
                <AddressCustomerSelect
                  v-model="selectedAddress"
                  :customerId="selectedCustomer?.id"
                  :storeId="1"
                  :label="t('order.selectAddress')"
                  :placeholder="t('order.searchAddress')"
                  @select="onAddressSelect"
                />
              </div>

              <div v-if="hasShippingAddress && !showAddressForm" class="address-display">
                <div class="address-card">
                  <div class="address-info">
                    <p class="address-text">{{ formatFullAddress }}</p>
                    <p class="address-zipcode">{{ orderForm.addressZipCode }}</p>
                  </div>
                  <div class="address-actions">
                    <IluriaButton 
                      size="small"
                      color="ghost"
                      :hugeIcon="Delete01Icon"
                      @click="removeSelectedAddress"
                      :title="t('remove')"
                    />
                    <IluriaButton 
                      size="small"
                      color="ghost"
                      :hugeIcon="PencilEdit01Icon"
                      @click="showAddressForm = !showAddressForm"
                      :title="t('edit')"
                    />
                  </div>
                </div>
              </div>

              <div v-if="!hasShippingAddress && !showAddressForm && selectedCustomer" class="add-address">
                <IluriaButton 
                  color="primary"
                  variant="outline"
                  :hugeIcon="PlusSignIcon"
                  @click="showAddressForm = true"
                >
                  {{ t('order.addNewAddress') }}
                </IluriaButton>
              </div>

              <AddressCustomerForm
                v-if="showAddressForm"
                :address="computedAddressObject"
                @update:address="updateAddressFields"
                @cancel="showAddressForm = false"
              />
            </div>
          </ViewContainer>

          <!-- Products -->
          <ViewContainer 
            :title="t('order.productsTitle')"
            :subtitle="t('order.productsSubtitle')"
            :icon="ShoppingBasket01Icon"
            iconColor="orange"
          >
            <div class="products-section">
              <!-- Empty State -->
              <div v-if="orderForm.items.length === 0" class="products-empty">
                <div class="empty-icon">
                  <HugeiconsIcon :icon="ShoppingBasket01Icon" class="w-12 h-12" />
                </div>
                <h3 class="empty-title">{{ t('order.noProductsAdded') }}</h3>
                <p class="empty-description">{{ t('order.noProductsDescription') }}</p>
                <div class="empty-actions">
                  <IluriaButton 
                    color="primary"
                    :hugeIcon="PlusSignIcon"
                    @click="openProductSelectorModal"
                  >
                    {{ t('order.addProduct') }}
                  </IluriaButton>
                </div>
              </div>

              <!-- Products List -->
              <div v-else class="products-list">
                <div class="products-header">
                  <IluriaButton 
                    color="primary"
                    :hugeIcon="PlusSignIcon"
                    @click="openProductSelectorModal"
                  >
                    {{ t('order.addProduct') }}
                  </IluriaButton>
                </div>
                <div v-for="(item, index) in orderForm.items" :key="index" class="product-item">
                  <div class="product-header">
                    <span class="product-number">{{ t('product.product') }} #{{ index + 1 }}</span>
                    <IluriaButton 
                      size="small"
                      color="ghost"
                      :hugeIcon="Delete01Icon"
                      @click="removeOrderItem(index)"
                      :title="t('delete')"
                    />
                  </div>
                  
                  <div class="product-content">
                    <div class="product-info">
                      <IluriaInputText
                        :id="`item-name-${index}`"
                        v-model="item.name"
                        :label="t('product.name')"
                        disabled
                        class="product-name-input"
                      />
                      <div v-if="item.variation" class="product-variation">
                        <span v-for="(value, key) in item.variation.attributes" :key="key" class="variation-item">
                          <span class="variation-key">{{ key }}:</span> 
                          <span class="variation-value">{{ value }}</span>
                        </span>
                      </div>
                    </div>
                    
                    <div class="product-quantity">
                      <IluriaInputText
                        type="number"
                        :id="`item-quantity-${index}`"
                        v-model="item.quantity"
                        :label="t('product.quantity')"
                        :min="1"
                        :max="999"
                        :step="1"
                        @update:model-value="(value) => updateItemQuantity(index, value)"
                      />
                      <div class="quantity-info">
                        <p class="unit-price">{{ t('product.unitPrice') }}: R$ {{ formatCurrency(item.price) }}</p>
                        <p v-if="item.quantity > item.stockQuantity" class="stock-warning">
                          <HugeiconsIcon :icon="Alert02Icon" class="w-4 h-4" />
                          {{ t('order.exceedingStock', { available: item.stockQuantity - item.quantity }) }}
                        </p>
                        <p v-else class="stock-info">
                          {{ t('product.availableStock') }}: {{ item.stockQuantity || 0 }} unidades
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Product Selection Modal -->
              <ProductSelectorModal
                :visible="showProductSelectorModal"
                @update:visible="showProductSelectorModal = $event"
                @add-products="addMultipleOrderItems"
              />
            </div>
          </ViewContainer>

          <!-- Shipping -->
          <ViewContainer 
            :title="t('order.shippingTitle')"
            :subtitle="t('order.shippingSubtitle')"
            :icon="TruckDeliveryIcon"
            iconColor="purple"
          >
            <ShippingSelector v-model="orderForm" @update:modelValue="updateOrderSummary" />
          </ViewContainer>

          <!-- Discount -->
          <ViewContainer 
            :title="t('order.discountTitle')"
            :subtitle="t('order.discountSubtitle')"
            :icon="DiscountIcon"
            iconColor="pink"
          >
            <div class="discount-section">
              <div class="discount-grid">
                <IluriaInputText
                  id="productsDiscountValue"
                  v-model="orderForm.productsDiscountValue"
                  type="money"
                  prefix="R$"
                  :label="t('order.productsDiscountValue')"
                  placeholder="29,99"
                  @update:modelValue="updateOrderSummary"
                />
                <IluriaInputText
                  id="productsDiscountDescription"
                  v-model="orderForm.productsDiscountDescription"
                  :label="t('order.productsDiscountDescription')"
                  :placeholder="t('order.productsDiscountDescriptionPlaceholder')"
                  @update:modelValue="updateOrderSummary"
                />
              </div>
            </div>
          </ViewContainer>

          <!-- Notes -->
          <ViewContainer 
            :title="t('order.notesTitle')"
            :subtitle="t('order.notesSubtitle')"
            :icon="Note01Icon"
            iconColor="indigo"
          >
            <div class="notes-section">
              <IluriaInputText
                id="annotation"
                v-model="orderForm.annotation"
                :label="t('order.annotation')"
                :placeholder="t('order.annotationPlaceholder')"
                @update:modelValue="updateOrderSummary"
              />
            </div>
          </ViewContainer>

          <!-- Order Status -->
          <ViewContainer 
            :title="t('order.orderStatusTitle')"
            :subtitle="t('order.orderStatusSubtitle')"
            :icon="Settings02Icon"
            iconColor="gray"
          >
            <div class="status-section">
              <div class="status-grid">
                <IluriaSelect
                  id="shippingStatus"
                  v-model="orderForm.shippingStatus"
                  :options="shippingStatusOptions"
                  :label="t('order.shippingStatus')"
                  :placeholder="t('order.selectStatus')"
                  @update:modelValue="updateOrderSummary"
                />
                <IluriaSelect
                  id="paymentStatus"
                  v-model="orderForm.paymentStatus"
                  :options="paymentStatusOptions"
                  :label="t('order.paymentStatus')"
                  :placeholder="t('order.selectStatus')"
                  @update:modelValue="updateOrderSummary"
                />
              </div>
              <div class="source-device">
                <IluriaSelect
                  id="sourceDevice"
                  v-model="orderForm.sourceDevice"
                  :options="[{label: 'Web', value: 'WEB'}, {label: 'Mobile', value: 'MOBILE'}, {label: 'Admin', value: 'ADMIN'}]"
                  :label="t('order.sourceDevice')"
                  @update:modelValue="updateOrderSummary"
                />
              </div>
            </div>
          </ViewContainer>
        </div>

        <!-- Order Summary Sidebar -->
        <div class="summary-sidebar">
          <OrderSummary :order-form="orderForm" />
        </div>
      </div>
    </Form>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from 'primevue/usetoast'
import { Form } from 'vee-validate'
import { OrderService } from '@/services/order.service'
import { useStatus } from '@/utils/statusHelper'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import CustomerSelect from '@/components/customer/CustomerSelect.vue'
import AddressCustomerSelect from '@/components/customer/AddressCustomerSelect.vue'
import ProductSelectorModal from '@/components/products/ProductSelectorModal.vue'
import ShippingSelector from '@/components/order/ShippingSelector.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'

import AddressCustomerForm from '@/components/customer/AddressCustomerForm.vue'
import OrderSummary from '@/components/order/OrderSummary.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserMultiple02Icon,
  Location01Icon,
  ShoppingBasket01Icon,
  TruckDeliveryIcon,
  DiscountIcon,
  Note01Icon,
  Settings02Icon,
  ArrowLeft01Icon,
  CheckmarkCircle02Icon,
  PlusSignIcon,
  Delete01Icon,
  PencilEdit01Icon,
  Alert02Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const route = useRoute()
const router = useRouter()
const toast = useToast()
const loading = ref(false)
const isEditMode = computed(() => !!route.params.id && route.name === 'order-edit')
const isViewMode = computed(() => !!route.params.id && route.name === 'order-view')
const { getStatusClass, getStatusLabel } = useStatus()
const STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  PAID: 'PAID',
  UNPAID: 'UNPAID'
}
const shippingStatusOptions = [
  { label: t('order.pending'), value: STATUS.PENDING },
  { label: t('order.processing'), value: STATUS.PROCESSING },
  { label: t('order.shipped'), value: STATUS.SHIPPED },
  { label: t('order.delivered'), value: STATUS.DELIVERED },
  { label: t('order.cancelled'), value: STATUS.CANCELLED }
]
const paymentStatusOptions = [
  { label: t('order.pending'), value: STATUS.PENDING },
  { label: t('order.paid'), value: STATUS.PAID },
  { label: t('order.unpaid'), value: STATUS.UNPAID },
  { label: t('order.cancelled'), value: STATUS.CANCELLED }
]

const orderForm = ref({
  shippingStatus: STATUS.PENDING,
  paymentStatus: STATUS.PENDING,
  items: [],
  addressCountry: 'Brasil',
  sourceDevice: 'WEB'
})

const selectedCustomer = ref(null)
const selectedAddress = ref(null)
const showAddressForm = ref(false)
function onCustomerSelect(customer) {
  if (customer) {
    selectedCustomer.value = customer
    orderForm.value.customerId = customer.id
    orderForm.value.customerName = customer.name
    orderForm.value.customerEmail = customer.email
    orderForm.value.customerPhone = customer.phone || ''
    orderForm.value.customerDocument = customer.document || ''
    orderForm.value.receiverName = customer.name
    updateOrderSummary()
  }
}
function onAddressSelect(address) {
  if (address) {
    selectedAddress.value = address
    orderForm.value.addressStreet = address.street
    orderForm.value.addressNumber = address.number
    orderForm.value.addressComplement = address.complement || ''
    orderForm.value.addressDistrict = address.district
    orderForm.value.addressCity = address.city
    orderForm.value.addressState = address.state
    orderForm.value.addressZipCode = address.zipCode
    orderForm.value.addressCountry = address.country || 'Brasil'
    showAddressForm.value = false
    updateOrderSummary()
  }
}
function removeSelectedAddress() {
  selectedAddress.value = null
  orderForm.value.addressStreet = ''
  orderForm.value.addressNumber = ''
  orderForm.value.addressComplement = ''
  orderForm.value.addressDistrict = ''
  orderForm.value.addressCity = ''
  orderForm.value.addressState = ''
  orderForm.value.addressZipCode = ''
  orderForm.value.addressCountry = 'Brasil'
  updateOrderSummary()
}
const hasShippingAddress = computed(() => {
  return !!(
    orderForm.value.addressStreet?.trim() &&
    orderForm.value.addressNumber?.trim() &&
    orderForm.value.addressCity?.trim() &&
    orderForm.value.addressState?.trim()
  )
})
const formatFullAddress = computed(() => {
  let fullAddress = `${orderForm.value.addressStreet}, ${orderForm.value.addressNumber}`
  
  if (orderForm.value.addressComplement) {
    fullAddress += ` - ${orderForm.value.addressComplement}`
  }
  
  fullAddress += `
${orderForm.value.addressDistrict}, ${orderForm.value.addressCity}/${orderForm.value.addressState}`
  
  if (orderForm.value.addressCountry && orderForm.value.addressCountry !== 'Brasil') {
    fullAddress += ` - ${orderForm.value.addressCountry}`
  }
  
  return fullAddress
})

const computedAddressObject = computed(() => ({
  street: orderForm.value.addressStreet || '',
  number: orderForm.value.addressNumber || '',
  complement: orderForm.value.addressComplement || '',
  district: orderForm.value.addressDistrict || '',
  city: orderForm.value.addressCity || '',
  state: orderForm.value.addressState || '',
  zipCode: orderForm.value.addressZipCode || '',
  country: orderForm.value.addressCountry || 'Brasil'
}));

function updateAddressFields(addressObj) {
  orderForm.value.addressStreet = addressObj.street || ''
  orderForm.value.addressNumber = addressObj.number || ''
  orderForm.value.addressComplement = addressObj.complement || ''
  orderForm.value.addressDistrict = addressObj.district || ''
  orderForm.value.addressCity = addressObj.city || ''
  orderForm.value.addressState = addressObj.state || ''
  orderForm.value.addressZipCode = addressObj.zipCode || ''
  orderForm.value.addressCountry = addressObj.country || 'Brasil'
  updateOrderSummary()
}
const showProductSelectorModal = ref(false);
function openProductSelectorModal() {
  showProductSelectorModal.value = true;
}
function addMultipleOrderItems(items) {
  if (!items || !items.length) return
  
  items.forEach(item => {
    orderForm.value.items.push(item)
  })
  
  updateOrderSummary()
  
  toast.add({
    severity: 'success',
    summary: t('success'),
    detail: `${items.length} ${items.length === 1 ? t('product.itemAdded') : t('product.itemsAdded')}`,
    life: 3000
  })
}
function removeOrderItem(index) {
  orderForm.value.items.splice(index, 1)
  updateOrderSummary()
}
const orderSubtotal = computed(() => {
  return orderForm.value.items.reduce((total, item) => {
    return total + (item.price * item.quantity)
  }, 0)
})
const discountValue = computed(() => {
  return orderForm.value.productsDiscountValue || 0
})
const orderTotal = computed(() => {
  const calculatedTotal = orderSubtotal.value - discountValue.value + Number(orderForm.value.shippingValue || 0) - Number(orderForm.value.shippingDiscount || 0)
  return Math.max(0, calculatedTotal)
})
const debugMode = ref(true)

// Header button functions
const getHeaderButtons = () => {
  const buttons = []

  // Botão Cancelar/Voltar
  buttons.push({
    text: t('cancel'),
    color: 'secondary',
    variant: 'outline',
    icon: ArrowLeft01Icon,
    onClick: goBack
  })

  // Botão Salvar (apenas se não estiver em modo visualização)
  if (!isViewMode.value) {
    buttons.push({
      text: isEditMode.value ? t('update') : t('save'),
      color: 'primary',
      icon: CheckmarkCircle02Icon,
      onClick: saveOrder,
      disabled: !selectedCustomer.value || orderForm.value.items.length === 0
    })
  }

  // Botão Editar (apenas em modo visualização)
  if (isViewMode.value) {
    buttons.push({
      text: t('order.editOrder'),
      color: 'primary',
      icon: PencilEdit01Icon,
      onClick: editCurrentOrder
    })
  }

  return buttons
}

const handleCustomButtonClick = (index, button) => {
  if (button.onClick) {
    button.onClick()
  }
}
function updateItemQuantity(index, value) {
  const item = orderForm.value.items[index];
  item.quantity = value;
  
  if (item.quantity > item.stockQuantity) {
  toast.add({
    severity: 'warn',
    summary: t('order.exceedingStock', { available: item.stockQuantity - item.quantity}),
    life: 3000
  });
}
  
  updateOrderSummary();
}
function updateOrderSummary() {
  orderForm.value.productsValue = orderSubtotal.value
}
function prepareOrderData() {
  const result = { 
    ...orderForm.value,
    productsValue: Number(orderForm.value.productsValue) || 0,
    productsDiscountValue: Number(orderForm.value.productsDiscountValue) || 0,
    shippingValue: Number(orderForm.value.shippingValue) || 0,
    shippingDiscount: Number(orderForm.value.shippingDiscount) || 0,
    items: orderForm.value.items.map(item => ({
      ...item,
      price: Number(item.price),
      originalPrice: Number(item.originalPrice || item.price),
      quantity: Number(item.quantity),
      shippingTime: Number(item.shippingTime) || 0
    })),
    paymentMethod: orderForm.value.paymentMethod?.toUpperCase()
  }
  
  return result
}
function formatCurrency(value) {
  return Number(value).toFixed(2).replace('.', ',')
}
function calculateTotal(order) {
  const productsValue = parseFloat(order.productsValue) || 0
  const productsDiscountValue = parseFloat(order.productsDiscountValue) || 0
  const shippingValue = parseFloat(order.shippingValue) || 0
  const shippingDiscount = parseFloat(order.shippingDiscount) || 0
  
  const total = productsValue - productsDiscountValue + shippingValue - shippingDiscount
  return Math.max(0, total)
}

function goBack() {
  router.push({ path: '/orders' })
}

function editCurrentOrder() {
  router.push({ name: 'order-edit', params: { id: route.params.id } })
}
async function saveOrder() {
  try {
    loading.value = true
    
    if (!selectedCustomer.value) {
      toast.add({
        severity: 'error',
        summary: t('error'),
        detail: t('customerInfo.selectClient'),
        life: 3000
      })
      loading.value = false
      return
    }
    
    if (orderForm.value.items.length === 0) {
      toast.add({
        severity: 'error',
        summary: t('error'),
        detail: t('product.noProductsFound'),
        life: 3000
      })
      loading.value = false
      return
    }
    
    const orderData = prepareOrderData()
    if (isEditMode.value) {
      await OrderService.updateOrder(route.params.id, orderData)
      toast.add({
        severity: 'success',
        summary: t('success'),
        detail: t('order.updateSuccess'),
        life: 3000
      })
    } else {
      await OrderService.createOrder(orderData)
      toast.add({
        severity: 'success',
        summary: t('success'),
        detail: t('order.createSuccess'),
        life: 3000
      })
    }
    
    router.push('/orders')
  } catch (error) {
    console.error('Error saving order:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: isEditMode.value ? t('order.updateError') : t('order.createError'),
      life: 3000
    })
  } finally {
    loading.value = false
  }
}
const processOrderItems = (items) => {
  return items.map(item => ({
    ...item,
    variation: item.variationTitle ? {
      attributes: parseVariationTitle(item.variationTitle)
    } : null
  }))
}

const setupCustomerData = (orderData) => {
  selectedCustomer.value = {
    id: orderData.customerId,
    name: orderData.customerName,
    email: orderData.customerEmail,
    phone: orderData.customerPhone,
    document: orderData.customerDocument,
    label: orderData.customerName
  }
}

const setupAddressData = (orderData) => {
  if (!orderData.addressStreet) return
  
  selectedAddress.value = {
    id: 'address-from-order',
    street: orderData.addressStreet,
    number: orderData.addressNumber,
    zipCode: orderData.addressZipCode,
    district: orderData.addressDistrict,
    city: orderData.addressCity,
    state: orderData.addressState,
    country: orderData.addressCountry,
    complement: orderData.addressComplement
  }
}

const loadOrderData = async () => {
  loading.value = true
  try {
    const orderData = await OrderService.getOrder(route.params.id)
    if (!orderData) return
    
    orderForm.value = { ...orderData }
    
    if (orderData.items) {
      orderForm.value.items = processOrderItems(orderData.items)
    }
    
    if (orderData.customerId && orderData.customerName) {
      setupCustomerData(orderData)
      setupAddressData(orderData)
    }
    
    updateOrderSummary()
  } catch (error) {
    console.error('Erro ao carregar pedido:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('order.loadError'),
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

onMounted(async () => {
  if (isEditMode.value) {
    await loadOrderData()
  }
})
const parseVariationTitle = (variationTitle) => {
  if (!variationTitle) return {}
  
  const attributes = {}
  const pairs = variationTitle.split(', ')
  
  pairs.forEach(pair => {
    const [key, value] = pair.split(': ')
    if (key && value) {
      attributes[key.trim()] = value.trim()
    }
  })
  
  return attributes
}
onMounted(() => {
  updateOrderSummary()
})
</script>

<style scoped>
.order-form-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



/* Form Layout */
.form-layout {
  display: flex;
  gap: 24px;
  align-items: flex-start;
}

.form-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 24px;
  max-width: calc(100% - 344px);
}

.summary-sidebar {
  width: 320px;
  flex-shrink: 0;
  position: sticky;
  top: 24px;
}

/* Customer Section */
.customer-section {
  padding: 0;
}

/* Address Section */
.address-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.no-customer-selected {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 32px 16px;
  text-align: center;
  background: var(--iluria-color-container-bg);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.no-customer-icon {
  color: var(--iluria-color-text-muted);
  margin-bottom: 12px;
}

.no-customer-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.address-display {
  margin-bottom: 16px;
}

.address-card {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 16px;
  background: var(--iluria-color-container-bg);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
}

.address-info {
  flex: 1;
}

.address-text {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.address-zipcode {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.address-actions {
  display: flex;
  gap: 8px;
  margin-left: 16px;
}

.add-address {
  margin-top: 16px;
}

/* Products Section */
.products-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.products-header {
  display: flex;
  justify-content: flex-start;
  margin-bottom: 20px;
}

/* Products Empty State */
.products-empty {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 48px 24px;
  text-align: center;
  background: var(--iluria-color-container-bg);
  border-radius: 8px;
  border: 2px dashed var(--iluria-color-border);
}

.empty-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 20px 0;
}

.empty-actions {
  display: flex;
  justify-content: center;
}

/* Products List */
.products-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.product-item {
  background: var(--iluria-color-container-bg);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
  overflow: hidden;
}

.product-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px 16px;
  background: var(--iluria-color-surface);
  border-bottom: 1px solid var(--iluria-color-border);
}

.product-number {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  font-size: 14px;
}

.product-content {
  padding: 16px;
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 20px;
  align-items: start;
}

.product-info {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.product-name-input {
  background: var(--iluria-color-surface) !important;
}

.product-variation {
  display: flex;
  flex-wrap: wrap;
  gap: 12px;
  margin-top: 8px;
}

.variation-item {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
}

.variation-key {
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.variation-value {
  color: var(--iluria-color-text-primary);
  font-weight: 600;
  background: var(--iluria-color-surface);
  padding: 2px 8px;
  border-radius: 4px;
  border: 1px solid var(--iluria-color-border);
}

.product-quantity {
  width: 160px;
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.quantity-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.unit-price {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.stock-warning {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 13px;
  color: var(--iluria-color-error);
  margin: 0;
}

.stock-info {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Discount Section */
.discount-section {
  padding: 0;
}

.discount-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

/* Notes Section */
.notes-section {
  padding: 0;
}

/* Status Section */
.status-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.status-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.source-device {
  width: 100%;
}

/* Dark theme specific overrides */
:deep(.p-datatable) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
}

:deep(.p-datatable-thead > tr > th) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
}

:deep(.p-datatable-tbody > tr) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
}

:deep(.p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-surface) !important;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .form-layout {
    flex-direction: column;
  }
  
  .form-content {
    max-width: 100%;
  }
  
  .summary-sidebar {
    width: 100%;
    position: static;
  }
}

@media (max-width: 768px) {
  .order-form-container {
    padding: 16px;
  }
  

  
  .product-content {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .product-quantity {
    width: 100%;
  }
  
  .discount-grid,
  .status-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .address-card {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .address-actions {
    margin-left: 0;
    justify-content: flex-end;
  }
}

@media (max-width: 480px) {

  
  .product-header {
    flex-direction: column;
    align-items: stretch;
    gap: 8px;
  }
  
  .variation-item {
    font-size: 12px;
  }
}
</style>
