<template>
  <div class="page-settings">
    <!-- Header -->

    <!-- Con<PERSON><PERSON><PERSON> das configurações -->
    <div class="settings-content">
      <SettingsPanel 
        @update:settings="handleSettingsUpdate"
        :inline="true"
      />
    </div>
  </div>
</template>

<script setup>
import { inject } from 'vue'
import { useI18n } from 'vue-i18n'
import SettingsPanel from '@/components/layoutEditor/Common/SettingsPanel.vue'

const { t } = useI18n()
const emit = defineEmits(['settings-updated'])

// Injeta as configurações globais da página
const pageSettings = inject('pageSettings', {
  backgroundColor: '#ffffff',
  contentWidth: 75
})

const handleSettingsUpdate = (settings) => {
  emit('settings-updated', settings)
}
</script>

<style scoped>
.page-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--iluria-color-surface);
}

/* Header */
.settings-header {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  flex-shrink: 0;
}

.settings-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.settings-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

/* Content */
.settings-content {
  flex: 1;
  overflow: auto;
}

/* Overrides para o SettingsPanel dentro da sidebar */
.settings-content :deep(.settings-panel) {
  padding: 0;
  max-height: none;
  border-radius: 0;
  box-shadow: none;
  background: transparent;
}

.settings-content :deep(.settings-title) {
  display: none; /* Esconder título duplicado */
}

.settings-content :deep(.category-switcher) {
  margin: 1rem;
  margin-bottom: 0;
}

.settings-content :deep(.category-content) {
  padding: 1rem;
}

.settings-content :deep(.form-actions) {
  padding: 1rem;
  border-top: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  margin-top: auto;
}

/* Responsividade */
@media (max-width: 768px) {
  .settings-header {
    padding: 0.75rem;
  }
  
  .settings-content :deep(.category-switcher) {
    margin: 0.75rem;
    margin-bottom: 0;
  }
  
  .settings-content :deep(.category-content) {
    padding: 0.75rem;
  }
  
  .settings-content :deep(.form-actions) {
    padding: 0.75rem;
  }
}
</style> 
