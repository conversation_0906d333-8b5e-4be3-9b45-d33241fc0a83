import { ref, computed } from 'vue'
import LayoutsService from '@/services/layouts.service'

// Estado global dos layouts
const availableLayouts = ref({
  static: [],
  product: [],
  collection: [],
  system: [],
  custom: []
})

const currentLayout = ref(null)
const isLoading = ref(false)

// Layouts padrão do sistema (sempre disponíveis)
const DEFAULT_LAYOUTS = {
  static: [
    {
      layoutId: 'index',
      name: '<PERSON><PERSON><PERSON><PERSON> inicial',
      filename: 'index.html',
      type: 'static',
      icon: '🏠',
      isDefault: true,
      deletable: false,
      duplicatable: true,
      description: 'Layout principal da loja (index.html existente)'
    }
  ],
  product: [
    {
      layoutId: 'product',
      name: 'Layout de produto padrão',
      filename: 'product.html',
      type: 'product',
      icon: '📦',
      isDefault: true,
      deletable: false,
      duplicatable: true
    }
  ],
  collection: [
    {
      layoutId: 'collection',
      name: 'Layout de coleção padrão',
      filename: 'collection.html',
      type: 'collection',
      icon: '📁',
      isDefault: true,
      deletable: false,
      duplicatable: true
    }
  ],
  system: [
    {
      layoutId: 'cart',
      name: 'Layout do carrinho',
      filename: 'cart.html',
      type: 'system',
      icon: '🛒',
      isDefault: true,
      deletable: false,
      duplicatable: false
    },
    {
      layoutId: '404',
      name: 'Layout 404',
      filename: '404.html',
      type: 'system',
      icon: '❌',
      isDefault: true,
      deletable: false,
      duplicatable: false
    }
  ]
}

export function useLayoutManager() {
  
  /**
   * Carrega todos os layouts disponíveis
   */
  const loadLayouts = async () => {
    isLoading.value = true
    
    try {
      // Tentar carregar layouts da API primeiro
      const apiLayouts = await LayoutsService.getLayouts()
      
      if (apiLayouts && Object.keys(apiLayouts).length > 0) {
        // Se API retornou dados, usar os dados da API e completar com padrões se necessário
        availableLayouts.value = {
          static: mergeWithDefaults(apiLayouts.static || [], DEFAULT_LAYOUTS.static),
          product: mergeWithDefaults(apiLayouts.product || [], DEFAULT_LAYOUTS.product),
          collection: mergeWithDefaults(apiLayouts.collection || [], DEFAULT_LAYOUTS.collection),
          system: mergeWithDefaults(apiLayouts.system || [], DEFAULT_LAYOUTS.system),
          custom: apiLayouts.custom || []
        }
      } else {
        // Se API não retornou dados, usar apenas layouts padrão
        availableLayouts.value = { 
          ...DEFAULT_LAYOUTS, 
          custom: [] 
        }
      }
    } catch (error) {
      console.warn('API de layouts não disponível, usando layouts padrão:', error)
      // Em caso de erro, usar apenas layouts padrão
      availableLayouts.value = { 
        ...DEFAULT_LAYOUTS, 
        custom: [] 
      }
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Mescla layouts da API com layouts padrão, evitando duplicações
   */
  const mergeWithDefaults = (apiLayouts, defaultLayouts) => {
    const merged = [...apiLayouts]
    
    // Adicionar layouts padrão que não existem na API
    defaultLayouts.forEach(defaultLayout => {
      const existsInApi = apiLayouts.some(apiLayout => 
        apiLayout.filename === defaultLayout.filename || 
        apiLayout.layoutId === defaultLayout.layoutId
      )
      
      if (!existsInApi) {
        merged.unshift(defaultLayout) // Adicionar no início para manter padrões primeiro
      }
    })
    
    // Ordenar: layouts padrão primeiro, depois customizados
    return merged.sort((a, b) => {
      // Layouts padrão primeiro
      if (a.isDefault && !b.isDefault) return -1;
      if (!a.isDefault && b.isDefault) return 1;
      
      // Entre layouts do mesmo tipo (padrão ou custom), ordenar por nome
      return a.name.localeCompare(b.name);
    })
  }

  /**
   * Cria novo layout baseado em um existente
   */
  const createLayout = async (baseLayoutId, newName, type) => {
    try {
      // Encontrar o layout base para obter o filename
      const baseLayout = findLayoutById(baseLayoutId)
      if (!baseLayout) {
        throw new Error(`Layout base ${baseLayoutId} não encontrado`)
      }
      
      // Gerar novo filename baseado no nome
      const newLayoutId = generateLayoutId(newName, type)
      const newFilename = `${newLayoutId}.html`
      
      const newLayout = await LayoutsService.duplicateLayout({
        sourceFilename: baseLayout.filename,
        newName,
        newFilename,
        type: type // 🔧 CORREÇÃO: Passar o tipo para a API
      })
      
      // Adicionar à lista local
      if (availableLayouts.value[type]) {
        availableLayouts.value[type].push({
          ...newLayout,
          type: type, // 🔧 GARANTIR: Sempre definir o tipo explicitamente
          icon: getIconForType(type),
          deletable: true,
          duplicatable: true
        })
      }
      
      return newLayout
    } catch (error) {
      console.warn('API de layouts não disponível, criando layout localmente:', error)
      
      // Fallback: criar layout localmente se API não estiver disponível
      const newLayoutId = generateLayoutId(newName, type)
      const newLayout = {
        id: Date.now(),
        layoutId: newLayoutId,
        name: newName,
        filename: `${newLayoutId}.html`,
        type: type,
        baseLayoutId: baseLayoutId,
        icon: getIconForType(type),
        isDefault: false,
        deletable: true,
        duplicatable: true,
        description: `Layout ${newName} criado localmente`,
        createdAt: new Date().toISOString()
      }
      
      // Adicionar à lista local
      if (availableLayouts.value[type]) {
        availableLayouts.value[type].push(newLayout)
      }
      
      return newLayout
    }
  }

  /**
   * Remove um layout
   */
  const deleteLayout = async (layoutId, type) => {
    try {
      await LayoutsService.deleteLayout(layoutId)
      
      // Remover da lista local
      if (availableLayouts.value[type]) {
        availableLayouts.value[type] = availableLayouts.value[type].filter(
          layout => layout.id !== layoutId
        )
      }
    } catch (error) {
      console.error('Erro ao deletar layout:', error)
      throw error
    }
  }

  /**
   * Define o layout atual
   */
  const setCurrentLayout = (layout) => {
    currentLayout.value = layout
  }

  /**
   * Busca layout por ID
   */
  const findLayoutById = (layoutId) => {
    for (const type of Object.keys(availableLayouts.value)) {
      const layout = availableLayouts.value[type].find(l => l.layoutId === layoutId)
      if (layout) return layout
    }
    return null
  }

  /**
   * Gera ID único para novo layout
   */
  const generateLayoutId = (name, type) => {
    const slug = name.toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '') // Remove acentos
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
    
    return `${type}-${slug}-${Date.now()}`
  }

  /**
   * Obtém ícone para tipo de layout
   */
  const getIconForType = (type) => {
    const icons = {
      static: '📄',
      product: '📦',
      collection: '📁',
      system: '⚙️',
      custom: '✨'
    }
    return icons[type] || '📄'
  }

  /**
   * Filtros computados
   */
  const allLayouts = computed(() => {
    const all = []
    Object.values(availableLayouts.value).forEach(typeLayouts => {
      all.push(...typeLayouts)
    })
    return all
  })

  const layoutsByType = computed(() => availableLayouts.value)

  const hasCustomLayouts = computed(() => {
    return Object.values(availableLayouts.value).some(typeLayouts => 
      typeLayouts.some(layout => !layout.isDefault)
    )
  })

  return {
    // Estado
    availableLayouts: layoutsByType,
    currentLayout,
    isLoading,
    
    // Computed
    allLayouts,
    hasCustomLayouts,
    
    // Métodos
    loadLayouts,
    createLayout,
    deleteLayout,
    setCurrentLayout,
    findLayoutById,
    generateLayoutId,
    getIconForType
  }
} 