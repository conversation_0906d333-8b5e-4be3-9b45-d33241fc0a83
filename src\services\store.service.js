import axios from 'axios';
import { API_URLS } from '../config/api.config';
import deviceDetection from '@/utils/deviceDetection';

class StoreService {
    #storeData;
    #endpoint = `${API_URLS.STORE_BASE_URL}/data`;
    #modeEndpoint = `${API_URLS.STORE_BASE_URL}`;
    constructor() {
        this.#storeData = {};
    }

    async getStoreData() {
        try {
            const response = await axios.get(this.#endpoint);
            this.#storeData = response.data;
            return this.#storeData;

        } catch (error) {
            console.error('GetStoreData error:', error);
            throw error;
        }
    } // qual é a api que retorna as lojas:  o endpoint /user/stores para pesqui

    async saveStoreData(data) {
        try {
            const response = await axios.post(this.#endpoint, data);
            this.#storeData = response.data;
            return this.#storeData;
        } catch (error) {
            console.error('SaveStoreData error:', error);
            throw error;
        }
    }

    async saveStoreStatus(data) {
        try {
            const response = await axios.post(`${this.#modeEndpoint}-status`, data);
            this.#storeData = response.data;
            return this.#storeData;
        } catch (error) {
            console.error('SaveStoreStatus error:', error);
            throw error;
        }
    }

    async uploadMaintenanceImage(formData) {
        try {
            const response = await axios.post(
                `${this.#modeEndpoint}-status/maintenance-image`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
            return response;
        } catch (error) {
            console.error('UploadImage error: ', error);
            throw error;
        }
    }


    async saveVacationStatus(data) {
        try {
            const response = await axios.post(`${this.#modeEndpoint}-status`, data);
            this.#storeData = response.data;
            return this.#storeData;
        } catch (error) {
            console.error('SaveVacationStatus error:', error);
            throw error;
        }
    }

    async getUserStores() {
        try {
            const response = await axios.get(`${API_URLS.SETTINGS_BASE_URL}/user/stores`);
            return response.data;
        } catch (error) {
            console.error('GetUserStores error:', error);
            throw error;
        }
    }

    async createStore(storeData) {
        try {
            const response = await axios.post(`${API_URLS.SETTINGS_BASE_URL}/user/stores`, storeData);
            return response.data;
        } catch (error) {
            console.error('CreateStore error:', error);
            throw error;
        }
    }

    async authenticateStore(storeId) {
        try {
            // Generate device fingerprint for session management
            const deviceFingerprint = deviceDetection.generateDeviceFingerprint();
            const sessionInfo = deviceDetection.getSessionIdentificationInfo();

            const response = await axios.post(`${API_URLS.AUTH_BASE_URL}/authenticate-store`, {
                storeId: storeId
            }, {
                headers: {
                    'X-Device-Fingerprint': deviceFingerprint,
                    'X-Device-Info': sessionInfo.deviceInfo,
                    'X-User-Agent': sessionInfo.userAgent,
                    'X-Screen-Resolution': sessionInfo.screenResolution,
                    'X-Timezone': sessionInfo.timezone
                }
            });
            return response.data;
        } catch (error) {
            console.error('AuthenticateStore error:', error);
            throw error;
        }
    }

    async getStoreById(storeId) {
        try {
            const response = await axios.get(`${API_URLS.STORE_BASE_URL}/${storeId}`);
            return response.data;
        } catch (error) {
            console.error('GetStoreById error:', error);
            throw error;
        }
    }
}

export default new StoreService();