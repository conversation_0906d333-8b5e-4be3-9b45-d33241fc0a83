<template>
  <div class="content-editor-component">
    <div class="editor-header" v-if="showHeader">
      <h1 class="editor-title" v-if="title">{{ title }}</h1>

    </div>
    <div class="editor-container" :style="containerStyle">
      <div :id="editorId" class="editor-instance"></div>
    </div>

    <!-- Product Selector Modal -->
    <ProductSelectorModal
      v-model:visible="showProductSelector"
      :multiSelect="true"
      :showCategoryFilter="true"
      @select="handleProductSelection"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, computed } from "vue";
import { useI18n } from "vue-i18n";
import IluriaButton from "@/components/iluria/IluriaButton.vue";
import { useToast } from '@/services/toast.service';

// Editor.js e plugins
import EditorJS from "@editorjs/editorjs";
import Header from "@editorjs/header";
import List from "@editorjs/list";
import Paragraph from "@editorjs/paragraph";
import Marker from "@editorjs/marker"; 
import ImageTool from '@editorjs/image';
import Quote from '@editorjs/quote';
import LinkTool from '@editorjs/link';
import Delimiter from '@editorjs/delimiter';
import Embed from '@editorjs/embed';
import ProductGridTool from '@/components/editor/tools/ProductGridTool.js';
import ImageTextTool from '@/components/editor/tools/ImageTextTool.js';
import Checklist from '@editorjs/checklist';
import Table from '@editorjs/table';
import Warning from '@editorjs/warning';
import CodeTool from '@editorjs/code';
import RawTool from '@editorjs/raw';
import AttachesTool from '@editorjs/attaches';
import InlineCode from '@editorjs/inline-code';

// Conversor de HTML
import edjsHTML from "editorjs-html";
import { htmlToEditorJS } from '@/utils/editorjs-converter';

// Importando o novo arquivo de tradução
import ptTranslations from "@/locales/pt-br/editor_pt.json";

// Product Selector Modal
import ProductSelectorModal from '@/components/products/ProductSelectorModal.vue';

// Props
const props = defineProps({
  // Configurações do editor
  modelValue: {
    type: Object,
    default: null
  },
  placeholder: {
    type: String,
    default: null
  },
  autofocus: {
    type: Boolean,
    default: true
  },
  readOnly: {
    type: Boolean,
    default: false
  },
  
  // Configurações visuais
  showHeader: {
    type: Boolean,
    default: true
  },
  title: {
    type: String,
    default: null
  },
  showCopyButton: {
    type: Boolean,
    default: true
  },
  copyButtonText: {
    type: String,
    default: null
  },
  height: {
    type: String,
    default: 'calc(100vh - 200px)'
  },
  
  // Configurações de ferramentas
  tools: {
    type: Object,
    default: null
  },
  enabledTools: {
    type: Array,
    default: () => ['paragraph', 'header', 'list', 'marker', 'quote', 'linkTool', 'delimiter', 'embed', 'table', 'checklist', 'warning', 'code', 'raw', 'attaches', 'inlineCode', 'productGrid', 'imageText', 'image']
  },
  
  // Configurações de upload
  imageUploader: {
    type: Function,
    default: null
  },
  
  // ID único para múltiplas instâncias
  editorId: {
    type: String,
    default: () => `editorjs-${Math.random().toString(36).substr(2, 9)}`
  }
});

// Emits
const emit = defineEmits(['update:modelValue', 'ready', 'change', 'save']);

const { t } = useI18n();
const Toast = useToast();

const isSaving = ref(false);
const showProductSelector = ref(false);
let editor = null;
let currentProductSelectionCallback = null;

// Computed
const containerStyle = computed(() => ({
  height: props.height
}));

// Configura o parser do editorjs-html para entender o bloco customizado
const edjsParser = edjsHTML({
  productGrid: function(block) {
    const cardColor = block.data.cardColor || '#ffffff';
    const buttonColor = block.data.buttonColor || '#1f2937';
    const titleColor = block.data.titleColor || '#1f2937';
    const descriptionColor = block.data.descriptionColor || '#6b7280';
    const ratingColor = block.data.ratingColor || '#f59e0b';
    const titleFontSize = block.data.titleFontSize || '1.125';
    const descriptionFontSize = block.data.descriptionFontSize || '0.875';

    const productsHtml = block.data.products.map(product => {
      // Use HTML entities for proper star rendering
      const filledStars = '&#9733;'.repeat(product.rating); // ★
      const emptyStars = '&#9734;'.repeat(5 - product.rating); // ☆
      const starsHTML = filledStars + emptyStars;

      return `
        <div class="product-card" style="
          background-color: ${cardColor};
          border: 1px solid #e5e7eb;
          border-radius: 8px;
          overflow: hidden;
          box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
          transition: transform 0.2s ease-in-out;
          display: flex;
          flex-direction: column;
          height: auto;
          min-height: 300px;
        ">
          <div class="product-image">
            <img src="${product.imageUrl}" alt="${product.title}" style="
              width: 100%;
              height: 150px;
              object-fit: cover;
              display: block;
            " />
          </div>
          <div class="product-info" style="
            padding: 1rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            justify-content: space-between;
          ">
            <h3 class="product-title" style="
              font-size: ${titleFontSize}rem;
              font-weight: 600;
              color: ${titleColor};
              margin: 0 0 0.5rem 0;
              line-height: 1.4;
            ">${product.title}</h3>
            <p class="product-description" style="
              font-size: ${descriptionFontSize}rem;
              color: ${descriptionColor};
              margin: 0 0 1rem 0;
              min-height: 40px;
              line-height: 1.5;
              flex-grow: 1;
            ">${product.description}</p>
            <div class="product-rating" style="
              color: ${ratingColor};
              margin-bottom: 1rem;
              font-size: 1.2rem;
              line-height: 1;
            ">${starsHTML}</div>
            <a href="${product.buttonUrl}" class="product-button" style="
              display: block;
              width: 100%;
              padding: 0.75rem 1rem;
              background-color: ${buttonColor};
              color: white;
              text-align: center;
              border-radius: 6px;
              text-decoration: none;
              transition: background-color 0.2s;
              box-sizing: border-box;
              margin-top: auto;
            ">${product.buttonText}</a>
          </div>
        </div>
      `;
    }).join('');

    return `
      <div class="product-grid-tool" style="
        display: grid;
        grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
        gap: 1rem;
        margin: 1rem 0;
        width: 100%;
        box-sizing: border-box;
      ">
        ${productsHtml}
      </div>
    `;
  },

  imageText: function(block) {
    const imagePosition = block.data.imagePosition || 'left';
    const title = block.data.title || '';
    const text = block.data.text || '';
    const imageUrl = block.data.imageUrl || '';
    const textColor = block.data.textColor || '#ffffff';
    const backgroundColor = block.data.backgroundColor || '#4a90e2';
    const buttons = block.data.buttons || [];

    const isImageLeft = imagePosition === 'left';

    const buttonsHtml = buttons.map(button => `
      <a href="${button.url}" style="
        display: inline-block;
        background: ${button.backgroundColor || 'transparent'};
        color: ${button.color || textColor};
        border: 1px solid ${button.color || textColor};
        padding: 12px 24px;
        margin-right: 12px;
        margin-bottom: 8px;
        border-radius: 6px;
        text-decoration: none;
        font-weight: 500;
        transition: all 0.3s ease;
      ">${button.text}</a>
    `).join('');

    return `
      <div class="image-text-block" style="
        background-color: ${backgroundColor};
        color: ${textColor};
        padding: 40px;
        border-radius: 12px;
        display: flex;
        align-items: center;
        gap: 40px;
        margin: 20px 0;
        flex-direction: ${isImageLeft ? 'row' : 'row-reverse'};
        min-height: 300px;
      ">
        <div class="image-section" style="flex: 1;">
          ${imageUrl ? `
            <img src="${imageUrl}" alt="${title}" style="
              width: 100%;
              height: auto;
              border-radius: 8px;
              object-fit: cover;
              max-height: 400px;
            " />
          ` : `
            <div style="
              border: 2px dashed rgba(255,255,255,0.3);
              border-radius: 8px;
              padding: 40px;
              text-align: center;
              color: rgba(255,255,255,0.7);
              min-height: 200px;
              display: flex;
              align-items: center;
              justify-content: center;
              flex-direction: column;
            ">
              <div style="font-size: 48px; margin-bottom: 16px;">📷</div>
              <div>Imagem não definida</div>
            </div>
          `}
        </div>

        <div class="text-section" style="flex: 1;">
          ${title ? `<h2 style="
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 16px 0;
            color: ${textColor};
          ">${title}</h2>` : ''}

          ${text ? `<p style="
            font-size: 16px;
            line-height: 1.6;
            margin: 0 0 20px 0;
            color: ${textColor};
          ">${text}</p>` : ''}

          ${buttonsHtml ? `<div class="buttons-container">${buttonsHtml}</div>` : ''}
        </div>
      </div>
    `;
  },

  warning: function(block) {
    const title = block.data.title || '';
    const message = block.data.message || '';
    return `
      <div class="warning-block" style="
        background-color: #fef3cd;
        border: 1px solid #fecaca;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        border-left: 4px solid #f59e0b;
      ">
        ${title ? `<h4 style="margin: 0 0 0.5rem 0; color: #92400e; font-weight: 600;">${title}</h4>` : ''}
        <p style="margin: 0; color: #92400e;">${message}</p>
      </div>
    `;
  },

  attaches: function(block) {
    const file = block.data.file || {};
    const title = block.data.title || file.name || 'Arquivo anexado';
    const url = file.url || '#';
    const size = file.size ? `(${Math.round(file.size / 1024)} KB)` : '';

    return `
      <div class="attaches-block" style="
        border: 1px solid #e5e7eb;
        border-radius: 8px;
        padding: 1rem;
        margin: 1rem 0;
        background-color: #f9fafb;
        display: flex;
        align-items: center;
        gap: 0.75rem;
      ">
        <div style="
          width: 40px;
          height: 40px;
          background-color: #3b82f6;
          border-radius: 6px;
          display: flex;
          align-items: center;
          justify-content: center;
          color: white;
          font-weight: bold;
        ">📎</div>
        <div style="flex-grow: 1;">
          <a href="${url}" style="
            color: #3b82f6;
            text-decoration: none;
            font-weight: 500;
          " download>${title}</a>
          ${size ? `<div style="font-size: 0.875rem; color: #6b7280;">${size}</div>` : ''}
        </div>
      </div>
    `;
  },

  raw: function(block) {
    return block.data.html || '';
  }
});

// Dados iniciais padrão
const getInitialData = () => {
  if (props.modelValue && props.modelValue.blocks && props.modelValue.blocks.length > 0) {
    return props.modelValue;
  }

  // Return empty data to ensure placeholder is shown
  return {
    time: new Date().getTime(),
    blocks: [],
    version: "2.28.2"
  };
};

// Configuração padrão de upload de imagem
const defaultImageUploader = {
  uploadByFile(file){
    if (props.imageUploader) {
      return props.imageUploader(file);
    }
    
    // Lógica de upload padrão (placeholder)
    return new Promise((resolve) => {
      resolve({
        success: 1,
        file: {
          url: 'https://www.lojadelayouts.com/wp-content/uploads/2024/04/Iluria.jpg',
        }
      });
    });
  },
  uploadByUrl(url) {
    return new Promise((resolve) => {
      resolve({
        success: 1,
        file: {
          url: url
        }
      })
    });
  }
};

// Configuração das ferramentas
const getToolsConfig = () => {
  const allTools = {
    paragraph: {
      class: Paragraph,
      inlineToolbar: true,
      config: {
        placeholder: props.placeholder || t("contentEditor.placeholder"),
        preserveBlank: false
      }
    },
    header: {
      class: Header,
      inlineToolbar: true,
      config: {
        placeholder: props.placeholder || t("contentEditor.headerPlaceholder"),
        levels: [1, 2, 3, 4],
        defaultLevel: 2
      },
    },
    list: { class: List, inlineToolbar: true, config: { defaultStyle: "unordered" } },
    marker: { class: Marker, shortcut: 'CMD+SHIFT+M' },
    quote: { class: Quote, inlineToolbar: true },
    linkTool: { class: LinkTool },
    delimiter: { class: Delimiter },
    embed: { class: Embed, inlineToolbar: true },
    table: { class: Table, inlineToolbar: true },
    checklist: { class: Checklist, inlineToolbar: true },
    warning: { class: Warning, inlineToolbar: true },
    code: { class: CodeTool },
    raw: { class: RawTool },
    attaches: { class: AttachesTool },
    inlineCode: { class: InlineCode },
    productGrid: ProductGridTool,
    imageText: ImageTextTool,
    image: {
      class: ImageTool,
      config: {
        uploader: defaultImageUploader
      }
    }
  };

  // Se ferramentas customizadas foram fornecidas, use-as
  if (props.tools) {
    return props.tools;
  }

  // Caso contrário, filtre as ferramentas habilitadas
  const enabledTools = {};
  props.enabledTools.forEach(toolName => {
    if (allTools[toolName]) {
      enabledTools[toolName] = allTools[toolName];
    }
  });

  return enabledTools;
};

// Watch para mudanças no modelValue (comentado para evitar re-renders desnecessários)
// watch(() => props.modelValue, (newValue) => {
//   if (editor && newValue) {
//     editor.render(newValue);
//   }
// }, { deep: true });

// Métodos
const initializeEditor = () => {
  editor = new EditorJS({
    holder: props.editorId,
    placeholder: props.placeholder || t("contentEditor.placeholder"),
    tools: getToolsConfig(),
    data: getInitialData(),
    autofocus: props.autofocus,
    readOnly: props.readOnly,
    i18n: {
      messages: ptTranslations
    },

    onReady: () => {
      // Ensure placeholder is applied after editor is ready
      setTimeout(() => {
        const editorElement = document.getElementById(props.editorId);
        if (editorElement) {
          const placeholderText = props.placeholder || t("contentEditor.placeholder");

          // Apply placeholder to existing empty paragraphs
          const paragraphs = editorElement.querySelectorAll('.ce-paragraph');

          paragraphs.forEach((p) => {
            if (!p.textContent.trim()) {
              p.setAttribute('data-placeholder', placeholderText);
            }
          });

          // Set up observer to handle dynamically created paragraphs
          const observer = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === Node.ELEMENT_NODE) {
                  const newParagraphs = node.classList?.contains('ce-paragraph')
                    ? [node]
                    : node.querySelectorAll?.('.ce-paragraph') || [];

                  newParagraphs.forEach(p => {
                    if (!p.textContent.trim()) {
                      p.setAttribute('data-placeholder', placeholderText);
                    }
                  });
                }
              });
            });
          });

          observer.observe(editorElement, {
            childList: true,
            subtree: true
          });

          // Store observer for cleanup
          editor._placeholderObserver = observer;
        }
      }, 100);

      emit('ready', editor);
    },

    onChange: async () => {
      try {
        const outputData = await editor.save();
        emit('update:modelValue', outputData);
        emit('change', outputData);
      } catch (error) {
        console.error('Erro ao salvar dados do editor:', error);
      }
    }
  });
};

const copyContentToClipboard = async () => {
  if (!editor) return;

  isSaving.value = true;
  try {
    const outputData = await editor.save();

    if (outputData.blocks.length === 0) {
      Toast.showWarning("O editor está vazio. Não há nada para copiar.");
      isSaving.value = false;
      return;
    }

    let htmlString;
    try {
      htmlString = edjsParser.parse(outputData);
    } catch (e) {
      console.error("Falha ao converter o conteúdo do Editor.js para HTML:", e);
      Toast.showError("Ocorreu um erro inesperado ao processar o conteúdo.");
      isSaving.value = false;
      return;
    }

    await navigator.clipboard.writeText(htmlString);
    Toast.showSuccess(t("contentEditor.copySuccess"));
    emit('save', { data: outputData, html: htmlString });

  } catch (error) {
    console.error("Erro ao salvar ou copiar conteúdo:", error);
    Toast.showError(t("contentEditor.copyError"));
  } finally {
    isSaving.value = false;
  }
};

// Métodos expostos para uso externo
const save = async () => {
  if (!editor) return null;

  try {
    const outputData = await editor.save();
    emit('update:modelValue', outputData);
    return outputData;
  } catch (error) {
    console.error('Erro ao salvar dados do editor:', error);
    return null;
  }
};

const getHTML = async () => {
  if (!editor) return '';

  try {
    const outputData = await editor.save();
    const htmlArray = edjsParser.parse(outputData);
    // Join array of HTML strings into single string
    return Array.isArray(htmlArray) ? htmlArray.join('') : '';
  } catch (error) {
    console.error('Erro ao obter HTML do editor:', error);
    return '';
  }
};

const setHTML = async (html) => {
  if (!editor || !html) return;

  try {
    const editorData = htmlToEditorJS(html);
    await editor.render(editorData);
    emit('update:modelValue', editorData);
  } catch (error) {
    console.error('Erro ao definir HTML no editor:', error);
  }
};

const clear = () => {
  if (editor) {
    editor.clear();
  }
};

const destroy = () => {
  if (editor) {
    // Clean up placeholder observer
    if (editor._placeholderObserver) {
      editor._placeholderObserver.disconnect();
      editor._placeholderObserver = null;
    }

    editor.destroy();
    editor = null;
  }
};

const render = (data) => {
  if (editor && data) {
    editor.render(data);
  }
};

// Product Selector handlers
const handleOpenProductSelector = (event) => {
  currentProductSelectionCallback = event.detail.onSelect;
  showProductSelector.value = true;
};

const handleProductSelection = (selectedProducts) => {
  if (currentProductSelectionCallback) {
    currentProductSelectionCallback(selectedProducts);
    currentProductSelectionCallback = null;
  }
  showProductSelector.value = false;
};

// Lifecycle
onMounted(() => {
  initializeEditor();

  // Listen for product selector events from ProductGridTool
  document.addEventListener('openProductSelector', handleOpenProductSelector);
});

onUnmounted(() => {
  destroy();

  // Clean up event listeners
  document.removeEventListener('openProductSelector', handleOpenProductSelector);
});

// Expose methods for parent components
defineExpose({
  save,
  clear,
  destroy,
  render,
  getHTML,
  setHTML,
  editor: () => editor
});
</script>

<style scoped>
.content-editor-component {
  display: flex;
  flex-direction: column;
  background-color: transparent;
  height: 100%;
}



.editor-title {
  font-size: 1.875rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary, #1f2937);
  margin: 0;
}



.editor-container {
  background-color: transparent;
  border-radius: 0;
  box-shadow: none;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  height: 100%;
}

.editor-instance {
  flex-grow: 1;
  padding: 1rem;
  height: 100%;
  overflow-y: auto;
}

/* --- Estilos Tematizados para o Editor.js --- */


/* Área de escrita principal */
.editor-instance :deep(.ce-block__content),
.editor-instance :deep(.ce-toolbar__content) {
  max-width: 800px; /* Mantém a legibilidade */
  margin: 0 auto;
}

.editor-instance :deep(.codex-editor-redactor) {
  padding-bottom: 150px !important;
  color: var(--iluria-color-text-primary);
}

.editor-instance :deep(.ce-popover__container) {
  background-color: var(--iluria-color-background) !important;
  border: 1px solid var(--iluria-color-border-heavy) !important;
}

.editor-instance :deep(.ce-toolbar__settings-btn),
.editor-instance :deep(.ce-toolbar__plus) {
  color: var(--iluria-color-text-primary) !important;
}

.editor-instance :deep(.ce-popover-item) {
  border: 0.5px solid var(--iluria-color-border) !important;
}

.editor-instance :deep(.ce-popover-item:hover) {
  background-color: var(--iluria-color-border-hover) !important;
}

.editor-instance :deep(.ce-popover-item:hover .ce-popover-item__icon),
.editor-instance :deep(.ce-popover-item:hover .ce-popover-item__title ) {
  color: var(--iluria-color-text-inverted) !important;
}

.editor-instance :deep(.ce-popover-item__title) {
  color: var(--iluria-color-text-primary) !important;
}

.editor-instance :deep(.ce-popover-item__icon) {
  color: var(--iluria-color-text-primary) !important;
}

/* Placeholder - Enhanced styles */
.editor-instance :deep(.ce-placeholder::before),
.editor-instance :deep(.ce-paragraph[data-placeholder]:empty::before),
.editor-instance :deep(.ce-paragraph:empty::before) {
  content: attr(data-placeholder) !important;
  font-style: italic !important;
  font-weight: 400 !important;
  color: var(--iluria-color-text-muted, #9ca3af) !important;
  opacity: 0.8 !important;
  display: block !important;
  visibility: visible !important;
  pointer-events: none !important;
  position: relative !important;
  z-index: 1 !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Estilos para Títulos */
.editor-instance :deep(h1.ce-header),
.editor-instance :deep(h2.ce-header),
.editor-instance :deep(h3.ce-header),
.editor-instance :deep(h4.ce-header) {
  color: var(--iluria-color-text-title);
  font-weight: 700;
}

/* Parágrafos e Listas */
.editor-instance :deep(.ce-paragraph),
.editor-instance :deep(.cdx-list__item) {
  line-height: 1.6;
  font-size: 1rem;
  color: var(--iluria-color-text-secondary);
}

/* Barra de ferramentas flutuante (Toolbox) */
.editor-instance :deep(.ce-toolbar__actions) {
  right: calc(100% + 15px);
}

.editor-instance :deep(.codex-editor--toolbox-opened .ce-toolbar__actions) {
  right: calc(100% + 15px);
}

.editor-instance :deep(.ce-toolbox) {
  background-color: var(--iluria-color-surface-raised);
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-lg);
}

.editor-instance :deep(.ce-toolbox__button),
.editor-instance :deep(.cdx-search-field__input) {
  color: var(--iluria-color-text-secondary);
}

.editor-instance :deep(.cdx-search-field__input) {
  background-color: transparent !important;
  border: none !important;
}

.editor-instance :deep(.ce-toolbox__button:hover) {
  background-color: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

.editor-instance :deep(.ce-toolbox__button--active) {
  background-color: var(--iluria-color-navbar-active) !important;
  color: var(--iluria-color-primary) !important;
}

/* Barra de ferramentas inline */
.editor-instance :deep(.ce-inline-toolbar) {
  background-color: var(--iluria-color-text-primary);
  border: 1px solid var(--iluria-color-border-heavy);
  border-radius: 8px;
  box-shadow: var(--iluria-shadow-lg);
  color: var(--iluria-color-text-inverted);
}

.editor-instance :deep(.ce-inline-tool) {
  color: var(--iluria-color-text-inverted);
  transition: background-color 0.2s ease;
}

.editor-instance :deep(.ce-inline-tool:hover) {
  background-color: var(--iluria-color-text-secondary);
}

.editor-instance :deep(.ce-inline-tool-input) {
  background-color: var(--iluria-color-text-secondary);
  color: var(--iluria-color-text-inverted);
}

/* Marcador (Highlight) */
.editor-instance :deep(.cdx-marker) {
  background-color: var(--iluria-color-primary-soft, rgba(255, 239, 134, 0.44));
  color: var(--iluria-color-text-primary);
  padding: 2px 0;
}

/* --- Estilos para o tema escuro do Editor.js --- */

/* Popover geral, menus e tooltips */
.editor-instance :deep(.ce-popover),
.editor-instance :deep(.ce-conversion-toolbar),
.editor-instance :deep(.ce-settings),
.editor-instance :deep(.ce-inline-toolbar) {
  background-color: var(--iluria-color-card-bg);
  color: var(--iluria-color-text-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  box-shadow: var(--iluria-shadow-lg);
}

.editor-instance :deep(.ce-popover__content) {
  background: var(--iluria-color-card-bg) !important;
}

/* Itens e botões dentro dos menus */
.editor-instance :deep(.ce-popover__item),
.editor-instance :deep(.ce-conversion-tool),
.editor-instance :deep(.ce-settings__button),
.editor-instance :deep(.ce-inline-tool) {
  color: var(--iluria-color-text-primary);
}

.editor-instance :deep(.ce-popover__item:hover),
.editor-instance :deep(.ce-conversion-tool:hover),
.editor-instance :deep(.ce-settings__button:hover),
.editor-instance :deep(.ce-inline-tool:hover) {
  background-color: var(--iluria-color-border-hover) !important;
  color: var(--iluria-color-text-primary);
}

/* Ícones nos menus */
.editor-instance :deep(.ce-popover__item-icon),
.editor-instance :deep(.ce-conversion-tool__icon),
.editor-instance :deep(.cdx-search-field__icon) {
  filter: invert(1); /* Inverte a cor dos ícones para branco */
}

/* Campo de filtro/busca */
.editor-instance :deep(.cdx-search-field),
.editor-instance :deep(.cdx-search-field__input) {
  background-color: var(--iluria-color-surface) !important;
  border-color: transparent !important;
  color: var(--iluria-color-text-primary);
}

/* Botão selecionado/ativo */
.editor-instance :deep(.ce-popover__item--active),
.editor-instance :deep(.ce-conversion-tool--active),
.editor-instance :deep(.ce-conversion-tool--focused) {
  background-color: var(--iluria-color-border-hover) !important;
  color: var(--iluria-color-text-primary) !important;
}

/* Estiliza a barra de rolagem para o tema escuro */
.editor-instance :deep(.ce-popover__content::-webkit-scrollbar) {
  width: 8px;
}

.editor-instance :deep(.ce-popover__content::-webkit-scrollbar-track) {
  background: transparent;
}

.editor-instance :deep(.ce-popover__content::-webkit-scrollbar-thumb) {
  background-color: var(--iluria-color-border);
  border-radius: 4px;
}

.editor-instance :deep(.ce-popover__content::-webkit-scrollbar-thumb:hover) {
  background-color: var(--iluria-color-text-muted);
}

/* Ícone "+" da Toolbox e handle de arrastar */
.editor-instance :deep(.ce-toolbar__plus svg),
.editor-instance :deep(.ce-block__drag-icon svg) {
  fill: var(--iluria-color-text-primary) !important;
}

.editor-instance :deep(.ce-toolbar__plus:hover svg),
.editor-instance :deep(.ce-block__drag-icon:hover svg) {
  fill: var(--iluria-color-text-primary) !important;
}

/* --- Estilos para o Grid de Produtos --- */
.editor-instance :deep(.product-grid-tool) {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(220px, 1fr));
  gap: 1rem;
  margin: 1rem 0;
  width: 100%;
  box-sizing: border-box;
}

.editor-instance :deep(.product-card) {
  border: 1px solid var(--iluria-color-border-heavy, #4a5568);
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-md);
  background-color: var(--iluria-color-surface-heavy, #2d3748);
  transition: transform 0.2s ease-in-out;
  display: flex;
  flex-direction: column;
  height: auto;
  min-height: 300px;
}

.editor-instance :deep(.product-card:hover) {
  transform: translateY(-5px);
}

.editor-instance :deep(.product-image img) {
  width: 100%;
  height: 150px;
  object-fit: cover;
  display: block;
}

.editor-instance :deep(.product-info) {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  flex-grow: 1;
  justify-content: space-between;
}

.editor-instance :deep(.product-title) {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary-on-dark, #ffffff);
  margin: 0 0 0.5rem 0;
  line-height: 1.4;
}

.editor-instance :deep(.product-description) {
  font-size: 0.875rem;
  color: var(--iluria-color-text-muted-on-dark, #a0aec0);
  margin: 0 0 1rem 0;
  min-height: 40px;
  line-height: 1.5;
  flex-grow: 1;
}

.editor-instance :deep(.product-rating) {
  color: #f59e0b; /* amber-500 */
  margin-bottom: 1rem;
  font-size: 1.2rem;
  line-height: 1;
}

.editor-instance :deep(.product-button) {
  display: block;
  width: 100%;
  padding: 0.75rem 1rem;
  background-color: var(--iluria-color-primary);
  color: white;
  text-align: center;
  border-radius: 6px;
  text-decoration: none;
  transition: background-color 0.2s;
  box-sizing: border-box;
  margin-top: auto;
}

.editor-instance :deep(.product-button:hover) {
  background-color: var(--iluria-color-primary-hover);
}

.editor-instance :deep(.ce-toolbar),
.editor-instance :deep(.ce-toolbar *),
.editor-instance :deep(.ce-block),
.editor-instance :deep(.ce-block *),
.editor-instance :deep(.codex-editor),
.editor-instance :deep(.codex-editor *) {
  /* Force remove any visual artifacts that might appear as boxes */
  box-shadow: none !important;
}



/* Force remove any visual elements that might appear as a box near toolbar */
.editor-instance :deep(.ce-toolbar__actions),
.editor-instance :deep(.ce-toolbar__actions *),
.editor-instance :deep(.ce-toolbar__plus),
.editor-instance :deep(.ce-toolbar__plus *),
.editor-instance :deep(.ce-toolbar__settings-btn),
.editor-instance :deep(.ce-toolbar__settings-btn *) {
  border: none !important;
}

/* Remove any margin/padding that might create visual spacing appearing as a box */
.editor-instance :deep(.ce-toolbar),
.editor-instance :deep(.ce-block) {
  margin: 0 !important;
  padding: 0 !important;
}

/* Restore necessary padding for content and ensure placeholder space */
.editor-instance :deep(.ce-block__content) {
  padding: 0.4em 0 !important;
}

.editor-instance :deep(.ce-paragraph) {
  padding: 0.4em 0 !important;
  margin: 0 !important;
  min-height: 1.5em !important;
  position: relative !important;
}
</style>

<style>
/* Global EditorJS Popover and Toolbox Theming */
.ce-popover,
.ce-conversion-toolbar,
.ce-settings,
.ce-inline-toolbar,
.ce-toolbox {
  color: var(--iluria-color-text-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  box-shadow: var(--iluria-shadow-lg);
}

/* Global placeholder styles for EditorJS */
.ce-paragraph {
  position: relative;
}

.ce-paragraph[data-placeholder]:empty::before {
  content: attr(data-placeholder) !important;
  display: block !important;
  font-style: italic;
  font-weight: 400;
  color: var(--iluria-color-text-muted, #9ca3af) !important;
  opacity: 0.7 !important;
  visibility: visible !important;
  pointer-events: none;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ce-paragraph:empty::before {
  content: attr(data-placeholder) !important;
  display: block !important;
  font-style: italic;
  font-weight: 400;
  color: var(--iluria-color-text-muted, #9ca3af) !important;
  opacity: 0.7 !important;
  visibility: visible !important;
  pointer-events: none;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.ce-popover__item,
.ce-conversion-tool,
.ce-settings__button,
.ce-inline-tool {
  color: var(--iluria-color-text-primary);
  transition: background-color 0.2s ease;
  border: none !important;
  outline: none !important;
}

/* Remove any pseudo-elements that might create visual artifacts, but preserve placeholder */
.ce-settings__button::before,
.ce-settings__button::after,
.ce-toolbar__settings-btn::before,
.ce-toolbar__settings-btn::after,
.ce-toolbar__plus::before,
.ce-toolbar__plus::after,
.ce-block::before,
.ce-block::after,
.codex-editor::before,
.codex-editor::after,
.ce-toolbar::before,
.ce-toolbar::after {
  display: none !important;
  content: none !important;
}

/* Enhanced placeholder styles to ensure visibility */
.ce-paragraph[data-placeholder]:empty::before,
.ce-paragraph:empty::before {
  content: attr(data-placeholder) !important;
  display: block !important;
  font-style: italic !important;
  font-weight: 400 !important;
  color: var(--iluria-color-text-muted, #9ca3af) !important;
  opacity: 0.8 !important;
  visibility: visible !important;
  pointer-events: none !important;
  position: relative !important;
  z-index: 1 !important;
  margin: 0 !important;
  padding: 0 !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  background: transparent !important;
  line-height: 1.5 !important;
}

/* Ensure placeholder disappears when content is present */
.ce-paragraph:not(:empty)::before {
  display: none !important;
  content: none !important;
}

/* Additional fallback for Editor.js placeholder */
.codex-editor .ce-paragraph:empty::before {
  content: attr(data-placeholder) !important;
  display: block !important;
  font-style: italic !important;
  color: var(--iluria-color-text-muted, #9ca3af) !important;
  opacity: 0.8 !important;
}

/* Force placeholder visibility in all scenarios */
#editorjs .ce-paragraph:empty::before,
[id^="editorjs"] .ce-paragraph:empty::before,
.editor-instance .ce-paragraph:empty::before {
  content: attr(data-placeholder) !important;
  display: block !important;
  font-style: italic !important;
  color: var(--iluria-color-text-muted, #9ca3af) !important;
  opacity: 0.8 !important;
  visibility: visible !important;
  pointer-events: none !important;
  position: relative !important;
  top: 0 !important;
  left: 0 !important;
  right: 0 !important;
  z-index: 1 !important;
  line-height: 1.5 !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ensure placeholder is hidden when content exists */
.editor-instance .ce-paragraph:not(:empty)::before {
  display: none !important;
  content: none !important;
}

/* Specifically target any elements that might be creating the visual box*/
.ce-toolbar:empty::before {
  display: none !important;
  content: none !important;
}

.ce-popover__item:hover,
.ce-conversion-tool:hover,
.ce-settings__button:hover,
.ce-inline-tool:hover {
  background-color: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

/* Active/selected state */
.ce-popover__item--active,
.ce-conversion-tool--active,
.ce-conversion-tool--focused {
  background-color: var(--iluria-color-primary-soft);
  color: var(--iluria-color-primary);
}
</style>
