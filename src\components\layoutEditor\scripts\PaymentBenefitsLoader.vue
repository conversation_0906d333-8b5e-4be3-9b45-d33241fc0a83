<template>
  <!-- Loader moderno para Benefícios de Pagamento -->
</template>

<script>
// Sistema de ícones moderno SVG
const ICONS_LIBRARY = {
  'shield-check': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
    <path d="M9 12l2 2 4-4"/>
  </svg>`,
  'truck': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="1" y="3" width="15" height="13"/>
    <path d="M16 8h4l3 3v5h-7V8zM16 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM7 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
  </svg>`,
  'refresh': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
    <path d="M3 3v5h5M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/>
    <path d="M16 16h5v5"/>
  </svg>`,
  'headset': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M3 17v-4a9 9 0 0 1 18 0v4"/>
    <rect x="17" y="16" width="4" height="5" rx="1"/>
    <rect x="3" y="16" width="4" height="5" rx="1"/>
  </svg>`,
  'credit-card': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
    <line x1="1" y1="10" x2="23" y2="10"/>
  </svg>`,
  'gift': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <polyline points="20,12 20,22 4,22 4,12"/>
    <rect x="2" y="7" width="20" height="5"/>
    <line x1="12" y1="22" x2="12" y2="7"/>
    <path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"/>
    <path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"/>
  </svg>`,
  'clock': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="12" cy="12" r="10"/>
    <polyline points="12,6 12,12 16,14"/>
  </svg>`,
  'star': `<svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
  </svg>`
}

// Esquemas de cores modernos
const COLOR_SCHEMES = {
  blue: {
    primary: '#3b82f6',
    secondary: '#1e40af',
    accent: '#dbeafe',
    text: '#1f2937'
  },
  green: {
    primary: '#10b981',
    secondary: '#047857',
    accent: '#d1fae5',
    text: '#1f2937'
  },
  purple: {
    primary: '#8b5cf6',
    secondary: '#6d28d9',
    accent: '#e9d5ff',
    text: '#1f2937'
  },
  orange: {
    primary: '#f59e0b',
    secondary: '#d97706',
    accent: '#fef3c7',
    text: '#1f2937'
  }
}

// Benefícios padrão modernos
const getDefaultBenefits = () => {
  const timestamp = Date.now()
  return [
    {
      id: `benefit-${timestamp}-1`,
      title: 'Frete Grátis',
      description: 'Entrega gratuita para compras acima de R$ 99 em todo o Brasil',
      icon: 'truck',
      enabled: true,
      highlight: false
    },
    {
      id: `benefit-${timestamp}-2`,
      title: 'Pagamento 100% Seguro',
      description: 'Suas informações protegidas com criptografia SSL de última geração',
      icon: 'shield-check',
      enabled: true,
      highlight: false
    },
    {
      id: `benefit-${timestamp}-3`,
      title: 'Garantia de Reembolso',
      description: '30 dias para devolução com reembolso total sem complicações',
      icon: 'refresh',
      enabled: true,
      highlight: false
    }
  ]
}

// Injeção de estilos CSS modernos
const injectModernStyles = () => {
  if (document.getElementById('payment-benefits-modern-styles')) return
  
  const style = document.createElement('style')
  style.id = 'payment-benefits-modern-styles'
  style.textContent = `
    /* SISTEMA DE DESIGN MODERNO - BENEFÍCIOS DE PAGAMENTO */
    .iluria-payment-benefits {
      --benefits-primary-color: #3b82f6;
      --benefits-secondary-color: #1e40af;
      --benefits-accent-color: #dbeafe;
      --benefits-text-color: #1f2937;
      
      padding: clamp(2rem, 5vw, 4rem) 1rem;
      background: #ffffff;
      border-radius: 16px;
      font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', 'Roboto', sans-serif;
      position: relative;
      overflow: hidden;
    }
    
    .benefits-container {
      max-width: 1200px;
      margin: 0 auto;
      position: relative;
      z-index: 2;
    }
    
    .benefits-header {
      text-align: center;
      margin-bottom: clamp(2rem, 4vw, 3rem);
    }
    
    .benefits-title {
      font-size: clamp(1.75rem, 4vw, 2.5rem);
      font-weight: 700;
      color: var(--benefits-text-color);
      margin: 0 0 0.5rem 0;
      line-height: 1.2;
      letter-spacing: -0.025em;
    }
    
    .benefits-subtitle {
      font-size: clamp(1rem, 2.5vw, 1.25rem);
      color: #6b7280;
      margin: 0;
      font-weight: 400;
      line-height: 1.5;
    }
    
    .benefits-grid {
      display: grid;
      gap: clamp(1rem, 3vw, 2rem);
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .benefits-grid[data-columns="1"] {
      grid-template-columns: 1fr;
      max-width: 600px;
      margin: 0 auto;
    }
    
    .benefits-grid[data-columns="2"] {
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    }
    
    .benefits-grid[data-columns="3"] {
      grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    }
    
    .benefits-grid[data-columns="4"] {
      grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }
    
    .benefit-card {
      background: #ffffff;
      border: 2px solid #f1f5f9;
      border-radius: 16px;
      padding: clamp(1.5rem, 3vw, 2rem);
      transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
      position: relative;
      cursor: pointer;
      overflow: hidden;
      transform: translateY(0);
      opacity: 0;
      animation: fadeInUp 0.6s cubic-bezier(0.4, 0, 0.2, 1) forwards;
      animation-delay: var(--animation-delay, 0ms);
    }
    
    .benefit-card::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      height: 4px;
      background: linear-gradient(90deg, var(--benefits-primary-color), var(--benefits-secondary-color));
      transform: scaleX(0);
      transition: transform 0.4s ease;
    }
    
    .benefit-card:hover::before {
      transform: scaleX(1);
    }
    
    .benefit-card:hover {
      border-color: var(--benefits-accent-color);
      box-shadow: 0 20px 40px -12px rgba(59, 130, 246, 0.15), 0 8px 16px -4px rgba(59, 130, 246, 0.1);
    }
    
    .benefit-highlighted {
      background: linear-gradient(135deg, var(--benefits-accent-color) 0%, #ffffff 100%);
      border-color: var(--benefits-primary-color);
      transform: scale(1.02);
    }
    
    .benefit-highlighted::before {
      transform: scaleX(1);
    }
    
    .benefit-badge {
      position: absolute;
      top: 1rem;
      right: 1rem;
      background: var(--benefits-primary-color);
      color: white;
      padding: 0.25rem 0.75rem;
      border-radius: 20px;
      font-size: 0.75rem;
      font-weight: 600;
      text-transform: uppercase;
      letter-spacing: 0.5px;
    }
    
    .benefit-icon {
      width: 64px;
      height: 64px;
      border-radius: 16px;
      display: flex;
      align-items: center;
      justify-content: center;
      padding: 8px;
      box-sizing: border-box;
      margin-bottom: 1.5rem;
      color: white;
      background: var(--icon-color, var(--benefits-primary-color));
      box-shadow: 0 8px 24px rgba(59, 130, 246, 0.3);
      transition: all 0.4s ease;
    }
    
    .benefit-icon[data-style="outlined"] {
      background: transparent;
      border: 2px solid var(--icon-color, var(--benefits-primary-color));
      color: var(--icon-color, var(--benefits-primary-color));
      box-shadow: none;
    }
    
    .benefit-icon[data-style="minimal"] {
      background: var(--benefits-accent-color);
      color: var(--benefits-primary-color);
      box-shadow: none;
    }
    
    .benefit-content {
      flex: 1;
    }
    
    .benefit-title {
      font-size: 1.25rem;
      font-weight: 600;
      color: var(--benefits-text-color);
      margin: 0 0 0.75rem 0;
      line-height: 1.3;
      letter-spacing: -0.01em;
      word-break: break-word;
      overflow-wrap: anywhere;
      padding: 0 8px;
      white-space: normal;
    }
    
    .benefit-description {
      font-size: 0.95rem;
      color: #6b7280;
      line-height: 1.6;
      margin: 0;
      word-break: break-word;
    }
    
    .benefits-grid[data-text-align="center"] .benefit-card {
      text-align: center;
    }
    
    .benefits-grid[data-text-align="center"] .benefit-icon {
      margin-left: auto;
      margin-right: auto;
    }
    
    .benefits-grid[data-text-align="right"] .benefit-card {
      text-align: right;
    }
    
    .benefits-grid[data-text-align="right"] .benefit-icon {
      margin-left: auto;
    }
    
    .benefits-empty-state,
    .benefits-error-state {
      text-align: center;
      padding: 3rem 1rem;
      color: #6b7280;
    }
    
    .empty-icon,
    .error-icon {
      font-size: 3rem;
      margin-bottom: 1rem;
    }
    
    @keyframes fadeInUp {
      from {
        opacity: 0;
        transform: translateY(30px);
      }
      to {
        opacity: 1;
        transform: translateY(0);
      }
    }
    
    @media (max-width: 768px) {
      .iluria-payment-benefits {
        padding: 2rem 1rem;
      }
      
      .benefits-grid {
        grid-template-columns: 1fr !important;
        gap: 1.5rem;
      }
      
      .benefit-card {
        padding: 1.5rem;
      }
      
      .benefit-icon {
        width: 56px;
        height: 56px;
        margin-bottom: 1rem;
      }
      
      .benefit-title {
        font-size: 1.125rem;
      }
      
      .benefit-description {
        font-size: 0.875rem;
      }
    }
    
    @media (prefers-reduced-motion: reduce) {
      .benefit-card {
        animation: none;
        opacity: 1;
        transform: none;
      }
    }
    
    .benefit-card:focus {
      outline: 2px solid var(--benefits-primary-color);
      outline-offset: 2px;
    }
  `
  
  document.head.appendChild(style)
}

// Funções auxiliares para geração de HTML
const generateModernHTML = (config) => {
  const colorScheme = COLOR_SCHEMES[config.colorScheme] || COLOR_SCHEMES.blue
  
  const headerHTML = `
    <header class="benefits-header">
      <h2 class="benefits-title">${config.title}</h2>
      ${config.showSubtitle && config.subtitle ? `
        <p class="benefits-subtitle">${config.subtitle}</p>
      ` : ''}
    </header>
  `
  
  const benefitsHTML = generateBenefitsGrid(config, colorScheme)
  
  return `
    <div class="benefits-container" data-layout="${config.layout}" data-spacing="${config.spacing}">
      ${headerHTML}
      ${benefitsHTML}
    </div>
  `
}

const generateBenefitsGrid = (config, colorScheme) => {
  if (config.benefits.length === 0) {
    return `
      <div class="benefits-empty-state">
        <div class="empty-icon">${ICONS_LIBRARY['gift']}</div>
        <h3>Nenhum benefício configurado</h3>
        <p>Configure benefícios para seus clientes no editor</p>
      </div>
    `
  }
  
  const benefitsHTML = config.benefits.map((benefit, index) => 
    generateBenefitCard(benefit, config, colorScheme, index)
  ).join('')
  
  return `
    <div class="benefits-grid" 
         data-columns="${config.columns}" 
         data-card-style="${config.cardStyle}"
         data-text-align="${config.textAlign}">
      ${benefitsHTML}
    </div>
  `
}

const generateBenefitCard = (benefit, config, colorScheme, index) => {
  const iconHTML = config.showIcons ? `
    <div class="benefit-icon" 
         data-style="${config.iconStyle}"
         style="--icon-color: ${benefit.highlight ? colorScheme.secondary : colorScheme.primary}">
      ${ICONS_LIBRARY[benefit.icon] || ICONS_LIBRARY['star']}
    </div>
  ` : ''
  
  const highlightClass = benefit.highlight ? 'benefit-highlighted' : ''
  const animationDelay = `style="--animation-delay: ${index * 100}ms"`
  
  return `
    <article class="benefit-card ${highlightClass}" 
             data-benefit-id="${benefit.id}"
             ${animationDelay}>
      ${iconHTML}
      <div class="benefit-content">
        <h3 class="benefit-title">${benefit.title}</h3>
        <p class="benefit-description">${benefit.description}</p>
      </div>
      ${benefit.highlight ? '<div class="benefit-badge">Popular</div>' : ''}
    </article>
  `
}

// Funções de configuração
const applyDynamicClasses = (element, config) => {
  const colorScheme = COLOR_SCHEMES[config.colorScheme] || COLOR_SCHEMES.blue
  
  element.style.setProperty('--benefits-primary-color', colorScheme.primary)
  element.style.setProperty('--benefits-secondary-color', colorScheme.secondary)
  element.style.setProperty('--benefits-accent-color', colorScheme.accent)
  element.style.setProperty('--benefits-text-color', colorScheme.text)
}

const applyAnimations = (element, config) => {
  if (config.animation === 'none') return
  
  const observer = new IntersectionObserver((entries) => {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        entry.target.classList.add('animate-in')
        observer.unobserve(entry.target)
      }
    })
  }, { threshold: 0.1 })
  
  const cards = element.querySelectorAll('.benefit-card')
  cards.forEach(card => observer.observe(card))
}

const setupInteractivity = (element, config) => {
  const cards = element.querySelectorAll('.benefit-card')
  
  cards.forEach(card => {
    card.addEventListener('click', () => {
      const benefitId = card.getAttribute('data-benefit-id')
      
      if (typeof gtag !== 'undefined') {
        gtag('event', 'benefit_click', {
          event_category: 'component_interaction',
          event_label: benefitId,
          component_title: config.title
        })
      }
    })
    
    card.addEventListener('mouseenter', () => {
      card.style.transform = 'translateY(-8px)'
    })
    
    card.addEventListener('mouseleave', () => {
      card.style.transform = 'translateY(0)'
    })
  })
}

const renderErrorState = (element) => {
  element.innerHTML = `
    <div class="benefits-error-state">
      <div class="error-icon">⚠️</div>
      <h3>Erro ao carregar benefícios</h3>
      <p>Verifique a configuração do componente</p>
    </div>
  `
}

const setupPaymentBenefitsComponent = (element, config) => {
  // Limpa loading state
  element.innerHTML = ''
  
  // Aplica estilos CSS modernos
  injectModernStyles()
  
  // Gera HTML estruturado
  const html = generateModernHTML(config)
  element.innerHTML = html
  
  // Aplica classes dinâmicas
  applyDynamicClasses(element, config)
  
  // Aplica animações
  applyAnimations(element, config)
  
  // Configura interatividade
  setupInteractivity(element, config)
}

// ✅ FUNÇÃO EXPORTADA PARA SISTEMA DE SCRIPTS
export function injectPaymentBenefitsScript(doc, authToken = '') {
  
  if (!doc || !doc.querySelector) {
    console.warn('⚠️ [PaymentBenefits] Documento inválido fornecido')
    return
  }

  const elements = doc.querySelectorAll('[data-component="payment-benefits"]')
  
  if (!elements || elements.length === 0) {

    return
  }


  elements.forEach(element => {
    if (element.hasAttribute('data-initialized')) {
      return
    }
    
    try {
      // Configuração completa modernizada
      const config = {
        title: element.getAttribute('data-title') || 'Por que escolher nossa loja?',
        subtitle: element.getAttribute('data-subtitle') || '',
        showSubtitle: element.getAttribute('data-show-subtitle') !== 'false',
        layout: element.getAttribute('data-layout') || 'grid',
        columns: parseInt(element.getAttribute('data-columns')) || 3,
        benefits: JSON.parse(element.getAttribute('data-benefits') || JSON.stringify(getDefaultBenefits())),
        showIcons: element.getAttribute('data-show-icons') !== 'false',
        iconStyle: element.getAttribute('data-icon-style') || 'filled',
        textAlign: element.getAttribute('data-text-align') || 'center',
        cardStyle: element.getAttribute('data-card-style') || 'modern',
        colorScheme: element.getAttribute('data-color-scheme') || 'blue',
        spacing: element.getAttribute('data-spacing') || 'comfortable',
        animation: element.getAttribute('data-animation') || 'fade-up'
      }
      
      // Aplica funcionalidade modernizada completa
      setupPaymentBenefitsComponent(element, config)
      
      // Marca como inicializado
      element.setAttribute('data-initialized', 'true')
      
      
    } catch (error) {
      console.error('❌ [PaymentBenefits] Erro ao inicializar componente:', error)
      renderErrorState(element)
    }
  })
}

// ✅ FUNÇÃO GLOBAL PARA SISTEMA DE AUTO-INICIALIZAÇÃO
export function initializePaymentBenefitsComponent(element, document) {
  try {
    if (!element || element.hasAttribute('data-initialized')) {
      return
    }
    
    // 🎯 GARANTIR QUE O ELEMENTO TEM data-component
    if (!element.hasAttribute('data-component')) {
      element.setAttribute('data-component', 'payment-benefits')
    }
    
    
    // Configuração completa modernizada
    const config = {
      title: element.getAttribute('data-title') || 'Por que escolher nossa loja?',
      subtitle: element.getAttribute('data-subtitle') || '',
      showSubtitle: element.getAttribute('data-show-subtitle') !== 'false',
      layout: element.getAttribute('data-layout') || 'grid',
      columns: parseInt(element.getAttribute('data-columns')) || 3,
      benefits: JSON.parse(element.getAttribute('data-benefits') || JSON.stringify(getDefaultBenefits())),
      showIcons: element.getAttribute('data-show-icons') !== 'false',
      iconStyle: element.getAttribute('data-icon-style') || 'filled',
      textAlign: element.getAttribute('data-text-align') || 'center',
      cardStyle: element.getAttribute('data-card-style') || 'modern',
      colorScheme: element.getAttribute('data-color-scheme') || 'blue',
      spacing: element.getAttribute('data-spacing') || 'comfortable',
      animation: element.getAttribute('data-animation') || 'fade-up'
    }
    
    // Aplica funcionalidade modernizada completa
    setupPaymentBenefitsComponent(element, config)
    
    // Marca como inicializado
    element.setAttribute('data-initialized', 'true')
    
    
  } catch (error) {
    console.error('❌ [PaymentBenefits] Erro na inicialização global:', error)
    renderErrorState(element)
  }
}
</script>

<script setup>
import { onMounted } from 'vue'

const props = defineProps({
  document: { type: Object, required: true },
  authToken: { type: String, default: '' }
})

onMounted(() => {

})
</script> 