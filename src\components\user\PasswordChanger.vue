<template>
  <ViewContainer
    title="Alterar Senha"
    subtitle="Altere sua senha para manter sua conta segura"
    :icon="SquareUnlock02Icon"
    iconColor="blue"
  >
    <form @submit.prevent="changePassword" class="password-form">
            <div class="form-field">
              <IluriaInputText
                v-model="formData.currentPassword"
                type="password"
                label="Senha Atual"
                placeholder="Digite sua senha atual"
                autocomplete="current-password"
              />
            </div>

            <div class="form-field">
              <IluriaInputText
                v-model="formData.newPassword"
                type="password"
                label="Nova Senha"
                placeholder="Digite sua nova senha"
                autocomplete="new-password"
              />

              <!-- Password Strength Indicator -->
              <div class="password-strength-container">
                <div class="password-strength">
                  <div class="strength-bar">
                    <div
                      class="strength-fill"
                      :class="passwordStrengthClass"
                      :style="{ width: passwordStrengthPercentage + '%' }"
                    ></div>
                  </div>
                  <p v-if="formData.newPassword" class="strength-text" :class="passwordStrengthClass">
                    {{ passwordStrengthText }}
                  </p>
                </div>
              </div>
            </div>

            <div class="form-field">
              <IluriaInputText
                v-model="formData.confirmPassword"
                type="password"
                label="Confirmar Nova Senha"
                placeholder="Confirme sua nova senha"
                autocomplete="new-password"
              />
              <div class="field-error-container">
                <p v-if="formData.confirmPassword && !passwordsMatch" class="field-error">
                  As senhas não coincidem
                </p>
              </div>
            </div>

            <!-- Password Requirements -->
            <div class="password-requirements">
              <h4 class="requirements-title">Sua senha deve conter:</h4>
              <ul class="requirements-list">
                <li :class="{ 'requirement-met': hasMinLength }">
                  <HugeiconsIcon
                    :icon="hasMinLength ? CheckmarkCircle02Icon : Cancel01Icon"
                    size="14"
                    :strokeWidth="1.5"
                  />
                  Pelo menos 8 caracteres
                </li>
                <li :class="{ 'requirement-met': hasUppercase }">
                  <HugeiconsIcon
                    :icon="hasUppercase ? CheckmarkCircle02Icon : Cancel01Icon"
                    size="14"
                    :strokeWidth="1.5"
                  />
                  Uma letra maiúscula
                </li>
                <li :class="{ 'requirement-met': hasLowercase }">
                  <HugeiconsIcon
                    :icon="hasLowercase ? CheckmarkCircle02Icon : Cancel01Icon"
                    size="14"
                    :strokeWidth="1.5"
                  />
                  Uma letra minúscula
                </li>
                <li :class="{ 'requirement-met': hasNumber }">
                  <HugeiconsIcon
                    :icon="hasNumber ? CheckmarkCircle02Icon : Cancel01Icon"
                    size="14"
                    :strokeWidth="1.5"
                  />
                  Um número
                </li>
                <li :class="{ 'requirement-met': hasSpecialChar }">
                  <HugeiconsIcon
                    :icon="hasSpecialChar ? CheckmarkCircle02Icon : Cancel01Icon"
                    size="14"
                    :strokeWidth="1.5"
                  />
                  Um caractere especial
                </li>
              </ul>
            </div>

            <div class="form-actions">
              <IluriaButton
                type="submit"
                color="primary"
                :disabled="!canSubmit || isChanging"
                :loading="isChanging"
                :hugeIcon="LockKeyIcon"
              >
                {{ isChanging ? 'Alterando...' : 'Alterar Senha' }}
              </IluriaButton>
            </div>
      </form>
    </ViewContainer>

    <!-- MFA Verification Modal -->
    <MfaVerificationModal
      v-model:visible="showMfaModal"
      :mfa-type="userMfaType"
      :user-email="authStore.userEmail"
      :title="$t('userSettings.password.mfaTitle')"
      :description="$t('userSettings.password.mfaDescription')"
      operation="change-password"
      :loading="isMfaVerifying"
      @verified="handleMfaVerified"
      @resend-email="handleMfaResend"
    />
</template>

<script setup>
import { ref, reactive, computed } from 'vue'
import { useToast } from '@/services/toast.service'
import { useAuthStore } from '@/stores/auth.store'
import authService from '@/services/auth.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import MfaVerificationModal from '@/components/modals/MfaVerificationModal.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  SquareUnlock02Icon,
  LockKeyIcon,
  CheckmarkCircle02Icon,
  Cancel01Icon,
  SecurityCheckIcon,
  ShieldKeyIcon,
  KeyIcon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const toast = useToast()
const authStore = useAuthStore()

// State
const isChanging = ref(false)

// MFA State
const showMfaModal = ref(false)
const userMfaType = ref(null)
const pendingPasswordData = ref(null)
const isMfaVerifying = ref(false)

const formData = reactive({
  currentPassword: '',
  newPassword: '',
  confirmPassword: ''
})

// Computed
const hasMinLength = computed(() => formData.newPassword.length >= 8)
const hasUppercase = computed(() => /[A-Z]/.test(formData.newPassword))
const hasLowercase = computed(() => /[a-z]/.test(formData.newPassword))
const hasNumber = computed(() => /\d/.test(formData.newPassword))
const hasSpecialChar = computed(() => /[!@#$%^&*(),.?":{}|<>]/.test(formData.newPassword))

const passwordsMatch = computed(() => {
  return formData.newPassword === formData.confirmPassword
})

const passwordStrength = computed(() => {
  let score = 0
  if (hasMinLength.value) score++
  if (hasUppercase.value) score++
  if (hasLowercase.value) score++
  if (hasNumber.value) score++
  if (hasSpecialChar.value) score++
  return score
})

const passwordStrengthPercentage = computed(() => {
  return (passwordStrength.value / 5) * 100
})

const passwordStrengthClass = computed(() => {
  if (passwordStrength.value <= 2) return 'strength-weak'
  if (passwordStrength.value <= 3) return 'strength-medium'
  if (passwordStrength.value <= 4) return 'strength-good'
  return 'strength-strong'
})

const passwordStrengthText = computed(() => {
  if (formData.newPassword === '') return ''
  if (passwordStrength.value <= 2) return 'Senha fraca'
  if (passwordStrength.value <= 3) return 'Senha média'
  if (passwordStrength.value <= 4) return 'Senha boa'
  return 'Senha forte'
})

const canSubmit = computed(() => {
  return formData.currentPassword !== '' &&
         formData.newPassword !== '' &&
         formData.confirmPassword !== '' &&
         passwordsMatch.value &&
         passwordStrength.value >= 4
})

// Methods
const changePassword = async () => {
  if (!canSubmit.value) return
  
  // Verificar se usuário está logado
  if (!authStore.userLoggedIn || !authStore.userEmail) {
    toast.addToast('Erro: usuário não está logado', 'error')
    return
  }
  
  isChanging.value = true
  
  try {
    // PRIMEIRO: Validar senha atual ANTES de qualquer coisa
    const passwordValidation = await authStore.validateCurrentPassword(formData.currentPassword)
    
    if (!passwordValidation.valid) {
      toast.addToast(passwordValidation.message || 'Senha atual incorreta', 'error')
      isChanging.value = false
      return
    }
    
    // SEGUNDO: Verificar se o usuário tem MFA ativo (apenas após validar senha)
    const mfaStatus = await authStore.getUserMfaStatus()
    
    if (mfaStatus.enabled) {
      // Armazenar dados da senha para usar após a verificação MFA
      pendingPasswordData.value = {
        currentPassword: formData.currentPassword,
        newPassword: formData.newPassword,
        confirmPassword: formData.confirmPassword
      }
      
      userMfaType.value = mfaStatus.type
      
      // Se for EMAIL_VERIFICATION, gerar e enviar código inicial
      if (mfaStatus.type === 'EMAIL_VERIFICATION') {
        try {
          await authService.resendMfaCode(authStore.userEmail)
          toast.addToast('Código MFA enviado para seu email', 'info')
        } catch (error) {
          toast.addToast('Erro ao enviar código MFA', 'error')
          isChanging.value = false
          return
        }
      }
      
      showMfaModal.value = true
      isChanging.value = false
      return
    }
    
    // Se não tem MFA, usa o método original
    const response = await authStore.changePassword(
      formData.currentPassword,
      formData.newPassword,
      formData.confirmPassword
    )
    
    if (response.success) {
      toast.addToast(response.message || 'Senha alterada com sucesso!', 'success')
      
      // Reset form apenas em caso de sucesso
      formData.currentPassword = ''
      formData.newPassword = ''
      formData.confirmPassword = ''
    } else {
      // Tratar erros específicos do backend
      let errorMessage = response.message || 'Erro ao alterar senha'
      
      if (response.code === 'INVALID_CURRENT_PASSWORD') {
        errorMessage = 'Senha atual incorreta'
      } else if (response.code === 'PASSWORD_TOO_WEAK') {
        errorMessage = 'Nova senha não atende aos critérios de segurança'
      }
      
      toast.addToast(errorMessage, 'error')
    }
    
  } catch (error) {
    const errorMessage = error.message || 'Erro inesperado ao alterar senha'
    toast.addToast(errorMessage, 'error')
  } finally {
    isChanging.value = false
  }
}

// MFA Handlers
const handleMfaVerified = async (data) => {
  if (!pendingPasswordData.value) {
    toast.addToast('Erro: dados da senha não encontrados', 'error')
    return
  }
  
  isMfaVerifying.value = true
  
  try {
    const response = await authStore.changePasswordWithMfa(
      pendingPasswordData.value.currentPassword,
      pendingPasswordData.value.newPassword,
      pendingPasswordData.value.confirmPassword,
      data.code
    )
    
    if (response.success) {
      toast.addToast(response.message || 'Senha alterada com sucesso!', 'success')
      
      // Reset form e modal
      formData.currentPassword = ''
      formData.newPassword = ''
      formData.confirmPassword = ''
      showMfaModal.value = false
      pendingPasswordData.value = null
      
      // Notificar sobre invalidação das sessões
      toast.addToast('Outras sessões foram desconectadas por segurança', 'info')
    } else {
      let errorMessage = response.message || 'Erro ao alterar senha'
      
      if (response.code === 'INVALID_CURRENT_PASSWORD') {
        errorMessage = 'Senha atual incorreta'
      } else if (response.code === 'PASSWORD_TOO_WEAK') {
        errorMessage = 'Nova senha não atende aos critérios de segurança'
      } else if (response.code === 'INVALID_MFA_TOKEN') {
        errorMessage = 'Código MFA inválido'
      }
      
      toast.addToast(errorMessage, 'error')
    }
  } catch (error) {
    const errorMessage = error.message || 'Erro inesperado ao alterar senha'
    toast.addToast(errorMessage, 'error')
  } finally {
    isMfaVerifying.value = false
  }
}


const handleMfaResend = async (data) => {
  try {
    await authService.resendMfaCode(data.email)
    toast.addToast('Código reenviado com sucesso!', 'success')
  } catch (error) {
    toast.addToast('Erro ao reenviar código', 'error')
  }
}
</script>

<style scoped>
.password-form {
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}



.password-strength-container {
  height: 32px;
  margin-top: 0.375rem;
}

.field-error-container {
  height: 16px;
  margin-top: 0.25rem;
}

.field-error {
  color: #ef4444;
  font-size: 0.75rem;
  margin: 0;
}

.password-strength {
  margin-top: 0.5rem;
}

.strength-bar {
  width: 100%;
  height: 4px;
  background: var(--iluria-color-border);
  border-radius: 2px;
  overflow: hidden;
  margin-bottom: 0.5rem;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.strength-weak {
  background: #ef4444;
}

.strength-fill.strength-medium {
  background: #f59e0b;
}

.strength-fill.strength-good {
  background: #3b82f6;
}

.strength-fill.strength-strong {
  background: #10b981;
}

.strength-text {
  font-size: 0.75rem;
  font-weight: 500;
  margin: 0;
}

.strength-weak {
  color: #ef4444;
}

.strength-medium {
  color: #f59e0b;
}

.strength-good {
  color: #3b82f6;
}

.strength-strong {
  color: #10b981;
}

.password-requirements {
  padding: 1.5rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  margin-top: 0.5rem;
}

.requirements-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.75rem 0;
}

.requirements-list {
  list-style: none;
  margin: 0;
  padding: 0;
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.requirements-list li {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  font-size: 0.75rem;
  color: var(--iluria-color-text-muted);
  transition: color 0.2s ease;
}

.requirements-list li.requirement-met {
  color: #10b981;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}







/* Responsive */
@media (max-width: 768px) {
  .form-actions-top {
    justify-content: stretch;
  }


}
</style>