<script setup>
import { Rocket01Icon,SourceCodeIcon } from '@hugeicons-pro/core-bulk-rounded';
import { useI18n } from 'vue-i18n';
import {HugeiconsIcon} from '@hugeicons/vue';
import { computed } from 'vue';

const { t } = useI18n();

const props = defineProps({
  currentEnvironment: String
});

const emit = defineEmits(['change']);

const switchEnvironment = (environment) => {
  emit('change', environment);
};

const getButtonStyle = (env) => {
  const isActive = props.currentEnvironment === env;
  if (isActive) {
    const fg = env === 'DEVELOP' ? 'var(--iluria-color-button-primary-fg)' : 'var(--iluria-color-button-secondary-fg)';
    return {
      color: fg
    };
  }
  return {
    color: 'var(--iluria-color-text-primary)'
  };
};

const sliderStyle = computed(() => {
  const translate = props.currentEnvironment === 'DEVELOP' ? 'translateX(0%)' : 'translateX(100%)';
  const bg = props.currentEnvironment === 'DEVELOP' ? 'var(--iluria-color-primary)' : 'var(--iluria-color-secondary)';
  return {
    transform: translate,
    backgroundColor: bg,
    color: 'var(--iluria-color-button-primary-fg)'
  };
});
</script>

<template>
  <div class="relative inline-grid grid-cols-2 bg-[var(--iluria-color-sidebar-bg)] rounded-full p-1">
    <!-- Slider -->
    <div class="absolute top-0 left-0 h-full w-1/2 rounded-full transition-transform duration-300 ease-out shadow-sm" :style="sliderStyle"></div>
    <button
      @click="switchEnvironment('DEVELOP')"
      :style="getButtonStyle('DEVELOP')"
      class="relative z-10 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 flex items-center justify-center"
    >
      <HugeiconsIcon :icon="SourceCodeIcon" class="mr-2 text-lg" />
      <span class="whitespace-nowrap">{{ t('fileManager.test') }}</span>
    </button>
    <button
      @click="switchEnvironment('PRODUCTION')"
      :style="getButtonStyle('PRODUCTION')"
      class="relative z-10 px-4 py-2 rounded-full text-sm font-medium transition-colors duration-200 flex items-center justify-center"
    >
      <HugeiconsIcon :icon="Rocket01Icon" class="mr-2 text-lg" />
      <span class="whitespace-nowrap">{{ t('fileManager.production') }}</span>
    </button>
  </div>
</template>
