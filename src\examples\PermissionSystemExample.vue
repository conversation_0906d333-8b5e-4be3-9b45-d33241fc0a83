<template>
  <div class="permission-examples-container p-6 max-w-4xl mx-auto">
    <h1 class="text-3xl font-bold mb-8">Sistema de Permissões - Exemplos de Uso</h1>
    
    <!-- Estado das permissões -->
    <div class="mb-8 p-4 bg-blue-50 rounded-lg">
      <h2 class="text-xl font-semibold mb-4">Estado Atual das Permissões</h2>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div>
          <p><strong>Permissões carregadas:</strong> {{ permissionsLoaded ? 'Sim' : 'Não' }}</p>
          <p><strong>Total de permissões:</strong> {{ userPermissions.length }}</p>
          <p><strong>É admin:</strong> {{ isStoreAdmin ? 'Sim' : 'Não' }}</p>
        </div>
        <div>
          <p><strong>Permissões do usuário:</strong></p>
          <ul class="text-sm mt-2 max-h-32 overflow-y-auto">
            <li v-for="permission in userPermissions.slice(0, 10)" :key="permission" class="text-gray-600">
              • {{ permission }}
            </li>
            <li v-if="userPermissions.length > 10" class="text-gray-500 italic">
              ... e mais {{ userPermissions.length - 10 }} permissões
            </li>
          </ul>
        </div>
      </div>
    </div>

    <!-- Exemplo 1: Usando PermissionGuard -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">1. Componente PermissionGuard</h2>
      
      <div class="space-y-4">
        <!-- Permissão única -->
        <PermissionGuard :required-permissions="[PERMISSIONS.PRODUCT_CREATE.code]">
          <IluriaButton color="primary" :hugeIcon="PackageAddIcon">
            Criar Produto (Visível se tem PRODUCT_CREATE)
          </IluriaButton>
        </PermissionGuard>

        <!-- Múltiplas permissões (AND) -->
        <PermissionGuard :required-permissions="[PERMISSIONS.PRODUCT_EDIT.code, PERMISSIONS.PRODUCT_VIEW.code]">
          <IluriaButton color="secondary" :hugeIcon="PencilEdit01Icon">
            Editar Produto (Visível se tem PRODUCT_EDIT E PRODUCT_VIEW)
          </IluriaButton>
        </PermissionGuard>

        <!-- Qualquer permissão (OR) -->
        <PermissionGuard 
          :required-permissions="[PERMISSIONS.ORDER_VIEW.code, PERMISSIONS.CUSTOMER_VIEW.code]" 
          any-permission
        >
          <IluriaButton color="info" :hugeIcon="AnalysisTextLinkIcon">
            Ver Dashboard (Visível se tem ORDER_VIEW OU CUSTOMER_VIEW)
          </IluriaButton>
        </PermissionGuard>

        <!-- Com fallback -->
        <PermissionGuard 
          :required-permissions="[PERMISSIONS.TEAM_EDIT.code]" 
          show-fallback
          fallback-message="Você precisa de permissão de TEAM_EDIT para gerenciar a equipe."
        >
          <IluriaButton color="warning" :hugeIcon="UserGroupIcon">
            Gerenciar Equipe
          </IluriaButton>
          <template #fallback>
            <div class="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <p class="text-yellow-800">⚠️ Função restrita para administradores da equipe.</p>
            </div>
          </template>
        </PermissionGuard>
      </div>
    </div>

    <!-- Exemplo 2: Usando diretiva v-permission -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">2. Diretiva v-permission</h2>
      
      <div class="space-y-4">
        <!-- Oculta elemento -->
        <div v-permission="PERMISSIONS.PRODUCT_DELETE.code">
          <IluriaButton color="danger" :hugeIcon="Delete01Icon">
            Excluir Produto (Oculto se não tem PRODUCT_DELETE)
          </IluriaButton>
        </div>

        <!-- Desabilita elemento -->
        <IluriaButton 
          v-permission:disable="PERMISSIONS.FINANCIAL_VIEW.code"
          color="primary" 
          :hugeIcon="DollarSquareIcon"
        >
          Ver Relatórios Financeiros (Desabilitado se não tem FINANCIAL_VIEW)
        </IluriaButton>

        <!-- Operação OR -->
        <div v-permission:any="[PERMISSIONS.ORDER_EDIT.code, PERMISSIONS.ORDER_CANCEL.code]">
          <IluriaButton color="secondary" :hugeIcon="ArrangeIcon">
            Ações do Pedido (Visível se tem ORDER_EDIT OU ORDER_CANCEL)
          </IluriaButton>
        </div>
      </div>
    </div>

    <!-- Exemplo 3: Usando composable diretamente -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">3. Usando Composable Diretamente</h2>
      
      <div class="space-y-4">
        <div class="p-4 bg-gray-50 rounded-lg">
          <h3 class="font-semibold mb-2">Verificações de Permissão:</h3>
          <ul class="space-y-1 text-sm">
            <li>
              <strong>Pode criar produtos:</strong> 
              <span :class="canCreateProduct ? 'text-green-600' : 'text-red-600'">
                {{ canCreateProduct ? 'Sim' : 'Não' }}
              </span>
            </li>
            <li>
              <strong>Pode ver clientes:</strong> 
              <span :class="canViewCustomers ? 'text-green-600' : 'text-red-600'">
                {{ canViewCustomers ? 'Sim' : 'Não' }}
              </span>
            </li>
            <li>
              <strong>Pode gerenciar vendas:</strong> 
              <span :class="canManageSales ? 'text-green-600' : 'text-red-600'">
                {{ canManageSales ? 'Sim' : 'Não' }}
              </span>
            </li>
          </ul>
        </div>

        <!-- Lista filtrada por permissões -->
        <div class="p-4 bg-white border rounded-lg">
          <h3 class="font-semibold mb-2">Actions Disponíveis (Filtradas por Permissão):</h3>
          <div class="grid grid-cols-1 md:grid-cols-2 gap-2">
            <IluriaButton 
              v-for="action in availableActions" 
              :key="action.name"
              size="small"
              variant="outline"
              :hugeIcon="action.icon"
              class="justify-start"
            >
              {{ action.name }}
            </IluriaButton>
          </div>
          <p v-if="availableActions.length === 0" class="text-gray-500 text-sm mt-2">
            Nenhuma ação disponível com suas permissões atuais.
          </p>
        </div>
      </div>
    </div>

    <!-- Exemplo 4: Navegação condicional -->
    <div class="mb-8">
      <h2 class="text-xl font-semibold mb-4">4. Navegação Condicional</h2>
      
      <div class="space-y-4">
        <div class="p-4 bg-white border rounded-lg">
          <h3 class="font-semibold mb-2">Rotas Disponíveis:</h3>
          <div class="space-y-2">
            <router-link 
              v-if="hasPermission(PERMISSIONS.PRODUCT_VIEW)"
              to="/products"
              class="block text-blue-600 hover:text-blue-800"
            >
              → Lista de Produtos
            </router-link>
            <router-link 
              v-if="hasPermission(PERMISSIONS.CUSTOMER_VIEW)"
              to="/customer-list"
              class="block text-blue-600 hover:text-blue-800"
            >
              → Lista de Clientes
            </router-link>
            <router-link 
              v-if="hasPermission(PERMISSIONS.TEAM_VIEW)"
              to="/team"
              class="block text-blue-600 hover:text-blue-800"
            >
              → Gerenciar Equipe
            </router-link>
            <p v-if="!hasAnyNavigationPermission" class="text-gray-500 text-sm">
              Nenhuma rota de navegação disponível com suas permissões.
            </p>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useGlobalPermissions } from '@/composables/usePermissions';
import { PERMISSIONS } from '@/constants/permissions';
import PermissionGuard from '@/components/common/PermissionGuard.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { HugeiconsIcon } from '@hugeicons/vue';
import {
  PackageAddIcon,
  PencilEdit01Icon,
  Delete01Icon,
  UserGroupIcon,
  AnalysisTextLinkIcon,
  DollarSquareIcon,
  ArrangeIcon
} from '@hugeicons-pro/core-stroke-standard';

// Usar o composable de permissões
const { 
  hasPermission, 
  hasAllPermissions, 
  hasAnyPermission,
  userPermissions,
  permissionsLoaded,
  isStoreAdmin,
  PERMISSIONS: PERMS 
} = useGlobalPermissions();

// Verificações específicas
const canCreateProduct = computed(() => hasPermission(PERMISSIONS.PRODUCT_CREATE));
const canViewCustomers = computed(() => hasPermission(PERMISSIONS.CUSTOMER_VIEW));
const canManageSales = computed(() => 
  hasAnyPermission([PERMISSIONS.ORDER_VIEW, PERMISSIONS.ORDER_EDIT])
);

// Lista de ações possíveis
const allActions = [
  { 
    name: 'Criar Produto', 
    icon: PackageAddIcon, 
    requiredPermissions: [PERMISSIONS.PRODUCT_CREATE] 
  },
  { 
    name: 'Editar Produto', 
    icon: PencilEdit01Icon, 
    requiredPermissions: [PERMISSIONS.PRODUCT_EDIT] 
  },
  { 
    name: 'Ver Clientes', 
    icon: UserGroupIcon, 
    requiredPermissions: [PERMISSIONS.CUSTOMER_VIEW] 
  },
  { 
    name: 'Gerenciar Equipe', 
    icon: UserGroupIcon, 
    requiredPermissions: [PERMISSIONS.TEAM_EDIT] 
  },
  { 
    name: 'Ver Financeiro', 
    icon: DollarSquareIcon, 
    requiredPermissions: [PERMISSIONS.FINANCIAL_VIEW] 
  }
];

// Filtra ações baseado nas permissões
const availableActions = computed(() => {
  return allActions.filter(action => {
    return hasAnyPermission(action.requiredPermissions);
  });
});

// Verifica se tem alguma permissão de navegação
const hasAnyNavigationPermission = computed(() => {
  return hasAnyPermission([
    PERMISSIONS.PRODUCT_VIEW,
    PERMISSIONS.CUSTOMER_VIEW,
    PERMISSIONS.TEAM_VIEW
  ]);
});
</script>

<style scoped>
.permission-examples-container {
  font-family: 'Inter', sans-serif;
}
</style>