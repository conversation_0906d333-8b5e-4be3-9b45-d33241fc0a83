<template>
  <div class="origin-cep-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="t('originCep.title')"
      subtitle="Configure o CEP de origem para cálculo de frete dos produtos"
      :showSave="true"
      :saveText="t('save')"
      @save-click="handleSubmit"
    />

    <!-- Main Content -->
    <div v-if="isLoading" class="loading-container">
      <Spinner />
    </div>

    <div v-else class="form-container">
      <ViewContainer 
        title="CEP de Origem"
        subtitle="Informe o CEP de onde os produtos serão enviados"
        :icon="LocationIcon"
        iconColor="orange"
      >
        <div class="form-field">
          <IluriaInputText
            id="originCep"
            name="originCep"
            label="CEP de Origem"
            type="text"
            v-model="form.cep"
            :placeholder="t('originCep.placeholder')"
          />
        </div>
      </ViewContainer>
    </div>

    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import Spinner from '@/components/Spinner.vue';
import { ref, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToast } from '@/services/toast.service';
import cepService from '@/services/cep.service';
import { useRouter } from 'vue-router';
import { 
  FloppyDiskIcon,
  LocationIcon
} from '@hugeicons-pro/core-stroke-rounded';

const router = useRouter()

const { t } = useI18n()

const toast = useToast();

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

// Modal control functions
const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const isLoading = ref(true);
const saving = ref(false);

const form = ref({
    id: null,
    cep: ''
});

async function handleSubmit() {
    if (saving.value) return;
    
    saving.value = true;
    try {
        if (form.value.id === null) {
            const response = await cepService.createOriginCep(form.value)
            form.value.id = response.id 
            toast.showSuccess(t('originCep.createSuccess'))
        } else {
            await cepService.updateOriginCep(form.value)
            toast.showSuccess(t('originCep.updateSuccess'))
        }
        await loadOriginCep()
    } catch (error) {
        console.error('Error saving CEP:', error)
        toast.showError(t(form.value.id === null ? 'originCep.createError' : 'originCep.updateError'))
        throw error
    } finally {
        saving.value = false;
    }
}

async function loadOriginCep() {
    try {
        isLoading.value = true;
        const response = await cepService.getOriginCep()
        if (response) {
            form.value = response
        }
    } catch (error) {
        console.error('Error loading CEP:', error)
        toast.showError(t('originCep.loadError'))
    } finally {
        isLoading.value = false;
    }
}

onMounted(() => {
    loadOriginCep()
})
</script>

<style scoped>
.origin-cep-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



.header-content {
  flex: 1;
  min-width: 0;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  line-height: 1.4;
  word-wrap: break-word;
  hyphens: auto;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-shrink: 0;
}

/* Loading Container */
.loading-container {
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 200px;
}

/* Form Container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Form Fields */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* Responsive */
@media (max-width: 768px) {
  .origin-cep-container {
    padding: 16px;
  }
  
  .page-header {
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 15px;
    margin: 6px 0 0 0;
  }
  
  .form-container {
    gap: 20px;
  }
}

@media (max-width: 640px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: flex-end;
    align-self: flex-end;
  }
}

@media (max-width: 480px) {
  .origin-cep-container {
    padding: 12px;
  }
  
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
    margin: 8px 0 0 0;
  }
  
  .form-container {
    gap: 16px;
  }
}

/* Form validation styles */
:deep(.p-form-invalid) {
  border-color: var(--iluria-color-error) !important;
}

:deep(.p-form-error) {
  color: var(--iluria-color-error);
  font-size: 12px;
  margin-top: 4px;
}
</style>
