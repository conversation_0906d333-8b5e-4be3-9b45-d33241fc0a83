<template>
  <div class="border border-[var(--iluria-color-border)] rounded-lg p-4 bg-[var(--iluria-color-surface)] measurement-table-preview">
    <!-- Preview para tipo imagem -->
    <div v-if="measurementTable.type === 'IMAGE'" class="text-center">
      <div v-if="measurementTable.imageUrl" class="max-w-full">
        <img 
          :src="measurementTable.imageUrl" 
          :alt="measurementTable.name || t('measurementTable.image')"
          class="max-w-full h-auto mx-auto rounded-lg shadow-md"
          style="max-height: 600px;"
        />
      </div>
      <div v-else class="py-8 text-gray-500">
        <div class="mx-auto mb-4 w-16 h-16 text-gray-300 flex items-center justify-center">
          <svg class="w-16 h-16" fill="currentColor" viewBox="0 0 20 20">
            <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
          </svg>
        </div>
        <p>{{ t('measurementTable.noImageSelected') }}</p>
      </div>
    </div>

    <!-- Preview para tipo estruturado com layout lado a lado -->
    <div v-else-if="measurementTable.type === 'STRUCTURED' && hasStructuredData" class="grid grid-cols-1 lg:grid-cols-3 gap-4">
      <!-- Tabela de medidas -->
      <div class="lg:col-span-2 overflow-x-auto">
        <table class="w-full border-collapse border border-[var(--iluria-color-border)] bg-[var(--iluria-color-surface)]">
          <thead>
            <tr class="bg-[var(--iluria-color-surface-hover)] text-[var(--iluria-color-text-secondary)]">
              <th class="border border-[var(--iluria-color-border)] px-4 py-2 text-left font-medium w-24">
                {{ t('measurementTable.size') }}
              </th>
              <th 
                v-for="(column, columnIndex) in measurementTable.columns" 
                :key="column.id"
                class="border border-[var(--iluria-color-border)] px-4 py-2 text-center font-medium cursor-pointer transition-colors relative group"
                :class="{ 
                  'bg-[var(--iluria-color-hover)]': hoveredColumn === columnIndex,
                  'hover:bg-[var(--iluria-color-surface-hover)]': hoveredColumn !== columnIndex 
                }"
                @mouseenter="onColumnHover(column, columnIndex)"
                @mouseleave="onColumnLeave"
              >
                <div class="flex flex-col items-center gap-1">
                  <span>{{ column.label }}</span>
                  <span v-if="column.unit" class="text-xs text-gray-500">({{ column.unit }})</span>
                </div>
              </th>
            </tr>
          </thead>
          <tbody>
            <tr v-for="row in measurementTable.rows" :key="row.id">
              <td class="border border-[var(--iluria-color-border)] px-4 py-2 font-medium bg-[var(--iluria-color-surface-hover)] text-[var(--iluria-color-text-primary)]">
                {{ row.sizeLabel }}
              </td>
              <td 
                v-for="(column, columnIndex) in measurementTable.columns" 
                :key="column.id"
                class="border border-[var(--iluria-color-border)] px-4 py-2 text-center cursor-pointer transition-colors"
                :class="{ 
                  'bg-[var(--iluria-color-hover)]': hoveredColumn === columnIndex,
                  'hover:bg-[var(--iluria-color-surface-hover)]': hoveredColumn !== columnIndex 
                }"
                @mouseenter="onColumnHover(column, columnIndex)"
                @mouseleave="onColumnLeave"
              >
                {{ getValueForRowColumn(row, column) || '-' }}
              </td>
            </tr>
          </tbody>
        </table>
      </div>

      <!-- Área de visualização da imagem -->
      <div class="lg:col-span-1">
        <div class="bg-[var(--iluria-color-surface)] rounded-lg p-3 flex flex-col h-fit">
          
          <!-- Imagem de visualização -->
          <div
            :class="[
              'flex items-center justify-center rounded-lg h-48',
              hasImage ? 'border border-[var(--iluria-color-border)] bg-[var(--iluria-color-surface)]' : 'bg-[var(--iluria-color-surface-hover)] border-2 border-dashed border-[var(--iluria-color-border)]',
              'w-48 mx-auto'
            ]"
          >
            <div v-if="currentImage" class="w-full h-full flex items-center justify-center p-2">
              <img 
                :src="currentImage" 
                :alt="currentImageAlt"
                class="max-w-full max-h-full object-contain rounded"
              />
            </div>
            <div v-else class="text-center text-[var(--iluria-color-text-secondary)] p-4">
              <!-- Imagem padrão do produto -->
              <div class="w-full h-full flex items-center justify-center">
                <img 
                  v-if="defaultProductImage"
                  :src="defaultProductImage" 
                  alt="Produto"
                  class="max-w-full max-h-full object-contain rounded"
                />
                <div v-else class="flex flex-col items-center">
                  <div class="mx-auto mb-2 w-8 h-8 text-gray-300 flex items-center justify-center">
                    <svg class="w-8 h-8" fill="currentColor" viewBox="0 0 20 20">
                      <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clip-rule="evenodd" />
                    </svg>
                  </div>
                  <p class="text-xs">{{ t('measurementTable.imagePreviewDescription') }}</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Estado vazio -->
    <div v-else class="text-center py-8 text-gray-500">
      <div class="mx-auto mb-4 w-12 h-12 text-[var(--iluria-color-text-tertiary)] flex items-center justify-center">
        <svg class="w-12 h-12" fill="currentColor" viewBox="0 0 20 20">
          <path fill-rule="evenodd" d="M3 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V4zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1V8zm0 4a1 1 0 011-1h12a1 1 0 011 1v2a1 1 0 01-1 1H4a1 1 0 01-1-1v-2z" clip-rule="evenodd" />
        </svg>
      </div>
      <p class="text-[var(--iluria-color-text-secondary)]">{{ t('measurementTable.previewEmpty') }}</p>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  measurementTable: {
    type: Object,
    default: () => ({})
  },
  productImages: {
    type: Array,
    default: () => []
  }
})

const hoveredColumn = ref(null)
const currentImage = ref(null)
const currentImageAlt = ref('')

// Existe alguma imagem a ser exibida?
const hasImage = computed(() => {
  return !!currentImage.value || !!defaultProductImage.value
})

// Imagem padrão do produto (primeira imagem disponível)
const defaultProductImage = computed(() => {
  if (props.productImages && props.productImages.length > 0) {
    return props.productImages[0]
  }
  return null
})

const hasStructuredData = computed(() => {
  return props.measurementTable.columns?.length > 0 && props.measurementTable.rows?.length > 0
})

const getValueForRowColumn = (row, column) => {
  if (!row.values || !props.measurementTable.columns) return null
  
  // Buscar por posição da coluna
  const columnIndex = props.measurementTable.columns.findIndex(col => col.id === column.id)
  if (columnIndex === -1) return null
  
  // Verificar se existe valor nessa posição
  if (row.values[columnIndex]) {
    return row.values[columnIndex].value
  }
  
  // Fallback: buscar por columnId (para dados carregados do backend)
  const value = row.values.find(v => v.columnId === column.id)
  return value ? value.value : null
}

const onColumnHover = (column, columnIndex) => {
  hoveredColumn.value = columnIndex
  
  // Se a coluna tem uma imagem, mostrar ela
  if (column.imageUrl) {
    currentImage.value = column.imageUrl
    currentImageAlt.value = column.label
  } else {
    currentImageAlt.value = column.label
  }
}

const onColumnLeave = () => {
  hoveredColumn.value = null
  currentImage.value = null
  currentImageAlt.value = ''
}

</script>

<style scoped>
table {
  font-size: 14px;
  table-layout: fixed;
}

th, td {
  min-width: 80px;
  transition: background-color 0.2s ease;
}

/* Responsividade */
@media (max-width: 1024px) {
  .grid {
    grid-template-columns: 1fr;
  }
  .h-48 {
    height: 200px;
  }
}
</style> 
