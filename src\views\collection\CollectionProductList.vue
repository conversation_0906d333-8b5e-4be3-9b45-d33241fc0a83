<template>
    <div v-if="isLoading" class="page-container">
        <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('product.loading') }}</span>
        </div>
    </div>

    <div v-else class="page-container">
        <!-- Page Header -->
        <IluriaHeader
            :title="t('collectionProduct.list.title')"
            :subtitle="t('collectionProduct.list.subtitle')"
            :showAdd="true"
            :addText="t('collectionProduct.list.new')"
            @add-click="newCollectionProduct"
        />

        <!-- Main Content -->

            <IluriaDataTable :value="sortedCollectionProducts" :columns="columns">
                <template #header-name>
                    <span class="column-header" data-sortable="true" @click="toggleSort('name')">
                        {{ t('collectionProduct.list.name') }}
                        <span v-if="sortField === 'name'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                    </span>
                </template>
                <template #header-productsLength>
                    <span class="column-header" data-sortable="true" @click="toggleSort('productsLength')">
                        {{ t('collectionProduct.list.productsLength') }}
                        <span v-if="sortField === 'productsLength'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                    </span>
                </template>
                <template #header-condition>
                    <span class="column-header">
                        {{ t('collectionProduct.list.condition') }}
                    </span>
                </template>
                <template #header-actions>
                    <span class="column-header">{{ t('actions') }}</span>
                </template>

                <template #column-name="{ data }">
                    <span class="collectionClickableName" @click="editCollectionProduct(data)">{{ data.name }}</span>
                </template>

                <template #column-productsLength="{ data }">
                    <span>{{ data.productsLength || 0 }}</span>
                </template>

                <template #column-condition="{ data }">
                    <span>{{ "Nenhuma condição" }}</span>
                </template>

                <template #column-actions="{ data }">
                    <div class="action-buttons">
                        <IluriaButton 
                            color="text-primary" 
                            size="small" 
                            :hugeIcon="PencilEdit01Icon" 
                            @click.prevent="editCollectionProduct(data)"
                            :title="t('edit')"
                        />
                        <IluriaButton 
                            color="text-danger" 
                            size="small" 
                            :hugeIcon="Delete01Icon" 
                            @click.prevent="confirmDelete(data)"
                            :title="t('delete')"
                        />
                    </div>
                </template>

                <!-- Empty State -->
                <template #empty>
                    <div class="empty-state">
                        <h3 class="empty-title">{{ t('collectionProduct.list.noCollectionsFound') }}</h3>
                        <p class="empty-description">{{ t('collectionProduct.list.noCollectionsDescription') }}</p>
                        <IluriaButton @click="newCollectionProduct" :hugeIcon="PlusSignSquareIcon" class="mt-4">
                            {{ t('collectionProduct.list.new') }}
                        </IluriaButton>
                    </div>
                </template>
            </IluriaDataTable>

            <IluriaPagination 
                v-if="totalPages > 1"
                :current-page="currentPage"
                :total-pages="totalPages"
                @go-to-page="changePage"
            />
    </div>

    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
</template>

<script setup>
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import collectionService from '@/services/collection.service.js';
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue';
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import { Delete01Icon, PencilEdit01Icon, PlusSignSquareIcon, Folder01Icon } from '@hugeicons-pro/core-bulk-rounded';
import { useI18n } from 'vue-i18n';
import { ref, onMounted, computed } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from '@/services/toast.service';

const router = useRouter();
const { t } = useI18n();
const toast = useToast();

const isLoading = ref(false);

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const collectionProducts = ref([]);

const currentPage = ref(0);
const totalPages = ref(0);

const sortField = ref(null);
const sortOrder = ref(null);

const toggleSort = (field) => {
  if (sortField.value === field) {
    sortOrder.value = sortOrder.value === 1 ? -1 : 1;
  } else {
    sortField.value = field;
    sortOrder.value = 1;
  }
};

const sortedCollectionProducts = computed(() => {
  if (!sortField.value) {
    return collectionProducts.value;
  }
  const sorted = [...collectionProducts.value].sort((a, b) => {
    const valA = a[sortField.value];
    const valB = b[sortField.value];
    if (valA < valB) return -1 * sortOrder.value;
    if (valA > valB) return 1 * sortOrder.value;
    return 0;
  });
  return sorted;
});

const columns = [
  { field: 'name', headerClass: 'col-flex', class: 'col-flex' },
  { field: 'productsLength', headerClass: 'col-medium', class: 'col-medium' },
  { field: 'condition', headerClass: 'col-large', class: 'col-large' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
]

function newCollectionProduct() {
    router.push(`collection-product-form`);
}

function editCollectionProduct(collectionProduct) {
    router.push(`collection-product-form/${collectionProduct.id}`);
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title || t('confirmDialog.defaultTitle')
    confirmModalConfirmText.value = t('delete')
    confirmModalCancelText.value = t('cancel')
    confirmModalType.value = 'error'
    confirmCallback.value = onConfirm
    showConfirmModal.value = true
}

const handleConfirm = () => {
    if (confirmCallback.value) {
        confirmCallback.value()
    }
    showConfirmModal.value = false
}

const handleCancel = () => {
    showConfirmModal.value = false
}

function confirmDelete(collectionProduct) {
    showConfirmDanger(
        `${t('collectionProduct.list.confirmDeleteMessage')} ${collectionProduct.name} ?`,
        null,
        () => deleteCollectionProduct(collectionProduct)
    );
}

async function deleteCollectionProduct(collectionProduct) {
    try{
        await collectionService.deleteCollection(collectionProduct.id);
        toast.showSuccess(t('collectionProduct.list.deleteSuccess'));
        await loadCollectionProducts();
    } catch(error) {
        toast.showError(t('collectionProduct.list.deleteError'));
    }
}

async function loadCollectionProducts() {
    try {
        isLoading.value = true;
        const collections = await collectionService.getAllCollections(currentPage.value, 5);
        collectionProducts.value = collections.content;
        totalPages.value = collections.totalPages;
    } catch(error) {
        toast.showError(t('collectionProduct.list.loadError'));
    } finally {
        isLoading.value = false;
    }
}

function changePage(page) {
    currentPage.value = page
    loadCollectionProducts()
}

onMounted(() => {
    loadCollectionProducts()
})
</script>

<style scoped>
.page-container {
    padding: 24px;
}



.header-content {
    flex: 1;
}

.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
    line-height: 1.2;
}

.page-subtitle {
    font-size: 16px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
    line-height: 1.4;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 12px;
}

/* Loading State */
.loading-state {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 12px;
    padding: 64px;
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
}

.loading-spinner {
    width: 20px;
    height: 20px;
    border: 2px solid var(--iluria-color-border);
    border-top: 2px solid var(--iluria-color-primary);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Column width definitions */
:deep(.col-flex) { width: auto; text-align: left; }
:deep(.col-medium) { width: 180px; text-align: center; }
:deep(.col-large) { width: 220px; text-align: center; }
:deep(.col-actions) { width: 120px; text-align: center; }

/* Column header style */
:deep(.p-datatable th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.p-datatable th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* General table styling */
:deep(.p-datatable) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.p-datatable .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.p-datatable .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.p-datatable .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.p-datatable .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.p-datatable .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.p-datatable .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.p-datatable .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}

/* Clickable Collection Name */
.collectionClickableName {
    cursor: pointer;
    color: var(--iluria-color-text-primary);
    font-weight: 500;
    transition: color 0.2s ease;
}

.collectionClickableName:hover {
    color: var(--iluria-color-primary);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 48px 24px;
}

.empty-title {
    font-size: 18px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
    margin: 0 0 8px 0;
}

.empty-description {
    font-size: 14px;
    color: var(--iluria-color-text-secondary);
    margin: 0 0 24px 0;
}

/* Utility classes */
.mt-4 {
    margin-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    
    .page-header {
        flex-direction: column;
        align-items: stretch;
        gap: 16px;
        margin-bottom: 24px;
        padding-bottom: 16px;
    }
    
    .page-title {
        font-size: 24px;
    }
    
    .page-subtitle {
        font-size: 15px;
        margin: 6px 0 0 0;
    }
    
    .header-actions {
        justify-content: flex-end;
        align-self: flex-end;
    }
}

@media (max-width: 640px) {
    .header-actions {
        justify-content: stretch;
        align-self: stretch;
    }
}
</style>
