<template>
  <div class="iluria-checkbox-wrapper">
    <div class="checkbox-container">
      <input
        :id="checkboxId"
        :name="name || checkboxId"
        v-model="model"
        type="checkbox"
        :disabled="disabled"
        :required="required"
        :aria-describedby="ariaDescribedBy"
        class="iluria-checkbox"
        @change="handleChange"
      />
      
      <label 
        :for="checkboxId" 
        class="checkbox-label"
        :class="{ 'disabled': disabled }"
      >
        <span class="checkbox-text">{{ label }}</span>
        <span v-if="description" class="checkbox-description">{{ description }}</span>
      </label>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'

const model = defineModel()

const props = defineProps({
  id: {
    type: String,
    default: null
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    required: true
  },
  description: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  ariaDescribedBy: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'change'])

const checkboxId = computed(() => props.id || `iluria-checkbox-${Math.random().toString(36).substring(2, 11)}`)

const handleChange = (event) => {
  const checked = event.target.checked
  model.value = checked
  emit('change', checked)
}
</script>

<style scoped>
.iluria-checkbox-wrapper {
  position: relative;
  width: 100%;
}

.checkbox-container {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.iluria-checkbox {
  width: 1rem;
  height: 1rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
  border: 2px solid var(--iluria-color-input-border, #d1d5db);
  border-radius: 4px;
  background-color: var(--iluria-color-input-bg, #ffffff);
  cursor: pointer;
  transition: all 0.2s ease;
  appearance: none;
  position: relative;
}

.iluria-checkbox:hover:not(:disabled) {
  border-color: var(--iluria-color-input-border-hover, #9ca3af);
}

.iluria-checkbox:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus, #3b82f6);
  box-shadow: 0 0 0 3px var(--iluria-color-input-shadow-focus, rgba(59, 130, 246, 0.1));
}

.iluria-checkbox:checked {
  background-color: var(--iluria-color-input-border-focus, #3b82f6);
  border-color: var(--iluria-color-input-border-focus, #3b82f6);
}

.iluria-checkbox:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 0.375rem;
  height: 0.25rem;
  border: 2px solid white;
  border-top: none;
  border-right: none;
  transform: translate(-50%, -60%) rotate(-45deg);
}

.iluria-checkbox:disabled {
  background-color: var(--iluria-color-input-bg-disabled, #f9fafb);
  border-color: var(--iluria-color-input-border, #e5e7eb);
  cursor: not-allowed;
  opacity: 0.6;
}

.iluria-checkbox:disabled:checked {
  background-color: var(--iluria-color-text-muted, #9ca3af);
  border-color: var(--iluria-color-text-muted, #9ca3af);
}

.checkbox-label {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  flex: 1;
  line-height: 1.5;
}

.checkbox-label.disabled {
  cursor: not-allowed;
  opacity: 0.6;
}

.checkbox-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-input-text, #374151);
}

.checkbox-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-muted, #6b7280);
  margin-top: 0.25rem;
}

/* Dark mode support */
.theme-dark .iluria-checkbox {
  background-color: var(--iluria-color-input-bg);
  border-color: var(--iluria-color-input-border);
}

.theme-dark .checkbox-text {
  color: var(--iluria-color-input-text);
}

.theme-dark .checkbox-description {
  color: var(--iluria-color-text-muted);
}

/* Error state */
.iluria-checkbox.error {
  border-color: var(--iluria-color-error, #ef4444);
}

.iluria-checkbox.error:focus {
  border-color: var(--iluria-color-error, #ef4444);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success state */
.iluria-checkbox.success {
  border-color: var(--iluria-color-success, #10b981);
}

.iluria-checkbox.success:focus {
  border-color: var(--iluria-color-success, #10b981);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .iluria-checkbox {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .iluria-checkbox {
    border-width: 3px;
  }
}

/* Focus visible for keyboard navigation */
.iluria-checkbox:focus-visible {
  outline: 2px solid var(--iluria-color-input-border-focus, #3b82f6);
  outline-offset: 2px;
}
</style> 