<template>
  <footer class="w-full bg-white border-t border-gray-100 mt-16">
    <div class="max-w-7xl mx-auto px-6 py-8">
      <!-- Main Footer Content -->
      <div class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        
        <!-- Company Info -->
        <div class="space-y-4">
          <div class="flex items-center">
            <img src="@/assets/img/logo-iluria-v10.svg" alt="Logo Iluria" class="h-10 w-auto" />
          </div>
          <p class="text-sm text-gray-600 leading-relaxed">
            Plataforma completa de e-commerce para criar e gerenciar sua loja online. 
            Transforme sua ideia em um negócio de sucesso.
          </p>
          <div class="flex space-x-4">
            <a href="#" class="social-link" :title="t('footer.followFacebook')">
              <HugeiconsIcon :icon="Facebook01Icon" :size="20" />
            </a>
            <a href="#" class="social-link" :title="t('footer.followInstagram')">
              <HugeiconsIcon :icon="InstagramIcon" :size="20" />
            </a>
            <a href="#" class="social-link" :title="t('footer.followTwitter')">
              <HugeiconsIcon :icon="NewTwitterIcon" :size="20" />
            </a>
            <a href="#" class="social-link" :title="t('footer.followLinkedin')">
              <HugeiconsIcon :icon="Linkedin01Icon" :size="20" />
            </a>
          </div>
        </div>

        <!-- E-commerce Solutions -->
        <div class="space-y-4">
          <h3 class="footer-title">{{ t('footer.ecommerceSolutions') }}</h3>
          <ul class="space-y-3">
            <li>
              <RouterLink to="/products/new" class="footer-link">
                <HugeiconsIcon :icon="ShoppingCart01Icon" :size="16" class="mr-2" />
                {{ t('footer.createStore') }}
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/orders" class="footer-link">
                <HugeiconsIcon :icon="AnalyticsUpIcon" :size="16" class="mr-2" />
                {{ t('footer.salesManagement') }}
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/customer/coupon-manager" class="footer-link">
                <HugeiconsIcon :icon="DiscountIcon" :size="16" class="mr-2" />
                {{ t('footer.promotions') }}
              </RouterLink>
            </li>
            <li>
              <RouterLink to="/settings/shipping" class="footer-link">
                <HugeiconsIcon :icon="TruckDeliveryIcon" :size="16" class="mr-2" />
                {{ t('footer.shippingConfig') }}
              </RouterLink>
            </li>
          </ul>
        </div>

        <!-- Support & Resources -->
        <div class="space-y-4">
          <h3 class="footer-title">{{ t('footer.supportResources') }}</h3>
          <ul class="space-y-3">
            <li>
              <a href="/docs" class="footer-link">
                <HugeiconsIcon :icon="BookOpen01Icon" :size="16" class="mr-2" />
                {{ t('footer.documentation') }}
              </a>
            </li>
            <li>
              <a href="/blog" class="footer-link">
                <HugeiconsIcon :icon="Edit01Icon" :size="16" class="mr-2" />
                {{ t('footer.blog') }}
              </a>
            </li>
            <li>
              <a href="/support" class="footer-link">
                <HugeiconsIcon :icon="CustomerServiceIcon" :size="16" class="mr-2" />
                {{ t('footer.support') }}
              </a>
            </li>
            <li>
              <a href="/tutorials" class="footer-link">
                <HugeiconsIcon :icon="PlayIcon" :size="16" class="mr-2" />
                {{ t('footer.tutorials') }}
              </a>
            </li>
          </ul>
        </div>

        <!-- Legal & Company -->
        <div class="space-y-4">
          <h3 class="footer-title">{{ t('footer.legalCompany') }}</h3>
          <ul class="space-y-3">
            <li>
              <a href="/privacy" class="footer-link">
                <HugeiconsIcon :icon="SecurityLockIcon" :size="16" class="mr-2" />
                {{ t('footer.privacyPolicy') }}
              </a>
            </li>
            <li>
              <a href="/terms" class="footer-link">
                <HugeiconsIcon :icon="Agreement01Icon" :size="16" class="mr-2" />
                {{ t('footer.termsOfService') }}
              </a>
            </li>
            <li>
              <a href="/about" class="footer-link">
                <HugeiconsIcon :icon="UserGroupIcon" :size="16" class="mr-2" />
                {{ t('footer.aboutUs') }}
              </a>
            </li>
            <li>
              <a href="/contact" class="footer-link">
                <HugeiconsIcon :icon="Mail01Icon" :size="16" class="mr-2" />
                {{ t('footer.contact') }}
              </a>
            </li>
          </ul>
        </div>
      </div>

      <!-- Bottom Bar -->
      <div class="border-t border-gray-100 mt-8 pt-6">
        <div class="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
          
          <!-- Copyright -->
          <div class="flex items-center space-x-4">
            <p class="text-sm text-gray-500">
              © {{ currentYear }} Iluria. {{ t('footer.allRightsReserved') }}
            </p>
            <div class="hidden md:flex items-center space-x-2 text-sm text-gray-500">
              <HugeiconsIcon :icon="LocationIcon" :size="14" />
              <span>Brasil</span>
            </div>
          </div>

          <!-- Quick Stats -->
          <div class="flex items-center space-x-6 text-sm text-gray-500">
            <div class="flex items-center space-x-1">
              <HugeiconsIcon :icon="ShoppingCart01Icon" :size="14" />
              <span>{{ t('footer.storesCreated', { count: '10.000+' }) }}</span>
            </div>
            <div class="flex items-center space-x-1">
              <HugeiconsIcon :icon="ChartUpIcon" :size="14" />
              <span>{{ t('footer.salesProcessed', { amount: 'R$ 100M+' }) }}</span>
            </div>
          </div>

          <!-- Version Info -->
          <div class="flex items-center space-x-2 text-sm text-gray-400">
            <HugeiconsIcon :icon="CodeIcon" :size="14" />
            <span>v2.0.0</span>
          </div>
        </div>
      </div>
    </div>
  </footer>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  ShoppingCart01Icon,
  AnalyticsUpIcon,
  DiscountIcon,
  TruckDeliveryIcon,
  BookOpen01Icon,
  Edit01Icon,
  CustomerServiceIcon,
  PlayIcon,
  SecurityLockIcon,
  Agreement01Icon,
  UserGroupIcon,
  Mail01Icon,
  LocationIcon,
  ChartUpIcon,
  CodeIcon,
  Facebook01Icon,
  InstagramIcon,
  NewTwitterIcon,
  Linkedin01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()

const currentYear = computed(() => new Date().getFullYear())
</script>

<style scoped>
.footer-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: #000000;
  margin-bottom: 0.75rem;
}

.footer-link {
  display: flex;
  align-items: center;
  font-size: 0.875rem;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
}

.footer-link:hover {
  color: #111827;
  transform: translateX(4px);
}

.social-link {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background-color: #f3f4f6;
  color: #6b7280;
  text-decoration: none;
  transition: all 0.2s ease;
}

.social-link:hover {
  background-color: #e5e7eb;
  color: #111827;
  transform: scale(1.1);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .footer-link {
    font-size: 1rem;
    padding: 0.25rem 0;
  }
  
  .social-link {
    width: 40px;
    height: 40px;
  }
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
  footer {
    background-color: #000000;
    border-color: #374151;
  }
  
  .footer-title {
    color: #f9fafb;
  }
  
  .footer-link {
    color: #9ca3af;
  }
  
  .footer-link:hover {
    color: #e5e7eb;
  }
  
  .social-link {
    background-color: #000000;
    color: #ffffff;
  }
  
  .social-link:hover {
    background-color: #52525227;
    color: #e5e7eb;
  }
}
</style> 
