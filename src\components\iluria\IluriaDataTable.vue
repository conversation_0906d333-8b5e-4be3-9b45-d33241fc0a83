<template>
  <!-- <PERSON><PERSON><PERSON> a tabela quando há dados OU quando showHeaderAllTime está ativo -->
  <DataTable 
    v-if="hasData || showHeaderAllTime"
    :value="value" 
    v-bind="$attrs" 
    class="iluria-data-table"
    :scrollable="false"
    :virtualScrollerOptions="null"
    :rows="0"
    :paginator="false"
    :lazy="false"
    :tableStyle="{ 'width': '100%', 'height': 'auto', 'table-layout': 'fixed' }"
  >
    <!-- Render columns from prop if provided -->
    <template v-if="columns && columns.length > 0">
      <Column 
        v-for="(column, index) in columns" 
        :key="`prop-${column.field}`" 
        :field="column.field" 
        :header="column.header"
        :sortable="column.sortable" 
        :style="column.bodyStyle" 
        :headerStyle="column.headerStyle" 
        :class="column.class" 
        :headerClass="column.headerClass"
      >
        <template #body="slotProps">
          <slot :name="`column-${index}`" v-bind="slotProps" v-if="$slots[`column-${index}`]"></slot>
          <slot :name="`column-${column.field}`" v-bind="slotProps" v-else-if="$slots[`column-${column.field}`]"></slot>
          <template v-else>
            <span :title="getFullText(slotProps.data[column.field])" class="table-cell-content"
              :class="{ 'truncated': shouldTruncate(slotProps.data[column.field], column) }">
              {{ truncateText(slotProps.data[column.field], column) }}
            </span>
          </template>
        </template>

        <!-- Forward custom header slots -->
        <template #header="slotProps">
          <slot :name="`header-${index}`" v-bind="slotProps" v-if="$slots[`header-${index}`]"></slot>
          <slot :name="`header-${column.field}`" v-bind="slotProps" v-else-if="$slots[`header-${column.field}`]"></slot>
          <template v-else>{{ column.header }}</template>
        </template>

        <!-- Support for custom footer content -->
        <template #footer v-if="$slots[`footer-${index}`] || $slots[`footer-${column.field}`]">
          <slot :name="`footer-${index}`" v-if="$slots[`footer-${index}`]"></slot>
          <slot :name="`footer-${column.field}`" v-else></slot>
        </template>
      </Column>
    </template>

    <!-- Render slot content for direct Column usage -->
    <template v-else>
      <slot></slot>
    </template>

    <!-- Empty state dentro da tabela quando showHeaderAllTime está ativo mas não há dados -->
    <template #empty v-if="showHeaderAllTime && !hasData">
      <slot name="empty" v-if="$slots.empty"></slot>
      <div v-else class="empty-state">No data available</div>
    </template>

    <!-- Loading state slot -->
    <template #loading v-if="$slots.loading">
      <slot name="loading"></slot>
    </template>

    <!-- Additional slots for header and footer -->
    <template #header v-if="$slots.header">
      <slot name="header"></slot>
    </template>

    <template #footer v-if="$slots.footer">
      <slot name="footer"></slot>
    </template>
  </DataTable>
  
  <!-- Estado vazio quando não há dados E showHeaderAllTime não está ativo -->
  <div v-else-if="!hasData && !showHeaderAllTime" class="iluria-data-table no-data">
    <div class="empty-state-wrapper">
      <slot name="empty" v-if="$slots.empty"></slot>
      <div v-else class="empty-state">No data available</div>
    </div>
  </div>
</template>

<script setup>
import DataTable from 'primevue/datatable';
import Column from 'primevue/column';
import { computed } from 'vue';

const props = defineProps({
  /**
   * The data to display in the table
   */
  value: {
    type: Array,
    required: true
  },
  /**
   * The columns configuration
   * @example
   * [
   *   { field: 'code', header: 'Code', sortable: true },
   *   { field: 'name', header: 'Name', sortable: true, maxLength: 30 },
   *   { field: 'category', header: 'Category' },
   *   { field: 'quantity', header: 'Quantity', style: 'width: 100px' }
   * ]
   *
   * @example Image column with automatic Image02Icon fallback
   * <!-- Template usage for image column - Image02Icon is automatically shown when no image -->
   * <template #column-image="{ data }">
   *   <div class="product-image-container">
   *     <img v-if="getProductImage(data)" :src="getProductImage(data)" alt="Product" class="product-image" />
   *     <div v-else class="product-image-not-found">
   *       <!-- Image02Icon will be automatically displayed via CSS -->
   *     </div>
   *   </div>
   * </template>
   *
   * Note: Any element with class 'product-image-placeholder' or 'product-image-not-found'
   * will automatically display the Image02Icon via CSS. FontAwesome icons are hidden automatically.
   */
  columns: {
    type: Array,
    required: false,
    default: () => [],
    validator: (value) => {
      return value.every(item => 'field' in item);
    }
  },
  /**
   * Default maximum length for text truncation
   */
  defaultMaxLength: {
    type: Number,
    default: 50
  },
  /**
   * Whether to enable text truncation globally
   */
  enableTruncation: {
    type: Boolean,
    default: true
  },
  /**
   * Whether to show the table header even when there's no data
   */
  showHeaderAllTime: {
    type: Boolean,
    default: false
  },

});

// Computed para verificar se há dados
const hasData = computed(() => {
  return props.value && Array.isArray(props.value) && props.value.length > 0;
});



/**
 * Truncates text based on column configuration or default settings
 */
const truncateText = (text, column) => {
  if (!props.enableTruncation || !text || column.maxLength === false) return text;

  const maxLength = column.maxLength || props.defaultMaxLength;

  if (text.toString().length <= maxLength) {
    return text;
  }

  return text.toString().substring(0, maxLength) + '...';
};

/**
 * Checks if text should be truncated
 */
const shouldTruncate = (text, column) => {
  if (!props.enableTruncation || !text || column.maxLength === false) return false;

  const maxLength = column.maxLength || props.defaultMaxLength;
  return text.toString().length > maxLength;
};

/**
 * Gets the full text for tooltip
 */
const getFullText = (text) => {
  return text ? text.toString() : '';
};


</script>

<style scoped>
/* Tabela principal - Layout compacto com colunas uniformes */
.iluria-data-table {
  border-collapse: separate;
  border-spacing: 0;
  width: 100%;
  height: auto;
  table-layout: fixed !important; /* Colunas uniformes */
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible !important;
  position: relative;
}

/* Container principal compacto */
.iluria-data-table,
.iluria-data-table :deep(.p-datatable),
.iluria-data-table :deep(.p-datatable-wrapper),
.iluria-data-table :deep(.p-datatable-table),
.iluria-data-table :deep(.p-datatable-scrollable-wrapper),
.iluria-data-table :deep(.p-datatable-scrollable-table),
.iluria-data-table :deep(.p-datatable-tbody),
.iluria-data-table :deep(.p-datatable-virtualscroller) {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
  width: 100% !important;
  table-layout: fixed !important; /* Layout fixo para colunas uniformes */
}

/* Remove qualquer scroll virtual ou limitação de altura */
.iluria-data-table :deep(.p-virtualscroller),
.iluria-data-table :deep(.p-virtualscroller-content),
.iluria-data-table :deep(.p-virtualscroller-spacer) {
  display: none !important;
  height: auto !important;
  max-height: none !important;
}

/* Força exibição de todas as linhas */
.iluria-data-table :deep(.p-datatable-tbody > tr) {
  display: table-row !important;
  visibility: visible !important;
  height: auto !important;
  width: 100% !important;
}

/* Células da tabela - compactas e consistentes com headers */
:deep(.p-datatable-tbody > tr > td) {
  padding: 8px 12px; /* Padding consistente com headers */
  position: relative;
  vertical-align: middle;
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  transition: background-color 0.2s ease, color 0.3s ease;
  overflow: hidden !important;
  white-space: nowrap;
  text-overflow: ellipsis;
  width: auto !important;
  max-width: 120px !important; /* Consistente com headers */
  min-width: 80px !important; /* Consistente com headers */
  font-size: 13px; /* Ligeiramente maior que header */
  line-height: 1.3;
  border-right: none !important;
  border-left: none !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
}

/* Conteúdo das células com truncamento */
.table-cell-content {
  display: block;
  overflow: hidden !important;
  white-space: nowrap;
  text-overflow: ellipsis !important;
  width: 100% !important;
  max-width: 100% !important;
  font-size: inherit;
}

.table-cell-content.truncated {
  cursor: help;
  max-width: 100%;
  overflow: hidden;
  text-overflow: ellipsis;
}

/* Sobrescreve truncamento para colunas de ação */
:deep(.p-datatable-tbody > tr > td:last-child) .table-cell-content,
:deep(.p-datatable-tbody > tr > td:last-child) .table-cell-content.truncated {
  max-width: none !important;
  overflow: visible !important;
  text-overflow: unset !important;
  white-space: nowrap !important;
}

/* Headers da tabela - compactos com quebra de linha */
:deep(.p-datatable-thead > tr > th) {
  white-space: normal; /* Permite quebra de linha */
  overflow: hidden;
  text-overflow: ellipsis;
  padding: 8px 12px; /* Padding menor para compactar */
  font-weight: 600;
  background: var(--iluria-color-sidebar-bg) !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
  font-size: 12px; /* Fonte menor */
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  width: auto !important;
  max-width: 120px !important; /* Largura máxima */
  min-width: 80px !important; /* Largura mínima */
  line-height: 1.3;
  word-wrap: break-word; /* Quebra palavras longas */
  hyphens: auto; /* Hifenização automática */
  /* Remoção de bordas laterais duplicadas */
  border-right: none !important;
  border-left: none !important;
  border-top: none !important;
}

/* Configurações específicas para ações (última coluna) */
:deep(.p-datatable-tbody > tr > td:last-child) {
  white-space: nowrap !important;
  overflow: visible !important;
  width: auto !important;
  min-width: fit-content !important;
  max-width: none !important;
}

:deep(.p-datatable-tbody > tr > td:last-child .table-cell-content) {
  white-space: nowrap !important;
  overflow: visible !important;
  text-overflow: unset !important;
  max-width: none !important;
  width: auto !important;
}

/* Header da coluna de ações sem limitação de largura */
:deep(.p-datatable-thead > tr > th:last-child) {
  max-width: none !important;
  min-width: fit-content !important;
  width: auto !important;
}

/* Garante que todas as colunas sejam visíveis */
:deep(.p-datatable-thead > tr > th),
:deep(.p-datatable-tbody > tr > td) {
  min-width: fit-content !important;
  width: auto !important;
}

/* Hover effect nas linhas */
:deep(.p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.p-datatable-tbody > tr:hover > td) {
  background: var(--iluria-color-hover) !important;
}

/* Bordas da tabela */
:deep(.p-datatable-table) {
  border: 1px solid var(--iluria-color-border) !important;
  border-radius: 8px;
  overflow: visible !important;
  background: var(--iluria-color-surface) !important;
  transition: border-color 0.3s ease;
  height: auto !important;
  width: 100% !important;
  table-layout: fixed !important; /* Layout fixo para compactação */
}

/* Última linha sem borda inferior */
:deep(.p-datatable-tbody > tr:last-child > td) {
  border-bottom: none;
}

/* Responsividade compacta */
@media (max-width: 768px) {
  :deep(.p-datatable-thead > tr > th) {
    padding: 6px 8px;
    font-size: 11px;
    max-width: 100px !important;
    min-width: 60px !important;
  }

  :deep(.p-datatable-tbody > tr > td) {
    padding: 6px 8px;
    font-size: 12px;
    max-width: 100px !important;
    min-width: 60px !important;
  }

  .table-cell-content {
    font-size: 12px;
  }

  /* Coluna de ações em mobile */
  :deep(.p-datatable-thead > tr > th:last-child),
  :deep(.p-datatable-tbody > tr > td:last-child) {
    max-width: 80px !important;
    min-width: 60px !important;
  }
}

/* Estados vazios */
:deep(.p-datatable-emptymessage) {
  text-align: center;
  padding: 24px 16px;
  color: var(--iluria-color-text-muted);
  background: var(--iluria-color-surface);
  transition: color 0.3s ease;
  font-size: 13px;
}

:deep(.p-datatable-emptymessage td) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-muted) !important;
}

/* Tema escuro específico - força aplicação */
.theme-dark .iluria-data-table :deep(.p-datatable-table),
.theme-dark .iluria-data-table :deep(.p-datatable-thead > tr > th),
.theme-dark .iluria-data-table :deep(.p-datatable-tbody > tr > td) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  border-color: var(--iluria-color-border) !important;
}

.theme-dark .iluria-data-table :deep(.p-datatable-tbody > tr:hover > td) {
  background: var(--iluria-color-hover) !important;
}

/* Ícone de imagem não encontrada para tema escuro */
.theme-dark :deep(.product-image-placeholder::before),
.theme-dark :deep(.product-image-not-found::before) {
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23cccccc' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21,15 16,10 5,21'/%3E%3C/svg%3E");
}

/* Melhoria das transições para todos os temas */
:deep(.p-datatable) {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: visible !important;
  height: auto !important;
}

:deep(.p-datatable-wrapper) {
  background: var(--iluria-color-surface) !important;
  border-radius: 8px;
  overflow: auto !important;
  height: auto !important;
  width: 100% !important;
  max-width: 100% !important;
}

/* Força aplicação em todos os elementos do PrimeVue DataTable */
:deep(.p-datatable),
:deep(.p-datatable-wrapper),
:deep(.p-datatable-table),
:deep(.p-datatable-scrollable-wrapper),
:deep(.p-datatable-scrollable-table) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  overflow: visible !important;
  height: auto !important;
}

/* Força aplicação nas linhas */
:deep(.p-datatable-tbody > tr) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  display: table-row !important;
  visibility: visible !important;
}

/* Força aplicação no cabeçalho */
:deep(.p-datatable-thead > tr) {
  background: var(--iluria-color-sidebar-bg) !important;
  display: table-row !important;
}

/* Estilos para imagens de produto na tabela */

:deep(.product-image-container) {
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  flex-shrink: 0; /* Impede que a imagem encolha */
}

/* Estilos básicos para imagens - cada view deve definir seus próprios estilos específicos */
:deep(.product-image) {
  width: 100%;
  height: 100%;
  object-fit: cover;
  transition: all 0.3s ease;
}



/* Hover effects para imagens */
:deep(.product-image-container:hover .product-image) {
  transform: scale(1.05);
  filter: brightness(1.1) contrast(1.1) saturate(1.1);
}

:deep(.product-image-placeholder) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-muted);
  font-size: 18px;
  transition: color 0.3s ease;
  background: var(--iluria-color-surface);
  position: relative;
}

/* Esconde ícones FontAwesome e outros ícones antigos */
:deep(.product-image-placeholder i),
:deep(.product-image-placeholder .fas),
:deep(.product-image-placeholder .fa) {
  display: none !important;
}

/* Adiciona o ícone Image02Icon automaticamente via CSS */
:deep(.product-image-placeholder::before) {
  content: '';
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21,15 16,10 5,21'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

/* Ícone para imagem não encontrada - classe alternativa */
:deep(.product-image-not-found) {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
  background: var(--iluria-color-surface);
  position: relative;
}

:deep(.product-image-not-found::before) {
  content: '';
  width: 24px;
  height: 24px;
  background-image: url("data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' width='24' height='24' viewBox='0 0 24 24' fill='none' stroke='%23999999' stroke-width='1.5' stroke-linecap='round' stroke-linejoin='round'%3E%3Crect x='3' y='3' width='18' height='18' rx='2' ry='2'/%3E%3Ccircle cx='8.5' cy='8.5' r='1.5'/%3E%3Cpolyline points='21,15 16,10 5,21'/%3E%3C/svg%3E");
  background-repeat: no-repeat;
  background-position: center;
  background-size: contain;
  opacity: 0.6;
  transition: opacity 0.3s ease;
}

:deep(.product-image-not-found .image-not-found-icon) {
  width: 24px;
  height: 24px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
}

/* Estado vazio personalizado */
.empty-state {
  padding: 3rem 1.5rem;
  text-align: center;
  color: var(--iluria-color-text-muted);
  background: var(--iluria-color-surface);
  font-size: 0.875rem;
  transition: color 0.3s ease;
}

/* Wrapper para o estado vazio */
.empty-state-wrapper {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  overflow: hidden;
}

.iluria-data-table.no-data {
  width: 100%;
}
</style>
