<template>
  <div class="pickup-shipping-container">
    <!-- Switch para ativar/desativar retirada na loja -->
    <div class="toggle-section">
      <label
        for="toggle-pickup"
        class="field-label clickable-label"
        @click="togglePickup"
      >
        {{ t('shippingConfig.offerPickupLabel') }}
      </label>
      <IluriaToggleSwitch
        id="toggle-pickup"
        v-model="pickupEnabled"
      />
    </div>

    <!-- Input da descrição - sempre visível -->
    <div class="description-field">
      <label for="pickupDescription" class="field-label">
        {{ t('shippingConfig.pickupDescriptionLabel') }}
      </label>
      <IluriaInputText
        id="pickupDescription"
        name="pickupDescription"
        v-model="pickupDescription"
        :disabled="!pickupEnabled"
        :formContext="formContext?.pickupDescription"
        class="description-input"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { requiredText } from '@/services/validation.service';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

// Define model for pickup description
const pickupDescription = defineModel('pickupDescription', { default: '' });

// Define model for pickup enabled state
const pickupEnabled = defineModel('pickupEnabled', { default: false });

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Function to toggle pickup state
const togglePickup = () => {
  pickupEnabled.value = !pickupEnabled.value;
};

// Define validation rules
const validationRules = computed(() => {
  if (pickupEnabled.value) {
    return {
      pickupDescription: requiredText(t('shippingConfig.pickupDescriptionRequired'))
    };
  }
  return {};
});

// Expose validation rules
defineExpose({ validationRules });

</script>

<style scoped>
.pickup-shipping-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  width: 100%;
}

.toggle-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  width: 100%;
}

.field-label {
  display: block;
  font-size: 16px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
  margin-bottom: 8px;
}

.clickable-label {
  cursor: pointer;
  user-select: none;
  margin-bottom: 0;
}

.clickable-label:hover {
  color: var(--iluria-color-primary);
}

.description-field {
  display: flex;
  flex-direction: column;
  width: 100%;
}

.description-input {
  width: 100%;
}

/* Estilo para input desabilitado */
:deep(.p-inputtext:disabled) {
  background-color: var(--iluria-color-bg-disabled, #f5f5f5);
  color: var(--iluria-color-text-disabled, #999);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive Design */
@media (max-width: 480px) {
  .toggle-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .field-label {
    font-size: 15px;
  }
  
  .description-field {
    margin-top: 1rem;
  }
}
</style>
