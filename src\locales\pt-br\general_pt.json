{"showing": "Mostrando", "to": "até", "of": "de", "results": "resultados", "pageTitle": "<PERSON><PERSON>", "error": "Erro", "success": "Sucesso", "info": "Informação", "hello": "O<PERSON><PERSON>", "home": "Home", "actions": "Ações", "status": "Status", "lastUpdate": "Ultima atualização", "createdAt": "C<PERSON><PERSON> em", "logout": "<PERSON><PERSON>", "description": "Descrição", "selectType": "Selecione o tipo", "save": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "requiredFieldsMessage": "Preencha todos os campos obrigatórios", "saveSuccess": "Salvo com sucesso", "saveError": "Erro ao salvar", "loadError": "Erro ao carregar", "loading": "Carregando...", "confirm": "Confirmar", "create": "<PERSON><PERSON><PERSON>", "delete": "Excluir", "tryAgain": "Tente novamente", "remove": "Remover", "cancel": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "UnknownError": "Erro inesperado. Tente novamente.", "search": "Buscar", "image": "Imagem", "name": "Nome", "sku": "SKU", "price": "Preço", "stock": "Estoque", "addProduct": "<PERSON><PERSON><PERSON><PERSON>", "onlyInStock": "Apenas com estoque", "noProductsFound": "Nenhum produto encontrado", "confirmDelete": "Tem certeza que deseja excluir {name}?", "page": "Página {current} de {total}", "go": "<PERSON>r", "previous": "Anterior", "next": "Próximo", "products": "<PERSON><PERSON><PERSON>", "tagsLabel": "Tags", "tagSystem": {"alreadyExists": "A tag \"{tag}\" já existe"}, "type": "Tipo", "ok": "OK", "yes": "<PERSON>m", "no": "Não", "warning": "Atenção", "saveChanges": "<PERSON><PERSON>", "cancelChanges": "<PERSON><PERSON><PERSON>", "active": "Ativo", "inactive": "Inativo", "selectAll": "Selecion<PERSON>", "unselectAll": "<PERSON><PERSON><PERSON>", "deleteSelected": "Apagar Selecion<PERSON>", "clear": "Limpar", "clearAll": "<PERSON><PERSON>", "preview": "Visualizar", "general": {"back": "Voltar", "cancel": "<PERSON><PERSON><PERSON>", "error": "Erro", "success": "Sucesso"}, "toast": {"titles": {"success": "Sucesso", "error": "Erro", "warning": "Atenção", "info": "Informação"}, "close": "<PERSON><PERSON>r not<PERSON>", "undo": "<PERSON><PERSON><PERSON>"}, "category": {"title": "Categorias", "categories": {"new": "Nova Categoria", "edit": "Editar Categoria", "addSub": "Adicionar Subcategoria", "noCategories": "Nenhuma categoria encontrada", "loadError": "Erro ao carregar categorias", "orderUpdated": "Ordem das categorias atualizada com sucesso", "orderError": "Erro ao atualizar a ordem das categorias", "confirmDelete": "Tem certeza que deseja excluir esta categoria?", "deleteSuccess": "Categoria excluída com sucesso", "deleteError": "Erro ao excluir categoria", "loadingCategories": "Carregando categorias...", "selectedSingular": "categoria selecionada", "selectedPlural": "categorias selecionadas", "selectCategories": "<PERSON><PERSON><PERSON><PERSON> as categorias", "searchPlaceHolder": "Buscar..."}}, "layoutEditor": {"videoConfigEditor": "Editor de Vídeo", "content": "<PERSON><PERSON><PERSON><PERSON>", "video": "Vídeo", "design": "Design", "left": "E<PERSON>rda", "center": "Centro", "right": "<PERSON><PERSON><PERSON>", "horizontal": "Horizontal", "vertical": "Vertical"}, "videoEditor": {"title": "Configu<PERSON>", "content": {"title": "<PERSON><PERSON><PERSON><PERSON>", "videoTitle": "Título do Vídeo", "videoTitlePlaceholder": "Digite o título do vídeo...", "description": "Descrição", "descriptionPlaceholder": "Digite uma descrição para o vídeo...", "buttonSettings": "Configurações do Botão", "enableButton": "Exibir botão de ação", "buttonText": "Texto do Botão", "buttonTextPlaceholder": "Compre agora", "buttonUrl": "URL do Botão", "buttonUrlPlaceholder": "https://exemplo.com"}, "video": {"title": "Configurações de Vídeo", "videoUrl": "URL do Vídeo", "videoUrlPlaceholder": "<PERSON> a URL do YouTube, Vime<PERSON> ou vídeo direto", "videoUrlHint": "Suporte a YouTube, Vimeo e arquivos de vídeo diretos", "videoType": "Tipo de Vídeo", "directVideo": "Vídeo <PERSON>", "aspectRatio": "Proporção", "videoPoster": "<PERSON><PERSON>", "videoPosterPlaceholder": "URL da imagem de capa (apenas vídeos diretos)", "playbackOptions": "Opções de Reprodução", "autoplay": "Reprodução automática", "loop": "Repetir vídeo", "muted": "Iniciar sem som", "showControls": "<PERSON><PERSON>r controles"}, "design": {"title": "Layout e Design", "layout": "Layout", "layoutType": "<PERSON><PERSON><PERSON> de Layout", "horizontal": "Horizontal", "vertical": "Vertical", "videoPosition": "Posição do Vídeo", "positionLeft": "E<PERSON>rda", "positionRight": "<PERSON><PERSON><PERSON>"}, "colors": {"title": "Cores", "backgroundColor": "<PERSON><PERSON> <PERSON>", "textColor": "<PERSON><PERSON> <PERSON>", "buttonColor": "Cor do Botão", "buttonTextColor": "Cor do Texto do Botão"}, "spacing": {"title": "Espaçamento", "paddingTop": "Espaçamento Superior", "paddingBottom": "Espaçamento Inferior", "borderRadius": "Arredondamento"}, "actions": {"preview": "Visualizar Vídeo", "reset": "<PERSON><PERSON><PERSON>", "apply": "Aplicar"}}, "saving": "Salvando...", "loadingCategories": "Carregando categorias...", "idNotFound": "ID não encontrado", "confirmDeleteTitle": "Confirmar <PERSON>", "common": {"cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir", "actions": "Ações", "saveChanges": "<PERSON><PERSON>", "save": "<PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "retry": "Tente novamente", "loadingCategories": "Carregando categorias...", "confirmDeleteTitle": "Confirmar <PERSON>", "saving": "Salvando...", "pagination": {"showing": "Mostrando {first} a {last} de {totalRecords} registros"}, "idNotFound": "ID não encontrado", "loading": "Carregando...", "active": "Ativo", "inactive": "Inativo"}}