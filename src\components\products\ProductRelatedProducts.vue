<template>
  <div class="related-products-container">
    <div class="options">
      <div
        v-for="option in radioOptions"
        :key="option.value"
        class="option"
        :class="{ selected: selectedMode === option.value }"
      >
        <div class="option-main">
          <span class="icon">
            <svg v-if="option.value === 'manual'" width="32" height="32" fill="none" stroke="#2563eb" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M8 12V7.5a1.5 1.5 0 1 1 3 0V12"/><path d="M11 12V6.5a1.5 1.5 0 1 1 3 0V12"/><path d="M14 12V8.5a1.5 1.5 0 1 1 3 0V16a4 4 0 0 1-4 4H9a4 4 0 0 1-4-4v-4.5a1.5 1.5 0 1 1 3 0V12"/></svg>
            <svg v-else-if="option.value === 'category'" width="32" height="32" fill="none" stroke="#2563eb" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><rect x="3" y="3" width="7" height="7" rx="2"/><rect x="14" y="3" width="7" height="7" rx="2"/><rect x="3" y="14" width="7" height="7" rx="2"/></svg>
            <svg v-else-if="option.value === 'collection'" width="32" height="32" fill="none" stroke="#2563eb" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><line x1="6" y1="9" x2="18" y2="9"/><line x1="6" y1="12" x2="18" y2="12"/><line x1="6" y1="15" x2="18" y2="15"/></svg>
          </span>
          <div class="option-content">
            <div class="option-title">{{ option.label }}</div>
            <div class="option-desc">{{ option.desc }}</div>
          </div>
          <div class="ml-auto">
            <IluriaToggleSwitch
              :id="'related-toggle-' + option.value"
              :modelValue="selectedMode === option.value"
              @update:modelValue="(val) => handleToggleChange(option.value, val)"
              label=""
              labelPosition="horizontal"
            />
          </div>
        </div>
        <transition name="fade">
          <div v-if="selectedMode === option.value" class="manual-expand-box">
            <div class="expand-content-bg">
            <template v-if="option.value === 'manual'">
              <!-- Conteúdo expandido da opção manual -->
              <IluriaButton color="primary" size="medium" class="rounded-xl shadow mb-4 flex flex-row items-center gap-3" @click.stop="openProductSelector">
                <span class="flex flex-row items-center gap-3">
                  <svg width="22" height="22" fill="none" stroke="currentColor" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24"><path d="M3 6h18M3 12h18M3 18h18"/></svg>
                  <span><span class="font-bold">Selecionar produtos</span> <span class="opacity-80 ml-1 font-bold">(máx 20)</span></span>
                </span>
              </IluriaButton>
              <div class="selected-tags-list">
                <div v-if="selectedProducts.length > 0" class="products-count" style="width: 100%; margin-bottom: 8px; font-size: 12px; color: #666;">
                  {{ selectedProducts.length }} produto(s) selecionado(s)
                </div>
                <span v-for="(product, idx) in selectedProducts" :key="`${product.id}-${idx}`" class="tag">
                  {{ product.name }}
                  <span v-if="product.variationTitle" class="variation-info">
                    ({{ product.variationTitle }})
                  </span>
                  <button class="remove-tag" @click.stop="removeProduct(idx)">×</button>
                </span>
              </div>
              <div class="order-section">
                <div class="order-title">
                  <svg width="22" height="22" fill="none" stroke="#111" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="vertical-align:middle;margin-right:6px;"><g><rect x="3" y="6" width="18" height="2" rx="1"/><rect x="3" y="11" width="12" height="2" rx="1"/><rect x="3" y="16" width="6" height="2" rx="1"/></g></svg>
                  Ordenação
                </div>
                <IluriaSelectButton
                  id="order-by-manual"
                  v-model="orderBy"
                  :options="orderByOptions"
                  optionLabel="label"
                  optionValue="value"
                  label=""
                  labelPosition="horizontal"
                  class="order-select-btn"
                />
              </div>
            </template>
            <template v-else-if="option.value === 'category'">
              <RelatedProductCategories v-model="relatedSelectedCategories" />
              <div class="order-section">
                <div class="order-title">
                  <svg width="22" height="22" fill="none" stroke="#111" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="vertical-align:middle;margin-right:6px;"><g><rect x="3" y="6" width="18" height="2" rx="1"/><rect x="3" y="11" width="12" height="2" rx="1"/><rect x="3" y="16" width="6" height="2" rx="1"/></g></svg>
                  Ordenação
                </div>
                <IluriaSelectButton
                  id="order-by-category"
                  v-model="orderBy"
                  :options="orderByOptions"
                  optionLabel="label"
                  optionValue="value"
                  label=""
                  labelPosition="horizontal"
                  class="order-select-btn"
                />
              </div>
            </template>
            <template v-else-if="option.value === 'collection'">
              <div class="relative mb-4">
                <input 
                  type="search" 
                  v-model="collectionSearchQuery"
                  class="block w-full p-2 text-sm rounded-lg bg-white"
                  placeholder="Buscar coleções..."
                />
              </div>
              
              <div class="collections-list">
                <IluriaCheckBox
                  v-for="col in filteredCollectionsList"
                  :key="col.id"
                  :id="'col-' + col.id"
                  :label="col.name"
                  :modelValue="selectedCollections.includes(col.id)"
                  @update:modelValue="val => handleCollectionCheck(val, col.id)"
                  wrapperClass="cat-checkbox"
                />
              </div>
              
              <div v-if="filteredCollectionsList.length === 0 && collectionSearchQuery" class="no-results">
                Nenhuma coleção encontrada para "{{ collectionSearchQuery }}"
              </div>
              <div class="order-section">
                <div class="order-title">
                  <svg width="22" height="22" fill="none" stroke="#111" stroke-width="2.2" stroke-linecap="round" stroke-linejoin="round" viewBox="0 0 24 24" style="vertical-align:middle;margin-right:6px;"><g><rect x="3" y="6" width="18" height="2" rx="1"/><rect x="3" y="11" width="12" height="2" rx="1"/><rect x="3" y="16" width="6" height="2" rx="1"/></g></svg>
                  Ordenação
                </div>
                <IluriaSelectButton
                  id="order-by-collection"
                  v-model="orderBy"
                  :options="orderByOptions"
                  optionLabel="label"
                  optionValue="value"
                  label=""
                  labelPosition="horizontal"
                  class="order-select-btn"
                />
              </div>
            </template>
            </div>
          </div>
        </transition>
      </div>
    </div>
    <ProductSelectorModal
      :visible="showProductSelectorModal"
      :preselectedProducts="selectedProducts"
      @update:visible="showProductSelectorModal = $event"
      @add-products="handleProductSelection"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, inject, watch, computed } from 'vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import ProductSelectorModal from '@/components/products/ProductSelectorModal.vue'
import IluriaSelectButton from '@/components/iluria/form/IluriaSelectButton.vue'
import RelatedProductCategories from '@/components/products/RelatedProductCategories.vue'
import collectionService from '@/services/collection.service.js'
import productService from '@/services/product.service.js'
import { useToast } from '@/services/toast.service'

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['update:modelValue'])

const productForm = inject('productForm')
const toast = useToast()
const selectedMode = ref(null)
const selectedProducts = ref([])
const selectedCategories = ref([1])
const selectedCollections = ref([])
const collectionsList = ref([])
const collectionSearchQuery = ref('')
const orderBy = ref('recent')
const relatedSelectedCategories = ref([])
const categories = ref([
  { id: 1, name: 'Eletrônicos' },
  { id: 2, name: 'Vestuário' },
  { id: 3, name: 'Acessórios' },
  { id: 4, name: 'Casa' }
])
const radioOptions = [
  { label: 'Escolher Manualmente', value: 'manual', icon: '🖐️', desc: 'Selecione produtos específicos para exibir' },
  { label: 'Escolher por Categorias', value: 'category', icon: '📂', desc: 'Exibir produtos de categorias específicas' },
  { label: 'Escolher por Coleções', value: 'collection', icon: '🗂️', desc: 'Exibir produtos de coleções específicas' }
]
const showProductSelectorModal = ref(false)
const orderByOptions = [
  { label: 'Mais recentes', value: 'recent' },
  { label: 'Aleatório', value: 'random' },
  { label: 'Destaques', value: 'featured' }
]
const isLoadingInitialData = ref(false)

const filteredCollectionsList = computed(() => {
  if (!collectionSearchQuery.value) {
    return collectionsList.value
  }
  
  return collectionsList.value.filter(collection => 
    collection.name.toLowerCase().includes(collectionSearchQuery.value.toLowerCase())
  )
})

onMounted(async () => {
  const collections = await collectionService.getAllCollections(0, 100)
  collectionsList.value = collections.content || []
})

function loadInitialData() {
  const form = productForm.value
  if (!shouldLoadInitialData(form)) {
    return
  }
  
  isLoadingInitialData.value = true
  
  const dataFlags = loadFormData(form)
  setSelectedMode(form, dataFlags)
  setSortOrder(form)
  
  setTimeout(() => {
    isLoadingInitialData.value = false
    syncConfiguration()
  }, 100)
}

function shouldLoadInitialData(form) {
  if (!form) return false
  
  return form.selectedProductIds || form.selectedCategoryIds || form.selectedCollectionIds || 
         form.manualModeEnabled || form.categoryModeEnabled || form.collectionModeEnabled
}

function loadFormData(form) {
  const dataFlags = {
    hasProductData: loadSelectedProducts(form.selectedProductIds),
    hasCategoryData: loadSelectedCategories(form.selectedCategoryIds),
    hasCollectionData: loadSelectedCollections(form.selectedCollectionIds)
  }
  return dataFlags
}

function loadSelectedProducts(selectedProductIds) {
  if (!selectedProductIds || selectedProductIds === 'null') return false
  
  try {
    const productIds = JSON.parse(selectedProductIds)
    if (Array.isArray(productIds) && productIds.length > 0) {
      loadProductsByIds(productIds)
      return true
    }
  } catch (e) {
    console.warn('Erro ao carregar produtos selecionados:', e)
  }
  return false
}

function loadSelectedCategories(selectedCategoryIds) {
  if (!selectedCategoryIds || selectedCategoryIds === 'null') return false
  
  try {
    const categoryIds = JSON.parse(selectedCategoryIds)
    if (Array.isArray(categoryIds) && categoryIds.length > 0) {
      relatedSelectedCategories.value = categoryIds
      setTimeout(() => {
        relatedSelectedCategories.value = categoryIds
      }, 500)
      return true
    }
  } catch (e) {
    console.warn('Erro ao carregar categorias selecionadas:', e)
  }
  return false
}

function loadSelectedCollections(selectedCollectionIds) {
  if (!selectedCollectionIds || selectedCollectionIds === 'null') return false
  
  try {
    const collectionIds = JSON.parse(selectedCollectionIds)
    if (Array.isArray(collectionIds) && collectionIds.length > 0) {
      selectedCollections.value = collectionIds
      return true
    }
  } catch (e) {
    console.warn('Erro ao carregar coleções selecionadas:', e)
  }
  return false
}

function setSelectedMode(form, dataFlags) {
  if (form.manualModeEnabled === true || dataFlags.hasProductData) {
    selectedMode.value = 'manual'
  } else if (form.categoryModeEnabled === true || dataFlags.hasCategoryData) {
    selectedMode.value = 'category'
  } else if (form.collectionModeEnabled === true || dataFlags.hasCollectionData) {
    selectedMode.value = 'collection'
  } else {
    selectedMode.value = null
  }
}

function setSortOrder(form) {
  orderBy.value = form.sortOrder || 'recent'
}


function handleToggleChange(mode, isEnabled) {
  if (isEnabled) {
    selectedMode.value = mode
  } else {
    selectedMode.value = null
  }
  
  syncConfiguration()
}

async function loadProductsByIds(productIds) {
  try {
    const loadedProducts = []
    
    for (const id of productIds) {
      try {
        const product = await productService.getById(id)
        loadedProducts.push({
          id: product.id,
          name: product.name,
          sku: product.sku,
          price: product.price,
          imageUrl: product.imageUrl || (product.photos && product.photos[0]?.url),
          variationTitle: null,
          variationId: null
        })
      } catch (error) {
        console.warn(`Erro ao carregar produto ${id}:`, error)
        loadedProducts.push({
          id: id,
          name: `Produto ${id} (não encontrado)`,
          sku: '',
          price: 0,
          imageUrl: null,
          variationTitle: null,
          variationId: null
        })
      }
    }
    
    selectedProducts.value = loadedProducts
  } catch (error) {
    console.error('Erro ao carregar produtos por IDs:', error)
  }
}

function handleCollectionCheck(val, id) {
  if (val) {
    if (!selectedCollections.value.includes(id)) selectedCollections.value.push(id)
  } else {
    selectedCollections.value = selectedCollections.value.filter(cid => cid !== id)
  }
}

function openProductSelector() {
  showProductSelectorModal.value = true
}
function removeProduct(idx) {
  selectedProducts.value.splice(idx, 1)
}
function handleProductSelection(products) {
  // Validar limite de 20 produtos
  if (products.length > 20) {
    toast.showWarning('Máximo de 20 produtos permitidos na seleção manual')
    return
  }
  
  selectedProducts.value = products.map(product => ({
    id: product.productId || product.id,
    name: product.name,
    sku: product.sku,
    price: product.price,
    imageUrl: product.imageUrl,
    variationTitle: product.variationTitle || null,
    variationId: product.variationId || null
  }))
  
  selectedMode.value = 'manual'
  
  setTimeout(() => {
    syncConfiguration()
  }, 50)
  
  showProductSelectorModal.value = false
}

function syncConfiguration() {
  const configData = {
    selectedProductIds: selectedMode.value === 'manual' && selectedProducts.value.length > 0 ? 
      JSON.stringify(selectedProducts.value.map(p => p.id)) : null,
    selectedCategoryIds: selectedMode.value === 'category' && relatedSelectedCategories.value.length > 0 ? 
      JSON.stringify(relatedSelectedCategories.value) : null,
    selectedCollectionIds: selectedMode.value === 'collection' && selectedCollections.value.length > 0 ? 
      JSON.stringify(selectedCollections.value) : null,
    sortOrder: orderBy.value,
    manualModeEnabled: selectedMode.value === 'manual',
    categoryModeEnabled: selectedMode.value === 'category',
    collectionModeEnabled: selectedMode.value === 'collection'
  }
  
  if (productForm?.value) {
    Object.assign(productForm.value, configData)
  }
  
  emit('update:modelValue', configData)
}

watch(selectedMode, () => {
  if (!isLoadingInitialData.value) {
    syncConfiguration()
  }
})

watch(selectedProducts, () => {
  if (!isLoadingInitialData.value && selectedMode.value === 'manual') {
    syncConfiguration()
  }
}, { deep: true })

watch(relatedSelectedCategories, () => {
  if (!isLoadingInitialData.value && selectedMode.value === 'category') {
    syncConfiguration()
  }
}, { deep: true })

watch(selectedCollections, () => {
  if (!isLoadingInitialData.value && selectedMode.value === 'collection') {
    syncConfiguration()
  }
}, { deep: true })

watch(orderBy, () => {
  if (!isLoadingInitialData.value) {
    syncConfiguration()
  }
})

watch(() => productForm?.value, (newForm, oldForm) => {
  if (newForm) {
    const checkAndLoad = () => {
      const hasAnyData = newForm.selectedProductIds || newForm.selectedCategoryIds || 
                        newForm.selectedCollectionIds || newForm.manualModeEnabled !== undefined ||
                        newForm.categoryModeEnabled !== undefined || newForm.collectionModeEnabled !== undefined
      
      if (hasAnyData) {
        loadInitialData()
      } else {
        setTimeout(checkAndLoad, 200)
      }
    }
    
    checkAndLoad()
  }
}, { immediate: true, deep: false })
</script>

<style scoped>
.related-products-container {
  background: #fff;
  border-radius: 12px;
  box-shadow: 0 2px 8px rgba(0,0,0,0.04);
  padding: 16px;
  overflow: visible;
  position: relative;
  margin-bottom: 8px;
}
.options {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 0;
  overflow: visible;
}
.option {
  display: flex;
  flex-direction: column;
  align-items: stretch;
  background: #f8f8f8;
  border: 2px solid transparent;
  border-radius: 10px;
  padding: 0;
  transition: border 0.2s, background 0.2s;
  overflow: visible;
  position: relative;
  z-index: 1;
}
.option-main {
  display: flex;
  align-items: center;
  padding: 16px;
}
.option.selected {
  border-color: #3b82f6;
  background: #eaf3ff;
}
.icon {
  font-size: 2rem;
  margin-right: 16px;
  color: #3b82f6;
}
.option-content {
  flex: 1;
}
.option-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 4px;
}
.option-desc {
  font-size: 0.9rem;
  color: #6b7280;
}

.manual-expand-box {
  border-top: 1px solid #e5e7eb;
  background: #f9fafb;
  border-radius: 0 0 8px 8px;
  overflow: visible;
  position: relative;
  z-index: 5;
}
.expand-content-bg {
  padding: 16px;
  border-radius: 0 0 8px 8px;
  background: #f9fafb;
  overflow: visible;
  position: relative;
  z-index: 10;
}
.selected-tags-list {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
  margin-bottom: 16px;
}
.tag {
  display: inline-flex;
  align-items: center;
  background: #3b82f6;
  color: white;
  padding: 6px 12px;
  border-radius: 6px;
  font-size: 0.85rem;
  font-weight: 500;
  max-width: 300px;
}
.variation-info {
  font-size: 0.75rem;
  opacity: 0.8;
  margin-left: 4px;
}
.remove-tag {
  margin-left: 8px;
  background: rgba(255, 255, 255, 0.2);
  border: none;
  color: white;
  border-radius: 50%;
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  font-size: 14px;
  font-weight: bold;
  transition: background 0.2s;
}
.remove-tag:hover {
  background: rgba(255, 255, 255, 0.3);
}
.products-count {
  color: #6b7280;
  font-size: 0.85rem;
  margin-bottom: 8px;
}
.no-results {
  text-align: center;
  color: #6b7280;
  font-size: 0.9rem;
  padding: 16px;
  font-style: italic;
}

.category-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
}

.collections-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
  margin-bottom: 16px;
  overflow-y: auto;
  max-height: 150px;
}
.order-section {
  margin-top: 16px;
}
.order-title {
  font-size: 1rem;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 12px;
  display: flex;
  align-items: center;
}
.order-select-btn {
  width: 100%;
}
.fade-enter-active, .fade-leave-active {
  transition: opacity 0.3s ease, max-height 0.3s ease;
}
.fade-enter-from, .fade-leave-to {
  opacity: 0;
  max-height: 0;
}
.fade-enter-to, .fade-leave-from {
  opacity: 1;
  max-height: 1000px;
}
</style> 
