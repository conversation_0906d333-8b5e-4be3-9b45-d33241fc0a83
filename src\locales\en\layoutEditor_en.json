{"back": "Back", "save": "Save", "saving": "Saving...", "saved": "Saved", "loading": "Loading...", "settings": "Settings", "openSettings": "Open Settings", "undo": "Undo", "redo": "Redo", "editMode": "Edit Mode", "viewMode": "View Mode", "mobileView": "Mobile View", "desktopView": "Desktop View", "editProducts": "Edit Products", "editProductStyle": "Product Style", "addAbove": "Add Above", "delete": "Delete", "error": {"title": "Error loading template", "message": "Could not load template.", "description": "The index.html file was not found in the environment.", "retry": "Try again"}, "pageSettings": "Page Settings", "pageInfo": "Page Information", "layoutSettings": "Layout Settings", "backgroundSettings": "Background Settings", "pageTitle": "Page Title", "pageTitlePlaceholder": "Enter page title", "pageLogo": "Logo", "pageLogoPlaceholder": "Logo URL", "pageFavicon": "Favicon", "pageFaviconPlaceholder": "Favicon URL", "contentWidth": "Content Width", "backgroundColor": "Background Color", "backgroundType": "Background Type", "backgroundImage": "Background Image", "backgroundImagePlaceholder": "Background Image URL", "backgroundPresets": "Background Presets", "backgroundTypes": {"solid": "Solid Color", "gradient": "Gradient", "image": "Image", "preset": "Presets"}, "gradientSettings": "<PERSON><PERSON><PERSON>s", "gradientType": "Gradient Type", "linear": "Linear", "radial": "Radial", "angle": "<PERSON><PERSON>", "presets": "Presets", "resetDefaults": "Reset to defaults", "imageSize": "Image Size", "imagePosition": "Image Position", "cover": "Cover", "contain": "Contain", "auto": "Auto", "center": "Center", "topLeft": "Top Left", "topCenter": "Top Center", "topRight": "Top Right", "textEditor": "Text Editor", "textContent": "Text Content", "enterText": "Enter text here...", "textColor": "Text Color", "fontSize": "Font Size", "fontSizeUnit": "{size}px", "minFontSize": "12px", "mediumFontSize": "32px", "maxFontSize": "72px", "colorValue": "{value}", "editText": "Edit Text", "editFont": "<PERSON>", "editSpacing": "Edit Spacing", "editBorder": "Edit Border", "editBackground": "Edit Background", "editAnimation": "Edit Animation", "editTransform": "Edit Transform", "editFilter": "Edit Filters", "editImage": "Edit Image", "editSize": "<PERSON>", "editLink": "Edit Link", "editColor": "Edit Color", "editLabel": "Edit Label", "editCarouselContent": "Edit Carousel Content", "editCarouselDesign": "Edit Carousel Design", "editLocationContent": "Edit Location Content", "editLocationDesign": "Edit Location Design", "carouselContentEditor": "Carousel Content Editor", "carouselDesignEditor": "Carousel Design Editor", "locationContentEditor": "Location Content Editor", "locationDesignEditor": "Location Design Editor", "remove": "Remove", "carousel": "Carousel", "location": "Location", "imageUrl": "Image URL", "enterImageUrl": "Enter Image URL", "imageAlt": "Alt Text", "enterImageAlt": "Enter alt text", "imageSizeContain": "Fit", "imageSizeCover": "Fill", "imageSizeFull": "100%", "linkUrl": "Link URL", "enterLinkUrl": "Enter Link URL", "linkTarget": "Link Target", "linkTargetSelf": "Same Window", "linkTargetBlank": "New Window", "spacingEditor": "Spacing Editor", "borderEditor": "Border Editor", "backgroundEditor": "Background Editor", "animationEditor": "Animation Editor", "transformEditor": "Transform Editor", "filterEditor": "Filter Editor", "sizeEditor": "Size Editor", "padding": "Padding", "margin": "<PERSON><PERSON>", "borderRadius": "Border Radius", "content": "Content", "quickLinks": "Quick Links", "resetSpacing": "Reset Spacing", "filters": {"blur": "Blur", "brightness": "Brightness", "contrast": "Contrast", "saturate": "Saturation", "hueRotate": "Hue Rotate", "sepia": "Sepia", "grayscale": "Grayscale", "invert": "Invert"}, "containerStyle": "Container Style", "containerPadding": "Container Padding", "gridGap": "Grid Gap", "productItemStyle": "Product Item Style", "itemBackgroundColor": "Item Background Color", "itemBorderColor": "Item Border Color", "itemBorderRadius": "Item Border Radius", "itemPadding": "<PERSON><PERSON>", "shadow": "Shadow", "noShadow": "No Shadow", "smallShadow": "Small Shadow", "mediumShadow": "Medium Shadow", "largeShadow": "Large Shadow", "textStyle": "Text Style", "titleColor": "Title Color", "titleSize": "Title Size", "priceColor": "Price Color", "priceSize": "Price Size", "buttonStyle": "Button Style", "buttonColor": "Button Color", "buttonTextColor": "Button Text Color", "productStyleEditor": "Product Style Editor", "productSelectionEditor": "Product Selection Editor", "selectProducts": "Select Products", "selectProductsDescription": "Choose products to display in this grid", "selectionMode": "Selection Mode", "automatic": "Automatic", "manual": "Manual", "cancel": "Cancel", "close": "Close", "addComponent": "Add Component", "apply": "Apply", "applyAll": "Apply All", "fontEditor": "Font Editor", "category": "Category", "allCategories": "All Categories", "sortBy": "Sort by", "newest": "Newest", "oldest": "Oldest", "priceAsc": "Lowest Price", "priceDesc": "Highest Price", "alphabetical": "Alphabetical", "popularity": "Popularity", "stockFilter": "Stock Filter", "onlyInStock": "Only In Stock", "searchProducts": "Search products...", "browse": "Browse", "selectedProducts": "Selected Products", "noProductsSelected": "No products selected", "removeProduct": "Remove product", "insertBelow": "Insert below", "insertAbove": "Insert above", "below": "Below", "above": "Above", "components": {"divider": "Divider", "spacer": "Spacer", "productGrid": "Product Grid", "dynamicProductGrid": "Dynamic Product Grid", "location": "Location"}, "categories": {"basic": "Basic", "media": "Media", "layout": "Layout", "interactive": "Interactive", "ecommerce": "E-commerce", "content": "Content"}, "actions": {"addElement": "Add Element", "deleteElement": "Delete Element", "duplicateElement": "Duplicate Element", "moveUp": "Move Up", "moveDown": "Move Down"}, "messages": {"elementAdded": "Element added successfully", "elementDeleted": "Element deleted successfully", "elementDuplicated": "Element duplicated successfully", "changesSaved": "Changes saved successfully", "errorSaving": "Error saving changes", "confirmDelete": "Are you sure you want to delete this element?", "invalidHtml": "Invalid HTML detected", "loadingError": "Error loading template"}, "dragToMove": "Drag to move", "selectImage": "Select Image", "slides": "Slides", "addSlide": "Add Slide", "slideTitle": "Slide Title", "slideSubtitle": "Slide Subtitle", "showButton": "Show But<PERSON>", "buttonText": "Button Text", "buttonLink": "Button Link", "layout": "Layout", "behavior": "Behavior", "autoPlay": "Auto Play", "autoPlayDescription": "Advance slides automatically", "interval": "Interval", "seconds": "seconds", "showIndicators": "Show Indicators", "showNavigation": "Show Navigation", "transitionType": "Transition Type", "transitionTypeHelp": "Choose how slides transition between each other", "transitionDuration": "Transition Duration", "transitionDurationHelp": "Time in milliseconds for animation between slides", "slide": "Slide", "fade": "Fade", "cube": "3D Cube", "flip": "Flip", "none": "No Animation", "height": "Height", "primary": "Primary", "secondary": "Secondary", "outline": "Outline", "ghost": "Ghost", "layoutStandard": "Standard", "layoutFullScreen": "Full Screen", "layoutCard": "Card", "layoutMinimal": "Minimal", "dimensions": "Dimensions", "heightPlaceholder": "Ex: 450px, 50vh", "heightHelp": "Use px, vh, rem or %", "borderRadiusPlaceholder": "Ex: 12px, 1rem", "textSettings": "Text Settings", "textAlignment": "Text Alignment", "alignment": "Alignment", "overlayOpacity": "Overlay Opacity", "overlayOpacityHelp": "Controls transparency of dark background over images", "buttonSettings": "Button Settings", "defaultButtonStyle": "De<PERSON>ult <PERSON>", "mobileLayout": "Mobile Layout", "mobileSettings": "Mobile Settings", "stackTextOnMobile": "Stack Text on Mobile", "mobileHeight": "Mobile Height", "mobileHeightPlaceholder": "Ex: 300px, 40vh", "left": "Left", "right": "Right", "top": "Top", "bottom": "Bottom", "bottomLeft": "Bottom Left", "bottomRight": "Bottom Right", "unit": "Unit", "titleScale": "Title Scale", "subtitleScale": "Subtitle Scale", "overlayColor": "Overlay Color", "navigationSettings": "Navigation Settings", "indicatorColor": "Indicator Color", "navigationButtonColor": "Navigation Button Color", "slideTitlePlaceholder": "Enter slide title", "slideSubtitlePlaceholder": "Enter slide subtitle", "buttonTextPlaceholder": "Enter button text", "buttonLinkPlaceholder": "https://example.com", "buttonUrlPlaceholder": "https://example.com", "untitled": "Untitled", "reset": "Reset", "editCompanyInformation": "Edit Company Information", "videoConfigEditor": "Video Configuration Editor", "videoContent": "Content", "videoInfo": "Information", "videoPlayback": "Playback", "videoSource": "Video Source", "videoInformation": "Video Information", "videoUrl": "Video URL", "videoUrlLabel": "Video URL", "videoUrlPlaceholder": "https://www.youtube.com/watch?v=...", "videoType": {"youtube": "YouTube", "vimeo": "Vimeo", "direct": "Direct"}, "videoPreview": "Preview", "videoPreviewPlaceholder": "Invalid or unsupported URL", "videoTitleLabel": "Video Title", "videoTitlePlaceholder": "My Amazing Video", "videoDescriptionLabel": "Description", "videoDescriptionPlaceholder": "Video description...", "videoPosterLabel": "Cover Image", "videoPosterPlaceholder": "https://example.com/image.jpg", "videoPosterHint": "URL of image that appears before video loads", "playbackSettings": "Playback Settings", "autoplay": "Autoplay", "autoplayDescription": "Start playback automatically when page loads", "loop": "Loop", "loopDescription": "Repeat video when it ends", "muted": "Muted", "mutedDescription": "Start video without sound", "controls": "Controls", "controlsDescription": "Show playback controls", "editVideoConfig": "Configure Video", "videoDimensions": "Video Dimensions", "videoWidth": "<PERSON><PERSON><PERSON>", "videoHeight": "Height", "videoWidthPlaceholder": "100%, 500px, 50vw", "videoHeightPlaceholder": "400", "videoResponsive": "Responsive", "videoResponsiveHint": "Automatically adjust to screen size (16:9 ratio)", "videoErrors": {"invalidUrl": "Invalid video URL", "youtubeInvalidId": "Invalid YouTube ID in URL", "vimeoInvalidId": "Invalid Vimeo ID in URL", "directVideoError": "Error loading direct video", "networkError": "Connection error loading video", "unsupportedFormat": "Unsupported video format"}, "responsive": "Responsive", "videoStartTime": "Start Time", "videoEndTime": "End Time", "videoStartTimeHelp": "Time in seconds to start video", "videoEndTimeHelp": "Time in seconds to stop video (0 = until end)", "confirmDelete": {"title": "Confirm Deletion", "message": "Are you sure you want to remove this element?", "cancel": "Cancel", "confirm": "Delete"}, "notifications": {"componentAdded": "Component added successfully", "componentDeleted": "Element removed successfully", "changesSaved": "Changes saved successfully", "saveSuccess": "<PERSON> saved successfully", "errorDeleting": "Error removing element"}, "statementConfig": "Statement Configuration", "editStatementConfig": "Edit Statement", "text": "Text", "textPlaceholder": "Enter text here...", "button": "<PERSON><PERSON>", "preview": "Preview", "textPreview": "This is a preview text for the statement", "buttonPreview": "Click here", "title": "Title", "titlePlaceholder": "Enter title...", "subtitle": "Subtitle", "subtitlePlaceholder": "Enter subtitle...", "description": "Description", "descriptionPlaceholder": "Enter description...", "general": "General", "colors": "Colors", "typography": "Typography", "spacing": "Spacing", "borders": "Borders", "grid": "Grid", "list": "List", "cards": "Cards", "locations": "Locations", "manageLocations": "Manage Locations", "addLocation": "Add Location", "newLocation": "New Location", "locationName": "Location Name", "locationNamePlaceholder": "Ex: Our Main Store", "address": "Address", "addressPlaceholder": "Enter complete address...", "phone": "Phone", "phonePlaceholder": "****** 999-9999", "email": "Email", "emailPlaceholder": "<EMAIL>", "hours": "Hours", "hoursPlaceholder": "Mon-Fri: 9am to 6pm", "image": "Image", "imagePlaceholder": "https://example.com/image.jpg", "imageUrlPlaceholder": "https://example.com/image.jpg", "imagePreview": "Image Preview", "edit": "Edit", "showMap": "Show Map", "socialLinks": "Social Links", "moveUp": "Move Up", "moveDown": "Move Down", "backgroundColors": "Background Colors", "sectionBackground": "Section Background", "cardBackground": "Card Background", "textColors": "Text Colors", "accentColor": "Accent Color", "sectionPadding": "Section Padding", "cardPadding": "Card Padding", "itemGap": "Item Gap", "shadowIntensity": "Shadow Intensity", "light": "Light", "medium": "Medium", "heavy": "Heavy", "maxWidth": "<PERSON>", "containerMaxWidth": "Container <PERSON>", "titletypography": "Title Typography", "titleFontSize": "Title Font Size", "titleFontWeight": "Title Font Weight", "locationNameTypography": "Location Name Typography", "locationNameFontSize": "Name Font Size", "locationNameFontWeight": "Name Font Weight", "updateMap": "Update Map", "mapUpdateHint": "Click to update map with current address", "bodyText": "Body Text", "bodyFontSize": "Body Font Size", "lineHeight": "Line Height", "normal": "Normal", "semibold": "Semibold", "bold": "Bold", "statementEditor": "Statement Editor", "companyInformationEditor": "Company Information Editor", "editPaymentBenefitsContent": "Edit Benefits Content", "editPaymentBenefitsDesign": "Edit Benefits Design", "paymentBenefitsContentEditor": "Benefits Content Editor", "paymentBenefitsDesignEditor": "Benefits Design Editor", "pontosDePagamento": "Payment Benefits", "adicionarPontoDePagamento": "Add Payment Benefit", "pontoDePagamento": "Payment Benefit", "newBenefit": "New Benefit", "descricao": "Description", "enterDescription": "Enter description...", "icone": "Icon", "ativar": "Activate", "customIcon": "Custom Icon", "usePresetIcon": "Use Preset Icon", "enterSvgCode": "Enter SVG code...", "iconeSettings": "Icon Settings", "showIcons": "Show Icons", "iconPosition": "Icon Position", "iconLeft": "Left", "iconTop": "Top", "iconRight": "Right", "iconBottom": "Bottom", "layoutType": "Layout Type", "standardLayout": "Standard Layout", "invertedLayout": "Inverted Layout", "spacingBetweenItems": "Spacing Between Items", "enterTitle": "Enter title", "titulo": "Title", "headerEditor": "Header Editor", "footerEditor": "Footer Editor", "editHeader": "Configure <PERSON><PERSON>", "editFooter": "Configure <PERSON><PERSON>", "logo": "Logo", "logoText": "Logo Text", "enterLogoText": "Enter logo text", "logoPosition": "Logo Position", "logoMobilePosition": "Logo Position (Mobile)", "menu": "<PERSON><PERSON>", "menuConfiguration": "Menu Configuration", "menuPosition": "Menu Position", "menuType": "Menu Type", "horizontal": "Horizontal", "dropdown": "Dropdown", "menuItems": "Menu Items", "addMenuItem": "Add Item", "menuTitle": "Menu Title", "menuUrl": "Menu URL", "hasDropdown": "Has Dropdown", "services": "Services", "servicesConfiguration": "Services Configuration", "countrySelector": "Country Selector", "languageSelector": "Language Selector", "accountAvatar": "Account <PERSON><PERSON>", "showSeparator": "Show Separator", "paddingTop": "Top Padding", "paddingBottom": "Bottom Padding", "innerMargin": "Inner Margin", "storeName": "Store Name", "storeNamePlaceholder": "Enter store name", "navigation": "Navigation", "showNavigationLinks": "Show Navigation Links", "navigationLinks": "Navigation Links", "addLink": "Add Link", "linkTitle": "Link Title", "copyright": "Copyright", "showCopyright": "Show Copyright", "copyrightText": "Copyright Text", "copyrightPlaceholder": "Enter copyright text", "madeWith": "Made With", "showMadeWith": "Show 'Made With'", "madeWithText": "Made With Text", "madeWithPlaceholder": "Made with", "brandName": "Brand Name", "brandNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> by Lightspeed", "brandLink": "Brand Link", "brandLinkPlaceholder": "https://www.lightspeedhq.com/products/ecommerce/", "contact": "Contact", "contactInformation": "Contact Information", "showContactInfo": "Show Contact Information", "contactEmail": "Contact Email", "contactEmailPlaceholder": "<EMAIL>", "contactPhone": "Contact Phone", "contactPhonePlaceholder": "(11) 99999-9999", "contactAddress": "Address", "contactAddressPlaceholder": "123 Example St - São Paulo, SP", "social": "Social", "socialNetworks": "Social Networks", "showSocialNetworks": "Show Social Networks", "facebookUrl": "Facebook URL", "facebookPlaceholder": "https://facebook.com/yourpage", "instagramUrl": "Instagram URL", "instagramPlaceholder": "https://instagram.com/yourusername", "twitterUrl": "Twitter URL", "twitterPlaceholder": "https://twitter.com/yourusername", "whatsappUrl": "WhatsApp URL", "whatsappPlaceholder": "https://wa.me/5511999999999", "design": "Design", "layoutStyle": "Layout Style", "multiColumn": "Multi-column", "singleColumn": "Single column", "linkColor": "Link Color", "linkHoverColor": "Link Hover Color", "benefits": "Benefits", "generalConfiguration": "General Configuration", "sectionTitle": "Section Title", "sectionTitlePlaceholder": "Enter section title", "enabled": "Enabled", "disabled": "Disabled", "addBenefit": "Add Benefit", "benefitConfiguration": "Benefit Configuration", "benefitTitle": "Benefit Title", "benefitTitlePlaceholder": "Enter benefit title", "benefitDescription": "Benefit Description", "benefitDescriptionPlaceholder": "Enter benefit description", "benefitIcon": "Benefit Icon", "enableBenefit": "Enable Benefit", "iconConfiguration": "Icon Configuration", "iconSize": "Icon Size", "icons": "Icons", "style": "Style", "ourBenefits": "Our Benefits", "benefit": "Benefit", "benefitDescriptionExample": "Brief benefit description", "layoutConfiguration": "Layout Configuration", "behaviorConfiguration": "Behavior Configuration", "dimensionsConfiguration": "Dimensions Configuration", "textConfiguration": "Text Configuration", "buttonConfiguration": "Button Configuration", "navigationConfiguration": "Navigation Configuration", "mobileConfiguration": "Mobile Configuration", "colorConfiguration": "Color Configuration", "styleConfiguration": "Style Configuration", "paddingConfiguration": "Padding Configuration", "marginConfiguration": "Margin Configuration", "borderRadiusConfiguration": "Border Radius Configuration", "companyTitlePlaceholder": "Company title", "companyDescriptionPlaceholder": "Company description", "layoutInverted": "Inverted", "descriptionColor": "Description Color", "solidColor": "Solid color", "transparent": "Transparent", "boxShadow": "Box Shadow", "buttonPrimaryColor": "Button Primary Color", "buttonScale": "Button Scale", "animationType": "Animation Type", "animationSettings": "Animation Settings", "advanced": "Advanced", "animationTypeConfiguration": "Animation Type Configuration", "animationTrigger": "Animation Trigger", "fadeIn": "Fade In", "slideInLeft": "Slide In Left", "slideInRight": "Slide In Right", "slideInUp": "Slide In Up", "slideInDown": "Slide In Down", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "rotateIn": "Rotate In", "bounceIn": "Bounce In", "flipHorizontal": "<PERSON><PERSON>", "flipVertical": "Flip Vertical", "pulse": "Pulse", "shake": "Shake", "custom": "Custom", "onLoad": "On Load", "onHover": "On Hover", "onScroll": "When Visible", "onClick": "On Click", "continuous": "Continuous", "animationSettingsConfiguration": "Animation Settings Configuration", "duration": "Duration", "timingFunction": "Timing Function", "smooth": "Smooth", "acceleration": "Acceleration", "deceleration": "Deceleration", "smoothStartEnd": "Smooth Start/End", "bounce": "<PERSON><PERSON><PERSON>", "delay": "Delay", "iterations": "Iterations", "once": "Once", "twice": "Twice", "thrice": "3 times", "infinite": "Infinite", "advancedConfiguration": "Advanced Configuration", "customKeyframes": "Custom Keyframes", "keyframesPlaceholder": "0% { transform: scale(1); }\n50% { transform: scale(1.2); }\n100% { transform: scale(1); }", "keyframesHelp": "Enter custom CSS keyframes", "animationPreview": "Animation Preview", "playPreview": "Play Preview", "removeAnimation": "Remove Animation", "descriptionSize": "Description Size", "rotation": "Rotation", "scale": "Scale", "position": "Position", "rotationConfiguration": "Rotation Configuration", "rotationAngle": "Rotation Angle", "quickAngles": "<PERSON>", "scaleConfiguration": "Scale Configuration", "horizontalScale": "Horizontal Scale", "verticalScale": "Vertical Scale", "maintainAspectRatio": "Maintain Aspect Ratio", "positionConfiguration": "Position Configuration", "horizontalPosition": "Horizontal Position", "verticalPosition": "Vertical Position", "transformPreview": "Transform Preview", "border": "Border", "borderConfiguration": "Border Configuration", "borderWidth": "Border Width", "borderStyle": "Border Style", "borderColor": "Border Color", "solid": "Solid", "dashed": "Dashed", "dotted": "Dotted", "double": "Double", "radiusCorners": "<PERSON><PERSON>", "shadowConfiguration": "Shadow Configuration", "shadowOffsetX": "Shadow Offset X", "shadowOffsetY": "Shadow Offset Y", "shadowBlur": "Shadow Blur", "shadowSpread": "Shadow Spread", "shadowColor": "Shadow Color", "shadowPresets": "Shadow Presets", "basicFilters": "Basic Filters", "colorFilters": "Color Filters", "effects": "Effects", "basicFiltersConfiguration": "Basic Filters Configuration", "colorFiltersConfiguration": "Color Filters Configuration", "effectsConfiguration": "Effects Configuration", "filterPreview": "Filter Preview", "filterPresets": "Filter Presets", "opacity": "Opacity", "showTitle": "Show Title", "showSubtitle": "Show Subtitle", "elements": "Elements", "showStars": "Show Stars", "showAvatar": "Show Avatar", "starColor": "Star Color", "cardBackgroundColor": "Card Background Color", "cardBorderRadius": "Card Border Radius", "cardShadow": "Card Shadow", "lightShadow": "Light Shadow", "heavyShadow": "Heavy Shadow", "styling": "Styl<PERSON>", "column": "Column", "columns": "Columns", "enterSubtitle": "Enter subtitle", "header": "Header", "video": "Video", "sidebar": {"layoutEditor": "Layout Editor", "addEditComponents": "Add and edit components", "backToMenu": "Back to menu", "closeSidebar": "Close sidebar", "pageConfiguration": "Page Configuration", "pageConfigurationDesc": "General page settings, theme and layout", "components": "Components", "styleEditors": "Style Editors", "noComponentsAvailable": "No components available", "loadingComponents": "Loading components...", "pageSections": "Page Sections", "addComponent": "Add Component", "selectElementToEdit": "Select an element to edit", "selectElementDesc": "Click on an element in the page to edit it here", "backToComponents": "Back to components", "loadingEditor": "Loading editor...", "editorError": "Editor error", "tryAgain": "Try again", "basicEditor": "Basic Editor", "spacing": "Spacing", "spacingDesc": "Adjust margins and spacing", "border": "Borders", "borderDesc": "Configure borders and rounded corners", "animation": "Animation", "animationDesc": "Add animation effects", "transform": "Transform", "transformDesc": "Rotate, scale and move", "filter": "Filters", "filterDesc": "Apply visual filters", "headerEditor": "Header Editor", "footerEditor": "Footer Editor", "carouselEditor": "Carousel Editor", "videoEditor": "Video Editor", "companyInfoEditor": "Company Information Editor", "locationEditor": "Location Editor", "statementEditor": "Statement Editor", "paymentBenefitsEditor": "Payment Benefits Editor", "customerReviewEditor": "Customer Review Editor", "specialOffersEditor": "Special Offers Editor", "productSelection": "Product Selection", "productStyle": "Product Style", "elementEditor": "Element Editor", "editingComponent": "Editing component {component}", "editingElement": "Editing element {element}", "customizeProperties": "Customize properties", "customizePageSettings": "Customize page settings", "currentPageSections": "Current page sections", "addNewSection": "Add new section", "manageSections": "Manage sections", "header": "Header", "footer": "Footer", "carousel": "Carousel", "video": "Video", "companyInfo": "Company Information", "location": "Location", "statement": "Statement", "paymentBenefits": "Payment Benefits", "customerReview": "Customer Reviews", "specialOffers": "Special Offers", "component": "Component", "htmlElement": "HTML Element", "heading1": "Heading 1", "heading2": "Heading 2", "heading3": "Heading 3", "heading4": "Heading 4", "heading5": "Heading 5", "heading6": "Heading 6", "paragraph": "Paragraph", "image": "Image", "link": "Link", "container": "Container", "text": "Text", "editorLoadError": "Error loading editor", "selectElement": "Select element", "addSection": "Add Section", "noSectionsFound": "No sections found", "expandSidebar": "Expand sidebar", "elementSelected": "Element selected", "deleteElement": "Delete Element", "deleteElementConfirmation": "Are you sure you want to delete this element?", "deleteElementConfirmationMessage": "This action cannot be undone.", "deleteElementConfirmationButton": "Delete", "deleteElementCancelButton": "Cancel", "general": "General", "design": "Design", "settings": "Settings", "content": "Content", "benefits": "Benefits", "offers": "Offers", "slides": "Slides", "locations": "Locations", "title": "Title", "titlePlaceholder": "Enter section title", "subtitle": "Subtitle", "subtitlePlaceholder": "Enter section subtitle", "layout": "Layout", "layoutGrid": "Grid", "layoutList": "List", "layoutCards": "Cards", "manageLocations": "Manage Locations", "addLocation": "Add Location", "newLocation": "New Location", "locationName": "Location Name", "locationNamePlaceholder": "Enter location name", "description": "Description", "descriptionPlaceholder": "Enter description", "address": "Address", "addressPlaceholder": "Enter complete address", "phone": "Phone", "email": "Email", "hours": "Business Hours", "hoursPlaceholder": "Ex: Mon-Fri: 9am to 6pm", "imageUrl": "Image URL", "selectImage": "Select Image", "showMap": "Show Map", "socialLinks": "Social Links", "moveUp": "Move Up", "moveDown": "Move Down", "remove": "Remove", "colors": "Colors", "sectionBackground": "Section Background", "cardBackground": "Card Background", "titleColor": "Title Color", "textColor": "Text Color", "iconColor": "Icon Color", "accentColor": "Accent Color", "badgeColor": "Badge Color", "overlayColor": "Overlay Color", "descriptionColor": "Description Color", "buttonColor": "Button Color", "sectionPadding": "Section Padding", "cardPadding": "Card Padding", "borderRadius": "Border Radius", "columns": "Columns", "typography": "Typography", "titleFontSize": "Title Font Size", "bodyFontSize": "Body Font Size", "descriptionFontSize": "Description Font Size", "priceFontSize": "Price Font Size", "badgeFontSize": "Badge Font Size", "iconSize": "Icon Size", "manageBenefits": "Manage Benefits", "addBenefit": "Add Benefit", "newBenefit": "New Benefit", "benefitTitle": "Benefit Title", "benefitTitlePlaceholder": "Enter benefit title", "benefitDescription": "Benefit Description", "benefitDescriptionPlaceholder": "Enter benefit description", "benefitIcon": "Benefit Icon", "enableBenefit": "Enable Benefit", "enabled": "Enabled", "disabled": "Disabled", "sectionTitle": "Section Title", "sectionTitlePlaceholder": "Enter section title", "calculatedPrice": "Calculated Price: $", "manageOffers": "Manage Offers", "addOffer": "<PERSON>d Offer", "newOffer": "<PERSON> Offer", "offerTitle": "Offer Title", "offerTitlePlaceholder": "Enter offer title", "offerTitleRequired": "Offer title is required", "offerDescription": "Offer Description", "offerDescriptionPlaceholder": "Enter offer description", "offerDescriptionRequired": "Offer description is required", "badge": "Badge", "badgePlaceholder": "Ex: 50% OFF", "discount": "Discount", "originalPrice": "Original Price", "finalPrice": "Final Price", "offerLink": "Offer Link", "offerLinkPlaceholder": "https://example.com/offer", "invalidUrl": "Invalid URL", "linkText": "Link Text", "linkTextPlaceholder": "View offer", "linkTextHelp": "Text that appears in the offer button", "activeOffer": "Active Offer", "active": "Active", "inactive": "Inactive", "optional": "Optional - appears as a highlight in the offer", "activeOfferHelp": "Uncheck to hide this offer", "discountValue": "Discount Value", "offerPreview": "Offer Preview", "noOffersFound": "No offers found", "createFirstOffer": "Create your first offer to start.", "selectionProblem": "Selection Problem", "availableOffers": "Available Offers", "currentOfferIndex": "Current Offer Index", "sectionTitleFontSize": "Section Title Font Size", "cardTitleFontSize": "Card Title Font Size", "specialOffersSubtitle": "Take advantage of our exclusive offers with unbeatable discounts", "finalPriceMustBeLessThanOriginalPrice": "Final price must be less than original price", "fixSelection": "Fix Selection", "selectOffer": "Select Offer", "duplicate": "Duplicate", "reset": "Reset", "manageSlides": "Manage Slides", "addSlide": "Add Slide", "newSlide": "New Slide", "slideTitle": "Slide Title", "slideTitlePlaceholder": "Enter slide title", "slideDescription": "Slide Description", "slideDescriptionPlaceholder": "Enter slide description", "slideImage": "Slide Image", "slideLink": "Slide Link", "slideLinkPlaceholder": "https://example.com", "activeSlide": "Active Slide", "autoPlay": "Auto Play", "enableAutoPlay": "Enable Auto Play", "autoPlaySpeed": "Auto Play Speed", "showArrows": "Show Arrows", "enableArrows": "Enable Arrows", "showDots": "Show Dots", "enableDots": "Enable Dots", "transition": "Transition", "dimensions": "Dimensions", "height": "Height", "overlayOpacity": "Overlay Opacity"}, "layoutCreatedSuccess": "Layout '{name}' created successfully!", "layoutCreatedSuccessDesc": "The new layout is available for editing", "layoutCreatedError": "Error creating layout", "layoutCreatedErrorDesc": "Could not create the layout. Please try again."}