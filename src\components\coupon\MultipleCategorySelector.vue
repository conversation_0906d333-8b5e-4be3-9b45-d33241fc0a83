<template>
  <div class="h-full max-h-[calc(100vh-220px)] overflow-y-auto rounded-lg border border-gray-200 p-3">
    <div v-if="categories.length === 0" class="py-4 text-center text-gray-400">
      <p>{{ t('category.loadingCategories') }}</p>
    </div>
    
    <div v-else class="space-y-1">
      <!-- Selected categories section -->
      <div v-if="selectedCategoryIds.length > 0" class="mb-3 space-y-2 rounded-lg bg-gray-50 p-3">
        <div>
          <span class="text-sm font-medium text-gray-600">
            {{ selectedCategoryIds.length }} {{ selectedCategoryIds.length === 1 ? t('category.selectedSingular') : t('category.selectedPlural') }}
          </span>
        </div>
        
        <div class="flex flex-wrap gap-2">
          <div 
            v-for="categoryId in selectedCategoryIds" 
            :key="categoryId" 
            class="flex items-center rounded-full bg-indigo-100 px-3 py-1 text-sm font-medium text-indigo-800"
          >
            <span>{{ getCategoryName(categoryId) }}</span>
            <button 
              type="button" 
              @click.stop="removeCategory(categoryId)" 
              class="ml-1.5 text-indigo-600 hover:text-indigo-800"
            >
              ×
            </button>
          </div>
        </div>
      </div>
      
      <div v-else-if="selectedCategoryIds.length === 0" class="py-3 text-center text-gray-400">
        <p>{{ t('category.selectCategories') }}</p>
      </div>
      
      <!-- Categories tree -->
      <div class="space-y-1.5">
        <div v-for="category in categories" :key="category.id" class="space-y-1.5">
          <!-- Level 1 -->
          <div 
            class="flex items-center rounded-md p-2 hover:bg-gray-50" 
            @click="toggleCategory(category)"
          >
            <div class="mr-3 flex h-5 w-5 items-center justify-center rounded border border-gray-300">
              <div 
                v-if="isSelected(category.id)" 
                class="h-full w-full rounded bg-indigo-600"
              />
            </div>
            <span class="text-sm font-bold text-gray-700">{{ category.title }}</span>
          </div>
          
          <!-- Level 2 -->
          <div v-if="category.children" class="ml-6 space-y-1.5">
            <div 
              v-for="subCategory in category.children" 
              :key="subCategory.id" 
              class="space-y-1.5"
            >
              <div 
                class="flex items-center rounded-md p-2 hover:bg-gray-50" 
                @click="toggleCategory(subCategory)"
              >
                <div class="mr-3 flex h-5 w-5 items-center justify-center rounded border border-gray-300">
                  <div 
                    v-if="isSelected(subCategory.id)" 
                    class="h-full w-full rounded bg-indigo-600"
                  />
                </div>
                <span class="text-sm font-semibold text-gray-700">{{ subCategory.title }}</span>
              </div>
              
              <!-- Level 3 -->
              <div v-if="subCategory.children" class="ml-6 space-y-1.5">
                <div 
                  v-for="thirdLevel in subCategory.children" 
                  :key="thirdLevel.id" 
                  class="flex items-center rounded-md p-2 hover:bg-gray-50" 
                  @click="toggleCategory(thirdLevel)"
                >
                  <div class="mr-3 flex h-5 w-5 items-center justify-center rounded border border-gray-300">
                    <div 
                      v-if="isSelected(thirdLevel.id)" 
                      class="h-full w-full rounded bg-indigo-600"
                    />
                  </div>
                  <span class="text-sm font-medium text-gray-700">{{ thirdLevel.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { categoryService } from '@/services/category.service';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  storeId: {
    type: String,
    required: true
  },
  placeholder: {
    type: String,
    default: 'Selecione as categorias'
  }
});

const emit = defineEmits(['update:modelValue']);
const categories = ref([]);
const selectedCategoryIds = ref(props.modelValue || []);

watch(() => props.modelValue, (newValue) => {
  selectedCategoryIds.value = newValue || [];
}, { deep: true });

onMounted(async () => {
  try {
    const categoriesData = await categoryService.fetchCategories();
    categories.value = categoriesData || [];
  } catch (error) {
    console.error('Erro ao carregar categorias:', error);
  }
});

function toggleCategory(category) {
  const categoryId = category.id;
  const index = selectedCategoryIds.value.indexOf(categoryId);
  
  if (index === -1) {
    selectedCategoryIds.value.push(categoryId);
  } else {
    selectedCategoryIds.value.splice(index, 1);
  }
  emit('update:modelValue', [...selectedCategoryIds.value]);
}

function removeCategory(categoryId) {
  const index = selectedCategoryIds.value.indexOf(categoryId);
  if (index !== -1) {
    selectedCategoryIds.value.splice(index, 1);
  }
  emit('update:modelValue', [...selectedCategoryIds.value]);
}

function getCategoryName(categoryId) {
  for (const category of categories.value) {
    if (category.id === categoryId) {
      return category.title;
    }
    
    if (category.children) {
      for (const subCategory of category.children) {
        if (subCategory.id === categoryId) {
          return subCategory.title;
        }
        
        if (subCategory.children) {
          for (const thirdLevel of subCategory.children) {
            if (thirdLevel.id === categoryId) {
              return thirdLevel.title;
            }
          }
        }
      }
    }
  }
  
  return 'Categoria';
}

function isSelected(categoryId) {
  return selectedCategoryIds.value.includes(categoryId);
}
</script>
