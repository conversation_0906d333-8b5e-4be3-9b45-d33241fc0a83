import axios from 'axios';
import { API_URLS } from '../config/api.config';

class CustomerService {
    #customerData;
    #endpoint = `${API_URLS.CUSTOMER_BASE_URL}`; 

    constructor() {
        this.#customerData = {};
    }

    // Método para obter os clientes
    async getCustomers(filter = '', page = 0, size = 5) {
        try {
            const response = await axios.get(this.#endpoint + '/search', {
                params: { filter, page, size }
            });
            
            return {
                content: response.data.content || [],  
                totalPages: response.data.page?.totalPages || 0,
                page: response.data.page || null
            };
        } catch (error) {
            console.error('GetCustomers error:', error);
            throw error;
        }
    }

    // Método para criar um cliente
    async createCustomer(customerData) {
        try {
            const response = await axios.post(this.#endpoint, customerData);
            this.#customerData = response.data;
            return this.#customerData; 
        } catch (error) {
            console.error('CreateCustomer error:', error);
            throw error;
        }
    }

    // Método para atualizar um cliente
    async updateCustomer(customerId, customerData) {
        try {
            const response = await axios.put(`${this.#endpoint}/${customerId}`, customerData);
            this.#customerData = response.data;
            return this.#customerData;  
        } catch (error) {
            console.error('UpdateCustomer error:', error);
            throw error;
        }
    }

    // Método para excluir um cliente
    async deleteCustomer(customerId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${customerId}`);
            return response.data;  // Retorna algum status de sucesso após a exclusão
        } catch (error) {
            console.error('DeleteCustomer error:', error);
            throw error;
        }
    }

    // Método para excluir múltiplos clientes
    async deleteCustomers(customerIds) {
        try {
            if (!Array.isArray(customerIds) || customerIds.length === 0) {
                throw new Error('IDs de clientes devem ser fornecidos como array não vazio');
            }

            const response = await axios.delete(`${this.#endpoint}/bulk`, {
                data: { customerIds }
            });
            
            return response.data;
        } catch (error) {
            console.error('DeleteCustomers error:', error);
            throw error;
        }
    }


    async getCustomer(customerId) {
        try {
            const response = await axios.get(`${this.#endpoint}/${customerId}`);
            return response.data;
        } catch (error) {
            console.error('GetCustomer error:', error);
            throw error;
        }
    }

    // Método para fazer upload da foto do cliente
    async uploadPhoto(customerId, file) {
        try {
            if (!file?.type?.startsWith('image/')) {
                throw new Error('O arquivo deve ser uma imagem válida');
            }

            const formData = new FormData();
            formData.append('file', file);

            const response = await axios.post(`${this.#endpoint}/${customerId}/photo`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            return response.data;
        } catch (error) {
            console.error('UploadPhoto error:', error);
            throw error;
        }
    }

    // Método para deletar a foto do cliente
    async deletePhoto(customerId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${customerId}/photo`);
            return response.data;
        } catch (error) {
            console.error('DeletePhoto error:', error);
            throw error;
        }
    }
}

export default new CustomerService();
