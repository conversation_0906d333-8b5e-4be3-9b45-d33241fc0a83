export default {
  name: '<PERSON><PERSON><PERSON><PERSON>',
  type: 'video',
  dataComponent: 'video',
  elementType: 'video',
  className: 'iluria-video',
  selectors: ['[data-component="video"]', '.iluria-video'],
  category: 'media',
  description: 'Componente de vídeo moderno com suporte a YouTube, Vimeo e vídeos diretos',
  priority: 80,
  icon: 'play_circle',
  
  html: `<div 
    data-component="video" 
    data-element-type="video"
    data-video-title="Principais itens básicos essenciais"
    data-video-description="Não sabe por onde começar para criar um guarda-roupa cápsula? Comece pelo básico. Crie uma base consistente com a nossa nova coleção básica unissex. Confira o vídeo para algumas combinações fáceis."
    data-button-text="Compre agora"
    data-button-url="#"
    data-button-enabled="true"
    data-video-url=""
    data-video-type="youtube"
    data-video-poster=""
    data-video-autoplay="false"
    data-video-loop="false"
    data-video-muted="false"
    data-video-controls="true"
    data-video-aspect-ratio="16:9"
    data-layout="horizontal"
    data-video-position="left"
    data-bg-color="#ffffff"
    data-text-color="#000000"
    data-button-color="#000000"
    data-button-text-color="#ffffff"
    data-padding-top="60"
    data-padding-bottom="60"
    data-border-radius="12"
    class="iluria-video"
    style="background-color: #ffffff; color: #000000; padding: 60px 0; width: 100%; border-radius: 12px; box-shadow: 0 2px 10px rgba(0,0,0,0.1);">
    <style>
      @media (max-width: 768px) {
        .iluria-video .video-content { flex-direction: column !important; text-align: center !important; gap: 1.5rem !important; }
        .iluria-video .video-section { max-width: 100% !important; }
        .iluria-video .video-title { font-size: 1.5rem !important; }
      }
    </style>
    <div class="video-container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
      <div class="video-content" style="display: flex; gap: 2rem; align-items: center;">
        <div class="video-section" style="flex: 1;">
          <div class="video-wrapper" style="aspect-ratio: 16/9; border-radius: 8px; overflow: hidden; background: rgba(0,0,0,0.1);">
            <div class="video-placeholder" style="width: 100%; height: 100%; display: flex; align-items: center; justify-content: center; color: #000000;">
              <div class="placeholder-content" style="text-align: center; opacity: 0.7;">
                <svg class="placeholder-icon" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="margin: 0 auto 10px;">
                  <polygon points="5,3 19,12 5,21"/>
                </svg>
                <p class="placeholder-text" style="margin: 0; font-size: 14px;">Configure o vídeo para visualizar</p>
              </div>
            </div>
          </div>
        </div>
        <div class="content-section" style="flex: 1; padding: 1rem;">
          <h2 class="video-title" style="font-size: 2rem; font-weight: bold; margin: 0 0 1rem; color: #000000; line-height: 1.2;">Principais itens básicos essenciais</h2>
          <p class="video-description" style="font-size: 1rem; line-height: 1.6; margin: 0 0 1.5rem; color: #000000; opacity: 0.8;">Não sabe por onde começar para criar um guarda-roupa cápsula? Comece pelo básico. Crie uma base consistente com a nossa nova coleção básica unissex. Confira o vídeo para algumas combinações fáceis.</p>
          <a href="#" class="video-button" style="background-color: #000000; color: #ffffff; padding: 12px 30px; border: none; border-radius: 6px; font-size: 1rem; font-weight: 600; text-decoration: none; display: inline-block; cursor: pointer; transition: all 0.3s ease;">Compre agora</a>
        </div>
      </div>
    </div>
  </div>
  
  <!-- CSS para neutralizar margens do container -->
  <style>
    .iluria-video {
      margin: 0 !important;
      width: 100% !important;
      box-sizing: border-box !important;
    }
    
    /* Neutralizar padding do element-content apenas para video */
    .component-container:has(.iluria-video) .element-content {
      padding: 0 !important;
      margin: 0 !important;
    }
    
    /* Fallback para navegadores sem :has() */
    .element-content:has(.iluria-video) {
      padding: 0 !important;
      margin: 0 !important;
    }
  </style>`,
  
  toolbarActions: [
    {
      type: 'video-config',
      title: 'Configurar Vídeo',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <polygon points="23 7 16 12 23 17 23 7"/>
        <rect x="1" y="5" width="15" height="14" rx="2" ry="2"/>
      </svg>`,
      condition: (element) => {
        return element?.getAttribute('data-component') === 'video'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'video-config',
      component: 'VideoConfigEditor',
              path: '@/components/layoutEditor/Sidebar/editors/VideoConfigEditor.vue'
    }
  ],
  
  attributes: {
    preserve: [
      'data-component', 'data-element-type',
      'data-video-title', 'data-video-description', 'data-button-text', 'data-button-url', 'data-button-enabled',
      'data-video-url', 'data-video-type', 'data-video-poster', 'data-video-autoplay',
      'data-video-loop', 'data-video-muted', 'data-video-controls', 'data-video-aspect-ratio',
      'data-layout', 'data-video-position',
      'data-bg-color', 'data-text-color', 'data-button-color', 'data-button-text-color',
      'data-padding-top', 'data-padding-bottom', 'data-border-radius'
    ],
    defaults: {
      'data-component': 'video',
      'data-element-type': 'video',
      'data-video-title': 'Principais itens básicos essenciais',
      'data-video-description': 'Não sabe por onde começar para criar um guarda-roupa cápsula?',
      'data-button-text': 'Compre agora',
      'data-button-url': '#',
      'data-button-enabled': 'true',
      'data-video-type': 'youtube',
      'data-video-autoplay': 'false',
      'data-video-loop': 'false',
      'data-video-muted': 'false',
      'data-video-controls': 'true',
      'data-video-aspect-ratio': '16:9',
      'data-layout': 'horizontal',
      'data-video-position': 'left',
      'data-bg-color': '#ffffff',
      'data-text-color': '#000000',
      'data-button-color': '#000000',
      'data-button-text-color': '#ffffff',
      'data-padding-top': '60',
      'data-padding-bottom': '60',
      'data-border-radius': '12'
    }
  },
  
  initialization: {
    autoInit: true,
    reinitOnChange: true,
    scriptLoader: 'initializeVideoComponent'
  },
  
  cleanup: {
    removeEditingStyles: false,
    preserveStyles: [
      'background-color', 'color', 'padding', 'margin', 'border-radius', 
      'width', 'box-shadow', 'display', 'flex-direction', 'gap', 'align-items',
      'max-width', 'aspect-ratio', 'overflow', 'font-size', 'font-weight',
      'line-height', 'opacity', 'text-decoration', 'cursor', 'transition'
    ],
    childSelectors: [
      '.video-container', '.video-content', '.video-section', '.video-wrapper',
      '.video-iframe', '.video-element', '.video-placeholder', '.placeholder-content',
      '.content-section', '.video-title', '.video-description', '.video-button'
    ],
    specialElements: {
      '.video-button': {
        preserveInlineStyles: true,
        enforceDisplay: 'inline-block'
      }
    }
  },
  
  detection: {
    priority: 70
  }
} 