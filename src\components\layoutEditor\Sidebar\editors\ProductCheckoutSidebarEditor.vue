<template>
  <div class="product-checkout-sidebar-editor">
    <!-- Tabs -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- Aba Produto -->
      <div v-if="activeTab === 'product'" class="tab-panel">
        
        <!-- Informações do Produto -->
        <div class="section">
          <h3 class="section-title">Informações do Produto</h3>
        
          <div class="form-group">
            <IluriaLabel>Título do Produto</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.productTitle"
              placeholder="Ex: Tech T-Shirt®"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Avaliação</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.productRating"
              placeholder="Ex: 4.9"
              type="number"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Número de Avaliações</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.productReviews"
              placeholder="Ex: 24712"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>

        <!-- Preços -->
        <div class="section">
          <h3 class="section-title">Preços</h3>
          
          <div class="form-group">
            <IluriaLabel>Preço Original</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.originalPrice"
              placeholder="Ex: R$ 189"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Preço Atual</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.currentPrice"
              placeholder="Ex: R$ 139"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Badge de Desconto</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.discountBadge"
              placeholder="Ex: 26% OFF"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>
      </div>

      <!-- Aba Opções -->
      <div v-if="activeTab === 'options'" class="tab-panel">
        
        <!-- Cores Disponíveis -->
        <div class="section">
          <h3 class="section-title">Cores Disponíveis</h3>
          
          <div class="colors-list">
            <div 
              v-for="(color, index) in localConfig.colors" 
              :key="`color-${index}`"
              class="color-item"
            >
              <IluriaInputText
                v-model="color.name"
                placeholder="Nome da cor"
                @update:modelValue="updateConfig"
              />
              <IluriaColorPicker
                v-model="color.value"
                @update:modelValue="updateConfig"
              />
              <IluriaButton
                v-if="localConfig.colors.length > 1"
                @click="removeColor(index)"
                color="danger"
                size="small"
                variant="ghost"
              >
                ✕
              </IluriaButton>
            </div>
          </div>
          
          <IluriaButton
            @click="addColor"
            color="primary"
            size="small"
            variant="outline"
            class="mt-2"
          >
            + Adicionar Cor
          </IluriaButton>
        </div>

        <!-- Tamanhos Disponíveis -->
        <div class="section">
          <h3 class="section-title">Tamanhos Disponíveis</h3>
          
          <div class="sizes-list">
            <div 
              v-for="(size, index) in localConfig.sizes" 
              :key="`size-${index}`"
              class="size-item"
            >
              <IluriaInputText
                v-model="localConfig.sizes[index]"
                placeholder="Ex: M"
                @update:modelValue="updateConfig"
              />
              <IluriaButton
                v-if="localConfig.sizes.length > 1"
                @click="removeSize(index)"
                color="danger"
                size="small"
                variant="ghost"
              >
                ✕
              </IluriaButton>
            </div>
          </div>
          
          <IluriaButton
            @click="addSize"
            color="primary"
            size="small"
            variant="outline"
            class="mt-2"
          >
            + Adicionar Tamanho
          </IluriaButton>
        </div>

        <!-- Texto do Botão -->
        <div class="section">
          <h3 class="section-title">Botão de Ação</h3>
          
          <div class="form-group">
            <IluriaLabel>Texto do Botão</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.buttonText"
              placeholder="Ex: ADICIONAR AO CARRINHO"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>
      </div>

      <!-- Aba Design -->
      <div v-if="activeTab === 'design'" class="tab-panel">
        
        <!-- Layout -->
        <div class="section">
          <h3 class="section-title">Layout</h3>
          
          <div class="form-group">
            <IluriaLabel>Largura Máxima</IluriaLabel>
            <IluriaInputText
              v-model="localConfig.maxWidth"
              placeholder="Ex: 100% ou 1200px"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>

        <!-- Cores -->
        <div class="section">
          <h3 class="section-title">Cores</h3>
          
          <div class="form-group">
            <IluriaLabel>Cor de Fundo</IluriaLabel>
            <IluriaColorPicker
              v-model="localConfig.backgroundColor"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Cor da Borda</IluriaLabel>
            <IluriaColorPicker
              v-model="localConfig.borderColor"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Cor do Botão</IluriaLabel>
            <IluriaColorPicker
              v-model="localConfig.buttonColor"
              @update:modelValue="updateConfig"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Cor do Texto do Botão</IluriaLabel>
            <IluriaColorPicker
              v-model="localConfig.buttonTextColor"
              @update:modelValue="updateConfig"
            />
          </div>
        </div>

        <!-- Espaçamento -->
        <div class="section">
          <h3 class="section-title">Espaçamento</h3>
          
          <div class="form-group">
            <IluriaRange
              v-model="localConfig.paddingValue"
              label="Padding Interno"
              :min="0"
              :max="80"
              :step="4"
              unit="px"
              @update:modelValue="updatePaddingFromRange"
            />
          </div>

          <div class="form-group">
            <IluriaRange
              v-model="localConfig.borderRadiusValue"
              label="Border Radius"
              :min="0"
              :max="50"
              :step="1"
              unit="px"
              @update:modelValue="updateBorderRadiusFromRange"
            />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed'])

// Tabs
const tabs = [
  { value: 'product', label: 'Produto' },
  { value: 'options', label: 'Opções' },
  { value: 'design', label: 'Design' }
]

// Estado das abas
const activeTab = ref('product')

// Configuração local
const localConfig = ref({
  productTitle: 'Tech T-Shirt®',
  productRating: '4.9',
  productReviews: '24712',
  originalPrice: 'R$ 189',
  currentPrice: 'R$ 139',
  discountBadge: '26% OFF',
  selectedColor: 'Preta',
  selectedSize: 'M',
  buttonText: 'ADICIONAR AO CARRINHO',
  colors: [
    { name: 'Preta', value: '#000000' },
    { name: 'Cinza', value: '#6b7280' },
    { name: 'Bege', value: '#f3f4f6' },
    { name: 'Verde', value: '#059669' },
    { name: 'Azul', value: '#2563eb' },
    { name: 'Vinho', value: '#991b1b' }
  ],
  sizes: ['PP', 'P', 'M', 'G', 'GG', 'XGG', 'XXGG'],
     maxWidth: 'none',
  backgroundColor: '#ffffff',
  borderColor: '#e5e7eb',
     buttonColor: '#111827',
   buttonTextColor: '#ffffff',
   paddingValue: 32, // valor numérico para o range (2rem = 32px)
   borderRadiusValue: 0 // valor padrão zero
})

// Carrega configuração do elemento
const loadConfig = () => {
  if (!props.element) return

  // Carrega todos os data-attributes do elemento
  localConfig.value = {
    productTitle: props.element.getAttribute('data-product-title') || 'Tech T-Shirt®',
    productRating: props.element.getAttribute('data-product-rating') || '4.9',
    productReviews: props.element.getAttribute('data-product-reviews') || '24712',
    originalPrice: props.element.getAttribute('data-original-price') || 'R$ 189',
    currentPrice: props.element.getAttribute('data-current-price') || 'R$ 139',
    discountBadge: props.element.getAttribute('data-discount-badge') || '26% OFF',
    selectedColor: props.element.getAttribute('data-selected-color') || 'Preta',
    selectedSize: props.element.getAttribute('data-selected-size') || 'M',
    buttonText: props.element.getAttribute('data-button-text') || 'ADICIONAR AO CARRINHO',
    colors: getColorsFromElement(),
    sizes: getSizesFromElement(),
         maxWidth: getStyleValue('max-width') || 'none',
    backgroundColor: getStyleValue('background') || '#ffffff',
    borderColor: getStyleValue('border-color') || '#e5e7eb',
         buttonColor: getButtonStyleValue('background') || '#111827',
     buttonTextColor: getButtonStyleValue('color') || '#ffffff',
     paddingValue: parsePaddingValue(getStyleValue('padding')) || 32,
     borderRadiusValue: parseBorderRadiusValue(getStyleValue('border-radius')) || 0
  }
}

// Extrai cores do elemento
const getColorsFromElement = () => {
  const colorOptions = props.element.querySelectorAll('.color-option')
  if (colorOptions.length === 0) {
    return [
      { name: 'Preta', value: '#000000' },
      { name: 'Cinza', value: '#6b7280' },
      { name: 'Bege', value: '#f3f4f6' },
      { name: 'Verde', value: '#059669' },
      { name: 'Azul', value: '#2563eb' },
      { name: 'Vinho', value: '#991b1b' }
    ]
  }
  
  return Array.from(colorOptions).map(option => ({
    name: option.getAttribute('data-color') || 'Cor',
    value: option.style.backgroundColor || '#000000'
  }))
}

// Extrai tamanhos do elemento
const getSizesFromElement = () => {
  const sizeOptions = props.element.querySelectorAll('.size-option')
  if (sizeOptions.length === 0) {
    return ['PP', 'P', 'M', 'G', 'GG', 'XGG', 'XXGG']
  }
  
  return Array.from(sizeOptions).map(option => 
    option.getAttribute('data-size') || option.textContent.trim()
  )
}

// Obtém valor de estilo do container principal
const getStyleValue = (property) => {
  const container = props.element.querySelector('.product-checkout-container')
  if (!container) return null
  
  const computed = window.getComputedStyle(container)
  return computed.getPropertyValue(property)
}

// Obtém valor de estilo do botão
const getButtonStyleValue = (property) => {
  const button = props.element.querySelector('.add-to-cart-btn')
  if (!button) return null
  
  const computed = window.getComputedStyle(button)
  return computed.getPropertyValue(property)
}

// Função para converter padding de string para número (px)
const parsePaddingValue = (paddingStr) => {
  if (!paddingStr) return 32
  // Remove 'px', 'rem', 'em' e converte para número
  const match = paddingStr.match(/^(\d+(?:\.\d+)?)(px|rem|em)?/)
  if (match) {
    const value = parseFloat(match[1])
    const unit = match[2] || 'px'
    // Converte rem/em para px (assumindo 1rem = 16px)
    if (unit === 'rem' || unit === 'em') {
      return value * 16
    }
    return value
  }
  return 32
}

// Função para converter border-radius de string para número (px)
const parseBorderRadiusValue = (radiusStr) => {
  if (!radiusStr) return 0
  const match = radiusStr.match(/^(\d+(?:\.\d+)?)(px|rem|em)?/)
  if (match) {
    const value = parseFloat(match[1])
    const unit = match[2] || 'px'
    if (unit === 'rem' || unit === 'em') {
      return value * 16
    }
    return value
  }
  return 0
}

// Atualiza configuração
const updateConfig = () => {
  if (!props.element) return

  // Atualiza data-attributes
  props.element.setAttribute('data-product-title', localConfig.value.productTitle)
  props.element.setAttribute('data-product-rating', localConfig.value.productRating)
  props.element.setAttribute('data-product-reviews', localConfig.value.productReviews)
  props.element.setAttribute('data-original-price', localConfig.value.originalPrice)
  props.element.setAttribute('data-current-price', localConfig.value.currentPrice)
  props.element.setAttribute('data-discount-badge', localConfig.value.discountBadge)
  props.element.setAttribute('data-selected-color', localConfig.value.selectedColor)
  props.element.setAttribute('data-selected-size', localConfig.value.selectedSize)
  props.element.setAttribute('data-button-text', localConfig.value.buttonText)

  // Atualiza elementos visuais
  updateVisualElements()
  
  emit('data-changed')
}

// Atualiza elementos visuais do componente
const updateVisualElements = () => {
  // Atualizar título
  const titleEl = props.element.querySelector('.product-title')
  if (titleEl) titleEl.textContent = localConfig.value.productTitle

  // Atualizar avaliação
  const ratingEl = props.element.querySelector('.rating-text')
  if (ratingEl) ratingEl.textContent = `${localConfig.value.productRating} (${localConfig.value.productReviews} reviews)`

  // Atualizar preços
  const originalPriceEl = props.element.querySelector('.original-price')
  if (originalPriceEl) originalPriceEl.textContent = localConfig.value.originalPrice

  const currentPriceEl = props.element.querySelector('.current-price')
  if (currentPriceEl) currentPriceEl.textContent = localConfig.value.currentPrice

  const discountBadgeEl = props.element.querySelector('.badge.discount')
  if (discountBadgeEl) discountBadgeEl.textContent = localConfig.value.discountBadge

  // Atualizar botão
  const buttonEl = props.element.querySelector('.add-to-cart-btn')
  if (buttonEl) {
    buttonEl.innerHTML = `<span class="btn-icon">🛒</span>${localConfig.value.buttonText}`
    buttonEl.style.background = localConfig.value.buttonColor
    buttonEl.style.color = localConfig.value.buttonTextColor
  }

  // Atualizar cores
  updateColorOptions()

  // Atualizar tamanhos
  updateSizeOptions()

  // Atualizar estilos do container
  updateContainerStyles()
}

// Atualiza opções de cores
const updateColorOptions = () => {
  const colorContainer = props.element.querySelector('.color-options')
  if (!colorContainer) return

  colorContainer.innerHTML = localConfig.value.colors.map((color, index) => `
    <div class="color-option ${index === 0 ? 'active' : ''}" 
         data-color="${color.name}" 
         style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid ${index === 0 ? '#2563eb' : 'transparent'}; background-color: ${color.value}; transition: all 0.2s ease;" 
         title="${color.name}">
    </div>
  `).join('')

  // Atualizar valor selecionado
  const selectedValueEl = props.element.querySelector('.selected-value')
  if (selectedValueEl && localConfig.value.colors.length > 0) {
    selectedValueEl.textContent = localConfig.value.colors[0].name
  }
}

// Atualiza opções de tamanhos
const updateSizeOptions = () => {
  const sizeContainer = props.element.querySelector('.size-options')
  if (!sizeContainer) return

  sizeContainer.innerHTML = localConfig.value.sizes.map((size, index) => `
    <div class="size-option ${index === 2 ? 'active' : ''}" 
         data-size="${size}"
         style="padding: 0.75rem 1rem; border: 2px solid ${index === 2 ? '#2563eb' : '#e5e7eb'}; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem; ${index === 2 ? 'background: #2563eb; color: white;' : ''}">
      ${size}
    </div>
  `).join('')
}

// Atualiza estilos do container
const updateContainerStyles = () => {
  const container = props.element.querySelector('.product-checkout-container')
  if (!container) return

  // Se maxWidth for 'none', remover a propriedade
  if (localConfig.value.maxWidth === 'none' || localConfig.value.maxWidth === '') {
    container.style.removeProperty('max-width')
  } else {
    container.style.maxWidth = localConfig.value.maxWidth
  }
  
  container.style.backgroundColor = localConfig.value.backgroundColor
  container.style.borderColor = localConfig.value.borderColor
  container.style.padding = `${localConfig.value.paddingValue}px`
  container.style.borderRadius = `${localConfig.value.borderRadiusValue}px`
  
  // Atualizar também o layout interno
  const layout = props.element.querySelector('.product-layout')
  if (layout) {
    if (localConfig.value.maxWidth === 'none' || localConfig.value.maxWidth === '') {
      layout.style.maxWidth = '1200px' // Manter largura interna controlada
    } else {
      layout.style.maxWidth = 'none'
    }
  }
}

// Funções para gerenciar cores
const addColor = () => {
  localConfig.value.colors.push({ name: 'Nova Cor', value: '#000000' })
  updateConfig()
}

const removeColor = (index) => {
  if (localConfig.value.colors.length > 1) {
    localConfig.value.colors.splice(index, 1)
    updateConfig()
  }
}

// Funções para gerenciar tamanhos
const addSize = () => {
  localConfig.value.sizes.push('XL')
  updateConfig()
}

const removeSize = (index) => {
  if (localConfig.value.sizes.length > 1) {
    localConfig.value.sizes.splice(index, 1)
    updateConfig()
  }
}

// Funções para atualizar valores dos ranges
const updatePaddingFromRange = (value) => {
  localConfig.value.paddingValue = value
  updateConfig()
}

const updateBorderRadiusFromRange = (value) => {
  localConfig.value.borderRadiusValue = value
  updateConfig()
}

// Lifecycle
onMounted(() => {
  loadConfig()
})
</script>

<style scoped>
.product-checkout-sidebar-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
}

/* Tabs */
.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  margin-bottom: 1rem;
  background: var(--iluria-color-surface);
}

.tab-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 500;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-surface-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background: var(--iluria-color-surface);
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
  padding: 0 0.5rem;
}

.tab-panel {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

/* Sections */
.section {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 1rem;
}

.section-title {
  margin: 0 0 1rem 0;
  color: var(--iluria-color-text-primary);
  font-size: 1rem;
  font-weight: 600;
}

/* Form Groups */
.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  margin-bottom: 1rem;
}

.form-group:last-child {
  margin-bottom: 0;
}

/* Colors List */
.colors-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.color-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.color-item .iluria-color-picker {
  flex-shrink: 0;
}

/* Sizes List */
.sizes-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.size-item {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

/* Utilities */
.mt-2 {
  margin-top: 0.5rem;
}
</style> 