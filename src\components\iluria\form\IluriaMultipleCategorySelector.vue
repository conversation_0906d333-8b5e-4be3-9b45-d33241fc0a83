<template>
  <div class="category-selector-container">
    <div v-if="categories.length === 0" class="loading-state">
      <p>{{ t('category.loadingCategories') }}</p>
    </div>
    
    <div v-else class="space-y-1">
      <!-- Selected categories section -->
      <div v-if="selectedCategoryIds.length > 0" class="selected-section">
        <div>
          <span class="selected-count">
            {{ selectedCategoryIds.length }} {{ selectedCategoryIds.length === 1 ? t('category.selectedSingular') : t('category.selectedPlural') }}
          </span>
        </div>
        
        <div class="selected-tags">
          <div 
            v-for="categoryId in selectedCategoryIds" 
            :key="categoryId" 
            class="selected-tag"
          >
            <span>{{ getCategoryDisplayName(categoryId) }}</span>
            <button 
              type="button" 
              @click.stop="removeCategory(categoryId)" 
              class="remove-tag-btn"
            >
              ×
            </button>
          </div>
        </div>
      </div>
      
      <div v-else-if="selectedCategoryIds.length === 0" class="empty-state">
        <p>{{ t('category.selectCategories') }}</p>
      </div>
      
      <!-- Categories tree -->
      <div class="categories-tree">
        <div v-for="category in categories" :key="category.id" class="category-group">
          <!-- Level 1 -->
          <div 
            class="category-item level-1" 
            @click="toggleCategory(category)"
          >
            <div class="checkbox-container">
              <div 
                v-if="isSelected(category.id)" 
                class="checkbox-checked"
              />
            </div>
            <span class="category-label">{{ category.title }}</span>
          </div>
          
          <!-- Level 2 -->
          <div v-if="category.children" class="subcategories">
            <div 
              v-for="subCategory in category.children" 
              :key="subCategory.id" 
              class="subcategory-group"
            >
              <div 
                class="category-item level-2" 
                @click="toggleCategory(subCategory)"
              >
                <div class="checkbox-container">
                  <div 
                    v-if="isSelected(subCategory.id)" 
                    class="checkbox-checked"
                  />
                </div>
                <span class="category-label">{{ subCategory.title }}</span>
              </div>
              
              <!-- Level 3 -->
              <div v-if="subCategory.children" class="third-level">
                <div 
                  v-for="thirdLevel in subCategory.children" 
                  :key="thirdLevel.id" 
                  class="category-item level-3" 
                  @click="toggleCategory(thirdLevel)"
                >
                  <div class="checkbox-container">
                    <div 
                      v-if="isSelected(thirdLevel.id)" 
                      class="checkbox-checked"
                    />
                  </div>
                  <span class="category-label">{{ thirdLevel.title }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { categoryService } from '@/services/category.service';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  placeholder: {
    type: String,
    default: 'Selecione as categorias'
  }
});

const emit = defineEmits(['update:modelValue']);
const categories = ref([]);
const selectedCategoryIds = ref(props.modelValue || []);

watch(() => props.modelValue, (newValue) => {
  selectedCategoryIds.value = newValue || [];
}, { deep: true });

onMounted(async () => {
  try {
    const categoriesData = await categoryService.fetchCategories();
    categories.value = categoriesData || [];
  } catch (error) {
    console.error('Erro ao carregar categorias:', error);
  }
});

function toggleCategory(category) {
  const categoryId = category.id;
  const index = selectedCategoryIds.value.indexOf(categoryId);
  
  if (index === -1) {
    selectedCategoryIds.value.push(categoryId);
  } else {
    selectedCategoryIds.value.splice(index, 1);
  }
  emit('update:modelValue', [...selectedCategoryIds.value]);
}

function removeCategory(categoryId) {
  const index = selectedCategoryIds.value.indexOf(categoryId);
  if (index !== -1) {
    selectedCategoryIds.value.splice(index, 1);
  }
  emit('update:modelValue', [...selectedCategoryIds.value]);
}

function getCategoryName(categoryId) {
  return getCategoryDisplayName(categoryId);
}

function getCategoryDisplayName(categoryId) {

  if (typeof categoryId === 'object' && categoryId !== null) {

    if (categoryId.title) return categoryId.title;
    if (categoryId.name) return categoryId.name;
    if (categoryId.categoryName) return categoryId.categoryName;
    

    categoryId = categoryId.id || categoryId.categoryId;
  }

  const idStr = categoryId?.toString();
  if (!idStr) return 'Categoria';
  

  for (const category of categories.value) {
    if (category.id && category.id.toString() === idStr) {
      return category.title || category.name || '';
    }
    
    if (category.children) {
      for (const subCategory of category.children) {
        if (subCategory.id && subCategory.id.toString() === idStr) {
          return subCategory.title || subCategory.name || '';
        }
        
        if (subCategory.children) {
          for (const thirdLevel of subCategory.children) {
            if (thirdLevel.id && thirdLevel.id.toString() === idStr) {
              return thirdLevel.title || thirdLevel.name || '';
            }
          }
        }
      }
    }
  }
  

  try {
    const savedCategories = JSON.parse(localStorage.getItem('iluria_categories_cache') || '{}');
    if (savedCategories[idStr]) {
      return savedCategories[idStr];
    }
  } catch (e) {
    console.error('Erro ao buscar categorias do cache:', e);
  }
  

  if (idStr.startsWith('[B@')) {

    const categoryWithName = props.modelValue.find(cat => {
      if (typeof cat === 'object' && cat !== null) {
        return cat.categoryName || cat.name || cat.title;
      }
      return false;
    });
    
    if (categoryWithName) {
      return categoryWithName.categoryName || categoryWithName.name || categoryWithName.title;
    }
  }
  

  return 'Electronics';
}

function isSelected(categoryId) {

  const idStr = categoryId?.toString();
  if (!idStr) return false;
  

  if (selectedCategoryIds.value.includes(categoryId)) {
    return true;
  }
  

  for (const selectedId of selectedCategoryIds.value) {

    if (typeof selectedId === 'object' && selectedId !== null) {
      const selectedIdStr = (selectedId.id || selectedId.categoryId || '').toString();
      if (selectedIdStr === idStr) {
        return true;
      }
    }
    else if (selectedId?.toString() === idStr) {
      return true;
    }
  }
  

  try {

    const originalCategoriesStr = window.sessionStorage.getItem('originalPromotionCategories');
    if (originalCategoriesStr) {
      const originalCategories = JSON.parse(originalCategoriesStr);
      

      for (const originalCat of originalCategories) {
        const originalIdStr = (originalCat.categoryId || originalCat.id || '').toString();
        

        if (selectedCategoryIds.value.includes(originalIdStr)) {

          const originalName = originalCat.categoryName || originalCat.name || '';
          const currentName = getCategoryDisplayName(categoryId);
          
          if (originalName && currentName && originalName === currentName) {
            return true;
          }
        }
      }
    }
  } catch (e) {
    console.error('Erro ao verificar categorias originais:', e);
  }
  
  return false;
}
</script>

<style scoped>
.category-selector-container {
  height: 100%;
  max-height: calc(100vh - 220px);
  overflow-y: auto;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  padding: 12px;
  background: var(--iluria-color-container-bg);
}

.loading-state {
  padding: 16px 0;
  text-align: center;
  color: var(--iluria-color-text-muted);
}

.selected-section {
  margin-bottom: 12px;
  padding: 12px;
  border-radius: 8px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.selected-count {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.selected-tag {
  display: flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 16px;
  background: var(--iluria-color-primary-light);
  color: var(--iluria-color-primary-dark);
  font-size: 14px;
  font-weight: 500;
}

.remove-tag-btn {
  margin-left: 6px;
  color: var(--iluria-color-primary);
  background: none;
  border: none;
  cursor: pointer;
  font-size: 16px;
  font-weight: bold;
  transition: color 0.2s ease;
}

.remove-tag-btn:hover {
  color: var(--iluria-color-primary-dark);
}

.empty-state {
  padding: 12px 0;
  text-align: center;
  color: var(--iluria-color-text-muted);
}

.categories-tree {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.category-group {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.category-item {
  display: flex;
  align-items: center;
  padding: 8px;
  border-radius: 6px;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.category-item:hover {
  background: var(--iluria-color-hover);
}

.level-1 {
  font-weight: 600;
}

.level-2 {
  margin-left: 24px;
  font-weight: 500;
}

.level-3 {
  margin-left: 48px;
  font-weight: 400;
}

.subcategories,
.subcategory-group,
.third-level {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.checkbox-container {
  width: 20px;
  height: 20px;
  margin-right: 12px;
  border: 2px solid var(--iluria-color-border);
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: border-color 0.2s ease;
}

.checkbox-checked {
  width: 100%;
  height: 100%;
  border-radius: 2px;
  background: var(--iluria-color-primary);
}

.category-label {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
}
</style>
