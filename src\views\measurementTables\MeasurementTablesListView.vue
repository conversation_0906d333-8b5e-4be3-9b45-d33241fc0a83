<template>
  <div class="page-container">
    <!-- <PERSON> Header -->
    <IluriaHeader
      :title="t('measurementTable.title')"
      :subtitle="t('measurementTable.subtitle', 'Gerencie tabelas de medidas para seus produtos')"
      :showSearch="true"
      :showAdd="true"
      :addText="t('measurementTable.createNew')"
      :searchPlaceholder="t('measurementTable.searchPlaceholder')"
      @search="handleSearch"
      @add-click="goToCreate"
    />

    <!-- Main Content -->
      <div class="table-container">
        <IluriaDataTable
          :value="measurementTables"
          :loading="loading"
          :columns="[
            { field: 'image', class: 'w-[80px] text-center', headerClass: 'text-center' },
            { field: 'name', class: 'w-[250px] text-center', headerClass: 'text-center' },
            { field: 'type', class: 'w-[120px] text-center', headerClass: 'text-center' },
            { field: 'columnsCount', class: 'w-[100px] text-center', headerClass: 'text-center' },
            { field: 'sizesCount', class: 'w-[100px] text-center', headerClass: 'text-center' },
            { field: 'createdAt', class: 'w-[140px] text-center', headerClass: 'text-center' },
            { field: 'actions', class: 'w-[200px] text-center', headerClass: 'text-center' }
          ]"
        >
          <template #empty>
            <div class="empty-state">
              <h3 class="empty-title">{{ t('measurementTable.noTables') }}</h3>
              <p class="empty-description">{{ t('measurementTable.noTablesDescription', 'Comece criando sua primeira tabela de medidas') }}</p>
              <IluriaButton @click="goToCreate" :hugeIcon="PlusSignSquareIcon" class="mt-4">
                {{ t('measurementTable.createNew') }}
              </IluriaButton>
            </div>
          </template>

          <template #header-image>
            <span class="column-header">{{ t('measurementTable.image') }}</span>
          </template>

          <template #header-name>
            <span class="column-header">{{ t('measurementTable.name') }}</span>
          </template>

          <template #header-type>
            <span class="column-header">{{ t('measurementTable.type') }}</span>
          </template>

          <template #header-columnsCount>
            <span class="column-header">{{ t('measurementTable.columnsCount') }}</span>
          </template>

          <template #header-sizesCount>
            <span class="column-header">{{ t('measurementTable.sizesCount') }}</span>
          </template>

          <template #header-createdAt>
            <span class="column-header">{{ t('createdAt') }}</span>
          </template>

          <template #header-actions>
            <span class="column-header">{{ t('actions') }}</span>
          </template>

          <template #column-image="{ data }">
            <div class="image-cell">
              <div class="image-container">
                <img 
                  v-if="data.imageUrl" 
                  :src="data.imageUrl" 
                  :alt="data.name"
                  class="table-image"
                />
                <div v-else class="no-image">
                  {{ t('measurementTable.noImage') }}
                </div>
              </div>
            </div>
          </template>

          <template #column-name="{ data }">
            <div class="name-cell">
              <div class="table-name">{{ data.name }}</div>
              <div class="table-description" v-if="data.description">
                {{ data.description }}
              </div>
            </div>
          </template>

          <template #column-type="{ data }">
            <div class="type-cell">
              <span 
                class="type-badge"
                :class="typeClasses[data.type]"
              >
                {{ t(`measurementTable.type${data.type}`) }}
              </span>
            </div>
          </template>

          <template #column-columnsCount="{ data }">
            <div class="count-cell">
              <span class="count-badge count-blue">
                {{ data.columns?.length || 0 }}
              </span>
            </div>
          </template>

          <template #column-sizesCount="{ data }">
            <div class="count-cell">
              <span class="count-badge count-green">
                {{ data.rows?.length || data.sizes?.length || 0 }}
              </span>
            </div>
          </template>

          <template #column-createdAt="{ data }">
            <div class="date-cell">
              <span class="date-text">{{ formatDate(data.createdAt) }}</span>
            </div>
          </template>

          <template #column-actions="{ data }">
            <div class="action-buttons">
              <IluriaButton
                @click.stop="previewTable(data)"
                color="text-primary"
                size="small"
                :hugeIcon="ViewIcon"
                :title="t('preview')"
              />
              
              <IluriaButton
                @click="editTable(data)"
                color="text-primary"
                size="small"
                :hugeIcon="Edit02Icon"
                :title="t('edit')"
              />
              
              <IluriaButton
                @click="deleteTable(data)"
                color="text-danger"
                size="small"
                :hugeIcon="Delete02Icon"
                :title="t('delete')"
              />
            </div>
          </template>
        </IluriaDataTable>
      </div>

      <IluriaPagination 
        v-if="totalPages > 0"
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />

    <IluriaModal
      v-model="showPreviewModal"
      :title="previewingTable?.name || t('measurementTable.preview')"
      :dialog-style="{ maxWidth: '800px', width: '90vw' }"
      size="lg"
    >
      <MeasurementTablePreview
        v-if="previewingTable"
        :measurement-table="previewingTable"
      />
    </IluriaModal>

    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { 
  PlusSignSquareIcon, 
  ViewIcon, 
  Edit02Icon, 
  Delete02Icon,
} from '@hugeicons-pro/core-bulk-rounded'

import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import MeasurementTablePreview from '@/components/measurementTables/MeasurementTablePreview.vue'
import { measurementTableApi } from '@/services/measurementTable.service'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

const measurementTables = ref([])
const loading = ref(false)
const currentPage = ref(0)
const totalPages = ref(0)
const perPage = ref(10)
const searchFilter = ref('')

const showPreviewModal = ref(false)
const previewingTable = ref(null)

const typeClasses = {
  IMAGE: 'type-image',
  STRUCTURED: 'type-structured'
}

const loadTables = async (page = 0) => {
  try {
    loading.value = true
    const response = await measurementTableApi.getAll({
      page,
      size: perPage.value,
      filter: searchFilter.value
    })
    
    measurementTables.value = response.data.content || response.data
    totalPages.value = response.data.page?.totalPages || response.data.totalPages
    currentPage.value = page
  } catch (error) {
    toast.showError(t('measurementTable.errorLoading'))
    measurementTables.value = []
    totalPages.value = 0
  } finally {
    loading.value = false
  }
}

let searchTimeout = null
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadTables()
  }, 300)
}

const handleSearch = (searchValue) => {
  searchFilter.value = searchValue
  debouncedSearch()
}

const changePage = (page) => {
  currentPage.value = page
  loadTables(page)
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const dateObj = dateString instanceof Date ? dateString : new Date(dateString)
  const day = dateObj.getDate().toString().padStart(2, '0')
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
  const year = dateObj.getFullYear()
  return `${day}/${month}/${year}`
}

const goToCreate = () => {
  router.push({ name: 'measurement-table-inline-new' })
}

const editTable = (table) => {
  router.push({ name: 'measurement-table-inline-edit', params: { id: table.id } })
}

const previewTable = (table) => {
  previewingTable.value = table
  showPreviewModal.value = true
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const deleteTable = (table) => {
  showConfirmDanger(
    t('measurementTable.deleteMessage', { name: table.name }),
    t('measurementTable.deleteTitle'),
    () => confirmDelete(table)
  )
}

const confirmDelete = async (table) => {
  try {
    loading.value = true
    await measurementTableApi.delete(table.id)
    
    await loadTables(currentPage.value)
    
    toast.showSuccess(t('measurementTable.deletedSuccess'))
  } catch (error) {
    toast.showError(t('measurementTable.errorDeleting'))
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  loadTables()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
}



.search-container {
  display: flex;
  align-items: center;
}

.search-input {
  min-width: 250px;
}

/* Table Container */
.table-container {
  width: 100%;
  overflow-x: auto;
}

/* Table Cells */
.image-cell {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 0;
}

.image-container {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--iluria-color-surface);
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--iluria-color-border);
}

.table-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.no-image {
  color: var(--iluria-color-text-muted);
  font-size: 10px;
  text-align: center;
  line-height: 1.2;
}

.name-cell {
  display: flex;
  flex-direction: column;
  gap: 4px;
  text-align: center;
  align-items: center;
}

.table-name {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  font-size: 14px;
  text-align: center;
}

.table-description {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.3;
  text-align: center;
}

.type-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.type-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 8px;
  border-radius: 6px;
  font-size: 11px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  text-align: center;
}

.type-image {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

.type-structured {
  background: rgba(34, 197, 94, 0.1);
  color: #15803d;
}

.count-cell {
  text-align: center;
}

.count-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 50%;
  font-size: 12px;
  font-weight: 600;
}

.count-blue {
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
}

.count-green {
  background: rgba(34, 197, 94, 0.1);
  color: #15803d;
}

.date-cell {
  display: flex;
  justify-content: center;
  align-items: center;
}

.date-text {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  text-align: center;
}

.action-buttons {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 24px 0;
}

/* Utility classes */
.mt-4 {
  margin-top: 1rem;
}

/* Table header and body alignment */
:deep(.iluria-data-table .p-datatable-thead > tr > th) {
  padding-left: 16px !important;
  padding-right: 16px !important;
}

/* Center alignment for centered columns */
:deep(.iluria-data-table .p-datatable-thead > tr > th.text-center) {
  text-align: center !important;
}

/* Body cell alignment */
:deep(.iluria-data-table .p-datatable-tbody > tr > td) {
  padding-left: 16px !important;
  padding-right: 16px !important;
  vertical-align: middle !important;
}

/* Center alignment for centered columns in body */
:deep(.iluria-data-table .p-datatable-tbody > tr > td.text-center) {
  text-align: center !important;
}



/* Column header styling - ensure they align with content */
.column-header {
  display: block;
  width: 100%;
  text-align: inherit;
  font-weight: 600;
}



/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  

  
  .search-input {
    min-width: auto;
    width: 100%;
  }
}

@media (max-width: 640px) {

  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  .table-name {
    font-size: 13px;
  }
  
  .table-description {
    font-size: 11px;
  }
}
</style>

 
