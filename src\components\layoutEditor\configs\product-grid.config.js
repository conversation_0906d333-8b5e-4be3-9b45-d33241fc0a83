/**
 * Configuração do Componente de Grade de Produtos
 * Conectado com API e editores completos
 */

export default {
  type: 'product-grid',
  name: 'Grade de Produtos',
  description: 'Grade de produtos conectada com API, com editores de seleção e estilo',
  category: 'ecommerce',
  priority: 90,
  layoutTypes: ['static', 'collection'], // ✅ Disponível apenas em layouts estáticos e de coleção
  icon: 'grid_view',
  
  html: `<div 
    data-component="product-grid" 
    data-element-type="product-grid" 
    data-columns="3" 
    data-limit="6"
    data-title="Produtos em Destaque"

    data-selection-mode="automatic"
    data-sort-by="newest"
    data-only-in-stock="false"
    class="iluria-product-grid" 
    style="width: 100%; max-width: 100%; background: white; padding: 2rem; border-radius: 12px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); overflow: hidden; box-sizing: border-box;">
    
    <div class="container" style="max-width: 1200px; margin: 0 auto; width: 100%;">
      <!-- Header <PERSON><PERSON><PERSON> preenchido dinamicamente -->
      <div class="products-header" style="text-align: center; margin-bottom: 3rem;">
        <h2 class="grid-title" style="font-size: 2.5rem; font-weight: 700; margin: 0; color: #1f2937; letter-spacing: -0.025em;">Produtos em Destaque</h2>
      </div>
      
      <!-- Loading inicial -->
      <div class="product-grid-container" style="display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 400px; text-align: center; width: 100%;">
        <div style="display: flex; flex-direction: column; align-items: center; justify-content: center;">
          <div class="loading-spinner" style="width: 50px; height: 50px; border: 4px solid #f3f4f6; border-radius: 50%; border-top-color: #3b82f6; animation: spin 1s linear infinite; margin-bottom: 1.5rem;"></div>
          <p style="margin: 0; color: #6b7280; font-size: 1rem; font-weight: 500; text-align: center;">Carregando produtos...</p>
          <p style="margin: 0.5rem 0 0 0; color: #9ca3af; font-size: 0.875rem; text-align: center;">Conectando com a API...</p>
        </div>
      </div>
    </div>
    
    <style>
      @keyframes spin {
        0% { transform: rotate(0deg); }
        100% { transform: rotate(360deg); }
      }
      
      .iluria-product-grid {
        background: white !important;
        border-radius: 12px !important;
        box-shadow: 0 4px 12px rgba(0,0,0,0.08) !important;
        max-width: 100% !important;
        box-sizing: border-box !important;
        overflow: hidden !important;
        margin: 0 !important;
        width: 100% !important;
      }
      
      .iluria-product-grid .container {
        width: 100% !important;
        max-width: 1200px !important;
        margin: 0 auto !important;
        padding: 0 !important;
        box-sizing: border-box !important;
      }
      
      .product-grid-container {
        display: grid !important;
        gap: 2rem !important;
        width: 100% !important;
        box-sizing: border-box !important;
      }
      
      .product-grid-3 {
        grid-template-columns: repeat(3, 1fr) !important;
      }
      
      .product-grid-4 {
        grid-template-columns: repeat(4, 1fr) !important;
      }
      
      .product-grid-2 {
        grid-template-columns: repeat(2, 1fr) !important;
      }
      
      .product-card {
        background: white !important;
        border-radius: 16px !important;
        overflow: hidden !important;
        box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1) !important;
        transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        border: 1px solid #f3f4f6 !important;
        box-sizing: border-box !important;
      }
      
      .product-card:hover {
        transform: translateY(-8px) !important;
        box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04) !important;
      }
      
      .product-image {
        width: 100% !important;
        height: 280px !important;
        object-fit: cover !important;
        background: linear-gradient(135deg, #f9fafb 0%, #f3f4f6 100%) !important;
        display: block !important;
      }
      
      .product-info {
        padding: 1.75rem !important;
      }
      
      .product-title {
        font-size: 1.125rem !important;
        font-weight: 600 !important;
        color: #111827 !important;
        margin: 0 0 0.75rem 0 !important;
        line-height: 1.5 !important;
        display: -webkit-box !important;
        -webkit-line-clamp: 2 !important;
        -webkit-box-orient: vertical !important;
        overflow: hidden !important;
      }
      
      .product-price {
        display: flex !important;
        align-items: center !important;
        gap: 0.75rem !important;
        margin-bottom: 1.25rem !important;
      }
      
      .current-price {
        font-size: 1.375rem !important;
        font-weight: 700 !important;
        color: #3b82f6 !important;
      }
      
      .original-price {
        font-size: 1rem !important;
        color: #6b7280 !important;
        text-decoration: line-through !important;
      }
      
      .product-button {
        width: 100% !important;
        background: linear-gradient(135deg, #3b82f6 0%, #2563eb 100%) !important;
        color: white !important;
        border: none !important;
        border-radius: 10px !important;
        padding: 0.875rem 1.25rem !important;
        font-size: 0.925rem !important;
        font-weight: 600 !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
        letter-spacing: 0.025em !important;
      }
      
      .product-button:hover {
        background: linear-gradient(135deg, #2563eb 0%, #1d4ed8 100%) !important;
        transform: translateY(-2px) !important;
        box-shadow: 0 8px 16px rgba(59, 130, 246, 0.3) !important;
      }
      
      @media (max-width: 1200px) {
        .iluria-product-grid {
          margin: 1rem 0.5rem !important;
          padding: 1.5rem !important;
        }
      }
      
      @media (max-width: 1023px) {
        .product-grid-4 { grid-template-columns: repeat(3, 1fr) !important; }
        .product-grid-3 { grid-template-columns: repeat(2, 1fr) !important; }
        
        .iluria-product-grid {
          padding: 1.5rem !important;
        }
        
        .products-header h2 {
          font-size: 2rem !important;
        }
      }
      
      @media (max-width: 767px) {
        .product-grid-4,
        .product-grid-3,
        .product-grid-2 { 
          grid-template-columns: repeat(2, 1fr) !important; 
        }
        
        .product-grid-container { 
          gap: 1.5rem !important; 
        }
        
        .product-image { 
          height: 220px !important; 
        }
        
        .product-info { 
          padding: 1.25rem !important; 
        }
        
        .product-title { 
          font-size: 1rem !important; 
        }
        
        .iluria-product-grid {
          padding: 1.25rem !important;
          margin: 0.75rem 0.25rem !important;
        }
        
        .products-header {
          margin-bottom: 2rem !important;
        }
        
        .products-header h2 {
          font-size: 1.75rem !important;
        }
      }
      
      @media (max-width: 480px) {
        .product-grid-4,
        .product-grid-3,
        .product-grid-2 { 
          grid-template-columns: 1fr !important; 
        }
        
        .product-grid-container { 
          gap: 1.25rem !important; 
        }
        
        .iluria-product-grid {
          padding: 1rem !important;
          margin: 0.5rem 0.125rem !important;
        }
        
        .products-header h2 {
          font-size: 1.5rem !important;
        }
      }
      
      /* Neutralizar padding do element-content apenas para product-grid */
      .component-container:has(.iluria-product-grid) .element-content {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      /* Fallback para navegadores sem :has() */
      .element-content:has(.iluria-product-grid) {
        padding: 0 !important;
        margin: 0 !important;
      }
    </style>
  </div>`,
  
  selectors: [
    '[data-component="product-grid"]',
    '.iluria-product-grid',
    '[data-element-type="product-grid"]'
  ],
  
  initialization: {
    autoInit: true,
    scriptLoader: 'injectProductGridScript'
  },
  
  toolbarActions: [
    {
      type: 'product-selection',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="3" y="3" width="18" height="18" rx="2"/>
        <path d="M9 9h6v6H9z"/>
        <path d="M3 15h18"/>
        <path d="M15 3v18"/>
      </svg>`,
      title: 'Selecionar Produtos',
      condition: (element) => {
        return element?.getAttribute('data-component') === 'product-grid'
      }
    },
    {
      type: 'product-style',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M12 2l3.09 6.26L22 9.27l-5 4.87 1.18 6.88L12 17.77l-6.18 3.25L7 14.14 2 9.27l6.91-1.01L12 2z"/>
      </svg>`,
      title: 'Editar Estilo',
      condition: (element) => {
        return element?.getAttribute('data-component') === 'product-grid'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'product-selection',
      component: 'ProductSelectionSidebarEditor',
      path: '@/components/layoutEditor/Sidebar/editors/ProductSelectionSidebarEditor.vue'
    },
    {
      type: 'product-style',
      component: 'ProductStyleSidebarEditor',
      path: '@/components/layoutEditor/Sidebar/editors/ProductStyleSidebarEditor.vue'
    }
  ],
  
  computedCheckers: {
    isProductGridComponent: (element) => {
      return element?.getAttribute('data-component') === 'product-grid' ||
             element?.classList?.contains('iluria-product-grid')
    },
    
    getLimit: (element) => {
      return parseInt(element?.getAttribute('data-limit')) || 6
    },
    
    getColumns: (element) => {
      return parseInt(element?.getAttribute('data-columns')) || 3
    },
    
    getTitle: (element) => {
      return element?.getAttribute('data-title') || 'Produtos em Destaque'
    },
    
    getEndpoint: (element) => {
      return element?.getAttribute('data-endpoint') || 'http://localhost:8081/api/products'
    },
    
    getSelectionMode: (element) => {
      return element?.getAttribute('data-selection-mode') || 'automatic'
    }
  },
  
  apiIntegration: {
    endpoint: 'http://localhost:8081/api/products',
    method: 'GET',
    headers: {
      'Content-Type': 'application/json'
    },
    requiresAuth: true,
    authToken: 'eyJhbGciOiJSUzI1NiJ9.**********************************************************************************************************************************************************************************************************************************************************************************************.VuWVKhFgfYpDMLftWpDPuWUEpnglpSYBX4_NRlBciusisLPKRSTtmrciuQHaFZYlP374QB9uKWeukUEmx0uWd-2b9uzq0uAYhwa771sjBqCE3izG9Wx4tCt0PJyaC4zMTeYbxovs1ckohQJ6gcDShPJVDo1aV5IlvsJb3I0wDxMfGYPBHH0WKJBYNJq4IgTI2kRV0DPo88ywDUFs3PHQhtASZSqKVTnt3WR_atE0lQdFGqDO2goCiBZ5IqFlTwaEcCfPBM8wZASv7bzJbVzKEfydMz0vXk9hlnpnd9s8zZ_YMqEwu-vjn26oMKJE-YdM3HG4vgTN9INphzHhFZ0i5w',
    responseMapping: {
      products: 'content',
      fallback: 'data'
    }
  },
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true,
    resizable: false
  },
  
  cleanup: {
    removeEditingStyles: true,
    preserveStyles: [
      'background', 
      'border-radius', 
      'box-shadow', 
      'padding', 
      'margin',
      'display', 
      'grid-template-columns', 
      'gap',
      'max-width',
      'box-sizing'
    ]
  }
} 