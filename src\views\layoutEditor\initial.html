<!DOCTYPE html>
<html lang="pt-BR">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <style>
    body { 
      font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen, Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
      padding: 2rem; 
      max-width: 1200px;
      margin: 0 auto;
      line-height: 1.5;
      color: #333;
    }
    
    
    .mobile-view {
      font-size: 14px;
      padding: 1rem;
    }
    
    .desktop-view {
      font-size: 16px;
    }
    
    .component-container {
      position: relative;
      padding: 8px;
      margin: 16px 0;
      transition: all 0.2s ease;
      border: 1px dashed transparent;
      border-radius: 4px;
    }
    
    .component-container:hover {
      background-color: rgba(59, 130, 246, 0.05);
    }
    
    .edit-mode .component-container:hover {
      outline: 1px solid #3b82f6;
    }
    
    .add-button {
      position: absolute;
      background-color: #3b82f6;
      color: white;
      width: 24px;
      height: 24px;
      border-radius: 50%;
      display: flex;
      align-items: center;
      justify-content: center;
      cursor: pointer;
      font-weight: bold;
      z-index: 100;
      opacity: 0;
      transition: opacity 0.2s;
      box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    }
    
    .edit-mode .component-container:hover .add-button {
      opacity: 1;
    }
    
    .add-button:hover {
      background-color: #2563eb;
      transform: scale(1.1);
    }
    
    .add-button-top {
      top: -12px;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .add-button-bottom {
      bottom: -12px;
      left: 50%;
      transform: translateX(-50%);
    }
    
    .dragging {
      opacity: 0.5;
      border: 2px dashed #3b82f6 !important;
    }
    
    .drop-zone {
      border: 2px dashed #3b82f6;
      background-color: rgba(59, 130, 246, 0.1);
      padding: 16px;
      border-radius: 4px;
    }
    
    .edit-mode .product-card:hover .remove-product {
      display: flex;
      align-items: center;
      justify-content: center;
      font-size: 18px;
    }
    
    .remove-product:hover {
      background: rgba(255,255,255,1);
    }
  </style>
  <script src="https://unpkg.com/vue@3/dist/vue.global.js"></script>
</head>
<body>
  <div id="app">
    <div class="component-container">
      <div class="add-button add-button-top">+</div>
      <h1 data-text-element="true">Título da loja</h1>
      <div class="add-button add-button-bottom">+</div>
    </div>
    
    <div class="component-container">
      <div class="add-button add-button-top">+</div>
      <p data-text-element="true">Bem-vindo à nossa loja! Aqui você encontrará os melhores produtos com preços especiais.</p>
      <div class="add-button add-button-bottom">+</div>
    </div>
    
    <div class="component-container">
      <div class="add-button add-button-top">+</div>
      <img data-media-element="true" src="https://images.pexels.com/photos/3184306/pexels-photo-3184306.jpeg?auto=compress&cs=tinysrgb&w=1260&h=750&dpr=2" alt="Banner" style="max-width: 100%; height: auto;" />
      <div class="add-button add-button-bottom">+</div>
    </div>
  </div>
</body>
</html>