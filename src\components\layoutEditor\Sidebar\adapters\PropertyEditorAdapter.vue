<template>
  <div class="property-editor-adapter">
    <!-- Header do editor -->
    <div class="editor-header">
      <h4 class="editor-title">{{ editorTitle }}</h4>
    </div>
    
    <!-- Conteú<PERSON> do editor adaptado -->
    <div class="editor-content">
      <component
        :is="editorComponent"
        v-if="editorComponent"
        :element="element"
        :selectedElement="element"
        :position="mockPosition"
        @close="handleClose"
        @save="handleSave"
        @data-changed="handleDataChanged"
        v-bind="editorProps"
      />
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, nextTick } from 'vue'

const props = defineProps({
  editorComponent: {
    type: [Object, Function],
    required: true
  },
  element: {
    type: Object,
    required: true
  },
  editorType: {
    type: String,
    required: true
  },
  editorProps: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['close', 'save', 'data-changed', 'element-updated'])

// Posição mock para editores que esperavam FloatingWindow
const mockPosition = ref({ x: 0, y: 0 })

// Título do editor
const editorTitle = computed(() => {
  const titleMap = {
    'header-config': 'Editor de Cabeçalho',
    'footer-config': 'Editor de Rodapé',
    'carousel-config': 'Editor de Carrossel',
    'video-config': 'Editor de Vídeo',
    'company-information-config': 'Editor de Informações da Empresa',
    'location-config': 'Editor de Localização',
    'statement-config': 'Editor de Comunicado',
    'payment-benefits-config': 'Editor de Benefícios',
    'customer-review-config': 'Editor de Avaliações',
    'special-offers-config': 'Editor de Ofertas',
    'product-selection': 'Editor de Produtos',
    'product-style': 'Editor de Estilo',
    'spacing': 'Editor de Espaçamento',
    'border': 'Editor de Bordas',
    'animation': 'Editor de Animação',
    'transform': 'Editor de Transformação',
    'filter': 'Editor de Filtros',
    'text': 'Editor de Texto',
    'image': 'Editor de Imagem',
    'link': 'Editor de Link'
  }
  
  return titleMap[props.editorType] || 'Editor de Propriedades'
})

// Handlers
const handleClose = () => {
  emit('close')
}

const handleSave = (data) => {
  emit('save', data)
  emit('element-updated', {
    element: props.element,
    data,
    description: 'Propriedades atualizadas'
  })
}

const handleDataChanged = (data) => {
  emit('data-changed', data)
  emit('element-updated', {
    element: props.element,
    data,
    description: 'Propriedades alteradas'
  })
}

// Adaptação para remover FloatingWindow dos editores antigos
onMounted(async () => {
  await nextTick()
  
  // Remove FloatingWindow se existir no componente renderizado
  const floatingWindows = document.querySelectorAll('.property-editor-adapter .floating-window')
  floatingWindows.forEach(window => {
    // Extrai o conteúdo interno e substitui o FloatingWindow
    const content = window.querySelector('.floating-window-content, .window-content')
    if (content) {
      const parent = window.parentNode
      parent.replaceChild(content, window)
    }
  })
})
</script>

<style scoped>
.property-editor-adapter {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--iluria-color-surface);
}

.editor-header {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  flex-shrink: 0;
}

.editor-title {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.editor-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Remover estilos do FloatingWindow quando usado na sidebar */
.editor-content :deep(.floating-window) {
  position: static !important;
  box-shadow: none !important;
  border: none !important;
  background: transparent !important;
  height: 100% !important;
  width: 100% !important;
  max-width: none !important;
  transform: none !important;
}

.editor-content :deep(.floating-window-header) {
  display: none !important;
}

.editor-content :deep(.floating-window-content),
.editor-content :deep(.window-content) {
  height: 100% !important;
  padding: 0 !important;
  overflow-y: auto !important;
}

/* Ajustar padding dos conteúdos internos */
.editor-content :deep(.section) {
  margin-bottom: 1rem;
}

.editor-content :deep(.form-group) {
  margin-bottom: 1rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .editor-header {
    padding: 0.75rem;
  }
  
  .editor-title {
    font-size: 0.9rem;
  }
}
</style> 
