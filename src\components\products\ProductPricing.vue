<template>
  <div v-if="form" class="space-y-4" id="productPricing">
    <div class="grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
      <IluriaInputText
        id="price"
        name="price"
        type="money"
        prefix="R$"
        :label="t('product.price')"
        v-model="form.price"
      />
      
      <IluriaInputText
        id="originalPrice"
        name="originalPrice"
        type="money"
        prefix="R$"
        :label="t('product.originalPrice')"
        v-model="form.originalPrice"
      />
      
      <IluriaInputText
        id="costPrice"
        name="costPrice"
        type="money"
        prefix="R$"
        :label="t('product.costPrice')"
        v-model="form.costPrice"
      />

      <IluriaInputText
        v-if="!hasNegativeMargin"
        id="profitMargin"
        name="profitMargin"
        type="text"
        suffix="%"
        :label="t('product.profitMargin')"
        v-model="form.profitMargin"
        :formContext="props.formContext?.profitMargin"
        @input="handleProfitMarginInput"
        @keypress="validateProfitMarginInput"
      />
      
      <div v-else class="profit-margin-error">
        <IluriaLabel class="field-label error-label">
          {{ t('product.profitMargin') }}
        </IluriaLabel>
        <div class="error-display">
          <div class="error-content">
            <HugeiconsIcon :icon="Alert01Icon" size="16" class="error-icon" />
            <div class="error-text">
              <IluriaLabel class="error-subtitle">
                {{ t('product.negativeMargin') }}: {{ negativeMarginValue }}{{ t('product.percentage') }}
              </IluriaLabel>
              <IluriaLabel class="error-hint">{{ t('product.negativeMarginHint') }}</IluriaLabel>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="border-t border-gray-200 pt-4">
      <IluriaCheckBox 
        v-model="hasPriceRanges"
        id="addPriceRanges"
        :label="t('product.addPriceRanges')"
        labelPosition="horizontal"
        class="mb-3"
      />
      
      <PriceRanges
        v-if="hasPriceRanges"
        :key="priceRangesKey"
        :priceRanges="form.priceRanges"
        @addPriceRange="handleAddPriceRange"
        @removeRange="handleRemoveRange"
        @updatePriceRanges="form.priceRanges = $event"
      />
    </div>
  </div>
</template>

<script setup>
import { inject, ref, watch, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';
import PriceRanges from '@/components/productVariations/PriceRanges.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import { HugeiconsIcon } from '@hugeicons/vue';
import { Alert01Icon } from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();

const form = inject('productForm');

const props = defineProps({});

const isUpdating = ref(false);
const hasPriceRanges = ref(false);
const priceRangesKey = ref(0);
const lastEdited = ref(null);
    const hasNegativeMargin = ref(false);
    const negativeMarginValue = ref('');
const userSetMargin = ref(false);

const validationRules = {
  price: {},
  originalPrice: {},
  costPrice: {},
  profitMargin: {}
};

defineExpose({ validationRules, hasNegativeMargin });

const determineCalculationType = () => {
  const currentField = lastEdited.value;
  
  if (currentField === 'profitMargin') {
    userSetMargin.value = true;
    return 'calculatePrice';
  } else if (currentField === 'price') {
    userSetMargin.value = false;
    return 'calculateMargin';
  } else if (currentField === 'costPrice') {
    if (userSetMargin.value && form.value.profitMargin && form.value.profitMargin !== '') {
      return 'calculatePrice';
    }
    return 'calculateMargin';
  }
  
  return null;
};

const parseMoneyValue = (value) => {
  if (!value || value === '') return 0;
  if (typeof value === 'number') return Math.max(0, value);
  
  let cleanValue = String(value).trim();
  cleanValue = cleanValue.replace(/[R$\s€£¥₹]/g, '');
  cleanValue = cleanValue.replace(/[^\d.,\-]/g, '');
  
  const isNegative = cleanValue.startsWith('-');
  if (isNegative) {
    cleanValue = cleanValue.substring(1);
  }
  
  if (!/\d/.test(cleanValue)) return 0;
  
  if (cleanValue.includes(',') && cleanValue.includes('.')) {
    const lastComma = cleanValue.lastIndexOf(',');
    const lastDot = cleanValue.lastIndexOf('.');
    
    if (lastComma > lastDot) {
      cleanValue = cleanValue.replace(/\./g, '').replace(',', '.');
    } else {
      cleanValue = cleanValue.replace(/,/g, '');
    }
  } else if (cleanValue.includes(',')) {
    const commaCount = (cleanValue.match(/,/g) || []).length;
    const afterLastComma = cleanValue.split(',').pop();
    
    if (commaCount === 1 && afterLastComma.length <= 2) {
      cleanValue = cleanValue.replace(',', '.');
    } else {
      cleanValue = cleanValue.replace(/,/g, '');
    }
  } else if (cleanValue.includes('.')) {
    const dotCount = (cleanValue.match(/\./g) || []).length;
    const afterLastDot = cleanValue.split('.').pop();
    
    if (dotCount === 1 && afterLastDot.length <= 2) {
    } else {
      cleanValue = cleanValue.replace(/\./g, '');
    }
  }
  
  const result = parseFloat(cleanValue);
  const finalResult = isNaN(result) ? 0 : result;
  
  return Math.max(0, isNegative ? -finalResult : finalResult);
};

const formatMoneyValue = (value) => {
  if (!value || isNaN(value)) return '';
  return parseFloat(value).toFixed(2);
};

const parsePercentValue = (value) => {
  if (!value || value === '') return 0;
  if (typeof value === 'number') return Math.max(0, value);
  
  let cleanValue = String(value).trim();
  cleanValue = cleanValue.replace(/[^\d.,-]/g, '');
  
  if (!/\d/.test(cleanValue)) return 0;
  
  if (cleanValue.includes(',')) {
    if (cleanValue.includes('.')) {
      cleanValue = cleanValue.replace(/\./g, '').replace(',', '.');
    } else {
      cleanValue = cleanValue.replace(',', '.');
    }
  } else if (cleanValue.includes('.')) {
    const parts = cleanValue.split('.');
    if (parts.length > 2 || (parts.length === 2 && parts[1].length > 2)) {
      cleanValue = cleanValue.replace(/\./g, '');
    }
  }
  
  const result = parseFloat(cleanValue);
  const finalResult = isNaN(result) ? 0 : result;
  
  return Math.max(0, finalResult);
};

const formatPercentValue = (value) => {
  if (!value || isNaN(value)) return '';
  const num = parseFloat(value);
  
  if (num % 1 === 0) {
    return num.toString();
  } else {
    return num.toFixed(2).replace('.', ',');
  }
};

const calculationLogic = () => {
  if (!form.value) return;
  isUpdating.value = true;

  const price = parseMoneyValue(form.value.price);
  const costPrice = parseMoneyValue(form.value.costPrice);
  const profitMargin = parsePercentValue(form.value.profitMargin);

  const clearNegativeMarginState = () => {
    hasNegativeMargin.value = false;
    negativeMarginValue.value = '';
  };

  const setNegativeMarginState = (marginValue) => {
    hasNegativeMargin.value = true;
    negativeMarginValue.value = formatPercentValue(marginValue);
    form.value.profitMargin = '';
    userSetMargin.value = false;
  };

  const calculationType = determineCalculationType();
  
  if (!calculationType) {
    nextTick(() => {
      isUpdating.value = false;
    });
    return;
  }

  if (price < 0 || costPrice < 0) {
    form.value.profitMargin = '';
    clearNegativeMarginState();
    userSetMargin.value = false;
    nextTick(() => {
      isUpdating.value = false;
    });
    return;
  }

  if (calculationType === 'calculatePrice') {
    clearNegativeMarginState();
    
    if (profitMargin < 0) {
      form.value.profitMargin = formatPercentValue(0);
      nextTick(() => {
        isUpdating.value = false;
      });
      return;
    }
    
    if (profitMargin >= 100) {
      form.value.profitMargin = formatPercentValue(99.9);
    }
    
    if (costPrice > 0 && profitMargin >= 0 && profitMargin < 100) {
      const newPrice = costPrice / (1 - (profitMargin / 100));
      
      if (!isNaN(newPrice) && isFinite(newPrice) && newPrice <= 1000000) {
        form.value.price = formatMoneyValue(newPrice);
        nextTick(() => props.formContext?.validateField?.('price'));
      }
    }
  } 
  else if (calculationType === 'calculateMargin') {
    if (price === 0 && costPrice === 0) {
      form.value.profitMargin = '';
      clearNegativeMarginState();
    } else if (costPrice === 0 && price > 0) {
      clearNegativeMarginState();
      form.value.profitMargin = formatPercentValue(100);
    } else if (price > 0 && costPrice > 0 && price <= costPrice) {
      const margin = ((price - costPrice) / price) * 100;
      setNegativeMarginState(margin);
    } else if (price > 0 && costPrice > 0) {
      const margin = ((price - costPrice) / price) * 100;
      clearNegativeMarginState();
      form.value.profitMargin = formatPercentValue(margin);
      if (calculationType === 'calculateMargin') {
        userSetMargin.value = false;
      }
    } else {
      form.value.profitMargin = '';
    }
  } else if (lastEdited.value === 'profitMargin') {
    if(profitMargin >= 100){
      form.value.profitMargin = 99;
    }
    if (costPrice > 0 && profitMargin >= 0 && profitMargin < 100) {
      const newPrice = costPrice / (1 - (profitMargin / 100));
      if (Math.abs(form.value.price - newPrice) > 0.01) {
        form.value.price = parseFloat(newPrice.toFixed(2));
      }
    } else {
      form.value.price = '';
      clearNegativeMarginState();
      userSetMargin.value = false;
    }
  }
  
  nextTick(() => {
    isUpdating.value = false;
  });
};

watch(
  () => form.value ? [form.value.price, form.value.costPrice, form.value.profitMargin] : [null, null, null],
  ([newPrice, newCost, newMargin], [oldPrice, oldCost, oldMargin]) => {
    if (isUpdating.value || !form.value) return;

    const isPriceEmpty = !newPrice || newPrice === '';
    const isCostEmpty = !newCost || newCost === '';
    
    if (isPriceEmpty && isCostEmpty && hasNegativeMargin.value) {
      hasNegativeMargin.value = false;
      negativeMarginValue.value = '';
      return;
    }

    const priceChanged = newPrice !== oldPrice;
    const costChanged = newCost !== oldCost;
    const marginChanged = newMargin !== oldMargin;

    if (priceChanged || costChanged || marginChanged) {
      const newPriceNum = parseMoneyValue(newPrice);
      const oldPriceNum = parseMoneyValue(oldPrice);
      const newCostNum = parseMoneyValue(newCost);
      const oldCostNum = parseMoneyValue(oldCost);
      const newMarginNum = parsePercentValue(newMargin);
      const oldMarginNum = parsePercentValue(oldMargin);

      if (marginChanged) {
        lastEdited.value = 'profitMargin';
        calculationLogic();
      } else if (priceChanged && Math.abs(newPriceNum - oldPriceNum) > 0.001) {
        lastEdited.value = 'price';
        calculationLogic();
      } else if (costChanged && Math.abs(newCostNum - oldCostNum) > 0.001) {
        lastEdited.value = 'costPrice';
        calculationLogic();
      }
    }
  }
);

const emit = defineEmits(['update:hasPriceRanges']);

watch(() => form.value, (newForm) => {
  if (newForm) {
    if (!newForm.priceRanges) {
      newForm.priceRanges = [];
    }
    hasPriceRanges.value = newForm.hasPriceRange || (newForm.priceRanges && newForm.priceRanges.length > 0);
    emit('update:hasPriceRanges', hasPriceRanges.value);
  }
}, { immediate: true, deep: true });

watch(() => hasPriceRanges.value, (newValue) => {
  if (!form.value) return;
  emit('update:hasPriceRanges', newValue);
  
  if (!newValue) {
    form.value.priceRanges = [];
    priceRangesKey.value++;
  } else if (newValue && form.value.priceRanges.length === 0) {
    form.value.priceRanges = [{ quantity: 1, price: form.value.price || 0 }];
  }
});

const validateProfitMarginInput = (event) => {
  const allowedChars = /[0-9.,]/;
  if (!allowedChars.test(event.key)) {
    event.preventDefault();
  }
  
  if ((event.key === ',' || event.key === '.') && 
      (event.target.value.includes(',') || event.target.value.includes('.'))) {
    event.preventDefault();
  }
};

const handleProfitMarginInput = (event) => {
  const value = event.target.value;
  lastEdited.value = 'profitMargin';
  
  const cleanValue = value.replace(/[^0-9.,]/g, '');
  
  const numValue = parseFloat(cleanValue.replace(',', '.'));
  
  if (!isNaN(numValue) && numValue >= 100) {
    form.value.profitMargin = '99,9';
  } else {
    form.value.profitMargin = cleanValue;
  }
  
  setTimeout(() => {
    if (lastEdited.value === 'profitMargin') {
      calculationLogic();
    }
  }, 0);
};
</script> 

<style scoped>
.profit-margin-error {
  display: flex;
  flex-direction: column;
  gap: 1px;
  max-width: 100%;
}

.field-label {
  margin-bottom: 6px;
  display: block;
}

.error-label {
  color: var(--iluria-color-error);
}

.error-display {
  padding: 3px 12px;
  border: 1px solid var(--iluria-color-error);
  border-radius: 8px;
  background: var(--iluria-color-error-bg);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  max-width: 100%;
  max-height: 39px;
  box-sizing: border-box;
}

.error-content {
  display: flex;
  align-items: center;
  gap: 8px;
}

.error-icon {
  font-size: 14px;
  color: var(--iluria-color-error);
  flex-shrink: 0;
}

.error-text {
  display: flex;
  flex-direction: column;
  gap: px;
  flex: 1;
  min-width: 0;
}

.error-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--iluria-color-error);
  line-height: 1.3;
}

.error-subtitle {
  font-size: 12px;
  font-weight: 500;
  color: var(--iluria-color-error-dark);
  line-height: 1.3;
}

.error-hint {
  font-size: 11px;
  color: var(--iluria-color-error-muted);
  margin-top: 1px;
  line-height: 1.3;
}

.error-display {
  animation: errorSlideIn 0.3s ease-out;
}

@keyframes errorSlideIn {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .error-display {
    padding: 6px 10px;
  }
  
  .error-content {
    gap: 6px;
  }

  .error-title {
    font-size: 12px;
  }
}
</style>