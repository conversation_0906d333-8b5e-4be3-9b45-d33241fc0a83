<template>
  <div class="store-health">
    <div v-if="loading" class="loading-state">
      <div class="score-skeleton"></div>
      <div v-for="i in 4" :key="i" class="indicator-skeleton"></div>
    </div>
    
    <div v-else class="health-content">
      <!-- Health Score -->
      <div class="health-score">
        <div class="score-container">
          <div class="score-circle">
            <svg :width="scoreSize" :height="scoreSize" class="score-svg">
              <circle
                :cx="scoreSize / 2"
                :cy="scoreSize / 2"
                :r="scoreRadius"
                fill="none"
                stroke="#f3f4f6"
                :stroke-width="strokeWidth"
              />
              <circle
                :cx="scoreSize / 2"
                :cy="scoreSize / 2"
                :r="scoreRadius"
                fill="none"
                :stroke="getScoreColor(health.score)"
                :stroke-width="strokeWidth"
                stroke-linecap="round"
                :stroke-dasharray="scoreCircumference"
                :stroke-dashoffset="scoreOffset"
                class="score-progress"
              />
            </svg>
            <div class="score-text">
              <span class="score-value">{{ health.score }}</span>
              <span class="score-label">{{ $t('dashboard.healthScore') }}</span>
            </div>
          </div>
          <div class="score-status">
            <component :is="getScoreIcon(health.score)" class="status-icon" />
            <span class="status-text">{{ getScoreStatus(health.score) }}</span>
          </div>
        </div>
      </div>

      <!-- Health Indicators -->
      <div class="health-indicators">
        <div 
          v-for="indicator in health.indicators" 
          :key="indicator.name"
          class="indicator-item"
        >
          <div class="indicator-header">
            <span class="indicator-name">{{ indicator.name }}</span>
            <span class="indicator-value" :class="getIndicatorClass(indicator.status)">
              {{ indicator.value }}
            </span>
          </div>
          <div class="indicator-bar">
            <div 
              class="indicator-fill" 
              :class="getIndicatorClass(indicator.status)"
              :style="{ width: getIndicatorWidth(indicator) + '%' }"
            ></div>
          </div>
          <div class="indicator-status">
            <component :is="getIndicatorIcon(indicator.status)" class="indicator-status-icon" />
            <span class="indicator-status-text">{{ getIndicatorStatusText(indicator.status) }}</span>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import {
  CheckmarkCircle01Icon,
  Alert02Icon,
  CancelCircleIcon,
  WinkIcon,
  Sad01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const props = defineProps({
  health: {
    type: Object,
    default: () => ({
      score: 0,
      indicators: []
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()

// Score circle dimensions
const scoreSize = 100
const strokeWidth = 8
const scoreRadius = (scoreSize - strokeWidth) / 2
const scoreCircumference = 2 * Math.PI * scoreRadius

const scoreOffset = computed(() => {
  const progress = props.health.score / 100
  return scoreCircumference * (1 - progress)
})

const getScoreColor = (score) => {
  if (score >= 80) return '#10b981'
  if (score >= 60) return '#f59e0b'
  return '#ef4444'
}

const getScoreIcon = (score) => {
  if (score >= 70) return WinkIcon
  return Sad01Icon
}

const getScoreStatus = (score) => {
  if (score >= 80) return t('dashboard.excellent')
  if (score >= 60) return t('dashboard.good')
  if (score >= 40) return t('dashboard.fair')
  return t('dashboard.poor')
}

const getIndicatorClass = (status) => {
  const classes = {
    'good': 'indicator-good',
    'warning': 'indicator-warning',
    'error': 'indicator-error'
  }
  return classes[status] || 'indicator-good'
}

const getIndicatorIcon = (status) => {
  const icons = {
    'good': CheckmarkCircle01Icon,
    'warning': Alert02Icon,
    'error': CancelCircleIcon
  }
  return icons[status] || CheckmarkCircle01Icon
}

const getIndicatorStatusText = (status) => {
  const texts = {
    'good': t('dashboard.good'),
    'warning': t('dashboard.warning'),
    'error': t('dashboard.error')
  }
  return texts[status] || status
}

const getIndicatorWidth = (indicator) => {
  // For visual purposes, convert the value to a percentage
  if (indicator.status === 'good') return Math.min(indicator.value, 100)
  if (indicator.status === 'warning') return Math.min(indicator.value * 0.6, 100)
  return Math.min(indicator.value * 0.3, 100)
}
</script>

<style scoped>
.store-health {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.score-skeleton {
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  margin: 0 auto 16px;
}

.indicator-skeleton {
  height: 48px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.health-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.health-score {
  display: flex;
  justify-content: center;
}

.score-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.score-circle {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
}

.score-svg {
  transform: rotate(-90deg);
}

.score-progress {
  transition: stroke-dashoffset 0.5s ease;
}

.score-text {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.score-value {
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  line-height: 1;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.score-label {
  font-size: 0.6rem;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.score-status {
  display: flex;
  align-items: center;
  gap: 6px;
}

.status-icon {
  width: 16px;
  height: 16px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.status-text {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.health-indicators {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.indicator-item {
  background: var(--iluria-color-container-bg);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--iluria-color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.indicator-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.indicator-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.indicator-value {
  font-size: 0.875rem;
  font-weight: 600;
}

.indicator-bar {
  height: 6px;
  background: var(--iluria-color-border);
  border-radius: 3px;
  overflow: hidden;
  margin-bottom: 8px;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.indicator-fill {
  height: 100%;
  border-radius: 3px;
  transition: width 0.3s ease;
}

.indicator-status {
  display: flex;
  align-items: center;
  gap: 4px;
}

.indicator-status-icon {
  width: 12px;
  height: 12px;
}

.indicator-status-text {
  font-size: 0.75rem;
  font-weight: 500;
}

/* Status colors */
.indicator-good {
  color: var(--iluria-color-success);
}

.indicator-good.indicator-fill {
  background: var(--iluria-color-success);
}

.indicator-good .indicator-status-icon {
  color: var(--iluria-color-success);
}

.indicator-warning {
  color: var(--iluria-color-warning);
}

.indicator-warning.indicator-fill {
  background: var(--iluria-color-warning);
}

.indicator-warning .indicator-status-icon {
  color: var(--iluria-color-warning);
}

.indicator-error {
  color: var(--iluria-color-error);
}

.indicator-error.indicator-fill {
  background: var(--iluria-color-error);
}

.indicator-error .indicator-status-icon {
  color: var(--iluria-color-error);
}

@media (max-width: 768px) {
  .health-content {
    gap: 20px;
  }
  
  .indicator-item {
    padding: 12px;
  }
  
  .indicator-header {
    margin-bottom: 6px;
  }
  
  .indicator-name,
  .indicator-value {
    font-size: 0.8rem;
  }
  
  .score-value {
    font-size: 1.25rem;
  }
  
  .score-label {
    font-size: 0.55rem;
  }
}
</style> 
