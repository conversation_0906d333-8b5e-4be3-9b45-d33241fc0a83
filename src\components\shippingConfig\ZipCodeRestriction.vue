<template>
  <div class="zip-code-restriction">
    <!-- Toggle Section -->
    <div class="toggle-section">
      <span class="toggle-label">{{ t('shippingConfig.zipCodeRestriction.toggleTitle') }}</span>
      <IluriaToggleSwitch
        id="toggle-zip-code-restriction"
        v-model="enabled"
      />
    </div>
    
    <!-- Form Section -->
    <div v-if="enabled" class="form-section">
      <!-- Header Labels -->
      <div class="form-header">
        <div class="header-label">{{ t('shippingConfig.zipCodeRestriction.regionNameLabel') }}</div>
        <div class="header-label">{{ t('shippingConfig.zipCodeRestriction.startZipLabel') }}</div>
        <div class="header-label">{{ t('shippingConfig.zipCodeRestriction.endZipLabel') }}</div>
        <div class="header-label"></div>
      </div>
      
      <!-- Range Rows -->
      <div class="ranges-container">
        <div v-for="(range, index) in ranges" :key="index" class="range-row">
          <div class="input-field">
            <IluriaInputText 
              v-model="range.name" 
              :id="`rangeName-${index}`"
              :name="`rangeName-${index}`"
              :placeholder="t('shippingConfig.zipCodeRestriction.regionPlaceholder')"
              :formContext="props.formContext[`rangeName-${index}`]"
            />
          </div>
          
          <div class="input-field">
            <IluriaInputText 
              v-model="range.startZipCode" 
              :id="`startZipCode-${index}`"
              :name="`startZipCode-${index}`"
              :placeholder="t('shippingConfig.zipCodeRestriction.zipPlaceholder')"
              type="mask"
              mask="cep"
              :formContext="props.formContext[`startZipCode-${index}`]"
            />
          </div>
          
          <div class="input-field">
            <IluriaInputText 
              v-model="range.endZipCode" 
              :id="`endZipCode-${index}`"
              :name="`endZipCode-${index}`"
              :placeholder="t('shippingConfig.zipCodeRestriction.zipPlaceholder')"
              type="mask"
              mask="cep"
              :formContext="props.formContext[`endZipCode-${index}`]"
            />
          </div>    
            <IluriaButton
              color="danger"
              variant="ghost"
              :hugeIcon="Delete03Icon"
              @click.prevent="removeZipCodeRestrictionRange(index)"
            >
            </IluriaButton>
        </div>
      </div>
      
      <!-- Add Button -->
      <div class="add-button-container">
        <IluriaButton
          type="button"
          color="dark"
          :hugeIcon="AddCircleIcon"
          @click.prevent="addZipCodeRestrictionRange"
        >
          {{ t('shippingConfig.zipCodeRestriction.addRange') }}
        </IluriaButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { nextTick, watch, computed } from 'vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { Delete03Icon, AddCircleIcon } from '@hugeicons-pro/core-stroke-rounded';
import { requiredText } from '@/services/validation.service';
import { useToast } from '@/services/toast.service';

const { t } = useI18n();
const toast = useToast();

const enabled = defineModel('enabled');
const ranges = defineModel('ranges');

const hasEmptyFields = computed(() => {
  if (!ranges.value || ranges.value.length === 0) return false;
  return ranges.value.some(range => 
    !range.name?.trim() || !range.startZipCode || !range.endZipCode
  );
});

const isValid = computed(() => !hasEmptyFields.value);

const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

const translations = {
  regionNameLabel: t('shippingConfig.zipCodeRestriction.regionNameLabel'),
  startZipLabel: t('shippingConfig.zipCodeRestriction.startZipLabel'),
  endZipLabel: t('shippingConfig.zipCodeRestriction.endZipLabel')
};

const validationRules = computed(() => {
  let rules = {}

  if(ranges.value && ranges.value.length > 0) {
    for (let i = 0; i < ranges.value.length; i++) {
      rules[`rangeName-${i}`] = requiredText(translations.regionNameLabel);
      rules[`startZipCode-${i}`] = requiredText(translations.startZipLabel);
      rules[`endZipCode-${i}`] = requiredText(translations.endZipLabel);
    }
  }

  return rules;
});

defineExpose({
  validationRules,
  isValid,
  hasEmptyFields: computed(() => hasEmptyFields.value)
});

// Observar mudanças no modelo para atualizar o estado interno
watch(
  enabled,
  (enabledValue) => {
    if (enabledValue) {
      if (ranges.value && ranges.value.length === 0) {
        nextTick(() => {
          addZipCodeRestrictionRange();
        });
      }
    }
  }
);

const addZipCodeRestrictionRange = () => {
  if (hasEmptyFields.value) {
    toast.showWarning(t('shippingConfig.zipCodeRestriction.completeAllFields'));
    return;
  }
  
  ranges.value = [...ranges.value, {
    name: '',
    startZipCode: '',
    endZipCode: ''
  }];
};

const removeZipCodeRestrictionRange = (index) => {
  if (ranges.value.length <= 1) {
    toast.showWarning(t('shippingConfig.zipCodeRestriction.atLeastOneRange'));
    return;
  }
  
  ranges.value = ranges.value.filter((_, i) => i !== index);
  
  if (ranges.value.length === 0) {
    enabled.value = false;
  }
};
</script>

<style scoped>
.zip-code-restriction {
  width: 100%;
  box-sizing: border-box;
}

.toggle-section {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
}

.toggle-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.form-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.form-header {
  display: grid;
  grid-template-columns: 1fr 0.8fr 0.8fr 0.6fr;
  gap: 12px;
  margin-bottom: 8px;
}

.header-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  padding: 0 4px;
}

.ranges-container {
  display: flex;
  flex-direction: column;
  gap: 12px;
  width: 100%;
  max-width: 100%;
}

.range-row {
  display: grid;
  grid-template-columns: 1fr 0.8fr 0.8fr 0.6fr;
  gap: 12px;
  align-items: center;
}

.input-field {
  width: 100%;
}

.input-field :deep(.p-inputtext) {
  width: 100%;
  box-sizing: border-box;
}

.delete-field {
  display: flex;
  justify-content: center;
  align-items: center;
}

.add-button-container {
  margin-top: 16px;
  display: flex;
  justify-content: flex-start;
  width: 100%;
  box-sizing: border-box;
}


/* Responsive Design */
@media (max-width: 1024px) {
  .form-header,
  .range-row {
    grid-template-columns: 1fr 0.7fr 0.7fr 0.5fr;
    gap: 10px;
  }
  

}

@media (max-width: 900px) {
  .form-header,
  .range-row {
    grid-template-columns: 1fr 0.6fr 0.6fr 0.4fr;
    gap: 8px;
  }
  

}

@media (max-width: 768px) {
  .form-header {
    display: none;
  }
  
  .range-row {
    display: flex;
    flex-direction: column;
    gap: 12px;
    padding: 16px;
    border: 1px solid var(--iluria-color-border);
    border-radius: 8px;
    background-color: var(--iluria-color-bg-secondary);
  }
  
  .add-button-container {
    display: flex;
    justify-content: flex-start;
    margin-top: 16px;
  }
  
  .input-field {
    display: flex;
    flex-direction: column;
    gap: 6px;
  }
  
  .input-field::before {
    content: attr(data-label);
    font-size: 14px;
    font-weight: 500;
    color: var(--iluria-color-text-secondary);
  }
  
  .range-row .input-field:nth-child(1)::before {
    content: "Nome da região atendida:";
  }
  
  .range-row .input-field:nth-child(2)::before {
    content: "CEP inicial:";
  }
  
  .range-row .input-field:nth-child(3)::before {
    content: "CEP final:";
  }
  
  .delete-field {
    justify-content: flex-end;
    margin-top: 8px;
  }
}

@media (max-width: 480px) {
  .toggle-section {
    flex-direction: column;
    align-items: flex-start;
    gap: 12px;
  }
  
  .add-button {
    width: 100%;
    justify-content: center;
  }
}

/* Prevent overflow */
.zip-code-restriction * {
  box-sizing: border-box;
}

.zip-code-restriction .form-header,
.zip-code-restriction .range-row {
  min-width: 0;
}

.zip-code-restriction .input-field,
.zip-code-restriction .delete-field {
  min-width: 0;
  overflow: hidden;
}

/* Ensure all children stay within bounds */
.zip-code-restriction > *,
.zip-code-restriction .form-section > *,
.zip-code-restriction .add-button-container > * {
  max-width: 100%;
  box-sizing: border-box;
}


</style>
