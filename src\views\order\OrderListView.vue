<template>
  <div class="orders-list-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('order.title')"
      :subtitle="t('order.ordersListSubtitle')"
      :showSearch="true"
      :showAdd="true"
      :addText="t('order.newOrder')"
      :searchPlaceholder="t('order.searchPlaceholder')"
      @search="handleSearch"
      @add-click="goToAddOrder"
    />

    <!-- Table Wrapper -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="sortedOrders"
        :columns="mainTableColumns"
        :loading="loading"
        dataKey="id"
        class="orders-table"
      >
        <template #header-orderNumber>
          <span class="column-header" data-sortable="true" @click="toggleSort('orderNumber')">
            {{ t('order.code') }}
            <span v-if="sortField === 'orderNumber'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-customerName>
          <span class="column-header" data-sortable="true" @click="toggleSort('customerName')">
            {{ t('order.customerName') }}
            <span v-if="sortField === 'customerName'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-createdAt>
          <span class="column-header" data-sortable="true" @click="toggleSort('createdAt')">
            {{ t('order.createdAt') }}
            <span v-if="sortField === 'createdAt'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-total>
          <span class="column-header" data-sortable="true" @click="toggleSort('total')">
            {{ t('order.total') }}
            <span v-if="sortField === 'total'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-shippingStatus>
          <span class="column-header" data-sortable="true" @click="toggleSort('shippingStatus')">
            {{ t('order.shippingStatus') }}
            <span v-if="sortField === 'shippingStatus'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-paymentStatus>
          <span class="column-header" data-sortable="true" @click="toggleSort('paymentStatus')">
            {{ t('order.paymentStatus') }}
            <span v-if="sortField === 'paymentStatus'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-actions>
          <span class="column-header">{{ t('order.action') }}</span>
        </template>

        <!-- Column Templates -->
        <template #column-orderNumber="{ data }">
          <span class="order-number">#{{ data.orderNumber }}</span>
        </template>
        <template #column-customerName="{ data }">
          <div class="customer-info">
            <span class="customer-name">{{ data.customerName }}</span>
            <a :href="`mailto:${data.customerEmail}`" class="email-link">
              {{ data.customerEmail }}
            </a>
          </div>
        </template>
        <template #column-createdAt="{ data }">
          <span class="date-text">{{ formatDate(data.createdAt) }}</span>
        </template>
        <template #column-total="{ data }">
          <span class="total-amount">R$ {{ formatCurrency(data.total) }}</span>
        </template>
        <template #column-shippingStatus="{ data }">
          <span
            class="status-badge"
            :class="getStatusClass(data.shippingStatus)"
          >
            {{ getStatusLabel(data.shippingStatus) }}
          </span>
        </template>
        <template #column-paymentStatus="{ data }">
          <span
            class="status-badge"
            :class="getStatusClass(data.paymentStatus)"
          >
            {{ getStatusLabel(data.paymentStatus) }}
          </span>
        </template>
        <template #column-actions="{ data }">
            <div class="action-buttons">
              <IluriaButton 
                color="text-primary" 
                size="small" 
                :hugeIcon="PencilEdit01Icon"
                @click="editOrder(data.id)" 
                :title="t('order.editOrder')"
              />
              <IluriaButton 
                color="text-danger" 
                size="small" 
                :hugeIcon="Delete01Icon"
                @click="confirmDeleteOrder(data)" 
                :title="t('order.deleteOrder')"
              />
            </div>
        </template>
        
        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <HugeiconsIcon :icon="ShoppingBasket01Icon" :size="48" class="text-gray-400" />
            </div>
            <h3 class="empty-title">{{ t('order.noOrders') }}</h3>
            <p class="empty-description">{{ t('order.noOrdersDescription') }}</p>
            <IluriaButton 
              color="dark"
              :hugeIcon="PlusSignIcon"
              @click="goToAddOrder"
              class="mt-4"
            >
              {{ t('order.createFirstOrder') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('order.loadingOrders') }}</span>
          </div>
        </template>
      </IluriaDataTable>
    </div>

    <!-- Pagination -->
    <div class="pagination-container" v-if="totalPages > 0">
      <IluriaPagination 
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />
    </div>

    <!-- Confirm Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { OrderService } from '@/services/order.service'
import { useToast } from 'primevue/usetoast'
import { useRouter } from 'vue-router'
import { useStatus } from '@/utils/statusHelper'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Delete01Icon,
  PencilEdit01Icon,
  ShoppingBasket01Icon,
  PlusSignIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const toast = useToast()
const router = useRouter()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const orders = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(5)
const itemsPerPage = 5
const filters = ref({ filter: '' })

const sortField = ref('createdAt')
const sortOrder = ref(-1)

const mainTableColumns = [
  { field: 'orderNumber', class: 'col-small' },
  { field: 'customerName', class: 'col-flex' },
  { field: 'createdAt', class: 'col-date' },
  { field: 'total', class: 'col-medium' },
  { field: 'shippingStatus', class: 'col-medium' },
  { field: 'paymentStatus', class: 'col-medium' },
  { field: 'actions', class: 'col-actions' }
]

const toggleSort = (field) => {
  if (sortField.value === field) {
    sortOrder.value *= -1
  } else {
    sortField.value = field
    sortOrder.value = 1
  }
}

const sortedOrders = computed(() => {
  const sorted = [...orders.value]

  sorted.sort((a, b) => {
    const fieldA = a[sortField.value]
    const fieldB = b[sortField.value]

    let comparison = 0
    if (fieldA > fieldB) {
      comparison = 1
    } else if (fieldA < fieldB) {
      comparison = -1
    }

    return comparison * sortOrder.value
  })

  return sorted
})


const STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED',
  PAID: 'PAID',
  UNPAID: 'UNPAID'
}

const formatDate = (date) => {
  if (!date) return ''
  const dateObj = date instanceof Date ? date : new Date(date)
  const day = dateObj.getDate().toString().padStart(2, '0')
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0')
  const year = dateObj.getFullYear()
  return `${day}/${month}/${year}`
}

const formatCurrency = (value) => {
  return Number(value).toFixed(2).replace('.', ',')
}

const { getStatusClass, getStatusLabel } = useStatus()

const loadOrders = async () => {
  try {
    loading.value = true
    const response = await OrderService.listOrders(filters.value.filter, currentPage.value, itemsPerPage)
    
    orders.value = response.content
    totalPages.value = response.totalPages
    
    orders.value = orders.value.map(order => ({
      ...order,
      total: order.totalValue,
      status: mapApiStatusToUiStatus(order.shippingStatus)
    }))
  } catch (error) {
    console.error('Error loading orders:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('order.loadError'),
      life: 3000
    })
    orders.value = []
    totalPages.value = 1
  } finally {
    loading.value = false
  }
}

const mapApiStatusToUiStatus = (apiStatus) => {
  switch (apiStatus) {
    case 'PENDING': return STATUS.PENDING
    case 'PROCESSING': return STATUS.PROCESSING
    case 'SHIPPED': return STATUS.SHIPPED
    case 'DELIVERED': return STATUS.DELIVERED
    case 'CANCELLED': return STATUS.CANCELLED
    case 'PAID': return STATUS.PAID
    case 'UNPAID': return STATUS.UNPAID
    default: return STATUS.PENDING
  }
}

const filteredOrders = computed(() => {
  return orders.value
})

let searchTimeout = null
const debouncedSearch = () => {
  if (searchTimeout) {
    clearTimeout(searchTimeout)
  }

  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadOrders()
  }, 300)
}

const handleSearch = (searchValue) => {
  filters.value.filter = searchValue
  debouncedSearch()
}

const changePage = (page) => {
  currentPage.value = page
  loadOrders()
}

const viewOrder = (orderId) => {
  router.push({ name: 'order-view', params: { id: orderId } })
}

const editOrder = (orderId) => {
  router.push({ name: 'order-edit', params: { id: orderId } })
}

const goToAddOrder = () => {
  router.push({ name: 'order-new' })
}

// Modal control functions
const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const confirmDeleteOrder = (order) => {
  showConfirmDanger(
    t('order.confirmDeleteMessage', { orderNumber: order.orderNumber }),
    t('order.confirmDeleteTitle'),
    async () => {
      try {
        loading.value = true

        await OrderService.deleteOrder(order.id)

        orders.value = orders.value.filter(o => o.id !== order.id)

        toast.add({
          severity: 'success',
          summary: t('success'),
          detail: t('order.deleteSuccess'),
          life: 3000
        })

        loadOrders()
      } catch (error) {
        console.error('Erro ao excluir pedido:', error)
        toast.add({
          severity: 'error',
          summary: t('error'),
          detail: t('order.deleteError'),
          life: 3000
        })
      } finally {
        loading.value = false
      }
    }
  )
}

onMounted(() => {
  loadOrders()
})
</script>

<style scoped>
.orders-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  min-width: 250px;
}

/* Order specific styles */
.order-number {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}

.customer-info {
  display: flex;
  flex-direction: column;
  min-width: 0;
  max-width: 100%;
  overflow: hidden;
  text-align: left;
}

.customer-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.email-link {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  text-decoration: none;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  transition: color 0.3s ease;
}

.email-link:hover {
  text-decoration: underline;
  color: var(--iluria-color-primary);
}

.date-text {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}

.total-amount {
  font-size: 14px;
  font-weight: 600;
  color: #059669;
  transition: color 0.3s ease;
}

/* Status badges */
.status-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  transition: all 0.3s ease;
  white-space: nowrap;
  display: inline-block;
  min-width: fit-content;
  text-align: center;
}

/* Status badge colors - similar to ProductListView pattern */
.status-badge.status-pending {
  background: #fef3c7;
  color: #d97706;
}

.status-badge.status-processing {
  background: #dbeafe;
  color: #2563eb;
}

.status-badge.status-shipped {
  background: #e0e7ff;
  color: #7c3aed;
}

.status-badge.status-delivered {
  background: #d1fae5;
  color: #059669;
}

.status-badge.status-cancelled {
  background: #fee2e2;
  color: #dc2626;
}

.status-badge.status-paid {
  background: #d1fae5;
  color: #059669;
}

.status-badge.status-unpaid {
  background: #fee2e2;
  color: #dc2626;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

.action-buttons .btn {
  transition: all 0.2s ease;
}

.action-buttons .btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* General table styling */
:deep(.orders-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.orders-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.orders-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 12px 16px;
  text-align: center;
}

:deep(.orders-table .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.orders-table .p-datatable-thead > tr > th.col-flex .column-header) {
  justify-content: flex-start;
}

:deep(.orders-table .p-datatable-thead > tr > th.col-small) {
  text-align: left;
}

:deep(.orders-table .p-datatable-thead > tr > th.col-small .column-header) {
  flex-direction: row;
  gap: 2px;
  justify-content: flex-start;
}

:deep(.orders-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.orders-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.orders-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: left;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: left;
}

:deep(.orders-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}

/* Column header style */
:deep(.orders-table th .column-header) {
  display: flex;
  align-items: left;
  justify-content: center;
  gap: 4px;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.orders-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* Column width definitions */
:deep(.col-small) { width: 10%; }
:deep(.col-date) { width: 12%; }
:deep(.col-medium) { width: 15%; }
:deep(.col-actions) { width: 10%; }
:deep(.col-flex) { width: auto; }

/* Text alignment */
:deep(.orders-table .p-datatable-tbody > tr > td.col-date),
:deep(.orders-table .p-datatable-tbody > tr > td.col-medium) {
  text-align: center;
}

:deep(.orders-table .p-datatable-thead > tr > th[data-field="actions"]) {
  text-align: center;
}

:deep(.orders-table .p-datatable-tbody > tr > td.col-actions .action-buttons) {
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  font-size: 48px;
  color: var(--iluria-color-text-muted);
  margin-bottom: 16px;
  transition: color 0.3s ease;
  display: flex;
  justify-content: center;
  align-items: center;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
  text-align: center;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 16px 0;
  transition: color 0.3s ease;
  text-align: center;
  line-height: 1.5;
  max-width: 400px;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
  background: var(--iluria-color-surface);
  transition: all 0.3s ease;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* Responsive */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .search-input {
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .orders-list-container {
    padding: 16px;
  }
  
  .search-input {
    min-width: 150px;
  }
  
  :deep(.orders-table .p-datatable-tbody > tr > td) {
    padding: 12px 16px;
  }
  
  :deep(.orders-table .p-datatable-thead > tr > th) {
    padding: 12px 16px;
    font-size: 11px;
  }
}
</style>