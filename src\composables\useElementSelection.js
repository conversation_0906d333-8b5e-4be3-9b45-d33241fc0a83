import { ref } from 'vue'

/**
 * Sistema centralizado para gerenciamento de seleção de elementos
 * 
 * Resolve o problema de elementos internos serem selecionáveis quando
 * deveriam selecionar o componente pai (ex: textos dentro de carrossel)
 */

// Registry de componentes protegidos e suas configurações
const protectedComponentsRegistry = ref(new Map())

/**
 * Registra um componente como "protegido" - seus elementos internos não são selecionáveis
 * @param {string} componentType - Tipo do componente
 * @param {Object} config - Configuração de seleção
 */
export function registerProtectedComponent(componentType, config) {
  protectedComponentsRegistry.value.set(componentType, {
    ...config,
    componentType
  })

}

/**
 * Configurações padrão para componentes protegidos
 */
const defaultProtectedComponents = [
  {
    type: 'product-grid',
    selectors: [
      '[data-component="dynamic-grid-produtos"]',
      '[data-product-grid]',
      '[data-element-type="dynamicProductGrid"]'
    ],
    protectedChildren: [
      '.product-item', '.product-title', '.product-price', 
      '.product-button', '.product-image', '.product-info'
    ],
    description: 'Grid de produtos - seleciona o grid inteiro, não itens individuais'
  },
  {
    type: 'carousel',
    selectors: [
      '[data-component="carousel"]',
      '.iluria-carousel',
      '[data-element-type="carousel"]'
    ],
    protectedChildren: [
      '.carousel-slide', '.slide-title', '.slide-subtitle', 
      '.slide-button', '.carousel-indicators', '.indicator', 
      '.carousel-navigation', '.nav-button', '.carousel-slides', 
      '.slide-content', '.slide-image'
    ],
    description: 'Carrossel - seleciona o carrossel inteiro, não slides individuais'
  },
  {
    type: 'video',
    selectors: [
      '[data-component="video"]',
      '.iluria-video',
      '[data-element-type="video"]'
    ],
    protectedChildren: [
      '.video-container', '.video-content', '.video-section', '.video-wrapper',
      '.video-iframe', '.video-element', '.video-placeholder', '.placeholder-content',
      '.placeholder-icon', '.placeholder-text', '.content-section',
      '.video-title', '.video-description', '.video-button',
      'iframe', 'video', 'source', '.video-overlay', '.video-controls'
    ],
    description: 'Componente de vídeo moderno - seleciona o componente inteiro'
  },
  {
    type: 'location',
    selectors: [
      '[data-component="location"]',
      '.location-section',
      '[data-element-type="location"]'
    ],
    protectedChildren: [
      '.location-item', '.location-name', '.location-description',
      '.location-address', '.location-phone', '.location-email',
      '.location-social', '.social-link', '.location-details',
      '.detail-item', '.get-directions'
    ],
    description: 'Seção de localização - seleciona a seção inteira'
  },
  {
    type: 'statement',
    selectors: [
      '[data-component="statement"]',
      '.iluria-statement',
      '[data-element-type="statement"]'
    ],
    protectedChildren: [
      '.statement-text', '.statement-title', '.statement-subtitle',
      '.statement-button', '.statement-content', '.statement-wrapper'
    ],
    description: 'Componente de statement - seleciona o componente inteiro'
  },
  {
    type: 'company-information',
    selectors: [
      '[data-component="company-information"]',
      '.iluria-company-information',
      '[data-element-type="company-information"]'
    ],
    protectedChildren: [
      '.company-info-content', '.company-info-title', 
      '.company-info-description', '.company-info-action',
      '.company-info-button', '.company-info-image'
    ],
    description: 'Informações da empresa - seleciona o componente inteiro'
  },
  {
    type: 'payment-benefits',
    selectors: [
      '[data-component="payment-benefits"]',
      '.iluria-payment-benefits',
      '[data-element-type="paymentBenefits"]'
    ],
    protectedChildren: [
      '.benefits-container', '.benefit-item', '.benefit-title', 
      '.benefit-description', '.benefit-icon', '.benefit-content',
      '.benefits-title'
    ],
    description: 'Benefícios de pagamento - seleciona o componente inteiro'
  },
  {
    type: 'header',
    selectors: [
      '[data-component="header"]',
      '.iluria-header',
      '[data-element-type="header"]'
    ],
    protectedChildren: [
      '.header-main', '.header-container', '.header-content',
      '.header-left', '.header-center', '.header-right',
      '.header-logo', '.logo-text', '.logo-link',
      '.header-navigation', '.nav-menu', '.nav-item', '.nav-link',
      '.utility-services', '.utility-service', '.utility-btn',
      '.header-actions', '.action-btn', '.search-btn', '.account-btn', '.cart-btn',
      '.dropdown-menu', '.dropdown-icon', '.header-separator'
    ],
    description: 'Header - seleciona o componente header inteiro'
  },
  {
    type: 'footer',
    selectors: [
      '[data-component="footer"]',
      '.iluria-footer',
      '[data-element-type="footer"]'
    ],
    protectedChildren: [
      '.footer-main', '.footer-container', '.footer-content', '.footer-bottom',
      '.footer-store', '.store-name', '.footer-navigation', '.footer-nav-list', '.footer-link',
      '.footer-contact', '.contact-info', '.contact-email', '.contact-phone', '.contact-address',
      '.footer-social', '.social-links', '.social-link',
      '.footer-copyright', '.copyright-text', '.footer-made-with', '.made-with-text', '.made-with-link',
      '.footer-content-single'
    ],
    description: 'Footer - seleciona o componente footer inteiro'
  },
  {
    type: 'customer-review',
    selectors: [
      '[data-component="customer-review"]',
      '.iluria-customer-review',
      '[data-element-type="customerReview"]'
    ],
    protectedChildren: [
      '.customer-review-container', '.review-header', '.review-title', '.review-subtitle',
      '.reviews-container', '.review-card', '.review-card-header', '.review-info',
      '.reviewer-name', '.reviewer-avatar', '.reviewer-avatar-placeholder',
      '.review-stars', '.star', '.review-date', '.review-content', '.review-comment'
    ],
    description: 'Avaliações de clientes - seleciona o componente inteiro'
  },
  {
    type: 'product-checkout',
    selectors: [
      '[data-component="product-checkout"]',
      '.product-checkout-container',
      '.checkout-widget'
    ],
    protectedChildren: [
      '.checkout-wrapper', '.pricing-section', '.price-container', '.original-price', '.current-price', '.discount-badge',
      '.product-options', '.option-group', '.option-label', '.selected-value', '.size-guide',
      '.color-options', '.color-option', '.size-options', '.size-option',
      '.quantity-selector', '.qty-btn', '.qty-input',
      '.action-buttons', '.btn-add-cart', '.btn-buy-now', '.btn-icon',
      '.shipping-info', '.shipping-calc', '.shipping-label', '.cep-input-group', '.cep-input', '.cep-btn',
      '.shipping-benefits', '.benefit-item', '.benefit-icon', '.benefit-text'
    ],
    description: 'Checkout de produto - seleciona o componente inteiro'
  }
]

/**
 * Inicializa o sistema com componentes padrão
 */
export function initializeElementSelectionSystem() {

  
  defaultProtectedComponents.forEach(config => {
    registerProtectedComponent(config.type, config)
  })
  

}

/**
 * Verifica se um elemento está dentro de um componente protegido
 * @param {Element} element - Elemento a verificar
 * @returns {Element|null} - Elemento do componente protegido ou null
 */
export function findProtectedParent(element) {
  if (!element) return null
  
  // Para cada componente protegido registrado
  for (const [componentType, config] of protectedComponentsRegistry.value) {
    const { selectors } = config
    
    // Verifica se o elemento ou algum ancestral é um componente protegido
    for (const selector of selectors) {
      const protectedParent = element.closest(selector)
      if (protectedParent) {

        return protectedParent
      }
    }
  }
  
  return null
}

/**
 * Verifica se um elemento é um filho protegido de algum componente
 * @param {Element} element - Elemento a verificar
 * @returns {boolean} - Se é um elemento protegido
 */
export function isProtectedChild(element) {
  if (!element) return false
  
  for (const [componentType, config] of protectedComponentsRegistry.value) {
    const { selectors, protectedChildren } = config
    
    // Verifica se está dentro de um componente protegido
    const protectedParent = element.closest(selectors.join(','))
    if (protectedParent) {
      // Verifica se o elemento é um dos filhos protegidos
      for (const childSelector of protectedChildren) {
        if (element.matches && element.matches(childSelector)) {

          return true
        }
        if (element.closest(childSelector) === element) {

          return true
        }
      }
    }
  }
  
  return false
}

/**
 * Encontra o elemento correto para seleção, considerando proteções
 * @param {Element} clickedElement - Elemento clicado
 * @returns {Element|null} - Elemento que deve ser selecionado
 */
export function findSelectableElement(clickedElement) {
  if (!clickedElement) return null
  
  // 1. Verifica se é um filho protegido - se for, seleciona o pai protegido
  if (isProtectedChild(clickedElement)) {
    const protectedParent = findProtectedParent(clickedElement)
    if (protectedParent) {

      return protectedParent
    }
  }
  
  // 2. Verifica se é um componente protegido - se for, seleciona ele mesmo
  const protectedParent = findProtectedParent(clickedElement)
  if (protectedParent) {

    return protectedParent
  }
  
  // 3. Se não é protegido, usa lógica normal de busca por elementos editáveis
  return findEditableElement(clickedElement)
}

/**
 * Encontra elemento editável usando a lógica tradicional
 * @param {Element} element - Elemento a verificar
 * @returns {Element|null} - Elemento editável encontrado
 */
function findEditableElement(element) {
  const editableSelectors = [
    '[data-element-type]',
    '[data-component]',
    '[data-interactive-element]',
    '[data-text-element]',
    '[data-media-element]',
    '[data-vue-component]',
    '.component-container'
  ]
  
  // Primeiro verifica se o próprio elemento é editável
  if (editableSelectors.some(selector => element.matches && element.matches(selector))) {
    return element
  }
  
  // Se não for, procura o ancestral editável mais próximo
  return element.closest(editableSelectors.join(','))
}

/**
 * Adiciona um novo componente protegido dinamicamente
 * @param {string} componentType - Tipo do componente
 * @param {Array<string>} selectors - Seletores do componente pai
 * @param {Array<string>} protectedChildren - Seletores dos filhos protegidos
 * @param {string} description - Descrição do componente
 */
export function addProtectedComponent(componentType, selectors, protectedChildren, description = '') {
  registerProtectedComponent(componentType, {
    selectors: Array.isArray(selectors) ? selectors : [selectors],
    protectedChildren: Array.isArray(protectedChildren) ? protectedChildren : [protectedChildren],
    description
  })
}

/**
 * Verifica se um elemento deve ser ignorado durante o processamento de HTML
 * (usado para evitar wrapping de elementos internos de componentes protegidos)
 * @param {Element} element - Elemento a verificar
 * @returns {boolean} - Se deve ser ignorado
 */
export function shouldIgnoreElementForWrapping(element) {
  if (!element) return false
  
  // 1. Se já está dentro de um component-container, ignora
  if (element.closest('.component-container')) {
    return true
  }
  
  // 2. Se é um filho protegido de qualquer componente protegido, ignora
  if (isProtectedChild(element)) {

    return true
  }
  
  // 3. Se está dentro de um componente protegido, ignora
  const protectedParent = findProtectedParent(element)
  if (protectedParent) {
    
    return true
  }
  
  // 4. Verifica se é um DIV que contém outros elementos estruturais
  if (element.tagName === 'DIV' && element.querySelector('h1, h2, h3, h4, h5, h6, p, img')) {
    return true
  }
  
  return false
}

/**
 * Hook principal do composable
 */
export function useElementSelection() {
  // Inicializa o sistema se ainda não foi feito
  if (protectedComponentsRegistry.value.size === 0) {
    initializeElementSelectionSystem()
  }
  
  return {
    findSelectableElement,
    findProtectedParent,
    isProtectedChild,
    shouldIgnoreElementForWrapping,
    addProtectedComponent,
    registerProtectedComponent,
    initializeElementSelectionSystem
  }
} 