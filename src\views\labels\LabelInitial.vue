<template>
    <div class="page-container">
        <!-- <PERSON> Header -->
        <IluriaHeader
            :title="t('product.label.title')"
            :subtitle="t('product.label.subtitle', 'Crie e gerencie etiquetas e emblemas para seus produtos')"
            :showAdd="true"
            :addText="t('product.label.createFromAi')"
            @add-click="handleCreateFromAi"
        />

        <!-- AI Generator Section -->
        <ViewContainer 
            :title="t('product.label.AiGeneratorTitle')" 
            :icon="SparklesIcon"
            iconColor="purple"
            :subtitle="t('product.label.aiSubtitle', 'Use inteligência artificial para gerar etiquetas personalizadas')"
        >
            <div class="ai-generator-card">
                <div class="ai-icon-container">
                    <div class="ai-icon-background">
                        <div class="ai-icon-overlay"></div>
                        <IluriaTitle class="ai-icon-text">AI</IluriaTitle>
                        <div class="ai-sparkle">
                            <IluriaText class="sparkle-text">✦</IluriaText>
                        </div>
                    </div>
                </div>
                
                <div class="ai-content">
                    <div class="ai-header">
                        <IluriaTitle class="ai-title">{{ t('product.label.AiGeneratorTitle') }}</IluriaTitle>
                        <IluriaText class="ai-usage">{{ t('product.label.used') }}</IluriaText>
                        <IluriaButton color="primary" size="small">{{ t('product.label.upgrade') }}</IluriaButton>
                    </div>
                    
                    <div class="ai-description">
                        <IluriaText class="description-text">{{ t('product.label.buttonDescription') }}</IluriaText>
                        <IluriaButton color="secondary" :hugeIcon="SparklesIcon" class="create-ai-btn">
                            {{ t('product.label.createFromAi') }}
                        </IluriaButton>
                    </div>
                </div>
            </div>
        </ViewContainer>

        <!-- Create Section -->
        <ViewContainer 
            :title="t('product.label.create')" 
            :icon="PlusSignSquareIcon"
            iconColor="green"
            :subtitle="t('product.label.createSubtitle', 'Crie etiquetas e emblemas personalizados')"
        >
            <div class="create-cards-grid">
                <CreateCard 
                    v-for="card in cards" 
                    :key="card.type"
                    :title="card.title"
                    :description="card.description"
                    :bgColor="card.bgColor"
                    :icon="card.icon"
                />
            </div>
        </ViewContainer>

        <!-- List Section -->
        <ViewContainer 
            :title="t('product.label.listTitle')" 
            :icon="ListViewIcon"
            iconColor="blue"
            :subtitle="t('product.label.listSubtitle', 'Visualize todas as etiquetas criadas')"
        >
            <template #rightHeader>
                <IluriaButton color="secondary" size="small">
                    {{ t('product.label.viewFullListButton') }}
                </IluriaButton>
            </template>
            
            <div class="list-cards-grid">
                <ListCard 
                    v-for="list in lists" 
                    :key="list.type"
                    :title="list.title"
                    :count="list.count"
                    :bgColor="list.bgColor"
                    :icon="list.icon"
                />
            </div>
        </ViewContainer>
    </div>
</template>

<script setup>
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaText from '@/components/iluria/IluriaText.vue';
import CreateCard from '@/components/labels/CreateCard.vue';
import ListCard from '@/components/labels/ListCard.vue';
import IluriaTitle from '@/components/iluria/IluriaTitle.vue';
import { 
    NewReleasesIcon, 
    LabelImportantIcon, 
    SparklesIcon,
    PlusSignSquareIcon 
} from '@hugeicons-pro/core-bulk-rounded';
import { ListViewIcon } from '@hugeicons-pro/core-stroke-standard';
import { useI18n } from 'vue-i18n';

const { t } = useI18n();

const cards = [
  {
    type: 'badges',
    title: t("product.label.badgesTitle"),
    description: t("product.label.badgesDescription"),
    bgColor: 'bg-green-400',
    icon: NewReleasesIcon
  },
  {
    type: 'labels',
    title: t("product.label.labelsTitle"),
    description: t("product.label.labelsDescription"),
    bgColor: 'bg-yellow-400',
    icon: LabelImportantIcon
  }
];

const lists = [ 
 {
    type: 'badges',
    title: t('product.label.badgesTitle'),
    count: 0,
    bgColor: 'bg-green-400',
    icon: NewReleasesIcon
 },
 {
    type: 'labels',
    title: t('product.label.labelsTitle'),
    count: 0,
    bgColor: 'bg-yellow-400',
    icon: LabelImportantIcon
 }
];

const handleCreateFromAi = () => {
  // Função para criar etiqueta com IA - implementar conforme necessário

}

</script>

<style scoped>
.page-container {
    padding: 24px;
}



/* AI Generator Card */
.ai-generator-card {
    display: flex;
    align-items: flex-start;
    gap: 24px;
    padding: 24px;
    background: var(--iluria-color-surface);
    border: 1px solid var(--iluria-color-border);
    border-radius: 12px;
    transition: all 0.2s ease;
}

.ai-generator-card:hover {
    border-color: var(--iluria-color-border-hover);
    box-shadow: var(--iluria-shadow-md);
}

.ai-icon-container {
    flex-shrink: 0;
    width: 112px;
    height: 112px;
    padding: 16px;
    background: #f3f4f6;
    border-radius: 12px;
}

.ai-icon-background {
    width: 80px;
    height: 80px;
    position: relative;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 12px;
}

.ai-icon-overlay {
    position: absolute;
    inset: 0;
    background: #a855f7;
    border-radius: 12px;
    opacity: 0.8;
}

.ai-icon-text {
    position: relative;
    font-size: 24px;
    font-weight: 700;
    color: white;
    z-index: 1;
}

.ai-sparkle {
    position: absolute;
    bottom: -4px;
    right: -4px;
    width: 20px;
    height: 20px;
    background: #7c3aed;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
}

.sparkle-text {
    color: white;
    font-size: 12px;
    font-weight: 600;
}

.ai-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.ai-header {
    display: flex;
    align-items: center;
    gap: 16px;
    flex-wrap: wrap;
}

.ai-title {
    font-size: 20px;
    font-weight: 600;
    color: var(--iluria-color-text-primary);
}

.ai-usage {
    color: var(--iluria-color-text-muted);
    font-size: 14px;
}

.ai-description {
    display: flex;
    flex-direction: column;
    gap: 16px;
}

.description-text {
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
    line-height: 1.5;
}

.create-ai-btn {
    align-self: flex-start;
}

/* Create Cards Grid */
.create-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 24px;
}

/* List Cards Grid */
.list-cards-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
    gap: 24px;
}

/* Spacing between ViewContainers */
.page-container > :deep(.view-container:not(:last-child)) {
    margin-bottom: 24px;
}

/* Responsive */
@media (max-width: 768px) {
    .page-container {
        padding: 16px;
    }
    

    
    .ai-generator-card {
        flex-direction: column;
        align-items: center;
        text-align: center;
        gap: 16px;
        padding: 20px;
    }
    
    .ai-header {
        flex-direction: column;
        align-items: center;
        gap: 12px;
    }
    
    .create-cards-grid {
        grid-template-columns: 1fr;
    }
    
    .list-cards-grid {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 640px) {

    
    .ai-icon-container {
        width: 96px;
        height: 96px;
        padding: 12px;
    }
    
    .ai-icon-background {
        width: 72px;
        height: 72px;
    }
    
    .ai-icon-text {
        font-size: 20px;
    }
}
</style>
