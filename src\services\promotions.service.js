import axios from 'axios';
import { API_URLS } from '../config/api.config';

const normalizeUlid = (ulid) => {
  if (!ulid) return null;
  
  const str = String(ulid).trim();
  
  return str;
};


class PromotionsService {
    #promotionData;
    #endpoint = `${API_URLS.PROMOTION_BASE_URL}`;

    constructor() {
        this.#promotionData = {};
    }

    async getPromotions(filter = '', page = 0, size = 10) {
        try {
            const response = await axios.get(`${this.#endpoint}`, {
                params: { 
                    filter, 
                    page, 
                    size
                }
            });
            
            return {
                content: response.data.content || [],
                totalPages: response.data.totalPages || 0
            };
        } catch (error) {
            console.error('Error fetching promotions:', error);
            throw error;
        }
    }

    async getPromotion(id) {
        try {
            const response = await axios.get(`${this.#endpoint}/${id}`);
            this.#promotionData = response.data;
            return response.data;
        } catch (error) {
            console.error(`Error fetching promotion with id ${id}:`, error);
            throw error;
        }
    }

    async createPromotion(promotionData) {
        try {
            const response = await axios.post(this.#endpoint, promotionData);
            return response.data;
        } catch (error) {
            console.error('Error creating promotion:', error);
            throw error;
        }
    }

    async updatePromotion(id, promotionData) {
        try {
            const response = await axios.put(`${this.#endpoint}/${id}`, promotionData);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    
    async deletePromotion(id) {
        try {
            await axios.delete(`${this.#endpoint}/${id}`);
        } catch (error) {
            throw error;
        }
    }
}

export default new PromotionsService();
