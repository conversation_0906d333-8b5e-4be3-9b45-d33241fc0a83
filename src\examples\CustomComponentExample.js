import { useEditorSystem } from '@/composables/useEditorSystem.js'

/**
 * Exemplo de como registrar um componente personalizado usando o novo sistema automático
 * 
 * Este arquivo demonstra como desenvolvedores podem criar novos componentes
 * sem precisar modificar o EditorCanvas ou sistema de sidebar
 */

const { registerComponentWithEditors, commonEditors, createAutoDetection } = useEditorSystem()

// Exemplo 1: Componente simples com editores padrão
export const registerSimpleButton = () => {
  registerComponentWithEditors({
    type: 'custom-button',
    name: 'Botão Personalizado',
    description: 'Um botão com estilos customizados',
    category: 'interactive',
    html: '<button class="custom-btn" data-element-type="interactive">Clique aqui</button>',
    
    // Editores automáticos baseados nos padrões
    editors: [
      commonEditors.text(),
      commonEditors.font(),
      commonEditors.spacing(),
      commonEditors.border(),
      commonEditors.background(),
      commonEditors.link() // Para configurar a ação do botão
    ]
  })
}

// Exemplo 2: Componente complexo com editores customizados
export const registerAdvancedCarousel = () => {
  registerComponentWithEditors({
    type: 'advanced-carousel',
    name: 'Carrossel Avançado',
    description: 'Carrossel com recursos avançados',
    category: 'media',
    html: `
      <div class="advanced-carousel" data-component="advanced-carousel">
        <div class="carousel-slides">
          <div class="slide active">
            <img src="/placeholder1.jpg" alt="Slide 1">
            <div class="slide-content">
              <h3>Título do Slide</h3>
              <p>Descrição do slide</p>
            </div>
          </div>
        </div>
        <div class="carousel-controls">
          <button class="prev">‹</button>
          <button class="next">›</button>
        </div>
      </div>
    `,
    
    // Editores customizados
    editors: [
      {
        type: 'carousel-slides',
        title: 'Gerenciar Slides',
        path: '@/components/custom/CarouselSlidesEditor.vue',
        priority: 1
      },
      {
        type: 'carousel-effects',
        title: 'Efeitos de Transição',
        path: '@/components/custom/CarouselEffectsEditor.vue',
        priority: 2
      },
      // Editores padrão também funcionam
      commonEditors.spacing(),
      commonEditors.border()
    ],
    
    // Detecção automática
    autoDetection: createAutoDetection({
      isCarousel: '[data-component="advanced-carousel"]',
      hasSlides: (element) => element.querySelector('.carousel-slides'),
      hasControls: (element) => element.querySelector('.carousel-controls')
    })
  })
}

// Exemplo 3: Registrando apenas um editor individual para elementos existentes
export const registerColorPicker = () => {
  const { registerIndividualEditor } = useEditorSystem()
  
  registerIndividualEditor('color-picker', {
    component: 'ColorPickerEditor',
    path: '@/components/custom/ColorPickerEditor.vue',
    category: 'styling',
    
    // Condições para quando este editor deve aparecer
    conditions: [
      '[data-supports-color]',
      '.colorable',
      (element) => element.style.color || element.style.backgroundColor
    ],
    
    // Configurações avançadas
    autoSave: true,
    debounceTime: 200,
    
    // Hooks de ciclo de vida
    beforeOpen: (element) => {

    },
    
    afterClose: (element, data) => {

    }
  })
}

// Exemplo 4: Adicionando editores a componentes existentes
export const extendExistingComponents = () => {
  const { addToolbarActions } = useEditorSystem()
  
  // Adiciona um editor de acessibilidade para todos os elementos de texto
  addToolbarActions('text', [
    {
      type: 'accessibility',
      title: 'Configurações de Acessibilidade',
      icon: '♿',
      component: 'AccessibilityEditor',
      path: '@/components/custom/AccessibilityEditor.vue'
    }
  ])
  
  // Adiciona um editor de SEO para elementos de heading
  addToolbarActions('heading', [
    {
      type: 'seo-optimizer',
      title: 'Otimização SEO',
      icon: '🔍',
      component: 'SeoEditor',
      path: '@/components/custom/SeoEditor.vue'
    }
  ])
}

// Exemplo 5: Sistema completo de componente com múltiplas variações
export const registerProductShowcase = () => {
  registerComponentWithEditors({
    type: 'product-showcase',
    name: 'Vitrine de Produtos',
    description: 'Componente avançado para exibir produtos',
    category: 'ecommerce',
    html: `
      <div class="product-showcase" data-component="product-showcase">
        <header class="showcase-header">
          <h2>Produtos em Destaque</h2>
          <p>Confira nossa seleção especial</p>
        </header>
        <div class="products-grid" data-layout="grid">
          <!-- Produtos serão carregados dinamicamente -->
        </div>
        <footer class="showcase-footer">
          <button class="view-all-btn">Ver Todos os Produtos</button>
        </footer>
      </div>
    `,
    
    editors: [
      {
        type: 'showcase-content',
        title: 'Conteúdo da Vitrine',
        path: '@/components/custom/ShowcaseContentEditor.vue',
        priority: 1
      },
      {
        type: 'product-selection',
        title: 'Seleção de Produtos',
        path: '@/components/custom/ProductSelectionEditor.vue',
        priority: 2
      },
      {
        type: 'showcase-layout',
        title: 'Layout da Vitrine',
        path: '@/components/custom/ShowcaseLayoutEditor.vue',
        priority: 3
      },
      {
        type: 'showcase-style',
        title: 'Estilo da Vitrine',
        path: '@/components/custom/ShowcaseStyleEditor.vue',
        priority: 4
      },
      // Editores padrão
      commonEditors.spacing(),
      commonEditors.background(),
      commonEditors.animation()
    ],
    
    // Detecção automática avançada
    autoDetection: createAutoDetection({
      isProductShowcase: '[data-component="product-showcase"]',
      hasProducts: (element) => element.querySelector('.products-grid'),
      hasHeader: (element) => element.querySelector('.showcase-header'),
      hasFooter: (element) => element.querySelector('.showcase-footer'),
      layoutType: (element) => {
        const grid = element.querySelector('[data-layout]')
        return grid ? grid.getAttribute('data-layout') : 'grid'
      }
    }),
    
    // Ações customizadas adicionais
    customActions: [
      {
        type: 'duplicate-showcase',
        title: 'Duplicar Vitrine',
        icon: '📋',
        action: (element) => {
          // Lógica customizada para duplicar

        }
      }
    ]
  })
}

// Função para registrar todos os exemplos
export const registerAllExamples = () => {
  
  registerSimpleButton()
  registerAdvancedCarousel()
  registerColorPicker()
  extendExistingComponents()
  registerProductShowcase()
  

} 