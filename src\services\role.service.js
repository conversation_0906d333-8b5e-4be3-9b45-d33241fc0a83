import axios from 'axios';
import { API_URLS } from '@/config/api.config';

// Base URL para as APIs de roles
const BASE_URL = `${API_URLS.ROLE_BASE_URL}api/v1/stores`;

export const roleApi = {
  // Get all roles for a store
  async getStoreRoles(storeId, activeOnly = true) {
    const url = `${BASE_URL}/${storeId}/roles`
    return axios.get(url, {
      params: { activeOnly }
    })
  },

  // Create a new role
  async createStoreRole(storeId, roleData) {
    const url = `${BASE_URL}/${storeId}/roles`
    return axios.post(url, roleData)
  },

  // Update an existing role
  async updateStoreRole(storeId, roleId, roleData) {
    const url = `${BASE_URL}/${storeId}/roles/${roleId}`
    return axios.put(url, roleData)
  },

  // Delete a role
  async deleteStoreRole(storeId, roleId) {
    const url = `${BASE_URL}/${storeId}/roles/${roleId}`
      return axios.delete(url)
  },

  // Get available permissions (admin only)
  async getAvailablePermissions(storeId) {
    const url = `${BASE_URL}/${storeId}/roles/permissions`
    return axios.get(url)
  },

  // Get current user permissions
  async getCurrentUserPermissions(storeId) {
    const url = `${BASE_URL}/${storeId}/users/current/permissions`
    return axios.get(url)
  }
}