<template>
  <IluriaModal
    :model-value="true"
    :title="$t('themes.create.title')"
    :dialog-style="{ width: '800px', maxWidth: '90vw' }"
    @update:model-value="handleClose"
  >
    <div class="create-theme-wizard">
      <!-- Step indicator -->
      <div class="step-indicator">
        <div 
          v-for="(step, index) in steps"
          :key="step.id"
          class="step"
          :class="{
            'step--active': currentStep === index,
            'step--completed': index < currentStep
          }"
        >
          <div class="step-number">
            <HugeiconsIcon 
              v-if="index < currentStep"
              :icon="CheckmarkSquare03Icon"
              class="step-icon"
            />
            <span v-else>{{ index + 1 }}</span>
          </div>
          <span class="step-label">{{ $t(step.label) }}</span>
        </div>
      </div>

      <!-- Step content -->
      <div class="step-content">
        <!-- Step 1: Basic Information -->
        <div v-if="currentStep === 0" class="step-panel">
          <h3 class="panel-title">{{ $t('themes.create.step1') }}</h3>
          
          <div class="form-grid">
            <div class="form-group">
              <label class="form-label">{{ $t('themes.themeName') }}</label>
              <IluriaInputText
                v-model="formData.name"
                :placeholder="$t('themes.themeName')"
                required
              />
            </div>
            
            <div class="form-group">
              <label class="form-label">{{ $t('themes.themeDescription') }}</label>
              <textarea
                v-model="formData.description"
                class="form-textarea"
                :placeholder="$t('themes.themeDescription')"
                rows="3"
              ></textarea>
            </div>
            
            <div class="form-group">
              <div class="form-group-header">
                <label class="form-label">{{ $t('themes.categoriesTitle') }}</label>
                <IluriaButton
                  variant="outline"
                  color="primary"
                  size="small"
                  @click="showCategoryModal = true"
                >
                  {{ $t('themes.addCategory') }}
                </IluriaButton>
              </div>
              <div class="categories-input">
                <div class="selected-categories">
                  <span
                    v-for="categoryId in formData.categoryIds"
                    :key="categoryId"
                    class="category-tag"
                  >
                    <div
                      class="category-color"
                      :style="{ backgroundColor: getCategoryById(categoryId)?.color }"
                    ></div>
                    {{ getCategoryById(categoryId)?.name }}
                    <button
                      @click="removeCategory(categoryId)"
                      class="category-remove"
                    >
                      <HugeiconsIcon :icon="Cancel01Icon" />
                    </button>
                  </span>
                </div>

                <div class="available-categories">
                  <button
                    v-for="category in availableCategories"
                    :key="category.id"
                    @click="addCategory(category.id)"
                    class="category-option"
                    :disabled="formData.categoryIds.includes(category.id)"
                  >
                    <div
                      class="category-color"
                      :style="{ backgroundColor: category.color }"
                    ></div>
                    {{ category.name }}
                  </button>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 2: Select Base Theme -->
        <div v-if="currentStep === 1" class="step-panel">
          <h3 class="panel-title">{{ $t('themes.create.step2') }}</h3>

          <!-- Mensagem quando não há temas disponíveis -->
          <div v-if="availableThemes.length === 0" class="no-themes-message">
            <div class="no-themes-icon">
              <HugeiconsIcon :icon="PaintBoardIcon" />
            </div>
            <h4>{{ $t('themes.noThemesAvailable') }}</h4>
            <p>{{ $t('themes.noThemesDescription') }}</p>
          </div>

          <div v-else class="base-themes-grid">
            <div
              v-for="theme in availableThemes"
              :key="theme.id"
              class="base-theme-card"
              :class="{ 'selected': formData.baseThemeId === theme.id }"
              @click="selectBaseTheme(theme.id)"
            >
              <div class="theme-preview-mini">
                <div 
                  class="preview-placeholder"
                  :style="{ backgroundColor: theme.colors?.background || '#f8f9fa' }"
                >
                  <HugeiconsIcon :icon="PaintBoardIcon" class="preview-icon" />
                </div>
              </div>
              
              <div class="theme-info-mini">
                <h4 class="theme-name-mini">{{ theme.name }}</h4>
                <p class="theme-desc-mini">{{ theme.description }}</p>
                
                <div class="theme-colors-mini">
                  <div 
                    v-for="(color, name) in theme.colors"
                    :key="name"
                    class="color-dot"
                    :style="{ backgroundColor: color }"
                    :title="`${name}: ${color}`"
                  ></div>
                </div>
              </div>
              
              <div class="selection-indicator">
                <HugeiconsIcon :icon="CheckmarkSquare02Icon" />
              </div>
            </div>
          </div>
        </div>

        <!-- Step 3: Customize Colors -->
        <div v-if="currentStep === 2" class="step-panel">
          <h3 class="panel-title">{{ $t('themes.create.step3') }}</h3>
          
          <div class="color-customization">
            <div class="color-preview">
              <div class="preview-card" :style="previewStyles">
                <div class="preview-header">
                  <h4>{{ formData.name || 'Novo Tema' }}</h4>
                  <button class="preview-button" :style="buttonStyles">
                    {{ $t('themes.preview') }}
                  </button>
                </div>
                <div class="preview-content">
                  <p>{{ $t('themes.colorPreviewText') }}</p>
                </div>
              </div>
            </div>
            
            <div class="color-controls">
              <div 
                v-for="(color, name) in formData.colors"
                :key="name"
                class="color-control"
              >
                <label class="color-label">
                  {{ $t(`themes.${name}Color`) }}
                </label>
                <div class="color-input-group">
                  <input
                    v-model="formData.colors[name]"
                    type="color"
                    class="color-picker"
                  />
                  <IluriaInputText
                    v-model="formData.colors[name]"
                    class="color-text-input"
                  />
                </div>
              </div>
              
              <div class="color-actions">
                <IluriaButton
                  variant="ghost"
                  @click="resetColors"
                >
                  {{ $t('themes.resetColors') }}
                </IluriaButton>
                
                <IluriaButton
                  variant="ghost"
                  @click="generateRandomColors"
                >
                  {{ $t('themes.randomColors') }}
                </IluriaButton>
              </div>
            </div>
          </div>
        </div>

        <!-- Step 4: Final Settings -->
        <div v-if="currentStep === 3" class="step-panel">
          <h3 class="panel-title">{{ $t('themes.create.step4') }}</h3>
          
          <div class="final-settings">
            <div class="setting-group">
              <label class="form-label">{{ $t('themes.layout') }}</label>
              <div class="radio-group">
                <label class="radio-option">
                  <input
                    v-model="formData.layout"
                    type="radio"
                    value="container"
                  />
                  <span class="radio-label">{{ $t('themes.layoutContainer') }}</span>
                  <span class="radio-desc">{{ $t('themes.layoutContainerDesc') }}</span>
                </label>
                
                <label class="radio-option">
                  <input
                    v-model="formData.layout"
                    type="radio"
                    value="full-width"
                  />
                  <span class="radio-label">{{ $t('themes.layoutFullWidth') }}</span>
                  <span class="radio-desc">{{ $t('themes.layoutFullWidthDesc') }}</span>
                </label>
              </div>
            </div>
            
            <div class="setting-group">
              <label class="checkbox-option">
                <input
                  v-model="formData.setAsCurrent"
                  type="checkbox"
                />
                <span class="checkbox-label">{{ $t('themes.setAsCurrent') }}</span>
                <span class="checkbox-desc">{{ $t('themes.setAsCurrentDesc') }}</span>
              </label>
            </div>
            
            <!-- Theme summary -->
            <div class="theme-summary">
              <h4 class="summary-title">{{ $t('themes.summary') }}</h4>
              <div class="summary-content">
                <div class="summary-item">
                  <span class="summary-label">{{ $t('themes.themeName') }}:</span>
                  <span class="summary-value">{{ formData.name }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">{{ $t('themes.basedOn') }}:</span>
                  <span class="summary-value">{{ getBaseThemeName() }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">{{ $t('themes.layout') }}:</span>
                  <span class="summary-value">{{ $t(`themes.layout${formData.layout === 'container' ? 'Container' : 'FullWidth'}`) }}</span>
                </div>
                <div class="summary-item">
                  <span class="summary-label">{{ $t('themes.tags') }}:</span>
                  <span class="summary-value tags">
                    {{ formData.categoryIds.map(id => getCategoryById(id)?.name).filter(Boolean).join(', ') }}
                  </span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Navigation buttons -->
      <div class="wizard-navigation">
        <IluriaButton
          v-if="currentStep > 0"
          variant="outline"
          @click="previousStep"
        >
          {{ $t('themes.create.previous') }}
        </IluriaButton>
        
        <div class="nav-spacer"></div>
        
        <IluriaButton
          v-if="currentStep < steps.length - 1"
          variant="solid"
          color="primary"
          @click="nextStep"
          :disabled="!canProceed"
        >
          {{ $t('themes.create.next') }}
        </IluriaButton>
        
        <IluriaButton
          v-else
          variant="solid"
          color="primary"
          @click="createTheme"
          :loading="isCreating"
          :disabled="!canCreate"
        >
          {{ $t('themes.create.finish') }}
        </IluriaButton>
      </div>
    </div>
  </IluriaModal>
  <CreateThemeCategoryModal v-model="showCategoryModal" @create="handleCategoryCreate" />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  CheckmarkSquare03Icon,
  Cancel01Icon,
  PaintBoardIcon,
  CheckmarkSquare02Icon
} from '@hugeicons-pro/core-stroke-standard'

import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import CreateThemeCategoryModal from './CreateThemeCategoryModal.vue'
import { useThemeManager } from '@/composables/useThemeManager'

const { t } = useI18n()
const { availableCategories, loadCategories, createThemeCategory } = useThemeManager()

// Props
const props = defineProps({
  availableThemes: {
    type: Array,
    default: () => []
  }
})

// Emits
const emit = defineEmits(['close', 'create'])

// State
const currentStep = ref(0)
const isCreating = ref(false)
const showCategoryModal = ref(false)

const steps = [
  { id: 'basic', label: 'themes.create.step1' },
  { id: 'base', label: 'themes.create.step2' },
  { id: 'colors', label: 'themes.create.step3' },
  { id: 'settings', label: 'themes.create.step4' }
]

const formData = ref({
  name: '',
  description: '',
  categoryIds: [],
  baseThemeId: '',
  colors: {
    primary: '#3B82F6',
    secondary: '#64748B',
    background: '#FFFFFF',
    text: '#1F2937'
  },
  typography: {
    fontFamily: 'system-ui, -apple-system, sans-serif',
    fontSize: '16px',
    fontWeight: '400',
    lineHeight: '1.5'
  },
  spacing: {
    xs: '0.25rem',
    sm: '0.5rem',
    md: '1rem',
    lg: '1.5rem',
    xl: '2rem'
  },
  layout: 'container',
  setAsCurrent: false
})

// Removido availableTags - agora usa categorias do backend

// Computed
const canProceed = computed(() => {
  switch (currentStep.value) {
    case 0:
      return formData.value.name.trim().length > 0
    case 1:
      return formData.value.baseThemeId.length > 0
    case 2:
      return true // Colors are optional
    case 3:
      return true
    default:
      return false
  }
})

const canCreate = computed(() => {
  return formData.value.name.trim().length > 0 && 
         formData.value.baseThemeId.length > 0
})

const previewStyles = computed(() => ({
  backgroundColor: formData.value.colors.background,
  color: formData.value.colors.text,
  border: `1px solid ${formData.value.colors.secondary}`
}))

const buttonStyles = computed(() => ({
  backgroundColor: formData.value.colors.primary,
  color: formData.value.colors.background,
  border: 'none'
}))

// Methods
const nextStep = () => {
  if (currentStep.value < steps.length - 1) {
    currentStep.value++
    console.log(currentStep.value)
  }
}

const previousStep = () => {
  if (currentStep.value > 0) {
    currentStep.value--
    console.log(currentStep.value)
  }
}

const addCategory = (categoryId) => {
  if (!formData.value.categoryIds.includes(categoryId)) {
    formData.value.categoryIds.push(categoryId)
  }
}

const removeCategory = (categoryId) => {
  const index = formData.value.categoryIds.indexOf(categoryId)
  if (index > -1) {
    formData.value.categoryIds.splice(index, 1)
  }
}

const getCategoryById = (categoryId) => {
  return availableCategories.value.find(cat => cat.id === categoryId)
}

const selectBaseTheme = (themeId) => {
  formData.value.baseThemeId = themeId

  // Copy data from base theme
  const baseTheme = props.availableThemes.find(t => t.id === themeId)
  if (baseTheme) {
    // Se for um tema padrão, sugerir nome personalizado
    if (baseTheme.isDefault) {
      formData.value.name = `${baseTheme.name} - Personalizado`
      formData.value.description = `Baseado no tema ${baseTheme.name}`
    }

    // Copiar configurações do tema base
    if (baseTheme.cssVariables) {
      formData.value.colors = { ...baseTheme.cssVariables }
    }
    
    // Inicializar typography e spacing se não existirem
    if (!formData.value.typography) {
      formData.value.typography = {}
    }
    if (!formData.value.spacing) {
      formData.value.spacing = {}
    }
    
    // Copiar typography e spacing do tema base
    if (baseTheme.typography) {
      formData.value.typography = { ...formData.value.typography, ...baseTheme.typography }
    }
    if (baseTheme.spacing) {
      formData.value.spacing = { ...formData.value.spacing, ...baseTheme.spacing }
    }
    if (baseTheme.categoryIds) {
      formData.value.categoryIds = [...baseTheme.categoryIds]
    }
  }
}

const getBaseThemeName = () => {
  const baseTheme = props.availableThemes.find(t => t.id === formData.value.baseThemeId)
  return baseTheme ? baseTheme.name : ''
}

const resetColors = () => {
  const baseTheme = props.availableThemes.find(t => t.id === formData.value.baseThemeId)
  if (baseTheme) {
    if (baseTheme.colors || baseTheme.cssVariables) {
      formData.value.colors = { ...baseTheme.colors || baseTheme.cssVariables }
    }
    if (baseTheme.typography) {
      formData.value.typography = { ...baseTheme.typography }
    }
    if (baseTheme.spacing) {
      formData.value.spacing = { ...baseTheme.spacing }
    }
  }
}

const generateRandomColors = () => {
  const colors = [
    '#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6',
    '#06B6D4', '#84CC16', '#F97316', '#EC4899', '#6366F1'
  ]
  
  formData.value.colors.primary = colors[Math.floor(Math.random() * colors.length)]
  formData.value.colors.secondary = colors[Math.floor(Math.random() * colors.length)]
}

const createTheme = async () => {
  if (!canCreate.value) return
  
  isCreating.value = true
  
  try {
    await new Promise(resolve => setTimeout(resolve, 1500)) // Simulate API call
    
    emit('create', formData.value)
  } catch (error) {
    console.error('Error creating theme:', error)
  } finally {
    isCreating.value = false
  }
}

const handleClose = () => {
  emit('close')
}

const handleCategoryCreate = async (categoryData) => {
  try{
    const newCategory =  await createThemeCategory(categoryData);
    await loadCategories();
    showCategoryModal.value = false;
  } catch (error){
    throw error;
  }
}

// Lifecycle
onMounted(async () => {
  // Carregar categorias
  await loadCategories()

  // Auto-select first theme if available
  if (props.availableThemes.length > 0) {
    selectBaseTheme(props.availableThemes[0].id)
  }
})
</script>

<style scoped>
.create-theme-wizard {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-height: 70vh;
  overflow-y: auto;
}

/* Step Indicator */
.step-indicator {
  display: flex;
  justify-content: center;
  gap: 2rem;
  padding: 1rem 0;
  border-bottom: 1px solid var(--iluria-color-border);
}

.step {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  opacity: 0.5;
  transition: all 0.3s ease;
}

.step--active,
.step--completed {
  opacity: 1;
}

.step-number {
  width: 2.5rem;
  height: 2.5rem;
  border-radius: 50%;
  background: var(--iluria-color-background);
  border: 2px solid var(--iluria-color-border);
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  transition: all 0.3s ease;
}

.step--active .step-number {
  background: var(--iluria-color-primary);
  border-color: var(--iluria-color-primary);
  color: white;
}

.step--completed .step-number {
  background: var(--iluria-color-success);
  border-color: var(--iluria-color-success);
  color: white;
}

.step-icon {
  width: 1.25rem;
  height: 1.25rem;
}

.step-label {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  text-align: center;
}

.step--active .step-label,
.step--completed .step-label {
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

/* Step Content */
.step-content {
  flex: 1;
  overflow-y: auto;
  padding: 1rem 0;
}

.step-panel {
  max-width: 600px;
  margin: 0 auto;
}

.panel-title {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 2rem;
  text-align: center;
}

/* Form Elements */
.form-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.form-textarea {
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  font-family: inherit;
  resize: vertical;
  min-height: 100px;
}

/* Tags Input */
.tags-input {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selected-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-height: 2rem;
}

.tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--iluria-color-primary);
  color: white;
  border-radius: 20px;
  font-size: 0.75rem;
}

.tag-remove {
  background: none;
  border: none;
  color: white;
  cursor: pointer;
  padding: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.available-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag-option {
  padding: 0.25rem 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.tag-option:hover:not(:disabled) {
  background: var(--iluria-color-primary);
  color: white;
}

.tag-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Categories Input */
.categories-input {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.selected-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
  min-height: 2rem;
}

.category-tag {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--iluria-color-primary-light);
  border: 1px solid var(--iluria-color-primary);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--iluria-color-primary);
}

.category-color {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(0,0,0,0.1);
}

.category-remove {
  background: none;
  border: none;
  cursor: pointer;
  padding: 0;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.available-categories {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.category-option {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
}

.category-option:hover:not(:disabled) {
  background: var(--iluria-color-primary);
  color: white;
}

.category-option:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* No Themes Message */
.no-themes-message {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  background: var(--iluria-color-background);
  border: 2px dashed var(--iluria-color-border);
  border-radius: 12px;
  margin: 1rem 0;
}

.no-themes-icon {
  width: 4rem;
  height: 4rem;
  background: var(--iluria-color-primary-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 1.5rem;
}

.no-themes-icon svg {
  width: 2rem;
  height: 2rem;
  color: var(--iluria-color-primary);
}

.no-themes-message h4 {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.5rem 0;
}

.no-themes-message p {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 2rem 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Base Themes Grid */
.base-themes-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 1rem;
}

.base-theme-card {
  border: 2px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.base-theme-card:hover {
  border-color: var(--iluria-color-primary);
}

.base-theme-card.selected {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-light);
}

.theme-preview-mini {
  aspect-ratio: 16/10;
  border-radius: 4px;
  overflow: hidden;
  margin-bottom: 1rem;
}

.preview-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.preview-icon {
  width: 2rem;
  height: 2rem;
  opacity: 0.3;
}

.theme-info-mini {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.theme-name-mini {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.theme-desc-mini {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.3;
}

.theme-colors-mini {
  display: flex;
  gap: 0.25rem;
  margin-top: 0.5rem;
}

.color-dot {
  width: 1rem;
  height: 1rem;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.selection-indicator {
  position: absolute;
  top: 0.5rem;
  right: 0.5rem;
  color: var(--iluria-color-primary);
  opacity: 0;
  transition: opacity 0.3s ease;
}

.base-theme-card.selected .selection-indicator {
  opacity: 1;
}

/* Color Customization */
.color-customization {
  display: grid;
  grid-template-columns: 1fr 300px;
  gap: 2rem;
}

.color-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f8f9fa;
  border-radius: 8px;
  padding: 2rem;
}

.preview-card {
  width: 100%;
  max-width: 300px;
  padding: 1.5rem;
  border-radius: 8px;
  transition: all 0.3s ease;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

.preview-button {
  padding: 0.5rem 1rem;
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.color-controls {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.color-control {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.color-label {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.color-input-group {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

.color-picker {
  width: 3rem;
  height: 2.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  cursor: pointer;
}

.color-text-input {
  flex: 1;
}

.color-actions {
  display: flex;
  gap: 0.5rem;
  margin-top: 1rem;
}

/* Final Settings */
.final-settings {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.setting-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.radio-option {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.radio-option:hover {
  border-color: var(--iluria-color-primary);
}

.radio-label {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.radio-desc {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

.checkbox-option {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  padding: 1rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
}

.checkbox-label {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.checkbox-desc {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

/* Theme Summary */
.theme-summary {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 1.5rem;
}

.summary-title {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 1rem;
}

.summary-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.summary-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.summary-label {
  color: var(--iluria-color-text-secondary);
}

.summary-value {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.tags{
  display: flex;
  flex-direction: row;
  flex-wrap: wrap;
  gap: 0.5rem;
}

/* Navigation */
.wizard-navigation {
  display: flex;
  align-items: center;
  padding-top: 2rem;
  border-top: 1px solid var(--iluria-color-border);
}

.nav-spacer {
  flex: 1;
}

.form-group-header{
  display: flex;
  justify-content: space-between;
  align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
  .step-indicator {
    gap: 1rem;
  }
  
  .step-label {
    font-size: 0.75rem;
  }
  
  .color-customization {
    grid-template-columns: 1fr;
  }
  
  .wizard-navigation {
    flex-direction: column;
    gap: 1rem;
  }
}
</style>