<script setup>
import { defineProps, defineEmits, ref, watch, reactive } from 'vue'
import FloatingWindow from './Common/FloatingWindow.vue'
import { 
  TextIcon, 
  Image01Icon, 
  ShoppingBag01Icon, 
  Touch02Icon,
  GridViewIcon,
  Add01Icon,
  ArrowDown01Icon,
  ArrowUp01Icon,
  Layout01Icon,

} from '@hugeicons-pro/core-bulk-rounded'

const props = defineProps({
  isOpen: {
    type: Boolean,
    required: true
  },
  position: {
    type: Object,
    required: true
  },
  categories: {
    type: Object,
    required: true
  },
  insertPosition: {
    type: String,
    default: 'below'
  },
  selectedElement: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['select', 'toggle-position', 'close'])

const togglePosition = () => {
  emit('toggle-position')
}

// Estado inicial: apenas a primeira categoria aberta
const expandedState = ref({})

// Inicializa apenas a primeira categoria como aberta
const initializeExpandedState = () => {
  const categoryKeys = Object.keys(props.categories || {})
  const newState = {}
  
  categoryKeys.forEach((key, index) => {
    newState[key] = index === 0 // Apenas a primeira categoria aberta
  })
  
  expandedState.value = newState
}

// Inicializa quando as categorias estão disponíveis
watch(() => props.categories, () => {
  if (props.categories && Object.keys(props.categories).length > 0) {
    initializeExpandedState()
  }
}, { immediate: true })

const toggleCategory = (categoryId) => {

  
  // Comportamento accordion: se clicou numa categoria já aberta, fecha ela
  // Se clicou numa fechada, fecha todas e abre apenas a clicada
  const currentState = expandedState.value[categoryId]
  
  if (currentState) {
    // Se está aberta, apenas fecha ela
    expandedState.value[categoryId] = false

  } else {
    // Se está fechada, fecha todas e abre apenas esta
    const newState = {}
    Object.keys(expandedState.value).forEach(key => {
      newState[key] = key === categoryId
    })
    expandedState.value = newState
    
  }
}

const isExpanded = (categoryId) => {
  return expandedState.value[categoryId] || false
}

// Observa mudanças no elemento selecionado para fechar o menu quando necessário
watch(() => props.selectedElement, (newElement) => {
  if (!newElement) {
    emit('close')
  }
}, { deep: true })

// Função para obter o componente de ícone da categoria
const getCategoryIcon = (categoryId) => {
  const icons = {
    text: TextIcon,
    media: Image01Icon,
    products: ShoppingBag01Icon,
    interactive: Touch02Icon,
    layout: GridViewIcon,
    content: Layout01Icon,
    custom: Add01Icon
  }
  return icons[categoryId] || Layout01Icon
}

// Função para obter o ícone do componente
const getComponentIcon = (componentId) => {
  const icons = {
 
  }
  return icons[componentId] || Layout01Icon
}

// Função para obter a cor da categoria
const getCategoryColor = (categoryId) => {
  const colors = {
    text: '#3b82f6',
    media: '#10b981',
    products: '#f59e0b',
    interactive: '#8b5cf6',
    content: '#0ea5e9',
    layout: '#64748b'
  }
  return colors[categoryId] || '#64748b'
}

const startSlideDown = (el) => {
  const height = el.scrollHeight
  el.style.height = '0'
  el.style.opacity = '0'
  el.style.overflow = 'hidden'
  
  requestAnimationFrame(() => {
    el.style.height = `${height}px`
    el.style.opacity = '1'
    el.style.transition = 'height 0.3s ease-out, opacity 0.2s ease-in'
  })

  const onTransitionEnd = () => {
    el.style.height = 'auto'
    el.style.overflow = 'visible'
    el.removeEventListener('transitionend', onTransitionEnd)
  }
  
  el.addEventListener('transitionend', onTransitionEnd)
}

const startSlideUp = (el) => {
  const height = el.scrollHeight
  el.style.height = `${height}px`
  el.style.overflow = 'hidden'
  
  requestAnimationFrame(() => {
    el.style.height = '0'
    el.style.opacity = '0'
    el.style.transition = 'height 0.3s ease-in, opacity 0.2s ease-out'
  })
}
</script>

<template>
  <FloatingWindow
    :title="$t('layoutEditor.addComponent')"
    :width="420"
    :min-height="350"
    :position="position"
    :visible="isOpen"
    @close="$emit('close')"
    draggable
  >
    <!-- Botão de posição -->
      <div class="position-control">
        <button 
          class="position-toggle"
          @click="$emit('toggle-position')"
          :title="insertPosition === 'below' ? 
            $t('layoutEditor.insertBelow') : 
            $t('layoutEditor.insertAbove')"
        >
          <span v-if="insertPosition === 'below'" class="icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 4L12 20M12 20L18 14M12 20L6 14" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          <span v-else class="icon">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
              <path d="M12 20L12 4M12 4L18 10M12 4L6 10" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </span>
          {{ insertPosition === 'below' ? 
            $t('layoutEditor.below') : 
            $t('layoutEditor.above') }}
        </button>
      </div>

      <!-- Lista de categorias -->
      <div class="categories-list">
        <div 
          v-for="(category, id) in categories" 
          :key="id"
          class="category"
        >
          <button 
            class="category-header"
            @click.stop="toggleCategory(id)"
            :aria-expanded="isExpanded(id)"
          >
            <component 
              :is="getCategoryIcon(id)"
              class="category-icon"
              :style="{ color: getCategoryColor(id) }"
            />
            <span class="category-title">{{ category.title }}</span>
            <span class="expand-icon" :class="{ expanded: isExpanded(id) }">
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M6 9L12 15L18 9" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </span>
          </button>

          <Transition name="slide-fade">
            <div 
              v-if="isExpanded(id)"
              class="category-content"
            >
              <div 
                v-for="(component, componentId) in category.components" 
                :key="componentId"
                class="component-item"
                @click.stop="$emit('select', componentId)"
              >
                <div class="component-info">
                  <div class="component-icon">
                    <component :is="getComponentIcon(componentId)" />
                  </div>
                  <div class="component-details">
                    <h4>{{ component.title }}</h4>
                    <p>{{ component.description }}</p>
                  </div>
                </div>
              </div>
            </div>
          </Transition>
      </div>
    </div>
  </FloatingWindow>
</template>

<style scoped>
/* Controle de posição */
.position-control {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.position-toggle {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem 1rem;
  background: var(--iluria-color-button-secondary-bg);
  border: 1px solid var(--iluria-color-button-secondary-border);
  border-radius: 8px;
  color: var(--iluria-color-button-secondary-text);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.position-toggle:hover {
  background: var(--iluria-color-button-secondary-bg-hover);
  border-color: var(--iluria-color-button-secondary-border-hover);
  color: var(--iluria-color-button-secondary-text-hover);
}

.position-toggle .icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 16px;
  height: 16px;
  color: currentColor;
}

/* Lista de categorias */
.categories-list {
  overflow-y: auto;
  max-height: calc(60vh - 120px);
  padding: 0.5rem 0;
  background: var(--iluria-color-container-bg);
}

.categories-list::-webkit-scrollbar {
  width: 6px;
}

.categories-list::-webkit-scrollbar-track {
  background: var(--iluria-color-container-bg);
}

.categories-list::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border-hover);
  border-radius: 3px;
}

.categories-list::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-muted);
}

/* Categoria */
.category {
  margin: 0;
}

.category-header {
  display: flex;
  align-items: center;
  width: 100%;
  padding: 0.875rem 1rem;
  background: transparent;
  border: none;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 0.875rem;
  font-weight: 600;
  text-align: left;
  gap: 0.75rem;
  user-select: none;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.category-header:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

.category-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  opacity: 0.9;
}

.category-title {
  flex: 1;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.expand-icon {
  width: 18px;
  height: 18px;
  color: var(--iluria-color-text-secondary);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Conteúdo da categoria */
.category-content {
  overflow: hidden;
  background: var(--iluria-color-background);
  border-top: 1px solid var(--iluria-color-border);
  border-bottom: 1px solid var(--iluria-color-border);
  margin: 0 0.5rem;
  border-radius: 8px;
}

/* Item do componente */
.component-item {
  display: flex;
  align-items: center;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-background);
}

.component-item:last-child {
  border-bottom: none;
}

.component-item:hover {
  background: var(--iluria-color-hover);
  transform: translateX(2px);
}

.component-item:active {
  transform: translateX(1px);
  background: var(--iluria-color-7);
}

.component-info {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  width: 100%;
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-primary);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.component-item:hover .component-icon {
  background: var(--iluria-color-primary-bg);
  border-color: var(--iluria-color-primary-border);
  color: var(--iluria-color-primary);
}

.component-details {
  flex: 1;
  min-width: 0;
}

.component-details h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
}

.component-details p {
  margin: 0;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  }

/* Animações */
.slide-fade-enter-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

.slide-fade-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
}

.slide-fade-enter-from,
.slide-fade-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-8px);
}

.slide-fade-enter-to,
.slide-fade-leave-from {
  opacity: 1;
  max-height: 600px;
  transform: translateY(0);
}

/* Responsividade */
@media (max-width: 768px) {
  .position-control {
    padding: 0.75rem;
  }
  
  .position-toggle {
    padding: 0.625rem 0.875rem;
    font-size: 0.8rem;
  }
  
  .category-header {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  
  .component-item {
    padding: 0.875rem 0.75rem;
  }
  
  .component-icon {
    width: 28px;
    height: 28px;
  }
  
  .component-details h4 {
    font-size: 0.8rem;
  }
  
  .component-details p {
    font-size: 0.7rem;
  }
}
</style>
