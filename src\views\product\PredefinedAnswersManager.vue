<template>
  <div class="predefined-answers-container">
    <!-- Header -->
    <IluriaHeader
      title="Respostas Predefinidas"
      subtitle="Gerencie suas respostas predefinidas para agilizar o atendimento"
      :showAdd="true"
      addText="Adicionar Resposta"
      :customButtons="[{
        text: 'Voltar às Perguntas',
        color: 'secondary',
        variant: 'outline',
        icon: ArrowLeftIcon,
        onClick: goBack
      }]"
      @add-click="goToCreate"
    />

    <!-- Search and Filters -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <div class="search-icon">
          <svg class="search-icon-svg" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z" clip-rule="evenodd" />
          </svg>
        </div>
        <input
          v-model="searchTerm"
          type="text"
          placeholder="Buscar por título ou conteúdo..."
          class="search-input"
        />
      </div>
      <div class="results-counter">
        {{ getTotalFilteredCount() }} respostas
      </div>
    </div>

    <!-- Table -->
    <div class="table-wrapper">
      <IluriaDataTable 
        :value="filteredAnswers" 
        :columns="columns" 
        :loading="loading"
        class="predefined-answers-table"
      >
        <template #column-title="{ data }">
          <div class="title-cell">
            <div class="answer-title">{{ data.title }}</div>
          </div>
        </template>

        <template #column-content="{ data }">
          <div class="content-cell">
            <p class="answer-content" :title="stripHtml(data.answerText)">
              {{ truncateText(stripHtml(data.answerText), 100) }}
            </p>
          </div>
        </template>

        <template #column-usage="{ data }">
          <div class="usage-cell">
            <span class="usage-badge" :class="getUsageBadgeClass(data.usageCount)">
              {{ data.usageCount }}
            </span>
          </div>
        </template>

        <template #column-date="{ data }">
          <div class="date-cell">
            <div class="date-created">Criado: {{ formatDate(data.createdAt) }}</div>
            <div v-if="data.updatedAt && data.updatedAt !== data.createdAt" class="date-updated">
              Editado: {{ formatDate(data.updatedAt) }}
            </div>
          </div>
        </template>

        <template #column-actions="{ data }">
          <div class="actions-cell">
            <IluriaButton
              @click="goToEdit(data.id)"
              size="small"
              color="primary"
              :hugeIcon="PencilEdit01Icon"
              title="Editar resposta"
            >
              Editar
            </IluriaButton>
            
            <IluriaButton
              @click="confirmDelete(data)"
              size="small"
              color="danger"
              :hugeIcon="Delete01Icon"
              title="Excluir resposta"
            >
              Excluir
            </IluriaButton>
          </div>
        </template>
      </IluriaDataTable>
    </div>

    <!-- Pagination -->
    <div class="pagination-container" v-if="totalPages >= 1">
      <IluriaPagination 
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />
    </div>
  </div>

  <!-- Modal de Confirmação de Exclusão -->
  <IluriaConfirmationModal
    :is-visible="showDeleteModal"
    :title="'Excluir Resposta Predefinida'"
    :message="`Tem certeza que deseja excluir a resposta '${selectedAnswer?.title}'? Esta ação não pode ser desfeita.`"
    confirm-text="Excluir"
    cancel-text="Cancelar"
    type="error"
    @confirm="deleteAnswer"
    @cancel="showDeleteModal = false"
  />
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { useRouter } from 'vue-router';
import { useToast } from '@/services/toast.service';
import questionsApi from '@/services/question.service';
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import IluriaPagination from '@/components/iluria/IluriaPagination.vue';
import { useTheme } from '@/composables/useTheme';
import {
  ArrowLeftIcon,
  DocumentAttachmentIcon,
  PlusSignIcon,
  PencilEdit01Icon,
  Delete01Icon
} from '@hugeicons-pro/core-stroke-rounded';

const router = useRouter();
const toast = useToast();

// Initialize theme system
const { initTheme } = useTheme();
initTheme();

// Estado
const predefinedAnswers = ref([]);
const loading = ref(false);
const searchTerm = ref('');
const currentPage = ref(0);
const totalPages = ref(1);
const itemsPerPage = ref(10);

// Modais
const showDeleteModal = ref(false);
const selectedAnswer = ref(null);

// Colunas da tabela
const columns = ref([
  { field: 'title', header: 'Título' },
  { field: 'content', header: 'Conteúdo' },
  { field: 'usage', header: 'Usos' },
  { field: 'date', header: 'Data' },
  { field: 'actions', header: 'Ações' },
]);

// Computed
const filteredAnswers = computed(() => {
  let filtered = predefinedAnswers.value;
  
  if (searchTerm.value) {
    const term = searchTerm.value.toLowerCase();
    filtered = predefinedAnswers.value.filter(answer => 
      answer.title.toLowerCase().includes(term) ||
      stripHtml(answer.answerText).toLowerCase().includes(term)
    );
  }
  
  // Calculate total pages based on filtered items
  totalPages.value = Math.ceil(filtered.length / itemsPerPage.value);
  
  // Ensure at least 1 page when we have data structure
  if (totalPages.value === 0 && predefinedAnswers.value !== null) {
    totalPages.value = 1;
  }
  
  // Return paginated results
  const start = currentPage.value * itemsPerPage.value;
  const end = start + itemsPerPage.value;
  return filtered.slice(start, end);
});

// Métodos
const fetchPredefinedAnswers = async () => {
  loading.value = true;
  try {
    const { data } = await questionsApi.getPredefinedAnswers();
    predefinedAnswers.value = data || [];
    
    // Reset to first page and calculate total pages
    currentPage.value = 0;
    totalPages.value = Math.ceil((data || []).length / itemsPerPage.value);
  } catch (error) {
    console.error('Erro ao carregar respostas predefinidas:', error);
    predefinedAnswers.value = [];
    // Don't reset totalPages to 0, keep it at 1 to show pagination
    if (error.response?.status !== 404) {
      toast.showError('Erro ao carregar respostas predefinidas');
    }
  } finally {
    loading.value = false;
  }
};

const stripHtml = (html) => {
  const tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || "";
};

const truncateText = (text, maxLength) => {
  if (text.length <= maxLength) return text;
  return text.substring(0, maxLength) + '...';
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const getUsageBadgeClass = (count) => {
  if (count === 0) return 'bg-gray-100 text-gray-600';
  if (count < 5) return 'bg-blue-100 text-blue-600';
  if (count < 10) return 'bg-green-100 text-green-600';
  return 'bg-purple-100 text-purple-600';
};

const goToCreate = () => {
  router.push({ name: 'predefined-answer-create' });
};

const goToEdit = (answerId) => {
  router.push({ name: 'predefined-answer-edit', params: { id: answerId } });
};

const confirmDelete = (answer) => {
  selectedAnswer.value = answer;
  showDeleteModal.value = true;
};

const deleteAnswer = async () => {
  try {
    await questionsApi.deletePredefinedAnswer(selectedAnswer.value.id);
    toast.showSuccess('Resposta predefinida excluída com sucesso!');
    showDeleteModal.value = false;
    selectedAnswer.value = null;
    fetchPredefinedAnswers();
  } catch (error) {
    console.error('Erro ao excluir resposta:', error);
    toast.showError('Erro ao excluir resposta predefinida');
  }
};

const goBack = () => {
  router.push({ name: 'questions-answers' });
};

const changePage = (page) => {
  currentPage.value = page;
};

const getTotalFilteredCount = () => {
  if (!searchTerm.value) {
    return predefinedAnswers.value.length;
  }
  
  const term = searchTerm.value.toLowerCase();
  return predefinedAnswers.value.filter(answer => 
    answer.title.toLowerCase().includes(term) ||
    stripHtml(answer.answerText).toLowerCase().includes(term)
  ).length;
};

// Lifecycle
onMounted(() => {
  fetchPredefinedAnswers();
});

// Watch for search changes to reset pagination
watch(searchTerm, () => {
  currentPage.value = 0;
}, { deep: true });
</script>

<style scoped>
.predefined-answers-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
}



/* Search Container */
.search-container {
  background: var(--iluria-color-surface);
  border-radius: 8px;
  padding: 20px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
  margin-bottom: 24px;
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  min-width: 300px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 1;
  pointer-events: none;
  color: var(--iluria-color-text-muted);
}

.search-icon-svg {
  width: 16px;
  height: 16px;
}

.search-input {
  width: 100%;
  padding: 12px 12px 12px 40px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  font-size: 14px;
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text);
  transition: all 0.3s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring);
}

.search-input::placeholder {
  color: var(--iluria-color-text-muted);
}

.results-counter {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  white-space: nowrap;
  font-weight: 500;
}

/* Table */
.table-wrapper {
  margin-bottom: 24px;
}

:deep(.predefined-answers-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  transition: all 0.3s ease;
}

:deep(.predefined-answers-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px 24px;
  transition: all 0.3s ease;
}

:deep(.predefined-answers-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
  transition: background-color 0.2s ease;
}

:deep(.predefined-answers-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.predefined-answers-table .p-datatable-tbody > tr > td) {
  padding: 16px 24px;
  border: none;
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  transition: all 0.3s ease;
}

/* Table Cells */
.title-cell {
  max-width: 200px;
}

.answer-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.content-cell {
  max-width: 300px;
}

.answer-content {
  font-size: 14px;
  color: var(--iluria-color-text);
  line-height: 1.4;
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.usage-cell {
  display: flex;
  justify-content: center;
}

.usage-badge {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 4px 8px;
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  min-width: 24px;
  text-align: center;
}

.usage-badge.bg-gray-100 {
  background: rgba(107, 114, 128, 0.1);
  color: #6b7280;
}

.usage-badge.bg-blue-100 {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
}

.usage-badge.bg-green-100 {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
}

.usage-badge.bg-purple-100 {
  background: rgba(147, 51, 234, 0.1);
  color: #7c3aed;
}

.date-cell {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.date-created,
.date-updated {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.3;
}

.date-updated {
  color: var(--iluria-color-text-muted);
}

.actions-cell {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
}

/* Responsive */
@media (max-width: 1024px) {

  
  .search-container {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .search-input-wrapper {
    min-width: unset;
    flex: none;
  }
  
  .results-counter {
    text-align: center;
  }
}

@media (max-width: 768px) {
  .predefined-answers-container {
    padding: 16px;
  }
  

  
  .search-input-wrapper {
    width: 100%;
  }
  
  .actions-cell {
    flex-direction: column;
    gap: 4px;
  }
  
  .title-cell,
  .content-cell {
    max-width: unset;
  }
  
  .answer-title,
  .answer-content {
    white-space: normal;
    overflow: visible;
    text-overflow: unset;
    word-wrap: break-word;
  }
}

@media (max-width: 480px) {

  
  .date-cell {
    gap: 1px;
  }
  
  .date-created,
  .date-updated {
    font-size: 12px;
  }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}
</style> 
