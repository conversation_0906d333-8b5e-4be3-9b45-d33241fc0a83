<template>
  <div class="sidebar-editor">
    <!-- Loading State -->
    <div v-if="isLoading" class="loading-state">
      <div class="spinner"></div>
      <p>{{ $t('layoutEditor.loadingEditor') }}</p>
    </div>

    <!-- Error State -->
    <div v-else-if="hasError" class="error-state">
      <div class="error-icon">
        <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="10"/>
          <line x1="15" y1="9" x2="9" y2="15"/>
          <line x1="9" y1="9" x2="15" y2="15"/>
        </svg>
      </div>
      <h4>{{ $t('layoutEditor.editorError') }}</h4>
      <p>{{ errorMessage }}</p>
      <button @click="retryLoad" class="retry-button">
        {{ $t('layoutEditor.tryAgain') }}
      </button>
    </div>

    <!-- Editor Content -->
    <div v-else-if="selectedElement" class="editor-content">
      <!-- Editor dinâmico -->
      <div class="editor-container">

        <component 
          v-if="currentEditorComponent"
          :is="currentEditorComponent"
          :key="`editor-${currentEditorType}-${editorKey}`"
          :element="selectedElement"
          :editor-type="currentEditorType"
          @element-updated="handleElementUpdated"
          @data-changed="handleElementUpdated"
          @close="handleClose"
        />
        
        <!-- Fallback para editor básico -->
        <div v-else class="basic-editor">
          <h4>{{ $t('layoutEditor.basicEditor') }}</h4>
          
          <div class="basic-controls">
            <!-- Espaçamento -->
            <div class="control-group">
              <label>{{ $t('layoutEditor.spacing') }}</label>
              <div class="spacing-controls">
                <button @click="editSpacing" class="control-button">
                  <Settings01Icon />
                  {{ $t('layoutEditor.editSpacing') }}
                </button>
              </div>
            </div>
            
            <!-- Bordas -->
            <div class="control-group">
              <label>{{ $t('layoutEditor.border') }}</label>
              <div class="border-controls">
                <button @click="editBorder" class="control-button">
                  <BorderTopIcon />
                  {{ $t('layoutEditor.editBorder') }}
                </button>
              </div>
            </div>
            
            <!-- Animação -->
            <div class="control-group">
              <label>{{ $t('layoutEditor.animation') }}</label>
              <div class="animation-controls">
                <button @click="editAnimation" class="control-button">
                  <Motion01Icon />
                  {{ $t('layoutEditor.editAnimation') }}
                </button>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- No Element Selected -->
    <div v-else class="no-selection">
      <div class="no-selection-icon">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6M12 17v6M4.22 4.22l4.24 4.24M15.54 15.54l4.24 4.24M1 12h6M17 12h6M4.22 19.78l4.24-4.24M15.54 8.46l4.24-4.24"/>
        </svg>
      </div>
      <h4>{{ $t('layoutEditor.selectElement') }}</h4>
      <p>{{ $t('layoutEditor.selectElementDesc') }}</p>
      <button @click="handleBack" class="back-to-home-button">
        {{ $t('layoutEditor.backToComponents') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { computed, watch, ref, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useEditorRegistry } from '@/components/layoutEditor/registry/EditorRegistry.js'
import { 
  Settings01Icon, 
  BorderTopIcon, 
  Motion01Icon
} from '@hugeicons-pro/core-bulk-rounded'

const { t } = useI18n()
const { getEditor, loadEditor: loadEditorFromRegistry } = useEditorRegistry()

const props = defineProps({
  selectedElement: {
    type: Object,
    default: null
  },
  editorType: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['element-updated', 'back'])

// Estado interno
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const currentEditorComponent = ref(null)
const currentEditorType = ref(null)
const editorKey = ref(0)

// Remover dados de elemento não utilizados

// Carregar editor
const loadEditor = async () => {
  if (!props.selectedElement && !props.editorType) {
    currentEditorComponent.value = null
    return
  }
  
  isLoading.value = true
  hasError.value = false
  
  try {
    let editorType = props.editorType
    
    // Se não foi passado tipo específico, detectar do elemento
    if (!editorType && props.selectedElement) {
      editorType = detectEditorType(props.selectedElement)
    }
    
    
    if (editorType) {
      const editorComponent = await loadEditorFromRegistry(editorType)
      currentEditorComponent.value = editorComponent
      currentEditorType.value = editorType
      editorKey.value++
      
    } else {
      // Fallback para editor básico
      currentEditorComponent.value = null
      currentEditorType.value = null
      editorKey.value++
      
    }
    
  } catch (error) {

    hasError.value = true
    errorMessage.value = error.message || t('layoutEditor.editorLoadError')
    currentEditorComponent.value = null
  } finally {
    isLoading.value = false
  }
}

// Detectar tipo de editor
const detectEditorType = (element) => {
  if (!element) return null
  
  // Verificar data-component primeiro
  const dataComponent = element.getAttribute('data-component')
  if (dataComponent) {
    return `${dataComponent}-config`
  }
  
  // Verificar data-element-type
  const elementType = element.getAttribute('data-element-type')
  if (elementType) {
    return `${elementType}-config`
  }
  
  // Fallback baseado na tag
  const tagName = element.tagName.toLowerCase()
  const typeMap = {
    'img': 'image',
    'a': 'link'
  }
  
  return typeMap[tagName] || 'spacing'
}

// Watchers
watch([() => props.selectedElement, () => props.editorType], () => {
  loadEditor()
}, { immediate: true })

// Handlers
const handleElementUpdated = (data) => {
  
  emit('element-updated', data)
}

const handleClose = () => {
  
  emit('back')
}

const handleBack = () => {
  
  emit('back')
}

const retryLoad = () => {
  hasError.value = false
  loadEditor()
}

// Editores básicos
const editSpacing = () => {
  loadSpecificEditor('spacing')
}

const editBorder = () => {
  loadSpecificEditor('border')
}

const editAnimation = () => {
  loadSpecificEditor('animation')
}

const loadSpecificEditor = async (editorType) => {
  try {
    isLoading.value = true
    const editorComponent = await loadEditorFromRegistry(editorType)
    currentEditorComponent.value = editorComponent
    currentEditorType.value = editorType
    editorKey.value++
        
  } catch (error) {
    hasError.value = true
    errorMessage.value = error.message || t('layoutEditor.editorLoadError')
  } finally {
    isLoading.value = false
  }
}
</script>

<style scoped>
.sidebar-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--iluria-color-surface);
}

/* Estados de carregamento */
.loading-state,
.error-state,
.no-selection {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  flex: 1;
  color: var(--iluria-color-text-secondary);
}

.spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.error-icon,
.no-selection-icon {
  margin-bottom: 1rem;
  opacity: 0.5;
}

.error-state h4,
.no-selection h4 {
  margin: 0 0 0.5rem 0;
  color: var(--iluria-color-text-primary);
}

.error-state p,
.no-selection p {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
}

.retry-button,
.back-to-home-button {
  padding: 0.5rem 1rem;
  background: var(--iluria-color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.retry-button:hover,
.back-to-home-button:hover {
  background: var(--iluria-color-primary-hover);
}

/* Conteúdo do editor */
.editor-content {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Container do editor */
.editor-container {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* Editor básico */
.basic-editor {
  padding: 1rem;
  overflow-y: auto;
}

.basic-editor h4 {
  margin: 0 0 1rem 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
}

.control-group {
  margin-bottom: 1.5rem;
}

.control-group label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.control-button {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.75rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.control-button:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary-border);
}

/* Responsividade */
@media (max-width: 768px) {
  .loading-state,
  .error-state,
  .no-selection {
    padding: 1.5rem;
  }
  
  .element-info {
    padding: 0.75rem;
  }
  
  .element-icon {
    width: 36px;
    height: 36px;
  }
  
  .element-title {
    font-size: 0.8rem;
  }
  
  .element-type {
    font-size: 0.7rem;
  }
  
  .basic-editor {
    padding: 0.75rem;
  }
  
  .control-button {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
}
</style> 
