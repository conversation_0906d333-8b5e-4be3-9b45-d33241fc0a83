{"title": "Tabelas de Medidas", "subtitle": "<PERSON><PERSON><PERSON><PERSON> tabelas de medidas para produtos como roupas e calçados", "searchPlaceholder": "Buscar tabelas de medidas...", "noTables": "<PERSON><PERSON><PERSON><PERSON> tabela de medidas encontrada", "noTablesDescription": "Crie sua primeira tabela de medidas para começar", "noImage": "Sem imagem", "name": "Nome", "namePlaceholder": "Digite o nome da tabela", "type": "Tipo", "typeImage": "Imagem", "typeStructured": "Estruturada", "typeIMAGE": "Imagem", "typeSTRUCTURED": "Estruturada", "targetGender": "Público Alvo", "genderUnissex": "Unissex", "genderFeminino": "Feminino", "genderMasculino": "<PERSON><PERSON><PERSON><PERSON>", "genderInfantil": "Infantil", "genderUNISSEX": "Unissex", "genderFEMININO": "Feminino", "genderMASCULINO": "<PERSON><PERSON><PERSON><PERSON>", "genderINFANTIL": "Infantil", "image": "Imagem", "imageDescription": "Faça upload de uma imagem que mostra as medidas do produto", "addImage": "Adicionar imagem", "changeImage": "Alterar imagem", "removeImage": "Remover imagem", "imageFormats": "PNG, JPG, GIF até 15MB", "imageTooLarge": "A imagem não pode ser maior que 15MB", "uploadImage": "Clique para fazer upload", "orDragDrop": "ou arraste e solte", "replaceImage": "Substituir imagem", "imageUploadSuccess": "Imagem enviada com sucesso", "imageUploadError": "Erro ao enviar imagem", "imageDeleteSuccess": "Imagem removida com sucesso", "imageDeleteError": "Erro ao remover imagem", "columns": "Colunas de Medidas", "addColumn": "<PERSON><PERSON><PERSON><PERSON>", "columnLabel": "Nome da Medida", "columnLabelPlaceholder": "Ex: Busto, Cintura, Quadril", "unit": "Unidade", "unitPlaceholder": "cm", "columnImage": "Ícone", "columnsCount": "<PERSON><PERSON><PERSON>", "rows": "<PERSON><PERSON><PERSON>", "addRow": "<PERSON><PERSON><PERSON><PERSON>", "sizeLabel": "<PERSON><PERSON><PERSON>", "sizeLabelPlaceholder": "<PERSON>, <PERSON>, <PERSON>, 36, 38...", "sizesCount": "<PERSON><PERSON><PERSON>", "valuePlaceholder": "0", "preview": "Visualização", "previewEmpty": "Configure as colu<PERSON> e linhas para ver a visualização", "visualization": "Visualização", "size": "<PERSON><PERSON><PERSON>", "createNew": "Nova Tabela", "createSubtitle": "Crie uma nova tabela de medidas", "editTable": "<PERSON><PERSON>", "editSubtitle": "Edite as informações da tabela", "createdSuccess": "Tabela criada com sucesso", "updatedSuccess": "Tabela atualizada com sucesso", "errorLoading": "Erro ao carregar tabela", "errorCreating": "<PERSON>rro ao criar tabela", "errorUpdating": "Erro ao atualizar tabela", "errorDeleting": "Erro ao deletar tabela", "deleteTitle": "Confirmar exclusão", "deleteMessage": "Tem certeza que deseja excluir a tabela \"{name}\"?", "deletedSuccess": "Tabela excluída com sucesso", "selectTable": "Selecionar tabela de medidas", "selectTablePlaceholder": "Escolha uma tabela...", "noImageSelected": "Nenhuma imagem selecionada", "typeRequired": "Tipo é obrigatório", "genderRequired": "Gênero é obrigatório", "nameRequired": "Nome é obrigatório", "saveSuccess": "Tabela salva com sucesso", "saveError": "<PERSON>rro ao salvar tabela", "columnTagsLabel": "Colunas de medidas", "columnTagsPlaceholder": "Digite o nome da coluna (ex: Busto, Cintura, Quadril)", "columnTagsHint": "Digite o nome da coluna e pressione Enter ou vírgula para adicionar. Arraste para reordenar.", "sizeTagsLabel": "<PERSON><PERSON><PERSON> disponí<PERSON>", "sizeTagsPlaceholder": "<PERSON><PERSON><PERSON> o<PERSON> (ex: <PERSON>, <PERSON>, <PERSON> ou 36, 38, 40)", "sizeTagsHint": "Digite os tamanhos e pressione Enter ou vírgula. Use os botões abaixo para ranges comuns.", "quickSizeRanges": "Ranges de tamanhos prontos:", "sizeRangePPtoGG": "PP ao GG", "sizeRangeXStoXL": "XS ao XL", "sizeRangeXStoXXL": "XS ao XXL", "sizeRangeNumeric": "36 ao 48", "sizeRangeInfantil": "2 ao 16", "sizeRangeBaby": "Bebê (RN-24M)", "customRange": "Range Personalizado", "rangeStart": "Início", "rangeEnd": "Fim", "generateRange": "<PERSON><PERSON><PERSON>", "customRangeHint": "Gera tamanhos de 2 em 2 (ex: 100, 102, 104...)", "measurementGrid": "<PERSON><PERSON><PERSON>das", "selectUnit": "Unidade", "sizes": "<PERSON><PERSON><PERSON>", "measurementPlaceholder": "Ex: 85-90, <PERSON>, <PERSON><PERSON>", "imagePreviewTitle": "Visualização da Medida", "imagePreviewDescription": "Imagem do produto. Passe o mouse sobre uma coluna da tabela para ver onde cada medida é tomada", "hoverColumnHint": "Passe o mouse sobre as colu<PERSON> para ver onde cada medida é tomada", "uploadColumnImage": "Upload de Imagem da Coluna", "columnImageDescription": "Faça upload de uma imagem que explique onde esta medida é tomada", "columnImageHelp": "Esta imagem será mostrada quando o usuário passar o mouse sobre esta coluna", "addColumnImage": "Adicionar imagem", "changeColumnImage": "Alterar imagem", "removeColumnImage": "Remover imagem", "columnImageSaved": "Imagem da coluna salva com sucesso", "createFooterNote": "Crie uma nova tabela de medidas", "createHelp": {"description": "Crie uma nova tabela de medidas com o tipo estruturado ou o tipo imagem", "title": "Crie uma nova tabela de medidas", "subtitle": "Crie uma nova tabela de medidas com o tipo estruturado", "image": "Crie uma nova tabela de medidas com o tipo imagem", "imageDescription": "Crie uma nova tabela de medidas", "imageHelp": "Crie uma nova tabela de medidas", "addColumn": "Crie uma nova tabela de medidas"}}