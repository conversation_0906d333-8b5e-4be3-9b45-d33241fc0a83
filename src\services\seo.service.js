import axios from 'axios';
import { API_URLS } from '../config/api.config';

class SeoService {
  #endpoint;

  constructor() {
    this.#endpoint = `${API_URLS.STORE_BASE_URL}/seo-settings`;
  }
  
  getEndpoint() {
    return this.#endpoint;
  }
  
  async getStoreSeoSettings() {
    try {
      try {
        const response = await axios.get(`${this.#endpoint}`);
        return response.data;
      } catch (serverError) {

        if (serverError.response && serverError.response.status === 404) {
          return this.#getDefaultSettings();
        }
        throw serverError;
      }
    } catch (error) {
      console.error('Erro ao buscar configurações SEO:', error);
      return this.#getDefaultSettings();
    }
  }

  #getDefaultSettings() {
    return {
      id: null,
      metaTitle: '',
      metaDescription: '',
      metaKeywords: [],
      featuredImage: null,
      featuredImageUrl: null,
      enableSeoIndexation: true
    };
  }

  async updateStoreSeoSettings(data) {
    try {
      
      const serverData = {
        metaTitle: data.metaTitle || '',
        metaDescription: data.metaDescription || '',
        metaKeywords: data.metaKeywords,
        featuredImage: data.featuredImage || '',
        enableSeoIndexation: data.enableSeoIndexation !== undefined ? data.enableSeoIndexation : true
      };
      
      const config = {
        headers: {
          'Content-Type': 'application/json'
        }
      };
      

      try {
        const response = await axios.put(`${this.#endpoint}`, serverData, config);

        return response.data;
      } catch (putError) {

        if (putError.response && putError.response.status === 404) {
          const postResponse = await axios.post(`${this.#endpoint}`, serverData, config);

          return postResponse.data;
        }
        throw putError;
      }
    } catch (error) {
      console.error('Erro ao atualizar configurações SEO:', error);
      throw error;
    }
  }
  
  /**
   * Faz o upload de uma imagem para as configurações de SEO
   * @param {File} file - Arquivo de imagem para upload
   * @param {Object} currentSettings - Configurações atuais de SEO
   * @returns {Promise<Object>} - Configurações SEO atualizadas
   * @throws {Error} - Com mensagem específica para cada tipo de erro
   */
  async uploadImage(file, currentSettings) {
    try {
      if (!file || !file.type?.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem válida');
      }
      
      // Verificação de tamanho no frontend (redundante, mas garante UX melhor)
      const MAX_SIZE_MB = 10;
      const MAX_SIZE_BYTES = MAX_SIZE_MB * 1024 * 1024;
      
      if (file.size > MAX_SIZE_BYTES) {
        throw new Error(`A imagem não pode ser maior que ${MAX_SIZE_MB}MB`);
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      await axios.post(`${this.#endpoint}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      const updatedSettings = await this.getStoreSeoSettings();
      return updatedSettings;
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      
      // Tratamento específico para erro de arquivo muito grande
      if (error?.response) {
        if (error.response.status === 413) {
          throw new Error('A imagem é muito grande. O tamanho máximo permitido é 10MB.');
        } else if (error.response.data?.message) {
          throw new Error(error.response.data.message);
        } else if (error.response.status === 400) {
          throw new Error('Formato de imagem inválido ou dados corrompidos.');
        } else if (error.response.status === 500) {
          throw new Error('Erro no servidor ao processar a imagem. Tente novamente mais tarde.');
        }
      } else if (error.code === 'ERR_NETWORK' || error.message?.includes('Network Error')) {
        throw new Error('Erro de conexão com o servidor. Verifique sua conexão de internet ou tente novamente mais tarde.');
      }
      
      throw error;
    }
  }
  
  /**
   * Remove uma imagem das configurações de SEO
   * @param {string} imageUrl - URL da imagem a ser removida
   * @returns {Promise<void>}
   */
  async deleteImage(imageUrl) {
    if (!imageUrl) {
      return;
    }
    
    try {
      const fileName = imageUrl.split('/').pop();
      
      await axios.delete(`${this.#endpoint}/images/${fileName}`);
      
      const currentSettings = await this.getStoreSeoSettings();
      if (currentSettings.featuredImage === imageUrl) {
        await this.updateStoreSeoSettings({
          ...currentSettings,
          featuredImage: '',
          featuredImageUrl: ''
        });
      }
    } catch (error) {
      console.error('Erro ao remover a imagem:', error);
      if (error.response && error.response.status === 404) {

        return;
      }
      throw error;
    }
  }
  
}

export default new SeoService();