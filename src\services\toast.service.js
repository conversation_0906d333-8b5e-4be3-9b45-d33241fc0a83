import { useToastStore } from '@/stores/toast.store'
import { useI18n } from 'vue-i18n'

export const useToast = () => {
  const toastStore = useToastStore()
  const { t } = useI18n()

  // Função principal para adicionar toast
  const addToast = (message, options = {}) => {
    const toastOptions = {
      message,
      type: options.type || 'info',
      title: options.title || '',
      duration: options.duration !== undefined ? options.duration : 4000,
      action: options.action || null,
      persistent: options.persistent || false,
      dismissible: options.dismissible !== undefined ? options.dismissible : true,
      ...options
    }

    // Se não foi fornecido um título, usar o padrão baseado no tipo
    if (!toastOptions.title && t) {
      toastOptions.title = t(`toast.titles.${toastOptions.type}`)
    }

    return toastStore.addToast(toastOptions)
  }

  // Métodos de conveniência para diferentes tipos
  const showSuccess = (message, options = {}) => {
    return addToast(message, { 
      ...options, 
      type: 'success',
      title: options.title || (t ? t('toast.titles.success') : 'Sucesso')
    })
  }

  const showError = (message, options = {}) => {
    return addToast(message, { 
      ...options, 
      type: 'error',
      title: options.title || (t ? t('toast.titles.error') : 'Erro')
    })
  }

  const showWarning = (message, options = {}) => {
    return addToast(message, { 
      ...options, 
      type: 'warning',
      title: options.title || (t ? t('toast.titles.warning') : 'Atenção')
    })
  }

  const showInfo = (message, options = {}) => {
    return addToast(message, { 
      ...options, 
      type: 'info',
      title: options.title || (t ? t('toast.titles.info') : 'Informação')
    })
  }

  // Métodos avançados
  const showSuccessWithAction = (message, actionLabel, actionHandler, options = {}) => {
    return showSuccess(message, {
      ...options,
      action: {
        label: actionLabel || (t ? t('toast.undo') : 'Desfazer'),
        handler: actionHandler
      }
    })
  }

  const showErrorWithAction = (message, actionLabel, actionHandler, options = {}) => {
    return showError(message, {
      ...options,
      action: {
        label: actionLabel || (t ? t('toast.undo') : 'Tentar novamente'),
        handler: actionHandler
      }
    })
  }

  const showPersistent = (message, type = 'info', options = {}) => {
    return addToast(message, {
      ...options,
      type,
      persistent: true,
      duration: 0
    })
  }

  // Métodos de controle
  const removeToast = (id) => {
    return toastStore.removeToast(id)
  }

  const clearAll = () => {
    return toastStore.clearAllToasts()
  }

  const updateToast = (id, updates) => {
    return toastStore.updateToast(id, updates)
  }

  // Compatibilidade com o método antigo (manter por um tempo)
  const showToast = addToast

    return {
    // Métodos principais
        addToast,
        showSuccess,
        showError,
        showWarning,
    showInfo,
    
    // Métodos avançados
    showSuccessWithAction,
    showErrorWithAction,
    showPersistent,
    
    // Controle
    removeToast,
    clearAll,
    updateToast,
    
    // Compatibilidade
    showToast
  }
}