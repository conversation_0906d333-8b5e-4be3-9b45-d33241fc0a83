<template>
  <div class="increase-shipping-config">
    <!-- Grid responsivo: lado a lado no desktop, empilhado no mobile -->
    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
      
      <!-- Seção Valor Fixo -->
      <div class="config-section">
        <div class="section-header">
          <div class="flex items-center justify-between w-full">
            <label for="switch-fixed-value" class="section-title">
              {{ t('shippingConfig.addFixedValueLabel') }}
            </label>
            <IluriaToggleSwitch 
              id="switch-fixed-value" 
              v-model="enableFixedValue" 
              class="toggle-switch"
            />
          </div>
        </div>
        
        <div class="section-content">
          <div class="form-group">
            <IluriaInputText
              id="increaseShippingValue"
              name="increaseShippingValue"
              class="w-full"
              placeholder=""
              v-model="increaseShippingValue"
              :formContext="props.formContext?.increaseShippingValue"
              :disabled="!enableFixedValue"
              type="money"
              prefix="R$"
            />
          </div>
        </div>
      </div>

      <!-- Seção Percentual -->
      <div class="config-section">
        <div class="section-header">
          <div class="flex items-center justify-between w-full">
            <label for="switch-percentage" class="section-title">
              {{ t('shippingConfig.addPercentageLabel') }}
            </label>
            <IluriaToggleSwitch 
              id="switch-percentage" 
              v-model="enablePercentage" 
              class="toggle-switch"
            />
          </div>
        </div>
        
        <div class="section-content">
          <div class="form-group">
            <IluriaInputText
              id="increaseShippingPercentage"
              name="increaseShippingPercentage"
              class="w-full percentage-input"
              placeholder=""
              v-model="increaseShippingPercentage"
              :formContext="props.formContext?.increaseShippingPercentage"
              :disabled="!enablePercentage"
              suffix="%"
              type="number"
            />
          </div>
        </div>
      </div>
      
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { useI18n } from 'vue-i18n';
import { requiredNonNegativeNumberWithMessages } from '@/services/validation.service';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';


const { t } = useI18n();

// Define models for the two form values
const increaseShippingValue = defineModel('increaseShippingValue');
const increaseShippingPercentage = defineModel('increaseShippingPercentage');

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Switches que se sincronizam com os valores dos campos
const enableFixedValue = ref(false);
const enablePercentage = ref(false);

// Watchers para sincronizar switches com valores dos campos
watch(increaseShippingValue, (newValue) => {
  enableFixedValue.value = newValue != null && newValue !== '' && newValue > 0;
}, { immediate: true });

watch(increaseShippingPercentage, (newValue) => {
  enablePercentage.value = newValue != null && newValue !== '' && newValue > 0;
}, { immediate: true });

// Watchers para limpar valores quando switches são desabilitados
watch(enableFixedValue, (newValue) => {
  if (!newValue) {
    increaseShippingValue.value = null;
  }
});

watch(enablePercentage, (newValue) => {
  if (!newValue) {
    increaseShippingPercentage.value = null;
  }
});

// Define validation rules condicionais como computed
const validationRules = computed(() => {
  const rules = {};
  
  if (enableFixedValue.value) {
    rules.increaseShippingValue = requiredNonNegativeNumberWithMessages(t('shippingConfig.fixedValueLabel'), {
      requiredMessage: t('validation.fieldRequired', { field: t('shippingConfig.fixedValueLabel') }),
      invalidMessage: t('validation.invalidNumber', { field: t('shippingConfig.fixedValueLabel') }),
      minValueMessage: t('validation.nonNegative', { field: t('shippingConfig.fixedValueLabel') })
    });
  }
  
  if (enablePercentage.value) {
    rules.increaseShippingPercentage = requiredNonNegativeNumberWithMessages(t('shippingConfig.percentageLabel'), {
      requiredMessage: t('validation.fieldRequired', { field: t('shippingConfig.percentageLabel') }),
      invalidMessage: t('validation.invalidNumber', { field: t('shippingConfig.percentageLabel') }),
      minValueMessage: t('validation.nonNegative', { field: t('shippingConfig.percentageLabel') })
    });
  }
  
  return rules;
});

// Expose validation rules
defineExpose({ 
  validationRules
});
</script>

<style scoped>
.increase-shipping-config {
  padding: 0;
}

.config-section {
  background: var(--iluria-surface-card);
  border: 1px solid var(--iluria-surface-border);
  border-radius: 8px;
  padding: 1.5rem;
  transition: all 0.2s ease;
}

.config-section:hover {
  border-color: var(--iluria-primary-color);
}

.section-header {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
  transition: color 0.3s ease;
  line-height: 1.4;
  flex: 1;
  margin-right: 1rem;
}

.section-title:hover {
  color: var(--iluria-primary-color);
}

.toggle-switch {
  flex-shrink: 0;
}

.section-content {
  margin-top: 1rem;
}

.form-group {
  width: 100%;
}




/* Estilização para input com prefix R$ */
.form-group :deep(.custom-prefix) {
  background: var(--iluria-surface-card);
  border-right: 1px solid var(--iluria-surface-border);
  color: var(--iluria-color-text-primary);
  font-weight: 600;
}

.form-group :deep(.custom-input-group) {
  box-shadow: none !important;
}

.form-group :deep(.custom-input-group:focus-within) {
  box-shadow: none !important;
}

/* Estilização específica para input de percentual */
.percentage-input :deep(.p-inputtext) {
  text-align: right;
  box-shadow: none !important;
}

.percentage-input :deep(.p-inputtext:focus) {
  box-shadow: none !important;
}

/* Estados desabilitados */
.config-section:has(:disabled) {
  opacity: 0.7;
}

/* Responsividade para tablets */
@media (max-width: 1024px) {
  .config-section {
    padding: 1.25rem;
  }
  
  .section-title {
    font-size: 15px;
  }
}

/* Responsividade para mobile */
@media (max-width: 768px) {
  .config-section {
    padding: 1rem;
  }
  
  .section-header {
    margin-bottom: 1rem;
  }
  
  .section-title {
    font-size: 14px;
    line-height: 1.3;
    margin-right: 0.5rem;
  }
  

  
  .percentage-input :deep(.p-inputtext) {
    box-shadow: none !important;
  }
}
</style>
