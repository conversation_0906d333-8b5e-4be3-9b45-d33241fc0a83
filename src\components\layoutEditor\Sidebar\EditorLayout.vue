<template>
  <div class="editor-layout">
    <!-- Sidebar à esquerda -->
    <EditorSidebar 
      ref="sidebarRef"
      :selectedElement="selectedElement"
      :isVisible="sidebarVisible"
      :isHidden="sidebarHidden"
      :activeTab="activeSidebarTab"
      :sidebarWidth="sidebarWidth"
      :iframeDocument="iframeDocument"
      :layoutType="layoutType"
      @tab-change="handleTabChange"
      @close="handleSidebarClose"
      @sidebar-shown="handleSidebarShown"
      @sidebar-hidden="handleSidebarHidden"
      @element-updated="handleElementUpdated"
      @component-select="handleComponentSelect"
      @page-settings-updated="handlePageSettingsUpdated"
      @section-select="handleSectionSelect"
      @section-highlight="handleSectionHighlight"
      @section-deleted="handleSectionDeleted"
      @section-will-delete="handleSectionWillDelete"
    />
    
    <!-- Canvas principal à direita -->
    <div 
      class="canvas-container"
      :style="{ 
        marginLeft: sidebarVisible && !sidebarHidden ? `${sidebarWidth}px` : '0px',
        transition: 'margin-left 0.3s ease'
      }"
    >
      <slot 
        name="canvas"
        :onElementSelect="handleElementSelect"
        :onToolbarAction="handleToolbarAction"
      />
    </div>
    
    <!-- Overlay para mobile quando sidebar está aberta -->
    <div 
      v-if="sidebarVisible && isMobile"
      class="sidebar-overlay"
      @click="handleSidebarClose"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted } from 'vue'
import EditorSidebar from './EditorSidebar.vue'

const props = defineProps({
  mode: {
    type: String,
    default: 'edit'
  },
  layoutType: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['element-select', 'toolbar-action', 'element-updated', 'component-select', 'page-settings-updated', 'section-select', 'section-highlight', 'section-deleted', 'section-will-delete'])

// Estado da sidebar - só deve estar visível no modo de edição
const sidebarVisible = computed(() => props.mode === 'edit')
const sidebarHidden = ref(false) // Estado de escondida
const activeSidebarTab = ref('initial') // 'initial', 'home', 'properties', 'page', 'add-component'
const sidebarWidth = ref(400)
const selectedElement = ref(null)
const iframeDocument = ref(null)

// Responsividade
const viewportWidth = ref(window.innerWidth)
const isMobile = computed(() => viewportWidth.value <= 768)

// Ajustar largura da sidebar baseado no viewport
watch(viewportWidth, (newWidth) => {
  if (newWidth <= 768) {
    sidebarWidth.value = Math.min(350, newWidth - 50) // Mobile: sidebar menor
  } else if (newWidth <= 1024) {
    sidebarWidth.value = 350 // Tablet
  } else {
    sidebarWidth.value = 400 // Desktop
  }
})

// Detecção do iframe document
const detectIframeDocument = () => {
  const iframe = document.querySelector('#editor-frame')
  if (iframe && iframe.contentDocument) {
    iframeDocument.value = iframe.contentDocument
    return true
  }
  return false
}

// Gerenciamento de redimensionamento
const handleResize = () => {
  viewportWidth.value = window.innerWidth
}

onMounted(() => {
  window.addEventListener('resize', handleResize)
  
  // Tenta detectar o iframe imediatamente
  if (!detectIframeDocument()) {
    // Se não encontrou, tenta novamente com intervalos
    const checkInterval = setInterval(() => {
      if (detectIframeDocument()) {
        clearInterval(checkInterval)
      }
    }, 500)
    
    // Para de tentar após 10 segundos
    setTimeout(() => {
      clearInterval(checkInterval)
    }, 10000)
  }
})

onUnmounted(() => {
  window.removeEventListener('resize', handleResize)
})

// Handlers
const handleElementSelect = (element) => {
  
  selectedElement.value = element
  
  if (element && props.mode === 'edit') {
    // Só mostra a sidebar se estiver no modo de edição
    sidebarHidden.value = false // Mostrar se escondida
    activeSidebarTab.value = 'properties'
    
  }
  
  emit('element-select', element)
}

const handleToolbarAction = (action) => {
  // Só processa ações da toolbar se estiver no modo de edição
  if (props.mode !== 'edit') {
    emit('toolbar-action', action)
    return
  }
  
  // Quando uma ação da toolbar é clicada, abrir sidebar na tab appropriada
  if (action.type === 'add-above' || action.type === 'add-below') {
    // Ações de adicionar componente -> tab Components
    activeSidebarTab.value = 'components'
    sidebarHidden.value = false
  } else if (action.type === 'delete') {
    // Ação de deletar -> fechar sidebar
    sidebarHidden.value = true
    selectedElement.value = null
  } else {
    // Outras ações (editores de propriedades) -> tab Properties
    activeSidebarTab.value = 'properties'
    sidebarHidden.value = false
  }
  
  emit('toolbar-action', action)
}

const handleTabChange = (tabName) => {
  activeSidebarTab.value = tabName
}

const handleSidebarClose = () => {
  // Em vez de fechar, esconder a sidebar
  sidebarHidden.value = true
  // Não limpar selectedElement para manter seleção
}

const handleSidebarShown = () => {
  sidebarHidden.value = false
}

const handleSidebarHidden = () => {
  sidebarHidden.value = true
}

const handleElementUpdated = (data) => {
  emit('element-updated', data)
}

const handleComponentSelect = (componentData) => {
  emit('component-select', componentData)
}

const handlePageSettingsUpdated = (settings) => {
  emit('page-settings-updated', settings)
}

const handleSectionSelect = (section) => {
  
  // Encontrar o elemento real no iframe
  if (iframeDocument.value && section.element) {
    // Se a seção tem um elemento associado, simular clique nele
    handleElementSelect(section.element)
  }
  
  emit('section-select', section)
}

const handleSectionHighlight = (data) => {
  emit('section-highlight', data)
}

const handleSectionDeleted = (section) => {
  emit('section-deleted', section)
}

const handleSectionWillDelete = (section) => {
  emit('section-will-delete', section)
}

// Funções expostas para controle externo
const openSidebar = (tab = 'initial') => {
  // Só permite abrir a sidebar se estiver no modo de edição
  if (props.mode === 'edit') {
    sidebarHidden.value = false
    activeSidebarTab.value = tab
  }
}

const closeSidebar = () => {
  sidebarHidden.value = true
}

const toggleSidebar = () => {
  // Só permite toggle se estiver no modo de edição
  if (props.mode === 'edit') {
    sidebarHidden.value = !sidebarHidden.value
  }
}

const setActiveTab = (tab) => {
  // Só permite mudança de tab se estiver no modo de edição
  if (props.mode === 'edit') {
    activeSidebarTab.value = tab
    if (sidebarHidden.value) {
      sidebarHidden.value = false
    }
  }
}

// Referência para a sidebar
const sidebarRef = ref(null)

const refreshSidebar = () => {
  if (sidebarRef.value && sidebarRef.value.refreshSections) {
    sidebarRef.value.refreshSections()
  }
}

defineExpose({
  openSidebar,
  closeSidebar,
  toggleSidebar,
  setActiveTab,
  refreshSidebar,
  sidebarVisible,
  sidebarHidden,
  activeSidebarTab,
  selectedElement
})
</script>

<style scoped>
.editor-layout {
  position: relative;
  width: 100%;
  height: 100%;
  display: flex;
  overflow: hidden;
}

.canvas-container {
  flex: 1;
  height: 100%;
  position: relative;
  background: var(--iluria-color-background);
  transition: margin-left 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sidebar-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  z-index: 999;
  transition: opacity 0.3s ease;
}

/* Responsividade */
@media (max-width: 768px) {
  .canvas-container {
    margin-left: 0 !important;
  }
}

/* Animações suaves */
.editor-layout * {
  box-sizing: border-box;
}

/* Otimizações de performance */
.canvas-container {
  will-change: margin-left;
}

.sidebar-overlay {
  will-change: opacity;
}
</style> 
