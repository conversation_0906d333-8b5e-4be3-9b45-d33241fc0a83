import useUserStore from '@/stores/auth.store'

class BaseApi {
    #requestBody = undefined;
    #requestHeader = undefined;
    #onSuccessCallback = undefined;
    #onApiErrorCallback = undefined;
    #onNetworkErrorCallback = undefined;
    #finalCallback = undefined;

    #defaultOnApiErrorCallback(response) {
//        FavesErrorAlert.build()
//            .withTitle("Error")
//            .withMessage(response.description)
 //           .show();
    }

    #defaultOnNetworkErrorCallback() {
//        FavesErrorAlert.build()
//            .withTitle("Error")
//            .withMessage("There was a problem communicating with the server. Please try again.")
//            .show();
    }

    #processFinalCallback(response) {
        if (typeof this.#finalCallback === "function") {
            this.#finalCallback(response);
        }
    }

    processOnSuccessCallback(response) {
        if (typeof this.#onSuccessCallback === "function") {
            this.#onSuccessCallback(response);
        }
        this.#processFinalCallback(response);
    }

    #processNetworkResponseError(response) {
        if(401 == response.status){
            useUserStore().logout();
            return;
        }

        if (typeof this.#onNetworkErrorCallback === "function") {
            this.#onNetworkErrorCallback(response);
        } else {
            this.#defaultOnNetworkErrorCallback(response);
        }
        this.#processFinalCallback(response);
    }

    async __get(requestUrl) {
        try {
            const response = await fetch(requestUrl, {
                method: 'GET',
                headers: {
                    ...(this.#requestHeader || {}),
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw response;
            }

            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                const data = await response.json();
                this.processOnSuccessCallback(data);
            } else {
                this.processOnSuccessCallback(null);
            }
        } catch (error) {
            this.#processNetworkResponseError(error);
        }
    }

    async __post(requestUrl) {
        try {
            const response = await fetch(requestUrl, {
                method: 'POST',
                headers: {
                    ...(this.#requestHeader || {}),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.#requestBody),
            });

            if (!response.ok) {
                throw response;
            }

            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                const data = await response.json();
                this.processOnSuccessCallback(data);
            } else {
                this.processOnSuccessCallback(null);
            }
        } catch (error) {
            this.#processNetworkResponseError(error);
        }
    }

    async __put(requestUrl) {
        try {
            const response = await fetch(requestUrl, {
                method: 'PUT',
                headers: {
                    ...(this.#requestHeader || {}),
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(this.#requestBody),
            });

            if (!response.ok) {
                throw response;
            }

            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                const data = await response.json();
                this.processOnSuccessCallback(data);
            } else {
                this.processOnSuccessCallback(null);
            }
        } catch (error) {
            this.#processNetworkResponseError(error);
        }
    }

    async __delete(requestUrl) {
        try {
            const response = await fetch(requestUrl, {
                method: 'DELETE',
                headers: {
                    ...(this.#requestHeader || {}),
                    'Content-Type': 'application/json',
                }
            });

            if (!response.ok) {
                throw response;
            }

            const contentType = response.headers.get("content-type");
            if (contentType && contentType.indexOf("application/json") !== -1) {
                const data = await response.json();
                this.processOnSuccessCallback(data);
            } else {
                this.processOnSuccessCallback(null);
            }
        } catch (error) {
            this.#processNetworkResponseError(error);
        }
    }

    withBody(requestBody) {
        this.#requestBody = requestBody;
        return this;
    }

    withHeader(header) {
        this.#requestHeader = {
            ...(this.#requestHeader || {}),
            ...(header || {})
        }
        return this;
    }

    authenticated(){
        const userStore = useUserStore();
        const token = userStore.jwtToken;

        if (!token) {
            userStore.logout();
        }

        this.#requestHeader = {
            ...(this.#requestHeader || {}),
            ...({'Authorization': 'Bearer '+token})
        }
        return this;
    }

    onSuccess(onSuccessCallback) {
        this.#onSuccessCallback = onSuccessCallback;
        return this;
    }

    onApiError(onApiErrorCallback) {
        this.#onApiErrorCallback = onApiErrorCallback;
        return this;
    }

    onNetworkError(onNetworkErrorCallback) {
        this.#onNetworkErrorCallback = onNetworkErrorCallback;
        return this;
    }

    finalCallback(finalCallback) {
        this.#finalCallback = finalCallback;
        return this;
    }

    static createRequest() {
        return new this();
    }
}

export { BaseApi as BaseApi };
