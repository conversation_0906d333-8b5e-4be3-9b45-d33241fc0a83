<template>
  <div class="flex flex-col h-full space-y-4">
    <div class="flex flex-col md:flex-row md:items-center justify-between gap-2">
      <PathNavigator 
        :path="currentPath" 
        :segments="pathSegments"
        :environment-color="environmentColor"
        @navigate="navigateTo" 
      />
      
      <div class="flex items-center gap-1">
        <button class="btn-icon" @click="refreshCurrentFolder" title="Refresh">
          <HugeiconsIcon :icon="RefreshIcon" size="22" />
        </button>
        <button 
          class="btn-icon" 
          title="New folder"
          @click="showNewFolderDialog = true"
        >
          <HugeiconsIcon :icon="FolderAddIcon" size="22" />
        </button>
        <button 
          class="btn-icon" 
          title="Download All"
          @click="handleDownloadAll"
        >
          <HugeiconsIcon :icon="CloudDownloadIcon" size="22" />
        </button>
        <button 
          class="btn-icon" 
          title="New file"
          @click="openNewFileDialog"
        >
          <HugeiconsIcon :icon="FileAddIcon" size="22" />
        </button>
        <button 
          class="btn-icon" 
          title="Upload files"
          @click="openUploadModal"
        >
          <HugeiconsIcon :icon="UploadCircle02Icon" size="22" />
        </button>
      </div>
    </div>

    <div class="flex flex-col md:flex-row gap-4 h-full">
      <div class="w-full md:w-64 bg-white rounded-lg shadow-sm border border-gray-200 p-4">
        <TreeView
          :nodes="fileStructure"
          :environment="environment"
          :parent-folder-id="selectedNode?.key"
          :environment-color="environmentColor"
          @node-select="handleNodeSelect"
          @refresh-tree="handleRefreshTree"
          @node-edit="handleNodeEdit"
          @node-delete="handleNodeDeleteFromTree"
          @node-updated="handleNodeUpdated"
          @folder-moved="handleFolderMoved"
          @item-moved="handleItemMoved"
        />
      </div>

      <div class="flex-1">
        <!-- Loading state -->
        <div v-if="loadingFiles" class="flex justify-center items-center py-8">
          <div class="animate-spin rounded-full h-8 w-8 border-t-2 border-b-2" :class="`border-${environmentColor === 'develop' ? 'green' : 'blue'}-500`"></div>
        </div>
        
        <!-- Error state -->
        <div v-else-if="fileError" class="bg-red-50 border border-red-200 text-red-700 px-4 py-3 rounded relative">
          <span class="block sm:inline">{{ fileError }}</span>
          <button 
            @click="refreshCurrentFolder" 
            class="mt-3 bg-red-100 hover:bg-red-200 text-red-800 font-semibold py-1 px-3 rounded-md transition-colors duration-200 text-sm">
            {{ $t('tryAgain') }}
          </button>
        </div>
        
        <!-- Empty state -->
        <div v-else-if="currentFiles.length === 0" class="flex flex-col items-center justify-center py-8 text-gray-500">
          <HugeiconsIcon :icon="Folder02Icon" />
          <p>{{ $t('fileManager.emptyFolder') }}</p>
        </div>
        
        <!-- Content when data is loaded -->
        <FileListView
          v-else
          :files="currentFiles"
          :environment-color="environmentColor"
          @file-click="handleFileClick"
          @folder-click="handleFolderClick"
          @file-updated="handleFileUpdated"
          @file-delete-requested="handleDeleteRequest"
          @delete-selected-requested="handleDeleteSelectedRequest"
          @item-moved="handleItemMoved"
        />
      </div>
    </div>
    
    <!-- Upload Modal -->
    <FileUploadModal
      v-model:visible="showUploadModal"
      :current-folder="currentFolder"
      :environment="environment"
      @upload-complete="handleUploadComplete"
    />
    
    <!-- New Folder Dialog -->
    <IluriaModal 
      v-model:visible="showNewFolderDialog" 
      :header="$t('fileManager.newFolder')" 
      :modal="true"
      class="w-full max-w-md"
    >
      <div class="p-4">
        <p class="mb-4 text-[var(--iluria-color-text-primary)]">{{ $t('fileManager.newFolderDescription') }}</p>
        <IluriaInputText
          class="w-full"
          v-model="newFolderName"
          @keyup.enter="createNewFolder"
          :disabled="isCreatingFolder"
        />
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <IluriaButton color="secondary" @click="showNewFolderDialog = false">
            {{ $t('cancel') }}
          </IluriaButton>
          <IluriaButton
            @click="createNewFolder"
            :disabled="!newFolderName || isCreatingFolder"
            :loading="isCreatingFolder"
          >
            {{ $t('create') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>
    
    <!-- New File Dialog -->
    <IluriaModal 
      v-model:visible="showNewFileDialog" 
      :header="$t('fileManager.newFile')" 
      :modal="true"
      class="w-full max-w-md"
    >
      <div class="p-4 space-y-4">
        <div>
          <label for="fileName" class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-1">{{ $t('fileManager.newFileDescription') }}</label>
          <IluriaInputText 
            id="fileName"
            v-model="newFileName" 
            class="w-full" 
            placeholder="exemplo.txt"
            @keyup.enter="createNewFile"
          />
          <p class="text-xs text-[var(--iluria-color-text-secondary)] mt-1">Use .txt, .html, .css, .js, etc.</p>
        </div>
      </div>
      <template #footer>
        <div class="flex justify-end gap-2">
          <IluriaButton color="secondary" @click="showNewFileDialog = false">
            {{ $t('cancel') }}
          </IluriaButton>
          <IluriaButton 
            @click="createNewFile" 
            :disabled="!newFileName"
          >
            {{ $t('create') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>  
    
    <!-- Image Preview Modal -->
    <IluriaModal 
      v-model:visible="showImagePreview" 
      :style="{ width: '80vw', maxWidth: '800px', height: '80vh' }"
      :show-header="false"
      :show-footer="false"
      :dismissable-mask="true"
      :content-style="{
        padding: 0,
        margin: 0,
        backgroundColor: 'rgba(0,0,0,0.9)',
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        overflow: 'hidden'
      }"
    >
      <div class="w-full h-full flex items-center justify-center p-4">
        <img 
          :src="currentImageUrl" 
          class="max-w-full max-h-full object-contain cursor-pointer"
          alt="Image preview"
          @click.self="showImagePreview = false"
        />
      </div>
    </IluriaModal>

    <!-- Confirmation Modal -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToast } from '@/services/toast.service';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { HugeiconsIcon } from '@hugeicons/vue';
import { Folder02Icon,RefreshIcon,FileAddIcon,FolderAddIcon,UploadCircle02Icon,CloudDownloadIcon } from '@hugeicons-pro/core-bulk-rounded';
import FileUploadModal from './FileUploadModal.vue';
import TreeView from './TreeView.vue';
import PathNavigator from './PathNavigator.vue';
import FileListView from './FileListView.vue';
import fileManagerService from '@/services/fileManager.service.js';
import IluriaInputText from '@/components/Iluria/form/IluriaInputText.vue';
import IluriaModal from '@/components/Iluria/IluriaModal.vue';

const toast = useToast();
const { t } = useI18n();

const emit = defineEmits(['file-selected', 'refresh-tree']);

const props = defineProps({
  environment: {
    type: String,
    required: true
  },
  fileStructure: {
    type: Array,
    default: () => []
  }
});

const currentPath = ref('');
const selectedNode = ref({
  key: '', 
  label: 'Root',
  type: 'FOLDER'
});

const currentFiles = ref([]);
const loadingFiles = ref(false);
const fileError = ref('');
const showUploadModal = ref(false);
const showNewFolderDialog = ref(false);
const showNewFileDialog = ref(false);
const showFileEditor = ref(false);
const showImagePreview = ref(false);
const currentImageUrl = ref('');
const newFolderName = ref('');
const newFileName = ref('');
const currentFile = ref(null);

const currentFolder = computed(() => {
  if (!selectedNode.value) return null;
  return {
    id: selectedNode.value.key,
    name: selectedNode.value.label,
    type: selectedNode.value.type,
    parentFolderId: selectedNode.value.parentFolderId
  };
});

const environmentColor = computed(() => {
  return props.environment === 'DEVELOP' ? 'develop' : 'production';
});

const findFolderById = async (folderId) => {
  if (!folderId) return null;
  
  try {
    const folderData = await fileManagerService.getById(props.environment, folderId);
    
    if (Array.isArray(folderData)) {
      return folderData.find(item => 
        item.id === folderId && 
        (!item.type || item.type === 'FOLDER')
      );
    } 
    else if (folderData && folderData.id === folderId && (!folderData.type || folderData.type === 'FOLDER')) {
      return folderData;
    }
    
    return null;
  } catch (error) {
    console.error('Error fetching folder by ID:', error);
    return null;
  }
};

const pathSegments = ref([]);

const updatePathSegments = async () => {
  const segments = [];
  let current = selectedNode.value;
  
  if (current && current.type === 'ARCHIVE' && current.parentFolderId) {
    const parentFolder = await findFolderById(current.parentFolderId);
    if (parentFolder) {
      current = parentFolder;
    }
  }
  
  while (current) {
    if (!current.type || current.type === 'FOLDER') {
      segments.unshift({
        id: current.key || current.id,
        name: current.label || current.name || 'Untitled',
        type: current.type
      });
    }
    current = current.parent;
  }
  
  pathSegments.value = segments;
};

watch(() => selectedNode.value, async () => {
  await updatePathSegments();
}, { immediate: true, deep: true });

const fetchNodeContents = async (nodeId) => {
  loadingFiles.value = true;
  fileError.value = null;

  try {
    const data = await fileManagerService.getById(
      props.environment, 
      nodeId === '' || nodeId === null || nodeId === undefined ? null : nodeId
    );
    
    if (data && Array.isArray(data)) {
      if (data.length === 1 && data[0].type === 'FOLDER' && Array.isArray(data[0].children)) {
        currentFiles.value = data[0].children.map(item => ({
          ...item,
          parent: data[0],
          parentId: data[0].key
        }));
      } else {
        currentFiles.value = data.map(item => ({
          ...item,
          parent: nodeId ? selectedNode.value : null,
          parentId: nodeId || null
        }));
      }
    } else {
      currentFiles.value = [];
    }
    
  } catch (error) {
    console.error('Error fetching node contents:', error);
    fileError.value = 'Falha ao carregar conteúdo da pasta';
    currentFiles.value = [];
  } finally {
    loadingFiles.value = false;
  }
};

const initializeRoot = () => {
  fetchNodeContents(null);
};

const handleNodeEdit = (node) => {
  emit('node-edit', node);
};

const handleNodeSelect = async (node) => {
  selectedNode.value = node;
  currentPath.value = node.key;
  
  if (node.type === 'FOLDER') {
    await fetchNodeContents(node.key);
  }
};

const handleRefreshTree = async () => {
  if (selectedNode.value?.key) {
    await fetchNodeContents(selectedNode.value.key);
  } else {
    await initializeRoot();
  }
};

const openUploadModal = () => {
  showUploadModal.value = true;
};

const handleUploadComplete = async () => {
  if (selectedNode.value?.key) {
    await fetchNodeContents(selectedNode.value.key);
  }
  
  emit('refresh-tree');
  handleRefreshTree();
  
  toast.showSuccess(t('fileManager.uploadComplete'));
};

const openNewFileDialog = () => {
  newFileName.value = '';
  showNewFileDialog.value = true;
};

const createNewFile = async () => {
  if (!newFileName.value.trim()) return;
  
  try {
    const fileName = newFileName.value;
    
    const currentNode = selectedNode.value;
    let parentId = null;
    
    if (currentNode) {
      if (currentNode.type === 'ARCHIVE' && currentNode.parentFolderId) {
        parentId = currentNode.parentFolderId;
      } else if (currentNode.type === 'FOLDER' || !currentNode.type) {
        parentId = currentNode.key;
      }
    }
    
    const response = await fileManagerService.createFile({
      name: fileName,
      parentFolderId: parentId,
      environment: props.environment
    });
    
    toast.showSuccess(t('fileManager.fileCreated'));
    
    showNewFileDialog.value = false;
    newFileName.value = '';
    
    emit('file-created', {
      id: response?.id,
      name: fileName,
      type: 'ARCHIVE',
      parentId: parentId,
    });
    
    if (parentId) {
      await fetchNodeContents(parentId);
    } else {
      await initializeRoot();
    }
    
  } catch (error) {
    console.error('Erro ao criar arquivo:', error);
    toast.showError(t('fileManager.errorCreatingFile'));
  }
};

const isEditingFolder = ref(false);
const isCreatingFolder = ref(false);

const createNewFolder = async () => {
  if (!newFolderName.value.trim() || isCreatingFolder.value) return;

  isCreatingFolder.value = true;
  
  try {
    const currentNode = selectedNode.value;
    let parentId = null;
    
    if (currentNode) {
      if (currentNode.type === 'ARCHIVE' && currentNode.parentFolderId) {
        parentId = currentNode.parentFolderId;
      } else if (currentNode.type === 'FOLDER' || !currentNode.type) {
        parentId = currentNode.key;
      }
    }
    
    if (currentFiles.value.some(file => 
      file.name === newFolderName.value.trim() && 
      file.type === 'FOLDER'
    )) {
      throw new Error('Já existe uma pasta com este nome');
    }
    
    if (isEditingFolder.value && selectedNode.value) {
      await fileManagerService.updateFolder(selectedNode.value.key, {
        name: newFolderName.value
      });
      
      handleNodeUpdated({
        ...selectedNode.value,
        name: newFolderName.value.trim(),
        label: newFolderName.value.trim()
      });
      
      toast.showSuccess(t('fileManager.folderRenamed'));
    } else {
      const newFolder = await fileManagerService.createFolder({
        name: newFolderName.value,
        parentFolderId: parentId,
        environment: props.environment
      });
      
      await handleFolderCreated(newFolder);
      
      if (parentId) {
        await fetchNodeContents(parentId);
      } else {
        await initializeRoot();
      }
      
      await loadCurrentFolderContents();
      
      toast.showSuccess(t('fileManager.folderCreated'));
    }
    
    showNewFolderDialog.value = false;
    newFolderName.value = '';
    isEditingFolder.value = false;
    
  } catch (error) {
    console.error('Error in folder operation:', error);

    toast.showError(t('fileManager.errorCreatingFolder'));
  } finally {
    isCreatingFolder.value = false;
  }
};

const handleFolderCreated = async (newFolder) => {
  try {
    emit('folder-created', newFolder);
    
    if (selectedNode.value?.key === newFolder.parentId || 
        (!selectedNode.value?.key && !newFolder.parentId)) {
      await fetchNodeContents(selectedNode.value?.key || '');
    }
    
    return newFolder;
  } catch (error) {
    console.error('Erro ao processar a criação da pasta:', error);
    throw error;
  }
};

const handleFileCreated = async (newFile) => {
  currentFiles.value = [newFile, ...currentFiles.value];
  
  if (selectedNode.value?.key === newFile.parentId) {
    await fetchNodeContents(selectedNode.value.key);
    
    if (newFile.type === 'FOLDER') {
      await initializeRoot();
    }
  } else if (!selectedNode.value && !newFile.parentId) {
    await fetchNodeContents(null);
  }
  
  emit('file-created', newFile);
};

const handleFolderClick = (folder) => {
  const node = {
    key: folder.id,
    label: folder.name,
    type: 'FOLDER'
  };
  handleNodeSelect(node);
  
  emit('folder-click', folder);
};

const handleFileUpdated = (updatedFile) => {
  const index = currentFiles.value.findIndex(f => f.id === updatedFile.id);
  if (index !== -1) {
    currentFiles.value = [
      ...currentFiles.value.slice(0, index),
      updatedFile,
      ...currentFiles.value.slice(index + 1)
    ];
  }
  
  const updatedNode = {
    key: updatedFile.id,
    label: updatedFile.name,
    data: updatedFile,
    parentId: updatedFile.parentId,
    type: updatedFile.type || 'ARCHIVE',
    children: currentFiles.value.find(f => f.id === updatedFile.id)?.children || []
  };
  
  handleNodeUpdated(updatedNode);
  
  if (selectedNode.value?.key === updatedFile.parentId) {
    fetchNodeContents(selectedNode.value.key);
  }
};


const handleNodeUpdated = (updatedNode) => {
  if (!props.fileStructure) {
    console.warn('fileStructure não está disponível');
    return;
  }
  
  const updatedFileStructure = JSON.parse(JSON.stringify(props.fileStructure));
  
  const updateNodeInTree = (nodes, nodeKey, updates) => {
    if (!nodes) return false;
    
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].key === nodeKey) {
        nodes[i] = { ...nodes[i], ...updates };
        return true;
      }
      
      if (nodes[i].children && updateNodeInTree(nodes[i].children, nodeKey, updates)) {
        return true;
      }
    }
    
    return false;
  };
  
  try {
    const updated = updateNodeInTree(updatedFileStructure, updatedNode.key, updatedNode);
    
    if (selectedNode.value && selectedNode.value.key === updatedNode.key) {
      selectedNode.value = { ...selectedNode.value, ...updatedNode };
    }
    
    if (selectedNode.value?.key === updatedNode.key || selectedNode.value?.key === updatedNode.parentId) {
      fetchNodeContents(selectedNode.value.key);
    }
    
    emit('update:file-structure', updatedFileStructure);
  } catch (error) {
    console.error('Erro ao atualizar nó na árvore:', error);
  }
  
  emit('refresh-tree');
  refreshCurrentFolder();
};

const handleFolderMoved = async (data) => {
  try {
    emit('refresh-tree');
    
    if (selectedNode.value?.key === data.movedFolder.key) {
      selectedNode.value = {
        ...selectedNode.value,
        parentFolderId: data.targetFolder.key,
        parentId: data.targetFolder.key
      };
      
      await updatePathSegments();
    }
    
    await loadCurrentFolderContents();
    
    if (selectedNode.value?.key === data.targetFolder.key) {
      await fetchNodeContents(selectedNode.value.key);
    }
    
    const originalParentId = data.movedFolder.parentId || data.movedFolder.parentFolderId;
    if (originalParentId && selectedNode.value?.key === originalParentId) {
      await fetchNodeContents(selectedNode.value.key);
    }
    
    emit('refresh-tree');
  } catch (error) {
    console.error('Error handling folder moved:', error);
    toast.showError(t('fileManager.errorMovingFolder'));
  }
};

// Handle item moved from drag and drop
const handleItemMoved = async (data) => {
  try {
    // Immediate local UI update - remove item from current view if it was moved out
    const originalParentId = data.originalParentId || data.movedItem.parentFolderId;
    
    // Remove from source folder if we're viewing it
    if (selectedNode.value?.key === originalParentId || 
        (!selectedNode.value?.key && !originalParentId)) {
      currentFiles.value = currentFiles.value.filter(file => file.id !== data.movedItem.id);
    }

    // If we're viewing the target folder, add the item immediately
    if (selectedNode.value?.key === data.targetFolder.id) {
      const updatedItem = {
        ...data.movedItem,
        parentFolderId: data.targetFolder.id,
        parentId: data.targetFolder.id
      };
      
      // Check if item already exists to avoid duplicates
      const existingIndex = currentFiles.value.findIndex(file => file.id === updatedItem.id);
      if (existingIndex === -1) {
        currentFiles.value = [...currentFiles.value, updatedItem];
      } else {
        // Update existing item
        currentFiles.value[existingIndex] = updatedItem;
      }
    }

    // Force immediate tree refresh
    emit('refresh-tree');

    // Use nextTick to ensure DOM updates before background refresh
    await nextTick();

    // Background refresh to ensure data consistency
    setTimeout(async () => {
      try {
        await loadCurrentFolderContents();
        emit('refresh-tree');
      } catch (error) {
        console.warn('Background refresh failed:', error);
      }
    }, 100);

  } catch (error) {
    console.error('Error handling item moved:', error);
    toast.showError(t('fileManager.moveError'));
  }
};

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const confirmResolve = ref(null)

// Modal control functions
const showConfirm = (message, title, confirmText = 'Excluir', cancelText = 'Cancelar', type = 'error') => {
  return new Promise((resolve) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title
    confirmModalConfirmText.value = confirmText
    confirmModalCancelText.value = cancelText
    confirmModalType.value = type
    confirmResolve.value = resolve
    showConfirmModal.value = true
  })
}

const handleConfirm = () => {
  if (confirmResolve.value) {
    confirmResolve.value(true)
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  if (confirmResolve.value) {
    confirmResolve.value(false)
  }
  showConfirmModal.value = false
}

const handleDeleteSelectedRequest = async (files) => {
  if (!files || files.length === 0) return;
  
  try {
    const confirmed = await showConfirm(
      `Tem certeza que deseja excluir ${files.length} ${files.length === 1 ? 'item selecionado' : 'itens selecionados'}?`,
      'Confirmar Exclusão'
    );

    if (confirmed) {
      try {
        for (const file of files) {
          if (file.type === 'FOLDER') {
            await fileManagerService.deleteFolder(file.id);
            emit('folder-deleted');
          } else {
            await fileManagerService.delete(file.id, 'ARCHIVE');
            emit('file-deleted', file);
          }

          currentFiles.value = currentFiles.value.filter(f => f.id !== file.id);
        }

        toast.showSuccess(`${files.length} ${files.length === 1 ? 'item excluído' : 'itens excluídos'} com sucesso`);

        if (selectedNode.value?.key) {
          await fetchNodeContents(selectedNode.value.key);
        }
      } catch (error) {
        console.error('Erro ao excluir itens:', error);
        toast.showError('Ocorreu um erro ao excluir os itens selecionados');
      }
    }
  } catch (error) {
    console.error('Erro ao processar exclusão em lote:', error);
    toast.showError('Ocorreu um erro ao processar a exclusão');
  }
};

const handleDeleteRequest = async (file) => {
  try {
    const confirmed = await showConfirm(
      `Tem certeza que deseja excluir ${file.type === 'FOLDER' ? 'a pasta' : 'o arquivo'} '${file.name}'?`,
      'Confirmar Exclusão'
    );

    if (confirmed) {
      try {
        if (file.type === 'FOLDER') {
          await fileManagerService.deleteFolder(file.id);

          if (selectedNode.value?.key === file.parentId) {
            currentFiles.value = currentFiles.value.filter(f => f.id !== file.id);
          }

          if (selectedNode.value?.key === file.id) {
            selectedNode.value = { key: '', label: 'Root', type: 'FOLDER' };
            currentPath.value = '';
          }

          emit('folder-deleted');

          if (selectedNode.value?.key) {
            await fetchNodeContents(selectedNode.value.key);
          } else {
            await loadCurrentFolderContents();
          }

          toast.showSuccess(t('fileManager.folderDeleted'));
        } else {
          await fileManagerService.delete(file.id, 'ARCHIVE');

          currentFiles.value = currentFiles.value.filter(f => f.id !== file.id);

          if (currentFile.value?.id === file.id) {
            currentFile.value = null;
            showFileEditor.value = false;
          }

          emit('file-deleted', file);

          toast.showSuccess(t('fileManager.fileDeleted'));
        }
      } catch (error) {
        console.error('Erro ao excluir item:', error);
        toast.showError(t('fileManager.deleteError'));
      }
    }
  } catch (error) {
    console.error('Erro ao excluir item:', error);
    toast.showError(t('fileManager.deleteError'));
  }
};

const handleNodeDeleteFromTree = async (node) => {
  const itemToDelete = {
    id: node.key,
    name: node.label || node.name,
    type: node.type === 'FOLDER' ? 'FOLDER' : 'ARCHIVE',
    parentId: node.parentId
  };
  await handleDeleteRequest(itemToDelete);
};

const isImageFile = (fileName) => {
  if (!fileName) return false;
  const imageExtensions = ['.png', '.jpg', '.jpeg', '.webp', '.gif', '.svg'];
  const lowerFileName = fileName.toLowerCase();
  return imageExtensions.some(ext => lowerFileName.endsWith(ext));
};

const getS3FileUrl = async (file) => {
  try {
    const envPath = props?.environment === 'DEVELOP' ? 'dev' : 'prod';
    const formatForS3 = (str) => str.replace(/\s+/g, '+');
    const s3FileName = formatForS3(file.name);
    
    if (!file.parentFolderId) {
      return `https://iluria-bucket-dev.s3.us-east-2.amazonaws.com/${file.storeId}/file-manager/${envPath}/${s3FileName}`;
    }
    
    const buildFolderPath = async (folderId) => {
      if (!folderId) return [];
      
      const folderDataArray = await fileManagerService.getById(props.environment, folderId);
      const folder = folderDataArray?.[0];
      
      if (!folder?.name) {
        throw new Error("Invalid folder data: Missing folder name");
      }
      
      let parentPath = [];
      if (folder.parentFolderId) {
        parentPath = await buildFolderPath(folder.parentFolderId);
      }
      
      return [...parentPath, formatForS3(folder.name)];
    };
    
    const folderPathArray = await buildFolderPath(file.parentFolderId);
    const folderPath = folderPathArray.join('/');
    
    return `https://iluria-bucket-dev.s3.us-east-2.amazonaws.com/${file.storeId}/file-manager/${envPath}/${folderPath}/${s3FileName}`;
  } catch (error) {
    console.error("Failed to get S3 file URL:", error);
    throw error;
  }
};

const handleFileClick = async (file) => {
  if (isImageFile(file.name)) {
    const imageUrl = await getS3FileUrl(file);
    currentImageUrl.value = imageUrl;
    showImagePreview.value = true;
  } 
};

const handleDownloadAll = async () => {
  try {
    const response = await fileManagerService.downloadRootFiles(props.environment);
    
    const blob = new Blob([response.data]);
    
    const url = window.URL.createObjectURL(blob);
    
    const link = document.createElement('a');
    link.href = url;
    
    link.setAttribute('download', `ILuriaFiles-${props.environment}.zip`);
    
    document.body.appendChild(link);
    link.click();
    
    window.URL.revokeObjectURL(url);
    document.body.removeChild(link);
  } catch (error) {
    console.error('Error downloading all files:', error);
    toast.showError(t('fileManager.downloadError'));
  }
};

const navigateTo = (path) => {

  if (path === -1 || path === '/' || path === '/Root') {
    currentPath.value = '/';
    selectedNode.value = {
      key: null,
      label: 'Root',
      type: 'FOLDER'
    };
    fetchNodeContents(undefined);
    return;
  }

  currentPath.value = path;
  const pathParts = path.split('/').filter(Boolean);

  // Remove "Root" from path parts if it exists
  if (pathParts[0] === 'Root') {
    pathParts.shift();
  }

  // If no parts left after removing Root, navigate to root
  if (pathParts.length === 0) {
    navigateTo('/');
    return;
  }

  const findNodeByPath = (nodes, parts, index = 0) => {
    if (index >= parts.length || !nodes) return null;

    const currentPart = parts[index];
    const node = nodes.find(n => n.label === currentPart || n.name === currentPart);

    if (!node) return null;
    if (index === parts.length - 1) return node;

    return findNodeByPath(node.children || [], parts, index + 1);
  };

  const targetNode = findNodeByPath(props.fileStructure, pathParts);

  if (targetNode) {
    selectedNode.value = targetNode;
    fetchNodeContents(targetNode.key);
  } else {
    console.warn('Node not found for path:', path);
    navigateTo('/');
  }
};

const loadCurrentFolderContents = async () => {
  if (selectedNode.value?.key) {
    await fetchNodeContents(selectedNode.value.key);
  } else {
    await initializeRoot();
  }
};

const refreshCurrentFolder = () => {
  loadCurrentFolderContents();
};

watch(() => props.environment, () => {
  if (selectedNode.value?.key) {
    fetchNodeContents(selectedNode.value.key);
  } else {
    initializeRoot();
  }
});

onMounted(() => {
  currentPath.value = '/R';
  selectedNode.value = {
    key: null,
    label: 'Root',
    type: 'FOLDER'
  };
  fetchNodeContents(undefined);
});

defineExpose({
  handleDeleteRequest
});
</script>
