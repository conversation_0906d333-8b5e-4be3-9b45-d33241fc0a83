<template>
  <div class="predefined-answer-editor-container">
    <!-- Header -->
    <IluriaHeader
      :title="isEditMode ? 'Editar Resposta Predefinida' : 'Nova Resposta Predefinida'"
      :subtitle="isEditMode ? 'Edite os dados da resposta predefinida' : 'Crie uma nova resposta predefinida para agilizar o atendimento'"
      :showCancel="true"
      :cancelText="'Voltar às Respostas Predefinidas'"
      :showSave="true"
      :saveText="isEditMode ? 'Atualizar Resposta' : 'Salvar Resposta'"
      @cancel-click="goBack"
      @save-click="saveAnswer"
    />

    <!-- Form -->
    <ViewContainer 
      :title="isEditMode ? 'Editar Resposta' : 'Nova Resposta'" 
      :icon="DocumentAttachmentIcon" 
      iconColor="green"
    >
      <form @submit.prevent="saveAnswer" class="form-content">
        <!-- Título -->
        <div class="form-field">
          <label class="field-label">
            Título <span class="required-mark">*</span>
          </label>
          <IluriaInputText 
            v-model="formData.title" 
            placeholder="Ex: Prazo de Entrega, Política de Troca, etc." 
            required
            :class="{ 'field-error': errors.title }"
          />
          <p v-if="errors.title" class="error-message">{{ errors.title }}</p>
          <p class="field-help">Este título aparecerá na lista de respostas predefinidas</p>
        </div>

        <!-- Conteúdo -->
        <div class="form-field">
          <label class="field-label">
            Conteúdo da Resposta <span class="required-mark">*</span>
          </label>
          <IluriaEditor 
            v-model="formData.answerText" 
            :show-mode-toggle="false" 
            height="300px"
            placeholder="Digite o conteúdo da resposta predefinida..."
            :class="{ 'field-error': errors.answerText }"
          />
          <p v-if="errors.answerText" class="error-message">{{ errors.answerText }}</p>
          <p class="field-help">Use formatação rica para criar respostas mais atrativas</p>
        </div>

        <!-- Informações adicionais para edição -->
        <ViewContainer 
          v-if="isEditMode && answerId" 
          title="Informações de Uso" 
          :icon="AnalyticsIcon" 
          iconColor="blue"
          class="usage-info"
        >
          <div class="usage-stats">
            <div class="usage-stat">
              <span class="stat-label">Vezes utilizada:</span>
              <span class="stat-value">{{ originalData?.usageCount || 0 }}</span>
            </div>
            <div class="usage-stat">
              <span class="stat-label">Criado em:</span>
              <span class="stat-value">{{ formatDate(originalData?.createdAt) }}</span>
            </div>
            <div v-if="originalData?.updatedAt && originalData.updatedAt !== originalData.createdAt" class="usage-stat">
              <span class="stat-label">Última edição:</span>
              <span class="stat-value">{{ formatDate(originalData?.updatedAt) }}</span>
            </div>
          </div>
        </ViewContainer>
      </form>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useToast } from '@/services/toast.service';
import questionsApi from '@/services/question.service';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaEditor from '@/components/editor/IluriaEditor.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { useTheme } from '@/composables/useTheme';
import {
  ArrowLeftIcon,
  DocumentAttachmentIcon,
  AnalyticsIcon,
  CheckmarkCircle02Icon
} from '@hugeicons-pro/core-stroke-rounded';

const router = useRouter();
const route = useRoute();
const toast = useToast();

// Initialize theme system
const { initTheme } = useTheme();
initTheme();

// Estado
const loading = ref(false);
const answerId = ref(route.params.id);
const isEditMode = computed(() => !!answerId.value);
const originalData = ref(null);
const errors = ref({});

// Form data
const formData = ref({
  title: '',
  answerText: ''
});

// Computed
const isFormValid = computed(() => {
  return formData.value.title.trim() && 
         formData.value.answerText.trim() && 
         Object.keys(errors.value).length === 0;
});

// Métodos
const validateForm = () => {
  errors.value = {};
  
  if (!formData.value.title.trim()) {
    errors.value.title = 'O título é obrigatório';
  }
  
  if (!formData.value.answerText.trim()) {
    errors.value.answerText = 'O conteúdo da resposta é obrigatório';
  }
  
  return Object.keys(errors.value).length === 0;
};

const stripHtml = (html) => {
  const tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || "";
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

const loadAnswerData = async () => {
  if (!isEditMode.value) return;
  
  loading.value = true;
  try {
    const { data } = await questionsApi.getPredefinedAnswerById(answerId.value);
    originalData.value = data;
    formData.value = {
      title: data.title,
      answerText: data.answerText
    };
  } catch (error) {
    console.error('Erro ao carregar resposta:', error);
    toast.showError('Erro ao carregar dados da resposta predefinida');
    goBack();
  } finally {
    loading.value = false;
  }
};

const saveAnswer = async () => {
  if (!validateForm()) return;
  
  loading.value = true;
  try {
    
    if (isEditMode.value) {
      await questionsApi.updatePredefinedAnswer(answerId.value, formData.value);
      toast.showSuccess('Resposta predefinida atualizada com sucesso!');
    } else {
      await questionsApi.createPredefinedAnswer(formData.value);
      toast.showSuccess('Resposta predefinida criada com sucesso!');
    }
    
    goBack();
  } catch (error) {
    console.error('Erro ao salvar resposta:', error);
    console.error('Error details:', error.response);
    toast.showError('Erro ao salvar resposta predefinida');
  } finally {
    loading.value = false;
  }
};

const goBack = () => {
  router.push({ name: 'predefined-answers' });
};

// Lifecycle
onMounted(() => {
  loadAnswerData();
});
</script>

<style scoped>
.predefined-answer-editor-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Form */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-label {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  display: flex;
  align-items: center;
  gap: 4px;
}

.required-mark {
  color: #dc2626;
  font-weight: 500;
}

.field-error {
  border-color: #dc2626 !important;
  box-shadow: 0 0 0 3px rgba(220, 38, 38, 0.1) !important;
}

.error-message {
  font-size: 13px;
  color: #dc2626;
  margin: 0;
  display: flex;
  align-items: center;
  gap: 4px;
}

.field-help {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* Usage Info */
.usage-info {
  margin: 16px 0;
}

.usage-stats {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 16px;
}

.usage-stat {
  display: flex;
  flex-direction: column;
  gap: 4px;
  padding: 12px;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.stat-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

.stat-value {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

/* Responsive */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: center;
  }
  
  .usage-stats {
    grid-template-columns: 1fr;
    gap: 12px;
  }
}

@media (max-width: 768px) {
  .predefined-answer-editor-container {
    padding: 16px;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 15px;
  }
  
  .usage-stat {
    padding: 8px;
  }
}

@media (max-width: 480px) {
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
  }
  
  .form-content {
    gap: 20px;
  }
  
  .form-field {
    gap: 6px;
  }
}

/* Form validation styles */
:deep(.p-form-invalid) {
  border-color: #dc2626 !important;
}

:deep(.p-form-error) {
  color: #dc2626;
  font-size: 13px;
  margin-top: 4px;
}
</style> 
