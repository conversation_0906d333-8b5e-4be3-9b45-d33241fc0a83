<template>
  <div class="space-y-4">
    <div class="gap-3 flex flex-col md:flex-row w-full">
      <div class="w-full">
        <IluriaFileUpload
          ref="fileUploadRef"
          :label="t('product.images')"
          :existingImages="form.images"
          @select="onImageSelect"
          @error="onImageUploadError"
          @remove-image="removeProductImage"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, inject } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaFileUpload from '@/components/iluria/form/IluriaFileUpload.vue';

const { t } = useI18n();

// Inject the product form data from parent component
const form = inject('productForm');

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Define emits
const emit = defineEmits(['select', 'error', 'remove-image']);

// File upload reference
const fileUploadRef = ref(null);

// Methods
const onImageSelect = (event) => {
  emit('select', event);
};

const onImageUploadError = (error) => {
  emit('error', error);
};

const removeProductImage = (index) => {
  emit('remove-image', index);
};

// Define validation rules
const validationRules = {
  images: {}
};

// Expose validation rules and file upload reference
defineExpose({ validationRules, fileUploadRef });
</script>
