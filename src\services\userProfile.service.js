import axios from 'axios';
import { API_URLS } from '../config/api.config';

class UserProfileService {
    #endpoint = API_URLS.USER_PROFILE_BASE_URL;

    /**
     * Obtém as configurações do perfil do usuário logado
     */
    async getUserProfileSettings() {
        try {
            const response = await axios.get(this.#endpoint);
            return response.data;
        } catch (error) {
            // Se for erro de autenticação ou servidor, não usar fallback
            if (error.response?.status === 401 || error.response?.status === 403) {
                throw error;
            }

            // Fallback temporário apenas para desenvolvimento
            const fallbackData = this.#getFallbackProfileData();
            return fallbackData;
        }
    }

    /**
     * Atualiza as configurações do perfil do usuário logado
     */
    async updateUserProfileSettings(profileData) {
        try {
            const response = await axios.post(this.#endpoint, profileData);
            return response.data;
        } catch (error) {
            // Se for erro de autenticação ou servidor, não usar fallback
            if (error.response?.status === 401 || error.response?.status === 403) {
                throw error;
            }

            // Fallback temporário apenas para desenvolvimento
            this.#saveFallbackProfileData(profileData);
            return profileData;
        }
    }

    /**
     * Atualiza a foto do perfil do usuário
     */
    async updateProfilePicture(formData) {
        try {
            const response = await axios.post(
                `${this.#endpoint}/picture`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Remove a foto do perfil do usuário
     */
    async removeProfilePicture() {
        try {
            const response = await axios.delete(`${this.#endpoint}/picture`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    /**
     * Obtém a URL da foto do perfil do usuário
     */
    async getProfilePictureUrl() {
        try {
            const response = await axios.get(`${this.#endpoint}/picture`);
            return response.data;
        } catch (error) {
            return null; // Sem foto de perfil
        }
    }

    /**
     * Métodos de fallback para desenvolvimento/teste
     */
    #getFallbackProfileData() {
        const saved = localStorage.getItem('user_profile_settings_fallback');
        if (saved) {
            return JSON.parse(saved);
        }

        // Dados padrão
        return {
            firstName: 'João',
            lastName: 'Silva',
            profilePictureUrl: null,
            active: true
        };
    }

    #saveFallbackProfileData(profileData) {
        // Adiciona timestamp para evitar problemas de cache
        const dataWithTimestamp = {
            ...profileData,
            lastUpdated: Date.now()
        };
        localStorage.setItem('user_profile_settings_fallback', JSON.stringify(dataWithTimestamp));
    }
}

export default new UserProfileService();
