<template>
  <div class="sidebar-initial">
    <!-- <PERSON><PERSON><PERSON><PERSON><PERSON> Página Inicial -->
    <div class="welcome-section">
      <div class="welcome-header">
        <h2>{{ $t('layoutEditor.sidebar.layoutEditor') }}</h2>
        <p><PERSON><PERSON> de navegação</p>
      </div>
    </div>

    <!-- <PERSON><PERSON> Principal de Navegação -->
    <div class="navigation-menu">
      
      <!-- Configuração da Página -->
      <div class="menu-item" @click="openPageSettings">
        <div class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
          </svg>
        </div>
        <div class="menu-content">
          <h3>{{ $t('layoutEditor.sidebar.pageConfiguration') }}</h3>
          <p>Configurações gerais da página, tema e layout</p>
        </div>
        <div class="menu-arrow">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </div>
      </div>

      <!-- Seções da Página -->
      <div class="menu-item" @click="showAllSections">
        <div class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"/>
          </svg>
        </div>
        <div class="menu-content">
          <h3>{{ $t('layoutEditor.sidebar.pageSections') }}</h3>
          <p>Gerencie as seções da sua página</p>
        </div>
        <div class="menu-arrow">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </div>
      </div>

      <!-- Adicionar Componente -->
      <div class="menu-item" @click="showAddComponentView">
        <div class="menu-icon">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6v6m0 0v6m0-6h6m-6 0H6"/>
          </svg>
        </div>
        <div class="menu-content">
          <h3>{{ $t('layoutEditor.sidebar.addComponent') }}</h3>
          <p>Adicione novos componentes à sua página</p>
        </div>
        <div class="menu-arrow">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
          </svg>
        </div>
      </div>

    </div>

    <!-- Informações de Ajuda -->
    <div class="help-section">
      <div class="help-card">
        <div class="help-icon">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
            <line x1="12" y1="17" x2="12.01" y2="17"/>
          </svg>
        </div>
        <div class="help-content">
          <h4>Dicas de Uso</h4>
          <ul>
            <li>Use "Configuração da Página" para definir tema e layout geral</li>
            <li>Em "Seções da Página" você pode editar componentes existentes</li>
            <li>Use "Adicionar Componente" para inserir novos elementos</li>
          </ul>
        </div>
      </div>
    </div>

  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  iframeDocument: {
    type: Object,
    default: null
  }
})

const emit = defineEmits(['navigate-to', 'component-select', 'section-select', 'section-highlight', 'page-settings-updated'])

// Ações de navegação
const openPageSettings = () => {
  emit('navigate-to', 'page-settings')
}

const showAllSections = () => {
  emit('navigate-to', 'home')
}

const showAddComponentView = () => {
  emit('navigate-to', 'add-component')
}
</script>

<style scoped>
.sidebar-initial {
  display: flex;
  flex-direction: column;
  height: 100%;
  overflow: hidden;
}

/* Seção de Boas-vindas */
.welcome-section {
  padding: 2rem 1.5rem 1.5rem 1.5rem;
  background: var(--iluria-color-container-bg);
  border-bottom: 1px solid var(--iluria-color-border);
  flex-shrink: 0;
}

.welcome-header {
  text-align: center;
}

.welcome-header h2 {
  margin: 0 0 0.5rem 0;
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
}

.welcome-header p {
  margin: 0;
  font-size: 1rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

/* Menu de Navegação */
.navigation-menu {
  flex: 1;
  padding: 1.5rem;
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1.25rem;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  color: inherit;
}

.menu-item:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary-border);
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.menu-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--iluria-color-primary-bg);
  border: 1px solid var(--iluria-color-primary-border);
  border-radius: 12px;
  color: var(--iluria-color-primary);
  flex-shrink: 0;
}

.menu-content {
  flex: 1;
  min-width: 0;
}

.menu-content h3 {
  margin: 0 0 0.25rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.4;
}

.menu-content p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

.menu-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  color: var(--iluria-color-text-secondary);
  flex-shrink: 0;
  transition: transform 0.2s ease;
}

.menu-item:hover .menu-arrow {
  color: var(--iluria-color-primary);
  transform: translateX(2px);
}

/* Seção de Ajuda */
.help-section {
  padding: 1.5rem;
  border-top: 1px solid var(--iluria-color-border);
  flex-shrink: 0;
}

.help-card {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-info-bg);
  border: 1px solid var(--iluria-color-info-border);
  border-radius: 8px;
}

.help-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--iluria-color-info);
  border-radius: 50%;
  color: white;
  flex-shrink: 0;
}

.help-content {
  flex: 1;
  min-width: 0;
}

.help-content h4 {
  margin: 0 0 0.75rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.help-content ul {
  margin: 0;
  padding: 0;
  list-style: none;
}

.help-content li {
  margin: 0 0 0.5rem 0;
  font-size: 0.8rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  position: relative;
  padding-left: 1rem;
}

.help-content li:before {
  content: '•';
  position: absolute;
  left: 0;
  color: var(--iluria-color-info);
  font-weight: bold;
}

.help-content li:last-child {
  margin-bottom: 0;
}

/* Responsividade */
@media (max-width: 768px) {
  .welcome-section {
    padding: 1.5rem 1rem 1rem 1rem;
  }
  
  .navigation-menu {
    padding: 1rem;
  }
  
  .menu-item {
    padding: 1rem;
  }
  
  .menu-icon {
    width: 40px;
    height: 40px;
  }
  
  .help-section {
    padding: 1rem;
  }
  
  .help-card {
    flex-direction: column;
    text-align: center;
  }
}
</style> 
