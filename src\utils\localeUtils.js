/**
 * Utility functions for locale conversion between frontend and backend
 */

/**
 * Convert backend language format to i18n locale format
 * @param {string} backendLanguage - Language from backend (pt-br, en)
 * @returns {string} - i18n locale (pt, en)
 */
export const convertToI18nLocale = (backendLanguage) => {
  return backendLanguage === 'pt-br' ? 'pt' : backendLanguage
}

/**
 * Convert i18n locale format to backend language format
 * @param {string} i18nLanguage - i18n locale (pt, en)
 * @returns {string} - Backend language (pt-br, en)
 */
export const convertToBackendLocale = (i18nLanguage) => {
  return i18nLanguage === 'pt' ? 'pt-br' : i18nLanguage
}

/**
 * Get supported locales for the application
 * @returns {Object} - Object with backend and i18n locale mappings
 */
export const getSupportedLocales = () => {
  return {
    backend: ['pt-br', 'en'],
    i18n: ['pt', 'en'],
    mapping: {
      'pt-br': 'pt',
      'en': 'en',
      'pt': 'pt-br'
    }
  }
}

/**
 * Validate if a language is supported by the backend
 * @param {string} language - Language to validate
 * @returns {boolean} - True if supported
 */
export const isValidBackendLanguage = (language) => {
  const supported = getSupportedLocales().backend
  return supported.includes(language)
}

/**
 * Validate if a locale is supported by i18n
 * @param {string} locale - Locale to validate
 * @returns {boolean} - True if supported
 */
export const isValidI18nLocale = (locale) => {
  const supported = getSupportedLocales().i18n
  return supported.includes(locale)
}
