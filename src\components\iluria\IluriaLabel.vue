<template>
  <label :for="for" class="cursor-pointer iluria-label" :class="labelClass">
    <slot></slot>
  </label>
</template>

<script setup>
const props = defineProps({
  for: {
    type: String,
    required: false,
    default: ''
  },
  labelClass: {
    type: String,
    default: ''
  }
});
</script>

<style scoped>
.iluria-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}
</style>

<style scoped>
.iluria-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}
</style>
