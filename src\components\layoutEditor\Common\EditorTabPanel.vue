<template>
  <div v-if="isActive" class="tab-panel">
    <slot />
  </div>
</template>

<script setup>
import { computed } from 'vue'

const props = defineProps({
  value: {
    type: String,
    required: true
  },
  activeTab: {
    type: String,
    required: true
  }
})

const isActive = computed(() => props.value === props.activeTab)
</script>

<style scoped>
.tab-panel {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}
</style> 
