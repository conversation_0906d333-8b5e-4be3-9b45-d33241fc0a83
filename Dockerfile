# Multi-stage build
# Build stage
FROM node:20 as build-stage

# Set working directory
WORKDIR /app

# Copy package.json and package-lock.json
COPY package*.json ./
COPY .npmrc ./

# Install dependencies with a strategy to avoid Rollup issues
RUN npm install --no-package-lock --force

# Copy project files
COPY . .

# Set environment variables for the build
ARG VITE_AUTH_API_URL
ARG VITE_STORE_API_URL
ENV VITE_AUTH_API_URL=${VITE_AUTH_API_URL}
ENV VITE_STORE_API_URL=${VITE_STORE_API_URL}

# Modify vite.config.js to disable rollup native
RUN echo "process.env.ROLLUP_NATIVE = 'false';" > rollup-config.js
RUN cat rollup-config.js vite.config.js > temp.js && mv temp.js vite.config.js

# Build the app
RUN ROLLUP_NATIVE=false npm run build

# Production stage
FROM nginx:stable-alpine as production-stage

# Copy built files from build stage to nginx serve directory
COPY --from=build-stage /app/dist /usr/share/nginx/html

# Add nginx configuration to handle SPA routing
RUN echo 'server { listen 80; location / { root /usr/share/nginx/html; index index.html; try_files $uri $uri/ /index.html; } }' > /etc/nginx/conf.d/default.conf

# Expose port 80
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
