<template>
    <div class="input-list-wrapper">
        <div v-for="(value, index) in inputs" :key="index" class="input-item">
            <IluriaInputText type="money" v-model.number="inputs[index]" class="number-input" :prefix="('R$')"
                :max="(10000)" />
            <IluriaButton @click="removeInput(index)" :disabled="inputs.length === 1" :hugeIcon="Delete01Icon"
                class="delete-btn" />
        </div>

        <IluriaButton @click="addInput" :disabled="inputs.length >= 10" :hugeIcon="PlusSignSquareIcon" class="add-btn">
            Adicionar denominação
        </IluriaButton>
    </div>
</template>

<script setup>
import { computed } from 'vue'
import IluriaButton from '../iluria/IluriaButton.vue'
import { PlusSignSquareIcon, Delete01Icon } from '@hugeicons-pro/core-stroke-rounded'
import IluriaInputText from '../iluria/form/IluriaInputText.vue'
const props = defineProps({
    modelValue: {
        type: Array,
        required: true
    }
})
const emit = defineEmits(['update:modelValue'])
const inputs = computed({
    get: () => props.modelValue,
    set: (val) => emit('update:modelValue', val)
})
function addInput() {
    if (inputs.value.length < 10) {
        inputs.value.push(0)
    }
}
function removeInput(index) {
    if (inputs.value.length > 1) {
        inputs.value.splice(index, 1)
    }
}
</script>

<style scoped>
.input-list-wrapper {
    margin: auto;
    display: flex;
    flex-direction: column;
    gap: 10px;
}
.input-item {
    display: flex;
    align-items: center;
    gap: 10px;
    width: 100%;
}
.number-input {
    flex: 1;
    padding: 5px;
    width: 100%;
}
.delete-btn {
    padding: 5px;
    cursor: pointer;
}
.add-btn {
    width: 20%;
    padding: 8px;
    margin-top: 10px;
    cursor: pointer;
}
</style>