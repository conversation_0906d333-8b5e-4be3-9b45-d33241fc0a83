/**
 * Utility functions to convert between HTML and Editor.js format
 */

/**
 * Convert HTML string to Editor.js blocks format
 * @param {string} html - HTML string to convert
 * @returns {Object} Editor.js data object with blocks
 */
export function htmlToEditorJS(html) {
  if (!html || html.trim() === '') {
    return {
      time: new Date().getTime(),
      blocks: []
    };
  }

  const parser = new DOMParser();
  const doc = parser.parseFromString(html, 'text/html');
  const blocks = [];

  // Process all child nodes of body
  const processNode = (node) => {
    if (node.nodeType === Node.TEXT_NODE) {
      const text = node.textContent.trim();
      if (text) {
        blocks.push({
          type: 'paragraph',
          data: {
            text: text
          }
        });
      }
      return;
    }

    if (node.nodeType !== Node.ELEMENT_NODE) return;

    const tagName = node.tagName.toLowerCase();

    switch (tagName) {
      case 'p':
        const paragraphText = node.innerHTML.trim();
        if (paragraphText) {
          blocks.push({
            type: 'paragraph',
            data: {
              text: paragraphText
            }
          });
        }
        break;

      case 'h1':
      case 'h2':
      case 'h3':
      case 'h4':
      case 'h5':
      case 'h6':
        blocks.push({
          type: 'header',
          data: {
            text: node.textContent,
            level: parseInt(tagName.charAt(1))
          }
        });
        break;

      case 'ul':
      case 'ol':
        const items = [];
        node.querySelectorAll('li').forEach(li => {
          items.push(li.innerHTML);
        });
        if (items.length > 0) {
          blocks.push({
            type: 'list',
            data: {
              style: tagName === 'ol' ? 'ordered' : 'unordered',
              items: items
            }
          });
        }
        break;

      case 'blockquote':
        blocks.push({
          type: 'quote',
          data: {
            text: node.textContent,
            caption: '',
            alignment: 'left'
          }
        });
        break;

      case 'pre':
      case 'code':
        blocks.push({
          type: 'code',
          data: {
            code: node.textContent
          }
        });
        break;

      case 'img':
        blocks.push({
          type: 'image',
          data: {
            url: node.src || node.getAttribute('src'),
            caption: node.alt || node.getAttribute('alt') || '',
            withBorder: false,
            withBackground: false,
            stretched: false
          }
        });
        break;

      case 'hr':
        blocks.push({
          type: 'delimiter',
          data: {}
        });
        break;

      case 'div':
        // Check if it's a product grid
        if (node.classList.contains('product-grid-tool')) {
          const productData = parseProductGrid(node);
          if (productData) {
            blocks.push(productData);
          }
        } else {
          // Process div children
          node.childNodes.forEach(child => processNode(child));
        }
        break;

      case 'table':
        const tableData = parseTable(node);
        if (tableData) {
          blocks.push(tableData);
        }
        break;

      default:
        // For other tags, process their children
        if (node.childNodes.length > 0) {
          node.childNodes.forEach(child => processNode(child));
        } else if (node.textContent.trim()) {
          blocks.push({
            type: 'paragraph',
            data: {
              text: node.innerHTML
            }
          });
        }
        break;
    }
  };

  // Process all nodes in body
  doc.body.childNodes.forEach(node => processNode(node));

  return {
    time: new Date().getTime(),
    blocks: blocks
  };
}

/**
 * Parse product grid HTML back to Editor.js format
 */
function parseProductGrid(gridNode) {
  const products = [];
  const productCards = gridNode.querySelectorAll('.product-card');
  
  productCards.forEach(card => {
    const title = card.querySelector('.product-title')?.textContent || '';
    const description = card.querySelector('.product-description')?.textContent || '';
    const button = card.querySelector('.product-button');
    const img = card.querySelector('.product-image img');
    const rating = card.querySelector('.product-rating');
    
    // Count filled stars
    const filledStars = (rating?.textContent.match(/★/g) || []).length;
    
    products.push({
      title,
      description,
      imageUrl: img?.src || '',
      rating: filledStars,
      buttonText: button?.textContent || '',
      buttonUrl: button?.href || '#'
    });
  });

  if (products.length === 0) return null;

  // Try to extract colors from inline styles
  const firstCard = productCards[0];
  const firstButton = firstCard?.querySelector('.product-button');
  const firstTitle = firstCard?.querySelector('.product-title');
  const firstDescription = firstCard?.querySelector('.product-description');
  const firstRating = firstCard?.querySelector('.product-rating');

  return {
    type: 'productGrid',
    data: {
      products,
      cardColor: firstCard?.style.backgroundColor || '#ffffff',
      buttonColor: firstButton?.style.backgroundColor || '#1f2937',
      titleColor: firstTitle?.style.color || '#1f2937',
      descriptionColor: firstDescription?.style.color || '#6b7280',
      ratingColor: firstRating?.style.color || '#f59e0b',
      titleFontSize: parseFloat(firstTitle?.style.fontSize) || '1.125',
      descriptionFontSize: parseFloat(firstDescription?.style.fontSize) || '0.875'
    }
  };
}

/**
 * Parse table HTML to Editor.js format
 */
function parseTable(tableNode) {
  const content = [];
  const rows = tableNode.querySelectorAll('tr');
  
  rows.forEach(row => {
    const rowData = [];
    row.querySelectorAll('td, th').forEach(cell => {
      rowData.push(cell.innerHTML);
    });
    if (rowData.length > 0) {
      content.push(rowData);
    }
  });

  if (content.length === 0) return null;

  return {
    type: 'table',
    data: {
      withHeadings: tableNode.querySelector('th') !== null,
      content: content
    }
  };
}

/**
 * Convert Editor.js data to HTML string
 * This is just a wrapper around the existing edjsHTML parser
 * @param {Object} editorData - Editor.js data object
 * @returns {string} HTML string
 */
export function editorJSToHTML(editorData) {
  // This will be handled by the existing edjsHTML parser in ContentEditor
  // We just need to ensure the data is valid
  if (!editorData || !editorData.blocks) {
    return '';
  }
  
  // Return a marker that tells ContentEditor to use its internal parser
  return { useInternalParser: true, data: editorData };
} 