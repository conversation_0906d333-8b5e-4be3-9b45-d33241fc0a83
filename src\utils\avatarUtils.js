// Gera um avatar padrão baseado no nome ou tipo
export const generateDefaultAvatar = (name = '', type = 'user', shape = 'round', size = 40) => {
  // Cores padrão para avatares
  const colors = [
    '#3B82F6', // blue
    '#10B981', // emerald
    '#8B5CF6', // violet
    '#F59E0B', // amber
    '#EF4444', // red
    '#06B6D4', // cyan
    '#84CC16', // lime
    '#F97316'  // orange
  ];

  // Pega as iniciais do nome
  const getInitials = (fullName) => {
    if (!fullName) return type === 'product' ? 'P' : 'U';
    
    return fullName
      .split(' ')
      .filter(word => word.length > 0)
      .slice(0, 2)
      .map(word => word[0])
      .join('')
      .toUpperCase();
  };

  // Seleciona cor baseada no nome
  const getColor = (text) => {
    let hash = 0;
    for (let i = 0; i < text.length; i++) {
      hash = text.charCodeAt(i) + ((hash << 5) - hash);
    }
    return colors[Math.abs(hash) % colors.length];
  };

  const initials = getInitials(name);
  const backgroundColor = getColor(name || type);
  
  // Define border radius baseado no shape e size
  const borderRadius = shape === 'square' ? Math.max(2, size * 0.1) : size / 2;
  const fontSize = Math.max(10, size * 0.35);
  const textY = size / 2 + fontSize / 3;
  
  // Gera SVG do avatar
  const svgAvatar = `
    <svg width="${size}" height="${size}" viewBox="0 0 ${size} ${size}" xmlns="http://www.w3.org/2000/svg">
      <rect width="${size}" height="${size}" fill="${backgroundColor}" rx="${borderRadius}"/>
      <text 
        x="${size / 2}" 
        y="${textY}" 
        text-anchor="middle" 
        fill="white" 
        font-family="Arial, sans-serif" 
        font-size="${fontSize}" 
        font-weight="600"
      >
        ${initials}
      </text>
    </svg>
  `;

  // Converte SVG para data URL
  return `data:image/svg+xml;base64,${btoa(svgAvatar)}`;
};

// Gera avatar específico para produtos
export const generateProductAvatar = (productName = '') => {
  return generateDefaultAvatar(productName, 'product');
};

// Gera avatar específico para clientes
export const generateCustomerAvatar = (customerName = '') => {
  return generateDefaultAvatar(customerName, 'user');
};

// Gera avatar específico para usuários/membros da equipe
export const generateUserAvatar = (userName = '', size = 40, shape = 'round') => {
  return generateDefaultAvatar(userName, 'user', shape, size);
};

// Alias para compatibilidade - função que estava sendo importada antes
export const generateAvatar = (userName = '') => {
  return generateDefaultAvatar(userName, 'user');
};

// Hook para tratar erro de imagem e usar avatar padrão
export const handleImageError = (event, fallbackName = '', type = 'user', shape = 'round', size = 40) => {
  const img = event.target;
  const avatar = generateDefaultAvatar(fallbackName, type, shape, size);
  img.src = avatar;
  img.onerror = null; // Evita loop infinito
};