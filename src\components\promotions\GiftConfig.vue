<template>
  <div class="mb-6">
    <IluriaRadioGroup
      id="gift-type"
      v-model="giftType"
      :options="[
        { value: 'SIMPLIFIED', label: t('promotions.editor.simplifiedGift') },
        { value: 'PRODUCT', label: t('promotions.editor.productGift') }
      ]"
      :label="t('promotions.editor.giftType')"
      direction="horizontal"
      @update:modelValue="setGiftType"
      class="mb-6"
    />

    <!-- Gift Tiers -->
    <div v-if="giftType === 'SIMPLIFIED'">
      <div v-for="(tier, index) in giftTiers" :key="index" class="flex items-center gap-3 mb-4">
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.minOrderValue') }}</IluriaLabel>
          <IluriaInputText
            v-model.number="tier.minValue"
            type="number"
            :min="0"
            :step="0.01"
            prefix="R$ "
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.giftDescription') }}</IluriaLabel>
          <IluriaInputText
            v-model="tier.description"
            type="text"
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex items-end">
          <IluriaButton
            size="small"
            color="ghost"
            :hugeIcon="Delete02Icon"
            @click="removeTier(index)"
            class="p-2"
            :title="'Remover brinde'"
          />
        </div>
      </div>
    </div>

    <div v-else>
      <div v-for="(tier, index) in giftTiers" :key="index" class="flex items-center gap-3 mb-4">
        <div class="flex-1">
          <IluriaLabel>{{ t('promotions.editor.minOrderValue') }}</IluriaLabel>
          <IluriaInputText
            v-model.number="tier.minValue"
            type="number"
            :min="0"
            :step="0.01"
            prefix="R$ "
            class="w-full"
            @update:modelValue="updateTier(index)"
          />
        </div>
        <div class="flex-1">
          <IluriaLabel for="product-name-${index}">{{ t('promotions.editor.product') }}</IluriaLabel>
          <div class="relative">
            <IluriaInputText
              :id="`product-name-${index}`"
              v-model="tier.productName"
              type="text"
              class="w-full cursor-pointer"
              readonly
              @click="openProductSelector(index)"
            />
            <input type="hidden" v-model="tier.productId" />
          </div>
        </div>
        <div class="flex items-end">
          <IluriaButton
            size="small"
            color="ghost"
            :hugeIcon="Delete02Icon"
            @click="removeTier(index)"
            class="p-2"
            :title="'Remover brinde'"
          />
        </div>
      </div>
    </div>

    <!-- Add New Tier Button -->
    <IluriaButton
      v-if="giftTiers.length < 5"
      color="primary"
      variant="outline"
      :hugeIcon="PlusSignIcon"
      @click="addTier"
      class="mt-2"
    >
      {{ t('promotions.editor.addGift') }}
    </IluriaButton>

    <!-- Product Selector Modal -->
    <ProductSelectorModal
      :visible="showProductSelector"
      @update:visible="closeProductSelector"
      @select="selectProduct"
      :multiSelect="false"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import ProductSelectorModal from '@/components/products/ProductSelectorModal.vue';
import { Delete02Icon, PlusSignIcon } from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const giftType = ref('SIMPLIFIED');
const giftTiers = ref([
  { minOrderValue: 0, description: '', productId: null, productName: '' }
]);

const showProductSelector = ref(false);
const selectedTierIndex = ref(0);

const setGiftType = (type) => {
  giftType.value = type;
  updateModelValue();
};

const addTier = () => {
  if (giftTiers.value.length < 5) {
    const lastTier = giftTiers.value[giftTiers.value.length - 1];
    const newTier = {
      minValue: lastTier.minValue + 50 || 50,
      minOrderValue: lastTier.minOrderValue + 50 || 50,
      description: '',
      productId: null,
      productName: '',
      sku: '',
      price: 0,
      imageUrl: '',
      variation: null,
      position: giftTiers.value.length
    };
    giftTiers.value.push(newTier);
    updateModelValue();
  }
};

const removeTier = (index) => {
  if (giftTiers.value.length > 1) {
    giftTiers.value.splice(index, 1);
    updateModelValue();
  }
};

const updateTier = (index) => {
  const tier = giftTiers.value[index];
  tier.minOrderValue = Math.max(0, tier.minOrderValue);
  
  updateModelValue();
};

const openProductSelector = (index) => {
  selectedTierIndex.value = index;
  showProductSelector.value = true;
};

const closeProductSelector = (value) => {
  showProductSelector.value = value;
  if (!value) {
    showProductSelector.value = false;
  }
};

const selectProduct = (products) => {
  if (Array.isArray(products) && products.length > 0) {
    const product = products[0];
    const tier = giftTiers.value[selectedTierIndex.value];
    tier.productId = product.id;
    tier.productName = product.name || product.productName;
    tier.sku = product.sku || '';
    tier.price = product.price || 0;
    tier.imageUrl = product.imageUrl || '';
    tier.variation = product.variation || null;
    
    giftTiers.value = [...giftTiers.value];
    updateModelValue();
  }
  showProductSelector.value = false;
};

const updateModelValue = () => {
  try {
    isUpdatingFromModelChange = true;
    props.modelValue.giftType = giftType.value;
    props.modelValue.subType = giftType.value;
    props.modelValue.giftTiers = JSON.parse(JSON.stringify(giftTiers.value));
    emit('update:modelValue', props.modelValue);
  } finally {
    isUpdatingFromModelChange = false;
  }
};

onMounted(() => {
  let typeValue = props.modelValue.subType || props.modelValue.giftType || 'SIMPLIFIED';

  giftType.value = typeValue;
  props.modelValue.giftType = typeValue;
  props.modelValue.subType = typeValue;
  
  if (props.modelValue.giftTiers && props.modelValue.giftTiers.length > 0) {
    giftTiers.value = props.modelValue.giftTiers.map((tier, index) => ({
      minValue: tier.minValue || tier.minOrderValue || 0,
      minOrderValue: tier.minValue || tier.minOrderValue || 0,
      description: tier.description || '',
      productId: tier.productId || null,
      productName: tier.productName || '',
      sku: tier.sku || '',
      price: tier.price || 0,
      imageUrl: tier.imageUrl || '',
      variation: tier.variation || null,
      position: tier.position || index
    }));
  }
});


let isUpdatingFromModelChange = false;

watch([() => props.modelValue.giftType, () => props.modelValue.subType], ([newGiftType, newSubType]) => {
  if (isUpdatingFromModelChange) return;
  
  try {
    isUpdatingFromModelChange = true;
    
    let typeValue = newSubType || newGiftType || 'SIMPLIFIED';
    if (typeValue !== 'SIMPLIFIED' && typeValue !== 'PRODUCT') {
      typeValue = 'SIMPLIFIED';
    }
    
    if (typeValue !== giftType.value) {
      giftType.value = typeValue;
      props.modelValue.giftType = typeValue;
      props.modelValue.subType = typeValue;
    }
  } finally {
    isUpdatingFromModelChange = false;
  }
}, { deep: true });

watch(() => props.modelValue.giftTiers, (newVal) => {
  if (isUpdatingFromModelChange) return;
  
  try {
    isUpdatingFromModelChange = true;
    if (newVal && JSON.stringify(newVal) !== JSON.stringify(giftTiers.value)) {
      giftTiers.value = JSON.parse(JSON.stringify(newVal));
    }
  } finally {
    isUpdatingFromModelChange = false;
  }
}, { deep: true });
</script>
