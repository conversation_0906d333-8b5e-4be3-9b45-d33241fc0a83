<template>
  <div>
    <label :for="id" v-if="label" class="date-picker-label">
      {{ label }}
    </label>
    <div class="iluria-datepicker-container relative">
      <DatePicker 
        :id="id" 
        :name="name || id" 
        v-model="model" 
        :disabled="disabled"
        :placeholder="placeholder"
        :required="required"
        :showIcon="false"
        :dateFormat="dateFormat"
        :minDate="minDate"
        :maxDate="maxDate"
        :showButtonBar="showButtonBar"
        :showTime="showTime"
        :hourFormat="hourFormat"
        :unstyled="false"
        class="w-full block"
        :class="[inputClass, 
              formContext?.invalid 
              ? 'bg-red-100 outline-red-300 outline-2' 
              : 'bg-[var(--iluria-color-7)] outline-transparent focus:outline-[var(--iluria-color-4)] focus:outline-2']"
      />
      <button 
        type="button" 
        @click="openCalendar"
        class="iluria-datepicker-icon"
      >
        <i class="pi pi-calendar"></i>
      </button>
    </div>
    <Message v-if="formContext && formContext?.invalid" severity="error" size="small" variant="simple"
      class="mt-1">
      {{ formContext?.error?.message }} 
    </Message>
  </div>
</template>

<script setup>
import { DatePicker, Message } from 'primevue';
import { inject, ref } from 'vue';

const model = defineModel();
const datePickerRef = ref(null);

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: null
  },
  disabled: {
    type: Boolean,
    default: false
  },
  placeholder: {
    type: String,
    default: null
  },
  required: {
    type: Boolean,
    default: false
  },
  showIcon: {
    type: Boolean,
    default: true
  },
  dateFormat: {
    type: String,
    default: 'dd/mm/yy'
  },
  minDate: {
    type: Date,
    default: null
  },
  maxDate: {
    type: Date,
    default: null
  },
  showButtonBar: {
    type: Boolean,
    default: true
  },
  showTime: {
    type: Boolean,
    default: false
  },
  hourFormat: {
    type: String,
    default: '24'
  },
  inputClass: {
    type: String,
    default: ''
  }
});

const formContext = inject('formContext', null);

function openCalendar() {
  // Encontrar o elemento do DatePicker e simular um clique
  const datePickerElement = document.getElementById(props.id);
  if (datePickerElement) {
    datePickerElement.click();
  }
}
</script>

<style scoped>
.date-picker-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
}

.iluria-datepicker-container {
  position: relative;
  width: 100%;
}

.iluria-datepicker-icon {
  position: absolute;
  right: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  background: transparent;
  border: none;
  color: #6b7280;
  width: 2rem;
  height: 2rem;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  z-index: 2;
}

.iluria-datepicker-icon:hover {
  color: var(--iluria-color-4);
}

.iluria-datepicker-icon:focus {
  outline: none;
  color: var(--iluria-color-primary);
}

:deep(.p-inputtext) {
  padding-right: 2.5rem !important;
  background: var(--iluria-color-container-bg) !important;
  border-color: var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
}

:deep(.p-inputtext:focus) {
  border-color: var(--iluria-color-primary) !important;
  box-shadow: 0 0 0 1px var(--iluria-color-primary) !important;
}

/* DatePicker Popup Styles */
:deep(.p-datepicker) {
  background: var(--iluria-color-container-bg) !important;
  border: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
  border-radius: 8px !important;
}

/* Header with navigation */
:deep(.p-datepicker .p-datepicker-header) {
  background: var(--iluria-color-sidebar-bg) !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
  padding: 12px !important;
  border-radius: 8px 8px 0 0 !important;
}

:deep(.p-datepicker .p-datepicker-title) {
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600 !important;
}

:deep(.p-datepicker .p-datepicker-prev),
:deep(.p-datepicker .p-datepicker-next) {
  color: var(--iluria-color-text-secondary) !important;
  background: transparent !important;
  border: none !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  transition: all 0.2s ease !important;
}

:deep(.p-datepicker .p-datepicker-prev:hover),
:deep(.p-datepicker .p-datepicker-next:hover) {
  background: var(--iluria-color-hover) !important;
  color: var(--iluria-color-text-primary) !important;
}

/* Calendar table */
:deep(.p-datepicker .p-datepicker-calendar) {
  background: var(--iluria-color-container-bg) !important;
}

:deep(.p-datepicker .p-datepicker-calendar thead tr th) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-secondary) !important;
  font-weight: 600 !important;
  padding: 8px !important;
  border: none !important;
}

:deep(.p-datepicker .p-datepicker-calendar tbody tr td) {
  border: none !important;
  padding: 2px !important;
}

:deep(.p-datepicker .p-datepicker-calendar tbody tr td > span) {
  color: var(--iluria-color-text-primary) !important;
  background: transparent !important;
  width: 32px !important;
  height: 32px !important;
  border-radius: 6px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

:deep(.p-datepicker .p-datepicker-calendar tbody tr td > span:hover) {
  background: var(--iluria-color-hover) !important;
  transform: scale(1.05) !important;
}

:deep(.p-datepicker .p-datepicker-calendar tbody tr td.p-datepicker-other-month > span) {
  color: var(--iluria-color-text-muted) !important;
}

:deep(.p-datepicker .p-datepicker-calendar tbody tr td.p-datepicker-today > span) {
  background: var(--iluria-color-primary-light) !important;
  color: var(--iluria-color-primary-dark) !important;
  font-weight: 600 !important;
  border: 2px solid var(--iluria-color-primary) !important;
}

:deep(.p-datepicker .p-datepicker-calendar tbody tr td.p-datepicker-selected > span) {
  background: var(--iluria-color-primary) !important;
  color: white !important;
  font-weight: 600 !important;
  box-shadow: var(--iluria-shadow-sm) !important;
}

/* Time picker section */
:deep(.p-datepicker .p-timepicker) {
  background: var(--iluria-color-surface) !important;
  border-top: 1px solid var(--iluria-color-border) !important;
  padding: 16px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  gap: 8px !important;
}

:deep(.p-datepicker .p-timepicker .p-hour-picker),
:deep(.p-datepicker .p-timepicker .p-minute-picker) {
  background: var(--iluria-color-container-bg) !important;
  border: 1px solid var(--iluria-color-border) !important;
  border-radius: 6px !important;
  color: var(--iluria-color-text-primary) !important;
  padding: 8px 12px !important;
  font-weight: 500 !important;
}

:deep(.p-datepicker .p-timepicker .p-separator) {
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600 !important;
  font-size: 16px !important;
}

:deep(.p-datepicker .p-timepicker .p-link) {
  background: var(--iluria-color-primary) !important;
  border: 1px solid var(--iluria-color-primary) !important;
  color: white !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 4px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

:deep(.p-datepicker .p-timepicker .p-link:hover) {
  background: var(--iluria-color-primary-hover) !important;
  border-color: var(--iluria-color-primary-hover) !important;
  transform: scale(1.1) !important;
}

/* Button bar */
:deep(.p-datepicker .p-datepicker-buttonbar) {
  background: var(--iluria-color-surface) !important;
  border-top: 1px solid var(--iluria-color-border) !important;
  padding: 12px 16px !important;
  display: flex !important;
  justify-content: space-between !important;
  border-radius: 0 0 8px 8px !important;
}

:deep(.p-datepicker .p-datepicker-buttonbar .p-button) {
  padding: 8px 16px !important;
  border-radius: 6px !important;
  font-weight: 500 !important;
  transition: all 0.2s ease !important;
}

:deep(.p-datepicker .p-datepicker-buttonbar .p-button.p-button-text) {
  background: transparent !important;
  border: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text-secondary) !important;
}

:deep(.p-datepicker .p-datepicker-buttonbar .p-button.p-button-text:hover) {
  background: var(--iluria-color-hover) !important;
  color: var(--iluria-color-text-primary) !important;
}

:deep(.p-datepicker .p-datepicker-buttonbar .p-button:not(.p-button-text)) {
  background: var(--iluria-color-primary) !important;
  border: 1px solid var(--iluria-color-primary) !important;
  color: white !important;
}

:deep(.p-datepicker .p-datepicker-buttonbar .p-button:not(.p-button-text):hover) {
  background: var(--iluria-color-primary-hover) !important;
  border-color: var(--iluria-color-primary-hover) !important;
  transform: translateY(-1px) !important;
  box-shadow: var(--iluria-shadow-md) !important;
}

/* Input field improvements */
:deep(.p-inputtext:hover) {
  border-color: var(--iluria-color-primary-light) !important;
}

:deep(.p-inputtext:disabled) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-muted) !important;
  border-color: var(--iluria-color-border) !important;
}

/* Panel overlay */
:deep(.p-datepicker-panel) {
  background: var(--iluria-color-container-bg) !important;
  border: 1px solid var(--iluria-color-border) !important;
  box-shadow: var(--iluria-shadow-lg) !important;
  border-radius: 8px !important;
}

/* Time picker inputs */
:deep(.p-datepicker .p-timepicker input) {
  background: var(--iluria-color-container-bg) !important;
  border: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
  border-radius: 6px !important;
}

:deep(.p-datepicker .p-timepicker input:focus) {
  border-color: var(--iluria-color-primary) !important;
  box-shadow: 0 0 0 1px var(--iluria-color-primary) !important;
}
</style>
