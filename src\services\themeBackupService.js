import axios from 'axios'
import { API_URLS } from '@/config/api.config'

/**
 * Serviço para gerenciamento de backups de temas
 */
class ThemeBackupService {
  #endpoint = `${API_URLS.THEME_BASE_URL}`

  constructor() {
 
  }
  
  /**
   * Lista todos os backups de uma loja
   */
  async getBackups() {
    try {
      const response = await axios.get(`${this.#endpoint}/backups`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar backups:', error)
      throw error
    }
  }

  /**
   * Lista backups de um tema específico
   */
  async getBackupsByTheme(themeId) {
    try {
      const url = `${this.#endpoint}/${themeId}/backups`
    

      const response = await axios.get(url)
    

      return response.data
    } catch (error) {
      console.error('❌ ThemeBackupService: Erro ao buscar backups do tema:', error)
      console.error('❌ ThemeBackupService: URL tentada:', `${this.#endpoint}/${themeId}/backups`)
      throw error
    }
  }

  /**
   * Cria backup manual de um tema
   */
  async createManualBackup(themeId) {
    try {
      const response = await axios.post(`${this.#endpoint}/${themeId}/backup`)
      return response.data
    } catch (error) {
      console.error('Erro ao criar backup manual:', error)
      throw error
    }
  }

  /**
   * Restaura um backup
   */
  async restoreBackup(backupId) {
    try {
      const response = await axios.post(`${this.#endpoint}/backups/${backupId}/restore`)
      return response.data
    } catch (error) {
      console.error('Erro ao restaurar backup:', error)
      throw error
    }
  }

  /**
   * Remove um backup
   */
  async deleteBackup(backupId) {
    try {
      const response = await axios.delete(`${this.#endpoint}/backups/${backupId}`)
      return response.data
    } catch (error) {
      console.error('Erro ao remover backup:', error)
      throw error
    }
  }

  /**
   * Busca estatísticas de backups
   */
  async getBackupStats() {
    try {
      const response = await axios.get(`${this.#endpoint}/backups/stats`)
      return response.data
    } catch (error) {
      console.error('Erro ao buscar estatísticas de backup:', error)
      throw error
    }
  }

  /**
   * Formata tamanho em bytes para exibição
   */
  formatFileSize(bytes) {
    if (bytes === 0) return '0 B'
    
    const k = 1024
    const sizes = ['B', 'KB', 'MB', 'GB']
    const i = Math.floor(Math.log(bytes) / Math.log(k))
    
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i]
  }

  /**
   * Formata data para exibição
   */
  formatDate(dateString) {
    if (!dateString) return '-'
    
    const date = new Date(dateString)
    return new Intl.DateTimeFormat('pt-BR', {
      year: 'numeric',
      month: '2-digit',
      day: '2-digit',
      hour: '2-digit',
      minute: '2-digit'
    }).format(date)
  }

  /**
   * Verifica se backup está próximo do vencimento
   */
  isBackupNearExpiration(expiresAt, days = 7) {
    if (!expiresAt) return false
    
    const expirationDate = new Date(expiresAt)
    const warningDate = new Date()
    warningDate.setDate(warningDate.getDate() + days)
    
    return expirationDate <= warningDate
  }

  /**
   * Verifica se backup é comprimido (novo formato)
   */
  isCompressedBackup(backup) {
    // Backup comprimido é identificado pela versão 2.0+ ou pela presença de metadados de compressão
    return backup?.metadata?.['backup-version'] >= '2.0' || 
           backup?.metadata?.['backup-compressed'] === 'true'
  }

  /**
   * Obtém informações de compressão do backup
   */
  getCompressionInfo(backup) {
    if (!this.isCompressedBackup(backup)) {
      return null
    }

    const metadata = backup.metadata || {}
    return {
      isCompressed: true,
      originalSize: parseInt(metadata['original-size'] || '0'),
      compressedSize: parseInt(metadata['compressed-size'] || '0'),
      compressionRatio: parseFloat(metadata['compression-ratio'] || '0'),
      fileCount: parseInt(metadata['file-count'] || '0')
    }
  }

  /**
   * Verifica se backup expirou
   */
  isBackupExpired(expiresAt) {
    if (!expiresAt) return false
    
    const expirationDate = new Date(expiresAt)
    const now = new Date()
    
    return expirationDate <= now
  }

  /**
   * Calcula dias até expiração
   */
  getDaysUntilExpiration(expiresAt) {
    if (!expiresAt) return null
    
    const expirationDate = new Date(expiresAt)
    const now = new Date()
    const diffTime = expirationDate - now
    const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24))
    
    return diffDays
  }

  /**
   * Retorna ícone baseado no tipo de backup
   */
  getBackupTypeIcon(backupReason) {
    const icons = {
      'THEME_CHANGE': 'SquareArrowDataTransferHorizontalIcon',
      'MANUAL_BACKUP': 'UserIcon', 
      'SCHEDULED_BACKUP': 'Appointment02Icon',
      'default': 'DataRecoveryIcon'
    }
    
    return icons[backupReason] || icons.default
  }

  /**
   * Retorna label amigável para tipo de backup
   */
  getBackupTypeLabel(backupReason) {
    const labels = {
      'THEME_CHANGE': 'Mudança de Tema',
      'MANUAL_BACKUP': 'Backup Manual',
      'SCHEDULED_BACKUP': 'Backup Automático',
      'default': 'Backup'
    }
    
    return labels[backupReason] || labels.default
  }

  /**
   * Retorna cor baseada no tipo de backup
   */
  getBackupTypeColor(backupReason) {
    const colors = {
      'THEME_CHANGE': 'blue',
      'MANUAL_BACKUP': 'green',
      'SCHEDULED_BACKUP': 'purple',
      'default': 'gray'
    }
    
    return colors[backupReason] || colors.default
  }

  /**
   * Formata informações de compressão para exibição
   */
  formatCompressionInfo(compressionInfo) {
    if (!compressionInfo || !compressionInfo.isCompressed) {
      return {
        text: 'Backup Legado',
        badge: 'warning',
        tooltip: 'Backup criado no formato antigo (arquivos individuais)'
      }
    }

    const { originalSize, compressedSize, compressionRatio } = compressionInfo
    
    return {
      text: `${compressionRatio.toFixed(1)}% comprimido`,
      badge: compressionRatio > 30 ? 'success' : compressionRatio > 15 ? 'info' : 'warning',
      tooltip: `Tamanho original: ${this.formatFileSize(originalSize)}\nComprimido: ${this.formatFileSize(compressedSize)}\nEconomia: ${this.formatFileSize(originalSize - compressedSize)}`,
      savings: originalSize - compressedSize
    }
  }

  /**
   * Calcula estatísticas de compressão para uma lista de backups
   */
  calculateCompressionStats(backups) {
    let totalOriginalSize = 0
    let totalCompressedSize = 0
    let compressedBackupsCount = 0
    let legacyBackupsCount = 0

    for (const backup of backups) {
      const compressionInfo = this.getCompressionInfo(backup)
      
      if (compressionInfo && compressionInfo.isCompressed) {
        totalOriginalSize += compressionInfo.originalSize
        totalCompressedSize += compressionInfo.compressedSize
        compressedBackupsCount++
      } else {
        legacyBackupsCount++
        totalCompressedSize += backup.backupSizeBytes || 0
        totalOriginalSize += backup.backupSizeBytes || 0
      }
    }

    const overallCompressionRatio = totalOriginalSize > 0 
      ? ((totalOriginalSize - totalCompressedSize) / totalOriginalSize) * 100 
      : 0
    
    const totalSavings = totalOriginalSize - totalCompressedSize

    return {
      totalBackups: backups.length,
      compressedBackups: compressedBackupsCount,
      legacyBackups: legacyBackupsCount,
      totalOriginalSize,
      totalCompressedSize,
      overallCompressionRatio,
      totalSavings,
      formattedOriginalSize: this.formatFileSize(totalOriginalSize),
      formattedCompressedSize: this.formatFileSize(totalCompressedSize),
      formattedSavings: this.formatFileSize(totalSavings)
    }
  }
}

export const themeBackupService = new ThemeBackupService()