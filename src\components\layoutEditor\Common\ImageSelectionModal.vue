<template>
  <!-- <PERSON>dal de seleção de imagem -->
  <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1100]" v-if="localVisible && !showCropEditor" @click.self="closeModal">
    <div class="bg-gray-900 rounded-lg shadow-2xl max-w-4xl w-full mx-4 max-h-[90vh] overflow-hidden" @click.stop>
      <!-- Header -->
      <div class="flex items-center justify-between p-4 border-b border-gray-700">
        <div class="flex items-center space-x-2">
          <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
          </svg>
          <h2 class="text-lg font-semibold text-white">{{ $t('media.title') }}</h2>
        </div>
        <button @click="closeModal" class="text-gray-400 hover:text-white transition-colors">
          <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </button>
      </div>
      
    <div class="p-4">
      <div class="flex flex-col h-full">
        <div class="max-w-3xl mx-auto w-full bg-gray-800 rounded-lg p-4">
          <!-- Botões de ação no topo -->
          <div class="flex justify-end mb-4">
            <!-- Upload button -->
            <label for="file-upload" class="upload-button" @click.stop>
              <span>{{ $t('media.uploadFiles') }}</span>
              <span class="upload-icon ml-2">&#x2B;</span>
              <input 
                id="file-upload" 
                type="file" 
                accept="image/*" 
                @change="handleFileUpload" 
                @click.stop
                class="hidden"
              />
            </label>
          </div>
          
          <!-- Status message -->
          <div v-if="statusMessage" class="status-message">
            {{ statusMessage }}
          </div>
          
          <!-- Loading indicator -->
          <div v-if="loading || uploading" class="flex justify-center items-center py-8">
            <div class="loading-spinner"></div>
            <div v-if="uploading" class="ml-4 text-gray-300">{{ $t('media.uploader.uploading') }}...</div>
          </div>
          
          <!-- Navegação de pastas -->
          <div v-if="currentPath.length > 0" class="folder-navigation mb-4">
            <button @click.stop="(event) => navigateToParent(event)" class="nav-button">
              ⬅️ {{ $t('layoutEditor.back') }}
            </button>
            <span class="current-path ml-2">{{ getCurrentPathDisplay() }}</span>
          </div>

          <!-- Image gallery -->
          <div v-if="!loading && !uploading" class="image-gallery">
            <div v-if="images.length === 0" class="no-images">
              <div class="empty-image-icon">📷</div>
              <p>{{ $t('media.noItems') }}</p>
              <p class="text-sm text-gray-400">{{ $t('media.dragAndDrop') }}</p>
            </div>
            <div v-else class="grid-container">
              <!-- Pastas -->
              <div 
                v-for="item in images.filter(item => item.type === 'FOLDER')" 
                :key="item.id" 
                class="folder-item"
                @click.stop="(event) => navigateToFolder(item, event)"
              >
                <div class="folder-icon">📁</div>
                <div class="folder-name">{{ item.name }}</div>
              </div>
              
              <!-- Imagens -->
              <div 
                v-for="item in images.filter(item => item.type !== 'FOLDER')" 
                :key="item.id" 
                class="image-item"
                @click.stop="selectImage(item)"
              >
                <div class="image-preview">
                  <img :src="item.previewUrl || placeholderImage" :alt="item.name" />
                </div>
                <div class="image-name">{{ item.name }}</div>
              </div>
            </div>
          </div>
        </div>
      </div>
      </div>
      
      <!-- Footer -->
      <div class="flex justify-end gap-3 p-4 border-t border-gray-700">
        <IluriaButton 
          color="secondary"
          @click="closeModal"
        >
          {{ $t('layoutEditor.cancel') }}
        </IluriaButton>
      </div>
    </div>
  </div>

  <!-- Editor de imagem -->
  <ImageCropEditor
    v-if="showCropEditor"
    :imageUrl="selectedImageData.url"
    :fileName="selectedImageData.name"
    :targetWidth="cropDimensions.width"
    :targetHeight="cropDimensions.height"
    :aspectRatio="cropAspectRatio || (cropDimensions.width / cropDimensions.height)"
    :quality="0.9"
    @close="onImageCancelled"
    @apply="onImageApplied"
  />
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import ImageCropEditor from './ImageCropEditor.vue'
import fileManagerService from '@/services/fileManager.service.js'
import { useI18n } from 'vue-i18n'

const { t } = useI18n()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  environment: {
    type: String,
    required: true,
    default: 'DEVELOP'
  },
  cropDimensions: {
    type: Object,
    default: () => ({ width: 300, height: 200 })
  },
  cropAspectRatio: {
    type: Number,
    default: null
  },
  enableCropEditor: {
    type: Boolean,
    default: true
  },
  targetElement: {
    type: Object,
    default: null
  },
  targetComponent: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'image-selected', 'imageCancelled'])

const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const images = ref([])
const loading = ref(true)
const uploading = ref(false)
const statusMessage = ref('')
const currentPath = ref([])
const showCropEditor = ref(false)
const selectedImageData = ref({})
const placeholderImage = 'data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAEAAAABCAQAAAC1HAwCAAAAC0lEQVR42mNkYAAAAAYAAjCB0C8AAAAASUVORK5CYII='

// Load images when component is mounted
onMounted(async () => {
  await loadImages()
})

// Obter o caminho atual formatado para exibição
const getCurrentPathDisplay = () => {
  if (currentPath.value.length === 0) return t('media.folderTree.title')
  return currentPath.value.map(folder => folder.name).join(' > ')
}

// Navegar para uma pasta
const navigateToFolder = async (folder, event) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  
  if (folder && folder.type === 'FOLDER') {
    currentPath.value.push(folder)
    await loadImages(folder.id)
  }
}

// Voltar para a pasta pai
const navigateToParent = async (event) => {
  if (event) {
    event.preventDefault()
    event.stopPropagation()
  }
  
  if (currentPath.value.length > 0) {
    currentPath.value.pop()
    const parentId = currentPath.value.length > 0 ? currentPath.value[currentPath.value.length - 1].id : null
    await loadImages(parentId)
  }
}

// Load images from the file manager
const loadImages = async (folderId = null) => {
  loading.value = true
  try {
    let fileTree = []
    if (folderId) {
      const folderData = await fileManagerService.getById(props.environment, folderId)
      // Se for array com um objeto de pasta, use o children desse objeto
      if (Array.isArray(folderData) && folderData.length === 1 && folderData[0].children) {
        fileTree = folderData[0].children
      } else if (Array.isArray(folderData)) {
        fileTree = folderData
      } else {
        fileTree = folderData.children || []
      }
    } else {
      fileTree = await fileManagerService.getFileTree(props.environment)
    }
    
    // Filter only image files and folders from the file tree
    const filteredFiles = fileTree.filter(file => {
      // Não mostrar a própria pasta dentro dela mesma
      if (folderId && file.id === folderId) return false;
      if (file.type === 'FOLDER') return true;
      if (file.type === 'ARCHIVE' || file.type === 'file') {
        return isImageFile(file.name);
      }
      return false;
    })
    
    if (filteredFiles.length === 0) {
      images.value = []
      loading.value = false
      return
    }
    
    // Preload image URLs
    const batchSize = 10
    let imagesWithUrls = []
    
    for (let i = 0; i < filteredFiles.length; i += batchSize) {
      const batch = filteredFiles.slice(i, i + batchSize)
      const batchResults = await Promise.all(
        batch.map(async (image) => {
          try {
            const previewUrl = await getS3FileUrl(image)
            return { ...image, previewUrl }
          } catch (error) {
            return { ...image, previewUrl: placeholderImage }
          }
        })
      )
      imagesWithUrls = [...imagesWithUrls, ...batchResults]
    }
    
    images.value = imagesWithUrls
  } catch (error) {
    images.value = []
  } finally {
    loading.value = false
  }
}

const selectImage = async (image) => {
  if (image.type === 'FOLDER') {
    return
  }
  
  try {
    if (!isImageFile(image.name)) {
      statusMessage.value = t('media.uploader.error.invalidType', { name: image.name })
      setTimeout(() => {
        statusMessage.value = ''
      }, 3000)
      return
    }
    
    const imageUrl = image.previewUrl || await getS3FileUrl(image)
    
    selectedImageData.value = {
      url: imageUrl,
      name: image.name,
      id: image.id,
      storeId: image.storeId
    }
    
    // Se o editor de crop está habilitado, abrir o editor
    if (props.enableCropEditor) {
      showCropEditor.value = true
    } else {
      // Caso contrário, emitir diretamente
      emit('image-selected', selectedImageData.value)
      closeModal()
    }
  } catch (error) {
    statusMessage.value = t('media.uploadError')
    setTimeout(() => {
      statusMessage.value = ''
    }, 3000)
  }
}

const handleFileUpload = async (event) => {
  const file = event.target.files[0]
  if (!file) return
  
  if (!isImageFile(file.name)) {
    statusMessage.value = t('media.invalidFileType')
    setTimeout(() => {
      statusMessage.value = ''
    }, 3000)
    return
  }
  
  uploading.value = true
  statusMessage.value = t('media.uploader.uploading')
  
  try {
    // Upload the file
    const uploadedFile = await fileManagerService.uploadFile(
      file,
      currentPath.value.length > 0 ? currentPath.value[currentPath.value.length - 1].id : null,
      props.environment
    )
    
    statusMessage.value = t('media.uploadComplete')
    setTimeout(() => {
      statusMessage.value = ''
    }, 3000)
    
    // Reload the images
    await loadImages()
    
    // Adicionar a URL de preview ao arquivo enviado e selecionar
    if (uploadedFile) {
      try {
        const previewUrl = await getS3FileUrl(uploadedFile)
        uploadedFile.previewUrl = previewUrl
        
        // Selecionar a imagem enviada
        await selectImage(uploadedFile)
      } catch (previewError) {
        // Fallback: tentar selecionar mesmo sem preview
        await selectImage(uploadedFile)
      }
    }
  } catch (error) {
    statusMessage.value = t('media.uploadError')
    setTimeout(() => {
      statusMessage.value = ''
    }, 3000)
  } finally {
    uploading.value = false
  }
}

const isImageFile = (fileName) => {
  const imageExtensions = ['.jpg', '.jpeg', '.png', '.gif', '.webp']
  return imageExtensions.some(ext => fileName.toLowerCase().endsWith(ext))
}

const getS3FileUrl = async (file) => {
  try {
    const envPath = 'dev'
    const formatForS3 = (str) => str.replace(/\s+/g, '+')
    const s3FileName = formatForS3(file.name)
    
    // Se o arquivo está na raiz (sem pasta pai)
    if (!file.parentFolderId) {
      return `https://iluria-bucket-dev.s3.us-east-2.amazonaws.com/${file.storeId}/file-manager/${envPath}/${s3FileName}`
    }
    
    // Se o arquivo está dentro de uma pasta, usar o currentPath para construir o caminho correto
    let folderPath = ''
    if (currentPath.value.length > 0) {
      // Usar o caminho atual da navegação para construir a URL
      const folderNames = currentPath.value.map(folder => formatForS3(folder.name))
      folderPath = folderNames.join('/')
    } else {
      // Fallback: tentar construir o caminho através da API (método anterior)
      const buildFolderPath = async (folderId) => {
        if (!folderId) return []
        
        const folderDataArray = await fileManagerService.getFileTree(props.environment)
        const folder = folderDataArray?.find(f => f.id === folderId)
        
        if (!folder?.name) {
          throw new Error("Invalid folder data: Missing folder name")
        }
        
        let parentPath = []
        if (folder.parentFolderId) {
          parentPath = await buildFolderPath(folder.parentFolderId)
        }
        
        return [...parentPath, formatForS3(folder.name)]
      }
      
      const folderPathArray = await buildFolderPath(file.parentFolderId)
      folderPath = folderPathArray.join('/')
    }
    
    return `https://iluria-bucket-dev.s3.us-east-2.amazonaws.com/${file.storeId}/file-manager/${envPath}/${folderPath}/${s3FileName}`
  } catch (error) {
    throw error
  }
}

const onImageApplied = (cropResult) => {

  
  // Criar dados compatíveis com o formato esperado
  const editedImageData = {
    ...selectedImageData.value,
    // Usar a imagem original (não cortada) para manter compatibilidade
    url: selectedImageData.value.url,
    
    // 🎯 IMAGEM CORTADA VIA CANVAS (se disponível)
    croppedImageUrl: cropResult.croppedImageUrl,
    croppedBlob: cropResult.croppedBlob,
    
    // 🎯 NOVA ARQUITETURA - CSS pronto do ImageCropEditor
    readyToUseCSS: cropResult.readyToUseCSS,
    containerCSS: cropResult.containerCSS,
    targetDimensions: cropResult.targetDimensions || { width: cropResult.width, height: cropResult.height },
    
    // Informações originais da imagem para fallback
    originalUrl: cropResult.originalUrl || selectedImageData.value.url,
    
    // ✅ DADOS DE CROP PRINCIPAIS (para compatibilidade)
    objectPosition: cropResult.objectPosition,
    objectFit: cropResult.objectFit || 'cover',
    scale: cropResult.scale,
    
    // Dados do crop detalhados
    cropData: {
      ...cropResult.cropData,
      width: cropResult.width,
      height: cropResult.height
    },
    
    // Informações adicionais (se houver imagem cortada)
    croppedImageData: cropResult.dataUrl ? {
      dataUrl: cropResult.dataUrl,
      blob: cropResult.blob,
      width: cropResult.width,
      height: cropResult.height
    } : undefined,
    
    // Target dimensions
    width: cropResult.width,
    height: cropResult.height,
    
    // Para aplicação no CSS se necessário (legado)
    style: {
      objectPosition: cropResult.objectPosition,
      objectFit: cropResult.objectFit || 'cover',
      width: '100%',
      height: '100%'
    }
  }
  

  emit('image-selected', editedImageData)
  closeModal()
}

const onImageCancelled = () => {
  showCropEditor.value = false
  emit('imageCancelled')
}

const closeModal = () => {
  localVisible.value = false
  showCropEditor.value = false
  selectedImageData.value = {}
}
</script>

<style scoped>
.iluria-modal-content {
  width: 100%;
  max-width: 900px;
  max-height: 90vh;
  min-height: 400px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  overflow: auto;
}

.p-4 {
  padding: 1.5rem !important;
}

.max-w-3xl {
  max-width: 800px;
}

@media (max-width: 1024px) {
  .iluria-modal-content, .max-w-3xl {
    max-width: 98vw;
    min-width: 0;
  }
}

@media (max-width: 600px) {
  .iluria-modal-content, .max-w-3xl {
    max-width: 100vw;
    min-width: 0;
    padding: 0.5rem !important;
  }
  .p-4 {
    padding: 0.5rem !important;
  }
}

.upload-button {
  display: flex;
  align-items: center;
  background-color: #3B82F6;
  color: white;
  padding: 0.75rem 1.25rem;
  border-radius: 0.375rem;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.upload-button:hover {
  background-color: #2563EB;
  transform: translateY(-1px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.upload-icon {
  font-size: 1.25rem;
  font-weight: bold;
}

.hidden {
  display: none;
}

.image-gallery {
  min-height: 400px;
  max-height: 70vh;
}

.no-images {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: #9CA3AF;
}

.grid-container {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
  gap: 1rem;
  margin-top: 1rem;
}

.image-item, .folder-item {
  display: flex;
  flex-direction: column;
  cursor: pointer;
  border-radius: 4px;
  overflow: hidden;
  transition: transform 0.2s, box-shadow 0.2s;
  border: 1px solid #374151;
  background-color: #1F2937;
}

.image-item:hover, .folder-item:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.3);
  border-color: #3B82F6;
}

.image-preview {
  height: 100px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  background-color: #111827;
}

.image-preview img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-name, .folder-name {
  padding: 0.5rem;
  font-size: 0.75rem;
  text-align: center;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  background-color: #1F2937;
  color: #D1D5DB;
}

.folder-icon {
  height: 100px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 2.5rem;
  background-color: #111827;
  color: #6B7280;
}

.folder-navigation {
  display: flex;
  align-items: center;
  padding: 0.5rem;
  background-color: #111827;
  border-radius: 0.25rem;
}

.nav-button {
  padding: 0.25rem 0.5rem;
  background-color: #374151;
  border-radius: 0.25rem;
  cursor: pointer;
  transition: background-color 0.2s;
  border: none;
  display: flex;
  align-items: center;
  color: #D1D5DB;
}

.nav-button:hover {
  background-color: #4B5563;
}

.current-path {
  font-size: 0.875rem;
  color: #9CA3AF;
}

.empty-image-icon {
  font-size: 3rem;
  margin-bottom: 1rem;
  color: #374151;
}

.status-message {
  margin-bottom: 1rem;
  padding: 0.5rem;
  text-align: center;
  background-color: rgba(59, 130, 246, 0.1);
  border-radius: 0.25rem;
  color: #3B82F6;
  border: 1px solid rgba(59, 130, 246, 0.3);
}

/* Loading spinner */
.loading-spinner {
  border: 4px solid rgba(59, 130, 246, 0.1);
  border-radius: 50%;
  border-top: 4px solid #3B82F6;
  width: 30px;
  height: 30px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Classe customizada similar ao ImageCropEditor */
.bg-gray-850 {
  background-color: #1f2937;
}
</style>
