/**
 * Configuração do Componente de Localização
 * Migrado de LocationComponent.js para o novo sistema de configs
 */

import { GOOGLE_MAPS_API_KEY } from '@/config/api.config.js'

const LocationConfig = {
  type: 'location',
  name: 'Localização',
  description: 'Seção para exibir localizações da loja com endereços, telefones e mapas',
  category: 'content',
  priority: 65,
  
  icon: 'location_on',
  
  html: `<div 
    data-component="location" 
    data-element-type="location"
    data-title="Nossas Lojas"
    data-subtitle="Visite uma de nossas lojas físicas para conhecer nossos produtos de perto e receber atendimento personalizado."
    data-layout="grid"
    data-locations='[{"id":1,"name":"Loja Principal - Centro","description":"Nossa loja principal no centro da cidade, com todo o catálogo de produtos disponível.","address":"Rua Augusta, 123 - Centro, São Paulo - SP, 01305-000","phone":"+55 (11) 3333-4444","email":"<EMAIL>","hours":"Segunda à Sexta: 9h às 18h | Sábado: 9h às 17h","image":"https://mercadoeconsumo.com.br/wp-content/smush-webp/2021/04/SHOPPING-CENTER-NORTE-1140x570.jpg.webp","hasMap":false,"social":{"youtube":"https://youtube.com/@minhaloja","instagram":"https://instagram.com/minhaloja","facebook":"https://facebook.com/minhaloja","twitter":"https://twitter.com/minhaloja","tiktok":"https://tiktok.com/@minhaloja"}}]'
    class="iluria-location-component"
    style="padding: 4rem 2rem; background: #f8f9fa;">
    
    <div class="location-container">
      <div class="location-header">
        <h1 class="location-title">Nossas Lojas</h1>
        <p class="location-subtitle">Visite uma de nossas lojas físicas para conhecer nossos produtos de perto e receber atendimento personalizado.</p>
      </div>
      
      <div class="locations-grid layout-grid">
        <div class="location-item" data-location-id="1">
          <div class="location-image">
            <img src="https://mercadoeconsumo.com.br/wp-content/smush-webp/2021/04/SHOPPING-CENTER-NORTE-1140x570.jpg.webp" alt="Loja Principal - Centro" class="location-photo">
          </div>
          <div class="location-content">
            <h2 class="location-name">Loja Principal - Centro</h2>
            <p class="location-description">Nossa loja principal no centro da cidade, com todo o catálogo de produtos disponível.</p>
            
            <div class="location-details">
              <div class="detail-item">
                <strong>Endereço</strong>
                <p class="location-address">Rua Augusta, 123 - Centro, São Paulo - SP, 01305-000</p>
                <a href="https://www.google.com/maps/search/?api=1&query=Rua+Augusta%2C+123+-+Centro%2C+S%C3%A3o+Paulo+-+SP%2C+01305-000" target="_blank" class="get-directions">Como chegar →</a>
              </div>
              
              <div class="detail-item">
                <strong>Horário de Funcionamento</strong>
                <p class="location-hours">Segunda à Sexta: 9h às 18h | Sábado: 9h às 17h</p>
              </div>
              
              <div class="detail-item">
                <strong>Entre em contato</strong>
                <p class="location-phone">+55 (11) 3333-4444</p>
                <p class="location-email"><EMAIL></p>
              </div>
              
              <div class="location-social">
                <a href="https://youtube.com/@minhaloja" target="_blank" class="social-link youtube" aria-label="YouTube">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/>
                  </svg>
                </a>
                <a href="https://instagram.com/minhaloja" target="_blank" class="social-link instagram" aria-label="Instagram">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                  </svg>
                </a>
                <a href="https://facebook.com/minhaloja" target="_blank" class="social-link facebook" aria-label="Facebook">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                  </svg>
                </a>
                <a href="https://twitter.com/minhaloja" target="_blank" class="social-link twitter" aria-label="Twitter">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/>
                  </svg>
                </a>
                <a href="https://tiktok.com/@minhaloja" target="_blank" class="social-link tiktok" aria-label="TikTok">
                  <svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor">
                    <path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/>
                  </svg>
                </a>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <style>
      .iluria-location-component {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
      }
      
      .location-container {
        max-width: 1200px;
        margin: 0 auto;
      }
      
      .location-header {
        text-align: center;
        margin-bottom: 3rem;
      }
      
      .location-title {
        font-size: 2.5rem;
        font-weight: 700;
        color: #1a1a1a;
        margin: 0 0 1rem 0;
      }
      
      .location-subtitle {
        font-size: 1.1rem;
        color: #666;
        max-width: 600px;
        margin: 0 auto;
        line-height: 1.6;
      }
      
      .locations-grid.layout-grid {
        display: grid;
        gap: 3rem;
        grid-template-columns: 1fr;
      }
      
      .layout-grid .location-item {
        display: grid;
        grid-template-columns: 1fr 1fr;
        gap: 2rem;
        align-items: center;
        background: white;
        border-radius: 12px;
        overflow: hidden;
        box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
      }
      
      .location-image {
        position: relative;
        overflow: hidden;
      }
      
      .location-photo {
        width: 100%;
        height: 300px;
        object-fit: cover;
        transition: transform 0.3s ease;
      }
      
      .location-photo:hover {
        transform: scale(1.05);
      }
      
      .location-content {
        padding: 2rem;
      }
      
      .location-name {
        font-size: 1.8rem;
        font-weight: 600;
        color: #1a1a1a;
        margin: 0 0 1rem 0;
      }
      
      .location-description {
        font-size: 1rem;
        color: #666;
        margin: 0 0 2rem 0;
        line-height: 1.6;
      }
      
      .location-details {
        display: flex;
        flex-direction: column;
        gap: 1.5rem;
      }
      
      .detail-item {
        display: flex;
        flex-direction: column;
        gap: 0.5rem;
      }
      
      .detail-item strong {
        font-weight: 600;
        color: #1a1a1a;
        font-size: 0.95rem;
      }
      
      .detail-item p {
        margin: 0;
        color: #666;
        line-height: 1.5;
      }
      
      .get-directions {
        color: #007bff;
        text-decoration: none;
        font-weight: 500;
        transition: color 0.2s ease;
        align-self: flex-start;
        margin-top: 0.25rem;
      }
      
      .get-directions:hover {
        color: #0056b3;
        text-decoration: underline;
      }
      
      .location-social {
        display: flex;
        gap: 0.75rem;
        margin-top: 0.5rem;
      }
      
      .social-link {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 36px;
        height: 36px;
        border-radius: 50%;
        transition: all 0.2s ease;
        text-decoration: none;
      }
      
      .social-link.youtube {
        background: #ff0000;
        color: white;
      }
      
      .social-link.instagram {
        background: linear-gradient(45deg, #f09433 0%, #e6683c 25%, #dc2743 50%, #cc2366 75%, #bc1888 100%);
        color: white;
      }
      
      .social-link.facebook {
        background: #1877f2;
        color: white;
      }
      
      .social-link.twitter {
        background: #1da1f2;
        color: white;
      }
      
      .social-link.tiktok {
        background: #000;
        color: white;
      }
      
      .social-link:hover {
        transform: translateY(-2px);
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      }
      
      .location-map {
        width: 100%;
        min-height: 350px;
        border-radius: 8px;
        overflow: hidden;
        margin: 1.5rem 0;
        box-sizing: border-box;
      }
      
      .location-map > div {
        width: 100%;
        height: 100%;
        min-height: 350px;
      }
      
      @media (max-width: 768px) {
        .iluria-location-component {
          padding: 2rem 1rem;
        }
        
        .location-title {
          font-size: 2rem;
        }
        
        .layout-grid .location-item {
          grid-template-columns: 1fr;
          gap: 1.5rem;
        }
        
        .location-content {
          padding: 1.5rem;
        }
        
        .location-name {
          font-size: 1.5rem;
        }
        
        .location-map {
          min-height: 280px;
        }
        
        .location-map > div {
          min-height: 280px;
        }
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'location-content',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M14.5 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7.5L14.5 2z"/>
        <polyline points="14,2 14,8 20,8"/>
        <line x1="16" y1="13" x2="8" y2="13"/>
        <line x1="16" y1="17" x2="8" y2="17"/>
        <polyline points="10,9 9,9 8,9"/>
      </svg>`,
      title: 'layoutEditor.editLocationContent',
      condition: (element) => {
        return element?.hasAttribute('data-component') && 
               element?.getAttribute('data-component') === 'location'
      }
    },
    {
      type: 'location-design',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M7 21a4 4 0 01-4-4V5a2 2 0 012-2h4a2 2 0 012 2v12a4 4 0 01-4 4zm0 0h12a2 2 0 002-2v-4a2 2 0 00-2-2h-2.343M11 7.343l1.657-1.657a2 2 0 012.828 0l2.829 2.829a2 2 0 010 2.828l-8.486 8.485M7 17h.01"/>
      </svg>`,
      title: 'layoutEditor.editLocationDesign',
      condition: (element) => {
        return element?.hasAttribute('data-component') && 
               element?.getAttribute('data-component') === 'location'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'location-content',
      component: 'LocationContentEditor',
              path: '@/components/layoutEditor/Sidebar/editors/LocationContentEditor.vue'
    },
    {
      type: 'location-design',
      component: 'LocationDesignEditor', 
              path: '@/components/layoutEditor/Sidebar/editors/LocationDesignEditor.vue'
    }
  ],
  
  computedCheckers: {
    isLocationComponent: (element) => {
      return element?.hasAttribute('data-component') && 
             element?.getAttribute('data-component') === 'location'
    },
    
    hasLocations: (element) => {
      const locationsData = element?.getAttribute('data-locations')
      if (!locationsData) return false
      try {
        const locations = JSON.parse(locationsData)
        return Array.isArray(locations) && locations.length > 0
      } catch {
        return false
      }
    },
    
    hasTitle: (element) => {
      const title = element?.getAttribute('data-title')
      return title && title.trim().length > 0
    },
    
    hasSubtitle: (element) => {
      const subtitle = element?.getAttribute('data-subtitle')
      return subtitle && subtitle.trim().length > 0
    },
    
    hasCustomStyles: (element) => {
      const hasBackground = element?.getAttribute('data-section-background') !== '#f8f9fa'
      const hasCustomColor = element?.getAttribute('data-title-color') !== '#1a1a1a'
      const hasCustomPadding = element?.getAttribute('data-section-padding') !== '64'
      return hasBackground || hasCustomColor || hasCustomPadding
    },
    
    getLocationCount: (element) => {
      const locationsData = element?.getAttribute('data-locations')
      if (!locationsData) return 0
      try {
        const locations = JSON.parse(locationsData)
        return Array.isArray(locations) ? locations.length : 0
      } catch {
        return 0
      }
    },
    
    needsConfiguration: (element) => {
      const title = element?.getAttribute('data-title')
      const locationsData = element?.getAttribute('data-locations')
      
      if (!title || title.trim().length === 0) return true
      if (!locationsData) return true
      
      try {
        const locations = JSON.parse(locationsData)
        return !Array.isArray(locations) || locations.length === 0
      } catch {
        return true
      }
    }
  },
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true
  },
  
  cleanup: {
    removeEditingStyles: true,
    specialElements: ['.location-map']
  },

  renderLocationMap: (element, location) => {
    if (!location.hasMap || !location.address) {
      return
    }
    
    const mapElement = element.querySelector(`[data-location-id="${location.id}"] .location-map`)
    if (!mapElement) {
      return
    }
    
    // Usar diretamente o Google Maps JavaScript API
    LocationConfig.renderGoogleMapsDirectly(mapElement, location)
  },

  // Renderizar Google Maps diretamente
  renderGoogleMapsDirectly: (mapElement, location) => {
    // Debug para verificar se está sendo chamado

    
    // Limpar conteúdo anterior
    mapElement.innerHTML = ''
    
    // Criar container do mapa
    const mapContainer = document.createElement('div')
    mapContainer.style.cssText = `
      width: 100%;
      height: 350px;
      background: #f0f0f0;
      border-radius: 8px;
      position: relative;
      margin: 1.5rem 0;
      border: 1px solid #e2e8f0;
    `
    mapContainer.id = `google-map-${location.id}-${Date.now()}`
    
    // Adicionar indicador de carregamento
    mapContainer.innerHTML = `
      <div style="
        position: absolute; 
        top: 50%; 
        left: 50%; 
        transform: translate(-50%, -50%);
        text-align: center;
        color: #666;
        font-family: Arial, sans-serif;
      ">
        <div style="margin-bottom: 10px;">🗺️</div>
        <div>Carregando Google Maps...</div>
      </div>
    `
    
    mapElement.appendChild(mapContainer)
    
    // Tentar carregar Google Maps
    const loadGoogleMaps = () => {
      // Verificar se já está carregado
      if (window.google && window.google.maps) {
        createMap()
        return
      }
      
      // Verificar se já existe script carregando
      const existingScript = document.querySelector('script[src*="maps.googleapis.com"]')
      if (existingScript) {
        const checkInterval = setInterval(() => {
          if (window.google && window.google.maps) {
            clearInterval(checkInterval)
            createMap()
          }
        }, 100)
        
        // Timeout de 10 segundos
        setTimeout(() => {
          clearInterval(checkInterval)
          if (!window.google || !window.google.maps) {
            showFallback()
          }
        }, 10000)
        return
      }
      
      // Carregar novo script (removendo libraries para evitar problemas de autorização)
      const script = document.createElement('script')
      script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY || 'AIzaSyCDhfeGIVpj_9bil4Hdc9NHFT7QuKtDnP0'}`
      script.async = true
      script.defer = true
      
      script.onload = () => {
        setTimeout(createMap, 500) // Aguardar um pouco para garantir que está pronto
      }
      
      script.onerror = () => {
        showFallback()
      }
      
      document.head.appendChild(script)
    }

    // Função alternativa de geocodificação usando API pública
    const tryAlternativeGeocoding = async (map, marker, address) => {
      try {
        // Primeiro tentar extrair CEP brasileiro e usar API do ViaCEP
        const cepMatch = address.match(/(\d{5})-?(\d{3})/)
        if (cepMatch) {
          const cep = `${cepMatch[1]}-${cepMatch[2]}`
          try {
            const cepResponse = await fetch(`https://viacep.com.br/ws/${cep}/json/`)
            if (cepResponse.ok) {
              const cepData = await cepResponse.json()
              if (cepData && !cepData.erro) {
                // Usar Nominatim para geocodificar o endereço completo do ViaCEP
                const fullAddress = `${cepData.logradouro}, ${cepData.bairro}, ${cepData.localidade}, ${cepData.uf}, Brasil`
                const encodedFullAddress = encodeURIComponent(fullAddress)
                const geoResponse = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodedFullAddress}&limit=1`)
                
                if (geoResponse.ok) {
                  const geoData = await geoResponse.json()
                  if (geoData && geoData.length > 0) {
                    const coords = {
                      lat: parseFloat(geoData[0].lat),
                      lng: parseFloat(geoData[0].lon)
                    }
                    map.setCenter(coords)
                    marker.setPosition(coords)
                    return
                  }
                }
              }
            }
          } catch (error) {
            // Erro silencioso, continua para próxima tentativa
          }
        }
        
        // Usar Nominatim (OpenStreetMap) - gratuito e sem necessidade de API key
        const encodedAddress = encodeURIComponent(address)
        const response = await fetch(`https://nominatim.openstreetmap.org/search?format=json&q=${encodedAddress}&limit=1`)
        
        if (response.ok) {
          const data = await response.json()
          if (data && data.length > 0) {
            const coords = {
              lat: parseFloat(data[0].lat),
              lng: parseFloat(data[0].lon)
            }
            map.setCenter(coords)
            marker.setPosition(coords)
            return
          }
        }
        
        // Se Nominatim falhar, tentar detectar coordenadas no próprio texto
        const coordsMatch = address.match(/(-?\d+\.?\d*),?\s*(-?\d+\.?\d*)/)
        if (coordsMatch) {
          const coords = {
            lat: parseFloat(coordsMatch[1]),
            lng: parseFloat(coordsMatch[2])
          }
          if (coords.lat >= -90 && coords.lat <= 90 && coords.lng >= -180 && coords.lng <= 180) {
            map.setCenter(coords)
            marker.setPosition(coords)
            return
          }
        }
        
        // Fallback para cidades brasileiras conhecidas
        const brazilianCities = {
          'São Paulo': { lat: -23.5505, lng: -46.6333 },
          'Rio de Janeiro': { lat: -22.9068, lng: -43.1729 },
          'Brasília': { lat: -15.7801, lng: -47.9292 },
          'Salvador': { lat: -12.9714, lng: -38.5014 },
          'Fortaleza': { lat: -3.7319, lng: -38.5267 },
          'Belo Horizonte': { lat: -19.9191, lng: -43.9386 },
          'Manaus': { lat: -3.1190, lng: -60.0217 },
          'Curitiba': { lat: -25.4284, lng: -49.2733 },
          'Recife': { lat: -8.0476, lng: -34.8770 },
          'Goiânia': { lat: -16.6869, lng: -49.2648 },
          'Belém': { lat: -1.4554, lng: -48.4898 },
          'Porto Alegre': { lat: -30.0346, lng: -51.2177 },
          'Guarulhos': { lat: -23.4538, lng: -46.5333 },
          'Campinas': { lat: -22.9099, lng: -47.0626 },
          'São Luís': { lat: -2.5387, lng: -44.2828 },
          'São Gonçalo': { lat: -22.8267, lng: -43.0537 },
          'Maceió': { lat: -9.6658, lng: -35.7353 },
          'Duque de Caxias': { lat: -22.7856, lng: -43.3054 },
          'Natal': { lat: -5.7945, lng: -35.2110 },
          'Teresina': { lat: -5.0892, lng: -42.8016 },
          'Campo Grande': { lat: -20.4697, lng: -54.6201 },
          'Nova Iguaçu': { lat: -22.7592, lng: -43.4507 },
          'São Bernardo do Campo': { lat: -23.6914, lng: -46.5646 },
          'João Pessoa': { lat: -7.1195, lng: -34.8450 },
          'Santo André': { lat: -23.6633, lng: -46.5304 },
          'Ribeirão Preto': { lat: -21.1775, lng: -47.8208 }
        }
        
        const addressLower = address.toLowerCase()
        for (const [city, coords] of Object.entries(brazilianCities)) {
          if (addressLower.includes(city.toLowerCase())) {
            map.setCenter(coords)
            marker.setPosition(coords)
            return
          }
        }
      } catch (error) {
        // Erro silencioso
      }
    }

    const createMap = () => {
      try {
        // Coordenadas padrão (São Paulo)
        const defaultCoords = { lat: -23.5505, lng: -46.6333 }
        
        // Limpar indicador de carregamento
        mapContainer.innerHTML = ''
        
        // Detectar se é site publicado para gestureHandling mais permissivo
        const isPublishedSite = window.location.hostname !== 'localhost' && 
                               !window.location.pathname.includes('/admin')
        
        // Criar o mapa com controles totalmente interativos
        const map = new window.google.maps.Map(mapContainer, {
          zoom: 15,
          center: defaultCoords,
          mapTypeId: 'roadmap',
          gestureHandling: isPublishedSite ? 'greedy' : 'cooperative', // Mais permissivo em sites publicados
          zoomControl: true,
          mapTypeControl: true,
          scaleControl: true,
          streetViewControl: true,
          rotateControl: true,
          fullscreenControl: true,
          disableDefaultUI: false, // Habilitar todos os controles
          clickableIcons: true,
          keyboardShortcuts: true,
          scrollwheel: true,
          draggable: true
        })
        

        
        // Criar marcador
        const marker = new window.google.maps.Marker({
          position: defaultCoords,
          map: map,
          title: location.name,
          animation: window.google.maps.Animation.DROP
        })
        
        // Tentar geocodificar endereço (com fallback)
        
        // Primeiro tentar com Google Geocoding (se disponível)
        if (window.google.maps.Geocoder) {
          try {
            const geocoder = new window.google.maps.Geocoder()
            geocoder.geocode({ address: location.address }, (results, status) => {
              if (status === 'OK' && results[0]) {
                const coords = {
                  lat: results[0].geometry.location.lat(),
                  lng: results[0].geometry.location.lng()
                }
                map.setCenter(coords)
                marker.setPosition(coords)
              } else {
                tryAlternativeGeocoding(map, marker, location.address)
              }
            })
          } catch (error) {
            tryAlternativeGeocoding(map, marker, location.address)
          }
        } else {
          tryAlternativeGeocoding(map, marker, location.address)
        }

        // Info window
        const infoWindow = new window.google.maps.InfoWindow({
          content: `
            <div style="padding: 12px; max-width: 250px; font-family: Arial, sans-serif;">
              <h4 style="margin: 0 0 8px 0; color: #1a1a1a; font-size: 1.1rem; font-weight: 600;">
                📍 ${location.name}
              </h4>
              <p style="margin: 0 0 12px 0; color: #666; font-size: 0.9rem; line-height: 1.4;">
                ${location.address}
              </p>
              <div style="display: flex; gap: 8px; flex-wrap: wrap;">
                ${location.phone ? `
                  <a href="tel:${location.phone}" 
                     style="background: #3b82f6; color: white; text-decoration: none; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 500; display: inline-block;">
                    📞 Ligar
                  </a>
                ` : ''}
                <a href="https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(location.address)}" 
                   target="_blank"
                   style="background: #10b981; color: white; text-decoration: none; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 500; display: inline-block;">
                  🧭 Direções
                </a>
              </div>
            </div>
          `
        })
        
        marker.addListener('click', () => {
          infoWindow.open(map, marker)
        })
        
        // Múltiplos triggers de resize para garantir renderização
        setTimeout(() => {
          window.google.maps.event.trigger(map, 'resize')
        }, 100)
        
        // Trigger adicional para sites publicados
        if (isPublishedSite) {
          setTimeout(() => {
            window.google.maps.event.trigger(map, 'resize')

          }, 1000)
        }
        
      } catch (error) {
        showFallback()
      }
    }
    
    const showFallback = () => {
      LocationConfig.renderFallbackMap(mapElement, location)
    }

    // Iniciar carregamento
    loadGoogleMaps()
  },

  // Função de fallback para quando o vue3-google-map não conseguir carregar
  renderFallbackMap: (mapElement, location) => {
    const addressEncoded = encodeURIComponent(location.address)
    
    // Criar container do mapa com espaçamento
    const mapContainer = document.createElement('div')
    mapContainer.style.cssText = `
      padding: 1.5rem;
      background: white;
      border-radius: 12px;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
      margin: 1.5rem 0;
      border: 1px solid #e2e8f0;
    `
    
    mapContainer.innerHTML = `
      <div style="
        width: 100%;
        height: 350px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow: hidden;
      ">
        <div style="
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
        "></div>
        <div style="position: relative; z-index: 1;">
          <div style="margin-bottom: 1.5rem;">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
            </svg>
          </div>
          <h3 style="margin: 0 0 0.75rem 0; font-size: 1.3rem; font-weight: 600;">📍 ${location.name}</h3>
          <p style="margin: 0 0 2rem 0; font-size: 1rem; line-height: 1.5; max-width: 320px; opacity: 0.9;">${location.address}</p>
          <div style="display: flex; gap: 1rem; justify-content: center; flex-wrap: wrap;">
            <a 
              href="https://www.google.com/maps/search/?api=1&query=${addressEncoded}" 
              target="_blank"
              style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                padding: 0.875rem 1.5rem;
                border-radius: 30px;
                font-weight: 600;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                display: flex;
                align-items: center;
                gap: 0.5rem;
              "
              onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-3px)'; this.style.boxShadow='0 8px 20px rgba(0,0,0,0.2)'"
              onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'"
            >
              🗺️ Google Maps
            </a>
            <a 
              href="https://www.openstreetmap.org/search?query=${addressEncoded}" 
              target="_blank"
              style="
                background: rgba(255, 255, 255, 0.2);
                color: white;
                text-decoration: none;
                padding: 0.875rem 1.5rem;
                border-radius: 30px;
                font-weight: 600;
                font-size: 0.9rem;
                transition: all 0.3s ease;
                backdrop-filter: blur(10px);
                border: 1px solid rgba(255, 255, 255, 0.3);
                display: flex;
                align-items: center;
                gap: 0.5rem;
              "
              onmouseover="this.style.background='rgba(255,255,255,0.3)'; this.style.transform='translateY(-3px)'; this.style.boxShadow='0 8px 20px rgba(0,0,0,0.2)'"
              onmouseout="this.style.background='rgba(255,255,255,0.2)'; this.style.transform='translateY(0)'; this.style.boxShadow='none'"
            >
              🌍 OpenStreetMap
            </a>
          </div>
        </div>
      </div>
    `
    
    mapElement.innerHTML = ''
    mapElement.appendChild(mapContainer)
  },

  // Função para atualizar todos os mapas
  updateAllMaps: (element) => {
    const locationsData = element.getAttribute('data-locations')
    if (!locationsData) return
    
    try {
      const locations = JSON.parse(locationsData)
      locations.forEach(location => {
        if (location.hasMap) {
                  // Usar timeout para garantir que o DOM foi atualizado
        setTimeout(() => {
          LocationConfig.renderLocationMap(element, location)
        }, 100)
        }
      })
    } catch (error) {
      console.error('Erro ao atualizar mapas:', error)
    }
  },

  // Função para mostrar placeholder estático no editor
  renderStaticPlaceholder: (mapElement, location) => {
    const addressEncoded = encodeURIComponent(location.address)
    
    mapElement.innerHTML = `
      <div style="
        width: 100%;
        height: 350px;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        border-radius: 8px;
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        text-align: center;
        color: white;
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
        position: relative;
        overflow: hidden;
        margin: 1.5rem 0;
        border: 1px solid #e2e8f0;
      ">
        <div style="
          position: absolute;
          top: 0;
          left: 0;
          right: 0;
          bottom: 0;
          background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
        "></div>
        <div style="position: relative; z-index: 1;">
          <div style="margin-bottom: 1.5rem;">
            <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
              <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
            </svg>
          </div>
          <h3 style="margin: 0 0 0.75rem 0; font-size: 1.3rem; font-weight: 600;">📍 ${location.name}</h3>
          <p style="margin: 0 0 1rem 0; font-size: 1rem; line-height: 1.5; max-width: 320px; opacity: 0.9;">${location.address}</p>
          <div style="background: rgba(255, 255, 255, 0.2); padding: 0.5rem 1rem; border-radius: 20px; font-size: 0.9rem; display: inline-block;">
            Mapa interativo no preview
          </div>
        </div>
      </div>
    `
  },

  // Função para inicializar mapas automaticamente quando o componente for carregado
  initializeMapsOnLoad: (element) => {
    if (!element) {
      return;
    }
    
    const locationsData = element.getAttribute('data-locations');
    if (!locationsData) {
      return;
    }
    
    try {
      const locations = JSON.parse(locationsData);

      
      // Renderizar mapas interativos diretamente
      const mapsToRender = locations.filter(location => location.hasMap && location.address);
      if (mapsToRender.length > 0) {

        mapsToRender.forEach(location => {
          const mapElement = element.querySelector('[data-location-id="' + location.id + '"] .location-map');
          if (mapElement && !mapElement.innerHTML.trim()) {

           setTimeout(() => {
             LocationConfig.renderGoogleMapsDirectly(mapElement, location);
           }, 300);
          }
        });
      }
    } catch (error) {
      
    }
  },

  // Função para gerar HTML dinamicamente
  generateHtml(data) {
    const {
      title = 'Nossas Lojas',
      subtitle = 'Visite uma de nossas lojas físicas para conhecer nossos produtos de perto e receber atendimento personalizado.',
      layout = 'grid',
      locations = [],
      sectionBackground = '#f8f9fa',
      titleColor = '#1a1a1a',
      sectionPadding = '64'
    } = data

    const generateLocationItem = (location) => {
      const hasMapSection = location.hasMap && location.address;
      const socialLinks = location.social ? Object.entries(location.social)
        .filter(([platform, url]) => url && url.trim())
        .map(([platform, url]) => {
          const socialIcons = {
            youtube: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M23.498 6.186a3.016 3.016 0 0 0-2.122-2.136C19.505 3.545 12 3.545 12 3.545s-7.505 0-9.377.505A3.017 3.017 0 0 0 .502 6.186C0 8.07 0 12 0 12s0 3.93.502 5.814a3.016 3.016 0 0 0 2.122 2.136c1.871.505 9.376.505 9.376.505s7.505 0 9.377-.505a3.015 3.015 0 0 0 2.122-2.136C24 15.93 24 12 24 12s0-3.93-.502-5.814zM9.545 15.568V8.432L15.818 12l-6.273 3.568z"/></svg>`,
            instagram: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                      </svg>`,
            facebook: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/></svg>`,
            twitter: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M23.953 4.57a10 10 0 01-2.825.775 4.958 4.958 0 002.163-2.723c-.951.555-2.005.959-3.127 1.184a4.92 4.92 0 00-8.384 4.482C7.69 8.095 4.067 6.13 1.64 3.162a4.822 4.822 0 00-.666 2.475c0 1.71.87 3.213 2.188 4.096a4.904 4.904 0 01-2.228-.616v.06a4.923 4.923 0 003.946 4.827 4.996 4.996 0 01-2.212.085 4.936 4.936 0 004.604 3.417 9.867 9.867 0 01-6.102 2.105c-.39 0-.779-.023-1.17-.067a13.995 13.995 0 007.557 2.209c9.053 0 13.998-7.496 13.998-13.985 0-.21 0-.42-.015-.63A9.935 9.935 0 0024 4.59z"/></svg>`,
            tiktok: `<svg width="20" height="20" viewBox="0 0 24 24" fill="currentColor"><path d="M12.525.02c1.31-.02 2.61-.01 3.91-.02.08 1.53.63 3.09 1.75 4.17 1.12 1.11 2.7 1.62 4.24 1.79v4.03c-1.44-.05-2.89-.35-4.2-.97-.57-.26-1.1-.59-1.62-.93-.01 2.92.01 5.84-.02 8.75-.08 1.4-.54 2.79-1.35 3.94-1.31 1.92-3.58 3.17-5.91 3.21-1.43.08-2.86-.31-4.08-1.03-2.02-1.19-3.44-3.37-3.65-5.71-.02-.5-.03-1-.01-1.49.18-1.9 1.12-3.72 2.58-4.96 1.66-1.44 3.98-2.13 6.15-1.72.02 1.48-.04 2.96-.04 4.44-.99-.32-2.15-.23-3.02.37-.63.41-1.11 1.04-1.36 1.75-.21.51-.15 1.07-.14 1.61.24 1.64 1.82 3.02 3.5 2.87 1.12-.01 2.19-.66 2.77-1.61.19-.33.4-.67.41-1.06.1-1.79.06-3.57.07-5.36.01-4.03-.01-8.05.02-12.07z"/></svg>`
          };
          return `<a href="${url}" target="_blank" class="social-link ${platform}" aria-label="${platform}">
            ${socialIcons[platform] || ''}
          </a>`;
        }).join('') : '';

      const addressEncoded = encodeURIComponent(location.address || '');

      return `
        <div class="location-item${hasMapSection ? ' map-location' : ''}" data-location-id="${location.id}">
          ${!hasMapSection ? `
          <div class="location-image">
            <img src="${location.image}" alt="${location.name}" class="location-photo">
          </div>` : ''}
          <div class="location-content">
            <h2 class="location-name">${location.name}</h2>
            <p class="location-description">${location.description}</p>
            
            <div class="location-details">
              <div class="detail-item">
                <strong>Endereço</strong>
                <p class="location-address">${location.address}</p>
                <a href="https://www.google.com/maps/search/?api=1&query=${addressEncoded}" target="_blank" class="get-directions">Como chegar →</a>
              </div>
              
              ${location.hours ? `
              <div class="detail-item">
                <strong>Horário de Funcionamento</strong>
                <p class="location-hours">${location.hours}</p>
              </div>` : ''}
              
              <div class="detail-item">
                <strong>Entre em contato</strong>
                ${location.phone ? `<p class="location-phone">${location.phone}</p>` : ''}
                ${location.email ? `<p class="location-email">${location.email}</p>` : ''}
              </div>
              
              ${socialLinks ? `
              <div class="location-social">
                ${socialLinks}
              </div>` : ''}
            </div>
          </div>
          
          ${hasMapSection ? '<div class="location-map"></div>' : ''}
        </div>
      `;
    };

    return `<div 
      data-component="location" 
      data-element-type="location"
      data-title="${title}"
      data-subtitle="${subtitle}"
      data-layout="${layout}"
      data-locations='${JSON.stringify(locations)}'
      class="iluria-location-component"
      style="padding: ${sectionPadding}px 2rem; background: ${sectionBackground};">
      
      <div class="location-container">
        <div class="location-header">
          <h1 class="location-title" style="color: ${titleColor};">${title}</h1>
          <p class="location-subtitle">${subtitle}</p>
        </div>
        
        <div class="locations-grid layout-${layout}">
          ${locations.map(generateLocationItem).join('')}
        </div>
      </div>

      <!-- Script do Google Maps será injetado automaticamente pelo sistema de injeção -->
    </div>`;
  }
}

export default LocationConfig 