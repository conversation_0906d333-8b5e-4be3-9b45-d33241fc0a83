import axios from 'axios';
import { API_URLS } from '../config/api.config';

export const CouponService = {
  async listCoupons(filter = '', page = 0, size = 5) {
    try {
      const response = await axios.get(`${API_URLS.COUPON_BASE_URL}`, {
        params: { filter, page, size },
      });
      
      return {
        content: response.data.content || [],  
        totalPages: response.data.totalPages || 0  
      };
    } catch (error) {
      console.error('Erro ao listar cupons:', error);
      throw error;
    }
  },

  async getCoupon(couponId) {
    try {
      const response = await axios.get(`${API_URLS.COUPON_BASE_URL}/${couponId}`);
      
      if (response && response.data) {
        if (response.data.id && response.data.code) {
          return response.data;
        }
        
        if (response.data.data && response.data.data.id) {
          return response.data.data;
        }
        
        if (typeof response.data === 'object') {
          for (const key in response.data) {
            const obj = response.data[key];
            if (obj && typeof obj === 'object' && obj.id && obj.code) {
              return obj;
            }
          }
        }
        
        return null;
      } else {
        return null;
      }
    } catch (error) {
      console.error('Erro ao obter cupom:', error);
      throw error;
    }
  },

  async createCoupon(couponData) {
    try {
      // Endpoint atualizado para não incluir o storeId na URL
      const response = await axios.post(`${API_URLS.COUPON_BASE_URL}`, couponData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar cupom:', error);
      throw error;
    }
  },

  async updateCoupon(couponId, couponData) {
    try {
      const response = await axios.put(`${API_URLS.COUPON_BASE_URL}/${couponId}`, couponData);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar cupom:', error);
      throw error;
    }
  },

  async deleteCoupon(couponId) {
    try {
      const response = await axios.delete(`${API_URLS.COUPON_BASE_URL}/${couponId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir cupom:', error);
      throw error;
    }
  },

  async deleteCoupons(couponIds) {
    try {
      if (!Array.isArray(couponIds) || couponIds.length === 0) {
        throw new Error('IDs de cupons devem ser fornecidos como array não vazio');
      }

      const response = await axios.delete(`${API_URLS.COUPON_BASE_URL}/bulk`, {
        data: { couponIds }
      });

      return response.data;
    } catch (error) {
      console.error('Erro ao excluir cupons em massa:', error);
      throw error;
    }
  },


  generateRandomCode(length = 8) {
    const characters = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    let result = '';
    const charactersLength = characters.length;
    
    for (let i = 0; i < length; i++) {
      result += characters.charAt(Math.floor(Math.random() * charactersLength));
    }
    
    return result;
  }
};

export default CouponService;
