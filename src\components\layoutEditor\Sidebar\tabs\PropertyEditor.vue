<template>
  <div class="property-editor">
    <!-- Header do editor -->
    <div class="editor-header">
      <div class="header-info">
        <h3 class="editor-title">{{ editorTitle }}</h3>
        <p class="editor-subtitle">{{ editorSubtitle }}</p>
      </div>
      <button 
        v-if="!isRequired"
        class="close-button" 
        @click="$emit('close')"
        :title="$t('layoutEditor.closeEditor')"
      >
        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <!-- <PERSON><PERSON><PERSON><PERSON> do editor -->
    <div class="editor-content">
      <!-- Estado: Nenhum elemento selecionado -->
      <div v-if="!selectedElement" class="no-selection">
        <div class="no-selection-icon">
          <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M12.22 2h-.44a2 2 0 0 0-2 2v.18a2 2 0 0 1-1 1.73l-.43.25a2 2 0 0 1-2 0l-.15-.08a2 2 0 0 0-2.73.73l-.22.38a2 2 0 0 0 .73 2.73l.15.1a2 2 0 0 1 1 1.72v.51a2 2 0 0 1-1 1.74l-.15.09a2 2 0 0 0-.73 2.73l.22.38a2 2 0 0 0 2.73.73l.15-.08a2 2 0 0 1 2 0l.43.25a2 2 0 0 1 1 1.73V20a2 2 0 0 0 2 2h.44a2 2 0 0 0 2-2v-.18a2 2 0 0 1 1-1.73l.43-.25a2 2 0 0 1 2 0l.15.08a2 2 0 0 0 2.73-.73l.22-.39a2 2 0 0 0-.73-2.73l-.15-.08a2 2 0 0 1-1-1.74v-.5a2 2 0 0 1 1-1.74l.15-.09a2 2 0 0 0 .73-2.73l-.22-.38a2 2 0 0 0-2.73-.73l-.15.08a2 2 0 0 1-2 0l-.43-.25a2 2 0 0 1-1-1.73V4a2 2 0 0 0-2-2z"/>
            <circle cx="12" cy="12" r="3"/>
          </svg>
        </div>
        <h4>{{ $t('layoutEditor.selectElementToEdit') }}</h4>
        <p>{{ $t('layoutEditor.selectElementDesc') }}</p>
      </div>

      <!-- Estado: Carregando editor -->
      <div v-else-if="isLoading" class="loading-state">
        <div class="loading-spinner">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M21 12a9 9 0 11-6.219-8.56"/>
          </svg>
        </div>
        <p>{{ $t('layoutEditor.loadingEditor') }}</p>
      </div>

      <!-- Estado: Erro ao carregar editor -->
      <div v-else-if="hasError" class="error-state">
        <div class="error-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <line x1="15" y1="9" x2="9" y2="15"/>
            <line x1="9" y1="9" x2="15" y2="15"/>
          </svg>
        </div>
        <h4>{{ $t('layoutEditor.editorError') }}</h4>
        <p>{{ errorMessage }}</p>
        <button @click="retryLoadEditor" class="retry-button">
          {{ $t('layoutEditor.retry') }}
        </button>
      </div>

      <!-- Editor carregado dinamicamente -->
      <div v-else-if="currentEditor" class="dynamic-editor">
        <component 
          :is="currentEditor"
          :element="selectedElement"
          :key="editorKey"
          @close="handleEditorClose"
          @save="handleEditorSave"
          @data-changed="handleDataChanged"
        />
      </div>

      <!-- Fallback: Editor não encontrado -->
      <div v-else class="fallback-editor">
        <div class="fallback-icon">
          <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <path d="M9.09 9a3 3 0 015.83 1c0 2-3 3-3 3"/>
            <line x1="12" y1="17" x2="12.01" y2="17"/>
          </svg>
        </div>
        <h4>{{ $t('layoutEditor.editorNotFound') }}</h4>
        <p>{{ $t('layoutEditor.basicEditorsAvailable') }}</p>
        
        <!-- Editores básicos sempre disponíveis -->
        <div class="basic-editors">
          <button 
            v-for="basicEditor in basicEditors"
            :key="basicEditor.type"
            @click="loadBasicEditor(basicEditor.type)"
            class="basic-editor-btn"
          >
            <component :is="basicEditor.icon" />
            <span>{{ basicEditor.label }}</span>
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useEditorRegistry } from '@/components/layoutEditor/registry/EditorRegistry.js'
import { Settings01Icon, PaintBoardIcon, BorderTopIcon, MoveIcon } from '@hugeicons-pro/core-bulk-rounded'

const { t } = useI18n()
const { loadEditor, hasEditor } = useEditorRegistry()

const props = defineProps({
  selectedElement: {
    type: Object,
    default: null
  },
  editorType: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['close', 'element-updated', 'editor-closed'])

// Estado interno
const currentEditor = ref(null)
const isLoading = ref(false)
const hasError = ref(false)
const errorMessage = ref('')
const editorKey = ref(0)

// Editores básicos sempre disponíveis
const basicEditors = computed(() => [
  {
    type: 'spacing',
    label: t('layoutEditor.spacing'),
    icon: Settings01Icon
  },
  {
    type: 'border',
    label: t('layoutEditor.border'),
    icon: BorderTopIcon
  },
  {
    type: 'transform',
    label: t('layoutEditor.transform'),
    icon: MoveIcon
  }
])

// Informações do editor atual
const editorTitle = computed(() => {
  if (!props.selectedElement) {
    return t('layoutEditor.properties')
  }
  
  if (props.editorType) {
    const titleMap = {
      'header-config': t('layoutEditor.headerEditor'),
      'footer-config': t('layoutEditor.footerEditor'),
      'carousel-config': t('layoutEditor.carouselEditor'),
      'video-config': t('layoutEditor.videoEditor'),
      'company-information-config': t('layoutEditor.companyInfoEditor'),
      'location-config': t('layoutEditor.locationEditor'),
      'statement-config': t('layoutEditor.statementEditor'),
      'payment-benefits-config': t('layoutEditor.paymentBenefitsEditor'),
      'customer-review-config': t('layoutEditor.customerReviewEditor'),
      'special-offers-config': t('layoutEditor.specialOffersEditor'),
      'product-selection': t('layoutEditor.productSelection'),
      'product-style': t('layoutEditor.productStyle'),
      'spacing': t('layoutEditor.spacingEditor'),
      'border': t('layoutEditor.borderEditor'),
      'animation': t('layoutEditor.animationEditor'),
      'transform': t('layoutEditor.transformEditor'),
      'filter': t('layoutEditor.filterEditor'),
      'text': t('layoutEditor.textEditor'),
      'image': t('layoutEditor.imageEditor'),
      'link': t('layoutEditor.linkEditor')
    }
    
    return titleMap[props.editorType] || t('layoutEditor.elementEditor')
  }
  
  return t('layoutEditor.elementProperties')
})

const editorSubtitle = computed(() => {
  if (!props.selectedElement) {
    return t('layoutEditor.selectElementToEdit')
  }
  
  const elementType = props.selectedElement.tagName.toLowerCase()
  const dataComponent = props.selectedElement.getAttribute('data-component')
  
  if (dataComponent) {
    return t('layoutEditor.editingComponent', { component: dataComponent })
  }
  
  return t('layoutEditor.editingElement', { element: elementType })
})

// Verifica se o editor é obrigatório (não pode ser fechado)
const isRequired = computed(() => {
  return false // Por enquanto, todos os editores podem ser fechados
})

// Observar mudanças no tipo de editor
watch(() => props.editorType, async (newType) => {
  if (newType && props.selectedElement) {
    await loadSpecificEditor(newType)
  } else {
    currentEditor.value = null
  }
}, { immediate: true })

// Observar mudanças no elemento selecionado
watch(() => props.selectedElement, (newElement) => {
  if (!newElement) {
    currentEditor.value = null
    hasError.value = false
  } else if (props.editorType) {
    // Recarregar editor se necessário
    loadSpecificEditor(props.editorType)
  }
  
  // Forçar re-render do editor
  editorKey.value++
})

// Carregar editor específico
const loadSpecificEditor = async (editorType) => {

  
  isLoading.value = true
  hasError.value = false
  errorMessage.value = ''
  currentEditor.value = null
  
  try {
    // Verificar se o editor existe no registry
    if (!hasEditor(editorType)) {
      throw new Error(`Editor ${editorType} não encontrado no registry`)
    }
    
    // Carregar o editor
    const editor = await loadEditor(editorType)
    
    await nextTick()
    
    currentEditor.value = editor
    
  } catch (error) {

    hasError.value = true
    errorMessage.value = error.message || t('layoutEditor.failedToLoadEditor')
  } finally {
    isLoading.value = false
  }
}

// Carregar editor básico
const loadBasicEditor = async (basicType) => {

  await loadSpecificEditor(basicType)
}

// Tentar novamente carregar o editor
const retryLoadEditor = () => {
  if (props.editorType) {
    loadSpecificEditor(props.editorType)
  }
}

// Handlers
const handleEditorClose = () => {

  emit('close')
}

const handleEditorSave = (data) => {

  emit('element-updated', data)
}

const handleDataChanged = (data) => {

  emit('element-updated', data)
}
</script>

<style scoped>
.property-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--iluria-color-surface);
}

/* Header */
.editor-header {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  flex-shrink: 0;
}

.header-info {
  flex: 1;
  min-width: 0;
}

.editor-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
}

.editor-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

.close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  flex-shrink: 0;
  margin-left: 0.5rem;
}

.close-button:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

/* Content */
.editor-content {
  flex: 1;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

.dynamic-editor {
  flex: 1;
  overflow: auto;
}

/* Estados */
.no-selection,
.loading-state,
.error-state,
.fallback-editor {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

.no-selection-icon,
.error-icon,
.fallback-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.loading-spinner {
  width: 32px;
  height: 32px;
  margin-bottom: 1rem;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}

.no-selection h4,
.error-state h4,
.fallback-editor h4 {
  margin: 0 0 0.5rem 0;
  color: var(--iluria-color-text-primary);
  font-size: 1.125rem;
}

.no-selection p,
.loading-state p,
.error-state p,
.fallback-editor p {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
  line-height: 1.5;
}

/* Botões */
.retry-button {
  padding: 0.75rem 1.5rem;
  background: var(--iluria-color-primary);
  color: white;
  border: none;
  border-radius: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.retry-button:hover {
  background: var(--iluria-color-primary-hover);
  transform: translateY(-1px);
}

/* Editores básicos */
.basic-editors {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
  width: 100%;
  max-width: 300px;
}

.basic-editor-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  padding: 1rem 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-text-secondary);
  font-size: 0.75rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.basic-editor-btn:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary-border);
  color: var(--iluria-color-text-primary);
  transform: translateY(-1px);
}

.basic-editor-btn:active {
  transform: translateY(0);
}

/* Responsividade */
@media (max-width: 768px) {
  .editor-header {
    padding: 0.75rem;
  }
  
  .no-selection,
  .loading-state,
  .error-state,
  .fallback-editor {
    padding: 1.5rem;
  }
  
  .basic-editors {
    grid-template-columns: repeat(2, 1fr);
    max-width: 250px;
  }
  
  .basic-editor-btn {
    padding: 0.75rem 0.5rem;
    font-size: 0.7rem;
  }
}
</style> 
