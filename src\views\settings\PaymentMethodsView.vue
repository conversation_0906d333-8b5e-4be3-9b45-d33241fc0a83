<template>
    <div class="payment-methods-container">
        <IluriaHeader
            :title="t('paymentMethods.title')"
            :subtitle="t('paymentMethods.subtitle')"
        />
        <ViewContainer :title="t('paymentMethods.title')" :icon="CreditCardPosIcon" iconColor="blue">
            
            <!-- Mercado <PERSON>go -->
            <div class="payment-method-card">
                <div class="card-content">
                    <div class="logo-container">
                        <svg class="payment-logo-svg" viewBox="0 0 240 80" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="mercadoPagoGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#009EE3"/>
                                    <stop offset="100%" style="stop-color:#0074BC"/>
                                </linearGradient>
                                <linearGradient id="mpYellowGradient" x1="0%" y1="0%" x2="100%" y2="100%">
                                    <stop offset="0%" style="stop-color:#FFE000"/>
                                    <stop offset="100%" style="stop-color:#FFC107"/>
                                </linearGradient>
                            </defs>
                            <rect x="0" y="0" width="240" height="80" fill="url(#mercadoPagoGradient)" rx="12"/>
                            
                            <!-- Ícone de aperto de mão estilizado -->
                            <g transform="translate(30, 25)">
                                <circle cx="10" cy="15" r="8" fill="url(#mpYellowGradient)"/>
                                <circle cx="20" cy="15" r="8" fill="#FFF" opacity="0.9"/>
                                <path d="M5 15 Q10 10, 15 15 Q20 20, 25 15" stroke="#FFF" stroke-width="2" fill="none" stroke-linecap="round"/>
                            </g>
                            
                            <!-- Texto Mercado Pago -->
                            <text x="90" y="35" font-family="system-ui, sans-serif" font-size="14" font-weight="bold" fill="#FFF">Mercado</text>
                            <text x="90" y="50" font-family="system-ui, sans-serif" font-size="14" font-weight="bold" fill="#FFE000">Pago</text>
                            
                            <!-- Elementos decorativos -->
                            <circle cx="200" cy="25" r="2" fill="#FFF" opacity="0.6"/>
                            <circle cx="210" cy="35" r="1.5" fill="#FFE000" opacity="0.8"/>
                            <circle cx="195" cy="45" r="1.8" fill="#FFF" opacity="0.4"/>
                            <circle cx="215" cy="55" r="2.2" fill="#FFE000" opacity="0.6"/>
                        </svg>
                    </div>
                    <div class="info-container">
                        <h1 class="payment-title">Mercado Pago</h1>
                        <p class="payment-description">
                            {{ t('paymentMethods.descriptionMP') }}
                        </p>
                    </div>
                    <div class="action-container">
                        <IluriaButton @click="connectMercadoPago">
                            {{ isConnectedMP ? t('paymentMethods.disconnectButton') : t('paymentMethods.connectButton') }}
                        </IluriaButton>
                        <div v-if="isConnectedMP" class="status-indicator">
                            <HugeiconsIcon :icon="CheckmarkCircle02Icon" color="#0F6908" :stroke-width="2" size="20" />
                            <label class="status-label">{{
                                t('paymentMethods.connectedLabel') }}</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PagBank -->
            <div class="payment-method-card">
                <div class="card-content">
                    <div class="logo-container">
                        <svg class="payment-logo-svg" viewBox="0 0 240 80" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="pagBankGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#FF6900"/>
                                    <stop offset="100%" style="stop-color:#E55A00"/>
                                </linearGradient>
                            </defs>
                            <rect x="0" y="0" width="240" height="80" fill="url(#pagBankGradient)" rx="12"/>
                            
                            <!-- Ícone de transferência/pagamento moderno -->
                            <g transform="translate(28, 28)">
                                <rect x="0" y="8" width="24" height="16" rx="3" fill="#FFF" opacity="0.9"/>
                                <rect x="2" y="10" width="20" height="2" fill="#FF6900"/>
                                <rect x="2" y="14" width="12" height="2" fill="#FF6900"/>
                                <rect x="16" y="14" width="4" height="2" fill="#FF6900"/>
                                <circle cx="22" cy="4" r="3" fill="#FFF"/>
                                <path d="M20 4 L22 2 L24 4" stroke="#FF6900" stroke-width="1.5" fill="none" stroke-linecap="round" stroke-linejoin="round"/>
                            </g>
                            
                            <text x="120" y="50" font-family="system-ui, sans-serif" font-size="20" font-weight="700" text-anchor="middle" fill="#FFF">PagBank</text>
                        </svg>
                    </div>
                    <div class="info-container">
                        <h1 class="payment-title">PagBank</h1>
                        <p class="payment-description">
                            {{ t('paymentMethods.descriptionPagBank') }}
                        </p>
                    </div>
                    <div class="action-container">
                        <IluriaButton @click="connectPagBank">
                            {{ isConnectedPagBank ? t('paymentMethods.disconnectButton') : t('paymentMethods.connectButton') }}
                        </IluriaButton>
                        <div v-if="isConnectedPagBank" class="status-indicator">
                            <HugeiconsIcon :icon="CheckmarkCircle02Icon" color="#0F6908" :stroke-width="2" size="20" />
                            <label class="status-label">{{
                                t('paymentMethods.connectedLabel') }}</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PayPal -->
            <div class="payment-method-card">
                <div class="card-content">
                    <div class="logo-container">
                        <svg class="payment-logo-svg" viewBox="0 0 240 80" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="paypalGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#003087"/>
                                    <stop offset="100%" style="stop-color:#012169"/>
                                </linearGradient>
                            </defs>
                            <rect x="0" y="0" width="240" height="80" fill="url(#paypalGradient)" rx="12"/>
                            <ellipse cx="50" cy="40" rx="15" ry="20" fill="#009CDE" opacity="0.8"/>
                            <ellipse cx="60" cy="40" rx="10" ry="15" fill="#012169" opacity="0.6"/>
                            <text x="120" y="45" font-family="Verdana, sans-serif" font-size="22" font-weight="bold" text-anchor="middle" fill="#009CDE">Pay</text>
                            <text x="160" y="45" font-family="Verdana, sans-serif" font-size="22" font-weight="bold" text-anchor="middle" fill="#FFC439">Pal</text>
                        </svg>
                    </div>
                    <div class="info-container">
                        <h1 class="payment-title">PayPal</h1>
                        <p class="payment-description">
                            {{ t('paymentMethods.descriptionPayPal') }}
                        </p>
                    </div>
                    <div class="action-container">
                        <IluriaButton @click="connectPayPal">
                            {{ isConnectedPayPal ? t('paymentMethods.disconnectButton') : t('paymentMethods.connectButton') }}
                        </IluriaButton>
                        <div v-if="isConnectedPayPal" class="status-indicator">
                            <HugeiconsIcon :icon="CheckmarkCircle02Icon" color="#0F6908" :stroke-width="2" size="20" />
                            <label class="status-label">{{
                                t('paymentMethods.connectedLabel') }}</label>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Stripe -->
            <div class="payment-method-card">
                <div class="card-content">
                    <div class="logo-container">
                        <svg class="payment-logo-svg" viewBox="0 0 240 80" xmlns="http://www.w3.org/2000/svg">
                            <defs>
                                <linearGradient id="stripeGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                                    <stop offset="0%" style="stop-color:#6772E5"/>
                                    <stop offset="100%" style="stop-color:#5469D4"/>
                                </linearGradient>
                            </defs>
                            <rect x="0" y="0" width="240" height="80" fill="url(#stripeGradient)" rx="12"/>
                            <path d="M30 40 Q45 25, 65 40 Q80 55, 95 40" stroke="#FFF" stroke-width="3" fill="none" opacity="0.6"/>
                            <text x="140" y="45" font-family="system-ui, sans-serif" font-size="24" font-weight="600" text-anchor="middle" fill="#FFF">Stripe</text>
                            <circle cx="200" cy="30" r="3" fill="#FFF" opacity="0.4"/>
                            <circle cx="210" cy="35" r="2" fill="#FFF" opacity="0.3"/>
                            <circle cx="205" cy="50" r="2.5" fill="#FFF" opacity="0.5"/>
                        </svg>
                    </div>
                    <div class="info-container">
                        <h1 class="payment-title">Stripe</h1>
                        <p class="payment-description">
                            {{ t('paymentMethods.descriptionStripe') }}
                        </p>
                    </div>
                    <div class="action-container">
                        <IluriaButton @click="connectStripe">
                            {{ isConnectedStripe ? t('paymentMethods.disconnectButton') : t('paymentMethods.connectButton') }}
                        </IluriaButton>
                        <div v-if="isConnectedStripe" class="status-indicator">
                            <HugeiconsIcon :icon="CheckmarkCircle02Icon" color="#0F6908" :stroke-width="2" size="20" />
                            <label class="status-label">{{
                                t('paymentMethods.connectedLabel') }}</label>
                        </div>
                    </div>
                </div>
            </div>

        </ViewContainer>
    </div>
</template>

<script setup>
import { ref, onMounted } from 'vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { HugeiconsIcon } from '@hugeicons/vue';
import { CheckmarkCircle02Icon, CreditCardPosIcon } from '@hugeicons-pro/core-stroke-standard';
import PaymentIntegrationService from '@/services/paymentIntegration.service';
import storeService from '@/services/store.service';
import { useToast } from '@/services/toast.service';
import { useI18n } from 'vue-i18n';

const { showSuccess, showError } = useToast();

const paymentIntegrationService = new PaymentIntegrationService();

const isConnectedMP = ref(false);
const isConnectedPagBank = ref(false);
const isConnectedPayPal = ref(false);
const isConnectedStripe = ref(false);
const storeId = ref(null);
const { t } = useI18n()

onMounted(async () => {
    await getStoreId();

    const statusMP = await paymentIntegrationService.getMercadoPagoStatus(storeId.value);
    isConnectedMP.value = statusMP === true;

    const statusPagBank = await paymentIntegrationService.getPagBankStatus(storeId.value);
    isConnectedPagBank.value = statusPagBank === true;

    const statusPayPal = await paymentIntegrationService.getPayPalStatus(storeId.value);
    isConnectedPayPal.value = statusPayPal === true;

    const statusStripe = await paymentIntegrationService.getStripeStatus(storeId.value);
    isConnectedStripe.value = statusStripe === true;

    // Verificar redirecionamentos
    const wasRedirectedMP = localStorage.getItem('redirectingToMercadoPago');
    if (wasRedirectedMP === 'true') {
        localStorage.removeItem('redirectingToMercadoPago');
        if (isConnectedMP.value) {
            showSuccess('Mercado Pago conectado com sucesso');
        } else {
            showError('Erro ao conectar Mercado Pago');
        }
    }

    const wasRedirectedPagBank = localStorage.getItem('redirectingToPagBank');
    if (wasRedirectedPagBank === 'true') {
        localStorage.removeItem('redirectingToPagBank');
        if (isConnectedPagBank.value) {
            showSuccess('PagBank conectado com sucesso');
        } else {
            showError('Erro ao conectar PagBank');
        }
    }

    const wasRedirectedPayPal = localStorage.getItem('redirectingToPayPal');
    if (wasRedirectedPayPal === 'true') {
        localStorage.removeItem('redirectingToPayPal');
        if (isConnectedPayPal.value) {
            showSuccess('PayPal conectado com sucesso');
        } else {
            showError('Erro ao conectar PayPal');
        }
    }

    const wasRedirectedStripe = localStorage.getItem('redirectingToStripe');
    if (wasRedirectedStripe === 'true') {
        localStorage.removeItem('redirectingToStripe');
        if (isConnectedStripe.value) {
            showSuccess('Stripe conectado com sucesso');
        } else {
            showError('Erro ao conectar Stripe');
        }
    }
});

const getStoreId = async () => {
    const response = await storeService.getStoreData();
    storeId.value = response.id;
}

const connectMercadoPago = async () => {
    try {
        if (isConnectedMP.value === false) {
            localStorage.setItem('redirectingToMercadoPago', 'true');
            window.location.href = paymentIntegrationService.authMercadoPago(storeId.value);
        } else {
            await paymentIntegrationService.disconnectMercadoPago(storeId.value);
            const status = await paymentIntegrationService.getMercadoPagoStatus(storeId.value);
            if (status == false) {
                isConnectedMP.value = false;
                showSuccess('Desconectado com sucesso');
            } else {
                showError('Erro ao desconectar');
            }
        }
    } catch (error) {
        console.error(error);
    };
}

const connectPagBank = async () => {
    try {
        if (isConnectedPagBank.value === false) {
            localStorage.setItem('redirectingToPagBank', 'true');
            window.location.href = paymentIntegrationService.authPagBank(storeId.value);
        } else {
            await paymentIntegrationService.disconnectPagBank(storeId.value);
            const status = await paymentIntegrationService.getPagBankStatus(storeId.value);
            if (status == false) {
                isConnectedPagBank.value = false;
                showSuccess('Desconectado com sucesso');
            } else {
                showError('Erro ao desconectar');
            }
        }
    } catch (error) {
        console.error(error);
    };
}

const connectPayPal = async () => {
    try {
        if (isConnectedPayPal.value === false) {
            localStorage.setItem('redirectingToPayPal', 'true');
            window.location.href = paymentIntegrationService.authPayPal(storeId.value);
        } else {
            await paymentIntegrationService.disconnectPayPal(storeId.value);
            const status = await paymentIntegrationService.getPayPalStatus(storeId.value);
            if (status == false) {
                isConnectedPayPal.value = false;
                showSuccess('Desconectado com sucesso');
            } else {
                showError('Erro ao desconectar');
            }
        }
    } catch (error) {
        console.error(error);
    };
}

const connectStripe = async () => {
    try {
        if (isConnectedStripe.value === false) {
            localStorage.setItem('redirectingToStripe', 'true');
            window.location.href = paymentIntegrationService.authStripe(storeId.value);
        } else {
            await paymentIntegrationService.disconnectStripe(storeId.value);
            const status = await paymentIntegrationService.getStripeStatus(storeId.value);
            if (status == false) {
                isConnectedStripe.value = false;
                showSuccess('Desconectado com sucesso');
            } else {
                showError('Erro ao desconectar');
            }
        }
    } catch (error) {
        console.error(error);
    };
}
</script>

<style scoped>

.payment-methods-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: var(--iluria-color-background);
    min-height: 100vh;
}

.payment-method-card {
    background: var(--iluria-color-card-bg);
    border: 1px solid var(--iluria-color-border);
    border-radius: 12px;
    padding: 20px;
    margin-bottom: 20px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.1);
    min-height: 160px;
    display: flex;
    align-items: center;
}

.card-content {
    display: flex;
    width: 100%;
    align-items: center;
    gap: 20px;
}

.logo-container {
    flex: 0 0 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    height: 90px;
}

.payment-logo {
    max-width: 100%;
    max-height: 100%;
    object-fit: contain;
}

.payment-logo-svg {
    width: 200px;
    height: 70px;
}

.info-container {
    flex: 1;
    display: flex;
    flex-direction: column;
    gap: 8px;
    min-height: 90px;
    justify-content: center;
}

.payment-title {
    color: var(--iluria-color-text);
    font-size: 24px;
    font-weight: 700;
    margin: 0;
    line-height: 1.2;
}

.payment-description {
    color: var(--iluria-color-text-secondary);
    font-size: 14px;
    line-height: 1.5;
    margin: 0;
}

.action-container {
    flex: 0 0 160px;
    display: flex;
    flex-direction: column;
    gap: 12px;
    align-items: center;
    justify-content: center;
    height: 90px;
}

.status-indicator {
    display: flex;
    align-items: center;
    gap: 8px;
}

.status-label {
    font-size: 12px;
    color: var(--iluria-color-text-secondary);
    margin: 0;
}
</style>