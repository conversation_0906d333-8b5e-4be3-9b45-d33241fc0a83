<!-- VideoLoader.vue - Sistema moderno de carregamento de vídeos -->
<template>
  <div>
    <!-- Este componente é apenas para exportar a função de injeção -->
  </div>
</template>

<script>
/**
 * Sistema moderno de carregamento de componentes de vídeo - VideoLoader.vue
 * 
 * Integrado com o sistema centralizado de injeção de scripts
 * Suporte para YouTube, Vimeo, vídeos diretos e incorporados
 * 
 * Recursos:
 * - Auto-detecção de tipo de vídeo
 * - Player responsivo
 * - Configurações avançadas (autoplay, loop, controls, etc.)
 * - Integração com sistema de edição
 */

import { nextTick } from 'vue'

/**
 * Injeta scripts e funcionalidades para componentes de vídeo
 * @param {Document} document - Documento onde injetar
 * @param {string} authToken - Token de autenticação (não usado para vídeos)
 */
export function injectVideoScript(document, authToken = '') {
  try {

    
    if (!document) {
      console.error('❌ [VideoLoader] Documento não fornecido')
      return
    }

    // Busca todos os componentes de vídeo
    const videoSelectors = [
      '[data-component="video"]',
      '.iluria-video',
      '[data-element-type="video"]'
    ]
    
    const videoElements = document.querySelectorAll(videoSelectors.join(', '))


    if (videoElements.length === 0) {

      return
    }

    // Processa cada elemento de vídeo
    videoElements.forEach((element, index) => {
      try {

        processVideoElement(element, document)
      } catch (error) {
        console.error(`❌ [VideoLoader] Erro ao processar vídeo ${index + 1}:`, error)
      }
    })


    
  } catch (error) {
    console.error('❌ [VideoLoader] Erro geral na injeção de scripts:', error)
  }
}

/**
 * Processa um elemento de vídeo individual
 * @param {Element} element - Elemento de vídeo
 * @param {Document} document - Documento
 */
function processVideoElement(element, document) {
  if (!element) return
  
  // Evita reprocessamento
  if (element.hasAttribute('data-video-processed')) {

    return
  }


  
  // Carrega configuração do elemento
  const config = loadVideoConfig(element)

  
  // Aplica a estrutura HTML
  setupVideoHTML(element, config)
  
  // ✅ REMOVIDO: applyVideoStyles() - causava conflitos CSS
  // Agora usa apenas estilos inline do useComponentRegistry.js
  
  // Inicializa funcionalidades
  initializeVideoFeatures(element, config)
  
  // Marca como processado
  element.setAttribute('data-video-processed', 'true')
  

}

/**
 * Carrega configuração do vídeo a partir dos atributos
 * @param {Element} element - Elemento de vídeo
 * @returns {Object} Configuração do vídeo
 */
function loadVideoConfig(element) {
  return {
    title: element.getAttribute('data-video-title') || 'Principais itens básicos essenciais',
    description: element.getAttribute('data-video-description') || 'Não sabe por onde começar para criar um guarda-roupa cápsula? Comece pelo básico. Crie uma base consistente com a nossa nova coleção básica unissex. Confira o vídeo para algumas combinações fáceis.',
    buttonText: element.getAttribute('data-button-text') || 'Compre agora',
    buttonUrl: element.getAttribute('data-button-url') || '#',
    buttonEnabled: element.getAttribute('data-button-enabled') !== 'false',
    
    // Configurações de vídeo
    videoUrl: element.getAttribute('data-video-url') || '',
    videoType: element.getAttribute('data-video-type') || 'youtube',
    videoPoster: element.getAttribute('data-video-poster') || '',
    videoAutoplay: element.getAttribute('data-video-autoplay') === 'true',
    videoLoop: element.getAttribute('data-video-loop') === 'true',
    videoMuted: element.getAttribute('data-video-muted') === 'true',
    videoControls: element.getAttribute('data-video-controls') !== 'false',
    
    // Layout e estilo
    layout: element.getAttribute('data-layout') || 'horizontal',
    videoPosition: element.getAttribute('data-video-position') || 'left',
    backgroundColor: element.getAttribute('data-bg-color') || '#2c3e50',
    textColor: element.getAttribute('data-text-color') || '#ffffff',
    buttonColor: element.getAttribute('data-button-color') || '#3498db',
    buttonTextColor: element.getAttribute('data-button-text-color') || '#ffffff',
    
    // Dimensões
    paddingTop: parseInt(element.getAttribute('data-padding-top') || '60'),
    paddingBottom: parseInt(element.getAttribute('data-padding-bottom') || '60'),
    borderRadius: parseInt(element.getAttribute('data-border-radius') || '12'),
    videoAspectRatio: element.getAttribute('data-video-aspect-ratio') || '16:9'
  }
}

/**
 * Configura a estrutura HTML do componente de vídeo
 * @param {Element} element - Elemento de vídeo
 * @param {Object} config - Configuração do vídeo
 */
function setupVideoHTML(element, config) {
  const videoSection = generateVideoSection(config)
  const contentSection = generateContentSection(config)
  
  const isVertical = config.layout === 'vertical'
  const videoFirst = config.videoPosition === 'left' || config.videoPosition === 'top'
  
  const html = `
    <div class="video-container layout-${config.layout} video-${config.videoPosition}">
      <div class="video-inner-container">
        <div class="video-content">
          ${videoFirst ? videoSection : ''}
          ${contentSection}
          ${!videoFirst ? videoSection : ''}
        </div>
      </div>
    </div>
  `
  
  element.innerHTML = html

}

/**
 * Gera seção do vídeo
 * @param {Object} config - Configuração do vídeo
 * @returns {string} HTML da seção de vídeo
 */
function generateVideoSection(config) {
  let videoHTML = ''
  
  if (config.videoUrl) {
    const videoId = extractVideoId(config.videoUrl, config.videoType)
    
    switch (config.videoType) {
              case 'youtube':
        videoHTML = generateYouTubeEmbed(videoId, config)
        break
              case 'vimeo':
        videoHTML = generateVimeoEmbed(videoId, config)
        break
              case 'direct':
        videoHTML = generateDirectVideo(config.videoUrl, config)
        break
              default:
        videoHTML = generatePlaceholderVideo(config)
    }
  } else {
    videoHTML = generatePlaceholderVideo(config)
  }
  
  return `
    <div class="video-section">
      <div class="video-wrapper" style="aspect-ratio: ${config.videoAspectRatio.replace(':', '/')}">
        ${videoHTML}
      </div>
    </div>
  `
}

/**
 * Gera embed do YouTube
 * @param {string} videoId - ID do vídeo
 * @param {Object} config - Configuração
 * @returns {string} HTML do embed
 */
function generateYouTubeEmbed(videoId, config) {
  if (!videoId) return generatePlaceholderVideo(config)
  
  const params = new URLSearchParams({
    autoplay: config.videoAutoplay ? '1' : '0',
    loop: config.videoLoop ? '1' : '0',
    mute: config.videoMuted ? '1' : '0',
    controls: config.videoControls ? '1' : '0',
    rel: '0',
    modestbranding: '1'
  })
  
  return `
    <iframe
      src="https://www.youtube.com/embed/${videoId}?${params.toString()}"
      title="YouTube video player"
      frameborder="0"
      allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
      allowfullscreen
      class="video-iframe">
    </iframe>
  `
}

/**
 * Gera embed do Vimeo
 * @param {string} videoId - ID do vídeo
 * @param {Object} config - Configuração
 * @returns {string} HTML do embed
 */
function generateVimeoEmbed(videoId, config) {
  if (!videoId) return generatePlaceholderVideo(config)
  
  const params = new URLSearchParams({
    autoplay: config.videoAutoplay ? '1' : '0',
    loop: config.videoLoop ? '1' : '0',
    muted: config.videoMuted ? '1' : '0',
    controls: config.videoControls ? '1' : '0'
  })
  
  return `
    <iframe
      src="https://player.vimeo.com/video/${videoId}?${params.toString()}"
      title="Vimeo video player"
      frameborder="0"
      allow="autoplay; fullscreen; picture-in-picture"
      allowfullscreen
      class="video-iframe">
    </iframe>
  `
}

/**
 * Gera vídeo direto (MP4, WebM, etc.)
 * @param {string} url - URL do vídeo
 * @param {Object} config - Configuração
 * @returns {string} HTML do vídeo
 */
function generateDirectVideo(url, config) {
  const attributes = [
    config.videoControls ? 'controls' : '',
    config.videoAutoplay ? 'autoplay' : '',
    config.videoLoop ? 'loop' : '',
    config.videoMuted ? 'muted' : '',
    'playsinline'
  ].filter(Boolean).join(' ')
  
  return `
    <video 
      class="video-element" 
      ${attributes}
      ${config.videoPoster ? `poster="${config.videoPoster}"` : ''}>
      <source src="${url}" type="video/mp4">
      <p>Seu navegador não suporta vídeos HTML5.</p>
    </video>
  `
}

/**
 * Gera placeholder quando não há vídeo
 * @param {Object} config - Configuração
 * @returns {string} HTML do placeholder
 */
function generatePlaceholderVideo(config) {
  return `
    <div class="video-placeholder">
      <div class="placeholder-content">
        <svg class="placeholder-icon" width="64" height="64" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polygon points="5,3 19,12 5,21"/>
        </svg>
        <p class="placeholder-text">Configure o vídeo para visualizar</p>
      </div>
    </div>
  `
}

/**
 * Gera seção de conteúdo
 * @param {Object} config - Configuração
 * @returns {string} HTML do conteúdo
 */
function generateContentSection(config) {
  const buttonHTML = config.buttonEnabled ? `
    <a href="${config.buttonUrl}" class="video-button">
      ${config.buttonText}
    </a>
  ` : ''
  
  return `
    <div class="content-section">
      <h2 class="video-title">${config.title}</h2>
      <p class="video-description">${config.description}</p>
      ${buttonHTML}
    </div>
  `
}

/**
 * ✅ REMOVIDO: Sistema de injeção CSS duplicado que causava conflitos
 * Agora usa apenas os estilos inline consistentes do useComponentRegistry.js
 */

/**
 * Inicializa funcionalidades do vídeo
 * @param {Element} element - Elemento de vídeo
 * @param {Object} config - Configuração
 */
function initializeVideoFeatures(element, config) {
  // Adiciona classe principal
  element.classList.add('iluria-video')
  
  // Event listeners para botões
  const buttons = element.querySelectorAll('.video-button, button')
  buttons.forEach(button => {
    button.addEventListener('click', (e) => {
      if (!button.href || button.href === '#' || button.href.endsWith('#')) {
        e.preventDefault()
      }
    })
  })
  
  // Otimização para vídeos incorporados
  nextTick(() => {
    const iframe = element.querySelector('.video-iframe')
    if (iframe) {
      iframe.addEventListener('load', () => {

      })
    }
  })
  
  
}

/**
 * Extrai ID do vídeo baseado na URL e tipo
 * @param {string} url - URL do vídeo
 * @param {string} type - Tipo do vídeo
 * @returns {string} ID do vídeo
 */
function extractVideoId(url, type) {
  if (!url) return ''
  
  switch (type) {
    case 'youtube':
      return extractYouTubeId(url)
    case 'vimeo':
      return extractVimeoId(url)
    default:
      return ''
  }
}

/**
 * Extrai ID do YouTube de diferentes formatos de URL
 * @param {string} url - URL do YouTube
 * @returns {string} ID do vídeo
 */
function extractYouTubeId(url) {
  const patterns = [
    /(?:youtube\.com\/watch\?v=|youtu\.be\/|youtube\.com\/embed\/)([^&\n?#]+)/,
    /^([a-zA-Z0-9_-]{11})$/
  ]
  
  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match) return match[1]
  }
  
  return ''
}

/**
 * Extrai ID do Vimeo de diferentes formatos de URL
 * @param {string} url - URL do Vimeo
 * @returns {string} ID do vídeo
 */
function extractVimeoId(url) {
  const patterns = [
    /vimeo\.com\/(\d+)/,
    /player\.vimeo\.com\/video\/(\d+)/,
    /^(\d+)$/
  ]
  
  for (const pattern of patterns) {
    const match = url.match(pattern)
    if (match) return match[1]
  }
  
  return ''
}

// ✅ REMOVIDO: darkenColor() - não mais necessária após remoção do sistema CSS conflitante

export default {
  name: 'VideoLoader'
  }
</script>
