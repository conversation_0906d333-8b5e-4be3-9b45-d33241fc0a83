<template>
  <div>
    <!-- Alerta para mudança na estrutura de variações -->
    <div v-if="hasVariationStructureChanged" class="mb-4 p-4 bg-[var(--iluria-color-warning-bg)] border border-[var(--iluria-color-warning)] rounded-lg">
      <div class="flex items-start">
        <HugeiconsIcon :icon="Alert01Icon" class="text-[var(--iluria-color-warning)] mr-3 mt-0.5" :size="20" />
        <div>
          <h4 class="text-[var(--iluria-color-warning)] font-medium">{{ t('product.variationStructureChanged') }}</h4>
          <p class="text-[var(--iluria-color-warning)] text-sm mt-1">
            {{ t('product.variationStructureChangedHelp') }}
          </p>
        </div>
      </div>
    </div>

    <!-- Group By and Search Section -->
    <div class="mb-6">
      <!-- Group By, Filters, Search and Clear All in one row -->
      <div class="flex items-start justify-between gap-6 mb-4">
        <!-- Left side: Group By and Filters -->
        <div class="flex items-start gap-6 flex-1">
          <!-- Group By -->
          <div class="flex flex-col gap-2">
            <span class="text-[var(--iluria-color-text-primary)] font-medium text-sm">{{ t('productVariations.groupBy') }}</span>
            <IluriaSelect 
              id="group-by-select"
              :model-value="groupBy" 
              @update:model-value="$emit('update:groupBy', $event)"
              class="w-48"
              :options="variationOptions"
              :placeholder="t('productVariations.groupBy')"
            />
          </div>
          
          <!-- Filters -->
          <div class="flex flex-col gap-2">
            <span class="text-[var(--iluria-color-text-primary)] font-medium text-sm">{{ t('productVariations.filter') }}</span>
            <div class="flex flex-wrap gap-2">
              <div v-for="variation in variations" :key="variation.name" class="relative">
                <IluriaButton 
                  v-if="!selectedFilters[variation.name]"
                  @click="toggleFilterOptions(variation.name)"
                  color="outline"
                  class="flex items-center gap-2"
                >
                  <span class="flex items-center gap-1">
                    {{ getFilterLabel(variation) }}
                    <HugeiconsIcon 
                      :icon="ArrowDown01Icon" 
                      :size="12" 
                    />
                  </span>
                </IluriaButton>
                <IluriaButton 
                  v-else
                  @click.stop="clearFilter(variation.name)" 
                  color="outline"
                  class="flex items-center gap-2 bg-[var(--iluria-color-primary-light)] border-[var(--iluria-color-primary)] text-[var(--iluria-color-primary)] hover:bg-red-100 hover:border-red-500 hover:text-red-600 transition-colors"
                >
                  <span class="flex items-center gap-1">
                    {{ getFilterLabel(variation) }}
                    <HugeiconsIcon 
                      :icon="Cancel01Icon" 
                      :size="12" 
                      class="text-[var(--iluria-color-danger)]"
                    />
                  </span>
                </IluriaButton>

                <!-- Filter Options Dropdown -->
                <div 
                  v-if="openFilter === variation.name"
                  class="absolute top-full left-0 mt-1 bg-[var(--iluria-color-surface)] rounded-md shadow-lg border border-[var(--iluria-color-border)] min-w-[200px] z-10"
                >
                  <div class="p-2 space-y-2">
                    <label 
                      v-for="option in variation.options" 
                      :key="option"
                      class="flex items-center gap-2 p-2 hover:bg-[var(--iluria-color-hover)] cursor-pointer rounded text-[var(--iluria-color-text-primary)]"
                    >
                      <input 
                        type="radio" 
                        :name="variation.name"
                        :value="option"
                        :checked="selectedFilters[variation.name] === option"
                        @change="selectFilter(variation.name, option)"
                        class="text-[var(--iluria-color-primary)] w-4 h-4 flex-shrink-0"
                      >
                      <span class="label-radio">{{ option }}</span>
                    </label>
                    <div class="pt-2 border-t border-[var(--iluria-color-border)]">
                      <IluriaButton 
                        @click="clearFilter(variation.name)"
                        color="text-primary"
                        size="small"
                      >
                        {{ t('productVariations.clear') }}
                      </IluriaButton>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <!-- Right side: Search and Clear All -->
        <div class="flex items-start gap-3">
          <!-- Search -->
          <div class="flex flex-col gap-2">
            <span class="text-[var(--iluria-color-text-primary)] font-medium text-sm">{{ t('productVariations.searchVariations') }}</span>
            <div class="relative">
              <IluriaInputText
                id="search-variations"
                v-model="searchQuery"

                :placeholder="t('productVariations.searchVariations')"
                class="w-64 pr-10"
                @keydown.esc="searchQuery = ''"
              />
            </div>
          </div>
          
          <!-- Clear All Button - always visible -->
          <div class="flex flex-col gap-2">
            <span class="text-transparent font-medium text-sm">.</span>
            <IluriaButton
              @click="clearAllData"
              color="outline"
              :huge-icon="RefreshIcon"
            >
              {{ t('productVariations.clearAll') }}
            </IluriaButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Show table only if there are filtered results -->
    <div v-if="filteredGroupedVariations.length > 0" class="mt-4">
      <table :key="tableKey" class="min-w-full rounded-lg bg-[var(--iluria-color-surface)] overflow-hidden shadow-sm variations-table">
        <thead>
          <tr class="text-[var(--iluria-color-text-secondary)] border-b bg-[var(--iluria-color-surface-hover)]">
            <th class="py-3 px-4 font-semibold text-left" style="min-width: 300px;">{{ t('productVariations.variationOptions') }}</th>
            <th class="py-3 px-4 font-semibold text-center" style="min-width: 180px;">{{ t('productVariations.price') }}</th>
            <th class="py-3 px-4 font-semibold text-center" style="min-width: 400px;">{{ t('productVariations.stock') }}</th>
            <th class="py-3 px-4 font-semibold text-center" style="min-width: 130px;">{{ t('productVariations.actions') }}</th>
          </tr>
        </thead>
        <tbody>
          <template v-for="group in filteredGroupedVariations" :key="group.name">
            <!-- Group Header Row -->
            <tr class="bg-[var(--iluria-color-surface-hover)] cursor-pointer hover:bg-[var(--iluria-color-hover)] transition-colors" @click="toggleGroup(group.name)">
              <td class="py-3 px-4 font-medium flex items-center text-[var(--iluria-color-text-primary)]">
                <HugeiconsIcon 
                  :icon="collapsedGroups.includes(group.name) ? ArrowRight01Icon : ArrowDown01Icon"
                  class="mr-2 text-[var(--iluria-color-text-secondary)] transition-transform"
                  :size="16"
                />
                {{ group.name }}
              </td>
              <td class="py-3 px-4 text-center">
                <span v-if="group.priceRange" class="text-sm text-[var(--iluria-color-text-secondary)]">{{ group.priceRange }}</span>
              </td>
              <td class="py-3 px-4 text-center">
                <span v-if="group.totalStock" class="text-sm text-[var(--iluria-color-text-secondary)]">{{ group.totalStock }}</span>
              </td>
              <td class="py-3 px-4 text-center"></td>
            </tr>
            
            <!-- Variation Items -->
            <tr 
              v-for="item in group.items" 
              :key="item.id"
              v-show="!collapsedGroups.includes(group.name)"
              class="border-b border-[var(--iluria-color-border)] hover:bg-[var(--iluria-color-hover)] transition-colors"
            >
              <td class="py-3 px-4 pl-8">
                <div class="flex items-center space-x-3">
                  <div 
                    class="w-12 h-12 bg-[var(--iluria-color-surface-hover)] rounded-lg overflow-hidden flex items-center justify-center cursor-pointer hover:bg-[var(--iluria-color-hover)] transition-colors border-2 border-transparent hover:border-[var(--iluria-color-primary)]"
                    :class="{ 'border-dashed border-[var(--iluria-color-primary)]': isNewVariation(item) }"
                    @click="openPhotoUpload(item)"
                  >
                    <img 
                      v-if="item.photos?.length" 
                      :src="getPreviewUrl(item.photos[0])" 
                      class="w-full h-full object-cover"
                      alt="Variation preview"
                    />
                    <HugeiconsIcon 
                      v-else 
                      :icon="Camera01Icon" 
                      class="text-[var(--iluria-color-text-tertiary)]" 
                      :size="20" 
                    />
                  </div>
                  <div class="flex flex-col">
                    <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">{{ getVariationLabel(item) }}</span>
                    <span v-if="isNewVariation(item)" class="text-xs text-[var(--iluria-color-primary)] font-medium">
                      {{ t('product.newVariation') }}
                    </span>
                  </div>
                </div>
              </td>
              <td class="py-3 px-4 text-center">
                <div class="flex justify-center">
                  <IluriaInputText
                    :id="`price-${item.id}`"
                    v-model="item.price" 
                    @input="updateVariation(item.id, 'price', item.price)"
                    class="w-40"
                    type="money"
                    :minFractionDigits="2"
                    :maxFractionDigits="2"
                    :step="0.01"
                    :min="0"
                    prefix="R$"
                  />
                </div>
              </td>
              <td class="py-3 px-4 text-center">
                <div class="flex justify-center">
                  <IluriaInputText 
                    :id="`stock-${item.id}`"
                    v-model="item.stock" 
                    @input="updateVariation(item.id, 'stock', item.stock)"
                    class="w-60"
                    type="number"
                    min="0"
                    suffix="unidade(s)"
                  />
                </div>
              </td>
              <td class="py-3 px-4 text-center">
                <div class="flex justify-center space-x-2">
                  <IluriaButton 
                    @click="openPhotoUpload(item)"
                    color="text-primary"
                    size="small"
                    :huge-icon="Camera01Icon"
                    :title="t('productVariations.editPhotos')"
                  />
                  <IluriaButton 
                    @click="editVariation(item)"
                    color="text-primary"
                    size="small"
                    :huge-icon="Edit02Icon"
                    :title="t('productVariations.editVariation')"
                  />
                </div>
              </td>
            </tr>
          </template>
        </tbody>
      </table>
    </div>

    <!-- No results message -->
    <div v-else class="mt-4 p-4 text-center text-[var(--iluria-color-text-secondary)] bg-[var(--iluria-color-surface)] rounded-lg border border-[var(--iluria-color-border)]">
      {{ t('productVariations.noVariationsFound') }}
    </div>

    <!-- Edit Variation Modal -->
    <EditVariationModal
      :is-open="isEditModalOpen"
      :variation="selectedVariation"
      :group-by="groupBy"
      @close="closeEditModal"
      @save="saveVariation"
      @copy-stock="copyStockToAllVariations"
      @copy-price="copyPriceToAllVariations"
      @copy-original-price="copyOriginalPriceToAllVariations"
      @copy-cost-price="copyCostPriceToAllVariations"
      @copy-weight="copyWeightToAllVariations"
      @copy-length="copyLengthToAllVariations"
      @copy-width="copyWidthToAllVariations"
      @copy-depth="copyDepthToAllVariations"
      @copy-all-to-all="copyAllToAllVariations"
    />

    <!-- Photo Upload Modal -->
    <PhotoUploadModal
      :is-open="isPhotoUploadOpen"
      :initial-files="selectedVariation?.photos"
      :product-id="productForm?.id || null"
      :variation-id="selectedVariation?.id"
      :is-editing="isEditing"
      @close="closePhotoUpload"
      @save="savePhotos"
    />
  </div>
</template>

<script setup>
import { defineProps, defineEmits, computed, ref, inject, watch, onUnmounted, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import {  
  Cancel01Icon, 
  ArrowDown01Icon, 
  ArrowRight01Icon, 
  RefreshIcon,
  Camera01Icon,
  Edit02Icon,
  Alert01Icon
} from '@hugeicons-pro/core-bulk-rounded';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import EditVariationModal from './EditVariationModal.vue';
import PhotoUploadModal from './PhotoUploadModal.vue';

const { t } = useI18n();

const productForm = inject('productForm');
const isEditing = inject('isEditing', ref(false));
const hasVariationStructureChanged = inject('hasVariationStructureChanged', ref(false));

const props = defineProps({
  variations: Array,
  combinedVariations: Array,
  groupBy: String
});

const emit = defineEmits(['update:variation', 'update:groupBy', 'clearAllData']);

const collapsedGroups = ref([]);
const isEditModalOpen = ref(false);
const isPhotoUploadOpen = ref(false);
const selectedVariation = ref(null);
const searchQuery = ref('');
const debouncedSearchQuery = ref('');
const selectedFilters = ref({});
const openFilter = ref(null);
const tableKey = ref(0);

const hasSelectedFilters = computed(() => Object.keys(selectedFilters.value).length > 0);

const variationOptions = computed(() =>
  props.variations.map(variation => ({
    label: variation.name,
    value: variation.name
  }))
);

// Debounce para o campo de busca
let searchTimeout = null;
watch(searchQuery, (newValue) => {
  clearTimeout(searchTimeout);
  searchTimeout = setTimeout(() => {
    debouncedSearchQuery.value = newValue;
  }, 300); // 300ms de debounce
});

const toggleFilterOptions = (variationName) => {
  openFilter.value = openFilter.value === variationName ? null : variationName;
};

const selectFilter = (variationName, option) => {
  selectedFilters.value[variationName] = option;
  openFilter.value = null;
};

const clearFilter = (variationName) => {
  delete selectedFilters.value[variationName];
  openFilter.value = null;
};

const clearAllFilters = () => {
  selectedFilters.value = {};
  openFilter.value = null;
  searchQuery.value = '';
  debouncedSearchQuery.value = '';
};

const clearAllData = () => {
  // Emitir evento para o componente pai limpar todos os dados das variações
  emit('clearAllData');

  // Também limpar os filtros
  clearAllFilters();
};

const getFilterLabel = (variation) => {
  const selectedOption = selectedFilters.value[variation.name];
  return selectedOption ? `${variation.name} é ${selectedOption}` : variation.name;
};

const toggleGroup = (groupName) => {
  const index = collapsedGroups.value.indexOf(groupName);
  if (index > -1) {
    collapsedGroups.value.splice(index, 1);
  } else {
    collapsedGroups.value.push(groupName);
  }
};

// Memoização para evitar recálculos desnecessários
const memoizedFilteredVariations = ref([]);
const lastFilterState = ref('');

const filteredGroupedVariations = computed(() => {
  if (!props.groupBy || props.combinedVariations.length === 0) {
    return [];
  }
  
  // Criar uma string representando o estado atual dos filtros
  const currentFilterState = JSON.stringify({
    groupBy: props.groupBy,
    searchQuery: debouncedSearchQuery.value,
    selectedFilters: selectedFilters.value,
    combinedVariationsLength: props.combinedVariations.length
  });
  
  // Se o estado não mudou, retornar o resultado memoizado
  if (currentFilterState === lastFilterState.value && memoizedFilteredVariations.value.length > 0) {
    return memoizedFilteredVariations.value;
  }
  
  const filteredVariations = props.combinedVariations.filter(variation => {
    if (debouncedSearchQuery.value) {
      const searchLower = debouncedSearchQuery.value.toLowerCase();
      const matchesSearch = Object.values(variation.options).some(value => 
        String(value).toLowerCase().includes(searchLower)
      );
      if (!matchesSearch) return false;
    }
    
    for (const [variationName, selectedOption] of Object.entries(selectedFilters.value)) {
      if (variation.options[variationName] !== selectedOption) {
        return false;
      }
    }
    
    return true;
  });
  
  const groupValues = [...new Set(filteredVariations.map(v => v.options[props.groupBy]))];
  
  const result = groupValues.map(groupValue => {
    const items = filteredVariations.filter(v => v.options[props.groupBy] === groupValue);
    
    const prices = items.map(item => Number(item.price))
      .filter(price => !isNaN(price) && price > 0);
    let priceRange = '';
    
    if (prices.length > 0) {
      const min = Math.min(...prices);
      const max = Math.max(...prices);
      priceRange = min === max ? `${min.toFixed(2)}` : `${min.toFixed(2)} - ${max.toFixed(2)}`;
    }
    
    const totalStock = items.reduce((sum, item) => {
      const stock = Number(item.stock);
      return sum + (isNaN(stock) ? 0 : stock);
    }, 0);
    
    return {
      name: groupValue,
      items,
      priceRange,
      totalStock: totalStock > 0 ? totalStock.toString() : ''
    };
  });
  
  // Salvar resultado memoizado
  memoizedFilteredVariations.value = result;
  lastFilterState.value = currentFilterState;
  
  return result;
});

// Limpar memoização quando props mudam significativamente
watch(() => props.combinedVariations, () => {
  memoizedFilteredVariations.value = [];
  lastFilterState.value = '';
}, { deep: false }); // Shallow watch para performance

const getPreviewUrl = (file) => {
  // If it's a File object, create an object URL
  if (file instanceof File) {
    return URL.createObjectURL(file);
  }
  
  // If it's a string (existing image URL), return it directly
  if (typeof file === 'string') {
    return file;
  }
  
  // If it's an object with a url property (our photo object format)
  if (file && typeof file === 'object' && file.url) {
    return file.url;
  }
  
  // Fallback - return empty string if none of the above
  return '';
};

const getVariationLabel = (variation) => {
  const parts = [];
  
  Object.entries(variation.options).forEach(([key, value]) => {
    if (key !== props.groupBy) {
      parts.push(value);
    }
  });
  
  return parts.join(' / ');
};

const updateVariation = (id, field, value) => {
  emit('update:variation', id, field, value);
};

const openPhotoUpload = (variation) => {
  selectedVariation.value = variation;
  isPhotoUploadOpen.value = true;
};

const closePhotoUpload = () => {
  isPhotoUploadOpen.value = false;
  selectedVariation.value = null;
};

const savePhotos = (files) => {
  if (selectedVariation.value) {
    // Garantir que files é um array válido
    const validFiles = Array.isArray(files) ? files : [];

    emit('update:variation', selectedVariation.value.id, 'photos', validFiles);
  }
  closePhotoUpload();
};

const editVariation = (variation) => {
  selectedVariation.value = variation;
  isEditModalOpen.value = true;
};

const closeEditModal = () => {
  isEditModalOpen.value = false;
  selectedVariation.value = null;
};

const saveVariation = async (data) => {
  if (selectedVariation.value) {
    // Atualizar faixas de preço
    if (data.hasPriceRanges) {
      updateVariation(selectedVariation.value.id, 'priceRanges', data.priceRanges);
      updateVariation(selectedVariation.value.id, 'hasPriceRanges', true);
    } else {
      updateVariation(selectedVariation.value.id, 'priceRanges', []);
      updateVariation(selectedVariation.value.id, 'hasPriceRanges', false);
    }

    // Atualizar todos os campos com valores numéricos corretos
    updateVariation(selectedVariation.value.id, 'price', Number(data.unitPrice) || 0);
    updateVariation(selectedVariation.value.id, 'stock', Number(data.stock) || 0);
    updateVariation(selectedVariation.value.id, 'originalPrice', Number(data.originalPrice) || 0);
    updateVariation(selectedVariation.value.id, 'costPrice', Number(data.costPrice) || 0);
    updateVariation(selectedVariation.value.id, 'weight', Number(data.weight) || 0);
    updateVariation(selectedVariation.value.id, 'length', Number(data.length) || 0);
    updateVariation(selectedVariation.value.id, 'width', Number(data.width) || 0);
    updateVariation(selectedVariation.value.id, 'depth', Number(data.depth) || 0);

    // Aguardar a próxima atualização do DOM antes de fechar o modal
    await nextTick();

    // Forçar re-renderização da tabela
    tableKey.value++;

    // Forçar atualização da variação selecionada para refletir na interface
    const updatedVariation = props.combinedVariations.find(v => v.id === selectedVariation.value.id);
    if (updatedVariation) {
      selectedVariation.value = { ...updatedVariation };
    }
  }
  closeEditModal();
};

const copyStockToAllVariations = (stockValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'stock', stockValue.toString());
  });
};

const copyPriceToAllVariations = (priceValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'price', priceValue.toString());
  });
};

const copyOriginalPriceToAllVariations = (originalPriceValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'originalPrice', originalPriceValue.toString());
  });
};

const copyCostPriceToAllVariations = (costPriceValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'costPrice', costPriceValue.toString());
  });
};

const copyWeightToAllVariations = (weightValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'weight', weightValue.toString());
  });
};

const copyLengthToAllVariations = (lengthValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'length', lengthValue.toString());
  });
};

const copyWidthToAllVariations = (widthValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'width', widthValue.toString());
  });
};

const copyDepthToAllVariations = (depthValue) => {
  props.combinedVariations.forEach(variation => {
    updateVariation(variation.id, 'depth', depthValue.toString());
  });
};

const copyAllToAllVariations = (data) => {
  if (data && props.combinedVariations) {
    props.combinedVariations.forEach(variation => {
      updateVariation(variation.id, 'price', data.price.toString());
      updateVariation(variation.id, 'stock', data.stock.toString());
      updateVariation(variation.id, 'originalPrice', data.originalPrice.toString());
      updateVariation(variation.id, 'costPrice', data.costPrice.toString());
      updateVariation(variation.id, 'weight', data.weight.toString());
      updateVariation(variation.id, 'length', data.length.toString());
      updateVariation(variation.id, 'width', data.width.toString());
      updateVariation(variation.id, 'depth', data.depth.toString());
      
      if (data.hasPriceRanges && data.priceRanges && data.priceRanges.length > 0) {
        updateVariation(variation.id, 'priceRanges', [...data.priceRanges]);
        updateVariation(variation.id, 'hasPriceRanges', true);
      } else {
        updateVariation(variation.id, 'priceRanges', []);
        updateVariation(variation.id, 'hasPriceRanges', false);
      }
    });
  }
};

const isNewVariation = (item) => {
  return item.id && 
    typeof item.id === 'string' && 
    !item.id.match(/^[0-9A-Z]{26}$/); // Não é um ULID do backend
};

// Cleanup timeouts
onUnmounted(() => {
  clearTimeout(searchTimeout);
});
</script>

<style scoped>
input[type="radio"] {
  margin: 5px;
  vertical-align: middle;
  appearance: none;
  -webkit-appearance: none;
  -moz-appearance: none;
  border: 2px solid var(--iluria-color-border);
  border-radius: 50%;
  background-color: var(--iluria-color-surface);
  position: relative;
  transition: all 0.2s ease;
}

input[type="radio"]:checked {
  border-color: var(--iluria-color-primary);
  background-color: var(--iluria-color-primary);
}

input[type="radio"]:checked::after {
  content: '';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  width: 6px;
  height: 6px;
  border-radius: 50%;
  background-color: var(--iluria-color-primary);
}

input[type="radio"]:hover {
  border-color: var(--iluria-color-primary);
}

input[type="radio"]:focus {
  outline: 2px solid var(--iluria-color-focus-ring);
  outline-offset: 2px;
}

.label-radio{
  margin-top: 1px !important;
}

:deep(.variations-table thead tr th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  transition: all 0.3s ease;
}

/* Table alignment styles */
.variations-table th.text-center,
.variations-table td.text-center {
  text-align: center !important;
}

.variations-table th.text-left,
.variations-table td.text-left {
  text-align: left !important;
}
</style>
