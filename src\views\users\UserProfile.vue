<template>
  <div class="user-profile-page">
    <IluriaHeader
      :title="$t('userProfile.title')"
      :subtitle="$t('userProfile.subtitle')"
    />

    <SectionContainer>
      <div class="profile-content">
        <!-- Avatar Section -->
        <div class="profile-section">
          <div class="section-header">
            <HugeiconsIcon :icon="UserIcon" size="20" :strokeWidth="1.5" />
            <h2 class="section-title">{{ $t('userProfile.avatar.title') }}</h2>
          </div>
          <div class="avatar-container">
            <div class="avatar-upload-section">
              <IluriaSimpleImageUpload
                v-model="avatarFile"
                accept="image/*"
                :add-button-text="$t('userProfile.avatar.upload')"
                :change-button-text="$t('userProfile.avatar.change')"
                :remove-button-text="$t('userProfile.avatar.remove')"
                :format-hint="$t('userProfile.avatar.formatHint')"
                :prevent-cache="true"
                @change="onAvatarChange"
                class="avatar-upload"
                :show-border="false"
              />
            </div>
            <div class="avatar-info">
              <h3 class="avatar-name">{{ user?.name || 'Usuário' }}</h3>
              <p class="avatar-role">{{ user?.role || 'Administrador' }}</p>
            </div>
          </div>
        </div>

        <!-- Personal Information -->
        <div class="profile-section">
          <div class="section-header">
            <HugeiconsIcon :icon="UserAccountIcon" size="20" :strokeWidth="1.5" />
            <h2 class="section-title">{{ $t('userProfile.personalInfo.title') }}</h2>
          </div>
          
          <VeeForm @submit="handleProfileUpdate" :validation-schema="profileSchema" class="profile-form">
            <div class="form-grid">
              <div class="form-group">
                <label for="name" class="form-label">{{ $t('userProfile.personalInfo.name') }}</label>
                <VeeField
                  id="name"
                  name="name"
                  type="text"
                  v-model="profileData.name"
                  class="form-input"
                  :placeholder="$t('userProfile.personalInfo.namePlaceholder')"
                />
                <ErrorMessage name="name" class="error-message" />
              </div>

              <div class="form-group">
                <label for="email" class="form-label">{{ $t('userProfile.personalInfo.email') }}</label>
                <VeeField
                  id="email"
                  name="email"
                  type="email"
                  v-model="profileData.email"
                  class="form-input"
                  :placeholder="$t('userProfile.personalInfo.emailPlaceholder')"
                />
                <ErrorMessage name="email" class="error-message" />
              </div>

              <div class="form-group">
                <label for="phone" class="form-label">{{ $t('userProfile.personalInfo.phone') }}</label>
                <VeeField
                  id="phone"
                  name="phone"
                  type="tel"
                  v-model="profileData.phone"
                  class="form-input"
                  :placeholder="$t('userProfile.personalInfo.phonePlaceholder')"
                />
                <ErrorMessage name="phone" class="error-message" />
              </div>

              <div class="form-group">
                <label for="position" class="form-label">{{ $t('userProfile.personalInfo.position') }}</label>
                <VeeField
                  id="position"
                  name="position"
                  type="text"
                  v-model="profileData.position"
                  class="form-input"
                  :placeholder="$t('userProfile.personalInfo.positionPlaceholder')"
                />
                <ErrorMessage name="position" class="error-message" />
              </div>
            </div>

            <div class="form-actions">
              <button type="button" class="btn-outline" @click="cancelChanges">
                {{ $t('userProfile.actions.cancel') }}
              </button>
              <button type="submit" class="btn-primary" :disabled="isLoading">
                <LoadingSpinner v-if="isLoading" size="16" />
                <HugeiconsIcon v-else :icon="CheckmarkCircle02Icon" size="16" :strokeWidth="1.5" />
                {{ $t('userProfile.actions.save') }}
              </button>
            </div>
          </VeeForm>
        </div>

        <!-- Preferences Section -->
        <div class="profile-section">
          <div class="section-header">
            <HugeiconsIcon :icon="Settings02Icon" size="20" :strokeWidth="1.5" />
            <h2 class="section-title">{{ $t('userProfile.preferences.title') }}</h2>
          </div>
          
          <div class="preferences-grid">
            <div class="preference-item">
              <div class="preference-info">
                <h3 class="preference-title">{{ $t('userProfile.preferences.language') }}</h3>
                <p class="preference-description">{{ $t('userProfile.preferences.languageDesc') }}</p>
              </div>
              <select v-model="preferences.language" class="preference-select">
                <option value="pt-br">Português (Brasil)</option>
                <option value="en">English</option>
              </select>
            </div>

            <div class="preference-item">
              <div class="preference-info">
                <h3 class="preference-title">{{ $t('userProfile.preferences.timezone') }}</h3>
                <p class="preference-description">{{ $t('userProfile.preferences.timezoneDesc') }}</p>
              </div>
              <select v-model="preferences.timezone" class="preference-select">
                <option value="America/Sao_Paulo">São Paulo (GMT-3)</option>
                <option value="America/New_York">New York (GMT-4)</option>
                <option value="Europe/London">London (GMT+0)</option>
              </select>
            </div>

            <div class="preference-item">
              <div class="preference-info">
                <h3 class="preference-title">{{ $t('userProfile.preferences.notifications') }}</h3>
                <p class="preference-description">{{ $t('userProfile.preferences.notificationsDesc') }}</p>
              </div>
              <label class="preference-toggle">
                <input 
                  type="checkbox" 
                  v-model="preferences.emailNotifications"
                  class="toggle-input"
                />
                <span class="toggle-slider"></span>
              </label>
            </div>
          </div>

          <div class="form-actions">
            <button type="button" class="btn-primary" @click="savePreferences" :disabled="isLoadingPreferences">
              <LoadingSpinner v-if="isLoadingPreferences" size="16" />
              <HugeiconsIcon v-else :icon="CheckmarkCircle02Icon" size="16" :strokeWidth="1.5" />
              {{ $t('userProfile.actions.savePreferences') }}
            </button>
          </div>
        </div>
      </div>
    </SectionContainer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import { Form as VeeForm, Field as VeeField, ErrorMessage } from 'vee-validate'
import * as yup from 'yup'
import SectionContainer from '@/components/layout/SectionContainer.vue'
import LoadingSpinner from '@/components/iluria/LoadingSpinner.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue'
import userProfileService from '@/services/userProfile.service'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserIcon,
  UserAccountIcon,
  Settings02Icon,
  CameraIcon,
  Delete02Icon,
  CheckmarkCircle02Icon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const { t } = useI18n()
const authStore = useAuthStore()
const toast = useToast()

// State
const isLoading = ref(false)
const isLoadingPreferences = ref(false)
const isUploadingAvatar = ref(false)
const avatarFile = ref(authStore.userProfilePictureUrl || '')
const user = computed(() => authStore.user)

const profileData = reactive({
  name: user.value?.name || '',
  email: user.value?.email || '',
  phone: user.value?.phone || '',
  position: user.value?.position || ''
})

const preferences = reactive({
  language: 'pt-br',
  timezone: 'America/Sao_Paulo',
  emailNotifications: true
})

// Validation Schema
const profileSchema = yup.object({
  name: yup.string().required(() => t('userProfile.validation.nameRequired')),
  email: yup.string().email(() => t('userProfile.validation.emailInvalid')).required(() => t('userProfile.validation.emailRequired')),
  phone: yup.string(),
  position: yup.string()
})

// Methods
const handleProfileUpdate = async (values) => {
  isLoading.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    // Update local user data
    Object.assign(profileData, values)
    
    toast.addToast(t('userProfile.messages.profileUpdated'), 'success')
  } catch (error) {
    toast.addToast(t('userProfile.messages.profileUpdateError'), 'error')
  } finally {
    isLoading.value = false
  }
}

const cancelChanges = () => {
  // Reset to original values
  profileData.name = user.value?.name || ''
  profileData.email = user.value?.email || ''
  profileData.phone = user.value?.phone || ''
  profileData.position = user.value?.position || ''
}

const onAvatarChange = async (file) => {
  if (!file) {
    // Remove avatar
    try {
      isUploadingAvatar.value = true
      await userProfileService.removeProfilePicture()

      // Update auth store
      authStore.updateUserProfile({ profilePictureUrl: null })

      toast.addToast(t('userProfile.messages.avatarRemoved'), 'success')
    } catch (error) {
      console.error('Error removing avatar:', error)
      toast.addToast(t('userProfile.messages.avatarRemoveError'), 'error')
    } finally {
      isUploadingAvatar.value = false
    }
    return
  }

  // Upload new avatar
  try {
    isUploadingAvatar.value = true

    // Validate file size (max 5MB)
    if (file.size > 5 * 1024 * 1024) {
      toast.addToast(t('userProfile.messages.avatarTooLarge'), 'error')
      return
    }

    // Create FormData for upload
    const formData = new FormData()
    formData.append('profilePicture', file)

    const response = await userProfileService.updateProfilePicture(formData)

    if (response && response.profilePictureUrl) {
      // Update auth store with new avatar URL
      authStore.updateUserProfile({ profilePictureUrl: response.profilePictureUrl })

      toast.addToast(t('userProfile.messages.avatarUpdated'), 'success')
    }
  } catch (error) {
    console.error('Error uploading avatar:', error)
    toast.addToast(t('userProfile.messages.avatarUploadError'), 'error')

    // Reset avatar file on error
    avatarFile.value = authStore.userProfilePictureUrl || ''
  } finally {
    isUploadingAvatar.value = false
  }
}

const savePreferences = async () => {
  isLoadingPreferences.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    toast.addToast(t('userProfile.messages.preferencesUpdated'), 'success')
  } catch (error) {
    toast.addToast(t('userProfile.messages.preferencesUpdateError'), 'error')
  } finally {
    isLoadingPreferences.value = false
  }
}

// Lifecycle
onMounted(() => {
  // Load user preferences if available
})
</script>

<style scoped>
.user-profile-page {
  min-height: 100vh;
  background: var(--iluria-color-body-bg);
}



.profile-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.profile-section {
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.section-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.avatar-container {
  display: flex;
  align-items: flex-start;
  gap: 2rem;
  flex-wrap: wrap;
}

.avatar-upload-section {
  flex-shrink: 0;
}

.avatar-upload {
  width: 200px;
}

.avatar-upload :deep(.upload-container) {
  min-height: 200px;
  border-radius: 12px;
}

.avatar-upload :deep(.image-container) {
  min-height: 180px;
}

.avatar-upload :deep(.image-container img) {
  border-radius: 8px;
  max-width: 180px;
  max-height: 180px;
}

.avatar-info {
  flex: 1;
  min-width: 200px;
  padding-top: 1rem;
}

.avatar-info h3 {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.5rem 0;
}

.avatar-info p {
  color: var(--iluria-color-text-secondary);
  margin: 0;
  font-size: 1rem;
}

.profile-form {
  width: 100%;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
}

.form-input {
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-input-bg);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.preferences-grid {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.preference-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-input-bg);
}

.preference-info {
  flex: 1;
}

.preference-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.25rem 0;
}

.preference-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.preference-select {
  padding: 0.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  min-width: 150px;
}

.preference-toggle {
  position: relative;
  display: inline-block;
  width: 48px;
  height: 24px;
}

.toggle-input {
  opacity: 0;
  width: 0;
  height: 0;
}

.toggle-slider {
  position: absolute;
  cursor: pointer;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: var(--iluria-color-border);
  border-radius: 24px;
  transition: 0.2s;
}

.toggle-slider:before {
  position: absolute;
  content: "";
  height: 18px;
  width: 18px;
  left: 3px;
  bottom: 3px;
  background-color: white;
  border-radius: 50%;
  transition: 0.2s;
}

.toggle-input:checked + .toggle-slider {
  background-color: var(--iluria-color-primary);
}

.toggle-input:checked + .toggle-slider:before {
  transform: translateX(24px);
}

.form-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  flex-wrap: wrap;
}

.btn-primary, .btn-secondary, .btn-outline {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 1px solid transparent;
}

.btn-primary {
  background: var(--iluria-color-primary);
  color: white;
}

.btn-primary:hover:not(:disabled) {
  background: var(--iluria-color-primary-dark);
  transform: translateY(-1px);
}

.btn-primary:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-secondary {
  background: var(--iluria-color-secondary);
  color: var(--iluria-color-text-primary);
}

.btn-secondary:hover {
  background: var(--iluria-color-secondary-dark);
  transform: translateY(-1px);
}

.btn-outline {
  background: transparent;
  color: var(--iluria-color-text-primary);
  border-color: var(--iluria-color-border);
}

.btn-outline:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-border-hover);
  transform: translateY(-1px);
}

.error-message {
  color: var(--iluria-color-danger);
  font-size: 0.75rem;
  margin-top: 0.25rem;
}

@media (max-width: 768px) {
  .avatar-container {
    flex-direction: column;
    align-items: center;
    text-align: center;
    gap: 1.5rem;
  }

  .avatar-upload {
    width: 180px;
  }

  .avatar-info {
    padding-top: 0;
    min-width: auto;
  }
  
  .preference-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .preference-select {
    min-width: 100%;
  }
  
  .form-actions {
    justify-content: stretch;
  }
  
  .btn-primary, .btn-secondary, .btn-outline {
    flex: 1;
    justify-content: center;
  }
}
</style>