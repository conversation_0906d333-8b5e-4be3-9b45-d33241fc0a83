<template>
  <div class="brand-assets-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="$t('storeBrandAssets.title')"
      :subtitle="$t('storeBrandAssets.subtitle')"
      :showSave="true"
      :saveText="$t('storeBrandAssets.saveButton')"
      @save-click="saveBrandAssets"
    />

    <!-- Main Content -->
    <div v-if="dataLoaded" class="form-container">
      
      <!-- Logo Section -->
      <ViewContainer 
        :title="$t('storeBrandAssets.logoSection.title')"
        :subtitle="$t('storeBrandAssets.logoSection.subtitle')"
        :icon="Image02Icon"
        iconColor="blue"
      >
        <div class="brand-grid">
          <div class="upload-section">
            <div class="form-field">
              <label class="field-label">{{ $t('storeBrandAssets.logoSection.title') }}</label>

              <div class="flex justify-center">
                <IluriaSimpleImageUpload
                  v-model="formData.logoUrl"
                  accept="image/*"
                  :add-button-text="$t('storeBrandAssets.logoSection.uploadButton')"
                  :change-button-text="$t('storeBrandAssets.logoSection.changeButton')"
                  :remove-button-text="$t('storeBrandAssets.logoSection.removeButton')"
                  :format-hint="$t('storeBrandAssets.logoSection.formats')"
                  :prevent-cache="true"
                  @change="onLogoChange"
                  class="image-upload"
                />
              </div>

              <!-- Logo Recommendations -->
              <div class="recommendations">
                <h4>{{ $t('storeBrandAssets.logoSection.recommendations') }}</h4>
                <ul>
                  <li>{{ $t('storeBrandAssets.logoSection.dimensionsText') }}</li>
                  <li>{{ $t('storeBrandAssets.logoSection.backgroundText') }}</li>
                  <li>{{ $t('storeBrandAssets.logoSection.retinaText') }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </ViewContainer>

      <!-- Favicon Section -->
      <ViewContainer 
        :title="$t('storeBrandAssets.faviconSection.title')"
        :subtitle="$t('storeBrandAssets.faviconSection.subtitle')"
        :icon="Globe02Icon"
        iconColor="green"
      >
        <div class="brand-grid">
          <div class="upload-section">
            <div class="form-field">
              <label class="field-label">{{ $t('storeBrandAssets.faviconSection.title') }}</label>

              <div class="upload-preview-grid">
                <IluriaSimpleImageUpload
                  v-model="formData.faviconUrl"
                  accept=".ico,.png,.jpg,.jpeg,image/x-icon,image/png,image/jpeg,image/jpg"
                  :add-button-text="$t('storeBrandAssets.faviconSection.uploadButton')"
                  :change-button-text="$t('storeBrandAssets.faviconSection.changeButton')"
                  :remove-button-text="$t('storeBrandAssets.faviconSection.removeButton')"
                  :format-hint="$t('storeBrandAssets.faviconSection.formats')"
                  :prevent-cache="true"
                  @change="onFaviconChange"
                  class="image-upload flex-1"
                />

                <!-- Live Favicon Preview -->
                <div class="favicon-live-preview">
                  <div class="browser-preview">
                    <!-- Address bar -->
                    <div class="browser-address-bar">
                      <div class="browser-controls">
                        <span class="browser-control browser-control-red"></span>
                        <span class="browser-control browser-control-yellow"></span>
                        <span class="browser-control browser-control-green"></span>
                      </div>
                      <div class="browser-url-container">
                        <div class="browser-url">https://minhaloja.com</div>
                      </div>
                    </div>
                    <!-- Tab bar -->
                    <div class="browser-tab-bar">
                      <div class="favicon-container">
                        <template v-if="faviconPreviewUrl || formData.faviconUrl">
                          <img :src="faviconPreviewUrl || formData.faviconUrl" alt="Favicon" class="favicon-image" />
                        </template>
                        <template v-else>
                          <div class="favicon-placeholder"></div>
                        </template>
                      </div>
                      <span class="tab-title">Minha&nbsp;Loja</span>
                      <svg class="tab-close-icon" fill="none" stroke="currentColor" viewBox="0 0 24 24"><path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" /></svg>
                    </div>
                  </div>
                </div>
              </div>

              <!-- Favicon Recommendations -->
              <div class="recommendations">
                <h4>{{ $t('storeBrandAssets.faviconSection.recommendations') }}</h4>
                <ul>
                  <li>{{ $t('storeBrandAssets.faviconSection.dimensionsText') }}</li>
                  <li>{{ $t('storeBrandAssets.faviconSection.formatText') }}</li>
                  <li>{{ $t('storeBrandAssets.faviconSection.designText') }}</li>
                </ul>
              </div>
            </div>
          </div>
        </div>
      </ViewContainer>

      <!-- Tips Section -->
      <ViewContainer 
        :title="$t('storeBrandAssets.tips.title')"
        :icon="Idea01Icon"
        iconColor="yellow"
      >
        <div class="tips-content">
          <ul class="tips-list">
            <li>{{ $t('storeBrandAssets.tips.transparentLogo') }}</li>
            <li>{{ $t('storeBrandAssets.tips.testFavicon') }}</li>
            <li>{{ $t('storeBrandAssets.tips.consistentDesign') }}</li>
          </ul>
        </div>
      </ViewContainer>

      <!-- Preview Sections removidos -->
    </div>

    <!-- Removed modal uploads -->
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, getCurrentInstance } from 'vue';
import { useI18n } from 'vue-i18n';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue';
import { useToast } from '@/services/toast.service';
import storeBrandAssetsService from '@/services/storeBrandAssets.service';
import { useStoreStore } from '@/stores/store.store';
import { 
  Image02Icon,
  Globe02Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { FloppyDiskIcon } from '@hugeicons-pro/core-bulk-rounded'
import { Idea01Icon } from '@hugeicons-pro/core-stroke-standard'
import { Eye, Monitor, Tablet, Smartphone } from 'lucide-vue-next';

const { t } = useI18n();
const toast = useToast();
const storeStore = useStoreStore();
const { proxy } = getCurrentInstance();
const _uid = proxy._.uid;

const dataLoaded = ref(false);
const componentMounted = ref(false);
const isUploading = ref(false);

const formData = reactive({
  logoUrl: '',
  faviconUrl: ''
});

const faviconPreviewUrl = ref('');

const selectedLogoFile = ref(null);
const selectedFaviconFile = ref(null);

// Handlers for simple upload changes
const onLogoChange = async (file) => {
  if (!file) {
    await removeLogo();
    return;
  }
  if (file instanceof File) {
    selectedLogoFile.value = file;
    formData.logoUrl = file;
  }
};

const onFaviconChange = async (file) => {
  if (!file) {
    await removeFavicon();
    return;
  }

  if (file instanceof File) {
    const allowedTypes = ['image/x-icon', 'image/vnd.microsoft.icon', 'image/png', 'image/jpeg', 'image/jpg'];
    if (!allowedTypes.includes(file.type)) {
      toast.showError(t('storeBrandAssets.messages.invalidFaviconFormat') || 'Formato inválido. Use ICO, PNG ou JPG para o favicon.');
      return;
    }

    selectedFaviconFile.value = file;
    formData.faviconUrl = file;

    // gerar preview local
    const reader = new FileReader();
    reader.onload = (e) => {
      faviconPreviewUrl.value = e.target.result;
    };
    reader.readAsDataURL(file);
  }
};

const removeLogo = async () => {
  try {
    await storeBrandAssetsService.deleteLogo();
    formData.logoUrl = '';
    await loadData();

    // Atualizar o store removendo a logo
    storeStore.updateBrandAssets({
      logoUrl: null
    });
  } catch (error) {
    console.error('Erro ao remover logo:', error);
    toast.showError(t('storeBrandAssets.messages.logoDeleteError'));
  }
};

const removeFavicon = async () => {
  try {
    await storeBrandAssetsService.deleteFavicon();
    formData.faviconUrl = '';
    faviconPreviewUrl.value = '';

    await loadData();
  } catch (error) {
    console.error('Erro ao remover favicon:', error);
    toast.showError(t('storeBrandAssets.messages.faviconDeleteError'));
  }
};

const handleUploadError = (error, type) => {
  if (!error.message) {
    toast.showError(t(`storeBrandAssets.messages.${type}UploadError`));
    return;
  }
  
  if (error.message.includes('muito grande') || error.message.includes('tamanho máximo')) {
    toast.showError(t(`storeBrandAssets.messages.${type}TooLarge`));
  } else if (error.message.includes('formato') || error.message.includes('inválido')) {
    toast.showError(t(`storeBrandAssets.messages.invalid${type.charAt(0).toUpperCase() + type.slice(1)}Format`));
  } else if (error.message.includes('servidor')) {
    toast.showError(t('storeBrandAssets.messages.serverError'));
  } else if (error.message.includes('conexão') || error.code === 'ERR_NETWORK' || 
            (error.message && error.message.includes('Network Error'))) {
    toast.showError(t('storeBrandAssets.messages.networkError'));
  } else {
    toast.showError(t(`storeBrandAssets.messages.${type}UploadError`));
  }
};

const loadData = async () => {
  try {
    const response = await storeBrandAssetsService.getStoreBrandAssets();
    if (response) {
      formData.logoUrl = response.logoUrl || '';
      formData.faviconUrl = response.faviconUrl || '';
      faviconPreviewUrl.value = response.faviconUrl || '';
      dataLoaded.value = true;
    }
  } catch (error) {
    console.error('Erro ao carregar configurações de marca:', error);
    if (componentMounted.value) {
      toast.showError(t('storeBrandAssets.messages.loadError'));
    }
  }
};

// Salvar configurações (padrão semelhante ao Store SEO Settings)
const saveBrandAssets = async () => {
  if (!componentMounted.value) return;
  isUploading.value = true;
  try {
    if (selectedLogoFile.value) {
      const res = await storeBrandAssetsService.uploadLogo(selectedLogoFile.value);
      if (res.logoUrl) {
        formData.logoUrl = res.logoUrl;
      }
      selectedLogoFile.value = null;
    }
    if (selectedFaviconFile.value) {
      const res2 = await storeBrandAssetsService.uploadFavicon(selectedFaviconFile.value);
      if (res2.faviconUrl) {
        formData.faviconUrl = res2.faviconUrl;
        faviconPreviewUrl.value = res2.faviconUrl;
      }
      selectedFaviconFile.value = null;
    }
    await loadData();

    // Atualizar o store com os novos brand assets
    storeStore.updateBrandAssets({
      logoUrl: formData.logoUrl,
      faviconUrl: formData.faviconUrl
    });

    toast.showSuccess(t('storeBrandAssets.messages.saveSuccess'));
  } catch (error) {
    console.error('Erro ao salvar brand assets:', error);
    toast.showError(t('storeBrandAssets.messages.saveError'));
  } finally {
    isUploading.value = false;
  }
};

onMounted(async () => {
  componentMounted.value = true;
  await loadData();
});
</script>

<style scoped>
.brand-assets-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  line-height: 1.4;
  word-wrap: break-word;
  hyphens: auto;
}

/* Header Actions */
.header-actions {
  display: flex;
  gap: 12px;
  align-items: flex-start;
  flex-shrink: 0;
}

/* Form Container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Brand Grid */
.brand-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 24px;
}

.upload-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

/* Form Fields */
.form-field {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.field-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.field-hint {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Recommendations */
.recommendations {
  padding: 16px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.recommendations h4 {
  margin: 0 0 8px 0;
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.recommendations ul {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.recommendations li {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 4px;
  line-height: 1.4;
}

.recommendations li:last-child {
  margin-bottom: 0;
}

/* Image Upload Areas */
.image-upload-area {
  /* existing styles retained for legacy usage elsewhere */
}

/* New side-by-side grid */
.image-upload-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  align-items: center;
}

/* Ensure dashed border is on individual upload placeholder now */
.upload-placeholder {
  border: 2px dashed var(--iluria-color-border);
  padding: 32px;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--iluria-color-surface-hover);
  width: 100%;
  min-height: 128px; /* Mesma altura mínima do preview */
  display: flex;
  align-items: center;
  justify-content: center;
}

.upload-placeholder:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 12px;
}

.upload-icon {
  font-size: 48px;
  color: var(--iluria-color-text-tertiary);
  transition: color 0.2s ease;
}

.upload-placeholder:hover .upload-icon {
  color: var(--iluria-color-primary);
}

.upload-text {
  margin: 0;
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
}

/* Preview Wrapper and Placeholder */
.preview-wrapper {
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-surface);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 160px;
  padding: 16px;
}

.preview-placeholder {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  text-align: center;
}

/* Restored definitions that are still used */
.image-preview-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--iluria-color-surface);
  border: none;
  border-radius: 8px;
  width: 100%;
  min-height: 128px;
}

.image-preview {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
  min-height: 96px;
  margin-left: 20px;
}

.preview-image {
  width: 80px;
  height: 80px;
  object-fit: contain;
  border-radius: 6px;
  background: var(--iluria-color-surface-hover);
  border: 1px solid var(--iluria-color-border);
  flex-shrink: 0;
}

.favicon-preview .preview-image {
  width: 48px;
  height: 48px;
}

.image-actions {
  display: flex;
  gap: 8px;
  flex-shrink: 0;
  align-items: center;
}

/* Tips Section */
.tips-content {
  padding: 16px;
  background: var(--iluria-color-info-bg);
  border: 1px solid var(--iluria-color-info-border);
  border-radius: 8px;
}

.tips-list {
  margin: 0;
  padding-left: 16px;
  list-style-type: disc;
}

.tips-list li {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  margin-bottom: 8px;
  line-height: 1.4;
}

.tips-list li:last-child {
  margin-bottom: 0;
}

/* Preview Sections */
.preview-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
  margin-top: 24px;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.image-upload {
  width: 100%;
}

/* Responsive */
@media (max-width: 768px) {
  .brand-assets-container {
    padding: 16px;
  }
  
  .page-header {
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 15px;
    margin: 6px 0 0 0;
  }
  
  .form-container {
    gap: 20px;
  }
  
  .brand-grid {
    gap: 20px;
  }
  
  .upload-section {
    gap: 16px;
  }
}

@media (max-width: 640px) {
  .page-header {
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
}

@media (max-width: 480px) {
  .brand-assets-container {
    padding: 12px;
  }
  
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
    margin: 8px 0 0 0;
  }
  
  .form-container {
    gap: 16px;
  }
  
  .brand-grid {
    gap: 16px;
  }
  
  .upload-section {
    gap: 14px;
  }

  .header-actions {
    flex-direction: column;
    gap: 8px;
  }
  .header-actions .btn {
    width: 100%;
  }
}

.upload-preview-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

@media (min-width: 768px) {
  .upload-preview-grid {
    flex-direction: row;
  }
}

.favicon-live-preview img {
  width: auto;
}

.preview-sections, .preview-content, .favicon-live-preview img {
  /* removed preview specific styles */
}

/* Favicon Live Preview */
.favicon-live-preview {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.5rem;
  background: var(--iluria-color-surface-hover);
  min-height: 160px;
  padding: 1rem;
}

.browser-preview {
  width: 100%;
  max-width: 20rem;
  font-size: 0.75rem;
}

.browser-address-bar {
  display: flex;
  align-items: center;
  padding: 0.5rem 0.75rem;
  background: var(--iluria-color-surface-hover);
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  border: 1px solid var(--iluria-color-border);
  border-bottom: none;
}

.browser-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.browser-control {
  width: 0.625rem;
  height: 0.625rem;
  border-radius: 50%;
}

.browser-control-red {
  background-color: #ef4444;
}

.browser-control-yellow {
  background-color: #f59e0b;
}

.browser-control-green {
  background-color: #10b981;
}

.browser-url-container {
  flex: 1;
  margin: 0 0.75rem;
}

.browser-url {
  background: var(--iluria-color-surface);
  border-radius: 0.25rem;
  border: 1px solid var(--iluria-color-border);
  padding: 0.25rem 0.75rem;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.browser-tab-bar {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-top-left-radius: 0.5rem;
  border-top-right-radius: 0.5rem;
  padding: 0.5rem 0.75rem;
}

.favicon-container {
  width: 3rem;
  height: 3rem;
  background: var(--iluria-color-surface-hover);
  border-radius: 0.125rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  flex-shrink: 0;
}

.favicon-image {
  width: 3rem;
  height: 3rem;
  object-fit: contain;
}

.favicon-placeholder {
  width: 1.5rem;
  height: 1.5rem;
  background: var(--iluria-color-text-tertiary);
  border-radius: 0.125rem;
}

.tab-title {
  font-size: 0.75rem;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  flex: 1;
}

.tab-close-icon {
  width: 0.875rem;
  height: 0.875rem;
  color: var(--iluria-color-text-tertiary);
  margin-left: auto;
  flex-shrink: 0;
}
</style> 