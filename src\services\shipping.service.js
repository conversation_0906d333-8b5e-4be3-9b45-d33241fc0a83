import axios from 'axios';
import { API_URLS } from '../config/api.config';

class ShippingService {
  #endpoint;

  constructor() {
    this.#endpoint = `${API_URLS.STORE_BASE_URL}/shipping-config`;
  }
  
  getEndpoint() {
    return this.#endpoint;
  }
  
  async getShippingConfig() {
    try {
      const response = await axios.get(`${this.#endpoint}`);
      
      return response.data;
    } catch (error) {
      console.error('GetShippingConfig error:', error);
      
      if (error.response && error.response.status === 404) {
        return {
          increaseShippingValue: 0,
          increaseShippingPercentage: 0,
          localShippingEnabled: false,
          localShippingDescription: '',
          localShippingDiscountType: 'NO_DISCOUNT',
          localShippingMinimumValueDiscount: 0,
          localShippingPercentageDiscount: 0,
          pickupEnabled: false,
          pickupDescription: '',
          zipCodeRestrictionEnabled: false,
          zipCodeRestrictionRanges: [],
          correiosOfferSedex: false,
          correiosOfferPac: false,
          correiosIncludeInsurance: false,
          correiosShowDeliveryTime: false,
          correiosAddFixedValue: false,
          correiosFixedValueAmount: 0,
          correiosAddPercentageValue: false,
          correiosPercentageAmount: 0,
          correiosHasContract: false,
          correiosUser: '',
          correiosApiKey: ''
        };
      }
      
      throw error;
    }
  }

  async updateShippingConfig(data) {
    try {
      const config = {
        headers: {
          'Content-Type': 'application/json'
        }
      };
      const response = await axios.put(`${this.#endpoint}`, data, config);
      
      return response.data;
    } catch (error) {
      console.error('UpdateShippingConfig error:', error);
      throw error;
    }
  }
  
  async uploadLocalShippingRanges(file) {
    try {
      const formData = new FormData();
      
      if (!(file instanceof File) && !(file instanceof Blob)) {
        if (file?.file && (file.file instanceof File || file.file instanceof Blob)) {
          file = file.file;
        } else if (file?.raw) {
          file = file.raw;
        } else if (file?.[0]) {
          file = file[0];
        } else {
          throw new Error('Tipo de arquivo inválido. Por favor, selecione um arquivo CSV.');
        }
      }
  
      formData.append('file', file, file.name || 'ranges.csv');
      
      const config = {
        headers: {
          'Accept': 'application/json',
          'Authorization': `Bearer ${localStorage.getItem('token')}`
        }
      };
      
      const response = await axios.post(
        `${this.#endpoint}/local-ranges/upload`, 
        formData, 
        config
      );
      
      return response.data;
    } catch (error) {
      console.error('UploadLocalShippingRanges error:', error);
      if (error.response) {
        console.error('Response data:', error.response.data);
        console.error('Response status:', error.response.status);
        console.error('Response headers:', error.response.headers);
      }
      throw error;
    }
  }
  
  async getLocalShippingRanges() {
    try {
      const response = await axios.get(`${this.#endpoint}/local-ranges`);
      return response.data;
    } catch (error) {
      console.error('GetLocalShippingRanges error:', error);
      throw error;
    }
  }
}

export default new ShippingService();
