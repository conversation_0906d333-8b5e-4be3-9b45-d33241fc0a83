import { ref, computed } from 'vue'
import { 
  PaintBoardIcon,
  LayoutGridIcon,
  DashboardSpeed01Icon,
  RoadWaysideIcon,
  SecurityCheckIcon
} from '@hugeicons-pro/core-stroke-standard'

// Estado global do mock de IA
const isAIEnabled = ref(false) // Por enquanto desabilitado
const isAnalyzing = ref(false)
const aiSuggestions = ref([])

// Sugestões mockadas realistas
const MOCK_AI_SUGGESTIONS = [
  {
    id: 'improve-colors',
    title: 'Melhorar Paleta de Cores',
    description: 'Sua paleta atual pode ter melhor contraste para aumentar conversões em 12%',
    type: 'colors',
    icon: PaintBoardIcon,
    confidence: 0.87,
    impact: 'high',
    estimatedImprovement: '12% de conversão',
    actionable: true,
    demoAvailable: true
  },
  {
    id: 'optimize-layout',
    title: 'Otimizar Layout Mobile',
    description: 'Reorganizar elementos pode melhorar a experiência mobile e reduzir bounce rate',
    type: 'layout',
    icon: LayoutGridIcon,
    confidence: 0.92,
    impact: 'medium',
    estimatedImprovement: '8% menos bounce rate',
    actionable: true,
    demoAvailable: true
  },
  {
    id: 'improve-performance',
    title: 'Melhorar Performance',
    description: 'Otimizações automáticas podem reduzir tempo de carregamento em 1.2s',
    type: 'performance',
    icon: DashboardSpeed01Icon,
    confidence: 0.95,
    impact: 'high',
    estimatedImprovement: '1.2s mais rápido',
    actionable: false,
    demoAvailable: false
  },
  {
    id: 'generate-variants',
    title: 'Gerar Variações A/B',
    description: 'Criamos 3 variações do seu tema para testes A/B automáticos',
    type: 'testing',
    icon: RoadWaysideIcon,
    confidence: 0.89,
    impact: 'medium',
    estimatedImprovement: 'Insights de conversão',
    actionable: false,
    demoAvailable: true
  },
  {
    id: 'check-accessibility',
    title: 'Verificar Acessibilidade',
    description: 'Detectamos 3 problemas de acessibilidade que podem ser corrigidos automaticamente',
    type: 'accessibility',
    icon: SecurityCheckIcon,
    confidence: 0.98,
    impact: 'high',
    estimatedImprovement: 'WCAG 2.1 compliant',
    actionable: true,
    demoAvailable: false
  }
]

// Métricas mockadas realistas
const MOCK_PERFORMANCE_METRICS = {
  coreWebVitals: {
    lcp: { value: 2.1, status: 'good', target: '<2.5s' },
    fid: { value: 85, status: 'good', target: '<100ms' },
    cls: { value: 0.08, status: 'good', target: '<0.1' }
  },
  lighthouse: {
    performance: 87,
    accessibility: 94,
    bestPractices: 92,
    seo: 89
  },
  conversionMetrics: {
    bounceRate: '34%',
    avgSessionDuration: '4m 32s',
    conversionRate: '2.8%',
    pageSpeed: '2.1s'
  }
}

// Removido MOCK_THEME_VARIATIONS - sistema não usa mais dados mockados

export function useMockAI() {

  /**
   * Simula análise de IA no tema
   */
  const analyzeTheme = async (theme) => {
    isAnalyzing.value = true
    
    try {
      // Simular delay de análise
      await new Promise(resolve => setTimeout(resolve, 2000))
      
      // Gerar sugestões baseadas no tema
      const suggestions = generateSuggestions(theme)
      aiSuggestions.value = suggestions
      
      return {
        suggestions,
        metrics: generateMockMetrics(theme),
        variations: generateMockVariations(theme)
      }
      
    } catch (error) {
      console.error('Erro na análise de IA:', error)
      throw error
    } finally {
      isAnalyzing.value = false
    }
  }

  /**
   * Gera sugestões baseadas no tema
   */
  const generateSuggestions = (theme) => {
    const suggestions = [...MOCK_AI_SUGGESTIONS]
    
    // Personalizar sugestões baseado no tema
    if (theme.type === 'dark') {
      suggestions[0].description = 'Tema escuro pode ter melhor contraste em elementos secundários'
    } else if (theme.type === 'minimal') {
      suggestions[1].description = 'Design minimalista pode beneficiar de mais elementos visuais'
    }
    
    return suggestions
  }

  /**
   * Gera métricas mockadas
   */
  const generateMockMetrics = (theme) => {
    const baseMetrics = { ...MOCK_PERFORMANCE_METRICS }
    
    // Ajustar métricas baseado no tema
    if (theme.type === 'dark') {
      baseMetrics.lighthouse.performance = 91 // Temas escuros "performam" melhor
    } else if (theme.layout === 'full-width') {
      baseMetrics.coreWebVitals.lcp.value = 2.4 // Full width pode ser mais lento
    }
    
    return baseMetrics
  }

  /**
   * Gera variações - agora retorna vazio (sem dados mockados)
   */
  const generateMockVariations = (theme) => {
    return []
  }

  /**
   * Aplica sugestão de IA (mock)
   */
  const applySuggestion = async (suggestion) => {
    try {
      // Simular aplicação da sugestão
      await new Promise(resolve => setTimeout(resolve, 1500))
      
      return {
        success: true,
        message: `Sugestão "${suggestion.title}" aplicada com sucesso`,
        changes: mockApplySuggestion(suggestion)
      }
      
    } catch (error) {
      console.error('Erro ao aplicar sugestão:', error)
      throw error
    }
  }

  /**
   * Mock da aplicação de sugestão
   */
  const mockApplySuggestion = (suggestion) => {
    const mockChanges = {
      'improve-colors': {
        primaryColor: '#1a365d',
        contrastRatio: '4.8:1',
        accessibility: 'AA compliant'
      },
      'optimize-layout': {
        mobileLayout: 'stack',
        buttonSize: '+20%',
        spacing: 'optimized'
      },
      'improve-performance': {
        imageOptimization: 'WebP format',
        codeMinification: 'enabled',
        caching: 'aggressive'
      }
    }
    
    return mockChanges[suggestion.id] || {}
  }

  /**
   * Obtém sugestões por categoria
   */
  const getSuggestionsByCategory = (category) => {
    return aiSuggestions.value.filter(s => s.type === category)
  }

  /**
   * Obtém sugestões de alta prioridade
   */
  const getHighPrioritySuggestions = computed(() => {
    return aiSuggestions.value.filter(s => 
      s.impact === 'high' && s.confidence > 0.8
    )
  })

  /**
   * Obtém sugestões acionáveis
   */
  const getActionableSuggestions = computed(() => {
    return aiSuggestions.value.filter(s => s.actionable)
  })

  /**
   * Simula geração de relatório de IA
   */
  const generateAIReport = async (theme) => {
    try {
      const analysis = await analyzeTheme(theme)
      
      return {
        theme: theme.name,
        analysisDate: new Date().toISOString(),
        overallScore: calculateOverallScore(analysis.metrics),
        suggestions: analysis.suggestions,
        metrics: analysis.metrics,
        variations: analysis.variations,
        recommendations: generateRecommendations(analysis)
      }
      
    } catch (error) {
      console.error('Erro ao gerar relatório:', error)
      throw error
    }
  }

  /**
   * Calcula score geral
   */
  const calculateOverallScore = (metrics) => {
    const lighthouse = metrics.lighthouse
    return Math.round(
      (lighthouse.performance + lighthouse.accessibility + 
       lighthouse.bestPractices + lighthouse.seo) / 4
    )
  }

  /**
   * Gera recomendações
   */
  const generateRecommendations = (analysis) => {
    const recommendations = []
    
    if (analysis.metrics.lighthouse.performance < 90) {
      recommendations.push('Considere otimizar imagens e scripts para melhor performance')
    }
    
    if (analysis.metrics.lighthouse.accessibility < 95) {
      recommendations.push('Melhorar contraste e adicionar alt text nas imagens')
    }
    
    return recommendations
  }

  /**
   * Status de disponibilidade da IA
   */
  const isAIAvailable = computed(() => {
    // Por enquanto sempre false - IA em desenvolvimento
    return false
  })

  /**
   * Sugestões mockadas para exibição
   */
  const mockAISuggestions = computed(() => {
    return MOCK_AI_SUGGESTIONS
  })

  /**
   * Controla exibição das sugestões de IA
   */
  const showAISuggestions = computed(() => {
    // Mostrar interface mockada mesmo com IA desabilitada
    return true
  })

  return {
    // Estado
    isAIEnabled,
    isAnalyzing,
    aiSuggestions,
    
    // Computed
    getHighPrioritySuggestions,
    getActionableSuggestions,
    isAIAvailable,
    mockAISuggestions,
    showAISuggestions,
    
    // Métodos
    analyzeTheme,
    applySuggestion,
    getSuggestionsByCategory,
    generateAIReport,
    generateSuggestions,
    generateMockMetrics,
    generateMockVariations
  }
}