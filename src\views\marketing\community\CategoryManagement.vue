<template>

  <ViewContainer
    :title="$t('community.categoryManagement.title')"
    :icon="Folder01Icon"
  >
    <template #rightHeader>
      <IluriaButton
        color="dark"
        size="medium"
        :hugeIcon="PlusSignSquareIcon"
        @click="openCreateModal"
      >
        {{ $t('community.categoryManagement.createButton') }}
      </IluriaButton>
    </template>

    <!-- Helper text -->
    <p class="text-sm text-[var(--iluria-color-text-tertiary)] mb-4">
      {{ $t('community.categoryManagement.dragHint') }}
    </p>

    <!-- Loading State -->
    <div v-if="loading" class="flex items-center justify-center py-12">
      <span class="text-[var(--iluria-color-text-muted)]">{{ $t('common.loadingCategories') }}</span>
    </div>

    <!-- Error State -->
    <div v-else-if="error" class="flex items-center justify-between p-4 bg-red-100 text-red-700 rounded-lg mb-4">
      <span>{{ error }}</span>
      <IluriaButton variant="ghost" color="primary" @click="loadCategories">
        {{ $t('common.retry') }}
      </IluriaButton>
    </div>

    <!-- Empty State -->
    <div v-else-if="categories.length === 0" class="text-center py-12 text-[var(--iluria-color-text-muted)]">
      {{ $t('community.categoryManagement.noCategories') }}
    </div>

    <!-- Categories List -->
    <draggable
      v-else
      v-model="categories"
      handle=".drag-handle"
      item-key="id"
      :animation="150"
      class="space-y-3"
      @end="handleReorder"
    >
      <template #item="{ element }">
        <div
          class="flex items-center justify-between bg-[var(--iluria-color-surface)] border border-[var(--iluria-color-border)] rounded-lg px-4 py-3"
        >
          <div class="flex items-center gap-3">
            <!-- Drag handle icon -->
            <div class="drag-handle cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-text-secondary)]">
              <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                <path
                  d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM3 12a1 1 0 000 2h.01a1 1 0 100-2H3zM8 4a1 1 0 000 2h.01a1 1 0 100-2H8zM8 8a1 1 0 000 2h.01a1 1 0 100-2H8zM8 12a1 1 0 000 2h.01a1 1 0 100-2H8z"
                />
              </svg>
        </div>
            <!-- Category name -->
            <span class="font-medium text-[var(--iluria-color-text-primary)]">{{ element.name }}</span>
        </div>

          <div class="flex items-center gap-4">
            <!-- Groups count -->
            <span
              v-if="getGroupCount(element) > 0"
              class="text-sm text-[var(--iluria-color-text-secondary)]"
            >
              {{ $t('community.categoryManagement.groupsLabel', { count: getGroupCount(element) }) }}
            </span>
            <!-- Edit button -->
            <IluriaButton
              variant="ghost"
              color="primary"
              size="small"
              :hugeIcon="PencilEdit01Icon"
              @click="handleEdit(element)"
              aria-label="Editar"
            />
            <!-- Delete button -->
          <IluriaButton
              variant="ghost"
              color="danger"
            size="small"
            :hugeIcon="Delete01Icon"
              @click="showDeleteConfirmation(element)"
              aria-label="Excluir"
          />
        </div>
      </div>
      </template>
    </draggable>

    <!-- Delete Confirmation Modal -->
    <IluriaConfirmationModal
      :isVisible="showDeleteModal"
      :title="$t('common.confirmDeleteTitle')"
      :message="confirmDeleteMessage"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />

    <!-- Create / Edit Category Modal -->
    <IluriaModal
      v-model="showCategoryModal"
      :title="categoryToEdit ? $t('community.categoryManagement.editModalTitle') : $t('community.categoryManagement.createModalTitle')"
      :showFooter="false"
      :dialogStyle="{ width: '450px' }"
    >
      <form @submit.prevent="handleSubmit" class="space-y-6 mt-4">
        <div>
          <label class="block text-sm font-medium mb-2">{{ $t('community.categoryManagement.nameLabel') }}</label>
          <input
            type="text"
            v-model="formData.name"
            :placeholder="$t('community.categoryManagement.namePlaceholder')"
            maxlength="30"
            class="w-full px-4 py-2 border border-[color:var(--iluria-color-border)] rounded-lg focus:outline-none focus:ring-2 focus:ring-[color:var(--iluria-color-border-focus)]"
            :class="{ 'border-red-500': errors.name }"
          />
          <div class="flex justify-between mt-2">
            <p v-if="errors.name" class="text-red-500 text-sm">{{ errors.name }}</p>
          </div>
        </div>
        <div class="flex justify-end gap-3">
          <IluriaButton type="button" variant="outline" color="secondary" @click="showCategoryModal = false">
            {{ $t('common.cancel') }}
          </IluriaButton>
          <IluriaButton type="submit" color="primary" :loading="isSubmitting">
            {{ categoryToEdit ? $t('common.saveChanges') : $t('common.create') }}
          </IluriaButton>
        </div>
      </form>
    </IluriaModal>
  </ViewContainer>
</template>

<script setup>
import { ref, reactive, onMounted, computed, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import { z } from 'zod';
import draggable from 'vuedraggable';
import { useToastStore } from '@/stores/toast.store';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaModal from '@/components/iluria/IluriaModal.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import { PlusSignSquareIcon, PencilEdit01Icon, Delete01Icon, Folder01Icon } from '@hugeicons-pro/core-bulk-rounded';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import communityService from '@/services/community.service';

// i18n & stores
const { t } = useI18n();
const toast = useToastStore();

// Emit definition for parent compatibility
const emit = defineEmits(['close', 'success']);

// Reactive state
const categories = ref([]);
const loading = ref(false);
const error = ref(null);

const isSubmitting = ref(false);
const showCategoryModal = ref(false);
const categoryToEdit = ref(null);
const showDeleteModal = ref(false);
const categoryToDelete = ref(null);

const formData = reactive({
  name: ''
});

const errors = reactive({
  name: ''
});

// Helper to safely get group count
const getGroupCount = (cat) => {
  return cat.groupCount ?? cat.groupsCount ?? (cat.groups?.length ?? 0);
};

/* ------------- VALIDATION ------------- */
const categorySchema = z.object({
  name: z
    .string()
    .trim()
    .min(1, () => t('community.categoryManagement.validation.nameRequired'))
    .max(30)
});

const validateForm = () => {
  const parsed = categorySchema.safeParse(formData);
  errors.name = '';
  if (!parsed.success) {
    const fieldErr = parsed.error.formErrors.fieldErrors;
    if (fieldErr.name) errors.name = fieldErr.name[0];
    return false;
  }
  return true;
};

/* ------------- API ------------- */
const loadCategories = async () => {
   loading.value = true;
   error.value = null;
   try {
    const catResp = await communityService.listCategories();
    const cats = Array.isArray(catResp) ? catResp : [];

    // Fetch groups list to compute counts per category
    let groupResp = [];
    try {
      groupResp = await communityService.listGroups();
    } catch (e) {
      console.warn('Could not load groups for count', e);
    }

    const counts = {};
    if (Array.isArray(groupResp)) {
      groupResp.forEach(g => {
        if (g.categoryId) {
          counts[g.categoryId] = (counts[g.categoryId] || 0) + 1;
        }
      });
    } else if (groupResp?.content) {
      groupResp.content.forEach(g => {
        if (g.categoryId) {
          counts[g.categoryId] = (counts[g.categoryId] || 0) + 1;
        }
      });
    }

    cats.forEach(cat => {
      cat.groupCount = counts[cat.id] || 0;
    });

    categories.value = cats;
  } catch (err) {
    console.error('Error loading categories:', err);
    error.value = t('community.categoryManagement.error.loadCategories');
    toast.addToast({ type: 'error', message: error.value });
  } finally {
    loading.value = false;
  }
};

const saveCategory = async () => {
  isSubmitting.value = true;
  try {
    if (categoryToEdit.value) {
      await communityService.updateCategory(categoryToEdit.value.id, formData);
      toast.addToast({ type: 'success', message: t('community.categoryManagement.success.updated') });
    } else {
      await communityService.createCategory(formData);
      toast.addToast({ type: 'success', message: t('community.categoryManagement.success.created') });
    }
    await loadCategories();
    // Note: emit('success') foi movido para handleSubmit para melhor controle da ordem
  } catch (err) {
    console.error('Error saving category:', err);
    toast.addToast({
      type: 'error',
      message: t('community.categoryManagement.error.saveCategory') || 'Erro ao salvar categoria. Por favor, tente novamente.'
    });
    throw err; // Re-throw para que handleSubmit possa tratar
  } finally {
    isSubmitting.value = false;
  }
};


const deleteCategory = async (id) => {
  try {
    loading.value = true;
    await communityService.deleteCategory(id);
    toast.addToast({ type: 'success', message: t('community.categoryManagement.success.categoryDeleted') });
    await loadCategories();
    emit('success'); // Notify parent component to reload categories
  } catch (err) {
    console.error('Error deleting category:', err);
    toast.addToast({ type: 'error', message: t('community.categoryManagement.error.deleteCategory') });
  } finally {
    loading.value = false;
  }
};

/* ------------- EVENT HANDLERS ------------- */
const openCreateModal = () => {
  categoryToEdit.value = null;
  formData.name = '';
  errors.name = '';
  showCategoryModal.value = true;
};

const handleEdit = (category) => {
  categoryToEdit.value = category;
  formData.name = category.name;
  errors.name = '';
  showCategoryModal.value = true;
};

const handleSubmit = async () => {
  if (!validateForm()) return;
  
  try {
    await saveCategory();
    showCategoryModal.value = false; // Fecha modal primeiro
    // Aguarda um tick para garantir que o DOM seja atualizado
    await nextTick();
    // Aguarda mais um pouco para garantir que a animação do modal termine
    setTimeout(() => {
      emit('success'); // Emite success após modal estar completamente fechado
    }, 100);
  } catch (err) {
    // Modal permanece aberto se houve erro
    console.error('Error in handleSubmit:', err);
  }
};

const showDeleteConfirmation = (category) => {
  if (!category?.id) return;
  categoryToDelete.value = category;
  showDeleteModal.value = true;
};

const confirmDelete = () => {
  if (!categoryToDelete.value?.id) return;
  deleteCategory(categoryToDelete.value.id);
  showDeleteModal.value = false;
  categoryToDelete.value = null;
};

const cancelDelete = () => {
  showDeleteModal.value = false;
  categoryToDelete.value = null;
};

const handleReorder = async () => {
  try {
    const categoryIds = categories.value.map(cat => cat.id);
    await communityService.reorderCategories(categoryIds);
    emit('success');
  } catch (err) {
    console.error('Error reordering categories:', err);
  }
};

const confirmDeleteMessage = computed(() =>
  categoryToDelete.value ? t('community.categoryManagement.confirmDeleteMessage', { name: categoryToDelete.value.name }) : ''
);

/* ------------- LIFECYCLE ------------- */
onMounted(() => {
  loadCategories();
});

</script>

<style scoped>
</style>