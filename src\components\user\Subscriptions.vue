<template>
  <ViewContainer
    :title="t('userSettings.sections.billing.subscriptions')"
    :icon="MoneyExchange02Icon"
    iconColor="purple"
  >
    <!-- Conteúdo será implementado aqui -->
  </ViewContainer>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { MoneyExchange02Icon } from '@hugeicons-pro/core-stroke-standard'

// Composables
const { t } = useI18n()
</script>

<style scoped>
/* Estilos serão implementados aqui */
</style>
