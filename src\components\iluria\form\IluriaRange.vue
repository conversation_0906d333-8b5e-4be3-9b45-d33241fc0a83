<template>
  <div class="iluria-range-wrapper">
    <IluriaLabel v-if="label" :for="rangeId" class="mb-2">
      {{ label }}
    </IluriaLabel>
    
    <div class="range-container">
      <input
        :id="rangeId"
        type="range"
        :name="name || rangeId"
        v-model="rangeValue"
        :min="min"
        :max="max"
        :step="step"
        :disabled="disabled"
        class="iluria-range"
        @input="handleInput"
        @change="handleChange"
      />
      
      <div v-if="showValue" class="range-value">
        {{ displayValue }}
      </div>
    </div>
    
    <!-- Marc<PERSON> opcionais -->
    <div v-if="showMarkers && markers.length > 0" class="range-markers">
      <div
        v-for="marker in markers"
        :key="marker.value"
        class="range-marker"
        :style="{ left: getMarkerPosition(marker.value) + '%' }"
      >
        <div class="marker-line"></div>
        <div class="marker-label">{{ marker.label || marker.value }}</div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import IluriaLabel from '../IluriaLabel.vue'

const props = defineProps({
  modelValue: {
    type: [Number, String],
    default: 0
  },
  label: {
    type: String,
    default: ''
  },
  min: {
    type: [Number, String],
    default: 0
  },
  max: {
    type: [Number, String],
    default: 100
  },
  step: {
    type: [Number, String],
    default: 1
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showValue: {
    type: Boolean,
    default: true
  },
  valueFormatter: {
    type: Function,
    default: (value) => value
  },
  unit: {
    type: String,
    default: ''
  },
  showMarkers: {
    type: Boolean,
    default: false
  },
  markers: {
    type: Array,
    default: () => []
  },
  name: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'input', 'change'])

// ID único para o range
const rangeId = `range-${Math.random().toString(36).substring(2, 11)}`

// Valor do range
const rangeValue = computed({
  get() {
    return props.modelValue
  },
  set(value) {
    const numValue = parseFloat(value)
    emit('update:modelValue', numValue)
  }
})

// Valor formatado para exibição
const displayValue = computed(() => {
  const formatted = props.valueFormatter(props.modelValue)
  return props.unit ? `${formatted}${props.unit}` : formatted
})

// Handlers
const handleInput = (event) => {
  const value = parseFloat(event.target.value)
  emit('update:modelValue', value)
  emit('input', value)
}

const handleChange = (event) => {
  const value = parseFloat(event.target.value)
  emit('change', value)
}

// Calcular posição dos marcadores
const getMarkerPosition = (value) => {
  const min = parseFloat(props.min)
  const max = parseFloat(props.max)
  return ((value - min) / (max - min)) * 100
}
</script>

<style scoped>
.iluria-range-wrapper {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.range-container {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.iluria-range {
  flex: 1;
  height: 6px;
  background: var(--iluria-color-background);
  border-radius: 3px;
  cursor: pointer;
  -webkit-appearance: none;
  appearance: none;
  outline: none;
  transition: all 0.2s ease;
}

.iluria-range::-webkit-slider-track {
  height: 6px;
  background: var(--iluria-color-background);
  border-radius: 3px;
}

.iluria-range::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  background: var(--iluria-color-button-primary-bg);
  border: 2px solid var(--iluria-color-surface);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.iluria-range::-webkit-slider-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.iluria-range::-moz-range-track {
  height: 6px;
  background: var(--iluria-color-background);
  border-radius: 3px;
  border: none;
}

.iluria-range::-moz-range-thumb {
  width: 18px;
  height: 18px;
  background: var(--iluria-color-button-primary-bg);
  border: 2px solid var(--iluria-color-surface);
  border-radius: 50%;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  transition: all 0.2s ease;
}

.iluria-range::-moz-range-thumb:hover {
  transform: scale(1.1);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
}

.iluria-range:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.iluria-range:disabled::-webkit-slider-thumb {
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.iluria-range:disabled::-moz-range-thumb {
  cursor: not-allowed;
  transform: none;
  box-shadow: none;
}

.range-value {
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  min-width: 3rem;
  text-align: center;
  background: var(--iluria-color-background);
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  border: 1px solid var(--iluria-color-border);
  font-family: monospace;
}

.range-markers {
  position: relative;
  height: 1.5rem;
  margin-top: 0.25rem;
}

.range-marker {
  position: absolute;
  display: flex;
  flex-direction: column;
  align-items: center;
  transform: translateX(-50%);
}

.marker-line {
  width: 1px;
  height: 0.5rem;
  background: var(--iluria-color-border);
}

.marker-label {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin-top: 0.125rem;
  white-space: nowrap;
}

/* Estados de foco para acessibilidade */
.iluria-range:focus {
  outline: 2px solid var(--iluria-color-input-border-focus);
  outline-offset: 2px;
}

/* Estilos responsivos */
@media (max-width: 480px) {
  .range-container {
    flex-direction: column;
    align-items: stretch;
    gap: 0.5rem;
  }
  
  .range-value {
    text-align: center;
    min-width: auto;
  }
}
</style> 
