import { API_URLS } from "@/config/api.config";
import axios from "axios";

class StorePhysicalDataService {

    #storePhysicalData;
    #endpoint = API_URLS.STORE_PHYSICAL_DATA_BASE_URL;

    constructor() {
        this.#storePhysicalData = {}
    }

    async getStorePhysicalData() {
        try {
            const response = await axios.get(this.#endpoint);
            this.#storePhysicalData = response.data;
            return this.#storePhysicalData;
        } catch (error) {
            if (error.response && error.response.status === 404) {
                return null;
            }
        }
    }
    async createStorePhysicalData(storePhysicalData) {
        try {
            const response = await axios.post(this.#endpoint, storePhysicalData);
            this.#storePhysicalData = response.data;
            return response.data;
        } catch (error) {
            throw error;
            console.error(error);
        }
    }
    async updateStorePhysicalData(storePhysicalData) {
        try {
            const response = await axios.put(this.#endpoint, storePhysicalData);
            this.#storePhysicalData = response.data;
            return response.data;
        } catch (error) {
            throw error;
            console.error(error);
        }
    }
    async saveStorePhysicalData(storePhysicalData) {
        try {
            const existingData = await this.getStorePhysicalData();
            if (existingData) {
                return await this.updateStorePhysicalData(storePhysicalData);
            } else {
                return await this.createStorePhysicalData(storePhysicalData);
            }
        } catch (error) {
            throw error;
            console.error(error);
        }
    }
}

export default new StorePhysicalDataService();