import { ref, onMounted, onUnmounted } from 'vue';
import { useAuthStore } from '@/stores/auth.store';
import tokenManager from '@/services/tokenManager.service';

export function useSessionTimeout() {
    const authStore = useAuthStore();
    const isWarningVisible = ref(false);
    const timeUntilExpiry = ref(0);
    const warningTimeout = ref(null);
    const checkInterval = ref(null);
    
    // Configuration
    const WARNING_THRESHOLD_MINUTES = 5; // Show warning 5 minutes before expiry
    const CHECK_INTERVAL_MS = 30000; // Check every 30 seconds
    
    /**
     * Check token expiration and show warning if needed
     */
    const checkTokenExpiration = () => {
        const validations = authStore.validateTokens();
        
        let nearestExpiry = null;
        let expiringTokenType = null;
        
        // Find the token that expires soonest
        for (const [tokenType, validation] of Object.entries(validations)) {
            if (validation && validation.isValid && validation.timeUntilExpiry) {
                if (!nearestExpiry || validation.timeUntilExpiry < nearestExpiry) {
                    nearestExpiry = validation.timeUntilExpiry;
                    expiringTokenType = tokenType;
                }
            }
        }
        
        if (nearestExpiry) {
            const minutesUntilExpiry = Math.floor(nearestExpiry / (1000 * 60));
            timeUntilExpiry.value = minutesUntilExpiry;
            
            // Show warning if token expires soon
            if (minutesUntilExpiry <= WARNING_THRESHOLD_MINUTES && !isWarningVisible.value) {
                showExpirationWarning(expiringTokenType, minutesUntilExpiry);
            }
            
            // Auto-logout if token has expired
            if (minutesUntilExpiry <= 0) {
                authStore.logout();
            }
        }
    };
    
    /**
     * Show expiration warning to user
     */
    const showExpirationWarning = (tokenType, minutes) => {
        isWarningVisible.value = true;
        
        console.warn(`${tokenType} token will expire in ${minutes} minutes`);
        
        // You can emit an event here for UI components to show a notification
        if (typeof window !== 'undefined') {
            window.dispatchEvent(new CustomEvent('session-warning', {
                detail: { tokenType, minutes }
            }));
        }
        
        // Auto-hide warning after some time
        if (warningTimeout.value) {
            clearTimeout(warningTimeout.value);
        }
        
        warningTimeout.value = setTimeout(() => {
            isWarningVisible.value = false;
        }, 60000); // Hide after 1 minute
    };
    
    /**
     * Extend session by refreshing tokens
     */
    const extendSession = async () => {
        try {
            const userToken = authStore.userToken;
            const storeToken = authStore.storeToken;
            
            // Try to refresh user token
            if (userToken && tokenManager.needsRefresh(userToken)) {
                const newUserToken = await tokenManager.refreshUserToken(userToken);
                if (newUserToken) {
                    authStore.userToken = newUserToken;
                }
            }
            
            // Try to refresh store token if available
            if (storeToken && tokenManager.needsRefresh(storeToken)) {
                const storeStore = await import('@/stores/store.store');
                const selectedStore = storeStore.useStoreStore().selectedStore;
                
                if (selectedStore?.id && authStore.userToken) {
                    const newStoreToken = await tokenManager.refreshStoreToken(selectedStore.id, authStore.userToken);
                    if (newStoreToken) {
                        authStore.setStoreToken(newStoreToken);
                    }
                }
            }
            
            // Hide warning after successful refresh
            isWarningVisible.value = false;
            
            return true;
        } catch (error) {
            console.error('Failed to extend session:', error);
            return false;
        }
    };
    
    /**
     * Dismiss the warning manually
     */
    const dismissWarning = () => {
        isWarningVisible.value = false;
        if (warningTimeout.value) {
            clearTimeout(warningTimeout.value);
            warningTimeout.value = null;
        }
    };
    
    /**
     * Get formatted time until expiry
     */
    const getFormattedTimeUntilExpiry = () => {
        if (timeUntilExpiry.value <= 0) return 'Expirado';
        if (timeUntilExpiry.value < 60) return `${timeUntilExpiry.value} minutos`;
        
        const hours = Math.floor(timeUntilExpiry.value / 60);
        const minutes = timeUntilExpiry.value % 60;
        
        return `${hours}h ${minutes}m`;
    };
    
    /**
     * Monitor user activity
     */
    const activityEvents = ['mousedown', 'mousemove', 'keypress', 'scroll', 'touchstart', 'click'];
    
    const handleUserActivity = () => {
        // Reset session timeout on user activity
        if (authStore.userToken) {
            tokenManager.resetSessionTimeout('user_session', () => {
                authStore.logout();
            });
        }
    };
    
    /**
     * Start monitoring
     */
    const startMonitoring = () => {
        // Start periodic token checking
        checkInterval.value = setInterval(checkTokenExpiration, CHECK_INTERVAL_MS);
        
        // Monitor user activity
        activityEvents.forEach(event => {
            document.addEventListener(event, handleUserActivity, true);
        });
        
        // Initial check
        checkTokenExpiration();
    };
    
    /**
     * Stop monitoring
     */
    const stopMonitoring = () => {
        if (checkInterval.value) {
            clearInterval(checkInterval.value);
            checkInterval.value = null;
        }
        
        if (warningTimeout.value) {
            clearTimeout(warningTimeout.value);
            warningTimeout.value = null;
        }
        
        // Remove activity listeners
        activityEvents.forEach(event => {
            document.removeEventListener(event, handleUserActivity, true);
        });
        
        isWarningVisible.value = false;
    };
    
    // Lifecycle hooks
    onMounted(() => {
        if (authStore.userLoggedIn) {
            startMonitoring();
        }
    });
    
    onUnmounted(() => {
        stopMonitoring();
    });
    
    return {
        isWarningVisible,
        timeUntilExpiry,
        checkTokenExpiration,
        extendSession,
        dismissWarning,
        getFormattedTimeUntilExpiry,
        startMonitoring,
        stopMonitoring
    };
}

export default useSessionTimeout;