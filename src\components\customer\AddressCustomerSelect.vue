<template>
  <div>
    <IluriaSelect
      id="address-select"
      name="address-select"
      v-model="selectedAddress"
      :options="filteredAddresses"
      :filter="true"
      optionLabel="label"
      :delay="0"
      :minLength="2"
      :placeholder="t('order.selectAddress')"
      :loading="loading"
      @filter="searchAddress"
      @update:modelValue="emitSelectedAddress"
      @change="emitSelectedAddress"
      @click="loadAddressesIfEmpty"
      class="w-full"
      :optionValue="'id'"
      :optionLabel="'label'"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import { useI18n } from 'vue-i18n'
import CustomerService from '@/services/customer.service'

const { t } = useI18n()
const props = defineProps({
  customerId: {
    type: [Number, String],
    default: null
  },
  modelValue: {
    type: [Object, String, Number, null],
    default: null
  }
})

const selectedAddress = ref(props.modelValue ? props.modelValue.id || props.modelValue : null)
const filteredAddresses = ref([])
const loading = ref(false)

const emit = defineEmits(['select', 'update:modelValue'])

watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    selectedAddress.value = newValue.id || newValue
  } else {
    selectedAddress.value = null
  }
}, { immediate: true })

const emitSelectedAddress = () => {
  const address = filteredAddresses.value.find(a => a.id === selectedAddress.value)
  if (address) {
    emit('update:modelValue', address)
    emit('select', address)
  }
}

const loadAddressesIfEmpty = () => {
  if (filteredAddresses.value.length === 0) {
    loadAddresses()
  }
}

const searchAddress = (event) => {
  loadAddresses()
}

const loadAddresses = async () => {
  if (!props.customerId) return;
  loading.value = true
  try {
    const customer = await CustomerService.getCustomer(props.customerId)
    if (customer?.addresses) {
      filteredAddresses.value = customer.addresses.map(address => ({
        ...address,
        label: `${address.street}, ${address.number} - ${address.city}/${address.state}`
      }))
    } else {
      filteredAddresses.value = []
    }
  } catch (error) {
    console.error('Error loading addresses:', error)
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  if (props.customerId) {
    loadAddresses()
  }
})
</script>
