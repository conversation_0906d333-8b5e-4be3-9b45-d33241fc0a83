<template>
  <div class="tree-view" @click.self="hideContextMenu">
    <!-- Menu de contexto -->
    <ContextMenu 
      :model="[
        { 
          label: t('fileManager.rename'), 
          icon: 'pi pi-pencil',
          command: () => handleEdit(contextMenu.node)
        },
      ]" 
      :pt="{
        menu: { class: 'shadow-lg border border-gray-200 rounded-md' },
        menuitem: { class: 'hover:bg-gray-100' }
      }"
      :position="contextMenu"
      @hide="hideContextMenu"
    />
    
    <div class="space-y-1">
      <template v-if="!rootNodes || rootNodes.length === 0">
        <div class="text-sm text-gray-500 py-2 px-2">{{ t('fileManager.noFolders') }}</div>
      </template>
      
      <template v-else>
        <div 
          v-for="node in rootNodes" 
          :key="node.key"
          class="tree-node"
        >
          <div
            :class="[
              'flex items-center py-1 px-1 rounded-md cursor-pointer hover:bg-gray-100 transition-colors group relative',
              selectedNodeKey === node.key ? `bg-${environmentColor}-light` : ''
            ]"
            @click="selectNode(node)"
            @contextmenu="showContextMenu($event, node)"
          >
            <template v-if="node.type === 'FOLDER'">
              <button
                @click.stop="toggleNode(node)"
                class="flex items-center justify-center w-5 h-5 mr-1 text-gray-500 hover:text-gray-700 focus:outline-none rounded hover:bg-gray-200"
                :disabled="loadingNodes[node.key]"
              >
                <template v-if="loadingNodes[node.key]">
                  <HugeiconsIcon :icon="Loading01Icon" size="14" />
                </template>
                <template v-else>
                  <HugeiconsIcon :icon="expandedKeys[node.key] ? ArrowDown01Icon : ArrowRight01Icon" size="14" />
                </template>
              </button>
            </template>
            <template v-else>
              <div class="w-5 mr-1"></div>
            </template>
            
            <button 
              v-if="(node.label || node.name)?.match(/\.(jpg|jpeg|png|gif|webp|svg|bmp)$/i)"
              @click.stop="previewImage(node)"
              class="mr-2 flex-shrink-0"
            >
              <HugeiconsIcon 
                :icon="getNodeIcon(node, expandedKeys[node.key])" 
                class="text-green-500 hover:text-green-600 transition-colors"
                size="16"
              />
            </button>
            <HugeiconsIcon
              v-else
              :icon="getNodeIcon(node, expandedKeys[node.key])"
              class="mr-2 flex-shrink-0"
              :class="{
                'text-yellow-500': node.type === 'FOLDER',
                'text-blue-500': node.type === 'FILE'
              }"
              size="16"
            />
            
            <div v-if="editingFile?.key === node.key" class="flex-1">
              <input
                :id="`rename-input-${node.key}`"
                v-model="newFileName"
                type="text"
                :size="Math.max(newFileName.length, 1)"
                class="text-xs bg-transparent border border-transparent focus:border-[var(--iluria-color-primary)] rounded px-0.5 py-0.5 w-auto max-w-[140px]"
                @keyup.enter="saveRename"
                @blur="saveRename"
                @keyup.escape="cancelRename"
              />
            </div>
            <span
              v-else
              class="text-xs truncate flex-1 max-w-[150px] leading-5"
              :title="node.label || node.name || t('fileManager.noName')"
            >
              {{ node.label || node.name || t('fileManager.noName') }}
            </span>
            
            <div class="flex items-center opacity-100 transition-opacity ml-auto gap-1">
              <button 
                v-if="editingFile?.key !== node.key"
                class="p-0.5 text-gray-500 hover:text-blue-600 rounded-full hover:bg-blue-50 transition-colors"
                @click.stop="handleRename(node)"
                title="Renomear"
              >
                <HugeiconsIcon :icon="PencilEdit01Icon" size="14" />
              </button>
              <button 
                v-else
                class="p-0.5 text-green-600 hover:bg-green-50 rounded-full transition-colors"
                @click.stop="saveRename"
                title="Salvar"
              >
                <HugeiconsIcon :icon="CheckmarkCircle02Icon" size="14" />
              </button>
              <button 
                class="p-0.5 text-gray-500 hover:text-red-600 rounded-full hover:bg-red-50 transition-colors"
                @click.stop="handleDelete(node)"
                title="Excluir"
              >
                <HugeiconsIcon :icon="Delete01Icon" size="14" />
              </button>
            </div>
          </div>
          
          <div
            v-if="node.type === 'FOLDER' && expandedKeys[node.key]"
            class="ml-5 mt-0.5 border-l border-gray-200 pl-3"
          >
            <div v-if="loadingNodes[node.key]" class="py-2 px-2">
              <div class="flex items-center text-xs text-gray-500">
                <HugeiconsIcon :icon="Loading01Icon" size="14" />
              </div>
            </div>
            
            <div 
              v-else-if="!nodeChildren[node.key] || nodeChildren[node.key].length === 0"
              class="text-xs text-gray-400 py-1 px-2"
            >
              {{ t('fileManager.empty') }}
            </div>
            
            <template v-else>
              <TreeView
                :nodes="nodeChildren[node.key]"
                :environment-color="environmentColor"
                :environment="environment"
                @node-select="(n) => $emit('node-select', n)"
                @node-delete="(n) => $emit('node-delete', n)"
                @node-edit="(n) => $emit('node-edit', n)"
                @node-updated="(n) => $emit('node-updated', n)"
                @refresh-tree="$emit('refresh-tree')"
              />
            </template>
          </div>
        </div>
      </template>
    </div>
  </div>
  
  <IluriaConfirmationModal
    :is-visible="showConfirmModal"
    :title="confirmModalTitle"
    :message="confirmModalMessage"
    :confirm-text="confirmModalConfirmText"
    :cancel-text="confirmModalCancelText"
    :type="confirmModalType"
    @confirm="handleConfirm"
    @cancel="handleCancel"
  />
</template>
<script setup>
import { ref, onMounted, computed, nextTick, watch } from 'vue';
// Removido useConfirm - usando IluriaConfirmationModal
import { useToast } from '@/services/toast.service';
import { useI18n } from 'vue-i18n'

import ContextMenu from 'primevue/contextmenu';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaInputText from '@/components/Iluria/form/IluriaInputText.vue';
import fileManagerService from '@/services/fileManager.service.js';
import {HugeiconsIcon} from '@hugeicons/vue';
import { CheckmarkCircle02Icon, Loading01Icon, Delete01Icon,
   ArrowDown01Icon, ArrowRight01Icon, PencilEdit01Icon,
    Folder01Icon, Folder02Icon,
    Image01Icon,
    Doc01Icon,
    Pdf01Icon,
    Xls01Icon,
    Ppt01Icon,
    File01Icon,
    DocumentCodeIcon,
    Zip01Icon } from '@hugeicons-pro/core-bulk-rounded';

const { t } = useI18n()

const props = defineProps({
  nodes: {
    type: Array,
    default: () => []
  },
  environment: {
    type: String,
    required: true
  },
  environmentColor: {
    type: String,
    default: 'blue'
  },
  parentFolderId: {
    type: String
  }
});

const emit = defineEmits(['node-select', 'node-edit', 'node-delete', 'node-updated', 'refresh-tree']);

const editingFile = ref(null);
const newFileName = ref('');



const handleRename = (node) => {
  editingFile.value = { ...node };
  newFileName.value = node.label || node.name || '';
  
  nextTick(() => {
    const input = document.getElementById(`rename-input-${node.key}`);
    if (input) {
      input.focus();
      input.select();
    }
  });
};

const saveRename = async () => {
  if (!editingFile.value || !newFileName.value.trim() || 
      newFileName.value.trim() === (editingFile.value.name || editingFile.value.label)) {
    editingFile.value = null;
    return;
  }

  try {
    const newName = newFileName.value.trim();
    const node = editingFile.value;
    
    const parentId = node.parentFolderId;
    
    await fileManagerService.update(node.key, { 
      name: newName,
      parentFolderId: parentId,
      environment: node.environment || props.environment
    });
    
    const updatedNode = { 
      ...node,
      name: newName,
      label: newName,
      parentFolderId: parentId 
    };
    
    const updateNodeInTree = (nodes, nodeKey, updates) => {
      for (let i = 0; i < nodes.length; i++) {
        if (nodes[i].key === nodeKey) {
          nodes[i] = { ...nodes[i], ...updates };
          return true;
        }
        if (nodes[i].children) {
          if (updateNodeInTree(nodes[i].children, nodeKey, updates)) {
            return true;
          }
        }
      }
      return false;
    };
    
    updateNodeInTree(props.nodes, node.key, {
      name: newName,
      label: newName
    });
    
    emit('node-updated', updatedNode);
    
    toast.showSuccess(t('fileManager.renameSuccess'));
  } catch (error) {
    console.error('Erro ao renomear:', error);
    toast.showError(t('fileManager.renameError'));
  } finally {
    editingFile.value = null;
    newFileName.value = '';
  }
};

const cancelRename = () => {
  editingFile.value = null;
};

const toast = useToast();

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

// Modal control functions
const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const contextMenu = ref({
  visible: false,
  x: 0,
  y: 0,
  node: null
});

const showContextMenu = (event, node) => {
  event.preventDefault();
  contextMenu.value = {
    visible: true,
    x: event.clientX,
    y: event.clientY,
    node
  };
};

const hideContextMenu = () => {
  contextMenu.value.visible = false;
};

const handleEdit = (node) => {
  handleRename(node);
  emit('node-edit', node);
};

const handleDelete = (node) => {
  hideContextMenu();
  emit('node-delete', node);
};

const removeNodeFromTree = (nodeKey) => {
  const removeNode = (nodes) => {
    if (!nodes || !Array.isArray(nodes)) return false;
    
    for (let i = 0; i < nodes.length; i++) {
      if (nodes[i].key === nodeKey) {
        nodes.splice(i, 1);
        return true;
      }
      
      if (nodes[i].children && nodes[i].children.length > 0) {
        if (removeNode(nodes[i].children)) {
          return true;
        }
      }
    }
    
    return false;
  };
  
  removeNode(props.nodes);
};

const expandedKeys = ref({});
const selectedNodeKey = ref(null);
const loadingNodes = ref({});
const nodeChildren = ref({});

// Observar mudanças nos nodes para atualizar a árvore automaticamente
watch(() => props.nodes, (newNodes) => {
  if (newNodes && Array.isArray(newNodes)) {
    // Atualizar os filhos dos nós que já estão expandidos
    Object.keys(expandedKeys.value).forEach(async (nodeKey) => {
      if (expandedKeys.value[nodeKey]) {
        // Encontrar o nó na nova estrutura
        const findNode = (nodes, key) => {
          for (const node of nodes) {
            if (node.key === key) return node;
            if (node.children && node.children.length > 0) {
              const found = findNode(node.children, key);
              if (found) return found;
            }
          }
          return null;
        };
        
        const node = findNode(newNodes, nodeKey);
        if (node) {
          // Atualizar os filhos deste nó
          await loadNodeChildren(node);
        }
      }
    });
  }
}, { deep: true });

onMounted(async () => {
  if (props.nodes && props.nodes.length > 0) {
    const firstNode = props.nodes[0];
    if (firstNode.type === 'FOLDER') {
      expandedKeys.value[firstNode.key] = true;
      
      if (firstNode.children && firstNode.children.length > 0) {
        nodeChildren.value[firstNode.key] = firstNode.children;
      } 
      else if (firstNode.hasChildren) {
        await loadNodeChildren(firstNode);
      }
    }
  }
});

const rootNodes = computed(() => {
  const filtered = props.nodes.filter(node => !node.parentId);
  return filtered;
});

const hasChildren = (node) => {
  return node.hasChildren || (node.children && node.children.length > 0);
};

const toggleNode = async (node) => {
  expandedKeys.value[node.key] = !expandedKeys.value[node.key];
  
  if (expandedKeys.value[node.key] && node.type === 'FOLDER' && !nodeChildren.value[node.key]) {
    await loadNodeChildren(node);
  }
};

const loadNodeChildren = async (node) => {
  if (!node.key || loadingNodes.value[node.key]) return;
  
  try {
    loadingNodes.value[node.key] = true;
    
    if (node.children && node.children.length > 0) {
      nodeChildren.value[node.key] = node.children;
    } 
    else if (node.hasChildren) {
      nodeChildren.value[node.key] = [];
    }
  } catch (error) {
    console.error('Error loading node children:', error);
  } finally {
    loadingNodes.value[node.key] = false;
  }
};

const selectNode = (node) => {
  selectedNodeKey.value = node.key;
  emit('node-select', node);
};

const getFileIcon = (fileName) => {
  if (!fileName) return Folder01Icon;
  
  const extension = fileName.split('.').pop().toLowerCase();
  
  const imageExtensions = ['jpg', 'jpeg', 'png', 'gif', 'webp', 'svg', 'bmp'];
  if (imageExtensions.includes(extension)) return Image01Icon;
  
  const docExtensions = ['doc', 'docx'];
  if (docExtensions.includes(extension)) return Doc01Icon;
  
  const pdfExtensions = ['pdf'];
  if (pdfExtensions.includes(extension)) return Pdf01Icon;
  
  const xlsExtensions = ['xls', 'xlsx', 'csv'];
  if (xlsExtensions.includes(extension)) return Xls01Icon;
  
  const pptExtensions = ['ppt', 'pptx'];
  if (pptExtensions.includes(extension)) return Ppt01Icon;
  
  const codeExtensions = ['js', 'ts', 'jsx', 'tsx', 'vue', 'html', 'css', 'scss', 'json'];
  if (codeExtensions.includes(extension)) return DocumentCodeIcon;
  
  const archiveExtensions = ['zip', 'rar', '7z', 'tar', 'gz'];
  if (archiveExtensions.includes(extension)) return Zip01Icon;
  
  return File01Icon;
};

const getNodeIcon = (node, isExpanded) => {
  if (node.type === 'FOLDER') {
    return isExpanded ? Folder02Icon : Folder01Icon;
  }
  return getFileIcon(node.label || node.name);
};
</script>

<style scoped>
.tree-view {
  width: 100%;
  overflow-x: hidden;
}

.tree-node {
  font-size: 0.75rem;
  line-height: 1rem;
}

/* Ensure buttons don't grow too large */
.tree-node button {
  min-width: auto;
  min-height: auto;
}

/* Improve spacing for deeply nested items */
.tree-node .tree-node {
  margin-top: 0.125rem;
}

/* Better alignment for folder items */
.tree-node .flex.items-center {
  min-height: 1.75rem;
}

/* Consistent spacing for folder icons */
.tree-node .flex.items-center > * {
  align-items: center;
}


</style>
