<template>
  <div>
    <!-- Category Item -->
    <div 
      :class="[
        'flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-[var(--color-background-alt)] transition-colors',
        isSelected ? 'bg-[var(--color-primary)]/10 text-[var(--color-primary)] font-medium' : 'text-[var(--color-text)]',
        `pl-${4 + (level * 4)}`
      ]"
      @click="selectCategory"
    >
      <!-- Expand/Collapse Button -->
      <button
        v-if="hasChildren"
        type="button"
        @click.stop="toggleExpand"
        class="mr-2 p-0.5 hover:bg-[var(--color-background-hover)] transition-colors flex-shrink-0 border-0 outline-none focus:outline-none"
        style="border: none; outline: none; background: transparent;"
      >
        <svg 
          :class="[
            'w-4 h-4 text-[var(--color-text-light)] transition-transform duration-200',
            isExpanded ? 'transform rotate-90' : ''
          ]" 
          fill="none" 
          stroke="currentColor" 
          viewBox="0 0 24 24"
        >
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
        </svg>
      </button>

      <!-- Spacer for items without children -->
      <div v-else class="w-5 mr-2 flex-shrink-0"></div>

      <!-- Category Icon -->
      <div class="mr-3 flex-shrink-0">
        <svg class="w-4 h-4 text-[var(--color-text-light)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
        </svg>
      </div>

      <!-- Category Name with Search Highlight -->
      <span class="flex-1 truncate" v-html="highlightedName"></span>

      <!-- Children Count Badge -->
      <span 
        v-if="hasChildren" 
        class="ml-2 px-2 py-0.5 text-xs bg-[var(--color-text-light)]/10 text-[var(--color-text-light)] rounded-full flex-shrink-0"
      >
        {{ category.children.length }}
      </span>
    </div>

    <!-- Children Categories -->
    <transition
      enter-active-class="transition-all duration-200 ease-out"
      enter-from-class="opacity-0 max-h-0"
      enter-to-class="opacity-100 max-h-screen"
      leave-active-class="transition-all duration-200 ease-in"
      leave-from-class="opacity-100 max-h-screen"
      leave-to-class="opacity-0 max-h-0"
    >
      <div v-if="hasChildren && isExpanded" class="overflow-hidden">
        <CategoryItem
          v-for="child in category.children"
          :key="child.id"
          :category="child"
          :level="level + 1"
          :selectedId="selectedId"
          :searchQuery="searchQuery"
          :expandedCategories="expandedCategories"
          @select="$emit('select', $event)"
          @toggle="$emit('toggle', $event)"
        />
      </div>
    </transition>
  </div>
</template>

<script setup>
import { computed } from 'vue';

// Props
const props = defineProps({
  category: {
    type: Object,
    required: true
  },
  level: {
    type: Number,
    default: 0
  },
  selectedId: {
    type: [String, Number],
    default: null
  },
  searchQuery: {
    type: String,
    default: ''
  },
  expandedCategories: {
    type: Set,
    default: () => new Set()
  }
});

// Emits
const emit = defineEmits(['select', 'toggle']);

// Computed
const hasChildren = computed(() => {
  return props.category.children && props.category.children.length > 0;
});

const isSelected = computed(() => {
  return props.selectedId === props.category.id;
});

const isExpanded = computed(() => {
  return props.expandedCategories.has(props.category.id) || 
         (props.searchQuery && hasChildren.value);
});

const categoryName = computed(() => {
  return props.category.name || props.category.title || 'Sem nome';
});

const highlightedName = computed(() => {
  if (!props.searchQuery) {
    return categoryName.value;
  }
  
  const regex = new RegExp(`(${props.searchQuery})`, 'gi');
  return categoryName.value.replace(regex, '<mark class="bg-[var(--color-primary)]/20 text-[var(--color-primary)] px-0.5 rounded">$1</mark>');
});

// Methods
const selectCategory = () => {
  emit('select', props.category.id);
};

const toggleExpand = () => {
  if (hasChildren.value) {
    emit('toggle', props.category.id);
  }
};
</script>

<style scoped>
/* Smooth transitions for collapse/expand */
.transition-all {
  transition-property: all;
}

/* Custom max-height for smooth collapse */
.max-h-screen {
  max-height: 100vh;
}

.max-h-0 {
  max-height: 0;
}

/* Ensure proper indentation */
.pl-4 { padding-left: 1rem; }
.pl-8 { padding-left: 2rem; }
.pl-12 { padding-left: 3rem; }
.pl-16 { padding-left: 4rem; }
.pl-20 { padding-left: 5rem; }
.pl-24 { padding-left: 6rem; }

/* Custom mark styling */
:deep(mark) {
  background-color: var(--color-primary, #3b82f6);
  background-color: color-mix(in srgb, var(--color-primary) 20%, transparent);
  color: var(--color-primary);
  padding: 0.125rem 0.25rem;
  border-radius: 0.25rem;
}
</style> 
