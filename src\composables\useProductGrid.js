import { ref, computed, watch } from 'vue'
import { productsApi } from '@/services/product.service'
import { categoryService } from '@/services/category.service'
import storeService from '@/services/store.service'
import axios from 'axios'
import { API_URLS } from '@/config/api.config'

/**
 * Composable para gerenciar Product Grid no Layout Editor
 * Fornece funcionalidades para seleção e estilo de produtos
 */
export function useProductGrid(element) {
  // Estado reativo
  const isLoading = ref(false)
  const products = ref([])
  const categories = ref([])
  const selectedProducts = ref([])
  
  // Configurações do grid
  const selectionMode = ref('automatic')
  const selectedCategory = ref('all')
  const sortBy = ref('name')
  const productLimit = ref(8)
  const columns = ref(4)
  
  // Configurações de estilo
  const gridGap = ref(1.5)
  const cardPadding = ref(1.5)
  const borderRadius = ref(12)
  const shadowType = ref('medium')
  const backgroundColor = ref('#ffffff')
  const textColor = ref('#1f2937')
  const buttonColor = ref('#3b82f6')
  const titleFontSize = ref(1.125)
  const priceFontSize = ref(1.25)
  const fontWeight = ref('600')
  const mobileColumns = ref(2)
  const tabletColumns = ref(3)

  // Computed properties
  const filteredProducts = computed(() => {
    if (selectionMode.value === 'manual') {
      return selectedProducts.value
    }
    
    let filtered = products.value
    
    // Filtrar por categoria
    if (selectedCategory.value !== 'all') {
      filtered = filtered.filter(product => 
        product.categoryId === selectedCategory.value
      )
    }
    
    // Ordenar
    switch (sortBy.value) {
      case 'newest':
        filtered = filtered.sort((a, b) => new Date(b.createdAt) - new Date(a.createdAt))
        break
      case 'price-asc':
        filtered = filtered.sort((a, b) => a.price - b.price)
        break
      case 'price-desc':
        filtered = filtered.sort((a, b) => b.price - a.price)
        break
      default:
        filtered = filtered.sort((a, b) => a.name.localeCompare(b.name))
    }
    
    // Limitar quantidade
    return filtered.slice(0, productLimit.value)
  })

  const gridStyles = computed(() => ({
    '--columns': columns.value,
    '--gap': `${gridGap.value}rem`,
    '--card-padding': `${cardPadding.value}rem`,
    '--border-radius': `${borderRadius.value}px`,
    '--background-color': backgroundColor.value,
    '--text-color': textColor.value,
    '--button-color': buttonColor.value,
    '--title-font-size': `${titleFontSize.value}rem`,
    '--price-font-size': `${priceFontSize.value}rem`,
    '--font-weight': fontWeight.value,
    '--mobile-columns': mobileColumns.value,
    '--tablet-columns': tabletColumns.value,
    '--shadow': getShadowValue(shadowType.value)
  }))

  // Test storefront connectivity
  const testStorefrontConnectivity = async (domain) => {
    try {
     

      // Primeiro, testar se o domínio resolve
      const hostname = new URL(domain).hostname
     

      // Testar endpoint básico do storefront
      const testUrl = `${domain}:8080/api/storefront/products?size=1`
     

      const response = await fetch(testUrl, {
        method: 'GET',
        mode: 'cors',
        headers: {
          'Content-Type': 'application/json',
          'Accept': 'application/json'
        },
        // Timeout mais curto para teste
        signal: AbortSignal.timeout(5000)
      })

    

      if (response.ok) {
        const data = await response.json()
       
      } else {
        console.warn('⚠️ [testStorefrontConnectivity] Storefront retornou erro:', response.status, response.statusText)
        const errorText = await response.text()
        console.warn('📄 [testStorefrontConnectivity] Resposta de erro:', errorText)
      }
    } catch (error) {
      console.error('❌ [testStorefrontConnectivity] Erro de conectividade:', error)

      if (error.name === 'AbortError') {
        console.error('⏱️ [testStorefrontConnectivity] Timeout - Storefront não responde')
        console.error('💡 [testStorefrontConnectivity] Verifique se o storefront está rodando')
      } else if (error.message.includes('CORS')) {
        console.error('🚫 [testStorefrontConnectivity] Problema de CORS detectado!')
        console.error('💡 [testStorefrontConnectivity] Verifique configuração de CORS no storefront')
      } else if (error.message.includes('Failed to fetch')) {
        console.error('🔌 [testStorefrontConnectivity] Falha na conexão')
        console.error('💡 [testStorefrontConnectivity] Possíveis causas:')
        console.error('   - Storefront offline')
        console.error('   - Problema de rede')
        console.error('   - Domínio não resolve')
      } else {
        console.error('❓ [testStorefrontConnectivity] Erro desconhecido:', error.message)
      }
    }
  }

  // Methods
  const getStorefrontDomain = async () => {
    try {
     

      // Buscar domínios registrados na tabela domain_url
      const domainsResponse = await axios.get(`${API_URLS.DOMAINS_BASE_URL}`)
      const domains = domainsResponse.data || []
     

      if (domains.length > 0) {
        // Priorizar domínio MAIN, depois qualquer outro
        const mainDomain = domains.find(d => d.domainUrlType === 'MAIN')
        const selectedDomain = mainDomain || domains[0]

       
        const domainName = selectedDomain.name

        // Garantir que tenha protocolo e porta
        let fullDomain
        if (domainName.startsWith('http://') || domainName.startsWith('https://')) {
          fullDomain = domainName
        } else {
          // Para desenvolvimento, SEMPRE usar HTTP com porta 8080
          // Detectar domínios de desenvolvimento: localhost, .iluria.com, .exemplo.com
          const isDevelopmentDomain = domainName.includes('localhost') ||
                                    domainName.includes('.iluria.com') ||
                                    domainName.includes('.exemplo.com')

          if (isDevelopmentDomain) {
            fullDomain = `http://${domainName}:8080`
           
          } else {
            fullDomain = `https://${domainName}`
         
          }
        }

        // Se já tem protocolo mas é domínio de desenvolvimento sem porta, corrigir
        if (fullDomain.startsWith('https://') &&
            (domainName.includes('.iluria.com') || domainName.includes('.exemplo.com') || domainName.includes('localhost'))) {
          // Converter HTTPS para HTTP:8080 para desenvolvimento
          const url = new URL(fullDomain)
          url.protocol = 'http:'
          url.port = '8080'
          fullDomain = url.toString()
        
        }

       

        // Testar se o storefront está acessível
        await testStorefrontConnectivity(fullDomain)

        // Se for domínio .iluria.com, verificar se localhost funciona como alternativa
        if (domainName.includes('.iluria.com')) {
         
        }

        return fullDomain
      }

      // Se não há domínios registrados, tentar localhost como fallback
      console.warn('⚠️ [getStorefrontDomain] Nenhum domínio registrado encontrado!')
     
      const localhostDomain = 'http://localhost:8080'
      try {
        await testStorefrontConnectivity(localhostDomain)
       
        return localhostDomain
      } catch (error) {
        console.error('❌ [getStorefrontDomain] Localhost também não funciona:', error.message)
      }

      // Tentar dados da loja como último recurso
    
      const storeData = await storeService.getStoreData()
      const domain = storeData.url || storeData.urlIluria

      if (domain) {
        const protocol = domain.includes('localhost') ? 'http://' : 'http://' // Sempre HTTP para desenvolvimento
        const fullDomain = domain.startsWith('http') ? domain : `${protocol}${domain}`
        console.warn('⚠️ [getStorefrontDomain] Usando domínio não registrado (pode falhar):', fullDomain)
        console.warn('💡 [getStorefrontDomain] RECOMENDAÇÃO: Registre este domínio em Configurações > Domínios')
        return fullDomain
      }

      // Último fallback
      console.error('❌ [getStorefrontDomain] Nenhum domínio encontrado!')
      console.error('💡 [getStorefrontDomain] Configure um domínio em Configurações > Domínios')
      console.error('🔧 [getStorefrontDomain] Usando fallback final: http://localhost:8080')
      return 'http://localhost:8080'

    } catch (error) {
      console.error('❌ [getStorefrontDomain] Erro ao buscar domínios:', error)
      return 'http://localhost:8080'
    }
  }

  const loadCategories = async () => {
    try {
      isLoading.value = true
      const response = await categoryService.fetchCategories()
      categories.value = response || []
    } catch (error) {
      console.error('Error loading categories:', error)
      categories.value = []
    } finally {
      isLoading.value = false
    }
  }

  const loadProducts = async () => {
    try {
      isLoading.value = true
      const response = await productsApi.getProductsWithVariations({
        page: 0,
        size: 100,
        categoryId: selectedCategory.value !== 'all' ? selectedCategory.value : undefined
      })
      products.value = response.data?.content || []
    } catch (error) {
      console.error('Error loading products:', error)
      products.value = []
    } finally {
      isLoading.value = false
    }
  }

  const getShadowValue = (type) => {
    const shadows = {
      none: 'none',
      small: '0 1px 3px 0 rgba(0, 0, 0, 0.1)',
      medium: '0 4px 6px -1px rgba(0, 0, 0, 0.1)',
      large: '0 10px 15px -3px rgba(0, 0, 0, 0.1)'
    }
    return shadows[type] || shadows.medium
  }

  const initializeFromElement = () => {
    if (!element) return

    // Inicializar configurações de seleção
    selectionMode.value = element.getAttribute('data-selection-mode') || 'automatic'
    selectedCategory.value = element.getAttribute('data-category') || 'all'
    sortBy.value = element.getAttribute('data-sort-by') || 'name'
    productLimit.value = parseInt(element.getAttribute('data-limit')) || 8
    columns.value = parseInt(element.getAttribute('data-columns')) || 4

    // Inicializar configurações de estilo
    gridGap.value = parseFloat(element.getAttribute('data-gap')) || 1.5
    cardPadding.value = parseFloat(element.getAttribute('data-card-padding')) || 1.5
    borderRadius.value = parseInt(element.getAttribute('data-border-radius')) || 12
    shadowType.value = element.getAttribute('data-shadow') || 'medium'
    backgroundColor.value = element.getAttribute('data-bg-color') || '#ffffff'
    textColor.value = element.getAttribute('data-text-color') || '#1f2937'
    buttonColor.value = element.getAttribute('data-button-color') || '#3b82f6'
    titleFontSize.value = parseFloat(element.getAttribute('data-title-font-size')) || 1.125
    priceFontSize.value = parseFloat(element.getAttribute('data-price-font-size')) || 1.25
    fontWeight.value = element.getAttribute('data-font-weight') || '600'
    mobileColumns.value = parseInt(element.getAttribute('data-mobile-columns')) || 2
    tabletColumns.value = parseInt(element.getAttribute('data-tablet-columns')) || 3

    // Carregar produtos selecionados manualmente
    if (selectionMode.value === 'manual') {
      const selectedIds = element.getAttribute('data-selected-products')
      if (selectedIds) {
        const ids = selectedIds.split(',')
        // Carregar produtos e filtrar selecionados após carregamento
        loadProducts().then(() => {
          selectedProducts.value = products.value.filter(p => ids.includes(p.id))
        })
      }
    }
  }

  const updateElementAttribute = (attribute, value) => {
    if (element) {
      element.setAttribute(attribute, value.toString())
    }
  }

  const applyStylesToElement = () => {
    if (!element) return

    const styles = gridStyles.value
    Object.entries(styles).forEach(([property, value]) => {
      element.style.setProperty(property, value)
    })
  }

  const toggleProduct = (product) => {
    const index = selectedProducts.value.findIndex(p => p.id === product.id)
    if (index > -1) {
      selectedProducts.value.splice(index, 1)
    } else {
      selectedProducts.value.push(product)
    }

    // Atualizar atributo do elemento
    const productIds = selectedProducts.value.map(p => p.id).join(',')
    updateElementAttribute('data-selected-products', productIds)
  }

  const isProductSelected = (productId) => {
    return selectedProducts.value.some(p => p.id === productId)
  }

  const formatCurrency = (value) => {
    if (typeof value !== 'number') value = parseFloat(value) || 0
    return new Intl.NumberFormat('pt-BR', { 
      style: 'currency', 
      currency: 'BRL' 
    }).format(value)
  }

  const refreshGrid = () => {
    // Trigger script re-injection for the product grid
    if (element && window.initAllProductGrids) {
      window.initAllProductGrids()
    }
  }

  // Watch for changes and update element
  watch([selectionMode, selectedCategory, sortBy, productLimit, columns], () => {
    refreshGrid()
  })

  watch(gridStyles, () => {
    applyStylesToElement()
    refreshGrid()
  }, { deep: true })

  // Initialize
  const initialize = async () => {
    initializeFromElement()
    await loadCategories()
    if (selectionMode.value === 'automatic' || selectedProducts.value.length === 0) {
      await loadProducts()
    }
  }

  return {
    // Estado
    isLoading,
    products,
    categories,
    selectedProducts,
    filteredProducts,
    
    // Configurações de seleção
    selectionMode,
    selectedCategory,
    sortBy,
    productLimit,
    columns,
    
    // Configurações de estilo
    gridGap,
    cardPadding,
    borderRadius,
    shadowType,
    backgroundColor,
    textColor,
    buttonColor,
    titleFontSize,
    priceFontSize,
    fontWeight,
    mobileColumns,
    tabletColumns,
    gridStyles,
    
    // Methods
    testStorefrontConnectivity,
    getStorefrontDomain,
    loadCategories,
    loadProducts,
    toggleProduct,
    isProductSelected,
    formatCurrency,
    updateElementAttribute,
    applyStylesToElement,
    refreshGrid,
    initialize
  }
}