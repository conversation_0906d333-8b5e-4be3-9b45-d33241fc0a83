export default {
  name: 'Checkout de Produto',
  type: 'product-checkout',
  dataComponent: 'product-checkout',
  elementType: 'product-checkout',
  className: 'product-checkout-container',
  category: 'Checkout',
  priority: 95,
  layoutTypes: ['product'],
  description: 'Componente para checkout de produto com seleção de variações, quantidade e botão de compra',
  icon: 'shopping_cart',
  
  selectors: [
    '[data-component="product-checkout"]',
    '.product-checkout-container',
    '.checkout-widget'
  ],
  
  html: `<div 
    data-component="product-checkout" 
    data-element-type="product-checkout"
    data-product-title="Tech T-Shirt®"
    data-product-rating="4.9"
    data-product-reviews="24712"
    data-original-price="R$ 189"
    data-current-price="R$ 139"
    data-discount-badge="26% OFF"
    data-selected-color="Preta"
    data-selected-size="M"
    data-button-text="ADICIONAR AO CARRINHO"
    class="product-checkout-container"
         style="width: 100%; margin: 0; padding: 2rem; background: #ffffff; border-radius: 0px; box-shadow: 0 4px 12px rgba(0,0,0,0.08); font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif; box-sizing: border-box;">
    
         <div class="product-layout" style="display: flex; gap: 3rem; align-items: flex-start; max-width: 1200px; margin: 0 auto;">
      <!-- Galeria de Imagens -->
      <div class="product-gallery" style="flex: 1; display: flex; flex-direction: column; gap: 1rem;">
        <div class="main-image" style="position: relative; border-radius: 12px; overflow: hidden; background: #f8fafc; aspect-ratio: 1;">
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iNTAwIiBoZWlnaHQ9IjUwMCIgdmlld0JveD0iMCAwIDUwMCA1MDAiIGZpbGw9Im5vbmUiIHhtbG5zPSJodHRwOi8vd3d3LnczLm9yZy8yMDAwL3N2ZyI+CjxyZWN0IHdpZHRoPSI1MDAiIGhlaWdodD0iNTAwIiBmaWxsPSIjMjU2M2ViIi8+Cjx0ZXh0IHg9IjI1MCIgeT0iMjUwIiBmaWxsPSJ3aGl0ZSIgZm9udC1zaXplPSIyNCIgZm9udC1mYW1pbHk9IkFyaWFsIiBkb21pbmFudC1iYXNlbGluZT0iY2VudHJhbCIgdGV4dC1hbmNob3I9Im1pZGRsZSI+VGVjaCBULVNoaXJ0PC90ZXh0Pgo8L3N2Zz4K" 
               alt="Tech T-Shirt" 
               class="product-main-image"
               style="width: 100%; height: 100%; object-fit: cover;">
        </div>
        <div class="thumbnail-gallery" style="display: flex; gap: 0.75rem; overflow-x: auto;">
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjMjU2M2ViIi8+Cjx0ZXh0IHg9IjQwIiB5PSI0MCIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjE8L3RleHQ+Cjwvc3ZnPgo=" alt="Imagem 1" class="thumbnail active" style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer; border: 2px solid #2563eb;">
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjMWYyOTM3Ii8+Cjx0ZXh0IHg9IjQwIiB5PSI0MCIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjI8L3RleHQ+Cjwvc3ZnPgo=" alt="Imagem 2" class="thumbnail" style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer; border: 2px solid transparent;">
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjMDU5NjY5Ii8+Cjx0ZXh0IHg9IjQwIiB5PSI0MCIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjM8L3RleHQ+Cjwvc3ZnPgo=" alt="Imagem 3" class="thumbnail" style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer; border: 2px solid transparent;">
          <img src="data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iODgiIGhlaWdodD0iODAiIHZpZXdCb3g9IjAgMCA4MCA4MCIgZmlsbD0ibm9uZSIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj4KPHJlY3Qgd2lkdGg9IjgwIiBoZWlnaHQ9IjgwIiBmaWxsPSIjZGMyNjI2Ii8+Cjx0ZXh0IHg9IjQwIiB5PSI0MCIgZmlsbD0id2hpdGUiIGZvbnQtc2l6ZT0iMTQiIGZvbnQtZmFtaWx5PSJBcmlhbCIgZG9taW5hbnQtYmFzZWxpbmU9ImNlbnRyYWwiIHRleHQtYW5jaG9yPSJtaWRkbGUiPjQ8L3RleHQ+Cjwvc3ZnPgo=" alt="Imagem 4" class="thumbnail" style="width: 80px; height: 80px; border-radius: 8px; object-fit: cover; cursor: pointer; border: 2px solid transparent;">
        </div>
      </div>

      <!-- Informações do Produto -->
      <div class="product-info" style="flex: 1; display: flex; flex-direction: column; gap: 1.5rem;">
        <!-- Cabeçalho do Produto -->
        <div class="product-header">
          <h1 class="product-title" style="font-size: 2rem; font-weight: 700; color: #111827; margin: 0 0 0.5rem 0; line-height: 1.2;">Tech T-Shirt®</h1>
          <div class="product-rating" style="display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem;">
            <div class="stars" style="display: flex; gap: 0.125rem;">
              <span class="star" style="color: #fbbf24; font-size: 1.25rem;">★</span>
              <span class="star" style="color: #fbbf24; font-size: 1.25rem;">★</span>
              <span class="star" style="color: #fbbf24; font-size: 1.25rem;">★</span>
              <span class="star" style="color: #fbbf24; font-size: 1.25rem;">★</span>
              <span class="star" style="color: #fbbf24; font-size: 1.25rem;">★</span>
            </div>
            <span class="rating-text" style="font-size: 0.875rem; color: #6b7280;">4.9 (24712 reviews)</span>
          </div>
          <div class="product-badges" style="display: flex; gap: 0.5rem; margin-bottom: 1rem;">
            <span class="badge discount" style="background: #ef4444; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 600;">26% OFF</span>
            <span class="badge bestseller" style="background: #059669; color: white; padding: 0.25rem 0.5rem; border-radius: 4px; font-size: 0.75rem; font-weight: 600;">BEST SELLER</span>
          </div>
        </div>

        <!-- Preços -->
        <div class="pricing-section">
          <div class="price-container" style="display: flex; align-items: center; gap: 1rem;">
            <span class="original-price" style="font-size: 1.125rem; color: #9ca3af; text-decoration: line-through;">R$ 189</span>
            <span class="current-price" style="font-size: 2rem; font-weight: 700; color: #111827;">R$ 139</span>
          </div>
        </div>

        <!-- Opções do Produto -->
        <div class="product-options" style="display: flex; flex-direction: column; gap: 1.25rem;">
          <!-- Seletor de Cor -->
          <div class="option-group">
            <label class="option-label" style="font-weight: 600; color: #374151; display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem;">
              Cor: <span class="selected-value" style="font-weight: 400; color: #6b7280;">Preta</span>
            </label>
            <div class="color-options" style="display: flex; gap: 0.75rem; flex-wrap: wrap;">
              <div class="color-option active" data-color="Preta" style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid #2563eb; background-color: #000000; transition: all 0.2s ease;" title="Preta"></div>
              <div class="color-option" data-color="Cinza" style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid transparent; background-color: #6b7280; transition: all 0.2s ease;" title="Cinza"></div>
              <div class="color-option" data-color="Bege" style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid transparent; background-color: #f3f4f6; transition: all 0.2s ease;" title="Bege"></div>
              <div class="color-option" data-color="Verde" style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid transparent; background-color: #059669; transition: all 0.2s ease;" title="Verde"></div>
              <div class="color-option" data-color="Azul" style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid transparent; background-color: #2563eb; transition: all 0.2s ease;" title="Azul"></div>
              <div class="color-option" data-color="Vinho" style="width: 40px; height: 40px; border-radius: 50%; cursor: pointer; border: 3px solid transparent; background-color: #991b1b; transition: all 0.2s ease;" title="Vinho"></div>
            </div>
          </div>

          <!-- Seletor de Tamanho -->
          <div class="option-group">
            <label class="option-label" style="font-weight: 600; color: #374151; display: flex; align-items: center; gap: 0.5rem; margin-bottom: 0.75rem;">
              Tamanho: <span class="size-guide" style="font-size: 0.875rem; color: #2563eb; cursor: pointer; text-decoration: underline; margin-left: auto;">📏 Descubra o seu tamanho</span>
            </label>
            <div class="size-options" style="display: flex; gap: 0.5rem; flex-wrap: wrap;">
              <div class="size-option" data-size="PP" style="padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem;">PP</div>
              <div class="size-option" data-size="P" style="padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem;">P</div>
              <div class="size-option active" data-size="M" style="padding: 0.75rem 1rem; border: 2px solid #2563eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem; background: #2563eb; color: white;">M</div>
              <div class="size-option" data-size="G" style="padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem;">G</div>
              <div class="size-option" data-size="GG" style="padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem;">GG</div>
              <div class="size-option" data-size="XGG" style="padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem;">XGG</div>
              <div class="size-option" data-size="XXGG" style="padding: 0.75rem 1rem; border: 2px solid #e5e7eb; border-radius: 6px; cursor: pointer; font-weight: 500; transition: all 0.2s ease; min-width: 60px; text-align: center; font-size: 0.875rem;">XXGG</div>
            </div>
          </div>

          <!-- Quantidade -->
          <div class="option-group">
            <label class="option-label" style="font-weight: 600; color: #374151; margin-bottom: 0.75rem; display: block;">Quantidade</label>
            <div class="quantity-options" style="display: flex; gap: 0.75rem;">
              <div class="quantity-option active" data-qty="1" style="padding: 1rem; border: 2px solid #2563eb; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; text-align: center; min-width: 100px; background: #2563eb; color: white;">
                <span class="qty-label" style="display: block; font-weight: 600; margin-bottom: 0.25rem;">1 un</span>
                <span class="qty-price" style="display: block; font-size: 0.875rem; opacity: 0.8;">R$ 139/un</span>
              </div>
              <div class="quantity-option" data-qty="3" style="padding: 1rem; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; text-align: center; min-width: 100px;">
                <span class="qty-label" style="display: block; font-weight: 600; margin-bottom: 0.25rem;">Kit 3</span>
                <span class="qty-price" style="display: block; font-size: 0.875rem; opacity: 0.8;">R$ 126/un</span>
              </div>
              <div class="quantity-option" data-qty="6" style="padding: 1rem; border: 2px solid #e5e7eb; border-radius: 8px; cursor: pointer; transition: all 0.2s ease; text-align: center; min-width: 100px;">
                <span class="qty-label" style="display: block; font-weight: 600; margin-bottom: 0.25rem;">Kit 6</span>
                <span class="qty-price" style="display: block; font-size: 0.875rem; opacity: 0.8;">R$ 114/un</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Botão de Compra -->
        <div class="purchase-section" style="display: flex; flex-direction: column; gap: 0.75rem;">
          <button class="add-to-cart-btn" style="background: #111827; color: white; border: none; padding: 1rem 2rem; border-radius: 8px; font-size: 1rem; font-weight: 600; cursor: pointer; display: flex; align-items: center; justify-content: center; gap: 0.5rem; transition: all 0.2s ease; text-transform: uppercase; letter-spacing: 0.5px;">
            <span class="btn-icon">🛒</span>
            ADICIONAR AO CARRINHO
          </button>
          <p class="shipping-info" style="text-align: center; font-size: 0.875rem; color: #2563eb; margin: 0;">Peça em estoque com envio imediato</p>
        </div>
      </div>
    </div>
    
         <!-- CSS para neutralizar margens do container -->
     <style>
       .product-checkout-container {
         margin: 0 !important;
         width: 100% !important;
         max-width: none !important;
         box-sizing: border-box !important;
       }
      
      /* Neutralizar padding do element-content apenas para product-checkout */
      .component-container:has(.product-checkout-container) .element-content {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      /* Fallback para navegadores sem :has() */
      .element-content:has(.product-checkout-container) {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      /* Hover effects */
      .color-option:hover,
      .size-option:hover,
      .quantity-option:hover {
        transform: scale(1.05);
      }
      
      .size-option:hover {
        border-color: #2563eb !important;
        background: #eff6ff !important;
      }
      
      .quantity-option:hover {
        border-color: #2563eb !important;
        background: #eff6ff !important;
      }
      
      .add-to-cart-btn:hover {
        background: #374151 !important;
        transform: translateY(-1px);
      }
      
      .thumbnail:hover {
        border-color: #2563eb !important;
      }
      
             /* Responsivo */
       @media (max-width: 1024px) {
         .product-layout {
           max-width: 100% !important;
           padding: 0 1rem !important;
         }
       }
       
       @media (max-width: 768px) {
         .product-layout {
           flex-direction: column !important;
           gap: 2rem !important;
           max-width: 100% !important;
           padding: 0 !important;
         }
         
         .product-checkout-container {
           padding: 1.5rem !important;
         }
        
        .product-title {
          font-size: 1.5rem !important;
        }
        
        .current-price {
          font-size: 1.5rem !important;
        }
        
        .color-option {
          width: 32px !important;
          height: 32px !important;
        }
        
        .size-option,
        .quantity-option {
          padding: 0.5rem 0.75rem !important;
          min-width: 50px !important;
        }
        
        .add-to-cart-btn {
          padding: 0.875rem 1.25rem !important;
          font-size: 0.875rem !important;
        }
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'product-checkout-config',
      title: 'Configurar Checkout',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <circle cx="9" cy="21" r="1"/>
        <circle cx="20" cy="21" r="1"/>
        <path d="m1 1 4 4 2.68 13.39a2 2 0 0 0 2 1.61h9.72a2 2 0 0 0 2-1.61L23 6H6"/>
      </svg>`,
      condition: (element) => {
        return element?.getAttribute('data-component') === 'product-checkout'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'product-checkout-config',
      component: 'ProductCheckoutSidebarEditor',
      path: '@/components/layoutEditor/Sidebar/editors/ProductCheckoutSidebarEditor.vue'
    }
  ],
  
  attributes: {
    preserve: [
      'data-component', 'data-element-type',
      'data-product-title', 'data-product-rating', 'data-product-reviews',
      'data-original-price', 'data-current-price', 'data-discount-badge',
      'data-selected-color', 'data-selected-size', 'data-button-text'
    ],
    defaults: {
      'data-component': 'product-checkout',
      'data-element-type': 'product-checkout',
      'data-product-title': 'Tech T-Shirt®',
      'data-product-rating': '4.9',
      'data-product-reviews': '24712',
      'data-original-price': 'R$ 189',
      'data-current-price': 'R$ 139',
      'data-discount-badge': '26% OFF',
      'data-selected-color': 'Preta',
      'data-selected-size': 'M',
      'data-button-text': 'ADICIONAR AO CARRINHO'
    }
  },
  
  initialization: {
    autoInit: true,
    reinitOnChange: true,
    scriptLoader: 'initializeProductCheckoutComponent'
  },
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true,
    resizable: false
  },
  
  cleanup: {
    removeEditingStyles: false,
    preserveStyles: [
      'width', 'max-width', 'margin', 'padding', 'background', 'border-radius', 
      'box-shadow', 'font-family', 'box-sizing', 'display', 'flex', 'gap', 
      'align-items', 'flex-direction', 'position', 'overflow', 'aspect-ratio',
      'object-fit', 'cursor', 'border', 'transition', 'font-size', 'font-weight',
      'color', 'line-height', 'text-align', 'text-decoration', 'opacity',
      'text-transform', 'letter-spacing', 'min-width'
    ],
    childSelectors: [
      '.product-layout', '.product-gallery', '.main-image', '.product-main-image',
      '.thumbnail-gallery', '.thumbnail', '.product-info', '.product-header',
      '.product-title', '.product-rating', '.stars', '.star', '.rating-text',
      '.product-badges', '.badge', '.pricing-section', '.price-container',
      '.original-price', '.current-price', '.product-options', '.option-group',
      '.option-label', '.selected-value', '.size-guide', '.color-options',
      '.color-option', '.size-options', '.size-option', '.quantity-options',
      '.quantity-option', '.qty-label', '.qty-price', '.purchase-section',
      '.add-to-cart-btn', '.btn-icon', '.shipping-info'
    ]
  },
  
  detection: {
    priority: 95
  },
  
} 