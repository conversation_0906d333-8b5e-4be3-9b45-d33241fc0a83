{"title": "Gerenciamento de Posts do Blog", "subtitle": "<PERSON><PERSON><PERSON><PERSON> todos os posts do seu blog", "listSubtitle": "<PERSON><PERSON><PERSON><PERSON> todos os posts do seu blog", "new": "Novo Post", "edit": "<PERSON>ar <PERSON>", "delete": "Excluir Post", "search": "Pesquisar posts...", "searchByTitle": "Pesquisar por título ou conteúdo", "noData": "Nenhum post encontrado", "loading": "Carregando posts...", "actions": "Ações", "itemsPerPage": "Posts por página", "totalItems": "Total de posts", "page": "<PERSON><PERSON><PERSON><PERSON>", "of": "de", "showing": "Mostrando", "to": "até", "results": "resultados", "titleView": "Posts do Blog", "form": {"title": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Digite o título do post", "titleRequired": "Título é obrigatório", "titleMaxLength": "<PERSON><PERSON><PERSON><PERSON> deve ter no máximo 255 caracteres", "slug": "Slug (URL)", "slugPlaceholder": "Digite o slug do post", "slugRequired": "Slug é obrigatório", "slugMaxLength": "Slug deve ter no máximo 255 caracteres", "slugHelp": "URL amigável para o post. Deixe em branco para gerar automaticamente.", "category": "Categoria", "categoryPlaceholder": "Selecione uma categoria", "categoryOptional": "Categoria é opcional", "content": "<PERSON><PERSON><PERSON><PERSON>", "contentPlaceholder": "Comece a digitar…", "contentRequired": "Conteúdo é obrigatório", "excerpt": "Resumo", "excerptPlaceholder": "Digite um resumo do post...", "excerptHelp": "Texto curto que aparece na listagem de posts", "excerptMaxLength": "Resumo deve ter no máximo 500 caracteres", "featuredImage": "Imagem em Destaque", "featuredImagePlaceholder": "URL da imagem em destaque", "featuredImageHelp": "URL da imagem principal do post", "published": "Publicado", "publishedHelp": "<PERSON><PERSON> para publicar o post no blog", "featured": "Post em Destaque", "featuredHelp": "Marque para destacar este post", "publishedAt": "Data de Publicação", "publishedAtHelp": "Data e hora em que o post foi publicado", "viewCount": "Visualizações", "viewCountHelp": "Número de visualizações do post", "basicData": "Dados Básicos", "basicDataDescription": "Informações principais do post", "seoConfig": "Configuração de SEO", "seoConfigDescription": "Otimização para mecanismos de busca", "metaTitle": "<PERSON><PERSON>", "metaTitlePlaceholder": "Digite o meta título", "metaTitleHelp": "Título que aparece nos resultados de busca (recomendado: 50-60 caracteres)", "metaTitleMaxLength": "Meta título deve ter no máximo 255 caracteres", "metaDescription": "Meta Descrição", "metaDescriptionPlaceholder": "Digite a meta descrição", "metaDescriptionHelp": "Descrição que aparece nos resultados de busca (recomendado: 150-160 caracteres)", "metaDescriptionMaxLength": "Meta descrição deve ter no máximo 500 caracteres", "googlePreview": "Prévia no Google", "googlePreviewTitle": "Como seu post aparecerá no Google", "googlePreviewSubtitle": "Veja como seu post aparecerá nos resultados de busca", "googlePreviewUrl": "seusite.com/blog/slug-do-post", "googlePreviewDefaultTitle": "Título do Post - Seu Site", "googlePreviewDefaultDescription": "Descrição do post que aparecerá nos resultados de busca do Google...", "imageUpload": "Upload de Imagem", "imageUploadDescription": "Imagem principal do post", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "saving": "Salvando...", "saved": "Post salvo com sucesso!", "backToPosts": "Voltar para Posts", "selectImage": "Selecionar Imagem", "changeImage": "Alterar Imagem", "removeImage": "Remover Imagem", "imageFormat": "PNG, JPG até 2MB", "settingsTitle": "Configurações do Post", "settingsSubtitle": "<PERSON>fina as configurações de publicação", "characters": "caracteres"}, "table": {"image": "Imagem", "title": "<PERSON><PERSON><PERSON><PERSON>", "category": "Categoria", "status": "Status", "featured": "Destaque", "views": "Visualizações", "publishedAt": "Publicado em", "createdAt": "C<PERSON><PERSON> em", "updatedAt": "Atualizado em", "actions": "Ações"}, "status": {"published": "Publicado", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "featured": "Em Destaque", "normal": "Normal"}, "filters": {"all": "Todos", "published": "Publicados", "draft": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "featured": "Em Destaque", "normal": "Normal", "category": "Categoria", "status": "Status"}, "messages": {"createSuccess": "Post criado com sucesso!", "updateSuccess": "Post atualizado com sucesso!", "deleteSuccess": "Post excluído com sucesso!", "deleteConfirm": "Tem certeza que deseja excluir o post \"{title}\"?", "deleteConfirmTitle": "Excluir Post", "loadError": "Erro ao carregar posts", "saveError": "Erro ao salvar post", "deleteError": "Erro ao excluir post", "slugExists": "Este slug já está em uso", "invalidData": "<PERSON><PERSON>", "required": "Campo obrigatório", "maxLength": "Excede o tamanho máximo", "titleRequired": "Título é obrigatório", "contentRequired": "Conteúdo é obrigatório", "loadCategoriesError": "Erro ao carregar categorias", "loadPostError": "Erro ao carregar post", "imageUploadSuccess": "Imagem enviada com sucesso!", "imageUploadError": "Post salvo, mas erro no upload da imagem", "metaTitleMaxChars": "Meta título deve ter no máximo 60 caracteres", "metaDescriptionMaxChars": "Meta descrição deve ter no máximo 160 caracteres", "successTitle": "Sucesso", "errorTitle": "Erro", "warningTitle": "Aviso"}, "placeholders": {"noImage": "Sem imagem", "noCategory": "Sem categoria", "noContent": "<PERSON><PERSON> conte<PERSON>do", "noExcerpt": "Sem resumo"}, "stats": {"total": "Total de Posts", "published": "Posts Publicados", "drafts": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "featured": "Posts em Destaque", "categories": "Categorias", "views": "Total de Visualizações"}, "blogDashboard": {"title": "Gerenciamento do Blog", "subtitle": "<PERSON><PERSON><PERSON><PERSON> posts e categorias do seu blog", "posts": {"title": "Posts do Blog", "description": "<PERSON><PERSON><PERSON><PERSON> todos os posts do seu blog", "viewAll": "Ver todos os posts", "createNew": "Criar novo post"}, "categories": {"title": "Categorias do Blog", "description": "Organize posts em categorias", "viewAll": "<PERSON><PERSON> <PERSON><PERSON> as categorias", "createNew": "Criar nova categoria"}}}