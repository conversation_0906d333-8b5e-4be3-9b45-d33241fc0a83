import axios from 'axios';
import API_URLS from '@/config/api.config';

class CepService {
    #endpointOriginCep;

    constructor() {
        this.#endpointOriginCep = `${API_URLS.CEP_BASE_URL}`;
    }

    async getOriginCep() {
        try {
            const response = await axios.get(`${this.#endpointOriginCep}`);
            return response.data;
        } catch (error) {
            console.error('Error fetching origin cep:', error);
            throw error;
        }
    }

    async createOriginCep(originCepData) {
        try {
            const response = await axios.post(`${this.#endpointOriginCep}`, originCepData);
            return response.data;
        } catch (error) {
            console.error('Error creating origin cep:', error);
            throw error;
        }
    }

    async updateOriginCep(originCepData) {
        try {
            const response = await axios.put(`${this.#endpointOriginCep}`, originCepData);
            return response.data;
        } catch (error) {
            console.error('Error updating origin cep:', error);
            throw error;
        }
    }

}

export default new CepService();