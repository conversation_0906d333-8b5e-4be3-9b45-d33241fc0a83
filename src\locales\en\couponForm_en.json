{"categoriesTitle": "Categories", "editTitle": "Edit Coupon", "editSubtitle": "Edit discount coupon information", "newTitle": "New Coupon", "newSubtitle": "Register and edit discount coupon information", "percentageValue": "Enter a value between 0 and 100 (without the % symbol)", "fixedValue": "Enter the discount value in reais (without the R$ symbol)", "backToList": "Back to List", "codeLabel": "Coupon Code", "codePlaceholder": "Ex: EXAMPLE123", "generateCode": "Generate Code", "descriptionLabel": "Description", "descriptionPlaceholder": "Ex: Coupon for summer discount", "discountTypeLabel": "Discount Type", "discountTypePercentage": "Percentage (%)", "discountTypeFixedAmount": "Fixed Amount (R$)", "discountValueLabel": "Discount Value", "discountValuePlaceholder": "Ex: 10.00", "usageLimitLabel": "Total Usage Limit", "usageLimitPlaceholder": "Ex: 100", "usageLimitPerCustomerLabel": "Usage Limit per Customer", "usageLimitPerCustomerPlaceholder": "Ex: 1", "startsAtLabel": "Start Date", "endsAtLabel": "End Date", "minimumValueLabel": "Minimum Purchase Value", "minimumValuePlaceholder": "Ex: 50.00", "minimumQuantityLabel": "Minimum Item Quantity", "minimumQuantityPlaceholder": "Ex: 2", "activeLabel": "Active Coupon", "promotionalCumulativeLabel": "Cumulative with Promotions", "usageByCustomerTitle": "Usage by Customer", "usageByCustomerDescription": "Here you can define specific usage limits for individual customers.", "customerIdPlaceholder": "Customer ID", "addCustomer": "Add Customer", "cancel": "Cancel", "save": "Save", "errorTitle": "Error", "errorLoadCoupon": "Could not load the coupon", "errorRequiredFields": "Fill in all required fields", "errorSaveCoupon": "Could not save the coupon", "errorCustomerRequired": "Select a customer for each usage record", "successTitle": "Success", "successUpdateCoupon": "Coupon updated successfully", "successCreateCoupon": "Coupon created successfully", "categoryIdsLabel": "Categories", "categoryIdsPlaceholder": "Select categories", "categoryIdsHelp": "Select the categories to which this coupon applies. If no categories are selected, the coupon will be valid for all categories.", "errorLoadingCategories": "Could not load categories", "customerRecord": "Customer", "selectCustomer": "Select Customer", "searchCustomerPlaceholder": "Search by name or email", "selectCustomerOption": "Select a customer", "customerName": "Name", "customerEmail": "Email", "usageCountLabel": "Usage Count", "noUsageRecords": "No usage records found", "addUsageRecord": "Add Customer", "removeCustomer": "Remove Customer", "percentagePlaceholder": "Ex: 100", "fixedPlaceholder": "Ex: 100.00", "saveFirstToManageUsage": "Save the coupon first to manage usage by customer", "searchCustomer": "Search", "usageLimitCustomer": "Usage Limit", "loading": "Loading...", "basicDataTitle": "Basic Coupon Data", "basicDataSubtitle": "Main information about the discount coupon", "limitsConditionsTitle": "Limits and Conditions", "limitsConditionsSubtitle": "Configure usage limits and application conditions", "validityPeriodTitle": "Validity Period", "validityPeriodSubtitle": "Define when the coupon will be valid", "categoriesSubtitle": "Categories where the coupon will apply"}