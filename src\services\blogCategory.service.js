import axios from 'axios';
import { API_URLS } from '@/config/api.config';

export const BlogCategoryService = {
  async listBlogCategories(filter = '', page = 0, size = 10) {
    try {
      const response = await axios.get(`${API_URLS.BLOG_CATEGORY_BASE_URL}`, {
        params: { filter, page, size },
      });
      
      return {
        content: response.data.content || [],
        totalPages: response.data.totalPages || 0,
        totalElements: response.data.totalElements || 0
      };
    } catch (error) {
      console.error('Erro ao listar categorias do blog:', error);
      throw error;
    }
  },

  async getBlogCategory(categoryId) {
    try {
      const response = await axios.get(`${API_URLS.BLOG_CATEGORY_BASE_URL}/${categoryId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter categoria do blog:', error);
      throw error;
    }
  },

  async createBlogCategory(categoryData) {
    try {
      const response = await axios.post(`${API_URLS.BLOG_CATEGORY_BASE_URL}`, categoryData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar categoria do blog:', error);
      throw error;
    }
  },

  async updateBlogCategory(categoryId, categoryData) {
    try {
      const response = await axios.put(`${API_URLS.BLOG_CATEGORY_BASE_URL}/${categoryId}`, categoryData);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar categoria do blog:', error);
      throw error;
    }
  },

  async deleteBlogCategory(categoryId) {
    try {
      const response = await axios.delete(`${API_URLS.BLOG_CATEGORY_BASE_URL}/${categoryId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir categoria do blog:', error);
      throw error;
    }
  },

  generateSlugFromName(name) {
    if (!name) return '';
    return name
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .trim()
      .replace(/\s+/g, '-');
  },

  // Image upload methods
  async uploadImage(categoryId, file) {
    try {
      if (!file?.type?.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem válida');
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await axios.post(`${API_URLS.BLOG_CATEGORY_BASE_URL}/${categoryId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      
      return response.data;
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      throw error;
    }
  },

  async deleteImage(categoryId) {
    try {
      const response = await axios.delete(`${API_URLS.BLOG_CATEGORY_BASE_URL}/${categoryId}/images`);
      return response.data;
    } catch (error) {
      console.error('Erro ao deletar imagem:', error);
      throw error;
    }
  },

  getImageUrl(categoryId) {
    return `${API_URLS.BLOG_CATEGORY_BASE_URL}/${categoryId}/images`;
  },

  async reorderCategories(categoryIds) {
    try {
      const response = await axios.put(`${API_URLS.BLOG_CATEGORY_BASE_URL}/reorder`, categoryIds);
      return response.data;
    } catch (error) {
      console.error('Erro ao reordenar categorias:', error);
      throw error;
    }
  }
};

export default BlogCategoryService; 