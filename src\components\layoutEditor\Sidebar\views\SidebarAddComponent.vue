<template>
  <div class="sidebar-add-component">
    <!-- Seção: Busca -->
    <div class="search-section">
      <div class="search-input-wrapper">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <input 
          v-model="searchQuery"
          type="text"
          class="search-input"
          placeholder="Buscar componentes..."
        />
        <button 
          v-if="searchQuery"
          @click="clearSearch"
          class="clear-search-btn"
        >
          <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <line x1="18" y1="6" x2="6" y2="18"/>
            <line x1="6" y1="6" x2="18" y2="18"/>
          </svg>
        </button>
      </div>
    </div>

    <!-- Seção: Componentes por categoria -->
    <div class="components-section">
      <div 
        v-for="(category, categoryId) in filteredCategories" 
        :key="categoryId"
        class="category-group"
      >
        <!-- Header da categoria -->
        <button 
          class="category-header"
          @click="toggleCategory(categoryId)"
          :aria-expanded="isExpanded(categoryId)"
        >
          <div class="category-info">
            <div class="category-icon" :style="{ color: getCategoryColor(categoryId) }">
              <component :is="getCategoryIcon(categoryId)" />
            </div>
            <span class="category-title">{{ category.title }}</span>
            <span class="category-count">({{ Object.keys(category.components).length }})</span>
          </div>
          <div class="expand-icon" :class="{ expanded: isExpanded(categoryId) }">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M6 9L12 15L18 9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </button>

        <!-- Componentes da categoria -->
        <Transition name="category-expand">
          <div 
            v-if="isExpanded(categoryId)"
            class="category-content"
          >
            <div 
              v-for="(component, componentId) in category.components" 
              :key="componentId"
              class="component-item"
              @click="selectComponent(componentId, component)"
              :title="component.description"
            >
              <div class="component-icon">
                <component :is="getComponentIcon(componentId)" />
              </div>
              <div class="component-info">
                <div class="component-title">{{ component.title }}</div>
                <div class="component-description">{{ component.description }}</div>
              </div>
              <div class="add-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="12" y1="5" x2="12" y2="19" stroke-width="2"/>
                  <line x1="5" y1="12" x2="19" y2="12" stroke-width="2"/>
                </svg>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>

    <!-- Estado vazio (busca sem resultados) -->
    <div v-if="hasSearchQuery && Object.keys(filteredCategories).length === 0" class="empty-search">
      <div class="empty-icon">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      </div>
      <h4>Nenhum componente encontrado</h4>
      <p>Tente buscar por outro termo ou navegue pelas categorias</p>
      <button @click="clearSearch" class="clear-search-button">
        Limpar busca
      </button>
    </div>

    <!-- Estado de carregamento -->
    <div v-if="!hasContent && !hasSearchQuery" class="loading-state">
      <div class="loading-spinner"></div>
      <p>Carregando componentes...</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted } from 'vue'
import { useComponentLibrary } from '@/views/layoutEditor/composables/useComponentLibrary'
import { useI18n } from 'vue-i18n'
import {
  TextIcon,
  Image01Icon,
  ShoppingBag01Icon,
  Touch02Icon,
  GridViewIcon,
  Add01Icon,
  Layout01Icon,
  PlayCircleIcon,
  StarIcon,
  LocationIcon,
  BuildingIcon
} from '@hugeicons-pro/core-bulk-rounded'

const { t } = useI18n()
const emit = defineEmits(['component-select', 'back'])

// Props
const props = defineProps({
  layoutType: {
    type: String,
    default: null
  }
})

// Composables
const { componentCategories, initializeIfNeeded, setLayoutType } = useComponentLibrary(props.layoutType)

// Estado interno
const searchQuery = ref('')
const expandedState = ref({})

// Inicializa a biblioteca
initializeIfNeeded()

// ✅ NOVO: Observar mudanças no tipo de layout
watch(() => props.layoutType, (newLayoutType) => {
  setLayoutType(newLayoutType)
}, { immediate: true })

// Filtrar categorias baseado na busca
const filteredCategories = computed(() => {
  const categories = componentCategories.value
  if (!categories || Object.keys(categories).length === 0) {
    return {}
  }

  if (!searchQuery.value.trim()) {
    // Sem busca, retornar todas as categorias que têm componentes
    const filtered = {}
    Object.entries(categories).forEach(([categoryId, category]) => {
      if (category.components && Object.keys(category.components).length > 0) {
        filtered[categoryId] = category
      }
    })
    return filtered
  }

  // Com busca, filtrar componentes por nome/descrição
  const query = searchQuery.value.toLowerCase()
  const filtered = {}

  Object.entries(categories).forEach(([categoryId, category]) => {
    if (!category.components) return

    const matchingComponents = {}
    Object.entries(category.components).forEach(([componentId, component]) => {
      const titleMatch = component.title.toLowerCase().includes(query)
      const descriptionMatch = component.description?.toLowerCase().includes(query)
      const idMatch = componentId.toLowerCase().includes(query)

      if (titleMatch || descriptionMatch || idMatch) {
        matchingComponents[componentId] = component
      }
    })

    if (Object.keys(matchingComponents).length > 0) {
      filtered[categoryId] = {
        ...category,
        components: matchingComponents
      }
    }
  })

  return filtered
})

// Estados computados
const hasSearchQuery = computed(() => searchQuery.value.trim().length > 0)
const hasContent = computed(() => Object.keys(filteredCategories.value).length > 0)

// Inicializa a primeira categoria como expandida quando não há busca
watch(() => [componentCategories.value, searchQuery.value], ([newCategories, newQuery]) => {
  if (newCategories && Object.keys(newCategories).length > 0) {
    if (newQuery.trim()) {
      // Com busca, expandir todas as categorias que têm resultados
      const newState = {}
      Object.keys(filteredCategories.value).forEach(key => {
        newState[key] = true
      })
      expandedState.value = newState
    } else {
      // Sem busca, apenas a primeira categoria expandida
      const categoryKeys = Object.keys(newCategories)
      const newState = {}
      categoryKeys.forEach((key, index) => {
        newState[key] = index === 0
      })
      expandedState.value = newState
    }
  }
}, { immediate: true })

// Controle de expansão
const toggleCategory = (categoryId) => {
  if (hasSearchQuery.value) {
    // Com busca, apenas toggle individual
    expandedState.value[categoryId] = !expandedState.value[categoryId]
  } else {
    // Sem busca, comportamento accordion (apenas uma aberta)
    const currentState = expandedState.value[categoryId]
    
    if (currentState) {
      expandedState.value[categoryId] = false
    } else {
      const newState = {}
      Object.keys(expandedState.value).forEach(key => {
        newState[key] = key === categoryId
      })
      expandedState.value = newState
    }
  }
}

const isExpanded = (categoryId) => {
  return expandedState.value[categoryId] || false
}

// Busca
const clearSearch = () => {
  searchQuery.value = ''
}

// Seleção de componente
const selectComponent = (componentId, component) => {
  
  emit('component-select', {
    type: componentId,
    data: component
  })
}

// Ícones das categorias
const getCategoryIcon = (categoryId) => {
  const icons = {
    text: TextIcon,
    media: Image01Icon,
    products: ShoppingBag01Icon,
    ecommerce: ShoppingBag01Icon,
    interactive: Touch02Icon,
    layout: GridViewIcon,
    content: Layout01Icon,
    custom: Add01Icon,
    marketing: StarIcon,
    testimonials: LocationIcon
  }
  return icons[categoryId] || Layout01Icon
}

// Ícones dos componentes
const getComponentIcon = (componentId) => {
  const icons = {
    'video': PlayCircleIcon,
    'carousel': Image01Icon,
    'header': Layout01Icon,
    'footer': Layout01Icon,
    'dynamic-grid-produtos': ShoppingBag01Icon,
    'product-grid': ShoppingBag01Icon,
            'product-checkout': ShoppingBag01Icon, // ✅ Ícone para componente de checkout
    'company-information': BuildingIcon,
    'location': LocationIcon,
    'statement': TextIcon,
    'payment-benefits': StarIcon,
    'customer-review': StarIcon,
    'special-offers': StarIcon
  }
  return icons[componentId] || Layout01Icon
}

// Cores das categorias
const getCategoryColor = (categoryId) => {
  const colors = {
    text: '#3b82f6',
    media: '#10b981',
    products: '#f59e0b',
    ecommerce: '#f59e0b',
    interactive: '#8b5cf6',
    content: '#0ea5e9',
    layout: '#64748b',
    marketing: '#ef4444',
    testimonials: '#84cc16'
  }
  return colors[categoryId] || '#64748b'
}

// Lifecycle
onMounted(() => {
  
})
</script>

<style scoped>
.sidebar-add-component {
  height: 100%;
  overflow-y: auto;
  background: var(--iluria-color-surface);
}

.sidebar-add-component::-webkit-scrollbar {
  width: 6px;
}

.sidebar-add-component::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.sidebar-add-component::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

/* Seção de busca */
.search-section {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  color: var(--iluria-color-text-secondary);
  pointer-events: none;
  z-index: 1;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.25rem;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary-border);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
}

.search-input::placeholder {
  color: var(--iluria-color-text-secondary);
}

.clear-search-btn {
  position: absolute;
  right: 0.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  background: transparent;
  border: none;
  border-radius: 4px;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-search-btn:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

/* Seção de componentes */
.components-section {
  flex: 1;
}

.category-group {
  border-bottom: 1px solid var(--iluria-color-border);
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.875rem 1rem;
  background: var(--iluria-color-container-bg);
  border: none;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.category-header:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.category-title {
  font-weight: 600;
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
}

.category-count {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  opacity: 0.7;
}

.expand-icon {
  width: 16px;
  height: 16px;
  color: var(--iluria-color-text-secondary);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  flex-shrink: 0;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Conteúdo da categoria */
.category-content {
  background: var(--iluria-color-background);
  overflow: hidden;
}

.component-item {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: pointer;
  transition: all 0.2s ease;
}

.component-item:hover {
  background: var(--iluria-color-hover);
}

.component-item:last-child {
  border-bottom: none;
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-primary);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.component-item:hover .component-icon {
  background: var(--iluria-color-primary-bg);
  border-color: var(--iluria-color-primary-border);
  color: var(--iluria-color-primary);
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
  margin-bottom: 0.25rem;
}

.component-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-icon {
  width: 16px;
  height: 16px;
  color: var(--iluria-color-text-secondary);
  flex-shrink: 0;
  opacity: 0.7;
  transition: all 0.2s ease;
}

.component-item:hover .add-icon {
  opacity: 1;
  color: var(--iluria-color-primary);
}

/* Animações */
.category-expand-enter-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

.category-expand-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
}

.category-expand-enter-from,
.category-expand-leave-to {
  opacity: 0;
  max-height: 0;
  transform: translateY(-8px);
}

.category-expand-enter-to,
.category-expand-leave-from {
  opacity: 1;
  max-height: 800px;
  transform: translateY(0);
}

/* Estados especiais */
.empty-search,
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 2rem;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-search h4 {
  margin: 0 0 0.5rem 0;
  color: var(--iluria-color-text-primary);
}

.empty-search p {
  margin: 0 0 1.5rem 0;
  font-size: 0.875rem;
}

.clear-search-button {
  padding: 0.5rem 1rem;
  background: var(--iluria-color-primary);
  color: white;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  cursor: pointer;
  transition: background 0.2s ease;
}

.clear-search-button:hover {
  background: var(--iluria-color-primary-hover);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 1rem;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsividade */
@media (max-width: 768px) {
  .search-section {
    padding: 0.75rem;
  }
  
  .search-input {
    padding: 0.625rem 0.625rem 0.625rem 2rem;
    font-size: 0.8rem;
  }
  
  .category-header {
    padding: 0.75rem;
  }
  
  .category-title {
    font-size: 0.8rem;
  }
  
  .category-count {
    font-size: 0.7rem;
  }
  
  .component-item {
    padding: 0.875rem 0.75rem;
  }
  
  .component-icon {
    width: 32px;
    height: 32px;
  }
  
  .component-title {
    font-size: 0.8rem;
  }
  
  .component-description {
    font-size: 0.7rem;
  }
  
  .empty-search,
  .loading-state {
    padding: 2rem 1.5rem;
  }
}
</style> 
