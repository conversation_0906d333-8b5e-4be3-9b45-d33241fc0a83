
<template>
  <div class="customer-list-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('customer.customersTitle')"
      :subtitle="t('customer.customersSubtitle')"
      :showAdd="true"
      :addText="t('customer.newCustomer')"
      :customButtons="headerCustomButtons"
      @add-click="goToNewCustomer"
      @custom-click="handleCustomButtonClick"
    />

    <!-- Table Wrapper -->
    <div class="table-wrapper">
      <IluriaDataTable :value="sortedCustomers" :columns="tableColumns" :loading="loading" dataKey="id"
        class="customers-table">
        <template #header-checkbox>
          <span class="column-header checkbox-header">
          </span>
        </template>
        <template #header-image>
          <span class="column-header">
            {{ t('customer.imageHeader') }}
          </span>
        </template>
        <template #header-name>
          <span class="column-header" data-sortable="true" @click="toggleSort('name')">
            {{ t('customer.nameHeader') }}
            <span v-if="sortField === 'name'">
              {{ sortOrder === 1 ? '▲' : sortOrder === -1 ? '▼' : '' }}
            </span>
          </span>
        </template>
        <template #header-email>
          <span class="column-header">
            {{ t('customer.emailHeader') }}
          </span>
        </template>
        <template #header-phone>
          <span class="column-header">{{ t('customer.phoneHeader') }}</span>
        </template>
        <template #header-creationDate>
          <span class="column-header" data-sortable="true" @click="toggleSort('creationDate')">
            {{ t('customer.createdAt') }}
            <span v-if="sortField === 'creationDate'">
              {{ sortOrder === 1 ? '▲' : sortOrder === -1 ? '▼' : '' }}
            </span>
          </span>
        </template>
        <template #header-actions>
          <span class="column-header">{{ t('customer.actionsHeader') }}</span>
        </template>
        <template #column-checkbox="{ data }">
          <div class="checkbox-cell">
            <IluriaCheckbox
              :model-value="isCustomerSelected(data.id)"
              :label="''"
              :disabled="isBulkDeleteLoading"
              :aria-describedBy="t('customer.bulk.selectCustomer') + ' ' + data.name"
              class="customer-selection-checkbox"
              @update:model-value="toggleCustomerSelection(data.id)"
            />
          </div>
        </template>
        <template #column-image="{ data }">
          <div class="customer-avatar clickable" @click="editCustomer(data.id)">
            <img 
              :src="data.photoUrl || generateCustomerAvatar(data.name)" 
              :alt="data.name" 
              class="avatar-image"
              @error="handleImageError($event, data)"
            />
          </div>
        </template>
        <template #column-name="{ data }">
          <div class="customer-name clickable" @click="editCustomer(data.id)">
            <span class="name-text">{{ data.name }}</span>
            <span v-if="data.document" class="document-text">{{ formatDocument(data.document) }}</span>
          </div>
        </template>
        <template #column-email="{ data }">
          <a :href="`mailto:${data.email}`" class="email-link">
            {{ data.email }}
          </a>
        </template>
        <template #column-phone="{ data }">
          <a v-if="data.phone" :href="`tel:${data.phone}`" class="phone-link">
            {{ formatPhone(data.phone) }}
          </a>
          <span v-else class="no-phone">{{ t('customer.noPhone') }}</span>
        </template>
        <template #column-creationDate="{ data }">
          <span class="created-at-text">{{ formatDate(data.creationDate) }}</span>
        </template>
        <template #column-actions="{ data }">
          <div class="action-buttons">
            <IluriaButton color="text-primary" size="small" :hugeIcon="Edit03Icon" @click="editCustomer(data.id)"
              :title="t('customer.editCustomer')" />
            <IluriaButton color="text-danger" size="small" :hugeIcon="Delete02Icon" @click="confirmDelete(data)"
              :title="t('customer.deleteCustomer')" />
          </div>
        </template>
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <HugeiconsIcon :icon="UserGroup02Icon" :size="48" class="text-gray-400" />
            </div>
            <h3 class="empty-title">{{ t('customer.noCustomers') }}</h3>
            <p class="empty-description">{{ t('customer.noCustomersDescription') }}</p>
            <IluriaButton color="dark" :hugeIcon="UserAddIcon" @click="goToAddCustomer" class="mt-4">
              {{ t('customer.createFirstCustomer') }}
            </IluriaButton>
          </div>
        </template>
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('customer.loadingCustomers') }}</span>
          </div>
        </template>
      </IluriaDataTable>
    </div>

    <!-- Bulk Action Bar -->
    <IluriaBulkActionBar
      :selected-count="selectedCustomers.size"
      :actions="bulkActions"
      :loading="isBulkDeleteLoading"
      :disabled="loading"
      entity-name="cliente"
      entity-name-plural="clientes"
      @action-click="handleBulkActionClick"
    />

    <!-- Pagination -->
    <div class="pagination-container" v-if="totalPages > 1">
      <IluriaPagination
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />
    </div>

    <!-- Confirmation Modal -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from 'primevue/usetoast'
import CustomerService from '@/services/customer.service'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaBulkActionBar from '@/components/iluria/IluriaBulkActionBar.vue'
import IluriaCheckbox from '@/components/iluria/form/IluriaCheckbox.vue'
import {
  Delete02Icon,
  Edit03Icon,
  UserAddIcon,
  UserGroup02Icon,
  Download01Icon,
  Upload01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { generateCustomerAvatar } from '@/utils/avatarUtils'
import { HugeiconsIcon } from '@hugeicons/vue'

const router = useRouter()
const { t } = useI18n()
const toast = useToast()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

const customers = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)
const filters = ref({ filter: '' })
const sortField = ref(null)
const sortOrder = ref(null)

// Bulk selection state
const selectedCustomers = ref(new Set())
const isBulkDeleteLoading = ref(false)

// Removido as variáveis antigas do modal que não estão sendo usadas

const loadCustomers = async () => {
  loading.value = true
  try {
    const response = await CustomerService.getCustomers(filters.value.filter, currentPage.value, 10)
    customers.value = response.content
    totalPages.value = response.totalPages || 0
    
    // Clear selections when loading new page data
    clearSelection()
  } catch (error) {
    console.error('Error loading customers:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('customer.loadError'),
      life: 3000
    })
  } finally {
    loading.value = false
  }
}

const changePage = (page) => {
  currentPage.value = page
  loadCustomers()
}

const editCustomer = (id) => {
  router.push(`/customers/${id}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const confirmDelete = (customer) => {
  showConfirmDanger(
    t('customer.confirmDeleteMessage', { name: customer.name }),
    t('customer.confirmDeleteTitle'),
    () => deleteCustomer(customer.id)
  )
}

const deleteCustomer = async (id) => {
  try {
    await CustomerService.deleteCustomer(id)
    toast.add({
      severity: 'success',
      summary: t('success'),
      detail: t('customer.deleteSuccess'),
      life: 3000
    })
    loadCustomers()
  } catch (error) {
    console.error('Error deleting customer:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('customer.deleteError'),
      life: 3000
    })
  }
}

const goToAddCustomer = () => {
  router.push('/customers/new')
}

const goToImportCustomers = () => {
  router.push('/customers/import')
}



const goToExportCustomers = () => {
  router.push('/customers/export')
}

const goToNewCustomer = () => {
  goToAddCustomer()
}

// Custom buttons for header
const headerCustomButtons = ref([
  {
    text: t('customer.list.importButton'),
    icon: Download01Icon,
    color: 'secondary',
    variant: 'outline',
    action: 'import'
  },
  {
    text: t('customer.list.exportButton'),
    icon: Upload01Icon,
    color: 'secondary',
    variant: 'outline',
    action: 'export'
  }
])

// Handle custom button clicks
const handleCustomButtonClick = (index, button) => {
  if (button.action === 'import') {
    goToImportCustomers()
  } else if (button.action === 'export') {
    goToExportCustomers()
  }
}

const formatDocument = (document) => {
  if (!document) return ''
  const cleaned = document.replace(/\D/g, '')
  if (cleaned.length === 11) {
    return cleaned.replace(/(\d{3})(\d{3})(\d{3})(\d{2})/, '$1.$2.$3-$4')
  } else if (cleaned.length === 14) {
    return cleaned.replace(/(\d{2})(\d{3})(\d{3})(\d{4})(\d{2})/, '$1.$2.$3/$4-$5')
  }
  return document
}

const formatPhone = (phone) => {
  if (!phone) return ''
  const cleaned = phone.replace(/\D/g, '')
  if (cleaned.length === 10) {
    return cleaned.replace(/(\d{2})(\d{4})(\d{4})/, '($1) $2-$3')
  } else if (cleaned.length === 11) {
    return cleaned.replace(/(\d{2})(\d{5})(\d{4})/, '($1) $2-$3')
  }
  return phone
}

const formatDate = (date) => {
  if (!date) return '';
  return date.replace(/-/g, '/');
};

const parseDate = (d) => {
  if (!d) return 0

  // Esperado: dd-MM-yyyy ou dd/MM/yyyy
  const normalized = d.replace(/\//g, '-')
  const [day, month, year] = normalized.split('-').map(Number)

  if (!isNaN(day) && !isNaN(month) && !isNaN(year)) {
    return new Date(year, month - 1, day).getTime()
  }

  return 0
}

let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadCustomers()
  }, 400)
}

const toggleSort = (field) => {
  if (sortField.value !== field) {
    sortField.value = field
    sortOrder.value = 1
  } else if (sortOrder.value === 1) {
    sortOrder.value = -1
  } else if (sortOrder.value === -1) {
    sortField.value = null
    sortOrder.value = null
  } else {
    sortOrder.value = 1
  }
}

const sortedCustomers = computed(() => {
  if (!sortField.value || !sortOrder.value) return customers.value
  const sorted = [...customers.value]
  if (sortField.value === 'name') {
    sorted.sort((a, b) => {
      if (!a.name || !b.name) return 0
      return sortOrder.value * a.name.localeCompare(b.name)
    })
  } else if (sortField.value === 'creationDate') {
    sorted.sort((a, b) => {
      return sortOrder.value * (parseDate(a.creationDate) - parseDate(b.creationDate))
    })
  }
  return sorted
})

const tableColumns = [
  { field: 'checkbox', headerClass: 'col-checkbox', class: 'col-checkbox' },
  { field: 'image', headerClass: 'col-image', class: 'col-image' },
  { field: 'name', headerClass: 'col-large', class: 'col-large' },
  { field: 'email', headerClass: 'col-large', class: 'col-large' },
  { field: 'phone', headerClass: 'col-phone', class: 'col-phone' },
  { field: 'creationDate', headerClass: 'col-date', class: 'col-date' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
]

onMounted(() => {
  loadCustomers()
})

const handleImageError = (event, customer) => {
  // Se a foto falhar, usar o avatar gerado
  event.target.src = generateCustomerAvatar(customer.name)
}

// Bulk selection functions
const toggleCustomerSelection = (customerId) => {
  if (selectedCustomers.value.has(customerId)) {
    selectedCustomers.value.delete(customerId)
  } else {
    selectedCustomers.value.add(customerId)
  }
  // Force reactivity update
  selectedCustomers.value = new Set(selectedCustomers.value)
}

const clearSelection = () => {
  selectedCustomers.value.clear()
  selectedCustomers.value = new Set()
}

const isCustomerSelected = (customerId) => {
  return selectedCustomers.value.has(customerId)
}

// Bulk actions configuration
const bulkActions = computed(() => [
  {
    text: t('customer.bulk.deleteSelected'),
    icon: Delete02Icon,
    color: 'danger',
    variant: 'solid',
    callback: confirmBulkDelete,
    loading: isBulkDeleteLoading.value
  }
])

const confirmBulkDelete = () => {
  const count = selectedCustomers.value.size
  const entityKey = count === 1 ? 'customer.bulk.customer' : 'customer.bulk.customers'
  const messageKey = count === 1 ? 'customer.bulk.confirmDeleteMessageSingle' : 'customer.bulk.confirmDeleteMessage'
  
  // Usar o modal centralizado
  showConfirmDanger(
    t(messageKey, { count, entity: t(entityKey) }),
    t('customer.bulk.confirmDeleteTitle'),
    bulkDeleteCustomers
  )
}

const bulkDeleteCustomers = async () => {
  if (selectedCustomers.value.size === 0) return
  
  isBulkDeleteLoading.value = true
  try {
    const customerIds = Array.from(selectedCustomers.value)
    await CustomerService.deleteCustomers(customerIds)
    
    const count = customerIds.length
    const entityKey = count === 1 ? 'customer.bulk.customer' : 'customer.bulk.customers'
    const messageKey = count === 1 ? 'customer.bulk.deleteSuccessSingle' : 'customer.bulk.deleteSuccess'
    
    toast.add({
      severity: 'success',
      summary: t('success'),
      detail: t(messageKey, { count, entity: t(entityKey) }),
      life: 3000
    })
    
    clearSelection()
    loadCustomers()
  } catch (error) {
    console.error('Error bulk deleting customers:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: t('customer.bulk.deleteError', { entity: t('customer.bulk.customers') }),
      life: 3000
    })
  } finally {
    isBulkDeleteLoading.value = false
  }
}

</script>

<style scoped>
.customer-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  min-width: 250px;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-checkbox) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  text-align: center;
}

:deep(.col-image) {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  text-align: center;
}

:deep(.col-large) {
  width: 200px;
  text-align: center;
}

:deep(.col-phone) {
  width: 150px;
  text-align: center;
}

:deep(.col-date) {
  width: 130px;
  text-align: center;
}

:deep(.col-actions) {
  width: 120px;
  text-align: center;
}

/* Sortable columns cursor */
:deep(.customers-table th .column-header) {
  cursor: default;
  display: block;
  width: 100%;
  text-align: center;
}

:deep(.customers-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
  user-select: none;
}

:deep(.customers-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  transition: all 0.3s ease;
}

/* Sobrescreve os estilos do IluriaDataTable para compactar */
:deep(.customers-table .p-datatable-table) {
  table-layout: auto !important;
  width: 100%;
  border-collapse: separate !important;
  border-spacing: 0 !important;
}

/* Remove qualquer limitação de largura do IluriaDataTable */
:deep(.customers-table .iluria-data-table),
:deep(.customers-table .p-datatable),
:deep(.customers-table .p-datatable-wrapper),
:deep(.customers-table .p-datatable-scrollable-wrapper) {
  overflow: visible !important;
  height: auto !important;
  max-height: none !important;
  width: 100% !important;
  table-layout: auto !important;
}

/* Força larguras compactas para o checkbox e imagem */
:deep(.customers-table .p-datatable-thead > tr > th.col-checkbox),
:deep(.customers-table .p-datatable-tbody > tr > td.col-checkbox) {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  padding: 8px 4px !important;
  text-align: center;
}

:deep(.customers-table .p-datatable-thead > tr > th.col-image),
:deep(.customers-table .p-datatable-tbody > tr > td.col-image) {
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
  padding: 8px 4px !important;
  text-align: center;
}

:deep(.customers-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  transition: all 0.3s ease;
}

:deep(.customers-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
  transition: background-color 0.2s ease;
}

:deep(.customers-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.customers-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  transition: all 0.3s ease;
  font-size: 14px;
}

/* Sobrescrevendo especificamente as células da checkbox e imagem */
:deep(.customers-table .p-datatable-tbody > tr > td.col-checkbox) {
  padding: 4px 2px !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

:deep(.customers-table .p-datatable-tbody > tr > td.col-image) {
  padding: 4px 2px !important;
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
}

/* Headers compactos também */
:deep(.customers-table .p-datatable-thead > tr > th.col-checkbox) {
  padding: 8px 2px !important;
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
}

:deep(.customers-table .p-datatable-thead > tr > th.col-image) {
  padding: 8px 2px !important;
  width: 60px !important;
  min-width: 60px !important;
  max-width: 60px !important;
}

.text-center {
  text-align: center !important;
}

/* Customer Avatar */
.customer-avatar {
  height: 40px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.customer-avatar.clickable {
  cursor: pointer;
  border-radius: 50%;
}

.customer-avatar.clickable:hover {
  transform: scale(1.02);
  opacity: 0.8;
}

.avatar-image {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  transition: all 0.3s ease;
  border: 2px solid var(--iluria-color-border);
}

.customer-avatar.clickable:hover .avatar-image {
  border-color: var(--iluria-color-primary);
}

/* Customer Name */
.customer-name {
  display: flex;
  flex-direction: column;
  gap: 2px;
  align-items: center;
}

.customer-name.clickable {
  cursor: pointer;
  transition: all 0.2s ease;
}

.customer-name.clickable:hover .name-text {
  color: var(--iluria-color-primary);
}

.name-text {
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}

.document-text {
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

/* Email and Phone Links */
.email-link {
  color: var(--iluria-color-primary);
  text-decoration: none;
  transition: color 0.2s ease;
  display: block;
  text-align: center;
}

.email-link:hover {
  color: var(--iluria-color-primary-hover);
  text-decoration: underline;
}

.phone-link {
  color: var(--iluria-color-success);
  text-decoration: none;
  transition: color 0.2s ease;
  display: block;
  text-align: center;
}

.phone-link:hover {
  color: #047857;
  text-decoration: underline;
}

.no-phone {
  color: var(--iluria-color-text-muted);
  font-style: italic;
  transition: color 0.3s ease;
  text-align: center;
}

.created-at-text {
  display: block;
  text-align: center;
}

/* Actions */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
}

.empty-title {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
  text-align: center;
}

.empty-description {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 16px 0;
  transition: color 0.3s ease;
  text-align: center;
  line-height: 1.5;
  max-width: 400px;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  background: var(--iluria-color-surface);
  transition: all 0.3s ease;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@keyframes spin {
  0% {
    transform: rotate(0deg);
  }

  100% {
    transform: rotate(360deg);
  }
}

.checkbox-cell {
  width: 40px !important;
  min-width: 40px !important;
  max-width: 40px !important;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
}


.checkbox-header {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 !important;
  margin: 0 !important;
}

/* Custom checkbox styling */
.customer-selection-checkbox {
  margin: 0 !important;
  padding: 0 !important;
  width: 16px !important;
  height: 16px !important;
}

.customer-selection-checkbox :deep(.iluria-checkbox-wrapper) {
  width: 16px !important;
  margin: 0 !important;
  padding: 0 !important;
}



.customer-selection-checkbox :deep(.checkbox-label) {
  display: none !important;
}

.customer-selection-checkbox :deep(.iluria-checkbox) {
  margin: 0 !important;
  padding: 0 !important;
  flex-shrink: 0;
  width: 16px !important;
  height: 16px !important;
}

/* Bulk Action Bar integration - removed since it's now sticky */

/* Responsive */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    justify-content: space-between;
  }

  .search-input {
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .customer-list-container {
    padding: 16px;
  }

  .search-input {
    min-width: 150px;
  }

  :deep(.customers-table .p-datatable-tbody > tr > td) {
    padding: 12px 16px;
  }

  :deep(.customers-table .p-datatable-thead > tr > th) {
    padding: 12px 16px;
    font-size: 11px;
  }
}
</style>
