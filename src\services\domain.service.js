import axios from 'axios';
import { API_URLS } from '../config/api.config';

class DomainsService{
    #endpointDomainUrl;

    constructor() {
        this.#endpointDomainUrl = `${API_URLS.DOMAINS_BASE_URL}`;
    }

    async getDomainUrlList() {
        try {
            const response = await axios.get(this.#endpointDomainUrl);
            return response.data;
        } catch (error) {
            console.error('Error fetching domain URLs:', error);
            throw error;
        }
    }

    async getDomainUrl(id){
        try{
            const response = await axios.get(`${this.#endpointDomainUrl}/${id}`);
            return response.data;
        }catch(error){
            console.error('Error fetching domain URL:', error);
            throw error;
        }
    }

    async createDomainUrl(domainUrlData) {
        try {
            const response = await axios.post(this.#endpointDomainUrl, domainUrlData);
            return response.data;
        } catch (error) {
            console.error('Error creating domain URL:', error);
            throw error;
        }
    }

    async updateDomainUrl(id, domainUrlData) {
        try {
            const response = await axios.put(`${this.#endpointDomainUrl}/${id}`, domainUrlData);
            return response.data;
        } catch (error) {
            console.error('Error updating domain URL:', error);
            throw error;
        }
    }

    async deleteDomainUrl(id) {
        try {
            const response = await axios.delete(`${this.#endpointDomainUrl}/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error deleting domain URL:', error);
            throw error;
        }
    }

    async validateDomainUrl(id) {
        try {
            const response = await axios.get(`${this.#endpointDomainUrl}/validate/${id}`);
            return response.data;
        } catch (error) {
            console.error('Error validating domain URL:', error);
            throw error;
        }
    }


}

export default new DomainsService();