<template>
  <div class="orders-chart" ref="chartContainer">
    <div v-if="loading" class="chart-loading">
      <div class="loading-skeleton"></div>
    </div>
    <div v-else class="chart-container">
      <div class="chart-header">
        <div class="chart-stats">
          <div class="stat-item">
            <span class="stat-label">{{ $t('dashboard.totalOrders') }}</span>
            <span class="stat-value">{{ totalOrders }}</span>
          </div>
          <div class="stat-item">
            <span class="stat-label">{{ $t('dashboard.averageDaily') }}</span>
            <span class="stat-value">{{ averageDaily }}</span>
          </div>
        </div>
      </div>
      
      <div class="chart-wrapper" ref="chartWrapper">
        <svg :width="chartDimensions.width" :height="chartDimensions.height" class="chart-svg">
          <!-- Grid lines -->
          <defs>
            <pattern id="ordersGrid" :width="gridSize" :height="gridSize" patternUnits="userSpaceOnUse">
              <path :d="`M ${gridSize} 0 L 0 0 0 ${gridSize}`" fill="none" stroke="#f3f4f6" stroke-width="1"/>
            </pattern>
          </defs>
          <rect width="100%" height="100%" fill="url(#ordersGrid)" />
          
          <!-- Chart bars -->
          <rect
            v-for="(point, index) in dataPoints"
            :key="index"
            :x="point.x - barWidth / 2"
            :y="point.y"
            :width="barWidth"
            :height="point.height"
            fill="url(#ordersGradient)"
            class="chart-bar"
            @mouseover="showTooltip($event, point)"
            @mouseleave="hideTooltip"
          />
          
          <!-- Gradient definition for bars -->
          <defs>
            <linearGradient id="ordersGradient" x1="0%" y1="0%" x2="0%" y2="100%">
              <stop offset="0%" style="stop-color:#3b82f6;stop-opacity:0.8" />
              <stop offset="100%" style="stop-color:#3b82f6;stop-opacity:0.4" />
            </linearGradient>
          </defs>
          
          <!-- Value labels on top of bars -->
          <text
            v-for="(point, index) in dataPoints"
            :key="index"
            :x="point.x"
            :y="point.y - 8"
            text-anchor="middle"
            class="value-label"
            :class="{ 'value-label-small': isMobile }"
          >
            {{ point.value }}
          </text>
        </svg>
        
        <!-- X-axis labels -->
        <div class="x-axis">
          <span
            v-for="(point, index) in dataPoints"
            :key="index"
            class="x-label"
            :class="{ 'x-label-small': isMobile }"
            :style="{ left: point.x + 'px' }"
          >
            {{ formatDate(point.date) }}
          </span>
        </div>
      </div>
      
      <!-- Tooltip -->
      <div
        v-if="tooltip.show"
        class="chart-tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div class="tooltip-content">
          <div class="tooltip-date">{{ tooltip.date }}</div>
          <div class="tooltip-value">{{ tooltip.value }} {{ $t('dashboard.orders') }}</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, onMounted, onUnmounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  data: {
    type: Array,
    required: true
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()

// Refs para elementos DOM
const chartContainer = ref(null)
const chartWrapper = ref(null)

// Estado responsivo
const containerDimensions = ref({ width: 600, height: 300 })
const isMobile = ref(false)

// Dimensões calculadas
const chartDimensions = computed(() => {
  const minWidth = 300
  const minHeight = 200
  const maxWidth = 1200
  const maxHeight = 400
  
  let width = Math.max(minWidth, Math.min(maxWidth, containerDimensions.value.width))
  let height = Math.max(minHeight, Math.min(maxHeight, containerDimensions.value.height))
  
  // Ajustar proporção para mobile
  if (isMobile.value) {
    height = Math.min(height, 250)
  }
  
  return { width, height }
})

const padding = computed(() => {
  const base = { top: 40, right: 20, bottom: 40, left: 20 }
  
  if (isMobile.value) {
    return { top: 30, right: 15, bottom: 35, left: 15 }
  }
  
  return base
})

const gridSize = computed(() => isMobile.value ? 30 : 40)

const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  date: '',
  value: ''
})

const totalOrders = computed(() => {
  return new Intl.NumberFormat('pt-BR').format(props.data.reduce((sum, item) => sum + item.value, 0))
})

const averageDaily = computed(() => {
  const total = props.data.reduce((sum, item) => sum + item.value, 0)
  const average = Math.round(total / props.data.length)
  return new Intl.NumberFormat('pt-BR').format(average)
})

const barWidth = computed(() => {
  if (!props.data.length) return 0
  const chartInnerWidth = chartDimensions.value.width - padding.value.left - padding.value.right
  const maxBarWidth = isMobile.value ? 25 : 40
  const calculatedWidth = (chartInnerWidth / props.data.length) * 0.6
  return Math.min(maxBarWidth, calculatedWidth)
})

const dataPoints = computed(() => {
  if (!props.data.length) return []
  
  const maxValue = Math.max(...props.data.map(d => d.value))
  const chartInnerWidth = chartDimensions.value.width - padding.value.left - padding.value.right
  const chartInnerHeight = chartDimensions.value.height - padding.value.top - padding.value.bottom
  
  return props.data.map((item, index) => {
    const x = padding.value.left + (index + 0.5) * (chartInnerWidth / props.data.length)
    const height = (item.value / maxValue) * chartInnerHeight
    const y = chartDimensions.value.height - padding.value.bottom - height
    
    return {
      x,
      y,
      height,
      date: item.date,
      value: item.value
    }
  })
})

const formatDate = (dateString) => {
  const date = new Date(dateString)
  return date.toLocaleDateString('pt-BR', { day: '2-digit', month: '2-digit' })
}

const showTooltip = (event, point) => {
  const tooltipWidth = 100
  const tooltipHeight = 10
  
  // Usar as coordenadas do ponto do gráfico (já calculadas em coordenadas SVG)
  let x = point.x - tooltipWidth / 2
  let y = point.y - tooltipHeight - 10
  
  // Ajustar posição para não sair da tela horizontalmente
  const margin = 15
  if (x < margin) x = margin
  if (x + tooltipWidth > chartDimensions.value.width - margin) {
    x = chartDimensions.value.width - tooltipWidth - margin
  }
  
  // Ajustar posição vertical se não houver espaço acima
  if (y < margin) {
    y = point.y + point.height + 20 // Posicionar abaixo da barra
  }
  
  tooltip.value = {
    show: true,
    x,
    y,
    date: formatDate(point.date),
    value: point.value
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}

const updateDimensions = () => {
  if (!chartContainer.value) return
  
  const rect = chartContainer.value.getBoundingClientRect()
  containerDimensions.value = {
    width: rect.width,
    height: rect.height || 300
  }
  
  isMobile.value = window.innerWidth < 768
}

let resizeObserver = null

onMounted(async () => {
  await nextTick()
  updateDimensions()
  
  // ResizeObserver para detectar mudanças de tamanho
  if (window.ResizeObserver && chartContainer.value) {
    resizeObserver = new ResizeObserver(updateDimensions)
    resizeObserver.observe(chartContainer.value)
  }
  
  // Fallback para window resize
  window.addEventListener('resize', updateDimensions)
})

onUnmounted(() => {
  if (resizeObserver) {
    resizeObserver.disconnect()
  }
  window.removeEventListener('resize', updateDimensions)
})
</script>

<style scoped>
.orders-chart {
  height: 100%;
  width: 100%;
  position: relative;
  min-height: 200px;
}

.chart-loading {
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
}

.loading-skeleton {
  width: 100%;
  height: 100%;
  min-height: 200px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.chart-container {
  height: 100%;
  width: 100%;
  display: flex;
  flex-direction: column;
}

.chart-header {
  margin-bottom: 20px;
  flex-shrink: 0;
}

.chart-stats {
  display: flex;
  gap: 32px;
}

.stat-item {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.stat-label {
  font-size: 0.875rem;
  color: #6b7280;
  font-weight: 500;
}

.stat-value {
  font-size: 1.125rem;
  color: #111827;
  font-weight: 600;
}

.chart-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
  border-radius: 8px;
  
  min-height: 200px;
}

.chart-svg {
  width: 100%;
  height: 100%;
  display: block;
}

.chart-bar {
  cursor: pointer;
  transition: all 0.2s ease;
}

.chart-bar:hover {
  filter: brightness(1.1);
}

.value-label {
  font-size: 0.75rem;
  font-weight: 600;
  fill: #374151;
}

.value-label-small {
  font-size: 0.7rem;
}

.x-axis {
  position: absolute;
  bottom: 10px;
  left: 0;
  right: 0;
  height: 20px;
}

.x-label {
  position: absolute;
  transform: translateX(-50%);
  font-size: 0.75rem;
  color: #6b7280;
  font-weight: 500;
}

.x-label-small {
  font-size: 0.7rem;
}

.chart-tooltip {
  position: absolute;
  pointer-events: none;
  z-index: 10;
}

.tooltip-content {
  background: #111827;
  color: white;
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  text-align: center;
  white-space: nowrap;
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: #111827;
}

.tooltip-date {
  font-weight: 500;
  margin-bottom: 2px;
}

.tooltip-value {
  font-weight: 600;
  color: #3b82f6;
}

@media (max-width: 768px) {
  .chart-stats {
    flex-direction: column;
    gap: 16px;
  }
  
  .stat-value {
    font-size: 1rem;
  }
  
  .chart-header {
    margin-bottom: 15px;
  }
  
  .tooltip-content {
    font-size: 0.8rem;
    padding: 6px 10px;
  }
}

@media (max-width: 480px) {
  .chart-stats {
    gap: 12px;
  }
  
  .stat-label {
    font-size: 0.8rem;
  }
  
  .stat-value {
    font-size: 0.95rem;
  }
  
     .chart-header {
     margin-bottom: 12px;
   }
 }
</style> 
