import axios from 'axios';
import { API_URLS } from '../config/api.config';

const endpoint = `${API_URLS.PRODUCT_BASE_URL}`;

export const productsApi = {
  getAll: (params = {}) => {
    const { page = 0, size = 1, name = '', stockQuantity = null, status = null, sortBy = 'name', sortDirection = 'asc' } = params;

    return axios.get(endpoint, {
      params: {
        page,
        size,
        name: name || undefined,
        stockQuantity: stockQuantity !== null ? stockQuantity : undefined,
        status: status || undefined,
        sortBy,
        sortDirection
      }
    });
  },

  getProductsWithVariations: (params = {}) => {
    const { page = 0, size = 10, name = '', categoryId } = params;

    return axios.get(endpoint, {
      params: {
        page,
        size,
        name: name || undefined,
        categoryId: categoryId || undefined,
        sortBy: 'name',
        sortDirection: 'asc'
      }
    });
  },

  getById: (id) => axios.get(`${endpoint}/${id}`).then(response => response.data),
  create: (product) => {
    return axios.post(endpoint, product);
  },
  update: (id, product) => {
    return axios.put(`${endpoint}/${id}`, product);
  },
  delete: (id) => axios.delete(`${endpoint}/${id}`),

  updateVariation: (productId, variationId, updates) => {
    return axios.put(`${endpoint}/${productId}/variations/${variationId}`, updates);
  },
  deleteVariation: (productId, variationId) => {
    return axios.delete(`${endpoint}/${productId}/variations/${variationId}`);
  },

  uploadImage: (productId, file) => {
    const formData = new FormData();
    formData.append('file', file);

    return Promise.reject(new Error('Funcionalidade de upload de imagens não implementada ainda'));
  },

  cloneImages: (originalProductId, clonedProductId) => {
    return axios.post(`${endpoint}/${originalProductId}/clone-images/${clonedProductId}`);
  },

  uploadVariationImages: (productId, variationId, files) => {
    const formData = new FormData();

    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i]);
    }

    return axios.post(`${endpoint}/${productId}/variations/${variationId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  uploadProductImages: (productId, files) => {
    const formData = new FormData();

    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i]);
    }

    return axios.post(`${endpoint}/${productId}/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  deleteImage: (productId, imageId) => {
    return Promise.reject(new Error('Funcionalidade de delete de imagens não implementada ainda'));
  },

  deleteProductImage: (productId, imageUrl) => {
    return axios.delete(`${endpoint}/${productId}/images`, {
      params: { imageUrl }
    });
  },

  deleteGiftCardImage: (productId, imageUrl) => {
    return axios.delete(`${endpoint}/${productId}/gift-card/images`, {
      params: { imageUrl }
    });
  },

  deleteVariationImage: (productId, variationId, imageUrl) => {
    return axios.delete(`${endpoint}/${productId}/variations/${variationId}/images`, {
      params: { imageUrl }
    });
  },

  uploadSeoVariationImage: (productId, attribute, attributeValue, file) => {
    const formData = new FormData();
    formData.append('file', file);
    formData.append('attribute', attribute);
    formData.append('attributeValue', attributeValue);

    return axios.post(`${endpoint}/${productId}/seo/variations/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  deleteSeoVariationImage: (productId, attribute, attributeValue, imageId) => {
    return axios.delete(`${endpoint}/${productId}/seo/variations/images`, {
      params: {
        attribute,
        attributeValue,
        imageId
      }
    });
  },

  getGiftCards: (params = {}) => {
    const { page = 0, size = 10, name = '' } = params;
    return axios.get(`${endpoint}/gift-cards`, {
      params: {
        page,
        size,
        name: name || undefined,
        sortBy: 'name',
        sortDirection: 'asc'
      }
    })
  },

  createGiftCard: (productData) => {
    return axios.post(`${endpoint}/gift-card`, productData)
  },

  updateGiftCard: (giftCardId, productData) => {
    return axios.put(`${endpoint}/${giftCardId}/gift-card`, productData)
  },

  uploadGiftCardImage: (giftCardId, files) => {
    const formData = new FormData();
    if (!files) return;
    for (let i = 0; i < files.length; i++) {
      formData.append('files', files[i]);
    }

    return axios.post(`${endpoint}/${giftCardId}/gift-card/images`, formData, {
      headers: {
        'Content-Type': 'multipart/form-data'
      }
    });
  },

  deleteGiftCard: (giftCardId) => {
    return axios.delete(`${endpoint}/${giftCardId}/gift-card/delete`)
  },

  getGiftCardById: (giftCardId) => {
    return axios.get(`${endpoint}/gift-card/${giftCardId}`)
  },

  deleteProducts: async (productIds) => {
    if (!Array.isArray(productIds) || productIds.length === 0) {
      throw new Error('IDs de produtos devem ser fornecidos como array não vazio');
    }
    
    const response = await axios.delete(`${endpoint}/bulk`, {
      data: { productIds }
    });
    
    return response.data;
  }

};
export default productsApi;