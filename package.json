{"name": "iluria-admin", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "preview": "vite preview"}, "dependencies": {"@codemirror/basic-setup": "^0.20.0", "@codemirror/commands": "^6.8.1", "@codemirror/lang-html": "^6.4.9", "@codemirror/state": "^6.5.2", "@codemirror/view": "^6.36.7", "@editorjs/attaches": "^1.3.0", "@editorjs/checklist": "^1.6.0", "@editorjs/code": "^2.9.3", "@editorjs/delimiter": "^1.4.2", "@editorjs/editorjs": "^2.30.8", "@editorjs/embed": "^2.7.6", "@editorjs/header": "^2.8.8", "@editorjs/image": "^2.10.3", "@editorjs/inline-code": "^1.5.2", "@editorjs/link": "^2.6.2", "@editorjs/list": "^2.0.8", "@editorjs/marker": "^1.4.0", "@editorjs/paragraph": "^2.11.7", "@editorjs/quote": "^2.7.6", "@editorjs/raw": "^2.5.1", "@editorjs/table": "^2.4.5", "@editorjs/warning": "^1.4.1", "@fortawesome/fontawesome-free": "^6.7.2", "@fortawesome/fontawesome-svg-core": "^6.5.1", "@fortawesome/free-solid-svg-icons": "^6.5.1", "@fortawesome/vue-fontawesome": "^3.0.5", "@headlessui/vue": "^1.7.23", "@heroicons/vue": "^2.2.0", "@hugeicons-pro/core-bulk-rounded": "^1.0.2", "@hugeicons-pro/core-duotone-rounded": "^1.0.2", "@hugeicons-pro/core-solid-rounded": "^1.0.16", "@hugeicons-pro/core-stroke-rounded": "^1.0.14", "@hugeicons-pro/core-stroke-sharp": "^1.0.2", "@hugeicons-pro/core-stroke-standard": "^1.0.2", "@hugeicons/vue": "^1.0.3", "@popperjs/core": "^2.11.8", "@primeuix/themes": "^1.0.0", "@primevue/forms": "^4.3.3", "@stomp/stompjs": "^7.1.1", "@tailwindcss/vite": "^4.0.12", "axios": "^1.7.9", "bootstrap-icons": "^1.11.3", "cropperjs": "^1.6.2", "editorjs-html": "^4.0.5", "editorjs-parser": "^1.5.3", "editorjs-style": "^3.0.3", "iluria-admin": "file:", "iluria-admin-vue": "file:", "jwt-decode": "^4.0.0", "lucide-vue-next": "^0.479.0", "monaco": "^1.201704190613.0", "monaco-editor": "^0.52.2", "monaco-editor-vue3": "^0.1.10", "pinia": "^3.0.1", "pinia-plugin-persistedstate": "^4.2.0", "primeicons": "^7.0.0", "primevue": "^4.3.3", "quill": "^2.0.3", "sockjs-client": "^1.6.1", "tailwindcss": "^4.0.12", "tailwindcss-primeui": "^0.5.1", "vee-validate": "^4.15.0", "vite": "^6.2.1", "vue": "^3.5.13", "vue-i18n": "^9.14.1", "vue-router": "^4.5.0", "vue-the-mask": "^0.11.1", "vue3-google-map": "^0.22.0", "vuedraggable": "^4.1.0", "yup": "^1.6.1", "zod": "^3.24.2"}, "devDependencies": {"@vitejs/plugin-vue": "^5.2.1", "autoprefixer": "^10.4.21", "glob": "^11.0.3", "postcss-nested": "^7.0.2", "vite": "^6.2.1", "vite-plugin-monaco-editor": "^1.1.0", "vite-plugin-vue-devtools": "^7.7.2"}}