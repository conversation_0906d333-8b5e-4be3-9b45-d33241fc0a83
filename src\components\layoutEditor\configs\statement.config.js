/**
 * Configuração do Componente de Comunicados
 * Componente simples de faixa de comunicado com texto e botão opcional
 */

export default {
  type: 'statement',
  name: 'Comunicado',
  description: 'Adiciona uma faixa de comunicado com texto e botão opcional',
  category: 'content',
  priority: 60,
  icon: 'description',

  html: `<div 
    data-component="statement" 
    data-element-type="statement"
    class="iluria-statement"
    style="width: 100%; padding: 20px 0; background-color: #3b82f6; color: white; text-align: center; margin: 0; position: relative;">
    
    <div class="statement-content" style="max-width: 1200px; margin: 0 auto; padding: 0 20px;">
      <p class="statement-text" data-text-element="true" style="margin: 0 0 16px 0; font-size: 16px; line-height: 1.5; font-weight: 500;">
        🎉 Oferta especial! Aproveite até 50% OFF em produtos selecionados por tempo limitado.
      </p>
      
      <button 
        class="statement-button" 
        data-button-element="true"
        style="display: inline-block; padding: 12px 24px; background-color: white; color: #3b82f6; border: none; border-radius: 6px; font-weight: 600; cursor: pointer; font-size: 14px; transition: all 0.2s ease; margin: 0;"
        onmouseover="this.style.transform='translateY(-1px)'; this.style.boxShadow='0 4px 8px rgba(0,0,0,0.1)'"
        onmouseout="this.style.transform='translateY(0)'; this.style.boxShadow='none'"
        onclick="handleStatementClick(this)"
      >
        Ver ofertas
      </button>
    </div>
    
    <script>
      function handleStatementClick(button) {
        const statementElement = button.closest('[data-component="statement"]');
        const url = statementElement?.getAttribute('data-button-url');
        
        if (url && url.trim()) {
          window.open(url, '_blank');
        }
      }
      
      // Sistema de atualização simplificado
      if (typeof window !== 'undefined') {
        window.updateStatement = function(element, config) {
          if (!element) return;
          
          try {
            // Garantir que não há margens estranhas
            const elementContent = element.closest('.element-content')
            if (elementContent) {
              elementContent.style.padding = '0'
              elementContent.style.margin = '0'
            }
            
            // Garantir que o statement seja full-width
            element.style.margin = '0'
            element.style.width = '100%'
            element.style.boxSizing = 'border-box'
            
            // Atualizar texto
            const textElement = element.querySelector('.statement-text');
            if (textElement && config.text !== undefined) {
              textElement.textContent = config.text;
            }
            
            // Atualizar botão
            const buttonElement = element.querySelector('.statement-button');
            if (buttonElement) {
              if (config.showButton === false) {
                buttonElement.style.display = 'none';
              } else {
                buttonElement.style.display = 'inline-block';
                if (config.buttonText) {
                  buttonElement.textContent = config.buttonText;
                }
              }
            }
            
            // Atualizar URL do botão
            if (config.buttonUrl !== undefined) {
              element.setAttribute('data-button-url', config.buttonUrl);
            }
            
            // Atualizar estilos
            if (config.backgroundColor) {
              element.style.backgroundColor = config.backgroundColor;
            }
            
            if (config.textColor) {
              if (textElement) textElement.style.color = config.textColor;
            }
            
            if (config.buttonColor && buttonElement) {
              buttonElement.style.backgroundColor = config.buttonColor;
            }
            
            if (config.buttonTextColor && buttonElement) {
              buttonElement.style.color = config.buttonTextColor;
            }
            
            if (config.textAlign) {
              element.style.textAlign = config.textAlign;
            }
            
          } catch (error) {
            console.error('Erro ao atualizar statement:', error);
          }
        };
      }
    </script>
    
    <!-- CSS para neutralizar margens do container -->
    <style>
      .iluria-statement {
        margin: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
      }
      
      /* Neutralizar padding do element-content apenas para statements */
      .component-container:has(.iluria-statement) .element-content {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      /* Fallback para navegadores sem :has() */
      .element-content:has(.iluria-statement) {
        padding: 0 !important;
        margin: 0 !important;
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'statement-config',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
        <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
      </svg>`,
      title: 'Editar Comunicado',
      condition: (element) => {
        return element?.getAttribute('data-component') === 'statement' ||
               element?.getAttribute('data-element-type') === 'statement' ||
               element?.closest('[data-component="statement"]')
      }
    }
  ],

  propertyEditors: [
    {
      type: 'statement-config',
      component: 'StatementEditor',
      path: '@/components/layoutEditor/Sidebar/editors/StatementEditor.vue'
    }
  ],
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true
  },
  
  cleanup: {
    removeEditingStyles: true,
    specialElements: ['.statement-text', '.statement-button']
  }
} 