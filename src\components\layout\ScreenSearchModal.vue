<template>
  <IluriaModal
    v-model="visible"
    :title="t('screenSearch.title')"
    :subtitle="t('screenSearch.subtitle')"
    :icon="Search02Icon"
    icon-color="blue"
    :show-footer="false"
    :dismissable-mask="true"
    :dialog-style="{ width: '90vw', maxWidth: '600px' }"
    class="screen-search-modal"
  >
    <!-- Campo de pesquisa -->
    <div class="search-container">
      <div class="search-input-wrapper">
        <HugeiconsIcon 
          :icon="Search02Icon" 
          size="20" 
          class="search-icon"
          style="color: var(--iluria-color-text-secondary);" 
        />
        <input
          ref="searchInputRef"
          v-model="searchQuery"
          type="text"
          :placeholder="t('screenSearch.placeholder')"
          class="search-input"
          @keydown="handleKeyNavigation"
          @input="handleSearchInput"
        />
        <div v-if="searchQuery" class="clear-search" @click="clearSearch">
          <HugeiconsIcon :icon="Cancel01Icon" size="16" />
        </div>
      </div>
      
      <!-- Filtro de categoria -->
      <div v-if="searchQuery || selectedCategory" class="category-filter">
        <button
          v-for="(category, key) in categories"
          :key="key"
          :class="['category-btn', { active: selectedCategory === key }]"
          @click="selectedCategory = selectedCategory === key ? null : key"
        >
          <HugeiconsIcon :icon="category.icon" size="16" />
          {{ category.name }}
        </button>
      </div>
    </div>

    <!-- Resultados da busca -->
    <div class="results-container" ref="resultsContainerRef">
      <!-- Telas recentes (quando não há busca) -->
      <div v-if="!searchQuery && recentScreens.length > 0" class="results-section">
        <div class="section-header">
          <h3 class="section-title">
            <HugeiconsIcon :icon="Time04Icon" size="16" />
            {{ t('screenSearch.sections.recent') }}
          </h3>
          <button 
            class="clear-recent-btn"
            @click="clearRecentScreens"
            :title="t('screenSearch.actions.clearRecent')"
          >
            <HugeiconsIcon :icon="Delete02Icon" size="14" />
            {{ t('screenSearch.actions.clear') }}
          </button>
        </div>
        <div class="results-list">
          <div
            v-for="(screen, index) in recentScreens"
            :key="screen.path"
            :class="['result-item', { selected: selectedIndex === index }]"
            @click="selectScreen(screen)"
            @mouseenter="selectedIndex = index"
          >
            <div class="result-icon">
              <HugeiconsIcon :icon="screen.icon" size="20" :style="{ color: getCategoryColor(screen.category) }" />
            </div>
            <div class="result-content">
              <div class="result-title">{{ screen.title }}</div>
              <div class="result-description">{{ screen.description }}</div>
            </div>
            <div class="result-category">
              {{ getCategoryName(screen.category) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Resultados da pesquisa -->
      <div v-else-if="(searchQuery || selectedCategory) && filteredResults.length > 0" class="results-section">
        <h3 class="section-title">
          <HugeiconsIcon :icon="Search02Icon" size="16" />
          {{ t('screenSearch.sections.results') }}
          <span class="results-count">({{ filteredResults.length }})</span>
        </h3>
        <div class="results-list">
          <div
            v-for="(result, index) in filteredResults"
            :key="result.path"
            :class="['result-item', { selected: selectedIndex === index }]"
            @click="selectScreen(result)"
            @mouseenter="selectedIndex = index"
          >
            <div class="result-icon">
              <HugeiconsIcon :icon="result.icon" size="20" :style="{ color: getCategoryColor(result.category) }" />
            </div>
            <div class="result-content">
              <div class="result-title" v-html="highlightMatch(result.title, searchQuery)"></div>
              <div class="result-description" v-html="highlightMatch(result.description, searchQuery)"></div>
            </div>
            <div class="result-category">
              {{ getCategoryName(result.category) }}
            </div>
          </div>
        </div>
      </div>

      <!-- Estado vazio -->
      <div v-else-if="(searchQuery || selectedCategory) && filteredResults.length === 0" class="empty-state">
        <HugeiconsIcon :icon="SearchRemoveIcon" size="48" class="empty-state-icon" style="color: var(--iluria-color-text-secondary);" />
        <h3>{{ t('screenSearch.states.noResults') }}</h3>
        <p>{{ t('screenSearch.states.noResultsDescription') }}</p>
      </div>

      <!-- Estado inicial -->
      <div v-else-if="!searchQuery && !selectedCategory" class="empty-state">
        <HugeiconsIcon :icon="Search02Icon" size="48" class="empty-state-icon" style="color: var(--iluria-color-text-secondary);" />
        <h3>{{ t('screenSearch.states.empty') }}</h3>
        
        <!-- Categorias populares -->
        <div class="popular-categories">
         
          <div class="category-grid">
            <button
              v-for="(category, key) in popularCategories"
              :key="key"
              class="category-card"
              @click="selectCategory(key)"
            >
              <HugeiconsIcon :icon="category.icon" size="24" :style="{ color: category.color }" />
              <span>{{ category.name }}</span>
            </button>
          </div>
        </div>
      </div>
    </div>

    <!-- Dicas de atalhos -->
    <div class="shortcuts-info">
      <div class="shortcut-item">
        <kbd>↑↓</kbd>
        <span>{{ t('screenSearch.shortcuts.navigate') }}</span>
      </div>
      <div class="shortcut-item">
        <kbd>Enter</kbd>
        <span>{{ t('screenSearch.shortcuts.select') }}</span>
      </div>
      <div class="shortcut-item">
        <kbd>Esc</kbd>
        <span>{{ t('screenSearch.shortcuts.close') }}</span>
      </div>
    </div>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import { HugeiconsIcon } from '@hugeicons/vue';
import IluriaModal from '@/components/iluria/IluriaModal.vue';

import {
  Search02Icon,
  Cancel01Icon,
  Time04Icon,
  SearchRemoveIcon,
  Delete02Icon
} from '@hugeicons-pro/core-stroke-standard';

import {
  searchScreensWithPermissions,
  getRecentScreensWithPermissions,
  addToRecentScreens,
  screenCategories,
  getScreensByCategory,
  hasScreenAccess
} from '@/data/screens.js';
import { useGlobalPermissions } from '@/composables/usePermissions';

const { t } = useI18n();
const router = useRouter();
const { hasPermission } = useGlobalPermissions();

// Props e emits
const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

// Refs
const searchInputRef = ref(null);
const resultsContainerRef = ref(null);

// Estado reativo
const searchQuery = ref('');
const selectedIndex = ref(0);
const selectedCategory = ref(null);
const recentScreens = ref([]);

// Computed
const visible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
});

const categories = computed(() => screenCategories);

const popularCategories = computed(() => {
  // Retorna categorias mais populares primeiro
  const popular = ['sales', 'products', 'settings', 'customers'];
  const result = {};
  
  popular.forEach(key => {
    if (categories.value[key]) {
      result[key] = categories.value[key];
    }
  });
  
  return result;
});

const searchResults = computed(() => {
  if (!searchQuery.value || searchQuery.value.length < 2) return [];
  return searchScreensWithPermissions(searchQuery.value, hasPermission);
});

const categoryResults = computed(() => {
  if (!selectedCategory.value) return [];
  
  const categoryScreens = getScreensByCategory(selectedCategory.value);
  
  // Filtrar por permissões
  return categoryScreens.filter(screen => hasScreenAccess(screen, hasPermission));
});

const filteredResults = computed(() => {
  // Se tem categoria selecionada mas não há busca de texto, mostrar resultados da categoria
  if (selectedCategory.value && (!searchQuery.value || searchQuery.value.length < 2)) {
    return categoryResults.value.slice(0, 10);
  }
  
  // Se há busca de texto, aplicar filtro de categoria se selecionado
  let results = searchResults.value;
  
  if (selectedCategory.value) {
    results = results.filter(result => result.category === selectedCategory.value);
  }
  
  return results.slice(0, 10); // Limitar a 10 resultados
});

// Métodos
const handleKeyNavigation = (event) => {
  const maxIndex = getMaxIndex();
  
  switch (event.key) {
    case 'ArrowDown':
      event.preventDefault();
      selectedIndex.value = selectedIndex.value < maxIndex ? selectedIndex.value + 1 : 0;
      scrollToSelected();
      break;
      
    case 'ArrowUp':
      event.preventDefault();
      selectedIndex.value = selectedIndex.value > 0 ? selectedIndex.value - 1 : maxIndex;
      scrollToSelected();
      break;
      
    case 'Enter':
      event.preventDefault();
      selectCurrentItem();
      break;
      
    case 'Escape':
      event.preventDefault();
      visible.value = false;
      break;
  }
};

const getMaxIndex = () => {
  if (searchQuery.value || selectedCategory.value) {
    return Math.max(0, filteredResults.value.length - 1);
  }
  return Math.max(0, recentScreens.value.length - 1);
};

const selectCurrentItem = () => {
  const currentResults = (searchQuery.value || selectedCategory.value) ? filteredResults.value : recentScreens.value;
  if (currentResults[selectedIndex.value]) {
    selectScreen(currentResults[selectedIndex.value]);
  }
};

const selectScreen = (screen) => {
  // Adicionar ao histórico de recentes
  addToRecentScreens(screen.path);
  
  // Navegar para a tela
  router.push(screen.path);
  
  // Fechar modal
  visible.value = false;
};

const selectCategory = (categoryKey) => {
  selectedCategory.value = categoryKey;
  nextTick(() => {
    searchInputRef.value?.focus();
  });
};

const clearSearch = () => {
  searchQuery.value = '';
  selectedCategory.value = null;
  selectedIndex.value = 0;
};

const handleSearchInput = () => {
  selectedIndex.value = 0;
  selectedCategory.value = null;
};

const scrollToSelected = () => {
  nextTick(() => {
    const container = resultsContainerRef.value;
    const selectedElement = container?.querySelector('.result-item.selected');
    
    if (selectedElement && container) {
      const containerRect = container.getBoundingClientRect();
      const elementRect = selectedElement.getBoundingClientRect();
      
      if (elementRect.bottom > containerRect.bottom) {
        selectedElement.scrollIntoView({ block: 'end', behavior: 'smooth' });
      } else if (elementRect.top < containerRect.top) {
        selectedElement.scrollIntoView({ block: 'start', behavior: 'smooth' });
      }
    }
  });
};

const getCategoryColor = (categoryKey) => {
  return categories.value[categoryKey]?.color || '#6B7280';
};

const getCategoryName = (categoryKey) => {
  return categories.value[categoryKey]?.name || categoryKey;
};

const highlightMatch = (text, query) => {
  if (!query || !text) return text;
  
  const regex = new RegExp(`(${query.replace(/[.*+?^${}()|[\]\\]/g, '\\$&')})`, 'gi');
  return text.replace(regex, '<mark>$1</mark>');
};

const clearRecentScreens = () => {
  try {
    localStorage.removeItem('iluria_recent_screens');
    recentScreens.value = [];
  } catch {
    // Ignorar erros de localStorage
  }
};

// Watchers
watch(visible, (newValue) => {
  if (newValue) {
    // Resetar estado quando abrir
    searchQuery.value = '';
    selectedIndex.value = 0;
    selectedCategory.value = null;
    
    // Carregar telas recentes (filtradas por permissões)
    recentScreens.value = getRecentScreensWithPermissions(hasPermission);
    
    // Focar no input
    nextTick(() => {
      searchInputRef.value?.focus();
    });
  }
});

// Lifecycle
onMounted(() => {
  // Carregar telas recentes (filtradas por permissões)
  recentScreens.value = getRecentScreensWithPermissions(hasPermission);
});
</script>

<style scoped>
.screen-search-modal {
  --search-border-radius: 8px;
  --result-item-height: 64px;
}

/* Container de pesquisa */
.search-container {
  margin-bottom: 1rem;
}

.search-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  background: var(--iluria-color-surface-secondary);
  border: 2px solid var(--iluria-color-border);
  border-radius: var(--search-border-radius);
  transition: border-color 0.2s ease;
}

.search-input-wrapper:focus-within {
  border-color: var(--iluria-color-primary);
}

.search-icon {
  position: absolute;
  left: 12px;
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 12px 16px 12px 44px;
  background: transparent;
  border: none;
  outline: none;
  font-size: 16px;
  color: var(--iluria-color-text-primary);
}

.search-input::placeholder {
  color: var(--iluria-color-text-secondary);
}

.clear-search {
  position: absolute;
  right: 12px;
  padding: 4px;
  cursor: pointer;
  color: var(--iluria-color-text-secondary);
  border-radius: 4px;
  transition: all 0.2s ease;
}

.clear-search:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

/* Filtro de categoria */
.category-filter {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  overflow-x: auto;
  padding-bottom: 4px;
}

.category-btn {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 6px 12px;
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  color: var(--iluria-color-text-secondary);  
  font-size: 14px;
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.category-btn:hover {
  color: var(--iluria-color-text-primary);
  border-color: var(--iluria-color-primary);
}

.category-btn.active {
  background: var(--iluria-color-primary);
  border-color: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast, white);
}

/* Container de resultados */
.results-container {
  max-height: 400px;
  overflow-y: auto;
}

.results-section {
  margin-bottom: 1rem;
}

.section-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.section-title {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.clear-recent-btn {
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 8px;
  background: transparent;
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  color: var(--iluria-color-text-muted);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
}

.clear-recent-btn:hover {
  color: var(--iluria-color-text-secondary);
  border-color: var(--iluria-color-text-muted);
  background: var(--iluria-color-hover);
}

.results-count {
  font-weight: normal;
  color: var(--iluria-color-text-muted);
}

.results-list {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.result-item {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 12px;
  border-radius: var(--search-border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: var(--result-item-height);
}

.result-item:hover,
.result-item.selected {
  background: var(--iluria-color-hover);
}

.result-item.selected {
  border: 1px solid var(--iluria-color-primary);
}

.result-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 40px;
  height: 40px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
}

.result-content {
  flex: 1;
  min-width: 0;
}

.result-title {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 2px;
}

.result-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.result-category {
  flex-shrink: 0;
  font-size: 12px;
  color: var(--iluria-color-text-muted);
  background: var(--iluria-color-surface-secondary);
  padding: 4px 8px;
  border-radius: 12px;
}

/* Estado vazio */
.empty-state {
  text-align: center;
  padding: 2rem 1rem;
  color: var(--iluria-color-text-secondary);
}

.empty-state-icon {
  display: block;
  margin: 0 auto;
}

.empty-state h3 {
  margin: 1rem 0 0.5rem 0;
  color: var(--iluria-color-text-primary);
}

.empty-state p {
  margin-bottom: 1.5rem;
}

/* Categorias populares */
.popular-categories {
  margin-top: 2rem;
}

.popular-categories h4 {
  margin-bottom: 1rem;
  font-size: 14px;
  color: var(--iluria-color-text-primary);
}

.category-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 12px;
}

.category-card {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
  padding: 16px 12px;
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: var(--search-border-radius);
  cursor: pointer;
  transition: all 0.2s ease;
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

.category-card:hover {
  border-color: var(--iluria-color-primary);
  color: var(--iluria-color-text-primary);
  transform: translateY(-1px);
}

/* Dicas de atalhos */
.shortcuts-info {
  display: flex;
  justify-content: center;
  gap: 24px;
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--iluria-color-border);
}

.shortcut-item {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  color: var(--iluria-color-text-muted);
}

kbd {
  padding: 2px 6px;
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  font-size: 11px;
  font-family: monospace;
  color: var(--iluria-color-text-secondary);
}

/* Highlight de busca */
:deep(mark) {
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast, white);
  padding: 0;
  border-radius: 0;
  font-weight: inherit;
}

/* Scrollbar customizada */
.results-container::-webkit-scrollbar {
  width: 6px;
}

.results-container::-webkit-scrollbar-track {
  background: var(--iluria-color-surface-secondary);
  border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.results-container::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-muted);
}

/* Responsividade */
@media (max-width: 640px) {
  .shortcuts-info {
    flex-wrap: wrap;
    gap: 16px;
  }
  
  .category-grid {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .results-container {
    max-height: 300px;
  }
}
</style>