<template>
  <div class="company-information-sidebar-editor">
    <!-- <PERSON><PERSON> do Editor -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { 'active': activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- <PERSON>te<PERSON>do das Abas -->
    <div class="tab-content">
      <!-- Aba: Conteúdo -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        <div class="form-group">
          <label>{{ $t('layoutEditor.title') }}</label>
          <IluriaInputText 
            v-model="formData.title" 
            :placeholder="$t('layoutEditor.titlePlaceholder')"
            @update:modelValue="updateTitle"
          />
        </div>

        <div class="form-group">
          <label>{{ $t('layoutEditor.titleColor') }}</label>
          <IluriaColorPicker 
            v-model="formData.titleColor" 
            @update:modelValue="updateColors"
          />
        </div>

        <div class="form-group">
          <label>{{ $t('layoutEditor.description') }}</label>
          <IluriaTextarea 
            v-model="formData.description" 
            :rows="4"
            :placeholder="$t('layoutEditor.descriptionPlaceholder')"
            @update:modelValue="updateDescription"
          />
        </div>

        <div class="form-group">
          <label>{{ $t('layoutEditor.descriptionColor') }}</label>
          <IluriaColorPicker 
            v-model="formData.descriptionColor" 
            @update:modelValue="updateColors"
          />
        </div>

        <div class="form-group">
          <label>{{ $t('layoutEditor.layoutType') }}</label>
          <div class="layout-selector">
            <div 
              class="layout-option" 
              :class="{ 'active': formData.layoutType === 'standard' }"
              @click="formData.layoutType = 'standard'; updateLayout()"
            >
              <div class="layout-preview standard">
                <div class="content-section">
                  <div class="text-line long"></div>
                  <div class="text-line medium"></div>
                  <div class="text-line short"></div>
                  <div class="button-preview"></div>
                </div>
                <div class="image-section">
                  <svg width="40" height="30" viewBox="0 0 40 30" fill="none">
                    <rect width="40" height="30" rx="4" fill="currentColor" opacity="0.2"/>
                    <rect x="12" y="8" width="16" height="14" rx="2" fill="currentColor" opacity="0.4"/>
                    <circle cx="16" cy="12" r="2" fill="currentColor" opacity="0.6"/>
                    <path d="M12 18l4-3 4 3 8-6v8H12v-2z" fill="currentColor" opacity="0.6"/>
                  </svg>
                </div>
              </div>
              <span class="layout-label">{{ $t('layoutEditor.standardLayout') }}</span>
            </div>
            
            <div 
              class="layout-option" 
              :class="{ 'active': formData.layoutType === 'inverted' }"
              @click="formData.layoutType = 'inverted'; updateLayout()"
            >
              <div class="layout-preview inverted">
                <div class="image-section">
                  <svg width="40" height="30" viewBox="0 0 40 30" fill="none">
                    <rect width="40" height="30" rx="4" fill="currentColor" opacity="0.2"/>
                    <rect x="12" y="8" width="16" height="14" rx="2" fill="currentColor" opacity="0.4"/>
                    <circle cx="16" cy="12" r="2" fill="currentColor" opacity="0.6"/>
                    <path d="M12 18l4-3 4 3 8-6v8H12v-2z" fill="currentColor" opacity="0.6"/>
                  </svg>
                </div>
                <div class="content-section">
                  <div class="text-line long"></div>
                  <div class="text-line medium"></div>
                  <div class="text-line short"></div>
                  <div class="button-preview"></div>
                </div>
              </div>
              <span class="layout-label">{{ $t('layoutEditor.invertedLayout') }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Aba: Imagem -->
      <div v-if="activeTab === 'image'" class="tab-panel">
        <div class="form-group">
          <label>{{ $t('layoutEditor.image') }}</label>
          <div class="image-upload-area" @click="openImageSelector">
            <div v-if="formData.imageUrl" class="image-preview">
              <img :src="formData.imageUrl" alt="Preview" />
              <div class="image-overlay">
                <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <path d="M12 5v14m-7-7h14"/>
                </svg>
                <span>Alterar imagem</span>
              </div>
            </div>
            <div v-else class="image-placeholder">
              <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="8.5" cy="8.5" r="1.5"/>
                <polyline points="21,15 16,10 5,21"/>
              </svg>
              <span>Clique para adicionar imagem</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Aba: Botão -->
      <div v-if="activeTab === 'button'" class="tab-panel">
        <div class="form-group">
          <IluriaCheckBox 
            v-model="formData.showButton" 
            :label="$t('layoutEditor.showButton')"
            @update:modelValue="updateButton"
          />
        </div>

        <div v-if="formData.showButton" class="form-group">
          <label>{{ $t('layoutEditor.buttonText') }}</label>
          <IluriaInputText 
            v-model="formData.buttonText" 
            :placeholder="$t('layoutEditor.buttonTextPlaceholder')"
            @update:modelValue="updateButton"
          />
        </div>

        <div v-if="formData.showButton" class="form-group">
          <label>{{ $t('layoutEditor.buttonUrl') }}</label>
          <IluriaInputText 
            v-model="formData.buttonUrl" 
            :placeholder="$t('layoutEditor.buttonUrlPlaceholder')"
            @update:modelValue="updateButton"
          />
        </div>

        <div v-if="formData.showButton" class="form-group">
          <label>{{ $t('layoutEditor.buttonColor') }}</label>
          <IluriaColorPicker 
            v-model="formData.buttonColor" 
            @update:modelValue="updateColors"
          />
        </div>
      </div>

      <!-- Aba: Estilo -->
      <div v-if="activeTab === 'style'" class="tab-panel">
        <div class="form-group">
          <label>{{ $t('layoutEditor.backgroundColor') }}</label>
          <IluriaColorPicker 
            v-model="formData.backgroundColor" 
            @update:modelValue="updateStyle"
          />
        </div>

        <div class="form-group">
          <label>{{ $t('layoutEditor.borderRadius') }}</label>
          <IluriaRange 
            v-model="formData.borderRadius" 
            :min="0" 
            :max="50"
            :step="1"
            unit="px"
            @update:modelValue="updateStyle"
          />
        </div>

        <div class="form-group">
          <IluriaCheckBox 
            v-model="formData.boxShadow" 
            :label="$t('layoutEditor.boxShadow')"
            @update:modelValue="updateStyle"
          />
        </div>
      </div>
    </div>

    <!-- Modal de Seleção de Imagem -->
    <div v-if="showImageModal" class="modal-overlay" @click="closeImageModal">
      <div class="modal-content" @click.stop>
        <div class="modal-header">
          <h4>{{ $t('layoutEditor.selectImage') }}</h4>
          <button @click="closeImageModal" class="close-button">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <line x1="18" y1="6" x2="6" y2="18"></line>
              <line x1="6" y1="6" x2="18" y2="18"></line>
            </svg>
          </button>
        </div>
        <div class="modal-body">
          <IluriaInputText 
            v-model="tempImageUrl" 
            :placeholder="$t('layoutEditor.imageUrlPlaceholder')"
          />
        </div>
        <div class="modal-footer">
          <IluriaButton 
            variant="secondary" 
            @click="closeImageModal"
          >
            {{ $t('layoutEditor.cancel') }}
          </IluriaButton>
          <IluriaButton 
            variant="primary" 
            @click="applyImage"
          >
            {{ $t('layoutEditor.apply') }}
          </IluriaButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaTextarea from '@/components/Textarea.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close'])

// Estado do editor
const activeTab = ref('content')
const showImageModal = ref(false)
const tempImageUrl = ref('')

// Dados do formulário
const formData = ref({
  title: '',
  description: '',
  imageUrl: '',
  buttonText: 'Saiba mais',
  buttonUrl: '',
  showButton: true,
  layoutType: 'standard',
  titleColor: '#333333',
  descriptionColor: '#666666',
  buttonColor: '#3b82f6',
  buttonTextColor: '#ffffff',
  backgroundColor: '#ffffff',
  borderRadius: 0,
  boxShadow: true
})

// Configuração das abas
const tabs = computed(() => [
  { 
    value: 'content', 
    label: t('layoutEditor.content')
  },
  { 
    value: 'image', 
    label: t('layoutEditor.image')
  },
  { 
    value: 'button', 
    label: t('layoutEditor.button')
  },

  { 
    value: 'style', 
    label: t('layoutEditor.style')
  }
])

// Função para encontrar o componente
const findCompanyComponent = () => {
  if (props.element.hasAttribute('data-component') && 
      props.element.getAttribute('data-component') === 'company-information') {
    return props.element
  }
  
  if (props.element.classList.contains('iluria-company-information')) {
    return props.element
  }
  
  const component = props.element.querySelector('[data-component="company-information"], .iluria-company-information')
  if (component) return component
  
  return props.element.closest('[data-component="company-information"], .iluria-company-information')
}

// Carregar dados do elemento
const loadElementData = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  // Carregar título
  const titleElement = component.querySelector('.company-info-title, h1, h2, h3, h4, h5, h6')
  if (titleElement) {
    formData.value.title = titleElement.textContent?.trim() || ''
  }
  
  // Carregar descrição
  const descElement = component.querySelector('.company-info-description p, .company-info-description, p')
  if (descElement) {
    formData.value.description = descElement.textContent?.trim() || ''
  }
  
  // Carregar imagem
  const imageElement = component.querySelector('.company-info-image img, img')
  if (imageElement) {
    formData.value.imageUrl = imageElement.getAttribute('src') || ''
  }
  
  // Carregar botão
  const buttonElement = component.querySelector('.company-info-button, button')
  if (buttonElement) {
    formData.value.buttonText = buttonElement.textContent?.trim() || 'Saiba mais'
    formData.value.buttonUrl = buttonElement.getAttribute('href') || ''
    formData.value.showButton = true
  } else {
    formData.value.showButton = false
  }
  
  // Carregar configurações de design
  loadDesignData(component)
}

// Carregar dados de design
const loadDesignData = (component) => {
  if (!component) return
  
  // Layout type
  const componentStyle = window.getComputedStyle(component)
  if (componentStyle.flexDirection === 'row-reverse') {
    formData.value.layoutType = 'inverted'
  }
  
  // Cores
  const titleElement = component.querySelector('.company-info-title, h1, h2, h3, h4, h5, h6')
  if (titleElement) {
    const titleStyle = window.getComputedStyle(titleElement)
    formData.value.titleColor = rgbToHex(titleStyle.color) || '#333333'
  }
  
  const descElement = component.querySelector('.company-info-description p, .company-info-description, p')
  if (descElement) {
    const descStyle = window.getComputedStyle(descElement)
    formData.value.descriptionColor = rgbToHex(descStyle.color) || '#666666'
  }
  
  const buttonElement = component.querySelector('.company-info-button, button')
  if (buttonElement) {
    const buttonStyle = window.getComputedStyle(buttonElement)
    formData.value.buttonColor = rgbToHex(buttonStyle.backgroundColor) || '#3b82f6'
    formData.value.buttonTextColor = rgbToHex(buttonStyle.color) || '#ffffff'
  }
  
  // Background e border radius
  formData.value.backgroundColor = rgbToHex(componentStyle.backgroundColor) || '#ffffff'
  formData.value.borderRadius = parseInt(componentStyle.borderRadius) || 12
  formData.value.boxShadow = componentStyle.boxShadow !== 'none'
}

// Converter RGB para HEX
const rgbToHex = (rgb) => {
  if (!rgb || rgb === 'rgba(0, 0, 0, 0)' || rgb === 'transparent') return null
  
  const result = rgb.match(/\d+/g)
  if (!result || result.length < 3) return null
  
  const r = parseInt(result[0])
  const g = parseInt(result[1])
  const b = parseInt(result[2])
  
  return "#" + ((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)
}

// Funções de atualização
const updateTitle = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  let titleElement = component.querySelector('.company-info-title, h1, h2, h3, h4, h5, h6')
  if (titleElement) {
    titleElement.textContent = formData.value.title
  }
  
  emit('data-changed', { type: 'title', value: formData.value.title })
}

const updateDescription = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  let descElement = component.querySelector('.company-info-description p, .company-info-description, p')
  if (descElement) {
    if (descElement.tagName === 'P') {
      descElement.textContent = formData.value.description
    } else {
      descElement.innerHTML = `<p>${formData.value.description}</p>`
    }
  }
  
  emit('data-changed', { type: 'description', value: formData.value.description })
}

const updateButton = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  const buttonElement = component.querySelector('.company-info-button, button')
  if (buttonElement) {
    buttonElement.textContent = formData.value.buttonText
    buttonElement.style.display = formData.value.showButton ? 'inline-block' : 'none'
    if (formData.value.buttonUrl) {
      buttonElement.setAttribute('href', formData.value.buttonUrl)
    }
  }
  
  emit('data-changed', { type: 'button', value: formData.value })
}

const updateLayout = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  if (formData.value.layoutType === 'inverted') {
    component.style.flexDirection = 'row-reverse'
  } else {
    component.style.flexDirection = 'row'
  }
  
  emit('data-changed', { type: 'layout', value: formData.value.layoutType })
}

const updateColors = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  const titleElement = component.querySelector('.company-info-title, h1, h2, h3, h4, h5, h6')
  if (titleElement) {
    titleElement.style.color = formData.value.titleColor
  }
  
  const descElement = component.querySelector('.company-info-description p, .company-info-description, p')
  if (descElement) {
    descElement.style.color = formData.value.descriptionColor
  }
  
  const buttonElement = component.querySelector('.company-info-button, button')
  if (buttonElement) {
    buttonElement.style.backgroundColor = formData.value.buttonColor
    buttonElement.style.color = formData.value.buttonTextColor
  }
  
  emit('data-changed', { type: 'colors', value: formData.value })
}

const updateStyle = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  component.style.backgroundColor = formData.value.backgroundColor
  component.style.borderRadius = `${formData.value.borderRadius}px`
  
  if (formData.value.boxShadow) {
    component.style.boxShadow = '0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06)'
  } else {
    component.style.boxShadow = 'none'
  }
  
  emit('data-changed', { type: 'style', value: formData.value })
}

const updateImage = () => {
  const component = findCompanyComponent()
  if (!component) return
  
  const imageElement = component.querySelector('.company-info-image img, img')
  if (imageElement) {
    imageElement.setAttribute('src', formData.value.imageUrl)
  }
  
  emit('data-changed', { type: 'image', value: formData.value.imageUrl })
}

// Funções do modal de imagem
const openImageSelector = () => {
  tempImageUrl.value = formData.value.imageUrl
  showImageModal.value = true
}

const closeImageModal = () => {
  showImageModal.value = false
  tempImageUrl.value = ''
}

const applyImage = () => {
  formData.value.imageUrl = tempImageUrl.value
  updateImage()
  closeImageModal()
}

// Inicialização
onMounted(() => {
  loadElementData()
})
</script>

<style scoped>
.company-information-sidebar-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--iluria-color-surface);
}

.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.editor-tabs::-webkit-scrollbar {
  display: none;
}

.tab-button {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-group label {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.form-input,
.form-textarea {
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  background: var(--iluria-color-input-bg);
  color: var(--iluria-color-input-text);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.form-input:focus,
.form-textarea:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
}

.form-textarea {
  resize: vertical;
  min-height: 80px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.checkbox-group input[type="checkbox"] {
  width: 16px;
  height: 16px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.radio-option input[type="radio"] {
  width: 16px;
  height: 16px;
}

.radio-option label {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

/* Seletores de Layout Visuais */
.layout-selector {
  display: flex;
  gap: 1rem;
  margin-top: 0.5rem;
}

.layout-option {
  flex: 1;
  padding: 1rem;
  border: 2px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--iluria-color-surface);
}

.layout-option:hover {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-hover);
}

.layout-option.active {
  border-color: var(--iluria-color-primary);
  background: rgba(59, 130, 246, 0.05);
}

.layout-preview {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
  height: 40px;
  align-items: center;
}

.layout-preview.standard {
  flex-direction: row;
}

.layout-preview.inverted {
  flex-direction: row;
}

.layout-preview.inverted .image-section {
  order: 1;
}

.layout-preview.inverted .content-section {
  order: 2;
}

.content-section {
  flex: 2;
  display: flex;
  flex-direction: column;
  gap: 3px;
  justify-content: center;
}

.image-section {
  flex: 1;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-secondary);
}

.text-line {
  height: 3px;
  background: var(--iluria-color-text-secondary);
  border-radius: 2px;
  opacity: 0.6;
}

.text-line.long {
  width: 90%;
}

.text-line.medium {
  width: 70%;
}

.text-line.short {
  width: 50%;
}

.button-preview {
  width: 40%;
  height: 8px;
  background: var(--iluria-color-primary);
  border-radius: 4px;
  margin-top: 2px;
  opacity: 0.8;
}

.layout-label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  text-align: center;
  display: block;
}

.color-input-group {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.color-input {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
}

.color-value {
  font-size: 0.875rem;
  font-family: monospace;
  color: var(--iluria-color-text-secondary);
}

.range-input {
  width: 100%;
}

.range-value {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  text-align: center;
}

.image-upload-area {
  border: 2px dashed var(--iluria-color-border);
  border-radius: 8px;
  padding: 1rem;
  text-align: center;
  cursor: pointer;
  transition: all 0.2s ease;
}

.image-upload-area:hover {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-hover);
}

.image-preview {
  position: relative;
  max-width: 200px;
  margin: 0 auto;
}

.image-preview img {
  width: 100%;
  height: auto;
  border-radius: 6px;
}

.image-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  border-radius: 6px;
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-preview:hover .image-overlay {
  opacity: 1;
}

.image-placeholder {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.75rem;
  color: var(--iluria-color-text-secondary);
}

.image-placeholder svg {
  opacity: 0.5;
}

.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-content {
  background: var(--iluria-color-surface);
  border-radius: 12px;
  max-width: 500px;
  width: 90%;
  max-height: 80vh;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-lg);
}

.modal-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 1rem 1.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.modal-header h4 {
  margin: 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.close-button {
  background: none;
  border: none;
  cursor: pointer;
  color: var(--iluria-color-text-secondary);
  padding: 0.25rem;
  border-radius: 4px;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.modal-body {
  padding: 1.5rem;
}

.modal-footer {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
  padding: 1rem 1.5rem;
  border-top: 1px solid var(--iluria-color-border);
}

.btn {
  padding: 0.5rem 1rem;
  border: none;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-secondary {
  background: var(--iluria-color-secondary);
  color: var(--iluria-color-secondary-fg);
}

.btn-secondary:hover {
  background: var(--iluria-color-secondary-hover);
}

.btn-primary {
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-fg);
}

.btn-primary:hover {
  background: var(--iluria-color-primary-hover);
}
</style> 
