<template>
  <LoginPage>
    <div>
      <div class="mb-4">
        <IluriaInputText
          v-model="email"
          type="email"
          :label="$t('signup.enterEmail')"
          autocomplete="username"
          autocorrect="off"
          autocapitalize="off"
          spellcheck="false"
          inputClass="bg-white"
          placeholder="<EMAIL>"
        />
        <div v-if="email && !isEmailValid" class="error-message">
          {{ t("signup.emailInvalid") }}
        </div>
        <div v-if="signupStatus === 'success'" class="mt-2 p-2 bg-green-100 text-green-800 rounded-md">
          {{ $t("signup.signupEmailSent") }}
        </div>
        <div v-if="signupStatus === 'error'" class="mt-2 p-2 bg-red-100 text-red-800 rounded-md">
          {{ $t("signup.errorSendingSignupEmail") }}
        </div>
      </div>
      <IluriaButton
        type="button"
        @click="requestSignup"
        :disabled="loading || !isEmailValid"
        class="w-full">
        {{ loading ? $t("login.sending") : $t("login.createAccount") }}
      </IluriaButton>
    </div>
    <div v-if="loading" class="loading-spinner mt-4">{{ $t("login.sending") }}</div>
  </LoginPage>
</template>

<script setup>
import { ref, computed } from "vue";
import { useToast } from "@/services/toast.service";
import { useAuthStore } from "@/stores/auth.store";
import { useI18n } from "vue-i18n";
import IluriaButton from "@/components/iluria/IluriaButton.vue";
import LoginPage from "@/components/login/LoginPage.vue";
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { addToast } = useToast();
const authStore = useAuthStore();
const { t } = useI18n();

const loading = ref(false);
const signupStatus = ref("idle");
const email = ref("");

const isEmailValid = computed(() => {
  if (!email.value) return false;
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
  return emailRegex.test(email.value);
});

const requestSignup = async () => {
  if (!isEmailValid.value) {
    addToast(t("signup.emailInvalid"), "error");
    return;
  }

  loading.value = true;

  try {
    const response = await authStore.signupRequest(email.value);

    if (response && response.success) {
      signupStatus.value = "success";
      addToast(t("signup.signupEmailSent"), "success");
    } else {
      signupStatus.value = "error";
      addToast(response ? response.message : t("UnknownError"), "error");
    }
  } catch (error) {
    console.error('Signup error:', error);
    signupStatus.value = "error";
    addToast(error?.message || t("UnknownError"), "error");
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.error {
  border-color: red;
}

.loading-spinner {
  text-align: center;
  color: #ce2d4f;
}

.error-message {
  color: red;
  font-size: 0.875rem;
  margin-top: 5px;
}

/* Removed redundant styling as IluriaInputText handles most of it */
</style>
