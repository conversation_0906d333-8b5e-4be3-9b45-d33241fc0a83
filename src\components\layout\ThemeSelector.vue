<template>
  <div class="theme-selector" ref="themeSelectorRef">
    <!-- Theme Toggle Button -->
    <button
      ref="toggleButtonRef"
      @click="toggleDropdown"
      class="theme-toggle-button"
      :class="{ 'active': isOpen }"
      :title="t('themeSelector.currentSelection', { theme: t(`themeSelector.themes.${currentTheme.id}`), font: t(`themeSelector.fonts.${currentFont.id}.name`) })"
    >
      <div class="theme-icon">
        <component :is="currentThemeIcon" />
      </div>
    </button>

            <!-- Dropdown Menu -->
    <Transition name="dropdown-slide">
      <div v-if="isOpen" class="theme-dropdown" ref="dropdownRef">
        <div class="dropdown-header">
          <div class="header-content">
            <div class="header-left">
              <h3 class="dropdown-title">{{ t('themeSelector.title') }}</h3>
              <p class="dropdown-subtitle">{{ isThemeMode ? t('themeSelector.themeSubtitle') : t('themeSelector.fontSubtitle') }}</p>
            </div>
            <button 
              @click="toggleMode"
              class="mode-toggle-button"
              :title="isThemeMode ? t('themeSelector.switchToFonts') : t('themeSelector.switchToThemes')"
            >
              <Transition name="mode-icon-rotate" mode="out-in">
                <Type v-if="isThemeMode" :key="'type'" :size="18" class="mode-icon" />
                <PaintBucket v-else :key="'paint'" :size="18" class="mode-icon" />
              </Transition>
            </button>
          </div>
        </div>

        <!-- Theme Content -->
        <Transition name="content-slide" mode="out-in">
          <div v-if="isThemeMode" key="themes" class="themes-list">
            <button
              v-for="theme in availableThemes"
              :key="theme.id"
              @click="selectTheme(theme.id)"
              class="theme-option"
              :class="{ 
                'active': currentThemeId === theme.id,
                'transitioning': isTransitioning && currentThemeId === theme.id
              }"
            >
              <div class="theme-option-icon">
                <component :is="getThemeIcon(theme.id)" />
              </div>
              <div class="theme-option-content">
                <span class="theme-name">{{ t(`themeSelector.themes.${theme.id}`) }}</span>
                <div class="theme-preview">
                  <div 
                    class="color-dot bg" 
                    :style="{ backgroundColor: theme.colors['--iluria-color-body-bg'] }"
                  ></div>
                  <div 
                    class="color-dot tertiary" 
                    :style="{ backgroundColor: theme.colors['--iluria-color-secondary'] }"
                  ></div>
                  <div 
                    class="color-dot primary" 
                    :style="{ backgroundColor: theme.colors['--iluria-color-primary'] }"
                  ></div>
                </div>
              </div>
              <div class="theme-check" v-if="currentThemeId === theme.id">
                <Check :size="16" :strokeWidth="2.5" />
              </div>
              <div class="theme-loading" v-else-if="isTransitioning && currentThemeId === theme.id">
                <div class="loading-spinner"></div>
              </div>
            </button>
          </div>

          <!-- Fonts Content -->
          <div v-else key="fonts" class="fonts-list">
            <div v-for="(fonts, category) in fontsByCategory" :key="category" class="font-category">
              <div class="font-category-title">{{ t(`themeSelector.fontCategories.${category}`) }}</div>
              <button
                v-for="font in fonts"
                :key="font.id"
                @click="selectFont(font.id)"
                class="font-option"
                :class="{ 
                  'active': currentFontId === font.id,
                  'loading': isLoadingFont && currentFontId === font.id
                }"
              >
                <div class="font-preview" :style="{ fontFamily: font.cssName }">
                  {{ font.previewText }}
                </div>
                <div class="font-info">
                  <span class="font-name">{{ t(`themeSelector.fonts.${font.id}.name`) }}</span>
                  <span class="font-description">{{ t(`themeSelector.fonts.${font.id}.description`) }}</span>
                </div>
                <div class="font-check" v-if="currentFontId === font.id">
                  <Check :size="16" :strokeWidth="2.5" />
                </div>
                <div class="font-loading" v-else-if="isLoadingFont && currentFontId === font.id">
                  <div class="loading-spinner"></div>
                </div>
              </button>
            </div>
          </div>
        </Transition>

        <div class="dropdown-footer">
          <p class="footer-text">
            {{ t('themeSelector.footerText') }}
          </p>
        </div>
      </div>
    </Transition>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, nextTick, computed, h } from 'vue'
import { Check, PaintBucket, Type } from 'lucide-vue-next'
import { useTheme } from '@/composables/useTheme'
import { useFont } from '@/composables/useFont'
import { useI18n } from 'vue-i18n'

// i18n
const { t } = useI18n()

// Theme composable
const { 
  currentTheme, 
  currentThemeId, 
  availableThemes, 
  isTransitioning,
  setTheme 
} = useTheme()

// Font composable
const { 
  currentFont, 
  currentFontId, 
  availableFonts, 
  fontsByCategory,
  isLoadingFont,
  setFont 
} = useFont()

// Component state
const isOpen = ref(false)
const isThemeMode = ref(true) // true = temas, false = fontes
const themeSelectorRef = ref(null)
const toggleButtonRef = ref(null)
const dropdownRef = ref(null)

// Scroll detection
const lastScrollY = ref(0)
const scrollThreshold = 10

// Theme Icons as Vue components
const LightIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      h('circle', {
        cx: '12',
        cy: '12',
        r: '5',
        fill: '#FBBF24'
      }),
      h('path', {
        d: 'M12 1v6m0 6v6m11-7h-6m-6 0H1m16.364-7.364l-4.243 4.243M7.879 16.121l-4.243 4.243m0-16.728l4.243 4.243m8.485 8.485l4.243 4.243',
        stroke: '#FBBF24',
        'stroke-width': '2',
        'stroke-linecap': 'round'
      })
    ])
  }
}

const DarkIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      h('path', {
        d: 'M21 12.79A9 9 0 1 1 11.21 3 7 7 0 0 0 21 12.79z',
        fill: '#6366F1',
        stroke: '#6366F1',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round'
      }),
      h('circle', {
        cx: '18',
        cy: '5',
        r: '1',
        fill: '#FBBF24'
      }),
      h('circle', {
        cx: '20',
        cy: '9',
        r: '0.5',
        fill: '#FBBF24'
      }),
      h('circle', {
        cx: '15',
        cy: '7',
        r: '0.5',
        fill: '#FBBF24'
      })
    ])
  }
}

const CupcakeIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Cupcake base
      h('path', {
        d: 'M7 12h10l-1 8H8l-1-8z',
        fill: '#8B4513',
        stroke: '#654321',
        'stroke-width': '1'
      }),
      // Frosting top
      h('path', {
        d: 'M6 12c0-3.5 2.5-6 6-6s6 2.5 6 6c0 1-0.5 2-1.5 2.5-0.5 1-1.5 1.5-2.5 1.5-1 0-2-0.5-2.5-1.5-0.5 1-1.5 1.5-2.5 1.5-1 0-2-0.5-2.5-1.5C6.5 14 6 13 6 12z',
        fill: '#FFB6C1',
        stroke: '#FF69B4',
        'stroke-width': '1'
      }),
      // Cherry on top
      h('circle', {
        cx: '12',
        cy: '8',
        r: '1.5',
        fill: '#DC143C'
      }),
      // Cherry stem
      h('path', {
        d: 'M12 6.5c0-0.5 0.2-1 0.5-1.5',
        stroke: '#228B22',
        'stroke-width': '1.5',
        'stroke-linecap': 'round'
      }),
      // Sprinkles
      h('rect', {
        x: '9',
        y: '10',
        width: '0.5',
        height: '2',
        fill: '#FFD700',
        transform: 'rotate(15 9 11)'
      }),
      h('rect', {
        x: '14',
        y: '9',
        width: '0.5',
        height: '2',
        fill: '#00CED1',
        transform: 'rotate(-20 14 10)'
      }),
      h('rect', {
        x: '11',
        y: '11',
        width: '0.5',
        height: '2',
        fill: '#FF4500',
        transform: 'rotate(45 11 12)'
      })
    ])
  }
}

const BumblebeeIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Bee body
      h('ellipse', {
        cx: '12',
        cy: '12',
        rx: '6',
        ry: '4',
        fill: '#F59E0B',
        stroke: '#D97706',
        'stroke-width': '1'
      }),
      // Black stripes
      h('rect', {
        x: '8',
        y: '10',
        width: '8',
        height: '1',
        fill: '#000000',
        rx: '0.5'
      }),
      h('rect', {
        x: '8',
        y: '12',
        width: '8',
        height: '1',
        fill: '#000000',
        rx: '0.5'
      }),
      h('rect', {
        x: '8',
        y: '14',
        width: '8',
        height: '1',
        fill: '#000000',
        rx: '0.5'
      }),
      // Wings
      h('ellipse', {
        cx: '8',
        cy: '8',
        rx: '3',
        ry: '2',
        fill: '#E5E7EB',
        stroke: '#9CA3AF',
        'stroke-width': '0.5',
        opacity: '0.8'
      }),
      h('ellipse', {
        cx: '16',
        cy: '8',
        rx: '3',
        ry: '2',
        fill: '#E5E7EB',
        stroke: '#9CA3AF',
        'stroke-width': '0.5',
        opacity: '0.8'
      }),
      // Antennae
      h('path', {
        d: 'M10 6l-1-2M14 6l1-2',
        stroke: '#000000',
        'stroke-width': '1.5',
        'stroke-linecap': 'round'
      }),
      // Antennae tips
      h('circle', {
        cx: '9',
        cy: '4',
        r: '0.8',
        fill: '#000000'
      }),
      h('circle', {
        cx: '15',
        cy: '4',
        r: '0.8',
        fill: '#000000'
      })
    ])
  }
}

const EmeraldIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Emerald gem main body
      h('path', {
        d: 'M12 3l-6 6v6l6 6 6-6V9l-6-6z',
        fill: '#10B981',
        stroke: '#059669',
        'stroke-width': '1'
      }),
      // Top facet
      h('path', {
        d: 'M12 3l-4 4h8l-4-4z',
        fill: '#34D399',
        stroke: '#059669',
        'stroke-width': '0.5'
      }),
      // Side facets
      h('path', {
        d: 'M6 9l6-2v10l-6-2V9z',
        fill: '#059669',
        stroke: '#047857',
        'stroke-width': '0.5'
      }),
      h('path', {
        d: 'M18 9l-6-2v10l6-2V9z',
        fill: '#065F46',
        stroke: '#047857',
        'stroke-width': '0.5'
      }),
      // Inner highlights
      h('path', {
        d: 'M12 7l-2 2v4l2-2V7z',
        fill: '#6EE7B7',
        opacity: '0.6'
      }),
      h('path', {
        d: 'M12 7l2 2v4l-2-2V7z',
        fill: '#34D399',
        opacity: '0.4'
      }),
      // Sparkle effects
      h('circle', {
        cx: '8',
        cy: '6',
        r: '0.5',
        fill: '#ECFDF5'
      }),
      h('circle', {
        cx: '16',
        cy: '10',
        r: '0.5',
        fill: '#ECFDF5'
      }),
      h('circle', {
        cx: '10',
        cy: '18',
        r: '0.5',
        fill: '#ECFDF5'
      })
    ])
  }
}

const CorporateIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Building main structure
      h('rect', {
        x: '4',
        y: '6',
        width: '16',
        height: '16',
        fill: '#2563EB',
        stroke: '#1D4ED8',
        'stroke-width': '1',
        rx: '1'
      }),
      // Windows row 1
      h('rect', {
        x: '6',
        y: '8',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '10',
        y: '8',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '14',
        y: '8',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '18',
        y: '8',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      // Windows row 2
      h('rect', {
        x: '6',
        y: '11',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '10',
        y: '11',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '14',
        y: '11',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '18',
        y: '11',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      // Windows row 3
      h('rect', {
        x: '6',
        y: '14',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '10',
        y: '14',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '14',
        y: '14',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      h('rect', {
        x: '18',
        y: '14',
        width: '2',
        height: '1.5',
        fill: '#F8FAFC',
        rx: '0.2'
      }),
      // Entrance door
      h('rect', {
        x: '11',
        y: '17',
        width: '2',
        height: '5',
        fill: '#0F172A',
        rx: '0.3'
      }),
      // Top antenna
      h('rect', {
        x: '11.5',
        y: '3',
        width: '1',
        height: '3',
        fill: '#64748B'
      })
    ])
  }
}

const GardenIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Main leaf
      h('path', {
        d: 'M12 3c0 0-8 2-8 10 0 4 3 8 8 8s8-4 8-8c0-8-8-10-8-10z',
        fill: '#16A34A',
        stroke: '#15803D',
        'stroke-width': '1'
      }),
      // Leaf vein
      h('path', {
        d: 'M12 3c0 0 0 8 0 18',
        stroke: '#15803D',
        'stroke-width': '1.5',
        'stroke-linecap': 'round'
      }),
      // Secondary veins
      h('path', {
        d: 'M8 7c2 1 4 2 4 4',
        stroke: '#15803D',
        'stroke-width': '0.8',
        'stroke-linecap': 'round'
      }),
      h('path', {
        d: 'M16 7c-2 1-4 2-4 4',
        stroke: '#15803D',
        'stroke-width': '0.8',
        'stroke-linecap': 'round'
      }),
      h('path', {
        d: 'M7 12c2 1 5 1 5 3',
        stroke: '#15803D',
        'stroke-width': '0.8',
        'stroke-linecap': 'round'
      }),
      h('path', {
        d: 'M17 12c-2 1-5 1-5 3',
        stroke: '#15803D',
        'stroke-width': '0.8',
        'stroke-linecap': 'round'
      }),
      // Small flower
      h('circle', {
        cx: '18',
        cy: '6',
        r: '1.2',
        fill: '#CA8A04'
      }),
      h('circle', {
        cx: '17',
        cy: '5',
        r: '0.6',
        fill: '#FBBF24'
      }),
      h('circle', {
        cx: '19',
        cy: '5',
        r: '0.6',
        fill: '#FBBF24'
      }),
      h('circle', {
        cx: '17',
        cy: '7',
        r: '0.6',
        fill: '#FBBF24'
      }),
      h('circle', {
        cx: '19',
        cy: '7',
        r: '0.6',
        fill: '#FBBF24'
      })
    ])
  }
}

const LofiIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Headphones band
      h('path', {
        d: 'M3 12a9 9 0 0 1 18 0',
        stroke: '#6B7280',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        fill: 'none'
      }),
      // Left ear cup
      h('rect', {
        x: '2',
        y: '12',
        width: '4',
        height: '6',
        fill: '#6B7280',
        rx: '2'
      }),
      h('rect', {
        x: '2.5',
        y: '12.5',
        width: '3',
        height: '5',
        fill: '#9CA3AF',
        rx: '1.5'
      }),
      // Right ear cup
      h('rect', {
        x: '18',
        y: '12',
        width: '4',
        height: '6',
        fill: '#6B7280',
        rx: '2'
      }),
      h('rect', {
        x: '18.5',
        y: '12.5',
        width: '3',
        height: '5',
        fill: '#9CA3AF',
        rx: '1.5'
      }),
      // Sound waves
      h('path', {
        d: 'M8 10c1 1 1 3 0 4',
        stroke: '#D1D5DB',
        'stroke-width': '1.5',
        'stroke-linecap': 'round',
        fill: 'none'
      }),
      h('path', {
        d: 'M9 8c2 2 2 6 0 8',
        stroke: '#E5E7EB',
        'stroke-width': '1.5',
        'stroke-linecap': 'round',
        fill: 'none'
      }),
      h('path', {
        d: 'M16 10c-1 1-1 3 0 4',
        stroke: '#D1D5DB',
        'stroke-width': '1.5',
        'stroke-linecap': 'round',
        fill: 'none'
      }),
      h('path', {
        d: 'M15 8c-2 2-2 6 0 8',
        stroke: '#E5E7EB',
        'stroke-width': '1.5',
        'stroke-linecap': 'round',
        fill: 'none'
      })
    ])
  }
}

const DraculaIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // bat 
      h('path', {
        d: 'M17 16c.74-2.286 2.778-3.762 5-3c-.173-2.595.13-5.314-2-7.5c-1.708 2.648-3.358 2.557-5 2.5V4l-3 2l-3-2v4c-1.642.057-3.292.148-5-2.5c-2.13 2.186-1.827 4.905-2 7.5c2.222-.762 4.26.714 5 3c2.593 0 3.889.952 5 4c1.111-3.048 2.407-4 5-4z',
        fill: 'none',
        stroke: '#FF79C6',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round'
      }),
      
      // Bat face detail
      h('path', {
        d: 'M9 8a3 3 0 0 0 6 0',
        fill: 'none',
        stroke: '#FF79C6',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round'
      })
    ])
  }
}

const PastelIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Rainbow cloud
      h('path', {
        d: 'M6 15c-2 0-3-1-3-3s1-3 3-3c0-3 2-5 5-5s5 2 5 5c2 0 3 1 3 3s-1 3-3 3H6z',
        fill: '#D8B4FE',
        stroke: '#C084FC',
        'stroke-width': '1'
      }),
      // Pastel dots
      h('circle', {
        cx: '8',
        cy: '12',
        r: '1.2',
        fill: '#FDA4AF'
      }),
      h('circle', {
        cx: '12',
        cy: '11',
        r: '1.2',
        fill: '#86EFAC'
      }),
      h('circle', {
        cx: '16',
        cy: '12',
        r: '1.2',
        fill: '#93C5FD'
      }),
      // Small sparkles
      h('circle', {
        cx: '7',
        cy: '8',
        r: '0.4',
        fill: '#FCD34D'
      }),
      h('circle', {
        cx: '17',
        cy: '8',
        r: '0.4',
        fill: '#FCD34D'
      }),
      h('circle', {
        cx: '10',
        cy: '18',
        r: '0.4',
        fill: '#FCD34D'
      }),
      h('circle', {
        cx: '14',
        cy: '18',
        r: '0.4',
        fill: '#FCD34D'
      }),
      // Soft glow effect
      h('circle', {
        cx: '12',
        cy: '12',
        r: '8',
        fill: 'none',
        stroke: '#D8B4FE',
        'stroke-width': '0.5',
        opacity: '0.3'
      })
    ])
  }
}

const NightIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Night sky background
      h('circle', {
        cx: '12',
        cy: '12',
        r: '10',
        fill: '#0F172A'
      }),
      // Moon
      h('circle', {
        cx: '9',
        cy: '8',
        r: '3',
        fill: '#38BDF8'
      }),
      h('circle', {
        cx: '10',
        cy: '7.5',
        r: '0.4',
        fill: '#0F172A'
      }),
      h('circle', {
        cx: '8.5',
        cy: '9',
        r: '0.3',
        fill: '#0F172A'
      }),
      h('circle', {
        cx: '9.5',
        cy: '9.5',
        r: '0.2',
        fill: '#0F172A'
      }),
      // Stars
      h('circle', {
        cx: '16',
        cy: '6',
        r: '0.6',
        fill: '#A78BFA'
      }),
      h('circle', {
        cx: '18',
        cy: '9',
        r: '0.4',
        fill: '#F1F5F9'
      }),
      h('circle', {
        cx: '15',
        cy: '16',
        r: '0.5',
        fill: '#CBD5E1'
      }),
      h('circle', {
        cx: '19',
        cy: '14',
        r: '0.3',
        fill: '#F1F5F9'
      }),
      h('circle', {
        cx: '5',
        cy: '15',
        r: '0.4',
        fill: '#A78BFA'
      }),
      h('circle', {
        cx: '7',
        cy: '18',
        r: '0.3',
        fill: '#F1F5F9'
      }),
      // Twinkling effect
      h('path', {
        d: 'M16 6l0.5-1 0.5 1-0.5 1-0.5-1zM5 15l0.3-0.8 0.3 0.8-0.3 0.8-0.3-0.8z',
        fill: '#A78BFA'
      })
    ])
  }
}

const WinterIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Main snowflake center
      h('circle', {
        cx: '12',
        cy: '12',
        r: '1',
        fill: '#0EA5E9'
      }),
      // Main lines
      h('path', {
        d: 'M12 4v16M4 12h16M7.76 7.76l8.48 8.48M16.24 7.76l-8.48 8.48',
        stroke: '#0EA5E9',
        'stroke-width': '1.5',
        'stroke-linecap': 'round'
      }),
      // Branch details
      h('path', {
        d: 'M12 6l-1-1M12 6l1-1M12 18l-1 1M12 18l1 1',
        stroke: '#0284C7',
        'stroke-width': '1',
        'stroke-linecap': 'round'
      }),
      h('path', {
        d: 'M6 12l-1-1M6 12l-1 1M18 12l1-1M18 12l1 1',
        stroke: '#0284C7',
        'stroke-width': '1',
        'stroke-linecap': 'round'
      }),
      h('path', {
        d: 'M9.17 9.17l-1-0.5M9.17 9.17l-0.5-1M14.83 14.83l1 0.5M14.83 14.83l0.5 1',
        stroke: '#0284C7',
        'stroke-width': '1',
        'stroke-linecap': 'round'
      }),
      h('path', {
        d: 'M14.83 9.17l1-0.5M14.83 9.17l0.5-1M9.17 14.83l-1 0.5M9.17 14.83l-0.5 1',
        stroke: '#0284C7',
        'stroke-width': '1',
        'stroke-linecap': 'round'
      }),
      // Ice crystals
      h('circle', {
        cx: '8',
        cy: '8',
        r: '0.5',
        fill: '#F8FAFC',
        opacity: '0.8'
      }),
      h('circle', {
        cx: '16',
        cy: '8',
        r: '0.5',
        fill: '#F8FAFC',
        opacity: '0.8'
      }),
      h('circle', {
        cx: '8',
        cy: '16',
        r: '0.5',
        fill: '#F8FAFC',
        opacity: '0.8'
      }),
      h('circle', {
        cx: '16',
        cy: '16',
        r: '0.5',
        fill: '#F8FAFC',
        opacity: '0.8'
      })
    ])
  }
}

const SunsetIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Sun
      h('circle', {
        cx: '12',
        cy: '10',
        r: '4',
        fill: '#F97316',
        stroke: '#EA580C',
        'stroke-width': '1'
      }),
      // Sun rays
      h('path', {
        d: 'M12 2v2M12 16v2M20 10h2M4 10H2m15.07-5.07l1.41-1.41M5.64 5.64L4.22 4.22m12.02 12.02l1.41 1.41M5.64 18.36l-1.42 1.42',
        stroke: '#F59E0B',
        'stroke-width': '1.5',
        'stroke-linecap': 'round'
      }),
      // Horizon line
      h('path', {
        d: 'M2 18h20',
        stroke: '#C2410C',
        'stroke-width': '2'
      }),
      // Orange gradient clouds
      h('ellipse', {
        cx: '18',
        cy: '15',
        rx: '4',
        ry: '2',
        fill: '#FB923C',
        opacity: '0.6'
      }),
      h('ellipse', {
        cx: '6',
        cy: '16',
        rx: '3',
        ry: '1.5',
        fill: '#FDBA74',
        opacity: '0.7'
      }),
      // Mountain silhouette
      h('path', {
        d: 'M0 18l5-6 4 4 6-8 4 4 5-3v9H0z',
        fill: '#9A3412',
        opacity: '0.8'
      })
    ])
  }
}

const NordIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Mountain peaks
      h('path', {
        d: 'M2 18l5-8 4 6 6-10 5 6v6H2z',
        fill: '#5E81AC'
      }),
      h('path', {
        d: 'M2 18l4-6 3 4 5-8 4 5v5H2z',
        fill: '#4C566A',
        opacity: '0.8'
      }),
      h('path', {
        d: 'M2 18l3-4 2 2 4-6 3 3v5H2z',
        fill: '#2E3440',
        opacity: '0.6'
      }),
      // Snow caps
      h('path', {
        d: 'M7 10l1-2 1 1-1 1zM13 8l1.5-3 1.5 1.5-1.5 1.5zM18 12l1-2 1 1-1 1z',
        fill: '#ECEFF4'
      }),
      // Aurora (northern lights)
      h('path', {
        d: 'M0 12c3-2 6-3 9-1s6 1 9-1c3-2 6-1 6 0v-2c-3 1-6 0-9 2s-6 0-9-2c-3-2-6-1-6 0v4z',
        fill: '#88C0D0',
        opacity: '0.4'
      }),
      h('path', {
        d: 'M0 8c2-1 4-1 6 0s4 0 6-1c2-1 4 0 6 1c2 1 4 0 6-1v-1c-2 1-4 2-6 1s-4-1-6 0c-2 1-4 2-6 1s-4-1-6 0v1z',
        fill: '#A3BE8C',
        opacity: '0.3'
      }),
      // Stars
      h('circle', {
        cx: '4',
        cy: '6',
        r: '0.5',
        fill: '#ECEFF4'
      }),
      h('circle', {
        cx: '20',
        cy: '4',
        r: '0.8',
        fill: '#ECEFF4'
      }),
      h('circle', {
        cx: '16',
        cy: '6',
        r: '0.4',
        fill: '#D8DEE9'
      })
    ])
  }
}

const DimIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Dark background
      h('circle', {
        cx: '12',
        cy: '12',
        r: '11',
        fill: '#1F2937'
      }),
      // Crescent moon
      h('path', {
        d: 'M12 2C8.5 2 5.5 4.5 5.5 8s3 6 6.5 6c1.5 0 2.8-0.5 3.8-1.3C14.2 14.2 12.5 16 10.5 16c-3.3 0-6-2.7-6-6s2.7-6 6-6c0.8 0 1.5 0.2 2.2 0.5C12.2 3.5 12.1 2.7 12 2z',
        fill: '#9CA3AF'
      }),
      // Moon surface details
      h('circle', {
        cx: '9',
        cy: '7',
        r: '0.8',
        fill: '#6B7280',
        opacity: '0.6'
      }),
      h('circle', {
        cx: '10.5',
        cy: '10',
        r: '0.5',
        fill: '#6B7280',
        opacity: '0.8'
      }),
      h('circle', {
        cx: '8',
        cy: '9.5',
        r: '0.3',
        fill: '#6B7280',
        opacity: '0.7'
      }),
      // Soft glow around moon
      h('circle', {
        cx: '9.5',
        cy: '8.5',
        r: '4',
        fill: 'none',
        stroke: '#9CA3AF',
        'stroke-width': '0.5',
        opacity: '0.2'
      }),
      // Dim stars
      h('circle', {
        cx: '18',
        cy: '6',
        r: '0.4',
        fill: '#6B7280',
        opacity: '0.6'
      }),
      h('circle', {
        cx: '16',
        cy: '18',
        r: '0.3',
        fill: '#6B7280',
        opacity: '0.5'
      }),
      h('circle', {
        cx: '20',
        cy: '14',
        r: '0.3',
        fill: '#6B7280',
        opacity: '0.4'
      })
    ])
  }
}

const AbyssIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Black hole event horizon
      h('circle', {
        cx: '12',
        cy: '12',
        r: '11',
        fill: '#000000'
      }),
      // Accretion disk rings
      h('ellipse', {
        cx: '12',
        cy: '12',
        rx: '9',
        ry: '3',
        fill: 'none',
        stroke: '#3B82F6',
        'stroke-width': '0.8',
        opacity: '0.4'
      }),
      h('ellipse', {
        cx: '12',
        cy: '12',
        rx: '7',
        ry: '2.2',
        fill: 'none',
        stroke: '#6366F1',
        'stroke-width': '1',
        opacity: '0.6'
      }),
      h('ellipse', {
        cx: '12',
        cy: '12',
        rx: '5',
        ry: '1.5',
        fill: 'none',
        stroke: '#1E40AF',
        'stroke-width': '1.2',
        opacity: '0.8'
      }),
      // Central black hole
      h('circle', {
        cx: '12',
        cy: '12',
        r: '3',
        fill: '#000000',
        stroke: '#1E40AF',
        'stroke-width': '0.5',
        opacity: '0.9'
      }),
      // Gravitational lensing effect
      h('path', {
        d: 'M12 3C8 3 5 6 5 10c0 2 1 4 2.5 5.5C9 17 10.5 18 12 18s3-1 4.5-2.5C18 14 19 12 19 10c0-4-3-7-7-7z',
        fill: 'none',
        stroke: '#60A5FA',
        'stroke-width': '0.3',
        opacity: '0.3'
      }),
      // Matter being pulled in
      h('path', {
        d: 'M3 8c2 1 4 2 6 3M21 16c-2-1-4-2-6-3M8 3c1 2 2 4 3 6M16 21c-1-2-2-4-3-6',
        stroke: '#3B82F6',
        'stroke-width': '0.8',
        'stroke-linecap': 'round',
        opacity: '0.5'
      }),
      // Hawking radiation
      h('circle', {
        cx: '6',
        cy: '6',
        r: '0.3',
        fill: '#60A5FA',
        opacity: '0.7'
      }),
      h('circle', {
        cx: '18',
        cy: '6',
        r: '0.2',
        fill: '#3B82F6',
        opacity: '0.6'
      }),
      h('circle', {
        cx: '6',
        cy: '18',
        r: '0.4',
        fill: '#1E40AF',
        opacity: '0.8'
      }),
      h('circle', {
        cx: '18',
        cy: '18',
        r: '0.3',
        fill: '#6366F1',
        opacity: '0.5'
      })
    ])
  }
}

const SilkIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Butterfly outline - left upper wing
      h('path', {
        d: 'M12 11c0-2-1-3.5-2.5-4.5s-3-1.5-4.5-1c-1.5 0.5-2 2-1.5 3.5s2 2.5 4 3c1.5 0.3 3 0.3 4.5-1z',
        stroke: '#BE185D',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        fill: 'none'
      }),
      
      // Butterfly outline - right upper wing
      h('path', {
        d: 'M12 11c0-2 1-3.5 2.5-4.5s3-1.5 4.5-1c1.5 0.5 2 2 1.5 3.5s-2 2.5-4 3c-1.5 0.3-3 0.3-4.5-1z',
        stroke: '#BE185D',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        fill: 'none'
      }),
      
      // Butterfly outline - left lower wing
      h('path', {
        d: 'M12 13c0 1.5-0.5 2.8-1.5 3.8s-2.3 1.5-3.5 1.2c-1.2-0.3-1.8-1.5-1.5-2.7s1.5-2 3-2.3c1.2-0.2 2.5 0 3.5 0z',
        stroke: '#BE185D',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        fill: 'none'
      }),
      
      // Butterfly outline - right lower wing
      h('path', {
        d: 'M12 13c0 1.5 0.5 2.8 1.5 3.8s2.3 1.5 3.5 1.2c1.2-0.3 1.8-1.5 1.5-2.7s-1.5-2-3-2.3c-1.2-0.2-2.5 0-3.5 0z',
        stroke: '#BE185D',
        'stroke-width': '2',
        'stroke-linecap': 'round',
        'stroke-linejoin': 'round',
        fill: 'none'
      }),
      
      // Butterfly body
      h('path', {
        d: 'M12 5v14',
        stroke: '#BE185D',
        'stroke-width': '2.5',
        'stroke-linecap': 'round'
      }),
      
      // Antennae
      h('path', {
        d: 'M11.5 5c-0.5-1-0.8-2-0.7-3',
        stroke: '#BE185D',
        'stroke-width': '1.5',
        'stroke-linecap': 'round',
        fill: 'none'
      }),
      h('path', {
        d: 'M12.5 5c0.5-1 0.8-2 0.7-3',
        stroke: '#BE185D',
        'stroke-width': '1.5',
        'stroke-linecap': 'round',
        fill: 'none'
      }),
      
      // Antennae tips
      h('circle', {
        cx: '10.8',
        cy: '2',
        r: '0.3',
        fill: '#BE185D'
      }),
      h('circle', {
        cx: '13.2',
        cy: '2',
        r: '0.3',
        fill: '#BE185D'
      })
    ])
  }
}

const FantasyIcon = {
  render() {
    return h('svg', {
      width: '20',
      height: '20',
      viewBox: '0 0 24 24',
      fill: 'none',
      xmlns: 'http://www.w3.org/2000/svg'
    }, [
      // Magic crystal/gem
      h('path', {
        d: 'M12 3l-4 6h8l-4-6z',
        fill: '#7C3AED'
      }),
      h('path', {
        d: 'M8 9v6l4 6 4-6V9H8z',
        fill: '#6D28D9'
      }),
      // Crystal facets
      h('path', {
        d: 'M12 3l-2 6h4l-2-6z',
        fill: '#A855F7',
        opacity: '0.8'
      }),
      h('path', {
        d: 'M8 9l4 6 4-6-4 3-4-3z',
        fill: '#8B5CF6',
        opacity: '0.6'
      }),
      // Magic sparkles
      h('path', {
        d: 'M6 6l1-2 1 2-1 2-1-2zM18 7l0.8-1.5 0.8 1.5-0.8 1.5-0.8-1.5z',
        fill: '#EC4899'
      }),
      h('path', {
        d: 'M5 14l0.6-1.2 0.6 1.2-0.6 1.2-0.6-1.2zM19 13l0.7-1.3 0.7 1.3-0.7 1.3-0.7-1.3z',
        fill: '#F472B6'
      }),
      h('path', {
        d: 'M7 18l0.5-1 0.5 1-0.5 1-0.5-1zM17 17l0.5-1 0.5 1-0.5 1-0.5-1z',
        fill: '#DB2777'
      }),
      // Magical aura
      h('circle', {
        cx: '12',
        cy: '12',
        r: '8',
        fill: 'none',
        stroke: '#C084FC',
        'stroke-width': '0.5',
        opacity: '0.3'
      }),
      h('circle', {
        cx: '12',
        cy: '12',
        r: '10',
        fill: 'none',
        stroke: '#E879F9',
        'stroke-width': '0.3',
        opacity: '0.2'
      }),
      // Inner magical light
      h('circle', {
        cx: '12',
        cy: '12',
        r: '2',
        fill: '#F0ABFC',
        opacity: '0.4'
      })
    ])
  }
}

// Map theme IDs to icons
const themeIcons = {
  light: LightIcon,
  dark: DarkIcon,
  cupcake: CupcakeIcon,
  bumblebee: BumblebeeIcon,
  emerald: EmeraldIcon,
  corporate: CorporateIcon,
  garden: GardenIcon,
  lofi: LofiIcon,
  dracula: DraculaIcon,
  pastel: PastelIcon,
  night: NightIcon,
  winter: WinterIcon,
  sunset: SunsetIcon,
  nord: NordIcon,
  dim: DimIcon,
  abyss: AbyssIcon,
  silk: SilkIcon,
  fantasy: FantasyIcon
}

// Get icon for theme
const getThemeIcon = (themeId) => {
  return themeIcons[themeId] || LightIcon
}

// Current theme icon
const currentThemeIcon = computed(() => {
  return getThemeIcon(currentThemeId.value)
})

// Toggle dropdown
const toggleDropdown = () => {
  isOpen.value = !isOpen.value
}

// Close dropdown programmatically
const closeDropdown = () => {
  isOpen.value = false
}

// Toggle between theme and font mode
const toggleMode = () => {
  isThemeMode.value = !isThemeMode.value
}

// Select theme
const selectTheme = (themeId) => {
  if (currentThemeId.value === themeId) {
    // Já está selecionado, não precisa fazer nada
    return
  }
  
  setTheme(themeId)
  // Menu permanece aberto após selecionar tema
}

// Select font
const selectFont = (fontId) => {
  if (currentFontId.value === fontId) {
    // Já está selecionada, não precisa fazer nada
    return
  }
  
  setFont(fontId)
  // Menu permanece aberto após selecionar fonte
}

// Handle scroll to close dropdown when navbar hides
const handleScroll = () => {
  const currentScrollY = window.scrollY
  
  // Calcular direção do scroll
  const scrollDifference = Math.abs(currentScrollY - lastScrollY.value)
  
  // Só atualizar se o scroll foi significativo
  if (scrollDifference < scrollThreshold) return
  
  // Se rolando para baixo e o menu está aberto, fechar
  if (currentScrollY > lastScrollY.value && currentScrollY > 50 && isOpen.value) {
    closeDropdown()
  }
  
  lastScrollY.value = currentScrollY
}

// Close dropdown when clicking outside
const handleClickOutside = (event) => {
  if (themeSelectorRef.value && !themeSelectorRef.value.contains(event.target)) {
    isOpen.value = false
  }
}

// Close dropdown on escape key
const handleKeydown = (event) => {
  if (event.key === 'Escape' && isOpen.value) {
    isOpen.value = false
    toggleButtonRef.value?.focus()
  }
}

// Add event listeners
onMounted(() => {
  document.addEventListener('click', handleClickOutside)
  document.addEventListener('keydown', handleKeydown)
  window.addEventListener('scroll', handleScroll)
})

// Remove event listeners
onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside)
  document.removeEventListener('keydown', handleKeydown)
  window.removeEventListener('scroll', handleScroll)
})

// Expose method for parent components
defineExpose({
  closeDropdown
})
</script>

<style scoped>
.theme-selector {
  position: relative;
  display: flex;
  align-items: center;
}

.theme-toggle-button {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  border: none;
  border-radius: 6px;
  background: transparent;
  color: var(--iluria-color-navbar-fg, #374151);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  font-size: 14px;
  font-weight: 500;
  outline: none;
  position: relative;
}

.theme-toggle-button:hover {
  background: transparent;
  transform: translateY(-1px);
}

.theme-toggle-button:active {
  transform: translateY(0) scale(0.98);
}

.theme-toggle-button.active {
  background: var(--iluria-color-navbar-active, rgba(0, 0, 0, 0.1));
  color: var(--iluria-color-primary, #3b82f6);
}

.theme-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  border-radius: 6px;
  transition: all 0.2s ease;
}

.theme-icon svg {
  width: 24px;
  height: 24px;
  transition: transform 0.2s ease;
}

.theme-toggle-button:hover .theme-icon svg {
  transform: none;
}



/* Dropdown */
.theme-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  right: 0;
  width: 320px;
  background: var(--iluria-color-container-bg, #f9fafb);
  border: 1px solid var(--iluria-color-border, #e5e7eb);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg, 0 10px 15px -3px rgba(0, 0, 0, 0.1)), 
              0 4px 6px -2px rgba(0, 0, 0, 0.05);
  z-index: 1000;
  overflow: hidden;
  backdrop-filter: blur(8px);
}

.dropdown-header {
  padding: 16px 16px 8px 16px;
  border-bottom: 1px solid var(--iluria-color-border, #e5e7eb);
}

.header-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 12px;
}

.header-left {
  flex: 1;
  min-width: 0;
}

.dropdown-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary, #1f2937);
  margin: 0 0 4px 0;
  line-height: 1.2;
}

.dropdown-subtitle {
  font-size: 12px;
  color: var(--iluria-color-text-secondary, #6b7280);
  margin: 0;
  line-height: 1.3;
}

.mode-toggle-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border: 1px solid var(--iluria-color-border, #e5e7eb);
  border-radius: 8px;
  background: var(--iluria-color-container-bg, #f9fafb);
  color: var(--iluria-color-text-secondary, #6b7280);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.mode-toggle-button:hover {
  background: transparent;
  transform: translateY(-1px);
}

.mode-toggle-button:active {
  transform: translateY(0) scale(0.98);
}

.mode-icon {
  transition: all 0.2s ease;
}

/* Mode icon rotation animation */
.mode-icon-rotate-enter-active,
.mode-icon-rotate-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.mode-icon-rotate-enter-from {
  opacity: 0;
  transform: rotate(-180deg) scale(0.8);
}

.mode-icon-rotate-leave-to {
  opacity: 0;
  transform: rotate(180deg) scale(0.8);
}

/* Content slide animation */
.content-slide-enter-active {
  transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-slide-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.content-slide-enter-from {
  opacity: 0;
  transform: translateX(20px);
}

.content-slide-leave-to {
  opacity: 0;
  transform: translateX(-20px);
}

.themes-list {
  padding: 8px;
  max-height: 300px;
  overflow-y: auto;
}

.theme-option {
  display: flex;
  align-items: center;
  gap: 12px;
  width: 100%;
  padding: 12px;
  border: none;
  border-radius: 8px;
  background: transparent;
  color: var(--iluria-color-text-primary, #1f2937);
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  text-align: left;
  position: relative;
  outline: none;
}

.theme-option:hover:not(.active) {
  background: rgba(156, 163, 175, 0.15);
  transform: translateX(2px);
}

.theme-option.active {
  background: rgba(156, 163, 175, 0.25);
  color: var(--iluria-color-text-primary, #1f2937);
  transform: scale(1.02);
  box-shadow: 0 0 0 1px rgba(156, 163, 175, 0.3);
}

.theme-option.transitioning {
  pointer-events: none;
}

.theme-option-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 36px;
  height: 36px;
  border-radius: 8px;
  background: rgba(156, 163, 175, 0.1);
  flex-shrink: 0;
  transition: all 0.2s ease;
  overflow: hidden;
}

.theme-option-icon svg {
  width: 24px;
  height: 24px;
  transition: all 0.2s ease;
}

.theme-option:hover .theme-option-icon svg {
  transform: scale(1.15) rotate(5deg);
}

.theme-option.active .theme-option-icon {
  background: rgba(156, 163, 175, 0.2);
}

.theme-option.active .theme-option-icon svg {
  transform: scale(1.1);
}

.theme-option-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.theme-name {
  font-size: 14px;
  font-weight: 500;
  line-height: 1.2;
}

.theme-preview {
  display: flex;
  gap: 4px;
  align-items: center;
}

.color-dot {
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 1px solid rgba(0, 0, 0, 0.1);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.theme-option.active .color-dot {
  border-color: rgba(156, 163, 175, 0.5);
}

.theme-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(156, 163, 175, 0.2);
  color: var(--iluria-color-primary, #3b82f6);
  flex-shrink: 0;
}

.theme-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid rgba(0, 0, 0, 0.1);
  border-top: 2px solid var(--iluria-color-primary, #3b82f6);
  border-radius: 50%;
  animation: spin 0.8s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.dropdown-footer {
  padding: 8px 16px 12px 16px;
  border-top: 1px solid var(--iluria-color-border, #e5e7eb);
}

.footer-text {
  font-size: 11px;
  color: var(--iluria-color-text-muted, #9ca3af);
  margin: 0;
  text-align: center;
  font-style: italic;
}

/* Dropdown slide animation */
.dropdown-slide-enter-active {
  transition: all 0.3s cubic-bezier(0.25, 0.46, 0.45, 0.94);
}

.dropdown-slide-leave-active {
  transition: all 0.2s cubic-bezier(0.55, 0.06, 0.68, 0.19);
}

.dropdown-slide-enter-from {
  opacity: 0;
  transform: translateY(-10px) scale(0.95);
}

.dropdown-slide-leave-to {
  opacity: 0;
  transform: translateY(-5px) scale(0.98);
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .theme-dropdown {
    width: 280px;
    right: -8px;
  }
  
  .theme-toggle-button {
    padding: 8px 10px;
  }

  .theme-icon {
    width: 24px;
    height: 24px;
  }
  

}

/* Scroll styling for themes list */
.themes-list::-webkit-scrollbar {
  width: 4px;
}

.themes-list::-webkit-scrollbar-track {
  background: transparent;
}

.themes-list::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border, #e5e7eb);
  border-radius: 2px;
}

.themes-list::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-border-hover, #d1d5db);
}

/* Tema escuro precisa de ajustes especiais */
.theme-dark .theme-dropdown {
  background: #1a1a1a;
  border-color: #333333;
}

.theme-dark .dropdown-header {
  border-bottom-color: #333333;
}

.theme-dark .theme-option:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
}

.theme-dark .theme-option.active {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.theme-dark .theme-option-icon {
  background: rgba(255, 255, 255, 0.05);
}

.theme-dark .theme-option.active .theme-option-icon {
  background: rgba(255, 255, 255, 0.1);
}

.theme-dark .dropdown-footer {
  border-top-color: #333333;
}

.theme-dark .themes-list::-webkit-scrollbar-thumb {
  background: #444444;
}

.theme-dark .themes-list::-webkit-scrollbar-thumb:hover {
  background: #555555;
}

/* Fonts List Styles */
.fonts-list {
  padding: 8px;
  max-height: 350px;
  overflow-y: auto;
}

.font-category {
  margin-bottom: 1.5rem;
}

.font-category:last-child {
  margin-bottom: 0;
}

.font-category-title {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--iluria-color-text-muted, #9ca3af);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  margin-bottom: 0.75rem;
  padding: 0 0.5rem;
}

.font-option {
  width: 100%;
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 0.875rem;
  margin-bottom: 0.5rem;
  border: 1px solid transparent;
  border-radius: 0.5rem;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  text-align: left;
}

.font-option:hover:not(.active) {
  background: rgba(156, 163, 175, 0.15);
  transform: translateX(2px);
}

.font-option.active {
  background: rgba(156, 163, 175, 0.25);
  color: var(--iluria-color-text-primary, #1f2937);
  transform: scale(1.02);
  box-shadow: 0 0 0 1px rgba(156, 163, 175, 0.3);
}

.font-preview {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 3rem;
  height: 3rem;
  background: rgba(156, 163, 175, 0.1);
  border: 1px solid transparent;
  border-radius: 0.5rem;
  font-size: 1.5rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary, #1f2937);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.font-option.active .font-preview {
  background: rgba(156, 163, 175, 0.2);
  border-color: rgba(156, 163, 175, 0.3);
  color: var(--iluria-color-text-primary, #1f2937);
}

.font-info {
  flex: 1;
  text-align: left;
  min-width: 0;
}

.font-name {
  display: block;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary, #1f2937);
  line-height: 1.25;
  margin-bottom: 0.25rem;
}

.font-description {
  display: block;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary, #6b7280);
  line-height: 1.3;
}

.font-loading {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.5rem;
  height: 1.5rem;
  flex-shrink: 0;
}

.font-check {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: rgba(156, 163, 175, 0.2);
  color: var(--iluria-color-primary, #3b82f6);
  flex-shrink: 0;
}

.font-loading .loading-spinner {
  width: 1rem;
  height: 1rem;
  border: 2px solid var(--iluria-color-border, #e5e7eb);
  border-top: 2px solid var(--iluria-color-primary, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Fonts List Scrollbar */
.fonts-list::-webkit-scrollbar {
  width: 4px;
}

.fonts-list::-webkit-scrollbar-track {
  background: transparent;
}

.fonts-list::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border, #e5e7eb);
  border-radius: 2px;
}

.fonts-list::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-border-hover, #d1d5db);
}

/* Dark theme adjustments for fonts */
.theme-dark .fonts-list {
  background: transparent;
}

.theme-dark .font-option:hover:not(.active) {
  background: rgba(255, 255, 255, 0.05);
}

.theme-dark .font-option.active {
  background: rgba(255, 255, 255, 0.1);
  box-shadow: 0 0 0 1px rgba(255, 255, 255, 0.15);
}

.theme-dark .font-preview {
  background: rgba(255, 255, 255, 0.05);
  border-color: transparent;
  color: #ffffff;
}

.theme-dark .font-option.active .font-preview {
  background: rgba(255, 255, 255, 0.1);
  border-color: rgba(255, 255, 255, 0.15);
  color: #ffffff;
}

.theme-dark .font-check {
  background: rgba(255, 255, 255, 0.1);
}

.theme-dark .mode-toggle-button {
  background: #222222;
  border-color: #333333;
  color: #9ca3af;
}

.theme-dark .mode-toggle-button:hover {
  background: #333333;
  border-color: #444444;
  color: #ffffff;
}
</style> 
