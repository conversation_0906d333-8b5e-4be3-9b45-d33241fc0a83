<template>
  <Teleport to="body">
    <div class="iluria-toast-container">
      <TransitionGroup 
        name="toast"
        tag="div"
        class="toast-stack"
      >
        <div
          v-for="toast in toasts"
          :key="toast.id"
          class="toast-wrapper"
        >
          <IluriaToast
            :id="toast.id"
            :type="toast.type"
            :message="toast.message"
            :title="toast.title"
            :duration="toast.duration"
            :action="toast.action"
            :persistent="toast.persistent"
            :dismissible="toast.dismissible"
            @close="removeToast"
            @action="handleToastAction"
          />
        </div>
      </TransitionGroup>
    </div>
  </Teleport>
</template>

<script setup>
import { computed } from 'vue'
import { useToastStore } from '@/stores/toast.store'
import IluriaToast from '@/components/Toast.vue'

const toastStore = useToastStore()

// Computed para os toasts ativos
const toasts = computed(() => toastStore.toasts)

// Função para remover toast
const removeToast = (id) => {
  toastStore.removeToast(id)
}

// Função para lidar com ações de toast
const handleToastAction = (payload) => {
  const { id, action } = payload
  
  // Executar a ação se existir
  if (action && typeof action.handler === 'function') {
    action.handler()
  }
  
  // Remover o toast após a ação (a menos que seja persistente)
  const toast = toasts.value.find(t => t.id === id)
  if (toast && !toast.persistent) {
    removeToast(id)
  }
}
</script>

<style scoped>
.iluria-toast-container {
  position: fixed;
  top: 80px;
  right: 24px;
  z-index: 9999;
  pointer-events: none;
  max-width: 420px;
  width: 100%;
}

.toast-stack {
  display: flex;
  flex-direction: column;
  gap: 12px;
  align-items: flex-end;
}

.toast-wrapper {
  pointer-events: auto;
  width: 100%;
  max-width: 400px;
}

/* Animações de entrada e saída */
.toast-enter-active {
  transition: all 0.4s cubic-bezier(0.34, 1.56, 0.64, 1);
}

.toast-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.toast-enter-from {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

.toast-leave-to {
  opacity: 0;
  transform: translateX(100%) scale(0.95);
}

.toast-move {
  transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Responsividade */
@media (max-width: 768px) {
  .iluria-toast-container {
    top: 70px;
    right: 16px;
    left: 16px;
    max-width: none;
  }
  
  .toast-stack {
    align-items: stretch;
  }
  
  .toast-wrapper {
    max-width: none;
  }
}

/* Ajustes para diferentes temas */
@media (prefers-reduced-motion: reduce) {
  .toast-enter-active,
  .toast-leave-active,
  .toast-move {
    transition-duration: 0.1s;
  }
}
</style>
