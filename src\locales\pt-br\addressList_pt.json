{"title": "Endereços", "addresses": "Endereços", "noAddresses": "Nenhum endereço encontrado", "noAddressesDescription": "Este cliente ainda não possui endereços cadastrados. Adicione o primeiro endereço para começar.", "addAddress": "<PERSON><PERSON><PERSON><PERSON>", "newAddress": "Novo Endereço", "editAddress": "<PERSON><PERSON>", "removeAddress": "Remover Endereço", "makePrimary": "<PERSON><PERSON>", "primary": "Principal", "setPrimary": "Definir como endereço principal", "primaryDescription": "O endereço principal será usado como padrão para entregas e correspondências", "addressType": "Tipo de Endereço", "selectType": "Selecione o tipo", "residential": "Residencial", "commercial": "Comercial", "other": "Outro", "nickname": "Apelido", "recipient": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "zip": "CEP", "street": "Rua/Avenida", "number": "Número", "complement": "Complemento", "neighborhood": "Bairro", "city": "Cidade", "state": "Estado", "country": "<PERSON><PERSON>", "createSuccess": "Endereço criado com sucesso", "updateSuccess": "Endereço atualizado com sucesso", "deleteSuccess": "Endereço removido com sucesso", "primarySuccess": "Endereço principal definido com sucesso", "saveError": "Erro ao salvar endereço", "deleteError": "Erro ao remover endereço", "primaryError": "<PERSON>rro ao definir endere<PERSON>o principal", "cancel": "<PERSON><PERSON><PERSON>"}