<template>
  <div class="notifications-content">
    <!-- Main Notifications -->
    <ViewContainer
      title="Notificações"
      :icon="Notification01Icon"
      iconColor="blue"
    >
      <div class="notification-list">
        <div
          v-for="notification in mainNotifications"
          :key="notification.id"
          class="notification-item"
        >
          <div class="notification-info">
            <div class="notification-icon">
              <HugeiconsIcon :icon="notification.icon" size="18" :strokeWidth="1.5" />
            </div>
            <div class="notification-content">
              <h4 class="notification-title">{{ notification.title }}</h4>
              <p class="notification-description">{{ notification.description }}</p>
            </div>
          </div>
          <div class="notification-toggle">
            <IluriaToggleSwitch
              :modelValue="notification.enabled"
              @update:modelValue="(value) => handleNotificationChange('main', notification.id, value)"
            />
          </div>
        </div>
      </div>
    </ViewContainer>

    <!-- Collaboration Notifications -->
    <ViewContainer
      title="Notificações de Colaboração"
      :icon="UserGroupIcon"
      iconColor="purple"
    >
      <div class="notification-list">
        <div
          v-for="collaboration in collaborationNotifications"
          :key="collaboration.id"
          class="notification-item"
        >
          <div class="notification-info">
            <div class="notification-icon">
              <HugeiconsIcon :icon="collaboration.icon" size="18" :strokeWidth="1.5" />
            </div>
            <div class="notification-content">
              <h4 class="notification-title">{{ collaboration.title }}</h4>
              <p class="notification-description">{{ collaboration.description }}</p>
            </div>
          </div>
          <div class="notification-toggle">
            <IluriaToggleSwitch
              :modelValue="collaboration.enabled"
              @update:modelValue="(value) => handleNotificationChange('collaboration', collaboration.id, value)"
            />
          </div>
        </div>
      </div>
    </ViewContainer>

    <!-- Notification Schedule -->
    <ViewContainer
      title="Horário das Notificações"
      :icon="Time04Icon"
      iconColor="orange"
    >
      <div class="schedule-content">
        <div class="schedule-item">
          <div class="schedule-info">
            <h4 class="schedule-title">Não Perturbe</h4>
            <p class="schedule-description">Defina um período em que não deseja receber notificações</p>
          </div>
          <div class="schedule-toggle">
            <IluriaToggleSwitch
              :modelValue="scheduleSettings.doNotDisturb"
              @update:modelValue="(value) => handleScheduleChange(value)"
            />
          </div>
        </div>

        <div v-if="scheduleSettings.doNotDisturb" class="schedule-times">
          <div class="time-field">
            <IluriaInputDatePicker
              id="startTime"
              v-model="scheduleSettings.startTime"
              label="Início"
              :showTime="true"
              :showButtonBar="false"
              dateFormat=""
              hourFormat="24"
              @update:modelValue="saveScheduleTimes"
            />
          </div>
          <div class="time-field">
            <IluriaInputDatePicker
              id="endTime"
              v-model="scheduleSettings.endTime"
              label="Fim"
              :showTime="true"
              :showButtonBar="false"
              dateFormat=""
              hourFormat="24"
              @update:modelValue="saveScheduleTimes"
            />
          </div>
        </div>
      </div>
    </ViewContainer>

    <!-- Marketing Communications -->
    <ViewContainer 
      title="Comunicações de Marketing"
      :icon="MegaphoneIcon"
      iconColor="green"
    >
      <div class="marketing-content">
        <div class="marketing-item">
          <div class="marketing-info">
            <h4 class="marketing-title">Newsletter</h4>
            <p class="marketing-description">Receba nossas atualizações semanais sobre novidades e recursos</p>
          </div>
          <div class="marketing-toggle">
            <IluriaToggleSwitch
              :modelValue="marketingSettings.newsletter"
              @update:modelValue="(value) => handleMarketingChange('newsletter', value)"
            />
          </div>
        </div>
        
        <div class="marketing-item">
          <div class="marketing-info">
            <h4 class="marketing-title">Ofertas Especiais</h4>
            <p class="marketing-description">Seja notificado sobre promoções e descontos exclusivos</p>
          </div>
          <div class="marketing-toggle">
            <IluriaToggleSwitch
              :modelValue="marketingSettings.specialOffers"
              @update:modelValue="(value) => handleMarketingChange('specialOffers', value)"
            />
          </div>
        </div>
        
        <div class="marketing-item">
          <div class="marketing-info">
            <h4 class="marketing-title">Pesquisas e Feedback</h4>
            <p class="marketing-description">Participe de pesquisas para nos ajudar a melhorar nossos serviços</p>
          </div>
          <div class="marketing-toggle">
            <IluriaToggleSwitch
              :modelValue="marketingSettings.feedbackSurveys"
              @update:modelValue="(value) => handleMarketingChange('feedbackSurveys', value)"
            />
          </div>
        </div>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useToast } from '@/services/toast.service'
import userNotificationService from '@/services/userNotification.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaInputDatePicker from '@/components/iluria/form/IluriaInputDatePicker.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Store01Icon,
  UserGroupIcon,
  SecurityCheckIcon,
  Settings02Icon,
  Time04Icon,
  MegaphoneIcon,
  UserIcon,
  Notification01Icon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const toast = useToast()

// Loading state
const isLoading = ref(false)

const isSaving = ref(false)

// State
const mainNotifications = ref([
  {
    id: 'stores',
    title: 'Notificações de Lojas',
    description: 'Receba notificações das suas lojas (vendas, pedidos, alertas)',
    icon: Store01Icon,
    enabled: true
  },
  {
    id: 'accountAndSafety',
    title: 'Conta e Segurança',
    description: 'Mudanças importantes na sua conta e alertas de segurança',
    icon: SecurityCheckIcon,
    enabled: true
  },
  {
    id: 'systemAndUpdates',
    title: 'Sistema e Atualizações',
    description: 'Notificações sobre atualizações da plataforma e manutenções',
    icon: Settings02Icon,
    enabled: true
  }
])

const collaborationNotifications = ref([
  {
    id: 'collabInvites',
    title: 'Convites de Colaboração',
    description: 'Notificações quando você for convidado para colaborar em lojas',
    icon: UserIcon,
    enabled: true
  },
  {
    id: 'collabUpdates',
    title: 'Atualizações de Colaboração',
    description: 'Notificações de lojas onde você é colaborador',
    icon: Notification01Icon,
    enabled: true
  },
  {
    id: 'roleChanges',
    title: 'Mudanças de Permissão',
    description: 'Quando suas permissões forem alteradas em uma loja',
    icon: UserGroupIcon,
    enabled: true
  }
])

const scheduleSettings = reactive({
  doNotDisturb: false,
  startTime: null,
  endTime: null
})

// Helper function to create Date object from time string
const createTimeDate = (timeString) => {
  if (!timeString) return null
  const [hours, minutes] = timeString.split(':')
  const date = new Date()
  date.setHours(parseInt(hours), parseInt(minutes), 0, 0)
  return date
}

// Helper function to extract time string from Date object
const extractTimeString = (date) => {
  if (!date) return null
  const hours = date.getHours().toString().padStart(2, '0')
  const minutes = date.getMinutes().toString().padStart(2, '0')
  return `${hours}:${minutes}`
}

const marketingSettings = reactive({
  newsletter: false,
  specialOffers: false,
  feedbackSurveys: false
})

const debounceTimers = new Map()

const debounce = (func, delay, key) => {
  if (debounceTimers.has(key)) {
    clearTimeout(debounceTimers.get(key))
  }

  const timer = setTimeout(() => {
    func()
    debounceTimers.delete(key)
  }, delay)

  debounceTimers.set(key, timer)
}

// Methods
const handleNotificationChange = (type, id, enabled) => {
  if (type === 'main') {
    const notification = mainNotifications.value.find(n => n.id === id)
    if (notification) notification.enabled = enabled
  } else if (type === 'collaboration') {
    const notification = collaborationNotifications.value.find(n => n.id === id)
    if (notification) notification.enabled = enabled
  }

  debounce(() => saveNotificationSetting(type, id, enabled), 500, `${type}-${id}`)
}

const saveNotificationSetting = async (type, id, enabled) => {
  if (isSaving.value) {
    return
  }

  try {
    isSaving.value = true

    await userNotificationService.updateNotificationSetting(id, enabled)
  } catch (error) {
     console.error('Erro ao salvar configuração:', error)
    toast.addToast('Erro ao salvar configuração', 'error')

    // Reverter o estado em caso de erro
    if (type === 'main') {
      const notification = mainNotifications.value.find(n => n.id === id)
      if (notification) notification.enabled = !enabled
    } else if (type === 'collaboration') {
      const notification = collaborationNotifications.value.find(n => n.id === id)
      if (notification) notification.enabled = !enabled
    }
  } finally {
    isSaving.value = false
  }
}

const handleScheduleChange = (enabled) => {
  scheduleSettings.doNotDisturb = enabled

  debounce(() => saveScheduleSetting(enabled), 500, 'schedule-doNotDisturb')
}

const saveScheduleSetting = async (enabled) => {
  if (isSaving.value) {
    return
  }

  try {
    isSaving.value = true

    // Se enabled é um boolean, está salvando o doNotDisturb
    if (typeof enabled === 'boolean') {
      await userNotificationService.updateNotificationSetting('doNotDisturb', enabled)
    } else {
      // Se não é boolean, está salvando os horários
      await saveScheduleTimes()
    }
  } catch (error) {
    console.error('Erro ao salvar configuração de horário:', error)
    toast.addToast('Erro ao salvar configuração', 'error')

    // Reverter o estado em caso de erro
    if (typeof enabled === 'boolean') {
      scheduleSettings.doNotDisturb = !enabled
    }
  } finally {
    isSaving.value = false
  }
}

const saveScheduleTimes = async () => {
  try {
    // Salvar startTime e endTime
    const currentSettings = await userNotificationService.getUserNotificationSettings()

    const updatedSettings = {
      ...currentSettings,
      startTime: extractTimeString(scheduleSettings.startTime),
      endTime: extractTimeString(scheduleSettings.endTime)
    }

    await userNotificationService.saveUserNotificationSettings(updatedSettings)
  } catch (error) {
    console.error('Erro ao salvar horários:', error)
    toast.addToast('Erro ao salvar horários', 'error')
    throw error
  }
}

const handleMarketingChange = (type, enabled) => {
  // Atualizar o estado local imediatamente
  marketingSettings[type] = enabled

  // Usar debounce para salvar no backend
  debounce(() => saveMarketingSetting(type, enabled), 500, `marketing-${type}`)
}

const saveMarketingSetting = async (type, enabled) => {
  if (isSaving.value) {
    return
  }

  try {
    isSaving.value = true

    await userNotificationService.updateNotificationSetting(type, enabled)
  } catch (error) {
    console.error('Erro ao salvar configuração de marketing:', error)
    toast.addToast('Erro ao salvar configuração', 'error')

    // Reverter o estado em caso de erro
    marketingSettings[type] = !enabled
  } finally {
    isSaving.value = false
  }
}

const loadNotificationSettings = async () => {
  try {
    isLoading.value = true

    const settings = await userNotificationService.getUserNotificationSettings()

    // Atualizar as configurações principais
    mainNotifications.value.forEach(notification => {
      if (settings.hasOwnProperty(notification.id)) {
        notification.enabled = settings[notification.id]
      }
    })

    // Atualizar as configurações de colaboração
    collaborationNotifications.value.forEach(notification => {
      if (settings.hasOwnProperty(notification.id)) {
        notification.enabled = settings[notification.id]
      }
    })

    // Atualizar configurações de horário
    if (settings.hasOwnProperty('doNotDisturb')) {
      scheduleSettings.doNotDisturb = settings.doNotDisturb
    }

    if (settings.startTime) {
      scheduleSettings.startTime = createTimeDate(settings.startTime)
    } else {
      scheduleSettings.startTime = createTimeDate('22:00') // Default value
    }

    if (settings.endTime) {
      scheduleSettings.endTime = createTimeDate(settings.endTime)
    } else {
      scheduleSettings.endTime = createTimeDate('08:00') // Default value
    }

    // Atualizar configurações de marketing
    if (settings.hasOwnProperty('newsletter')) {
      marketingSettings.newsletter = settings.newsletter
    }

    if (settings.hasOwnProperty('specialOffers')) {
      marketingSettings.specialOffers = settings.specialOffers
    }

    if (settings.hasOwnProperty('feedbackSurveys')) {
      marketingSettings.feedbackSurveys = settings.feedbackSurveys
    }

  } catch (error) {
    console.error('Erro ao carregar configurações:', error)
    toast.addToast('Erro ao carregar configurações. Usando valores padrão.', 'error')
  } finally {
    isLoading.value = false
  }
}

// Lifecycle
onMounted(() => {
  loadNotificationSettings()
})
</script>

<style scoped>
.notifications-content {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.notification-section {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.5rem;
}

.section-title {
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.notification-card {
  background: var(--iluria-color-input-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 1.5rem;
}

.notification-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.notification-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.notification-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.notification-icon {
  flex-shrink: 0;
  color: var(--iluria-color-primary);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.25rem 0;
}

.notification-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.notification-toggle {
  flex-shrink: 0;
}



.schedule-content, .marketing-content {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.schedule-item, .marketing-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.schedule-info, .marketing-info {
  flex: 1;
}

.schedule-title, .marketing-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.25rem 0;
}

.schedule-description, .marketing-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.schedule-toggle, .marketing-toggle {
  flex-shrink: 0;
}

.schedule-times {
  display: flex;
  gap: 1rem;
  margin-top: 1rem;
  padding: 1rem;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 8px;
}

.time-field {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  flex: 1;
}



/* Responsive */
@media (max-width: 768px) {
  .notification-item, .schedule-item, .marketing-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .notification-toggle, .schedule-toggle, .marketing-toggle {
    align-self: flex-end;
  }
  
  .schedule-times {
    flex-direction: column;
  }
}
</style>