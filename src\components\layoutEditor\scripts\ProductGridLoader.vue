<template>
  <!-- Este componente não renderiza nada, apenas fornece funcionalidades de script -->
</template>

<script>
import { ref, onMounted } from 'vue'

/**
 * Sistema de injeção de script para Product Grid
 * Integrado com o sistema centralizado de componentes
 */
export async function injectProductGridScript(doc, authToken) {
 

  if (!doc || !doc.querySelector) {
    console.error('❌ [ProductGrid] Documento inválido')
    return
  }

  const grids = doc.querySelectorAll('[data-component="product-grid"], [data-component="dynamic-grid-produtos"], [data-product-grid="true"], .iluria-product-grid')

 

  if (grids.length === 0) {

    return
  }

  // Obter domínio da loja e definir no window para uso no script
  let storefrontDomain = 'http://localhost:8080' // fallback padrão

  try {
    const { useProductGrid } = await import('@/composables/useProductGrid.js')
    const { getStorefrontDomain } = useProductGrid()
    storefrontDomain = await getStorefrontDomain()
   
  } catch (error) {
    console.warn('⚠️ [ProductGrid] Erro ao obter domínio da loja, usando fallback:', error)
  }

  // Definir no window para acesso no script injetado (com verificação de segurança)
  try {
    if (doc.defaultView && doc.defaultView.window) {
      doc.defaultView.storefrontDomain = storefrontDomain
      
    } else {
      console.warn('⚠️ [ProductGrid] doc.defaultView não disponível, usando variável no script')
    }
  } catch (error) {
    console.warn('⚠️ [ProductGrid] Erro ao definir no window:', error)
  }

  // Limpar scripts e observers existentes
  const existingScript = doc.querySelector('#product-grid-script')
  if (existingScript) {
    existingScript.remove()
  }

  // Desconectar observer anterior se existir
  if (doc._productGridObserver) {
    doc._productGridObserver.disconnect()
    doc._productGridObserver = null
  }

  try {
    // Adicionar CSS apenas uma vez
    if (!doc.querySelector('#product-grid-styles')) {
      const cssContent = [
        '@keyframes productSpin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }',
        '.product-loading-spinner { width: 40px; height: 40px; border: 3px solid #e5e7eb; border-radius: 50%; border-top-color: #3b82f6; animation: productSpin 1s linear infinite; margin: 0 auto; }',
        '.product-grid-container { display: grid; gap: 1.5rem; width: 100%; max-width: 1200px; margin: 0 auto; padding: 0 1rem; }',
        '.product-card { background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1); transition: all 0.3s ease; border: 1px solid #e5e7eb; }',
        '.product-card:hover { transform: translateY(-4px); box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1); }',
        '.product-image { width: 100%; height: 250px; object-fit: cover; background: #f3f4f6; }',
        '.product-info { padding: 1.5rem; }',
        '.product-title { font-size: 1.125rem; font-weight: 600; color: #111827; margin: 0 0 0.75rem 0; line-height: 1.4; display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical; overflow: hidden; }',
        '.product-price { display: flex; align-items: center; gap: 0.5rem; margin-bottom: 1rem; }',
        '.current-price { font-size: 1.25rem; font-weight: 700; color: #3b82f6; }',
        '.original-price { font-size: 0.875rem; color: #6b7280; text-decoration: line-through; }',
        '.product-button { width: 100%; background-color: #3b82f6; color: white; border: none; border-radius: 8px; padding: 0.75rem 1rem; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: all 0.2s ease; }',
        '.product-button:hover { background-color: #2563eb; transform: translateY(-1px); }',
        '@media (min-width: 1024px) { .product-grid-4 { grid-template-columns: repeat(4, 1fr); } .product-grid-3 { grid-template-columns: repeat(3, 1fr); } .product-grid-2 { grid-template-columns: repeat(2, 1fr); } }',
        '@media (min-width: 768px) and (max-width: 1023px) { .product-grid-4 { grid-template-columns: repeat(3, 1fr); } .product-grid-3 { grid-template-columns: repeat(2, 1fr); } .product-grid-2 { grid-template-columns: repeat(2, 1fr); } }',
        '@media (max-width: 767px) { .product-grid-4, .product-grid-3, .product-grid-2 { grid-template-columns: repeat(2, 1fr); } .product-grid-container { gap: 1rem; padding: 0 0.5rem; } .product-image { height: 200px; } .product-info { padding: 1rem; } .product-title { font-size: 1rem; } }',
        '@media (max-width: 480px) { .product-grid-4, .product-grid-3, .product-grid-2 { grid-template-columns: 1fr; } .product-grid-container { gap: 0.75rem; } }'
      ].join(' ')
      
      const styleElement = doc.createElement('style')
      styleElement.id = 'product-grid-styles'
      styleElement.textContent = cssContent
      doc.head.appendChild(styleElement)
    }

    // Criar script principal
    const scriptElement = doc.createElement('script')
    scriptElement.id = 'product-grid-standalone-script'
    
    const scriptParts = [
      '(function() {',
      '  "use strict";',
      '  const authToken = ' + JSON.stringify(authToken) + ';',
      '',
      '  // Evitar re-execução',
      '  if (window._productGridActive) {',
      '    return;',
      '  }',
      '  window._productGridActive = true;',
      '',
      '  // Armazenar grids processados',
      '  const processedGrids = new Set();',
      '  const loadingGrids = new Set();',
      '',
      '  function formatCurrency(value) {',
      '    if (typeof value !== "number") value = parseFloat(value) || 0;',
      '    return new Intl.NumberFormat("pt-BR", { style: "currency", currency: "BRL" }).format(value);',
      '  }',
      '',
      '  function createLoadingHTML(title) {',
      '    return "<div style=\\"text-align: center; padding: 2rem;\\"><h2 style=\\"font-size: 2rem; font-weight: 700; color: #1f2937; margin: 0 0 2rem 0;\\">" + title + "</h2><div style=\\"display: flex; flex-direction: column; align-items: center; justify-content: center; min-height: 300px;\\"><div class=\\"product-loading-spinner\\"></div><p style=\\"margin-top: 1rem; color: #6b7280; font-size: 0.875rem;\\">Carregando produtos...</p></div></div>";',
      '  }',
      '',
      '  function createErrorHTML(title, errorMessage) {',
      '    return "<div style=\\"text-align: center; padding: 2rem;\\"><h2 style=\\"font-size: 2rem; font-weight: 700; color: #1f2937; margin: 0 0 2rem 0;\\">" + title + "</h2><div style=\\"display: flex; flex-direction: column; align-items: center; padding: 3rem 1rem; background: #fef2f2; border-radius: 12px; border: 2px solid #fecaca; max-width: 500px; margin: 0 auto;\\"><div style=\\"font-size: 3rem; margin-bottom: 1rem; opacity: 0.7;\\">❌</div><h3 style=\\"margin: 0 0 1rem 0; color: #dc2626; font-size: 1.25rem; font-weight: 600;\\">Falha ao Conectar com a API</h3><p style=\\"margin: 0 0 1rem 0; color: #7f1d1d; font-size: 0.875rem; text-align: center; line-height: 1.5;\\">" + errorMessage + "</p><p style=\\"margin: 0 0 2rem 0; color: #6b7280; font-size: 0.8rem; text-align: center; line-height: 1.4;\\">Verifique se a API está rodando em <strong>http://localhost:8081</strong> e se as configurações de CORS estão corretas.</p><div style=\\"display: flex; gap: 1rem; flex-wrap: wrap; justify-content: center;\\"><button onclick=\\"location.reload()\\" style=\\"background: #dc2626; color: white; border: none; border-radius: 8px; padding: 0.75rem 1.5rem; font-size: 0.875rem; font-weight: 600; cursor: pointer; transition: background 0.2s;\\">🔄 Tentar Novamente</button></div></div></div>";',
      '  }',
      '',
      '  function createEmptyHTML(title) {',
      '    return "<div style=\\"text-align: center; padding: 2rem;\\"><h2 style=\\"font-size: 2rem; font-weight: 700; color: #1f2937; margin: 0 0 2rem 0;\\">" + title + "</h2><div style=\\"display: flex; flex-direction: column; align-items: center; padding: 3rem 1rem; background: #f9fafb; border-radius: 12px; border: 2px dashed #d1d5db;\\"><div style=\\"font-size: 3rem; margin-bottom: 1rem; opacity: 0.5;\\">📦</div><h3 style=\\"margin: 0 0 0.5rem 0; color: #374151; font-size: 1.125rem;\\">Nenhum produto encontrado</h3><p style=\\"margin: 0; color: #6b7280; font-size: 0.875rem;\\">Tente ajustar os filtros</p></div></div>";',
      '  }',
      '',

      '',
      '  window.loadProductsFromAPI = async function(endpoint, limit, grid, token) {',
      '    const gridId = grid.id || (grid.id = "grid-" + Date.now() + "-" + Math.random().toString(36).substr(2, 5));',
      '',
      '    // Verificar se já está carregando ou carregado',
      '    if (loadingGrids.has(gridId)) {',

      '      return;',
      '    }',
      '',
      '    if (processedGrids.has(gridId)) {',

      '      return;',
      '    }',
      '',

      '    loadingGrids.add(gridId);',
      '',
      '    try {',
      '      const title = grid.getAttribute("data-title") || "Most Popular";',
      '      grid.innerHTML = createLoadingHTML(title);',
      '      ',
      '     {',
      '        endpoint: endpoint,',
      '        hasToken: !!token,',
      '        tokenLength: token ? token.length : 0,',
      '        gridId: gridId',
      '      });',
      '',
      '      const headers = { "Content-Type": "application/json" };',
      '      // API do storefront não precisa de Authorization (é pública)',
      '      // O interceptor do axios já adiciona X-Store-ID automaticamente',
  
      '',
      '      const url = new URL(endpoint);',
      '      url.searchParams.append("size", limit.toString());',
      '      url.searchParams.append("page", "0");',
      '',
      '      const category = grid.getAttribute("data-category");',
      '      if (category && category !== "all") {',
      '        url.searchParams.append("categoryId", category);',
      '      }',
      '',
      '      const sortBy = grid.getAttribute("data-sort-by") || "name";',
      '      switch (sortBy) {',
      '        case "newest": url.searchParams.append("sort", "createdAt,desc"); break;',
      '        case "price-asc": url.searchParams.append("sort", "price,asc"); break;',
      '        case "price-desc": url.searchParams.append("sort", "price,desc"); break;',
      '        default: url.searchParams.append("sort", "name,asc");',
      '      }',
      '',
      
      '        url: url.toString(),',
      '        headers: headers',
      '      });',
      '',
      
      '',
      '      const controller = new AbortController();',
      '      const timeoutId = setTimeout(() => controller.abort(), 8000);',
      '',
      '      const response = await fetch(url.toString(), { headers, signal: controller.signal });',
      '      clearTimeout(timeoutId);',
      '',
      
      '        status: response.status,',
      '        statusText: response.statusText,',
      '        ok: response.ok',
      '      });',
      '',
      '      if (!response.ok) throw new Error("HTTP " + response.status);',
      '',
      '      const data = await response.json();',
     
      '      let products = [];',
      '      if (data.content && Array.isArray(data.content)) products = data.content;',
      '      else if (Array.isArray(data)) products = data;',
      '      else if (data.data && Array.isArray(data.data)) products = data.data;',
      '',
      '      if (products.length === 0) {',
      '        grid.innerHTML = createEmptyHTML(title);',
      '        processedGrids.add(gridId);',
      '        loadingGrids.delete(gridId);',
      '        return;',
      '      }',
      '',
      '      const limitedProducts = products.slice(0, limit);',
      '      const columns = parseInt(grid.getAttribute("data-columns") || "4");',
      '',
      '      let html = "<div style=\\"text-align: center; padding: 2rem 1rem;\\"><h2 style=\\"font-size: 2rem; font-weight: 700; color: #1f2937; margin: 0 0 3rem 0;\\">" + title + "</h2><div class=\\"product-grid-container product-grid-" + columns + "\\">";',
      '',
      '      limitedProducts.forEach(function(product) {',
      '        const imageUrl = product.image || product.imageUrl || "";',
      '        const productName = product.name || "Produto";',
      '        const productPrice = product.price || 0;',
      '        const originalPrice = product.originalPrice;',
      '',
      '        html += "<div class=\\"product-card\\">";',
      '        if (imageUrl) {',
      '          html += "<img class=\\"product-image\\" src=\\"" + imageUrl + "\\" alt=\\"" + productName + "\\" onerror=\\"this.style.display=\'none\'; this.nextElementSibling.style.display=\'flex\';\\" />";',
      '          html += "<div class=\\"product-image-placeholder\\" style=\\"display: none; width: 100%; height: 250px; background: #f3f4f6; border-radius: 8px; align-items: center; justify-content: center; color: #9ca3af; font-size: 0.875rem;\\">📦 Imagem não disponível</div>";',
      '        } else {',
      '          html += "<div class=\\"product-image-placeholder\\" style=\\"width: 100%; height: 250px; background: #f3f4f6; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #9ca3af; font-size: 0.875rem;\\">📦 Sem imagem</div>";',
      '        }',
      '        html += "<div class=\\"product-info\\">";',
      '        html += "<h3 class=\\"product-title\\">" + productName + "</h3>";',
      '        html += "<div class=\\"product-price\\">";',
      '        html += "<span class=\\"current-price\\">" + formatCurrency(productPrice) + "</span>";',
      '        if (originalPrice && originalPrice > productPrice) {',
      '          html += "<span class=\\"original-price\\">" + formatCurrency(originalPrice) + "</span>";',
      '        }',
      '        html += "</div>";',
      '        html += "<button class=\\"product-button\\">Ver Produto</button>";',
      '        html += "</div></div>";',
      '      });',
      '',
      '      html += "</div></div>";',
      '      grid.innerHTML = html;',
      '      processedGrids.add(gridId);',
      '      loadingGrids.delete(gridId);',
      '',

      '',
      '    } catch (error) {',
      '      console.error("❌ [ProductGrid] Erro no grid", gridId + ":", error);',
      '      console.error("❌ [ProductGrid] Detalhes do erro:", {',
      '        name: error.name,',
      '        message: error.message,',
      '        stack: error.stack,',
      '        response: error.response',
      '      });',
      '      let errorMessage = "Erro ao carregar produtos";',
      '      if (error.name === "AbortError") errorMessage = "Timeout na requisição";',
      '      else if (error.message.includes("fetch")) errorMessage = "Erro de conexão";',
      '      ',
      '      const title = grid.getAttribute("data-title") || "Most Popular";',
      '      const columns = parseInt(grid.getAttribute("data-columns") || "4");',
      '      const limit = parseInt(grid.getAttribute("data-limit") || "8");',
      '      ',
      '      // Mostrar tela de erro quando a API falhar',
      '      if (error.message.includes("CORS") || error.message.includes("Failed to fetch")) {',
      '        errorMessage = "Erro de conexão - Verifique se a API está disponível";',
      '      }',
      '      grid.innerHTML = createErrorHTML(title, errorMessage);',
      '      ',
      '      processedGrids.add(gridId);',
      '      loadingGrids.delete(gridId);',
      '    }',
      '  };',
      '',
      '  function processGrid(grid) {',
      '    if (!grid || !grid.nodeType) return;',
      '',
      '    // Garantir ID único',
      '    if (!grid.id) {',
      '      grid.id = "product-grid-" + Date.now() + "-" + Math.random().toString(36).substr(2, 5);',
      '    }',
      '',
      '    // Evitar reprocessamento',
      '    if (processedGrids.has(grid.id) || loadingGrids.has(grid.id)) {',
      '      return;',
      '    }',
      '',
      '    // Configurar atributos padrão',
      '    if (!grid.hasAttribute("data-columns")) grid.setAttribute("data-columns", "4");',
      '    if (!grid.hasAttribute("data-limit")) grid.setAttribute("data-limit", "8");',
      '    if (!grid.hasAttribute("data-title")) grid.setAttribute("data-title", "Most Popular");',
      '',
      '    // Configurar endpoint usando domínio da loja',
      '    let endpoint = grid.getAttribute("data-endpoint");',
      '    if (!endpoint) {',
      '      // Usar domínio da loja (definido durante a injeção)',
      `      const storefrontDomain = "${storefrontDomain}";`,
      '      endpoint = `${storefrontDomain}/api/storefront/products`;',
     
      '    }',
      '    const limit = parseInt(grid.getAttribute("data-limit")) || 8;',
      '',
      '    window.loadProductsFromAPI(endpoint, limit, grid, authToken);',
      '  }',
      '',
      '  window.initAllProductGrids = function() {',
      '    const grids = document.querySelectorAll("[data-component=\\"product-grid\\"], [data-component=\\"dynamic-grid-produtos\\"], [data-product-grid=\\"true\\"], .iluria-product-grid");',
      '',
      '    grids.forEach(function(grid) {',
      '      processGrid(grid);',
      '    });',
      '  };',
      '',
      '  // Observer simples e eficiente',
      '  let observerTimeout;',
      '  const observer = new MutationObserver(function(mutations) {',
      '    clearTimeout(observerTimeout);',
      '    observerTimeout = setTimeout(function() {',
      '      let hasNewGrids = false;',
      '      mutations.forEach(function(mutation) {',
      '        if (mutation.type === "childList") {',
      '          mutation.addedNodes.forEach(function(node) {',
      '            if (node.nodeType === 1) {',
      '              if (node.matches && node.matches("[data-component=\\"product-grid\\"], [data-component=\\"dynamic-grid-produtos\\"], [data-product-grid=\\"true\\"], .iluria-product-grid")) {',
      '                processGrid(node);',
      '                hasNewGrids = true;',
      '              }',
      '              if (node.querySelectorAll) {',
      '                const childGrids = node.querySelectorAll("[data-component=\\"product-grid\\"], [data-component=\\"dynamic-grid-produtos\\"], [data-product-grid=\\"true\\"], .iluria-product-grid");',
      '                if (childGrids.length > 0) {',
      '                  childGrids.forEach(function(grid) {',
      '                    processGrid(grid);',
      '                  });',
      '                  hasNewGrids = true;',
      '                }',
      '              }',
      '            }',
      '          });',
      '        }',
      '      });',
      '      if (hasNewGrids) {',

      '      }',
      '    }, 150);',
      '  });',
      '',
      '  // Aguardar DOM estar pronto antes de inicializar observer',
      '  function startObserver() {',
      '    if (document.body && document.body.nodeType === 1) {',
      '      try {',
      '        observer.observe(document.body, { childList: true, subtree: true });',
      '        document._productGridObserver = observer;',
      
      '      } catch (error) {',
      '        console.error("❌ [ProductGrid] Erro ao iniciar observer:", error);',
      '      }',
      '    } else {',
      '      console.warn("⚠️ [ProductGrid] document.body não disponível ou inválido, tentando novamente...");',
      '      setTimeout(startObserver, 100);',
      '    }',
      '  }',
      '',
      '  // Inicializar observer quando DOM estiver pronto',
      '  function initializeWhenReady() {',
      '    if (document.readyState === "loading") {',
      '      document.addEventListener("DOMContentLoaded", function() {',
      '        setTimeout(startObserver, 100);',
      '      });',
      '    } else if (document.readyState === "interactive" || document.readyState === "complete") {',
      '      setTimeout(startObserver, 100);',
      '    } else {',
      '      setTimeout(initializeWhenReady, 50);',
      '    }',
      '  }',
      '  ',
      '  initializeWhenReady();',
      '',
      '  // Inicializar grids existentes',
      '  setTimeout(function() {',
      '    window.initAllProductGrids();',
      '  }, 300);',
      '',
      '})();'
    ]
    
    scriptElement.textContent = scriptParts.join('\n')
    doc.head.appendChild(scriptElement)

   

  } catch (error) {
    console.error('❌ [ProductGrid] Erro ao injetar script:', error)
  }
}

export const ProductGridLoader = {
  name: 'ProductGridLoader',
  injectScript: injectProductGridScript,
  componentConfig: {
    type: 'dynamic-grid-produtos',
    name: 'Product Grid',
    description: 'Grid dinâmico de produtos',
    selectors: [
      '[data-component="dynamic-grid-produtos"]',
      '[data-product-grid="true"]',
      '[data-element-type="dynamicProductGrid"]'
    ],
    requiresAuth: true,
    autoInit: true
  }
}

export default {
  name: 'ProductGridLoader',
  setup() {
    const isLoaded = ref(false)
    
    onMounted(() => {

      isLoaded.value = true
    })
    
    return {
      isLoaded,
      injectProductGridScript
    }
  }
}
</script>

<style scoped>
/* Componente não renderiza elementos visuais */
</style>
