<template>
  <div
    class="iluria-spinner"
    :class="[
      `spinner-${size}`,
      `spinner-${variant}`,
      { 'spinner-centered': centered }
    ]"
    :style="customStyle"
  >
    <svg class="spinner-svg" viewBox="0 0 50 50">
      <circle
        class="spinner-circle"
        cx="25"
        cy="25"
        r="20"
        fill="none"
        stroke-width="4"
        stroke-linecap="round"
      />
    </svg>
  </div>
</template>

<script>
export default {
  name: 'IluriaSpinner',
  props: {
    size: {
      type: String,
      default: 'medium',
      validator: (value) => ['small', 'medium', 'large', 'xlarge'].includes(value)
    },
    variant: {
      type: String,
      default: 'primary',
      validator: (value) => ['primary', 'secondary', 'contrast', 'muted'].includes(value)
    },
    centered: {
      type: <PERSON>olean,
      default: false
    },
    width: {
      type: [String, Number],
      default: null
    },
    height: {
      type: [String, Number],
      default: null
    }
  },
  computed: {
    customStyle() {
      const style = {}
      if (this.width) {
        style.width = typeof this.width === 'number' ? `${this.width}px` : this.width
      }
      if (this.height) {
        style.height = typeof this.height === 'number' ? `${this.height}px` : this.height
      }
      return style
    }
  }
}
</script>

<style scoped>
/* Base spinner styles */
.iluria-spinner {
  position: relative;
  display: inline-block;
  flex-shrink: 0;
}

.spinner-svg {
  width: 100%;
  height: 100%;
  animation: iluria-rotate 2s linear infinite;
}

.spinner-circle {
  stroke-dasharray: 90, 150;
  stroke-dashoffset: 0;
  animation: iluria-arc-pulse 2s ease-in-out infinite;
  stroke-width: 6;
  stroke-linecap: round;
}

/* Size variants */
.spinner-small {
  width: 16px;
  height: 16px;
}

.spinner-small .spinner-circle {
  stroke-width: 5;
}

.spinner-medium {
  width: 20px;
  height: 20px;
}

.spinner-large {
  width: 32px;
  height: 32px;
}

.spinner-large .spinner-circle {
  stroke-width: 7;
}

.spinner-xlarge {
  width: 48px;
  height: 48px;
}

.spinner-xlarge .spinner-circle {
  stroke-width: 9;
}

.spinner-primary .spinner-circle {
  stroke: var(--iluria-color-button-primary-bg, var(--iluria-color-primary, #2563eb));
  filter: contrast(1.2) saturate(1.1);
}

.spinner-secondary .spinner-circle {
  stroke: var(--iluria-color-button-secondary-bg, var(--iluria-color-secondary, #64748b));
  filter: contrast(1.3) saturate(1.2);
}

.spinner-contrast .spinner-circle {
  stroke: var(--iluria-color-primary-contrast, #ffffff);
}

.spinner-muted .spinner-circle {
  stroke: var(--iluria-color-text-primary, var(--iluria-color-text, #1f2937));
  filter: contrast(1.4) brightness(0.8);
}





.spinner-centered {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  min-height: 40px;
}

@keyframes iluria-rotate {
  0% {
    transform: rotate(0deg);
  }
  100% {
    transform: rotate(360deg);
  }
}

@keyframes iluria-arc-pulse {
  0% {
    stroke-dasharray: 1, 150;
    stroke-dashoffset: 0;
  }
  50% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -35;
  }
  100% {
    stroke-dasharray: 90, 150;
    stroke-dashoffset: -125;
  }
}

@media (prefers-reduced-motion: reduce) {
  .spinner-svg {
    animation-duration: 3s;
  }

  .spinner-circle {
    animation-duration: 2.5s;
  }
}

@media (prefers-contrast: high) {
  .spinner-circle {
    stroke-width: 7;
  }

  .spinner-small .spinner-circle {
    stroke-width: 6;
  }

  .spinner-large .spinner-circle {
    stroke-width: 8;
  }

  .spinner-xlarge .spinner-circle {
    stroke-width: 10;
  }
}
</style>
