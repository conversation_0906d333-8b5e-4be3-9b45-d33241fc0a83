import axios from 'axios';
import { API_URLS } from '../config/api.config';

class LayoutsService {
    #layoutData;
    #endpoint = `${API_URLS.LAYOUT_BASE_URL}`;

    constructor() {
        this.#layoutData = {};
    }

    /**
     * Busca todos os layouts organizados por tipo
     * @returns {Object} Layouts organizados por tipo (static, product, collection, etc.)
     */
    async getLayouts() {
        try {
            const response = await axios.get(this.#endpoint);
            const layouts = response.data || [];
            
            // Organizar layouts por tipo
            const organizedLayouts = {
                static: [],
                product: [],
                collection: [],
                system: [],
                custom: []
            };
            
            layouts.forEach(layout => {
                // 🔧 DETECÇÃO INTELIGENTE: Determinar tipo baseado no filename se não estiver definido
                const type = layout.type || this.detectTypeFromFilename(layout.filename);
                if (organizedLayouts[type]) {
                    // Adicionar propriedades necessárias para o frontend
                    const frontendLayout = {
                        layoutId: layout.filename ? layout.filename.replace('.html', '') : layout.layoutId,
                        name: layout.name,
                        filename: layout.filename,
                        type: type, // 🔧 CORREÇÃO: Usar a variável type em vez de layout.type
                        icon: this.getIconForType(type), // 🔧 CORREÇÃO: Usar a variável type
                        isDefault: this.isDefaultLayout(layout.filename),
                        deletable: !this.isDefaultLayout(layout.filename),
                        duplicatable: true,
                        description: layout.description || `Layout ${layout.name}`,
                        createdAt: layout.createdAt,
                        updatedAt: layout.updatedAt
                    };
                    
                    organizedLayouts[type].push(frontendLayout);
                } else {
                    // Se o tipo não existe, adicionar em custom
                    organizedLayouts.custom.push({
                        layoutId: layout.filename ? layout.filename.replace('.html', '') : layout.layoutId,
                        name: layout.name,
                        filename: layout.filename,
                        type: type, // 🔧 CORREÇÃO: Usar a variável type em vez de layout.type
                        icon: '✨',
                        isDefault: false,
                        deletable: true,
                        duplicatable: true,
                        description: layout.description || `Layout ${layout.name}`,
                        createdAt: layout.createdAt,
                        updatedAt: layout.updatedAt
                    });
                }
            });
            
            // Ordenar cada tipo: layouts padrão primeiro, depois os customizados
            Object.keys(organizedLayouts).forEach(type => {
                organizedLayouts[type].sort((a, b) => {
                    // Layouts padrão primeiro
                    if (a.isDefault && !b.isDefault) return -1;
                    if (!a.isDefault && b.isDefault) return 1;
                    
                    // Entre layouts do mesmo tipo (padrão ou custom), ordenar por nome
                    return a.name.localeCompare(b.name);
                });
            });
            
            return organizedLayouts;
        } catch (error) {
            console.warn('API de layouts não disponível, retornando estrutura vazia:', error);
            // Em caso de erro da API, retorna estrutura vazia para funcionar offline
            // O composable vai usar os layouts padrão
            return {
                static: [],
                product: [],
                collection: [],
                system: [],
                custom: []
            };
        }
    }

    /**
     * Verifica se um layout é padrão baseado no filename
     * @param {string} filename - Nome do arquivo
     * @returns {boolean} True se for um layout padrão
     */
    isDefaultLayout(filename) {
        const defaultFiles = ['index.html', 'product.html', 'collection.html', 'cart.html', '404.html'];
        return defaultFiles.includes(filename);
    }

    /**
     * 🔧 NOVO: Detecta o tipo de layout baseado no filename
     * @param {string} filename - Nome do arquivo
     * @returns {string} Tipo detectado do layout
     */
    detectTypeFromFilename(filename) {
        if (!filename) return 'custom';
        
        // Remover extensão .html para análise
        const baseName = filename.replace('.html', '');
        
        // Layouts padrão
        if (baseName === 'index') return 'static';
        if (baseName === 'product') return 'product';
        if (baseName === 'collection') return 'collection';
        if (baseName === 'cart' || baseName === '404') return 'system';
        
        // Detectar por prefixo (para layouts customizados)
        if (baseName.startsWith('product-')) return 'product';
        if (baseName.startsWith('collection-')) return 'collection';
        if (baseName.startsWith('static-')) return 'static';
        if (baseName.startsWith('system-')) return 'system';
        
        // Default para custom se não conseguir detectar
        return 'custom';
    }

    /**
     * Obtém ícone para tipo de layout
     * @param {string} type - Tipo do layout
     * @returns {string} Emoji do ícone
     */
    getIconForType(type) {
        const icons = {
            static: '📄',
            product: '📦',
            collection: '📁',
            system: '⚙️',
            custom: '✨'
        };
        return icons[type] || '📄';
    }

    /**
     * Obtém um layout específico pelo ID
     * @param {string} layoutId - ID do layout
     * @returns {Object} Dados do layout
     */
    async getLayout(layoutId) {
        try {
            const response = await axios.get(`${this.#endpoint}/${layoutId}`);
            return response.data;
        } catch (error) {
            console.error('GetLayout error:', error);
            throw error;
        }
    }

    // Método removido: criação de layouts do zero não é mais permitida
    // Use duplicateLayout() para criar novos layouts baseados em existentes

    /**
     * Duplica um layout existente
     * @param {Object} duplicateData - Dados para duplicação
     * @param {string} duplicateData.baseLayoutId - ID do layout base
     * @param {string} duplicateData.newName - Nome do novo layout
     * @param {string} duplicateData.type - Tipo do layout
     * @returns {Object} Dados do layout duplicado
     */
    async duplicateLayout(duplicateData) {
        try {
            // Verificar se o endpoint está definido
            if (!this.#endpoint || this.#endpoint.includes('undefined')) {
                throw new Error('Endpoint de layouts não configurado corretamente');
            }
            
            const response = await axios.post(`${this.#endpoint}/duplicate`, duplicateData);
            this.#layoutData = response.data;
            return this.#layoutData;
        } catch (error) {
            console.error('DuplicateLayout error:', error);
            // Re-throw o erro para que o composable possa fazer fallback
            throw error;
        }
    }

    /**
     * Atualiza um layout existente
     * @param {Object} layoutData - Dados do layout
     * @param {string} layoutData.id - ID do layout
     * @param {string} layoutData.layoutId - ID único do layout
     * @param {string} layoutData.name - Nome do layout
     * @param {string} layoutData.type - Tipo do layout
     * @param {string} layoutData.filename - Nome do arquivo HTML
     * @param {string} layoutData.baseLayoutId - ID do layout base (opcional)
     * @param {boolean} layoutData.isDefault - Se é o layout padrão (opcional)
     * @returns {Object} Dados do layout atualizado
     */
    async updateLayout(layoutData) {
        try {
            const response = await axios.put(this.#endpoint, layoutData);
            this.#layoutData = response.data;
            return this.#layoutData;
        } catch (error) {
            console.error('UpdateLayout error:', error);
            throw error;
        }
    }

    /**
     * Remove um layout
     * @param {string} layoutId - ID do layout
     * @returns {Object} Resposta da API
     */
    async deleteLayout(layoutId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${layoutId}`);
            return response.data;
        } catch (error) {
            console.error('DeleteLayout error:', error);
            throw error;
        }
    }
}

export default new LayoutsService(); 