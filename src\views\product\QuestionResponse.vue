<template>
    <div class="question-response-container">
      <!-- Header -->
      <IluriaHeader
        :title="isEditMode ? 'Editar Resposta' : 'Responder Pergunta'"
        :subtitle="isEditMode ? 'Edite sua resposta para a pergunta do cliente' : 'Responda à pergunta do cliente'"
        :customButtons="[{
          text: $t('questions.response.backToQuestions'),
          color: 'secondary',
          variant: 'outline',
          icon: ArrowLeftIcon,
          onClick: goBack
        }]"
      />

      <!-- Product Info -->
      <ViewContainer v-if="question" title="Informações do Produto" :icon="PackageIcon" iconColor="blue">
        <div class="product-info-content">
          <div class="product-details">
            <img 
              :src="question.product.imageUrl || generateProductAvatar(question.product.name)" 
              alt="Product Image" 
              class="product-image"
              @error="handleProductImageError"
            >
            <div class="product-text">
              <h2 class="product-name">{{ question.product.name }}</h2>
              <p class="product-meta">SKU: {{ question.product.sku }} • Categoria: {{ question.product.categoryName }}</p>
            </div>
          </div>
          <span class="product-status">
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="status-icon">
              <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path><polyline points="22 4 12 14.01 9 11.01"></polyline>
            </svg>
            {{ $t('questions.response.productActive') }}
          </span>
        </div>
      </ViewContainer>
      
      <!-- Customer Question -->
      <ViewContainer v-if="question" title="Pergunta do Cliente" :icon="UserIcon" iconColor="green">
        <div class="customer-question">
          <div class="customer-info">
            <img 
              :src="question.customer.avatarUrl || generateCustomerAvatar(question.customer.name)" 
              alt="Customer Avatar" 
              class="customer-avatar"
              @error="handleCustomerImageError"
            >
            <div class="customer-details">
              <p class="customer-name">{{ question.customer.name }}</p>
              <p class="question-date">{{ formatDate(question.createdAt) }}</p>
            </div>
          </div>
          <div class="question-content">
            <p class="question-text">{{ question.questionText }}</p>
          </div>
        </div>
      </ViewContainer>

      <!-- Response Section -->
      <ViewContainer 
        :title="isEditMode ? $t('questions.response.editYourResponse') : $t('questions.response.yourResponse')" 
        :icon="BubbleChatIcon" 
        iconColor="purple"
      >
        <template #headerActions>
          <span v-if="isEditMode" class="edit-mode-badge">
            <svg xmlns="http://www.w3.org/2000/svg" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="edit-icon">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7" />
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z" />
            </svg>
            {{ $t('questions.response.editMode') }}
          </span>
        </template>

        <div class="response-editor">
          <IluriaEditor v-model="responseText" :show-mode-toggle="false" height="150px" />
        </div>
      </ViewContainer>

      <!-- AI Suggestion and Predefined Answers -->
      <div class="suggestions-grid">
        <!-- AI Suggestion -->
        <ViewContainer title="Sugestão de IA" :icon="BulbIcon" iconColor="blue">
          <div class="ai-suggestion-content">
            <p class="ai-suggestion-text">{{ question?.aiSuggestion || 'Sugestão de resposta será gerada aqui...' }}</p>
            <div class="ai-suggestion-actions">
              <IluriaButton 
                @click="useAiSuggestion"
                size="small"
                color="primary"
                :hugeIcon="CheckmarkCircle02Icon"
              >
                {{ $t('questions.response.useThisResponse') }}
              </IluriaButton>
              <IluriaButton 
                @click="generateAnotherSuggestion"
                size="small"
                color="text-secondary"
                variant="outline"
                :hugeIcon="RefreshIcon"
              >
                {{ $t('questions.response.generateAnother') }}
              </IluriaButton>
            </div>
          </div>
        </ViewContainer>

        <!-- Predefined Answers -->
        <ViewContainer title="Respostas Predefinidas" :icon="DocumentAttachmentIcon" iconColor="orange">
          <div class="predefined-answers-content">
            <IluriaSelect 
              :placeholder="predefinedAnswers.length > 0 ? $t('questions.response.selectPredefinedAnswer') : 'Nenhuma resposta predefinida disponível'" 
              :options="predefinedAnswers"
              optionLabel="label"
              optionValue="value"
              v-model="selectedPredefinedAnswer"
              :disabled="predefinedAnswers.length === 0"
            />
            <p v-if="predefinedAnswers.length === 0" class="predefined-help-text">
              Você pode criar respostas predefinidas marcando a opção "Salvar como resposta pré-definida" ao responder perguntas.
            </p>
          </div>
        </ViewContainer>
      </div>

      <!-- Highlight Checkbox - Only show when NOT in edit mode -->
      <ViewContainer v-if="!isEditMode" title="Opções de Salvamento" :icon="SettingsIcon" iconColor="gray">
        <div class="save-options">
          <div class="checkbox-group">
            <IluriaCheckbox v-model="saveAsPredefined" inputId="saveAsPredefined" />
            <label for="saveAsPredefined" class="checkbox-label">{{ $t('questions.response.saveAsPredefined') }}</label>
            <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="help-icon">
              <circle cx="12" cy="12" r="10"></circle><line x1="12" y1="16" x2="12" y2="12"></line><line x1="12" y1="8" x2="12.01" y2="8"></line>
            </svg>
          </div>
          
          <div v-if="saveAsPredefined" class="predefined-title-input">
              <IluriaInputText v-model="predefinedTitle" :placeholder="$t('questions.response.predefinedTitle')" />
          </div>
        </div>
      </ViewContainer>

      <!-- Edit Mode Info - Only show when in edit mode and has predefined answer -->
      <ViewContainer v-if="isEditMode && existingPredefinedAnswer" title="Informação da Resposta Predefinida" :icon="InformationCircleIcon" iconColor="blue">
        <div class="predefined-info">
          <svg xmlns="http://www.w3.org/2000/svg" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="info-icon">
            <circle cx="12" cy="12" r="10"></circle>
            <line x1="12" y1="16" x2="12" y2="12"></line>
            <line x1="12" y1="8" x2="12.01" y2="8"></line>
          </svg>
          <span class="predefined-info-text">
            Esta resposta está salva como predefinida: "<strong>{{ existingPredefinedAnswer.title }}</strong>"
          </span>
        </div>
      </ViewContainer>

      <!-- Action Buttons -->
      <div class="action-buttons-container">
        <div class="action-buttons">
          <IluriaButton 
            color="text-secondary" 
            variant="outline"
            @click="goBack"
            :hugeIcon="CancelSquareIcon"
          >
            {{ $t('questions.response.cancel') }}
          </IluriaButton>

          <div class="primary-actions">
            <IluriaButton 
              color="success" 
              size="medium" 
              @click="submitAnswer(false)" 
              :disabled="!responseText.trim()"
              :hugeIcon="CheckmarkCircle02Icon"
            >
              {{ isEditMode ? $t('questions.response.updateResponse') : $t('questions.response.publishResponse') }}
            </IluriaButton>
            <IluriaButton 
              color="warning" 
              size="medium" 
              @click="submitAnswer(true)" 
              :disabled="!responseText.trim()"
              :hugeIcon="StarIcon"
            >
              {{ isEditMode ? $t('questions.response.updateHighlighted') : $t('questions.response.publishHighlighted') }}
            </IluriaButton>
          </div>
        </div>
      </div>
    </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue';
import { useRoute, useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import questionsApi from '@/services/question.service';
import IluriaEditor from '@/components/editor/IluriaEditor.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import IluriaCheckbox from '@/components/iluria/IluriaCheckbox.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { useToast } from '@/services/toast.service';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import { generateProductAvatar, generateCustomerAvatar, handleImageError } from '@/utils/avatarUtils';
import { useTheme } from '@/composables/useTheme';
import {
  ArrowLeftIcon,
  PackageIcon,
  UserIcon,
  BubbleChatIcon,
  BulbIcon,
  DocumentAttachmentIcon,
  SettingsIcon,
  InformationCircleIcon,
  CheckmarkCircle02Icon,
  RefreshIcon,
  StarIcon,
  CancelSquareIcon
} from '@hugeicons-pro/core-stroke-rounded';

const route = useRoute();
const router = useRouter();
const { t } = useI18n();
const toast = useToast();

// Initialize theme system
const { initTheme } = useTheme();
initTheme();

const question = ref(null);
const loading = ref(true);
const responseText = ref('');
const selectedPredefinedAnswer = ref(null);
const saveAsPredefined = ref(false);
const predefinedTitle = ref('');
const isEditMode = ref(false);
const existingPredefinedAnswer = ref(null);
const ignoreTextWatcher = ref(false);

const fetchQuestionDetails = async () => {
  loading.value = true;
  try {
    const { data } = await questionsApi.getById(route.params.id);
    question.value = data;
    
    // Se já tem resposta, entrar em modo de edição
    if (data.answers && data.answers.length > 0) {
      isEditMode.value = true;
      
      // Marcar para ignorar o watcher ao carregar dados existentes
      ignoreTextWatcher.value = true;
      responseText.value = data.answers[0].answerText;
      
      // Verificar se esta resposta foi salva como predefinida
      await checkIfResponseIsPredefined(data.answers[0].answerText);
    }
  } catch (error) {
    console.error('Erro ao carregar pergunta:', error);
    router.push({ name: 'questions-answers' });
  } finally {
    loading.value = false;
  }
};

const checkIfResponseIsPredefined = async (answerText) => {
  try {
    const { data } = await questionsApi.getPredefinedAnswers();
    const predefined = data.find(answer => answer.answerText === answerText);
    
    if (predefined) {
      existingPredefinedAnswer.value = predefined;
      saveAsPredefined.value = true;
      predefinedTitle.value = predefined.title;
    }
  } catch (error) {
    console.warn('Erro ao verificar respostas predefinidas:', error);
  }
};

const submitAnswer = async (isHighlighted = false) => {
  if (!responseText.value.trim()) {
    toast.showError(t('questions.response.responseEmpty'));
    return;
  }
  
  // Só validar predefinida se não estiver em modo de edição
  if (!isEditMode.value && saveAsPredefined.value && !predefinedTitle.value.trim()) {
    toast.showError(t('questions.response.predefinedTitleRequired'));
    return;
  }

  // Gerenciar resposta predefinida (só quando não está editando)
  if (!isEditMode.value) {
    await managePredefinedAnswer();
  }

  const payload = {
    answerText: responseText.value,
    saveAsPredefined: !isEditMode.value ? saveAsPredefined.value : false,
    predefinedTitle: (!isEditMode.value && saveAsPredefined.value) ? predefinedTitle.value : undefined,
    isHighlighted: isHighlighted
  };

  try {
    if (isEditMode.value) {
      // Modo de edição: usar PUT para atualizar resposta existente
      await questionsApi.updateAnswer(question.value.id, payload);
    } else {
      // Modo de criação: usar POST para criar nova resposta
      await questionsApi.answer(question.value.id, payload);
    }
    
    toast.showSuccess(isEditMode.value ? t('questions.response.responseUpdated') : t('questions.response.responseSuccess'));
    
    setTimeout(() => {
      router.push({ name: 'questions-answers' });
    }, 500);
  } catch (error) {
    console.error('Erro ao enviar resposta:', error);
    toast.showError(t('questions.response.responseError'));
  }
};

const managePredefinedAnswer = async () => {
  // Caso 1: Existia predefinida e foi desmarcada (remover)
  if (existingPredefinedAnswer.value && !saveAsPredefined.value) {
    try {
      await questionsApi.deletePredefinedAnswer(existingPredefinedAnswer.value.id);
    } catch (error) {
      console.error('Erro ao remover resposta predefinida:', error);
    }
  }
  
  // Caso 2: Existia predefinida e foi editada (atualizar)
  else if (existingPredefinedAnswer.value && saveAsPredefined.value && 
           (predefinedTitle.value !== existingPredefinedAnswer.value.title || 
            responseText.value !== existingPredefinedAnswer.value.answerText)) {
    try {
      await questionsApi.updatePredefinedAnswer(existingPredefinedAnswer.value.id, {
        title: predefinedTitle.value,
        answerText: responseText.value
      });
    } catch (error) {
      console.error('Erro ao atualizar resposta predefinida:', error);
    }
  }
  
  // Caso 3: Não existia predefinida e foi marcada (criar)
  // Este caso será tratado pelo backend através do saveAsPredefined
};

const goBack = () => {
  router.push({ name: 'questions-answers' });
};

const formatDate = (dateString) => {
  if (!dateString) return '';
  const date = new Date(dateString);
  return date.toLocaleString('pt-BR', {
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  });
};

// Função para limpar HTML tags e comparar apenas o texto
const stripHtml = (html) => {
  const tmp = document.createElement("DIV");
  tmp.innerHTML = html;
  return tmp.textContent || tmp.innerText || "";
};

// Watcher para detectar mudanças no texto e deselecionar predefinida
watch(responseText, (newText, oldText) => {
  // Ignorar mudanças programáticas
  if (ignoreTextWatcher.value) {
    ignoreTextWatcher.value = false;
    return;
  }
  
  // Se mudou o texto e tinha uma predefinida selecionada, deselecionar
  if (newText !== oldText && selectedPredefinedAnswer.value) {
    // Comparar o texto limpo (sem HTML) com a predefinida selecionada
    const cleanNewText = stripHtml(newText).trim();
    const cleanPredefinedText = stripHtml(selectedPredefinedAnswer.value).trim();
    
    if (cleanNewText !== cleanPredefinedText) {
      selectedPredefinedAnswer.value = null;
    }
  }
});

// Watcher para quando uma resposta predefinida é selecionada
watch(selectedPredefinedAnswer, (newValue) => {
  if (newValue) {
    onPredefinedAnswerChange();
  }
});

onMounted(() => {
  fetchQuestionDetails();
  fetchPredefinedAnswers();
});

const predefinedAnswers = ref([]);

const fetchPredefinedAnswers = async () => {
  try {
    const { data } = await questionsApi.getPredefinedAnswers();
    predefinedAnswers.value = data.map(answer => ({
      id: answer.id,
      label: answer.title,
      value: answer.answerText
    }));
  } catch (error) {
    console.warn('Respostas predefinidas não disponíveis:', error.message);
    predefinedAnswers.value = []; // Garantir que seja um array vazio se falhar
    
    // Se for erro 404, significa que não há respostas predefinidas ainda
    if (error.response?.status === 404) {
      console.info('Nenhuma resposta predefinida encontrada. Isso é normal se for a primeira vez usando o sistema.');
    } else {
      // Para outros erros, mostrar no console mas não interromper a aplicação
      console.error('Erro ao carregar respostas predefinidas:', error);
    }
  }
};

const useAiSuggestion = () => {
  if (question.value?.aiSuggestion) {
    // Marcar para ignorar o watcher
    ignoreTextWatcher.value = true;
    
    responseText.value = question.value.aiSuggestion;
    
    // Limpar qualquer predefinida selecionada
    selectedPredefinedAnswer.value = null;
    
    // Força atualização do editor
    nextTick(() => {
      ignoreTextWatcher.value = true;
      responseText.value = question.value.aiSuggestion;
    });
  }
};

const generateAnotherSuggestion = () => {
  

};

const onPredefinedAnswerChange = async () => {
  if (selectedPredefinedAnswer.value) {
    
    // Marcar para ignorar o watcher na próxima mudança
    ignoreTextWatcher.value = true;
    
    // Força a atualização do texto no editor
    responseText.value = selectedPredefinedAnswer.value;
    
    // Aguardar o próximo tick para garantir que o DOM foi atualizado
    await nextTick();
    
    // Segunda tentativa de atualização para garantir
    setTimeout(() => {
      ignoreTextWatcher.value = true;
      responseText.value = selectedPredefinedAnswer.value;
    }, 100);
    
    // Só incrementar contador se não estiver editando uma resposta predefinida existente
    if (!isEditMode.value || !existingPredefinedAnswer.value) {
      incrementPredefinedAnswerUsage();
    }
  }
};

const incrementPredefinedAnswerUsage = async () => {
  if (!selectedPredefinedAnswer.value) return;
  
  // Encontrar o ID da resposta predefinida selecionada
  const predefinedAnswer = predefinedAnswers.value.find(answer => 
    answer.value === selectedPredefinedAnswer.value
  );
  
  if (predefinedAnswer?.id) {
    try {
      await questionsApi.incrementPredefinedAnswerUsage(predefinedAnswer.id);
    } catch (error) {
      console.error('Erro ao incrementar uso da resposta predefinida:', error);
    }
  }
};

// Métodos para tratar erros de imagem
const handleProductImageError = (event) => {
  if (question.value?.product?.name) {
    handleImageError(event, question.value.product.name, 'product');
  }
};

const handleCustomerImageError = (event) => {
  if (question.value?.customer?.name) {
    handleImageError(event, question.value.customer.name, 'user');
  }
};

</script>

<style scoped>
.question-response-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
  display: flex;
  flex-direction: column;
  gap: 24px;
}



/* Product Info */
.product-info-content {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 16px;
}

.product-details {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.product-image {
  width: 64px;
  height: 64px;
  border-radius: 8px;
  object-fit: cover;
  border: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-sidebar-bg);
}

.product-text {
  flex: 1;
  min-width: 0;
}

.product-name {
  font-size: 20px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.3;
}

.product-meta {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.product-status {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 12px;
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  flex-shrink: 0;
}

.status-icon {
  width: 16px;
  height: 16px;
}

/* Customer Question */
.customer-question {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.customer-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.customer-avatar {
  width: 40px;
  height: 40px;
  border-radius: 50%;
  object-fit: cover;
  border: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-sidebar-bg);
}

.customer-details {
  flex: 1;
}

.customer-name {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 2px 0;
}

.question-date {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.question-content {
  padding: 16px;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.question-text {
  font-size: 15px;
  color: var(--iluria-color-text);
  line-height: 1.5;
  margin: 0;
}

/* Response Editor */
.response-editor {
  margin-top: 8px;
}

.edit-mode-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 8px;
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border-radius: 6px;
  font-size: 12px;
  font-weight: 500;
}

.edit-icon {
  width: 12px;
  height: 12px;
}

/* Suggestions Grid */
.suggestions-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
}

/* AI Suggestion */
.ai-suggestion-content {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.ai-suggestion-text {
  font-size: 14px;
  color: var(--iluria-color-text);
  line-height: 1.5;
  margin: 0;
  padding: 12px;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.ai-suggestion-actions {
  display: flex;
  gap: 12px;
}

/* Predefined Answers */
.predefined-answers-content {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.predefined-help-text {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

/* Save Options */
.save-options {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.checkbox-group {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-label {
  font-size: 14px;
  color: var(--iluria-color-text);
  font-weight: 500;
  cursor: pointer;
}

.help-icon {
  color: var(--iluria-color-text-muted);
}

.predefined-title-input {
  margin-left: 32px;
}

/* Predefined Info */
.predefined-info {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px;
  background: rgba(59, 130, 246, 0.1);
  border-radius: 8px;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.info-icon {
  color: #2563eb;
  flex-shrink: 0;
}

.predefined-info-text {
  font-size: 14px;
  color: #1e40af;
}

/* Action Buttons */
.action-buttons-container {
  padding-top: 24px;
  border-top: 1px solid var(--iluria-color-border);
  margin-top: auto;
}

.action-buttons {
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.primary-actions {
  display: flex;
  gap: 12px;
}

/* Responsive */
@media (max-width: 1024px) {

  
  .suggestions-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .product-info-content {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .product-status {
    align-self: flex-start;
  }
}

@media (max-width: 768px) {
  .question-response-container {
    padding: 16px;
    gap: 20px;
  }
  

  
  .product-details {
    gap: 12px;
  }
  
  .product-image {
    width: 48px;
    height: 48px;
  }
  
  .product-name {
    font-size: 18px;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 12px;
  }
  
  .primary-actions {
    width: 100%;
    justify-content: space-between;
  }
  
  .ai-suggestion-actions {
    flex-direction: column;
  }
}

@media (max-width: 480px) {

  
  .product-details {
    gap: 8px;
  }
  
  .product-image {
    width: 40px;
    height: 40px;
  }
  
  .product-name {
    font-size: 16px;
  }
  
  .customer-info {
    gap: 8px;
  }
  
  .customer-avatar {
    width: 32px;
    height: 32px;
  }
  
  .primary-actions {
    flex-direction: column;
    gap: 8px;
  }
}
</style> 
