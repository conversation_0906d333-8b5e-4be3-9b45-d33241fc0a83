import axios from 'axios';
import { API_URLS } from '../config/api.config';

class PagesService {
    #pageData;
    #endpoint = `${API_URLS.PAGE_BASE_URL}`;

    constructor() {
        this.#pageData = {};
    }

    /**
     * Busca páginas com paginação e filtro opcional
     * @param {string} filter - Texto para filtrar páginas
     * @param {number} page - Número da página (0-based)
     * @param {number} size - Tam<PERSON>ho da página
     * @returns {Object} Conteúdo das páginas e total de páginas
     */
    async getPages(filter = '', page = 0, size = 10) {
        try {
            const response = await axios.get(`${this.#endpoint}/search`, {
                params: { 
                    filter, 
                    page, 
                    size
                }
            });
            
            return {
                content: response.data.content || [],
                totalPages: response.data.totalPages || 0
            };
        } catch (error) {
            console.error('GetPages error:', error);
            return {
                content: [],
                totalPages: 0
            };
        }
    }

    /**
     * Obtém uma página específica pelo ID
     * @param {string} pageId - ID da página
     * @returns {Object} Dados da página incluindo metadados
     */
    async getPage(pageId) {
        try {
            const response = await axios.get(`${this.#endpoint}/${pageId}`);
            return response.data;
        } catch (error) {
            console.error('GetPage error:', error);
            throw error;
        }
    }

    /**
     * Cria uma nova página
     * @param {Object} pageData - Dados da página
     * @param {string} pageData.title - Título da página
     * @param {string} pageData.content - Conteúdo HTML da página
     * @param {boolean} pageData.activeProd - Status de publicação em produção
     * @param {boolean} pageData.activeDev - Status de publicação em desenvolvimento
     * @param {string} pageData.metaTitle - Título para SEO (opcional)
     * @param {string} pageData.metaDescription - Descrição para SEO (opcional)
     * @param {string} pageData.slug - URL amigável (opcional)
     * @returns {Object} Dados da página criada
     */
    async createPage(pageData) {
        try {
            const response = await axios.post(this.#endpoint, pageData);
            this.#pageData = response.data;
            return this.#pageData;
        } catch (error) {
            console.error('CreatePage error:', error);
            throw error;
        }
    }

    /**
     * Atualiza uma página existente
     * @param {Object} pageData - Dados da página
     * @param {string} pageData.id - ID da página
     * @param {string} pageData.title - Título da página
     * @param {string} pageData.content - Conteúdo HTML da página
     * @param {boolean} pageData.activeProd - Status de publicação em produção
     * @param {boolean} pageData.activeDev - Status de publicação em desenvolvimento
     * @param {string} pageData.metaTitle - Título para SEO (opcional)
     * @param {string} pageData.metaDescription - Descrição para SEO (opcional)
     * @param {string} pageData.slug - URL amigável (opcional)
     * @returns {Object} Dados da página atualizada
     */
    async updatePage(pageData) {
        try {
            const response = await axios.put(`${this.#endpoint}`, pageData);
            this.#pageData = response.data;
            return this.#pageData;
        } catch (error) {
            console.error('UpdatePage error:', error);
            throw error;
        }
    }

    async deletePage(pageId) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${pageId}`);
            return response.data;
        } catch (error) {
            console.error('DeletePage error:', error);
            throw error;
        }
    }
}

export default new PagesService();
