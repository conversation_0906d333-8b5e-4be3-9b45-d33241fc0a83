<template>
  <div class="category-container">
    <!-- Search Input -->
    <div class="mb-4">
      <input 
        type="search" 
        v-model="searchQuery"
        class="block w-full p-2 text-sm rounded-lg bg-white search-input border border-gray-200"
        :placeholder="t('category.searchPlaceHolder')"
        :disabled="isLoading"
      />
    </div>
    
    <!-- Loading State -->
    <div v-if="isLoading" class="flex justify-center items-center p-4">
      <div class="animate-spin rounded-full h-6 w-6 border-b-2 border-gray-500"></div>
    </div>
    
    <!-- Error State -->
    <div v-else-if="error" class="text-red-500 p-2 text-center">
      {{ t('category.loadError') }}
    </div>
    
    <!-- Categories List -->
    <div v-else class="categories-list">
      <div v-if="filteredCategories.length === 0" class="text-center p-4 text-gray-500">
        {{ t('category.noCategories') }}
      </div>
      <div v-else v-for="category in filteredCategories" :key="category.id" class="text-left">
        <IluriaCategoryItem 
          :category="category" 
          :toggleExpand="toggleExpand"
          :toggleCheck="toggleCheck"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch, defineProps, defineEmits } from 'vue';
import IluriaCategoryItem from '@/components/iluria/IluriaCategoryItem.vue';
import { categoryService } from '@/services/category.service';
import { useI18n } from 'vue-i18n';

const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  selectedIds: {
    type: Array,
    default: () => []
  }
});

const emit = defineEmits(['update:modelValue', 'update:categoryName']);

const { t } = useI18n();

const searchQuery = ref('');
const isLoading = ref(false);
const error = ref(null);

const categories = ref([]);

const filterCategories = (items, query) => {
  if (!query) return items;

  return items.map(category => {
    const matchesSearch = category.name.toLowerCase().includes(query.toLowerCase());
    
    let filteredChildren = [];
    if (category.children && category.children.length > 0) {
      filteredChildren = filterCategories(category.children, query);
    }

    if (matchesSearch || filteredChildren.length > 0) {
      return {
        ...category,
        children: filteredChildren,
        expanded: filteredChildren.length > 0 ? true : category.expanded
      };
    }
    
    return null;
  }).filter(Boolean);
};

const filteredCategories = computed(() => {
  return filterCategories(categories.value, searchQuery.value);
});

const toggleExpand = (category) => {
  category.expanded = !category.expanded;
};

const findParentCategory = (categoryId, categories) => {
  for (const category of categories) {
    if (category.children) {
      for (const child of category.children) {
        if (child.id === categoryId) {
          return category;
        }
        const found = findParentCategory(categoryId, [child]);
        if (found) return found;
      }
    }
  }
  return null;
};

const checkParents = (category) => {
  let parent = findParentCategory(category.id, categories.value);
  while (parent) {
    parent.checked = true;
    parent = findParentCategory(parent.id, categories.value);
  }
};

const fetchCategories = async () => {
  isLoading.value = true;
  error.value = null;
  
  try {
    const data = await categoryService.fetchCategories();
    categories.value = formatCategories(data);
  } catch (err) {
    console.error(t('category.loadError'), err);
    error.value = t('category.loadError');
  } finally {
    isLoading.value = false;
  }
};

const formatCategories = (backendCategories) => {
  const formatCategory = (category) => {
    return {
      id: category.id,
      name: category.title,
      // Marca a categoria se ela estiver no array selectedIds
      checked: props.selectedIds.includes(category.id),
      expanded: false,
      children: category.children ? category.children.map(child => formatCategory(child)) : []
    };
  };

  return backendCategories.map(category => formatCategory(category));
};

onMounted(() => {
  fetchCategories();
});

const propagateCheckState = (category) => {
  if (!category.children || category.children.length === 0) return;
  
  category.children.forEach(child => {
    child.checked = category.checked;
    propagateCheckState(child);
  });
};

const toggleCheck = (category) => {
  category.checked = !category.checked;
  
  if (!category.checked) {
    propagateCheckState(category);
  }
  
  if (category.checked) {
    checkParents(category);
  }
  
  // Recalcula o conjunto completo de IDs selecionados (pais + itens marcados)
  const buildSelectedIds = (items, ancestors = []) => {
    const ids = [];
    items.forEach(item => {
      if (item.checked) {
        // inclui ancestrais e o item
        ids.push(...ancestors, item.id);
      }
      if (item.children && item.children.length) {
        ids.push(...buildSelectedIds(item.children, [...ancestors, item.id]));
      }
    });
    return ids;
  };
  
  const allSelected = Array.from(new Set(buildSelectedIds(categories.value)));
  
  // Se ainda quiser manter modelValue para compatibilidade, envia último item alternado
  emit('update:modelValue', category.checked ? category.id : null);
  emit('update:categoryName', category.checked ? category.name : null);
  emit('update:categoryIds', allSelected);
};

const initializeCheckedState = () => {
  if (!categories.value.length || props.selectedIds.length === 0) return;

  const markIds = (items, ids) => {
    for (const item of items) {
      if (ids.includes(item.id)) {
        item.checked = true;
        checkParents(item);
      }
      if (item.children && item.children.length > 0) {
        markIds(item.children, ids);
      }
    }
  };

  markIds(categories.value, props.selectedIds);
};

watch(() => categories.value, () => {
  initializeCheckedState();
}, { immediate: true });
</script>

<style scoped>
.category-container {
  min-height: 200px;
  max-height: 300px;
}

.categories-list {
  overflow-y: auto;
  max-height: 250px;
}

.search-input:focus {
  outline: none;
  box-shadow: none;
  border: none;
}

.search-input:focus-visible {
  outline: none;
}
</style>
