{"title": "SEO Product Variation", "productVariationsTitle": "Product Variations", "description": "Configure the SEO for each product variation using the 'Edit' button in the table below.", "image": "Image", "metaTitle": "Meta Title", "metaDescription": "Meta Description", "urlSlug": "URL Slug", "status": "Status", "actions": "Actions", "editButton": "Edit", "variationType": "Variation Type", "selectAttribute": "Select an attribute", "noVariationsFound": "No variations found for this attribute", "complete": "Complete", "incomplete": "Incomplete", "metaDescriptionFallback": "Product available in the option", "seoVariationDataCleared": "SEO variation data was removed when changing to a simple product", "imageAdvice": "The variation structure has changed, the default image is no longer applied automatically. To change the image, click 'Edit' and select an image.", "imageFromVariationWarning": "Using variation image - we recommend adding a specific image for SEO", "usingVariationImage": "Using variation image", "seoImageOptimizationTitle": "SEO Image Optimization", "seoImageOptimizationMessage": "Some variations are using variation images for SEO. We recommend adding specific images for each variation for better SEO optimization.", "validation": {"allFieldsRequired": "All SEO fields for variations must be filled before saving the product", "metaTitleRequired": "Meta title is required", "metaDescriptionRequired": "Meta description is required", "urlSlugRequired": "URL is required", "seoImageRequired": "SEO image is recommended"}, "edit": {"variationImage": "Variation Image", "saveSettings": "Save Settings", "changeImage": "Change", "metaDescriptionPlaceholder": "Enter the meta description", "restoreDefault": "<PERSON><PERSON>", "variationType": "Variation", "metaTitlePlaceholder": "Enter the meta title", "urlSlugPlaceholder": "Enter the URL", "noImageSelected": "No image selected", "seoImageNotification": "Select an image for the variation or keep it to apply the default", "success": "Setting<PERSON> saved successfully!", "metaTitleRequired": "Meta title is required", "urlRequired": "URL is required", "metaDescriptionRequired": "Meta description is required", "defaultValuesRestored": "Default values restored", "defaultValuesRestoredNoImage": "Default values restored", "imageUpdated": "SEO image updated successfully!", "imageRemoved": "SEO image removed", "imageRemovedNoImage": "SEO image removed - no default image available", "imageDeletedFromServer": "Image removed from server successfully!", "imageDeleteError": "Error removing image from server", "uploadingImage": "Uploading image...", "imageUploadSuccess": "SEO image uploaded successfully!", "imageUploadError": "Error uploading SEO image", "imageUploadGenericError": "Error uploading SEO image: {error}", "variationImageDescription": "SEO image for variation"}}