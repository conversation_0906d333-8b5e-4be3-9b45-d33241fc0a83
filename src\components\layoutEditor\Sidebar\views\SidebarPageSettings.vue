<template>
  <div class="sidebar-page-settings">
    <PageSettings 
      @settings-updated="handleSettingsUpdated"
      class="sidebar-page-settings-content"
    />
  </div>
</template>

<script setup>
import PageSettings from '../tabs/PageSettings.vue'

const emit = defineEmits(['settings-updated', 'back'])

const handleSettingsUpdated = (settings) => {

  emit('settings-updated', settings)
}
</script>

<style scoped>
.sidebar-page-settings {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.sidebar-page-settings-content {
  flex: 1;
  overflow: hidden;
}

/* Ajustes para sidebar */
.sidebar-page-settings-content :deep(.page-settings) {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.sidebar-page-settings-content :deep(.settings-content) {
  flex: 1;
  overflow-y: auto;
  padding: 0;
}

.sidebar-page-settings-content :deep(.settings-section) {
  border-bottom: 1px solid var(--iluria-color-border);
  margin-bottom: 0;
}

.sidebar-page-settings-content :deep(.section-title) {
  padding: 1rem 1rem 0.5rem 1rem;
  margin: 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: var(--iluria-color-container-bg);
}

.sidebar-page-settings-content :deep(.section-content) {
  padding: 0 1rem 1rem 1rem;
}

.sidebar-page-settings-content :deep(.form-group) {
  margin-bottom: 1rem;
}

.sidebar-page-settings-content :deep(.form-label) {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 0.5rem;
}

.sidebar-page-settings-content :deep(.form-input) {
  width: 100%;
  padding: 0.75rem;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.sidebar-page-settings-content :deep(.form-input:focus) {
  outline: none;
  border-color: var(--iluria-color-primary-border);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
}

.sidebar-page-settings-content :deep(.form-textarea) {
  min-height: 80px;
  resize: vertical;
}

.sidebar-page-settings-content :deep(.color-picker) {
  width: 100%;
}

.sidebar-page-settings-content :deep(.file-upload) {
  width: 100%;
}

/* Ajustes para componentes específicos */
.sidebar-page-settings-content :deep(.theme-selector) {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(120px, 1fr));
  gap: 0.75rem;
}

.sidebar-page-settings-content :deep(.theme-option) {
  aspect-ratio: 1;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  border: 2px solid transparent;
}

.sidebar-page-settings-content :deep(.theme-option:hover) {
  border-color: var(--iluria-color-primary-border);
  transform: scale(1.02);
}

.sidebar-page-settings-content :deep(.theme-option.active) {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
}

/* Layout responsivo */
@media (max-width: 768px) {
  .sidebar-page-settings-content :deep(.section-title) {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  
  .sidebar-page-settings-content :deep(.section-content) {
    padding: 0 0.75rem 0.75rem 0.75rem;
  }
  
  .sidebar-page-settings-content :deep(.form-input) {
    padding: 0.625rem;
    font-size: 0.8rem;
  }
  
  .sidebar-page-settings-content :deep(.theme-selector) {
    grid-template-columns: repeat(auto-fit, minmax(100px, 1fr));
    gap: 0.5rem;
  }
}

/* Scrollbar personalizada */
.sidebar-page-settings-content :deep(.settings-content)::-webkit-scrollbar {
  width: 6px;
}

.sidebar-page-settings-content :deep(.settings-content)::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.sidebar-page-settings-content :deep(.settings-content)::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.sidebar-page-settings-content :deep(.settings-content)::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-secondary);
}
</style> 
