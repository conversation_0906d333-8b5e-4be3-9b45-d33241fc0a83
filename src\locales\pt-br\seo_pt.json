{"title": "Configurações SEO", "storeSectionTitle": "Informações para a loja", "searchEnginesSectionTitle": "Informações para mecanismos de busca", "useCategoryImage": "Usar a mesma imagem da categoria na imagem de SEO", "metaTitle": "<PERSON><PERSON><PERSON><PERSON> (meta-title)", "metaDescription": "Descrição da Página (meta-description)", "metaDescriptionPlaceholder": "", "metaKeywords": "Palavras-chave (separadas por vírgula)", "metaRobots": "Instruções para robôs de busca", "canonicalUrl": "URL Canônico", "ogTitle": "<PERSON><PERSON><PERSON><PERSON> para Redes Sociais (og:title)", "ogDescription": "Descrição para Redes Sociais (og:description)", "ogType": "<PERSON><PERSON><PERSON> de Conteúdo (og:type)", "ogImage": "Imagem para Redes Sociais (og:image)", "twitterCard": "Tipo de Card do Twitter", "twitterTitle": "<PERSON><PERSON><PERSON><PERSON>", "twitterDescription": "Descrição para Twitter", "twitterImage": "Imagem para Twitter", "saveSuccess": "Configurações SEO salvas com sucesso", "saveError": "<PERSON>rro ao <PERSON>l<PERSON> as configurações SEO", "loadError": "<PERSON><PERSON> a<PERSON> as configurações SEO", "generateFromContent": "Gerar automaticamente a partir do conteúdo", "preview": "Pré-visualização", "seoPreview": "Pré-visualização de Resultado de Busca", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> (meta-title)", "pageUrl": "URL da Página", "pageDescription": "Descrição da Página", "pageDescriptionPlaceholder": "Descrição", "characters": "caracteres", "recommendedLengths": {"title": "Recomendado: 50-60 caracteres", "description": "Recomendado: 150-160 caracteres"}, "socialPreview": "Pré-visualização para Redes Sociais", "socialTitle": "<PERSON><PERSON><PERSON><PERSON>", "socialDescription": "Descrição", "socialImage": "Imagem", "uploadImage": "Enviar Imagem", "changeImage": "Alterar Imagem", "removeImage": "Remover Imagem", "imageRequirements": "A imagem deve ter no mínimo 1200x630 pixels", "advancedSettings": "Configurações Avançadas", "schemaMarkup": "Marcação Schema.org", "customMetaTags": "Tags Meta Personalizadas", "addCustomTag": "Adicionar Tag Personalizada", "tagName": "<PERSON><PERSON> da <PERSON>", "tagContent": "<PERSON><PERSON><PERSON><PERSON>", "removeTag": "Remover Tag", "saveBeforePreview": "<PERSON>ve as alterações para visualizar as mud<PERSON><PERSON><PERSON>", "slug": "URL do produto (slug)", "slugHelp": "Informações da página para melhorar visibilidade em buscadores.", "slugPlaceholder": "Esse texto fará parte da URL"}