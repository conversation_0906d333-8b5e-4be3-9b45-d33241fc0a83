<template>
  <div>
    <label :for="id" v-if="label" class="mb-2 cursor-pointer block text-sm font-normal">
      {{ label }}
    </label>
    <InputMask
      :mask="mask" 
      :id="id" 
      :name="name || id" 
      v-model="model" 
      :type="type"
      :disabled="disabled"
      :placeholder="placeholder"
      :required="required"
      :unmask="true"
      :unstyled="true"
      class="w-full block h-[38px]
            px-3 py-2 rounded-lg
            text-base sm:text-sm/6
            border-2 transition-colors"
      :class="[inputClass, 
            formContext?.invalid 
            ? 'input-error border-[var(--iluria-color-error)]' 
            : 'border-[var(--iluria-color-input-border)] focus:border-[var(--iluria-color-input-border-focus)] focus:shadow-[0_0_0_2px_var(--iluria-color-focus-ring)]']"
      :style="{ 
        backgroundColor: 'var(--iluria-color-input-bg)', 
        color: 'var(--iluria-color-input-text)' 
      }"
    />
    <Message v-if="formContext && formContext?.invalid" severity="error" size="small" variant="simple"
      class="mt-1">
      {{ formContext?.error?.message }} 
    </Message>
  </div>
</template>

<script setup>
import { InputMask, Message } from 'primevue';

const model = defineModel();

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    required: true
  },
  inputClass: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'text'
  },
  formContext: {
    type: Object,
    default: null
  },
  mask: {
    type: String,
    default: null
  }
});

</script>

<style>
/* Estilos para IluriaInputMask seguir os temas */
.p-inputmask {
  background-color: var(--iluria-color-input-bg) !important;
  color: var(--iluria-color-input-text) !important;
  border-color: var(--iluria-color-input-border) !important;
}

.p-inputmask:focus {
  background-color: var(--iluria-color-input-bg) !important;
  color: var(--iluria-color-input-text) !important;
  border-color: var(--iluria-color-input-border-focus) !important;
}

.p-inputmask::placeholder {
  color: var(--iluria-color-input-placeholder) !important;
}

.p-inputmask.input-error {
  background-color: var(--iluria-color-input-bg) !important;
  border-color: var(--iluria-color-error) !important;
}

/* ===== PREVINE MUDANÇAS DE COR NO AUTOFILL ===== */

/* Força o InputMask a manter a cor do tema mesmo com autofill */
.p-inputmask:-webkit-autofill,
.p-inputmask:-webkit-autofill:focus,
.p-inputmask:-webkit-autofill:hover {
  -webkit-box-shadow: 0 0 0 1000px var(--iluria-color-input-bg) inset !important;
  -webkit-text-fill-color: var(--iluria-color-input-text) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Para outros navegadores */
.p-inputmask:autofill {
  background-color: var(--iluria-color-input-bg) !important;
  color: var(--iluria-color-input-text) !important;
}

/* Força o container a manter a cor do tema no autofill */
input.p-inputmask:-webkit-autofill {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
  -webkit-box-shadow: 0 0 0 1000px var(--iluria-color-input-bg) inset !important;
}

/* Garante que o PrimeVue InputMask mantenha as cores do tema */
.p-component.p-inputmask {
  background-color: var(--iluria-color-input-bg) !important;
  color: var(--iluria-color-input-text) !important;
}

.p-component.p-inputmask:focus {
  background-color: var(--iluria-color-input-bg) !important;
  color: var(--iluria-color-input-text) !important;
}
</style>
