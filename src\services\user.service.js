import axios from 'axios';
import { API_URLS } from '../config/api.config';

class UserService {
    #endpoint = `${API_URLS.SETTINGS_BASE_URL}/api/user`;

    /**
     * Obtém os dados do perfil do usuário logado
     */
    async getUserProfile() {
        try {
            // Buscar preferências do usuário
            const preferencesResponse = await axios.get(`${this.#endpoint}/preferences`);
            const preferences = preferencesResponse.data;

            // Retornar dados combinados (preferências + dados padrão do perfil)
            return {
                firstName: 'João', // TODO: Implementar endpoint de perfil completo
                lastName: 'Silva',  // TODO: Implementar endpoint de perfil completo
                profilePictureUrl: null, // TODO: Implementar endpoint de perfil completo
                language: preferences.language || 'pt-br',
                timezone: preferences.timezone || 'America/Sao_Paulo'
            };
        } catch (error) {
            console.error('GetUserProfile error:', error);

            // Fallback temporário - usar dados do localStorage ou padrão
            const fallbackData = this.#getFallbackProfileData();
            return fallbackData;
        }
    }

    /**
     * Atualiza os dados do perfil do usuário logado
     */
    async updateUserProfile(profileData) {
        try {
            const response = await axios.put(`${this.#endpoint}/profile`, profileData);
            return response.data;
        } catch (error) {
            console.error('UpdateUserProfile error:', error);

            // Fallback temporário - salvar no localStorage
            this.#saveFallbackProfileData(profileData);
            return profileData;
        }
    }

    /**
     * Atualiza a foto do perfil do usuário
     */
    async updateProfilePicture(formData) {
        try {
            const response = await axios.post(
                `${this.#endpoint}/profile/picture`,
                formData,
                {
                    headers: {
                        'Content-Type': 'multipart/form-data',
                    },
                }
            );
            return response.data;
        } catch (error) {
            console.error('UpdateProfilePicture error:', error);
            throw error;
        }
    }

    /**
     * Remove a foto do perfil do usuário
     */
    async removeProfilePicture() {
        try {
            const response = await axios.delete(`${this.#endpoint}/profile/picture`);
            return response.data;
        } catch (error) {
            console.error('RemoveProfilePicture error:', error);
            throw error;
        }
    }

    /**
     * Obtém a URL da foto do perfil do usuário
     */
    async getProfilePictureUrl() {
        try {
            const response = await axios.get(`${this.#endpoint}/profile/picture`);
            return response.data;
        } catch (error) {
            console.error('GetProfilePictureUrl error:', error);
            return null; // Sem foto de perfil
        }
    }

    /**
     * Obtém as preferências do usuário (idioma, timezone)
     */
    async getUserPreferences() {
        try {
            const response = await axios.get(`${this.#endpoint}/preferences`);
            return response.data;
        } catch (error) {
            console.error('GetUserPreferences error:', error);

            // Fallback temporário - usar dados do localStorage ou padrão
            const fallbackPreferences = this.#getFallbackPreferences();
            return fallbackPreferences;
        }
    }

    /**
     * Atualiza as preferências do usuário (idioma, timezone)
     */
    async updateUserPreferences(preferences) {
        try {
            const response = await axios.put(`${this.#endpoint}/preferences`, preferences);

            // Salvar também no localStorage como fallback
            this.#saveFallbackPreferences(preferences);

            return response.data;
        } catch (error) {
            console.error('UpdateUserPreferences error:', error);

            // Salvar no localStorage como fallback
            this.#saveFallbackPreferences(preferences);

            // Simular sucesso para não quebrar a UX
            return { success: true, message: 'Preferências salvas localmente' };
        }
    }

    /**
     * Métodos de fallback para desenvolvimento/teste
     */
    #getFallbackProfileData() {
        const saved = localStorage.getItem('user_profile_fallback');
        if (saved) {
            const data = JSON.parse(saved);

            // Incluir preferências salvas
            const preferences = this.#getFallbackPreferences();
            return { ...data, ...preferences };
        }

        // Dados padrão
        return {
            firstName: 'João',
            lastName: 'Silva',
            profilePictureUrl: null,
            language: 'pt-br',
            timezone: 'America/Sao_Paulo'
        };
    }

    #saveFallbackProfileData(profileData) {
        localStorage.setItem('user_profile_fallback', JSON.stringify(profileData));
    }

    #getFallbackPreferences() {
        const saved = localStorage.getItem('user_preferences_fallback');
        if (saved) {
            return JSON.parse(saved);
        }

        return {
            language: 'pt-br',
            timezone: 'America/Sao_Paulo'
        };
    }

    #saveFallbackPreferences(preferences) {
        localStorage.setItem('user_preferences_fallback', JSON.stringify(preferences));
    }
}

export default new UserService();
