import { z } from 'zod';
import { useI18n } from 'vue-i18n';
/**
* ValidationService provides reusable validation functions for form fields.
* Each function returns a Zod schema with appropriate validation rules.
*
* @module ValidationService
*/
/**
* Creates a validator for required text fields with maximum length validation
*
* @param {string} fieldName - The name of the field for error messages
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.maxLengthMessage - Custom max length message
* @param {number} options.maxLength - Maximum allowed length (default: 255)
* @returns {z.ZodType} - Zod schema for text validation
*/
export function requiredText(fieldName, options = {}) {
  const { t } = useI18n();
  const {
    requiredMessage = t('validation.fieldRequired', { field: fieldName }),
    maxLengthMessage = t('validation.maxLength', { field: fieldName, length: options.maxLength || 255 }),
    maxLength = 255
  } = options;
  return z.string()
    .nullable()
    .transform(val => val === null || val === '' ? '' : val)
    .refine(val => val !== '', { message: requiredMessage })
    .refine(val => val.length <= maxLength, { message: maxLengthMessage });
}
/**
* Creates a validator for optional text fields with maximum length validation
*
* @param {string} fieldName - The name of the field for error messages
* @param {object} options - Configuration options
* @param {string} options.maxLengthMessage - Custom max length message
* @param {number} options.maxLength - Maximum allowed length (default: 255)
* @returns {z.ZodType} - Zod schema for optional text validation
*/
export function optionalText(fieldName, options = {}) {
  const { t } = useI18n();
  const {
    maxLengthMessage = t('validation.maxLength', { field: fieldName, length: options.maxLength || 255 }),
    maxLength = 255
  } = options;
  return z.string()
    .nullable()
    .transform(val => val === null || val === '' ? undefined : val)
    .optional()
    .refine(val => val === undefined || val.length <= maxLength, { message: maxLengthMessage });
}
/**
* Creates a validator for text fields that are conditionally required based on a condition
*
* @param {string} fieldName - The name of the field for error messages
* @param {boolean} condition - Condition that determines if the field is required
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.maxLengthMessage - Custom max length message
* @param {number} options.maxLength - Maximum allowed length (default: 255)
* @returns {z.ZodType} - Zod schema for conditional text validation
*/
export function conditionalText(fieldName, condition, options = {}) {
  return condition
    ? requiredText(fieldName, options)
    : optionalText(fieldName, options);
}
/**
* Creates a validator for optional numeric fields with minimum value validation
*
* @param {string} fieldName - The name of the field for error messages
* @param {object} options - Configuration options
* @param {string} options.invalidMessage - Custom invalid number message
* @param {string} options.minValueMessage - Custom minimum value message
* @param {number} options.minValue - Minimum allowed value (default: 0)
* @returns {z.ZodType} - Zod schema for optional number validation
*/
export function optionalNonNegativeNumber(fieldName, options = {}) {
  const { t } = useI18n();
  const {
    invalidMessage = t('validation.invalidNumber', { field: fieldName }),
    minValueMessage = options.minValue > 0
      ? t('validation.greaterThan', { field: fieldName, value: options.minValue })
      : t('validation.nonNegative', { field: fieldName }),
    minValue = 0
  } = options;
  return z.union([z.string().nullable(), z.number()])
    .transform(val => {
      if (val === null || val === '') return undefined;
      const parsed = parseFloat(val);
      if (isNaN(parsed)) throw new Error(invalidMessage);
      return parsed;
    })
    .refine(val => val === undefined || val >= minValue, { message: minValueMessage });
}
/**
* Creates a validator for required numeric fields with minimum value validation
*
* @param {string} fieldName - The name of the field for error messages
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.invalidMessage - Custom invalid number message
* @param {string} options.minValueMessage - Custom minimum value message
* @param {number} options.minValue - Minimum allowed value (default: 0)
* @returns {z.ZodType} - Zod schema for required number validation
*/
export function requiredNonNegativeNumber(fieldName, options = {}) {
  const { t } = useI18n();
  const {
    requiredMessage = t('validation.fieldRequired', { field: fieldName }),
    invalidMessage = t('validation.invalidNumber', { field: fieldName }),
    minValueMessage = options.minValue > 0
      ? t('validation.greaterThan', { field: fieldName, value: options.minValue })
      : t('validation.nonNegative', { field: fieldName }),
    minValue = 0
  } = options;
  return z.union([
    z.string().nullable(),
    z.number()
  ])
    .transform(val => {
      // Se for número, retorna diretamente
      if (typeof val === 'number') {
        if (val === 0 || val) return val;
        return '';
      }
      // Se for string ou null, faz o tratamento normal
      return val === null || val === '' ? '' : val;
    })
    .refine(val => val !== '', { message: requiredMessage })
    .transform(val => {
      if (val === '') throw new Error(requiredMessage);
      const parsed = parseFloat(val);
      if (isNaN(parsed)) throw new Error(invalidMessage);
      return parsed;
    })
    .refine(val => val >= minValue, { message: minValueMessage });
}
/**
* Creates a validator for numeric fields that are conditionally required based on a condition
*
* @param {string} fieldName - The name of the field for error messages
* @param {boolean} condition - Condition that determines if the field is required
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.invalidMessage - Custom invalid number message
* @param {string} options.minValueMessage - Custom minimum value message
* @param {number} options.minValue - Minimum allowed value (default: 0)
* @returns {z.ZodType} - Zod schema for conditional number validation
*/
export function conditionalNonNegativeNumber(fieldName, condition, options = {}) {
  return condition
    ? requiredNonNegativeNumber(fieldName, options)
    : optionalNonNegativeNumber(fieldName, options);
}
/**
* Creates a validator for email fields
*
* @param {string} fieldName - The name of the field for error messages
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.invalidMessage - Custom invalid email message
* @param {boolean} options.required - Whether the email is required (default: true)
* @returns {z.ZodType} - Zod schema for email validation
*/
export function emailValidator(fieldName, options = {}) {
  const { t } = useI18n();
  const {
    requiredMessage = t('validation.fieldRequired', { field: fieldName }),
    invalidMessage = t('validation.invalidEmail', { field: fieldName }),
    required = true
  } = options;
  if (required) {
    return z.string()
      .min(1, { message: requiredMessage })
      .email({ message: invalidMessage });
  } else {
    return z.union([
      z.string().length(0),
      z.string().email({ message: invalidMessage })
    ]).optional().nullable();
  }
}
/**
* Creates a validator for email fields that are conditionally required based on a condition
*
* @param {string} fieldName - The name of the field for error messages
* @param {boolean} condition - Condition that determines if the field is required
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.invalidMessage - Custom invalid email message
* @returns {z.ZodType} - Zod schema for conditional email validation
*/
export function conditionalEmail(fieldName, condition, options = {}) {
  const newOptions = { ...options, required: condition };
  return emailValidator(fieldName, newOptions);
}
/**
* Creates a validator for URL fields
*
* @param {string} fieldName - The name of the field for error messages
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.invalidMessage - Custom invalid URL message
* @param {boolean} options.required - Whether the URL is required (default: false)
* @returns {z.ZodType} - Zod schema for URL validation
*/
export function urlValidator(fieldName, options = {}) {
  const { t } = useI18n();
  const {
    requiredMessage = t('validation.fieldRequired', { field: fieldName }),
    invalidMessage = t('validation.invalidUrl', { field: fieldName }),
    required = false
  } = options;
  if (required) {
    return z.string()
      .min(1, { message: requiredMessage })
      .url({ message: invalidMessage });
  } else {
    return z.union([
      z.string().length(0),
      z.string().url({ message: invalidMessage })
    ]).optional().nullable();
  }
}
/**
* Creates a validator for URL fields that are conditionally required based on a condition
*
* @param {string} fieldName - The name of the field for error messages
* @param {boolean} condition - Condition that determines if the field is required
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Custom required field message
* @param {string} options.invalidMessage - Custom invalid URL message
* @returns {z.ZodType} - Zod schema for conditional URL validation
*/
export function conditionalUrl(fieldName, condition, options = {}) {
  const newOptions = { ...options, required: condition };
  return urlValidator(fieldName, newOptions);
}

/**
* Creates a validator for required non-negative numbers without using i18n internally
* This version accepts pre-translated messages to avoid i18n hooks being called in computed properties
*
* @param {string} fieldName - The name of the field for error messages  
* @param {object} options - Configuration options
* @param {string} options.requiredMessage - Pre-translated required field message
* @param {string} options.invalidMessage - Pre-translated invalid number message
* @param {string} options.minValueMessage - Pre-translated minimum value message
* @param {number} options.minValue - Minimum allowed value (default: 0)
* @returns {z.ZodType} - Zod schema for required number validation
*/
export function requiredNonNegativeNumberWithMessages(fieldName, options = {}) {
  const {
    requiredMessage = `${fieldName} é obrigatório`,
    invalidMessage = `${fieldName} deve ser um número válido`,
    minValueMessage = `${fieldName} deve ser um valor não negativo`,
    minValue = 0
  } = options;
  
  return z.union([
    z.string().nullable(),
    z.number()
  ])
    .transform(val => {
      // Se for número, retorna diretamente
      if (typeof val === 'number') {
        if (val === 0 || val) return val;
        return '';
      }
      // Se for string ou null, faz o tratamento normal
      return val === null || val === '' ? '' : val;
    })
    .refine(val => val !== '', { message: requiredMessage })
    .refine(val => {
      if (val === '') return true; // Já foi validado como obrigatório acima
      const numValue = Number(val);
      return !isNaN(numValue);
    }, { message: invalidMessage })
    .refine(val => {
      if (val === '') return true; // Já foi validado como obrigatório acima
      const numValue = Number(val);
      return numValue >= minValue;
    }, { message: minValueMessage })
    .transform(val => {
      if (val === '') return '';
      return Number(val);
    });
}