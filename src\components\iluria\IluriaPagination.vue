<template>
  <div class="flex justify-center items-center mt-4 gap-2" v-if="totalPages > 1">
    <!-- Previous button -->
    <IluriaButton 
      color="outline"
      @click="$emit('goToPage', currentPage - 1)"
      :aria-label="t('pagination.previous')"
      :disabled="currentPage === 0"
    >
      &laquo;
    </IluriaButton>
    
    <!-- Page buttons for small number of pages -->
    <template v-if="totalPages <= 7">
      <IluriaButton 
        v-for="page in totalPages" 
        :key="page" 
        :color="currentPage === page - 1 ? 'primary' : 'outline'"
        @click="$emit('goToPage', page - 1)"
      >
        {{ page }}
      </IluriaButton>
    </template>
    
    <!-- Page buttons for large number of pages -->
    <template v-else>
      <!-- First page -->
      <IluriaButton 
        :color="currentPage === 0 ? 'primary' : 'outline'"
        @click="$emit('goToPage', 0)"
      >
        1
      </IluriaButton>
      <!-- Middle section with input -->
      <div class="flex items-center gap-1">
        <span class="text-gray-500">...</span>
        <IluriaInputText
          type="number" 
          v-model.number="goToPageInput" 
          min="3" 
          :max="totalPages - 2"
          class="w-12 px-1"
          @keyup.enter="handleGoToPage"
        />
        <span class="text-gray-500">...</span>
      </div>
      
      <!-- Show dots if current page is far from start -->
      <span v-if="currentPage > 3" class="px-2">...</span>
      
      <!-- Pages around current page -->
      <template v-for="page in visiblePages" :key="page">
        <IluriaButton 
          :color="currentPage === page - 1 ? 'primary' : 'outline'"
          @click="$emit('goToPage', page - 1)"
        >
          {{ page }}
        </IluriaButton>
      </template>
      
      <!-- Show dots if current page is far from end -->
      <span v-if="currentPage < totalPages - 4" class="px-2">...</span>
      
      <!-- Last page -->
      <IluriaButton 
        v-if="totalPages > 1"
        :color="currentPage === totalPages - 1 ? 'primary' : 'outline'"
        @click="$emit('goToPage', totalPages - 1)"
      >
        {{ totalPages }}
      </IluriaButton>
    </template>
    
    <!-- Next button -->
    <IluriaButton 
      color="outline"
      @click="$emit('goToPage', currentPage + 1)"
      :aria-label="t('pagination.next')"
      :disabled="currentPage === totalPages - 1"
    >
      &raquo;
    </IluriaButton>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

const props = defineProps({
  currentPage: {
    type: Number,
    required: true
  },
  totalPages: {
    type: Number,
    required: true
  }
});

const emit = defineEmits(['goToPage']);

// Input for jumping to a specific page
const goToPageInput = ref('');

// Handle page jump
const handleGoToPage = () => {
  const pageNumber = parseInt(goToPageInput.value);
  if (pageNumber >= 1 && pageNumber <= props.totalPages) {
    emit('goToPage', pageNumber - 1); // Convert to 0-based
    goToPageInput.value = '';
  }
};

// Calculate visible pages around current page
const visiblePages = computed(() => {
  const current = props.currentPage + 1; // Convert to 1-based
  const total = props.totalPages;
  const pages = [];
  
  // Show pages around current page
  const start = Math.max(2, current - 1);
  const end = Math.min(total - 1, current + 1);
  
  for (let i = start; i <= end; i++) {
    if (i > 1 && i < total) {
      pages.push(i);
    }
  }
  
  return pages;
});
</script>
