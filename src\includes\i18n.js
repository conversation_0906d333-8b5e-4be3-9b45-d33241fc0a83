import { createI18n } from "vue-i18n";
import { en, pt } from '@/locales'

// Get user preferred language from localStorage or browser
const getUserLocale = () => {
    // First, try to get from user preferences (saved in localStorage)
    const userPreferences = localStorage.getItem('user_preferences_fallback');
    if (userPreferences) {
        try {
            const preferences = JSON.parse(userPreferences);
            if (preferences.language) {
                // Convert pt-br to pt for i18n
                return preferences.language === 'pt-br' ? 'pt' : preferences.language;
            }
        } catch (error) {
            console.warn('Error parsing user preferences:', error);
        }
    }

    // Fallback to browser language
    const navigatorLocale = navigator.languages !== undefined
        ? navigator.languages[0]
        : navigator.language;

    if (!navigatorLocale) {
        return 'pt';
    }

    // Transform locale to match our supported locales
    const locale = navigatorLocale.trim().split(/-|_/)[0];
    return ['en', 'pt'].includes(locale) ? locale : 'pt';
};

// Create i18n instance
export default createI18n({
    legacy: false,
    locale: getUserLocale(),
    fallbackLocale: "pt",
    messages: {
        en: en,
        pt: pt
    }
});