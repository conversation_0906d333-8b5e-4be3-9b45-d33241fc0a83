<template>
  <!-- Este é um script que será injetado no HTML standalone -->
</template>

<script>
/**
 * Script de injeção para componentes de localização
 * Injeta funcionalidades necessárias para mapas funcionarem independentemente
 */

// Função principal de injeção
export function injectLocationScript(doc, authToken = '') {


  // Verifica se o script já foi injetado
  if (doc.querySelector('#location-component-standalone-script')) {

    return
  }
  
  // Verificar se os componentes de localização já têm scripts embebidos
  const locationComponents = doc.querySelectorAll('[data-component="location"]')
  let hasEmbeddedScripts = false
  
  locationComponents.forEach(component => {
    if (component.querySelector('script')) {
      hasEmbeddedScripts = true

    }
  })
  
  if (hasEmbeddedScripts) {

    return
  }

  try {
    // Criar script diretamente sem usar getAutoContainedScript
    const scriptElement = doc.createElement('script')
    scriptElement.id = 'location-component-standalone-script'
    scriptElement.type = 'text/javascript'
    
    // Adicionar CSS apenas uma vez
    if (!doc.querySelector('#location-component-styles')) {
      const cssContent = [
        '.iluria-location-component { font-family: -apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif; }',
        '.location-container { max-width: 1200px; margin: 0 auto; }',
        '.location-header { text-align: center; margin-bottom: 3rem; }',
        '.location-title { font-size: 2.5rem; font-weight: 700; color: #1a1a1a; margin: 0 0 1rem 0; }',
        '.location-subtitle { font-size: 1.1rem; color: #666; max-width: 600px; margin: 0 auto; line-height: 1.6; }',
        '.locations-grid.layout-grid { display: grid; gap: 3rem; grid-template-columns: 1fr; }',
        '.layout-grid .location-item { display: grid; grid-template-columns: 1fr 1fr; gap: 2rem; align-items: center; background: white; border-radius: 12px; overflow: hidden; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1); }',
        '.location-image { position: relative; overflow: hidden; }',
        '.location-photo { width: 100%; height: 300px; object-fit: cover; transition: transform 0.3s ease; }',
        '.location-photo:hover { transform: scale(1.05); }',
        '.location-content { padding: 2rem; }',
        '.location-name { font-size: 1.8rem; font-weight: 600; color: #1a1a1a; margin: 0 0 1rem 0; }',
        '.location-description { font-size: 1rem; color: #666; margin: 0 0 2rem 0; line-height: 1.6; }',
        '.location-details { display: flex; flex-direction: column; gap: 1.5rem; }',
        '.detail-item { display: flex; flex-direction: column; gap: 0.5rem; }',
        '.detail-item strong { font-weight: 600; color: #1a1a1a; font-size: 0.95rem; }',
        '.detail-item p { margin: 0; color: #666; line-height: 1.5; }'
      ].join(' ')
      
      const styleElement = doc.createElement('style')
      styleElement.id = 'location-component-styles'
      styleElement.textContent = cssContent
      doc.head.appendChild(styleElement)
    }
    
    // Script principal simplificado
    const scriptContent = `
      (function() {
        "use strict";

        
        if (window._locationComponentActive) {

          return;
        }
        window._locationComponentActive = true;
        
        function initLocationComponents() {
          try {
            const locationElements = document.querySelectorAll('[data-component="location"]');

            
            locationElements.forEach(function(element) {
              const locationsData = element.getAttribute('data-locations');
              if (!locationsData) return;
              
              try {
                const locations = JSON.parse(locationsData);
                locations.forEach(function(location) {
                  if (location.hasMap) {
                    const mapElement = element.querySelector('[data-location-id="' + location.id + '"] .location-map');
                    if (mapElement && !mapElement.innerHTML.trim()) {

                      renderLocationMap(mapElement, location);
                    }
                  }
                });
              } catch (error) {

              }
            });
          } catch (error) {

          }
        }
        
                 function renderLocationMap(mapElement, location) {
           // Tentar carregar Google Maps primeiro
           if (typeof google !== 'undefined' && google.maps) {
             renderGoogleMap(mapElement, location);
           } else {
             // Tentar carregar a API do Google Maps
             loadGoogleMapsAPI().then(function() {
               renderGoogleMap(mapElement, location);
             }).catch(function() {

               renderOpenStreetMap(mapElement, location);
             });
           }
         }
         
         function loadGoogleMapsAPI() {
           return new Promise(function(resolve, reject) {
             if (typeof google !== 'undefined' && google.maps) {
               resolve();
               return;
             }
             
             // Verificar se já existe um script carregando
             if (window._loadingGoogleMaps) {
               window._loadingGoogleMaps.then(resolve).catch(reject);
               return;
             }
             
             window._loadingGoogleMaps = new Promise(function(resolveLoader, rejectLoader) {
               const script = document.createElement('script');
               script.src = 'https://maps.googleapis.com/maps/api/js?key=AIzaSyCDhfeGIVpj_9bil4Hdc9NHFT7QuKtDnP0&libraries=geometry,places';
               script.async = true;
               script.defer = true;
               
               script.onload = function() {

                 resolveLoader();
                 resolve();
               };
               
               script.onerror = function() {

                 rejectLoader();
                 reject();
               };
               
               document.head.appendChild(script);
               
               // Timeout de 10 segundos
               setTimeout(function() {
                 if (typeof google === 'undefined') {
                   rejectLoader();
                   reject();
                 }
               }, 10000);
             });
           });
         }
         
         function renderGoogleMap(mapElement, location) {
           try {
             
             const mapHeight = window.innerWidth <= 768 ? 280 : 350;
             mapElement.style.height = mapHeight + 'px';
             mapElement.style.width = '100%';
             mapElement.style.borderRadius = '8px';
             mapElement.style.overflow = 'hidden';
             
                           // Geocoding para obter coordenadas
              const geocoder = new google.maps.Geocoder();
              geocoder.geocode({ address: location.address }, function(results, status) {
                if (status === 'OK' && results[0]) {
                  const position = results[0].geometry.location;
                  
                  const map = new google.maps.Map(mapElement, {
                    zoom: 16,
                    center: position,
                    mapTypeControl: false,
                    streetViewControl: false,
                    fullscreenControl: true,
                    zoomControl: true,
                    styles: [
                      {
                        featureType: 'all',
                        elementType: 'geometry.fill',
                        stylers: [{ saturation: -40 }, { lightness: 40 }]
                      }
                    ]
                  });
                  
                  const marker = new google.maps.Marker({
                    position: position,
                    map: map,
                    title: location.name,
                    animation: google.maps.Animation.DROP
                  });
                  
                  const infoWindow = new google.maps.InfoWindow({
                    content: '<div style="padding: 12px; max-width: 250px; font-family: -apple-system, BlinkMacSystemFont, \\"Segoe UI\\", Roboto, sans-serif;"><h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">' + location.name + '</h3><p style="margin: 0; font-size: 14px; color: #666; line-height: 1.4;">' + location.address + '</p></div>'
                  });
                  
                  marker.addListener('click', function() {
                    infoWindow.open(map, marker);
                  });
                  
                  // Abrir info window automaticamente
                  setTimeout(function() {
                    infoWindow.open(map, marker);
                  }, 1000);
                  

                } else {

                  renderOpenStreetMap(mapElement, location);
                }
              });
                       } catch (error) {

              renderOpenStreetMap(mapElement, location);
            }
          }
          
          function renderOpenStreetMap(mapElement, location) {
            try {
              
              // Carregar Leaflet se não estiver disponível
              if (typeof L === 'undefined') {
                loadLeaflet().then(function() {
                  createOpenStreetMap(mapElement, location);
                }).catch(function() {
                  renderFallbackMap(mapElement, location);
                });
              } else {
                createOpenStreetMap(mapElement, location);
              }
            } catch (error) {

              renderFallbackMap(mapElement, location);
            }
          }
          
          function loadLeaflet() {
            return new Promise(function(resolve, reject) {
              if (typeof L !== 'undefined') {
                resolve();
                return;
              }
              
              // Carregar CSS do Leaflet
              const link = document.createElement('link');
              link.rel = 'stylesheet';
              link.href = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.css';
              document.head.appendChild(link);
              
              // Carregar JS do Leaflet
              const script = document.createElement('script');
              script.src = 'https://unpkg.com/leaflet@1.9.4/dist/leaflet.js';
              script.onload = function() {

                resolve();
              };
              script.onerror = function() {

                reject();
              };
              document.head.appendChild(script);
            });
          }
          
          function createOpenStreetMap(mapElement, location) {
            try {
              const mapHeight = window.innerWidth <= 768 ? 280 : 350;
              mapElement.style.height = mapHeight + 'px';
              mapElement.style.width = '100%';
              mapElement.style.borderRadius = '8px';
              mapElement.style.overflow = 'hidden';
              mapElement.innerHTML = '';
              
              // Geocoding usando Nominatim
              const addressEncoded = encodeURIComponent(location.address);
              fetch('https://nominatim.openstreetmap.org/search?format=json&q=' + addressEncoded)
                .then(response => response.json())
                .then(data => {
                  if (data && data[0]) {
                    const lat = parseFloat(data[0].lat);
                    const lon = parseFloat(data[0].lon);
                    
                    const map = L.map(mapElement).setView([lat, lon], 16);
                    
                    L.tileLayer('https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png', {
                      attribution: '© OpenStreetMap contributors'
                    }).addTo(map);
                    
                    const marker = L.marker([lat, lon]).addTo(map);
                    
                    const popupContent = '<div style="font-family: -apple-system, BlinkMacSystemFont, \\"Segoe UI\\", Roboto, sans-serif;"><h3 style="margin: 0 0 8px 0; font-size: 16px; font-weight: 600;">' + location.name + '</h3><p style="margin: 0; font-size: 14px; color: #666; line-height: 1.4;">' + location.address + '</p></div>';
                    
                    marker.bindPopup(popupContent);
                    
                    // Abrir popup automaticamente
                    setTimeout(function() {
                      marker.openPopup();
                    }, 1000);
                    

                  } else {

                    renderFallbackMap(mapElement, location);
                  }
                })
                .catch(error => {

                  renderFallbackMap(mapElement, location);
                });
            } catch (error) {

              renderFallbackMap(mapElement, location);
            }
          }
         
         function renderFallbackMap(mapElement, location) {
           const addressEncoded = encodeURIComponent(location.address);
           const mapHeight = window.innerWidth <= 768 ? "280px" : "350px";
           
           mapElement.innerHTML = '<div style="width: 100%; height: ' + mapHeight + '; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); border-radius: 8px; display: flex; flex-direction: column; align-items: center; justify-content: center; text-align: center; color: white; font-family: -apple-system, BlinkMacSystemFont, \\"Segoe UI\\", Roboto, sans-serif; position: relative; overflow: hidden;"><div style="position: relative; z-index: 1;"><div style="margin-bottom: 1.5rem;"><svg width="60" height="60" viewBox="0 0 24 24" fill="currentColor"><path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/></svg></div><h3 style="margin: 0 0 0.75rem 0; font-size: 1.2rem; font-weight: 600;">📍 ' + location.name + '</h3><p style="margin: 0 0 1.5rem 0; font-size: 0.9rem; line-height: 1.4; max-width: 280px; opacity: 0.9;">' + location.address + '</p><div style="display: flex; gap: 0.75rem; justify-content: center; flex-wrap: wrap;"><a href="https://www.google.com/maps/search/?api=1&query=' + addressEncoded + '" target="_blank" style="background: rgba(255, 255, 255, 0.2); color: white; text-decoration: none; padding: 0.75rem 1.25rem; border-radius: 25px; font-weight: 600; font-size: 0.85rem; transition: all 0.3s ease; backdrop-filter: blur(10px); border: 1px solid rgba(255, 255, 255, 0.3); display: flex; align-items: center; gap: 0.5rem;">🗺️ Ver no Google Maps</a></div></div></div>';
         }
        
        // Inicialização
        if (document.readyState === 'complete' || document.readyState === 'interactive') {
          setTimeout(initLocationComponents, 500);
        }
        
        document.addEventListener('DOMContentLoaded', function() {
          setTimeout(initLocationComponents, 1000);
        });
        
        window.addEventListener('load', function() {
          setTimeout(initLocationComponents, 1500);
        });
        
      })();
    `;
    
    scriptElement.textContent = scriptContent
    doc.head.appendChild(scriptElement)


  } catch (error) {

    
    // Fallback: criar script básico de inicialização
    try {
      const fallbackScript = doc.createElement('script')
      fallbackScript.id = 'location-component-fallback-script'
      fallbackScript.textContent = `
        // Fallback para componentes de localização
        document.addEventListener('DOMContentLoaded', function() {

          const locationElements = document.querySelectorAll('[data-component="location"]')
          locationElements.forEach(function(element) {
            const maps = element.querySelectorAll('.location-map')
            maps.forEach(function(mapEl) {
              if (!mapEl.innerHTML.trim()) {
                mapEl.innerHTML = '<div style="height: 300px; background: #f8f9fa; border-radius: 8px; display: flex; align-items: center; justify-content: center; color: #6c757d;">🗺️ Mapa será carregado em breve...</div>'
              }
            })
          })
        })
      `
      doc.head.appendChild(fallbackScript)

    } catch (fallbackError) {

    }
  }
}

// Função de fallback para quando o script não consegue ser injetado
export function createLocationFallback(doc) {

  
  // Adiciona CSS básico para componentes de localização
  const style = doc.createElement('style')
  style.textContent = `
    .location-map {
      min-height: 300px;
      background: #f8f9fa;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin: 1.5rem 0;
    }
    
    .map-placeholder {
      text-align: center;
      color: #6c757d;
      padding: 2rem;
    }
  `
  
  doc.head.appendChild(style)

}

// Exporta função principal para o sistema de injeção
export default {
  inject: injectLocationScript,
  fallback: createLocationFallback
}
</script> 
