@import url('https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap');
@import "tailwindcss";

#app {
  margin: 0;
  padding: 0;
}

:root {
  /* Fonte principal - controlada pelo sistema de fontes */
  --iluria-font-family: "Poppins", sans-serif;
  /* Cores originais mantidas para compatibilidade */

  

  font-family: Inter, sans-serif;
  font-feature-settings: "liga" 1, "calt" 1, "tnum" 1, "cv01" 1, "cv02" 1, "zero" 1, "cv11";
}

@supports (font-variation-settings: normal) {
  :root {
    font-family: Poppins, InterVariable, sans-serif;
  }
}

body {
  background-color: var(--iluria-color-body-bg);
  color: var(--iluria-color-body-fg);
  font-family: var(--iluria-font-family);
  min-height: 100vh;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              font-family 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Aplicar fonte em todos os elementos principais */
*, *::before, *::after {
  font-family: inherit;
}

/* Transições suaves para elementos que usam CSS variables */
input, textarea, select, button, .input-themed, .navbar-button, .view-container {
  font-family: var(--iluria-font-family) !important;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1), 
              border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              color 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              box-shadow 0.3s cubic-bezier(0.4, 0, 0.2, 1),
              font-family 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
}

/* Force refresh das CSS variables em todos os inputs */
input, 
.p-inputtext, 
.p-inputnumber-input, 
.p-inputmask,
[class*="input"] {
  border-color: var(--iluria-color-input-border) !important;
  color: var(--iluria-color-input-text) !important;
}

input:focus,
.p-inputtext:focus,
.p-inputnumber-input:focus,
.p-inputmask:focus,
[class*="input"]:focus {
  border-color: var(--iluria-color-input-border-focus) !important;
  border-width: 1px !important;
  border-style: solid !important;
}

/* Classes de tema para diferentes temas */
.theme-light {
  /* Tema claro - já definido no :root */
}

.theme-dark {
  /* Force aplicação para tema escuro */
}

.theme-dark input,
.theme-dark .p-inputtext,
.theme-dark .p-inputnumber-input,
.theme-dark .p-inputmask,
.theme-dark textarea,
.theme-dark select {
  background-color: #222222 !important;
  border-color: #333333 !important;
  color: #ffffff !important;
}

.theme-dark input:focus,
.theme-dark .p-inputtext:focus,
.theme-dark .p-inputnumber-input:focus,
.theme-dark .p-inputmask:focus,
.theme-dark textarea:focus,
.theme-dark select:focus {
  border-color: #ffffff !important;
  border-width: 1px !important;
  border-style: solid !important;
  background-color: #222222 !important;
  box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1) !important;
}

.theme-dark input::placeholder,
.theme-dark .p-inputtext::placeholder,
.theme-dark textarea::placeholder {
  color: #9ca3af !important;
}

.theme-blue {
  /* Variações específicas para tema azul podem ser definidas aqui se necessário */
}

.theme-green {
  /* Variações específicas para tema verde podem ser definidas aqui se necessário */
}

.theme-purple {
  /* Variações específicas para tema roxo podem ser definidas aqui se necessário */
}

form label {
  @apply cursor-pointer;
  @apply block text-sm font-normal;
}

@layer base {
  input[type='number']::-webkit-outer-spin-button,
  input[type='number']::-webkit-inner-spin-button,
  input[type='number'] {
    -webkit-appearance: none;
    margin: 0;
    -moz-appearance: textfield !important;
  }
}

/* Estilos para o InputNumber do PrimeVue */
.p-inputnumber {
  width: 100%;
  border-radius: 0.5rem !important;
}

.p-inputnumber-input {
  background-color: var(--iluria-color-7) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  outline: transparent !important;
  font-size: 0.875rem !important;
  line-height: 1.5rem !important;
  color: #111827 !important;
}

.p-inputnumber-input:focus {
  outline: 2px solid var(--iluria-color-4) !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}

.p-inputnumber-input.p-invalid {
  background-color: #fee2e2 !important;
  outline: 2px solid #fca5a5 !important;
}

.p-inputnumber-input::placeholder {
  color: #9ca3af !important;
}

.p-inputnumber-button {
  background-color: transparent !important;
  border: none !important;
  color: #6b7280 !important;
}

.p-inputnumber-button:hover {
  background-color: #f3f4f6 !important;
  color: #111827 !important;
}

.p-inputnumber-button:focus {
  box-shadow: none !important;
}

.p-inputnumber:has(.p-inputnumber-input:focus) {
  border-radius: 0.5rem !important;
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
}

.p-inputnumber:has(.p-inputnumber-input:focus) .p-inputnumber-input:focus {
  outline: none !important;
  box-shadow: none !important;
}

.p-inputnumber:has(.p-inputnumber-input:focus) .p-inputnumber-input,
.p-inputnumber:has(.p-inputnumber-input:focus) .p-inputgroup-addon {
  border-color: var(--iluria-color-input-border-focus) !important;
}

@supports not selector(:has(*)) {
  .p-inputnumber:focus-within {
    border-radius: 0.5rem !important;
    box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
  }
  
  .p-inputnumber:focus-within .p-inputnumber-input:focus {
    outline: none !important;
    box-shadow: none !important;
  }
  
  .p-inputnumber:focus-within .p-inputnumber-input,
  .p-inputnumber:focus-within .p-inputgroup-addon {
    border-color: var(--iluria-color-input-border-focus) !important;
  }
}

.p-inputnumber .p-inputgroup-addon,
.p-inputnumber .p-inputgroup-addon::before,
.p-inputnumber .p-inputgroup-addon::after {
  border-left: 0 !important;
  border-left-width: 0 !important;
  border-left-style: none !important;
  border-left-color: transparent !important;
}

.p-inputnumber .p-inputgroup-addon:not(:first-child),
.p-inputnumber .p-inputgroup-addon:last-child,
.p-inputnumber div[class*="suffix"],
.p-inputnumber div[class*="addon"] {
  border-left: 0 !important;
  border-left-width: 0 !important;
  border-left-style: none !important;
  border-left-color: transparent !important;
}

.p-inputnumber .p-inputgroup-addon::before,
.p-inputnumber .p-inputgroup-addon::after {
  display: none !important;
  content: none !important;
  border: none !important;
}

.p-inputnumber .p-inputgroup-addon:last-child {
  margin-left: -1px !important;
  padding-left: 0.75rem !important;
}

input[type="checkbox"] {
  @apply col-start-1 row-start-1 appearance-none rounded-sm border border-gray-300 bg-white checked:border-[#86B84B] checked:bg-[#86B84B] indeterminate:border-[#86B84B] indeterminate:bg-[#86B84B] focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-[#86B84B] disabled:border-gray-300 disabled:bg-gray-100 disabled:checked:bg-gray-100 forced-colors:appearance-auto;
}

.iluria-confirm-button {
  @apply rounded-xl px-3 py-2;
  @apply text-sm font-semibold;
  @apply text-[var(--iluria-color-button-confirm-fg)] bg-[var(--iluria-color-button-confirm-bg)];
  @apply hover:bg-[var(--iluria-color-button-confirm-bg-hover)] focus-visible:outline-[var(--iluria-color-button-confirm)];
  @apply active:scale-90 transition;
  @apply focus-visible:outline-2 focus-visible:outline-offset-2;
  @apply cursor-pointer shadow-xs;
}

/* Estilos para o DatePicker do PrimeVue */
.p-datepicker {
  width: 100%;
  border-radius: 0.5rem !important;
}

.p-datepicker .p-inputtext {
  background-color: var(--iluria-color-7) !important;
  border: none !important;
  border-radius: 0.5rem !important;
  padding: 0.5rem 0.75rem !important;
  width: 100% !important;
  outline: transparent !important;
  font-size: 0.875rem !important;
  line-height: 1.5rem !important;
  color: #111827 !important;
}

.p-datepicker .p-inputtext:focus {
  outline: 2px solid var(--iluria-color-4) !important;
  outline-offset: 0 !important;
  box-shadow: none !important;
}

.p-datepicker .p-inputtext.p-invalid {
  background-color: #fee2e2 !important;
  outline: 2px solid #fca5a5 !important;
}

.p-datepicker .p-inputtext::placeholder {
  color: #9ca3af !important;
}

/* Estilo do botão do calendário */
.p-datepicker-trigger {
  background: transparent !important;
  border: none !important;
  color: #6b7280 !important;
  padding: 0 !important;
  margin: 0 !important;
  position: absolute !important;
  right: 0.75rem !important;
  top: 50% !important;
  transform: translateY(-50%) !important;
  width: 2rem !important;
  height: 2rem !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  z-index: 1 !important;
}

.p-datepicker-trigger:hover {
  background-color: transparent !important;
  color: var(--iluria-color-4) !important;
}

.p-datepicker-trigger:focus {
  box-shadow: none !important;
  outline: none !important;
}

.p-datepicker-trigger .p-button-icon {
  font-size: 1rem !important;
}

/* Ajuste do container para posicionamento do ícone */
.p-calendar {
  position: relative !important;
  width: 100% !important;
}

.p-calendar .p-inputtext {
  padding-right: 2.5rem !important;
}

/* Estilo do painel do calendário */
.p-datepicker-panel {
  border-radius: 0.5rem !important;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06) !important;
  background-color: white !important;
  border: none !important;
  padding: 0.5rem !important;
}

/* Estilização dos dias do calendário */
.p-datepicker-calendar th {
  padding: 0.5rem !important;
  font-weight: 600 !important;
  color: #4b5563 !important;
}

.p-datepicker-calendar td {
  padding: 0.25rem !important;
}

.p-datepicker-calendar td > span {
  width: 2rem !important;
  height: 2rem !important;
  border-radius: 9999px !important;
  display: flex !important;
  align-items: center !important;
  justify-content: center !important;
  transition: all 0.2s ease !important;
}

.p-datepicker-calendar td > span:hover {
  background-color: #f3f4f6 !important;
}

.p-datepicker-calendar td > span.p-highlight {
  background-color: var(--iluria-color-4) !important;
  color: white !important;
}

/* Estilização dos botões de navegação do mês */
.p-datepicker-header {
  padding: 0.5rem !important;
  border: none !important;
  background-color: transparent !important;
}

.p-datepicker-header .p-datepicker-prev,
.p-datepicker-header .p-datepicker-next {
  width: 2rem !important;
  height: 2rem !important;
  border-radius: 9999px !important;
  color: #4b5563 !important;
}

.p-datepicker-header .p-datepicker-prev:hover,
.p-datepicker-header .p-datepicker-next:hover {
  background-color: #f3f4f6 !important;
  color: #111827 !important;
}

/* Estilização do seletor de hora */
.p-timepicker {
  margin-top: 0.5rem !important;
  padding-top: 0.5rem !important;
  border-top: 1px solid #e5e7eb !important;
  display: flex !important;
  justify-content: center !important;
}

.p-hour-picker,
.p-minute-picker,
.p-second-picker {
  margin: 0 0.25rem !important;
}

.p-separator {
  margin: 0 0.25rem !important;
}

.p-datepicker-buttonbar {
  padding: 0.5rem !important;
  border-top: 1px solid #e5e7eb !important;
  display: flex !important;
  justify-content: space-between !important;
}

.p-datepicker-buttonbar button {
  padding: 0.375rem 0.75rem !important;
  font-size: 0.875rem !important;
  border-radius: 0.375rem !important;
  transition: all 0.2s ease !important;
}

.p-datepicker-buttonbar button:first-child {
  background-color: #f3f4f6 !important;
  color: #4b5563 !important;
}

.p-datepicker-buttonbar button:last-child {
  background-color: var(--iluria-color-4) !important;
  color: white !important;
}

.p-datepicker-buttonbar button:hover {
  filter: brightness(0.95) !important;
}

/* ================================
   SCROLLBAR GLOBAIS - ILURIA DESIGN SYSTEM
   ================================ */

/* Estilização global para TODAS as scrollbars */
* {
  scrollbar-width: thin;
  scrollbar-color: var(--iluria-color-border-hover) var(--iluria-color-container-bg);
}

/* Webkit (Chrome, Safari, Edge) - Scrollbar vertical */
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

::-webkit-scrollbar-track {
  background: var(--iluria-color-container-bg);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border-hover);
  border-radius: 4px;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-primary);
}

::-webkit-scrollbar-corner {
  background: var(--iluria-color-container-bg);
}

/* Classe para scrollbars horizontais específicas (quando necessário override) */
.iluria-scrollbar-horizontal {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--iluria-color-border-hover) var(--iluria-color-container-bg);
}

.iluria-scrollbar-horizontal::-webkit-scrollbar {
  height: 6px;
}

.iluria-scrollbar-horizontal::-webkit-scrollbar-track {
  background: var(--iluria-color-container-bg);
  border-radius: 3px;
  margin: 0 4px;
}

.iluria-scrollbar-horizontal::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border-hover);
  border-radius: 3px;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.iluria-scrollbar-horizontal::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-primary);
  transform: scaleY(1.2);
}

.iluria-scrollbar-horizontal::-webkit-scrollbar-corner {
  background: var(--iluria-color-container-bg);
}

/* Classe global para scrollbars verticais elegantes */
.iluria-scrollbar-vertical {
  overflow-y: auto;
  overflow-x: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--iluria-color-border-hover) var(--iluria-color-container-bg);
}

.iluria-scrollbar-vertical::-webkit-scrollbar {
  width: 6px;
}

.iluria-scrollbar-vertical::-webkit-scrollbar-track {
  background: var(--iluria-color-container-bg);
  border-radius: 3px;
  margin: 4px 0;
}

.iluria-scrollbar-vertical::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border-hover);
  border-radius: 3px;
  transition: background-color 0.2s ease, transform 0.2s ease;
}

.iluria-scrollbar-vertical::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-primary);
  transform: scaleX(1.2);
}

.iluria-scrollbar-vertical::-webkit-scrollbar-corner {
  background: var(--iluria-color-container-bg);
}

/* Variação minimalista para scrollbars horizontais */
.iluria-scrollbar-horizontal-minimal {
  overflow-x: auto;
  overflow-y: hidden;
  scrollbar-width: thin;
  scrollbar-color: var(--iluria-color-border) transparent;
}

.iluria-scrollbar-horizontal-minimal::-webkit-scrollbar {
  height: 3px;
}

.iluria-scrollbar-horizontal-minimal::-webkit-scrollbar-track {
  background: transparent;
}

.iluria-scrollbar-horizontal-minimal::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 1.5px;
  transition: background-color 0.2s ease;
}

.iluria-scrollbar-horizontal-minimal::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-primary);
}

/* Classes utilitárias para containers com scrollbars */
.iluria-scroll-container {
  position: relative;
}

.iluria-scroll-fade-left::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to right, var(--iluria-color-container-bg), transparent);
  z-index: 1;
  pointer-events: none;
}

.iluria-scroll-fade-right::after {
  content: '';
  position: absolute;
  top: 0;
  right: 0;
  width: 20px;
  height: 100%;
  background: linear-gradient(to left, var(--iluria-color-container-bg), transparent);
  z-index: 1;
  pointer-events: none;
}

/* ================================
   COMO USAR AS CLASSES DE SCROLLBAR
   ================================ 
   
   Para scrollbar horizontal elegante:
   <div class="iluria-scrollbar-horizontal">
   
   Para scrollbar horizontal minimalista:
   <div class="iluria-scrollbar-horizontal-minimal">
   
   Para scrollbar vertical elegante:
   <div class="iluria-scrollbar-vertical">
   
   Para container com fade nas bordas:
   <div class="iluria-scroll-container iluria-scroll-fade-left iluria-scroll-fade-right">
   
   ================================ */

/* --- Iluria Theme Standardization: InputNumber --- */
.p-inputnumber {
  background-color: var(--iluria-color-input-bg) !important;
  border: 2px solid var(--iluria-color-input-border) !important;
  border-radius: 0.5rem !important;
}

.p-inputnumber:has(.p-inputnumber-input:focus) {
  /* Keep focus ring consistent with other inputs */
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
}

.p-inputnumber-input {
  background-color: transparent !important; /* Use container background */
  color: var(--iluria-color-input-text) !important;
}

.p-inputnumber-input::placeholder {
  color: var(--iluria-color-input-placeholder) !important;
}
/* --- end InputNumber standardization --- */

