<template>
  <div class="store-analytics">
    <div v-if="loading" class="loading-state">
      <div class="chart-skeleton"></div>
      <div class="legend-skeleton"></div>
    </div>
    
    <div v-else class="analytics-content">
      <div class="charts-grid">
        <!-- Sales by Category -->
        <div class="chart-section">
          <h4 class="chart-title">{{ $t('dashboard.salesByCategory') }}</h4>
          <div class="chart-container">
            <svg :width="chartSize" :height="chartSize" class="pie-chart">
              <g :transform="`translate(${chartSize/2}, ${chartSize/2})`">
                <path
                  v-for="(segment, index) in categorySegments"
                  :key="index"
                  :d="segment.path"
                  :fill="segment.color"
                  class="pie-segment"
                  @mouseover="showTooltip($event, segment)"
                  @mouseleave="hideTooltip"
                />
              </g>
            </svg>
            <div class="chart-legend">
              <div 
                v-for="(item, index) in data.salesByCategory" 
                :key="index"
                class="legend-item"
              >
                <div 
                  class="legend-color" 
                  :style="{ backgroundColor: getCategoryColor(index) }"
                ></div>
                <span class="legend-label">{{ item.name }}</span>
                <span class="legend-value">{{ item.value }}%</span>
              </div>
            </div>
          </div>
        </div>

        <!-- Payment Methods -->
        <div class="chart-section">
          <h4 class="chart-title">{{ $t('dashboard.paymentMethods') }}</h4>
          <div class="chart-container">
            <svg :width="chartSize" :height="chartSize" class="pie-chart">
              <g :transform="`translate(${chartSize/2}, ${chartSize/2})`">
                <path
                  v-for="(segment, index) in paymentSegments"
                  :key="index"
                  :d="segment.path"
                  :fill="segment.color"
                  class="pie-segment"
                  @mouseover="showTooltip($event, segment)"
                  @mouseleave="hideTooltip"
                />
              </g>
            </svg>
            <div class="chart-legend">
              <div 
                v-for="(item, index) in data.paymentMethods" 
                :key="index"
                class="legend-item"
              >
                <div 
                  class="legend-color" 
                  :style="{ backgroundColor: getPaymentColor(index) }"
                ></div>
                <span class="legend-label">{{ item.name }}</span>
                <span class="legend-value">{{ item.value }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- Tooltip -->
      <div
        v-if="tooltip.show"
        class="chart-tooltip"
        :style="{ left: tooltip.x + 'px', top: tooltip.y + 'px' }"
      >
        <div class="tooltip-content">
          <div class="tooltip-label">{{ tooltip.label }}</div>
          <div class="tooltip-value">{{ tooltip.value }}%</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref } from 'vue'
import { useI18n } from 'vue-i18n'

const props = defineProps({
  data: {
    type: Object,
    default: () => ({
      salesByCategory: [],
      paymentMethods: []
    })
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()
const chartSize = 120
const radius = 45

const tooltip = ref({
  show: false,
  x: 0,
  y: 0,
  label: '',
  value: ''
})

const categoryColors = ['#10b981', '#3b82f6', '#f59e0b', '#ef4444', '#8b5cf6']
const paymentColors = ['#6366f1', '#06b6d4', '#84cc16', '#f97316', '#ec4899']

const getCategoryColor = (index) => categoryColors[index % categoryColors.length]
const getPaymentColor = (index) => paymentColors[index % paymentColors.length]

const createPieSegments = (data, colors) => {
  if (!data || !data.length) return []
  
  const total = data.reduce((sum, item) => sum + item.value, 0)
  let currentAngle = 0
  
  return data.map((item, index) => {
    const percentage = item.value / total
    const angle = percentage * 2 * Math.PI
    const startAngle = currentAngle
    const endAngle = currentAngle + angle
    
    const x1 = radius * Math.cos(startAngle)
    const y1 = radius * Math.sin(startAngle)
    const x2 = radius * Math.cos(endAngle)
    const y2 = radius * Math.sin(endAngle)
    
    const largeArcFlag = angle > Math.PI ? 1 : 0
    
    const path = [
      'M', 0, 0,
      'L', x1, y1,
      'A', radius, radius, 0, largeArcFlag, 1, x2, y2,
      'Z'
    ].join(' ')
    
    currentAngle += angle
    
    return {
      path,
      color: colors[index % colors.length],
      label: item.name,
      value: item.value
    }
  })
}

const categorySegments = computed(() => 
  createPieSegments(props.data.salesByCategory, categoryColors)
)

const paymentSegments = computed(() => 
  createPieSegments(props.data.paymentMethods, paymentColors)
)

const showTooltip = (event, segment) => {
  const rect = event.target.getBoundingClientRect()
  tooltip.value = {
    show: true,
    x: rect.left + rect.width / 2,
    y: rect.top - 40,
    label: segment.label,
    value: segment.value
  }
}

const hideTooltip = () => {
  tooltip.value.show = false
}
</script>

<style scoped>
.store-analytics {
  height: 100%;
  position: relative;
}

.loading-state {
  display: flex;
  flex-direction: column;
  gap: 16px;
  height: 100%;
}

.chart-skeleton {
  width: 120px;
  height: 120px;
  border-radius: 50%;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  margin: 0 auto;
}

.legend-skeleton {
  flex: 1;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.analytics-content {
  height: 100%;
}

.charts-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 24px;
  height: 100%;
}

.chart-section {
  display: flex;
  flex-direction: column;
}

.chart-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 16px 0;
  text-align: center;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.pie-chart {
  flex-shrink: 0;
}

.pie-segment {
  cursor: pointer;
  transition: all 0.2s ease;
}

.pie-segment:hover {
  filter: brightness(1.1);
  transform: scale(1.05);
  transform-origin: center;
}

.chart-legend {
  display: flex;
  flex-direction: column;
  gap: 8px;
  width: 100%;
  max-width: 140px;
}

.legend-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.75rem;
}

.legend-color {
  width: 12px;
  height: 12px;
  border-radius: 2px;
  flex-shrink: 0;
}

.legend-label {
  flex: 1;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.legend-value {
  color: var(--iluria-color-text-secondary);
  font-weight: 600;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.chart-tooltip {
  position: fixed;
  pointer-events: none;
  z-index: 50;
  transform: translateX(-50%);
}

.tooltip-content {
  background: var(--iluria-color-text-primary);
  color: var(--iluria-color-container-bg);
  padding: 8px 12px;
  border-radius: 6px;
  font-size: 0.875rem;
  box-shadow: var(--iluria-shadow-lg);
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.tooltip-content::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 5px solid transparent;
  border-top-color: var(--iluria-color-text-primary);
}

.tooltip-label {
  font-weight: 500;
  margin-bottom: 2px;
}

.tooltip-value {
  font-weight: 600;
  color: var(--iluria-color-success);
}

@media (max-width: 768px) {
  .charts-grid {
    grid-template-columns: 1fr;
    gap: 32px;
  }
  
  .chart-container {
    gap: 12px;
  }
  
  .legend-item {
    font-size: 0.7rem;
  }
  
  .legend-color {
    width: 10px;
    height: 10px;
  }
}
</style> 
