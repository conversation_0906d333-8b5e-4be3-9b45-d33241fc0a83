  <template>
  <div class="collaboration-invites">
    <!-- Invites Table -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="invites"
        :columns="invitesTableColumns"
        :loading="loading"
        dataKey="id"
        class="invites-table iluria-data-table"
      >
        <!-- Column Headers -->
        <template #header-store>
          <span class="column-header store-header">Loja</span>
        </template>
        <template #header-inviter>
          <span class="column-header inviter-header">Convidado por</span>
        </template>
        <template #header-role>
          <span class="column-header role-header">Cargo Oferecido</span>
        </template>
        <template #header-date>
          <span class="column-header date-header">Data do Convite</span>
        </template>
        <template #header-actions>
          <span class="column-header actions-header">Ações</span>
        </template>

        <!-- Column Templates -->
        <template #column-store="{ data }">
          <div class="store-info">
            <div class="store-icon">
              <img
                v-if="data.storeIcon"
                :src="data.storeIcon"
                :alt="data.storeName || 'Logo da loja'"
                class="store-logo"
              />
              <HugeiconsIcon
                v-else
                :icon="Store01Icon"
                size="16"
                :strokeWidth="1.5"
              />
            </div>
            <div class="store-details">
              <h4 class="store-name">{{ data.storeName || data.store?.name || 'Loja' }}</h4>
              <p class="store-url" v-if="data.store?.urlIluria">
                {{ data.store.urlIluria }}
              </p>
            </div>
          </div>
        </template>

        <template #column-inviter="{ data }">
          <div class="inviter-info">
            <div class="inviter-avatar">
              <HugeiconsIcon :icon="UserIcon" size="16" :strokeWidth="1.5" />
            </div>
            <div class="inviter-details">
              <h4 class="inviter-name">{{ data.inviterName || data.inviter?.name || 'Usuário' }}</h4>
              <p class="inviter-email">{{ data.inviterEmail || data.inviter?.email || 'Email não disponível' }}</p>
            </div>
          </div>
        </template>

        <template #column-role="{ data }">
          <span class="role-badge" :class="getRoleClass(data.roleName)">
            {{ data.roleName || 'Carregando...' }}
          </span>
        </template>

        <template #column-date="{ data }">
          <div class="invite-date">
            <span class="date-text">{{ formatDate(data.createdAt) }}</span>
            <span class="time-ago">{{ getTimeAgo(data.createdAt) }}</span>
          </div>
        </template>

        <template #column-actions="{ data }">
          <div class="action-buttons">
            <IluriaButton 
              color="success" 
              size="small" 
              :hugeIcon="CheckmarkCircle01Icon" 
              @click.prevent="confirmAcceptInvite(data)"
              title="Aceitar convite"
              :loading="data.accepting"
            >
              Aceitar
            </IluriaButton>
            <IluriaButton 
              color="danger" 
              size="small" 
              variant="outline"
              :hugeIcon="Cancel01Icon" 
              @click.prevent="confirmRejectInvite(data)"
              title="Recusar convite"
              :loading="data.rejecting"
            >
              Recusar
            </IluriaButton>
          </div>
        </template>
        
        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <HugeiconsIcon :icon="Mail01Icon" size="48" :strokeWidth="1.5" />
            </div>
            <h3 class="empty-title">Nenhum convite pendente</h3>
            <p class="empty-description">Quando você receber convites para colaborar em lojas, eles aparecerão aqui.</p>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>Carregando convites...</span>
          </div>
        </template>
      </IluriaDataTable>

      <!-- Pagination -->
      <div class="pagination-container" v-if="totalPages > 0">
        <IluriaPagination 
          :current-page="currentPage"
          :total-pages="totalPages"
          @go-to-page="changePage"
        />
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal 
      :isVisible="showConfirmDialog"
      :title="confirmData.title" 
      :message="confirmData.message"
      :type="confirmData.type || 'error'"
      :confirmText="confirmData.confirmText || 'Confirmar'"
      :cancelText="confirmData.cancelText || 'Cancelar'"
      @confirm="handleConfirm"
      @cancel="showConfirmDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import { 
  Store01Icon,
  UserIcon,
  CheckmarkCircle01Icon,
  Cancel01Icon,
  Mail01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { collaborationApi } from '@/services/collaboration.service'
import { useToast } from '@/services/toast.service'

const toast = useToast()

// Confirmation dialog state
const showConfirmDialog = ref(false)
const confirmData = ref({ title: '', message: '', type: 'error', onConfirm: null })

// Reactive data
const invites = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)

// Table columns configuration
const invitesTableColumns = computed(() => [
  { field: 'store', headerClass: 'col-store', class: 'col-store' },
  { field: 'inviter', headerClass: 'col-inviter', class: 'col-inviter' },
  { field: 'role', headerClass: 'col-role', class: 'col-role' },
  { field: 'date', headerClass: 'col-date', class: 'col-date' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
])

// Methods
const loadInvites = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: 10,
      status: 'pending'
    }
    
    const response = await collaborationApi.getMyInvites(params)
    
    // Se response.data é um array, use diretamente; se é um objeto com content, use content
    if (Array.isArray(response.data)) {
      invites.value = response.data
      totalPages.value = response.data.length > 10 ? Math.ceil(response.data.length / 10) : 0
    } else {
      invites.value = response.data.content || response.data
      totalPages.value = response.data.page?.totalPages || response.data.totalPages || 0
    }
    
  } catch (error) {
    console.error('Error loading invites:', error)
    toast.showError('Erro ao carregar convites')
  } finally {
    loading.value = false
  }
}

const changePage = (page) => {
  currentPage.value = page
  loadInvites()
}

const confirmAcceptInvite = (invite) => {
  const storeName = invite.storeName || invite.store?.name || 'esta loja'
  const roleLabel = invite.roleName || 'colaborador'
  
  confirmData.value = {
    title: 'Aceitar Convite',
    message: `Deseja aceitar o convite para ser ${roleLabel} na loja "${storeName}"?`,
    type: 'info',
    confirmText: 'Aceitar',
    cancelText: 'Cancelar',
    onConfirm: () => acceptInvite(invite)
  }
  showConfirmDialog.value = true
}

const confirmRejectInvite = (invite) => {
  const storeName = invite.storeName || invite.store?.name || 'esta loja'
  
  confirmData.value = {
    title: 'Recusar Convite',
    message: `Deseja recusar o convite para colaborar na loja "${storeName}"?`,
    type: 'error',
    confirmText: 'Recusar',
    cancelText: 'Cancelar',
    onConfirm: () => rejectInvite(invite)
  }
  showConfirmDialog.value = true
}

const acceptInvite = async (invite) => {
  const inviteIndex = invites.value.findIndex(i => i.id === invite.id)
  if (inviteIndex !== -1) {
    invites.value[inviteIndex].accepting = true
  }
  
  try {
    await collaborationApi.acceptInvite(invite.id)
    toast.showSuccess('Convite aceito com sucesso!')
    
    // Remove the invite from the list
    invites.value = invites.value.filter(i => i.id !== invite.id)
    
  } catch (error) {
    console.error('Error accepting invite:', error)
    toast.showError('Erro ao aceitar convite')
    
    if (inviteIndex !== -1) {
      invites.value[inviteIndex].accepting = false
    }
  }
}

const rejectInvite = async (invite) => {
  const inviteIndex = invites.value.findIndex(i => i.id === invite.id)
  if (inviteIndex !== -1) {
    invites.value[inviteIndex].rejecting = true
  }
  
  try {
    await collaborationApi.rejectInvite(invite.id)
    toast.showSuccess('Convite recusado')
    
    // Remove the invite from the list
    invites.value = invites.value.filter(i => i.id !== invite.id)
    
  } catch (error) {
    console.error('Error rejecting invite:', error)
    toast.showError('Erro ao recusar convite')
    
    if (inviteIndex !== -1) {
      invites.value[inviteIndex].rejecting = false
    }
  }
}

// Utility functions
const getRoleClass = (roleName) => {
  if (!roleName) return 'role-default'
  
  // Generate a consistent class based on role name
  const normalizedName = roleName.toLowerCase()
  if (normalizedName.includes('admin')) return 'role-admin'
  if (normalizedName.includes('design')) return 'role-designer'
  if (normalizedName.includes('market')) return 'role-marketer'
  
  return 'role-default'
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('pt-BR')
}

const getTimeAgo = (dateString) => {
  if (!dateString) return ''
  
  const now = new Date()
  const date = new Date(dateString)
  const diffInSeconds = Math.floor((now - date) / 1000)
  
  if (diffInSeconds < 60) return 'Agora mesmo'
  if (diffInSeconds < 3600) return `${Math.floor(diffInSeconds / 60)}m atrás`
  if (diffInSeconds < 86400) return `${Math.floor(diffInSeconds / 3600)}h atrás`
  
  const days = Math.floor(diffInSeconds / 86400)
  return `${days}d atrás`
}

// Confirmation dialog handler
const handleConfirm = () => {
  if (confirmData.value.onConfirm) {
    confirmData.value.onConfirm()
  }
  showConfirmDialog.value = false
}

// Lifecycle
onMounted(() => {
  loadInvites()
})
</script>

<style scoped>
.collaboration-invites {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-store) { width: auto; text-align: center; }
:deep(.col-inviter) { width: 200px; text-align: center; }
:deep(.col-role) { width: 140px; text-align: center; }
:deep(.col-date) { width: 140px; text-align: center; }
:deep(.col-actions) { width: 100%; text-align: center; }

/* Table Styling */
:deep(.invites-table) {
  border-radius: 8px;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.invites-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

/* Header alignments */
.store-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

.inviter-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

.role-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

.date-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

.actions-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

:deep(.invites-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  font-size: 14px;
}

/* Store Info */
.store-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  max-width: fit-content;
}

.store-icon {
  width: 32px;
  height: 32px;
  background: var(--iluria-color-primary);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  overflow: hidden;
}

.store-logo {
  width: 100%;
  height: 100%;
  object-fit: cover;
  border-radius: 8px;
}

.store-details {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.store-name {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-url {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Inviter Info */
.inviter-info {
  display: flex;
  align-items: center;
  justify-content: flex-start;
  gap: 8px;
  width: 100%;
  margin-left: auto;
  margin-right: auto;
  max-width: fit-content;
}

.inviter-avatar {
  width: 28px;
  height: 28px;
  background: var(--iluria-color-sidebar-bg);
  color: var(--iluria-color-text-secondary);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  flex-shrink: 0;
  border: 1px solid var(--iluria-color-border);
}

.inviter-details {
  flex: 1;
  min-width: 0;
  text-align: left;
}

.inviter-name {
  font-size: 13px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.inviter-email {
  font-size: 11px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

/* Role Badge */
.role-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  text-align: center;
  white-space: nowrap;
  display: block;
  margin: 0 auto;
  width: fit-content;
}

.role-admin {
  background: #fef3c7;
  color: #d97706;
}

.role-designer {
  background: #e0e7ff;
  color: #3730a3;
}

.role-marketer {
  background: #f3e8ff;
  color: #7c3aed;
}

.role-default {
  background: #f3f4f6;
  color: #374151;
}

/* Date Column */
.invite-date {
  text-align: center;
  width: 100%;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.date-text {
  display: block;
  font-size: 13px;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  margin-bottom: 2px;
}

.time-ago {
  display: block;
  font-size: 11px;
  color: var(--iluria-color-text-secondary);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
  align-items: center;
  width: 100%;
  flex-wrap: wrap;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  color: var(--iluria-color-text-muted);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 16px;
  background: var(--iluria-color-sidebar-bg);
}

/* Responsive */
@media (max-width: 768px) {
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
  
  :deep(.invites-table .p-datatable-tbody > tr > td) {
    padding: 12px 8px;
  }
  
  .store-details,
  .inviter-details {
    max-width: 120px;
  }
}
</style>