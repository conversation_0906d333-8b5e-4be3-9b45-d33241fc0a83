{"title": "<PERSON><PERSON><PERSON>", "subtitle": "<PERSON><PERSON><PERSON><PERSON> seus produtos combinados", "searchPlaceholder": "Buscar produtos combinados...", "actions": {"add": "<PERSON><PERSON><PERSON><PERSON>", "edit": "<PERSON><PERSON>", "delete": "Excluir"}, "table": {"headers": {"image": "Imagem", "name": "Nome do Produto", "status": "Status", "actions": "Ações"}}, "status": {"active": "Ativo", "inactive": "Inativo"}, "empty": {"message": "Nenhum produto combinado encontrado.", "action": "Crie seu primeiro produto combinado!"}, "loading": "Carregando produtos combinados...", "edit": {"titleCreate": "<PERSON><PERSON><PERSON><PERSON>", "titleEdit": "Editar <PERSON>", "sections": {"basicInfo": "Informações Básicas", "basicInfoDescription": "Configure nome, descrição e status do produto", "status": "Status", "images": "Imagens", "imagesDescription": "Faça upload e configure stickers nas imagens"}, "sidebar": {"quickInfo": "Informações Rápidas", "quickInfoDescription": "Resumo do produto em construção", "totalImages": "Total de Imagens", "totalStickers": "Total de Stickers", "linkedProducts": "<PERSON><PERSON><PERSON>", "limits": "Limites", "limitsDescription": "Acompanhe os limites de produtos"}, "fields": {"productName": "Nome do Produto *", "productNamePlaceholder": "Digite o nome do produto...", "description": "Descrição", "descriptionPlaceholder": "Descreva este produto...", "productStatus": "Status do Produto", "statusPlaceholder": "Selecione um status"}, "statusOptions": {"active": "Visível", "inactive": "Invisível"}, "preview": {"title": "Pré-visualização do item {index}", "dragHint": "Pegue ele aqui:", "stickerHint": "Novo sticker nasce no centro. Arraste para posicionar.", "removeHint": "<PERSON><PERSON><PERSON> até a borda para remover. Clique duas vezes para escolher produto."}, "removeZone": {"title": "Arraste aqui para excluir"}, "tooltips": {"withProduct": "{name} - R$ {price}\nArraste para mover, arraste até a borda para remover, clique duplo para trocar produto.", "withoutProduct": "Sticker {index}: <PERSON><PERSON><PERSON> para mover ou arraste até a borda para remover. Clique duplo para escolher produto.", "newSticker": "Novo sticker: nasce no centro da imagem. Arraste para posicionar ou arraste até a borda para remover."}, "actions": {"create": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>"}, "loading": "Carregando...", "validation": {"nameRequired": "Nome do produto é obrigatório", "imageRequired": "Pelo menos uma imagem é obrigatória", "maxImages": "Máximo de 5 imagens permitidas", "productRequired": "Adicione pelo menos um produto a um sticker para criar o produto combinado"}, "saving": {"info": "Salvando: {productCount} produtos em {stickerCount} stickers"}}, "messages": {"limitReached": "Limite de {max} produtos combinados atingido", "deleteConfirm": "Tem certeza que deseja excluir o produto combinado \"{name}\"?", "deleteSuccess": "Produto \"{name}\" excluído com sucesso", "deleteError": "Erro ao excluir produto combinado", "deleteNotFound": "Produto não encontrado", "deleteNoPermission": "Sem permissão para excluir este produto", "deleteServerError": "Erro no servidor. Tente novamente mais tarde", "deleteNetworkError": "Erro de conexão. Verifique sua internet", "invalidProduct": "Produto inválido para exclusão", "loadError": "Erro ao carregar produtos combinados", "stickerRemoved": "Sticker removido com sucesso!", "createSuccess": "Produto combinado criado com sucesso", "updateSuccess": "Produto combinado atualizado com sucesso", "createError": "Erro ao criar produto combinado", "updateError": "Erro ao atualizar produto combinado", "loadSuccess": "Produto carregado com sucesso", "loadEditError": "Erro ao carregar produto combinado", "savedLocally": "Produto salvo localmente. Será sincronizado quando possível."}}