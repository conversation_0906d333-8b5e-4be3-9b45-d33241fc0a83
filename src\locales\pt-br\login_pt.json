{"pageTitle": "<PERSON><PERSON> - <PERSON><PERSON>", "pageDescription": "<PERSON><PERSON> - <PERSON><PERSON><PERSON><PERSON>", "welcomeBack": "Bem-vindo de volta!", "welcomeMessage": "Por favor, faça login na sua conta", "email": "E-mail", "password": "<PERSON><PERSON>", "rememberMe": "<PERSON><PERSON><PERSON> de mim", "forgotPassword": "Esqueceu sua senha?", "createAccount": "C<PERSON><PERSON> conta", "signIn": "Entrar", "dontHaveAccount": "Não tem uma conta?", "signUp": "Cadastre-se", "orContinueWith": "Ou continue com", "googleSignIn": "Entrar com Google", "facebookSignIn": "Entrar com Facebook", "githubSignIn": "Entrar com GitHub", "microsoftSignIn": "Entrar com Microsoft", "appleSignIn": "Entrar com Apple", "showPassword": "<PERSON><PERSON> senha", "hidePassword": "<PERSON><PERSON><PERSON> <PERSON><PERSON>a", "networkError": "Erro de rede. Por favor, verifique sua conexão com a internet", "invalidCredentials": "E-mail ou senha in<PERSON>", "accountLocked": "Conta bloqueada. Tente novamente mais tarde ou redefina sua senha.", "accountDisabled": "Esta conta foi desativada. Entre em contato com o suporte.", "emailNotVerified": "E-mail não verificado. Verifique sua caixa de entrada para confirmar seu cadastro.", "resendVerificationEmail": "Reenviar e-mail de verificação", "verificationEmailSent": "E-mail de verificação enviado. Verifique sua caixa de entrada.", "tooManyAttempts": "Muitas tentativas de login. Tente novamente em {minutes} minutos.", "sessionExpired": "Sua sessão expirou. Por favor, faça login novamente.", "maintenanceMode": "O sistema está em manutenção no momento. Por favor, tente novamente mais tarde.", "mfaRequired": "Autenticação de dois fatores necessária", "mfaCode": "Código de Verificação", "mfaCodePlaceholder": "Digite o código de 6 dígitos", "mfaCodeHelp": "Digite o código de verificação do seu aplicativo autenticador", "mfaRecoveryCode": "Código de Recuperação", "mfaRecoveryCodePlaceholder": "Digite o código de recuperação", "mfaRecoveryCodeHelp": "Perdeu acesso ao seu dispositivo? Use um código de recuperação", "mfaVerify": "Verificar", "mfaBackToLogin": "Voltar para o login", "mfaRememberDevice": "Lembrar este dispositivo por 30 dias", "mfaInvalidCode": "Código de verificação inválido", "mfaTooManyAttempts": "Muitas tentativas. Tente novamente mais tarde.", "mfaRecoveryCodeUsed": "Código de recuperação utilizado. Gere novos códigos após fazer login.", "mfaRecoveryCodeInvalid": "Código de recuperação inválido", "mfaSetupRequired": "Configuração de autenticação de dois fatores necessária", "mfaSetupInstructions": "Escaneie o código QR com seu aplicativo autenticador e digite o código de verificação abaixo:", "mfaManualSetupCode": "Ou digite este código manualmente:", "mfaVerifySetup": "Verificar Configuração", "mfaSetupComplete": "Autenticação de dois fatores ativada com sucesso", "mfaRecoveryCodesTitle": "Códigos de Recuperação", "mfaRecoveryCodesInstructions": "Salve estes códigos em um local seguro. Você pode usá-los para acessar sua conta se perder o acesso ao seu aplicativo autenticador.", "welcome": "Bem-vindo!", "mfaEmailSent": "Enviamos um código de verificação para", "checkYourInbox": "Verifique sua caixa de entrada.", "totpRequired": "Digite o código do seu aplicativo autenticador", "totpCode": "Código do Aplicativo", "rememberMfa": "Lembrar este dispositivo por 30 dias", "submitMfa": "Verificar", "resend": "Reenviar", "resendIn": "Reenviar em {seconds}s", "mfaResentSuccess": "Código reenviado com sucesso!", "mfaResentError": "Erro ao reenviar código. Tente novamente.", "genericError": "Ocorreu um erro. Tente novamente.", "invalidCode": "<PERSON><PERSON><PERSON>", "sending": "Enviando..."}