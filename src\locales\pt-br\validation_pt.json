{"required": "Este campo é obrigatório", "fieldRequired": "{field} é obrigatório", "maxLength": "{field} deve ter no máximo {length} caracteres", "invalidNumber": "{field} deve ser um número válido", "nonNegative": "{field} deve ser maior ou igual a 0", "positive": "{field} deve ser maior que 0", "invalidEmail": "E-mail inválido", "invalidUrl": "URL inválida", "invalidDate": "Data inválida", "invalidTime": "<PERSON><PERSON>", "invalidDateTime": "Data e hora inválidas", "invalidColor": "<PERSON><PERSON>", "invalidHexColor": "Cor hexadecimal inválida", "invalidPhone": "<PERSON><PERSON><PERSON>", "invalidZipCode": "CEP inválido", "invalidCpf": "CPF inválido", "invalidCnpj": "CNPJ inválido", "passwordsDontMatch": "As senhas não conferem", "invalidPassword": "A senha deve conter pelo menos 8 caracteres, incluindo letras maiús<PERSON>s, minúsculas, números e caracteres especiais", "invalidUsername": "O nome de usuário deve conter apenas letras, n<PERSON><PERSON>os, hífens e sublinhados", "minLength": "{field} deve ter pelo menos {length} caracteres", "exactLength": "{field} deve ter exatamente {length} caracteres", "between": "{field} deve estar entre {min} e {max}", "lessThan": "{field} deve ser menor que {max}", "lessThanOrEqual": "{field} deve ser menor ou igual a {max}", "greaterThan": "{field} deve ser maior que {min}", "greaterThanOrEqual": "{field} deve ser maior ou igual a {min}", "notEqual": "{field} não pode ser igual a {value}", "equal": "{field} deve ser igual a {value}", "contains": "{field} deve conter {value}", "notContains": "{field} não pode conter {value}", "isIn": "{field} deve ser um dos seguintes valores: {values}", "notIn": "{field} não pode ser um dos seguintes valores: {values}", "isEmail": "{field} deve ser um e-mail válido", "isUrl": "{field} deve ser uma URL válida", "isIp": "{field} deve ser um endereço IP válido", "isIPv4": "{field} deve ser um endereço IPv4 válido", "isIPv6": "{field} deve ser um endereço IPv6 válido", "isAlpha": "{field} deve conter apenas letras", "isAlphanumeric": "{field} deve conter apenas letras e números", "isNumeric": "{field} deve conter apenas n<PERSON>", "isInt": "{field} deve ser um número inteiro", "isFloat": "{field} deve ser um número decimal", "isDecimal": "{field} deve ser um número decimal", "isLowercase": "{field} deve estar em letras minúsculas", "isUppercase": "{field} deve estar em letras maiús<PERSON>s", "isJson": "{field} deve ser um JSON válido", "isBase64": "{field} deve ser um Base64 válido", "isMongoId": "{field} deve ser um ID do MongoDB válido", "isUuid": "{field} deve ser um UUID válido", "isCreditCard": "{field} deve ser um número de cartão de crédito válido", "mustBePositive": "Deve ser um valor positivo", "maxPercentage": "O valor máximo é 100%"}