<template>
  <IluriaModal
    v-model="isVisible"
    :title="t('layoutEditor.createLayout')"
    :subtitle="t('layoutEditor.createLayoutSubtitle')"
    :save-label="t('create')"
    :cancel-label="t('cancel')"
    :icon="PlusSignSquareIcon"
    icon-color="blue"
    @save="handleCreate"
    @cancel="handleCancel"
    :dialog-style="{ width: '500px' }"
  >
    <form @submit.prevent="handleCreate" class="create-layout-form">
      <!-- Tipo de Layout -->
      <div class="form-group">
        <label class="form-label">{{ t('layoutEditor.layoutType') }}</label>
        <select 
          v-model="formData.type" 
          class="form-select"
          required
          :disabled="props.baseType !== null"
        >
          <option value="static">{{ t('layoutEditor.staticLayout') }}</option>
          <option value="product">{{ t('layoutEditor.productLayout') }}</option>
          <option value="collection">{{ t('layoutEditor.collectionLayout') }}</option>
        </select>
        <small class="form-help">{{ t('layoutEditor.layoutTypeHelp') }}</small>
      </div>

      <!-- Nome do Layout -->
      <div class="form-group">
        <label class="form-label">{{ t('layoutEditor.layoutName') }}</label>
        <IluriaInputText
          v-model="formData.name"
          type="text"
          :placeholder="t('layoutEditor.layoutNamePlaceholder')"
          required
          maxlength="100"
          class="form-input"
        />
        <small class="form-help">{{ t('layoutEditor.layoutNameHelp') }}</small>
      </div>

      <!-- Baseado em (sempre obrigatório) -->
      <div class="form-group">
        <label class="form-label">{{ t('layoutEditor.basedOn') }}</label>
        <div class="base-layout-info">
          <span class="base-layout-icon">{{ getBaseLayoutIcon() }}</span>
          <span class="base-layout-name">{{ getBaseLayoutName() }}</span>
        </div>
        <small class="form-help">{{ t('layoutEditor.basedOnHelp') }}</small>
      </div>

      <!-- Preview do ID que será gerado -->
      <div v-if="formData.name" class="form-group">
        <label class="form-label">{{ t('layoutEditor.generatedId') }}</label>
        <div class="generated-id">
          <code>{{ generatePreviewId() }}</code>
        </div>
        <small class="form-help">{{ t('layoutEditor.generatedIdHelp') }}</small>
      </div>
    </form>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import { PlusSignSquareIcon } from '@hugeicons-pro/core-stroke-rounded'
import { useLayoutManager } from '@/composables/useLayoutManager'

const { t } = useI18n()
const { findLayoutById, generateLayoutId } = useLayoutManager()

const props = defineProps({
  modelValue: {
    type: Boolean,
    default: false
  },
  baseLayoutId: {
    type: String,
    default: null
  },
  baseType: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:modelValue', 'created'])

// Estado do modal
const isVisible = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

// Dados do formulário
const formData = ref({
  type: props.baseType || 'static',
  name: ''
})

// Resetar formulário quando modal abre
watch(() => props.modelValue, (newValue) => {
  if (newValue) {
    formData.value = {
      type: props.baseType || 'static',
      name: ''
    }
  }
})

// Métodos
const handleCreate = async () => {
  if (!formData.value.name.trim()) {
    return
  }

  // Sempre exige um baseLayoutId - não permite criar do zero
  if (!props.baseLayoutId) {
    console.error('baseLayoutId é obrigatório para criar um layout')
    return
  }

  try {
    const layoutData = {
      baseLayoutId: props.baseLayoutId,
      newName: formData.value.name.trim(),
      type: formData.value.type
    }

    emit('created', layoutData)
    handleCancel()
  } catch (error) {
    console.error('Erro ao criar layout:', error)
  }
}

const handleCancel = () => {
  isVisible.value = false
  formData.value = {
    type: props.baseType || 'static',
    name: ''
  }
}

const getBaseLayoutIcon = () => {
  if (!props.baseLayoutId) return '📄'
  
  const baseLayout = findLayoutById(props.baseLayoutId)
  return baseLayout?.icon || '📄'
}

const getBaseLayoutName = () => {
  if (!props.baseLayoutId) return ''
  
  const baseLayout = findLayoutById(props.baseLayoutId)
  return baseLayout?.name || props.baseLayoutId
}

const generatePreviewId = () => {
  if (!formData.value.name.trim()) return ''
  
  return generateLayoutId(formData.value.name, formData.value.type)
}
</script>

<style scoped>
.create-layout-form {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.form-label {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.form-select {
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.form-select:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
}

.form-select:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.form-input {
  width: 100%;
}

.form-help {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

.base-layout-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.75rem;
  background: var(--iluria-color-hover);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.base-layout-icon {
  font-size: 1.25rem;
}

.base-layout-name {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.generated-id {
  padding: 0.75rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.generated-id code {
  font-family: 'SF Mono', Monaco, 'Cascadia Code', 'Roboto Mono', Consolas, 'Courier New', monospace;
  font-size: 0.875rem;
  color: var(--iluria-color-primary);
  background: none;
  padding: 0;
}
</style> 