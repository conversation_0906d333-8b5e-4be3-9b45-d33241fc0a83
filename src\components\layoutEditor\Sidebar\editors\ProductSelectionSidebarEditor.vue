<template>
  <div class="product-selection-sidebar-editor">
    <!-- Header -->
    <div class="editor-header">
      <h3>{{ $t('layoutEditor.sidebar.productSelection') }}</h3>
      <IluriaButton 
        variant="ghost" 
        color="dark" 
        size="small" 
        @click="emit('close')"
      >
        <Cancel01Icon />
      </IluriaButton>
    </div>

    <!-- Selection Mode Toggle -->
    <div class="selection-mode-section">
      <label class="section-label">{{ $t('productGrid.selectionMode') }}</label>
      <IluriaToggleSwitch 
        v-model="selectionMode" 
        :label="selectionMode === 'automatic' ? $t('productGrid.automatic') : $t('productGrid.manual')"
        @update:modelValue="updateSelectionMode"
      />
    </div>

    <!-- Automatic Mode Settings -->
    <div v-if="selectionMode === 'automatic'" class="automatic-settings">
      <!-- Category Selection -->
      <div class="form-group">
        <label class="section-label">{{ $t('productGrid.category') }}</label>
        <select 
          v-model="selectedCategory" 
          @change="updateCategory"
          class="category-select"
        >
          <option value="all">{{ $t('productGrid.allCategories') }}</option>
          <option 
            v-for="category in categories" 
            :key="category.id" 
            :value="category.id"
          >
            {{ category.name }}
          </option>
        </select>
      </div>

      <!-- Sort Options -->
      <div class="form-group">
        <label class="section-label">{{ $t('productGrid.sortBy') }}</label>
        <select 
          v-model="sortBy" 
          @change="updateSortBy"
          class="sort-select"
        >
          <option value="name">{{ $t('productGrid.sortByName') }}</option>
          <option value="newest">{{ $t('productGrid.sortByNewest') }}</option>
          <option value="price-asc">{{ $t('productGrid.sortByPriceAsc') }}</option>
          <option value="price-desc">{{ $t('productGrid.sortByPriceDesc') }}</option>
        </select>
      </div>

      <!-- Limit -->
      <div class="form-group">
        <label class="section-label">{{ $t('productGrid.limit') }}</label>
        <IluriaInputText 
          v-model="productLimit" 
          type="number" 
          min="1" 
          max="50"
          @input="updateLimit"
        />
      </div>
    </div>

    <!-- Manual Mode Settings -->
    <div v-if="selectionMode === 'manual'" class="manual-settings">
      <!-- Product Search -->
      <div class="form-group">
        <label class="section-label">{{ $t('productGrid.searchProducts') }}</label>
        <IluriaInputText 
          v-model="searchTerm" 
          :placeholder="$t('productGrid.searchPlaceholder')"
          @input="searchProducts"
        />
      </div>

      <!-- Available Products -->
      <div class="available-products">
        <label class="section-label">{{ $t('productGrid.availableProducts') }}</label>
        <div class="products-list">
          <div 
            v-for="product in availableProducts" 
            :key="product.id" 
            class="product-item"
            @click="toggleProduct(product)"
          >
            <input 
              type="checkbox" 
              :checked="isProductSelected(product.id)"
              @change="toggleProduct(product)"
            />
            <img 
              v-if="product.image" 
              :src="product.image" 
              :alt="product.name"
              class="product-image"
            />
            <div class="product-info">
              <span class="product-name">{{ product.name }}</span>
              <span class="product-price">{{ formatCurrency(product.price) }}</span>
            </div>
          </div>
        </div>
      </div>

      <!-- Selected Products -->
      <div v-if="selectedProducts.length > 0" class="selected-products">
        <label class="section-label">{{ $t('productGrid.selectedProducts') }} ({{ selectedProducts.length }})</label>
        <div class="selected-list">
          <div 
            v-for="product in selectedProducts" 
            :key="product.id" 
            class="selected-item"
          >
            <span class="product-name">{{ product.name }}</span>
            <IluriaButton 
              variant="ghost" 
              color="danger" 
              size="small"
              @click="removeProduct(product.id)"
            >
              <Delete01Icon />
            </IluriaButton>
          </div>
        </div>
      </div>
    </div>

    <!-- Preview Section -->
    <div class="preview-section">
      <label class="section-label">{{ $t('productGrid.preview') }}</label>
      <div class="preview-info">
        <p>{{ $t('productGrid.previewText', { 
          mode: selectionMode === 'automatic' ? $t('productGrid.automatic') : $t('productGrid.manual'),
          count: selectionMode === 'automatic' ? productLimit : selectedProducts.length 
        }) }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import { Cancel01Icon, Delete01Icon } from '@hugeicons-pro/core-stroke-rounded'
import { productsApi } from '@/services/product.service'
import { categoryService } from '@/services/category.service'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close'])

// Reactive data
const selectionMode = ref('automatic')
const selectedCategory = ref('all')
const sortBy = ref('name')
const productLimit = ref(8)
const searchTerm = ref('')
const categories = ref([])
const availableProducts = ref([])
const selectedProducts = ref([])

// Initialize from element data
onMounted(async () => {
  // Load categories
  try {
 
    const categoryData = await categoryService.fetchCategories()
  
    categories.value = categoryData || []
  } catch (error) {
    console.error('❌ [ProductSelectionEditor] Erro ao carregar categorias:', error)
  }

  // Initialize from element attributes
  const element = props.element
  selectionMode.value = element.getAttribute('data-selection-mode') || 'automatic'
  selectedCategory.value = element.getAttribute('data-category') || 'all'
  sortBy.value = element.getAttribute('data-sort-by') || 'name'
  productLimit.value = parseInt(element.getAttribute('data-limit')) || 8
  
  // Load initial products
 
  if (selectionMode.value === 'manual') {
    await loadProducts()
    // Load selected products from 
    const selectedIds = element.getAttribute('data-selected-products')
    if (selectedIds) {
      const ids = selectedIds.split(',')
      selectedProducts.value = availableProducts.value.filter(p => ids.includes(p.id))
    }
  } else {
    
  }
})

// Methods
const updateSelectionMode = (mode) => {
  props.element.setAttribute('data-selection-mode', mode)
  emit('data-changed')
}

const updateCategory = () => {
  props.element.setAttribute('data-category', selectedCategory.value)
  emit('data-changed')
}

const updateSortBy = () => {
  props.element.setAttribute('data-sort-by', sortBy.value)
  emit('data-changed')
}

const updateLimit = () => {
  props.element.setAttribute('data-limit', productLimit.value.toString())
  emit('data-changed')
}

const loadProducts = async () => {
  try {
    const params = {
      page: 0,
      size: 100,
      name: searchTerm.value,
      categoryId: selectedCategory.value !== 'all' ? selectedCategory.value : undefined
    }
   
    const response = await productsApi.getProductsWithVariations(params)
   
    availableProducts.value = response.data?.content || []
    
  } catch (error) {
    console.error('❌ [ProductSelectionEditor] Erro ao carregar produtos:', error)
    availableProducts.value = []
  }
}

const searchProducts = () => {
  if (selectionMode.value === 'manual') {
    loadProducts()
  }
}

const toggleProduct = (product) => {
  const index = selectedProducts.value.findIndex(p => p.id === product.id)
  if (index > -1) {
    selectedProducts.value.splice(index, 1)
  } else {
    selectedProducts.value.push(product)
  }
  updateSelectedProducts()
}

const removeProduct = (productId) => {
  const index = selectedProducts.value.findIndex(p => p.id === productId)
  if (index > -1) {
    selectedProducts.value.splice(index, 1)
    updateSelectedProducts()
  }
}

const updateSelectedProducts = () => {
  const productIds = selectedProducts.value.map(p => p.id).join(',')
  props.element.setAttribute('data-selected-products', productIds)
  emit('data-changed')
}

const isProductSelected = (productId) => {
  return selectedProducts.value.some(p => p.id === productId)
}

const formatCurrency = (value) => {
  if (typeof value !== 'number') value = parseFloat(value) || 0
  return new Intl.NumberFormat('pt-BR', { style: 'currency', currency: 'BRL' }).format(value)
}

// Watch for selection mode changes
watch(selectionMode, (newMode) => {
  if (newMode === 'manual' && availableProducts.value.length === 0) {
    loadProducts()
  }
})
</script>

<style scoped>
.product-selection-sidebar-editor {
  padding: 20px;
  max-height: 100vh;
  overflow-y: auto;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.editor-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.section-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.selection-mode-section {
  margin-bottom: 24px;
}

.automatic-settings,
.manual-settings {
  margin-bottom: 24px;
}

.form-group {
  margin-bottom: 20px;
}

.category-select,
.sort-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
}

.category-select:focus,
.sort-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.products-list {
  max-height: 300px;
  overflow-y: auto;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
}

.product-item {
  display: flex;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #f3f4f6;
  cursor: pointer;
  transition: background-color 0.2s;
}

.product-item:hover {
  background-color: #f9fafb;
}

.product-item:last-child {
  border-bottom: none;
}

.product-item input[type="checkbox"] {
  margin-right: 12px;
}

.product-image {
  width: 40px;
  height: 40px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 12px;
}

.product-info {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: #1f2937;
  margin-bottom: 4px;
}

.product-price {
  font-size: 12px;
  color: #3b82f6;
  font-weight: 600;
}

.selected-products {
  margin-top: 16px;
}

.selected-list {
  background: #f9fafb;
  border: 1px solid #e5e7eb;
  border-radius: 6px;
  max-height: 200px;
  overflow-y: auto;
}

.selected-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  border-bottom: 1px solid #e5e7eb;
}

.selected-item:last-child {
  border-bottom: none;
}

.preview-section {
  margin-top: 24px;
  padding-top: 16px;
  border-top: 1px solid #e5e7eb;
}

.preview-info {
  background: #f0f9ff;
  border: 1px solid #bae6fd;
  border-radius: 6px;
  padding: 12px;
}

.preview-info p {
  margin: 0;
  font-size: 14px;
  color: #0369a1;
}
</style> 
