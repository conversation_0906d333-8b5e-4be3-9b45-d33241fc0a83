<template>
    <div class="giftCard-editor-container">
        <IluriaHeader :title="isEditing ? t('product.editGiftCardTitle') : t('product.newGiftCardTitle')"
            :subtitle="t('product.newGiftCardSubtitle')" :showCancel="true" :cancelText="t('cancel')" :showSave="true"
            :saveText="isEditing ? t('update') : t('save')" @cancel-click="cancel" @save-click="saveGiftCard" />

        <Form ref="formRef" v-model="form" @submit.prevent="saveGiftCard" :validate-on-blur="true"
            :validate-on-value-update="true" :validate-on-submit="true">
            <div class="editor-content">
                <ViewContainer :title="t('product.basicGiftCard')" :icon="DocumentValidationIcon">
                    <div class="flex flex-col gap-5">
                        <ProductBasicData ref="productBasicDataRef" :description="false"
                            :labelName="t('product.giftCardName')" />
                        <IluriaInputText :label="t('product.description')" v-model="form.description" />
                    </div>
                </ViewContainer>
                <ViewContainer :title="t('product.denominations')" :icon="MoneyBag02Icon" iconColor="green">
                    <GiftCardDenominations v-model="form.denominations" />
                </ViewContainer>
                <ViewContainer :title="t('product.image')" :icon="Image02Icon" iconColor="orange">
                    <ProductPhotoUpload v-model="form.photos" :max-files="5" :product-id="giftCardId || form.id"
                        :is-editing="isEditing" :is-gift-card="true" @change="handlePhotoChange" />
                </ViewContainer>
                <ViewContainer :title="t('product.seoConfiguration')" :icon="DocumentValidationIcon" iconColor="gray">
                    <IluriaSeoForm :form="form" />
                </ViewContainer>
            </div>
        </Form>
    </div>
</template>

<script setup>
import { ref, computed, provide, onMounted, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from '@/services/toast.service';
import { useI18n } from 'vue-i18n'
import { Form } from '@primevue/forms'
import IluriaHeader from '../../components/iluria/IluriaHeader.vue';
import ViewContainer from '../../components/layout/ViewContainer.vue';
import ProductPhotoUpload from '../../components/products/ProductPhotoUpload.vue';
import {
    DocumentValidationIcon,
    Image02Icon,
    MoneyBag02Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { productsApi } from '@/services/product.service'
import ProductBasicData from '@/components/products/ProductBasicData.vue'
import GiftCardDenominations from '../../components/products/GiftCardDenominations.vue';
import IluriaSeoForm from '@/components/iluria/form/IluriaSeoForm.vue'
import IluriaInputText from '../../components/iluria/form/IluriaInputText.vue';

const router = useRouter()
const route = useRoute()
const { t } = useI18n()
const toast = useToast()

const isEditing = computed(() => !!route.params.id);
const giftCardId = computed(() => route.params.id || form.value.id);
const saving = ref(false)
const productBasicDataRef = ref(null)


const form = ref({
    name: '',
    description: '',
    denominations: [],
    photos: [],
    seo: {
        metaTitle: '',
        metaDescription: '',
        slug: ''
    }
});

provide('productForm', form)

const formRef = ref(null)

const saveGiftCard = async () => {
    saving.value = true;
    try {
        const productData = {
            name: form.value.name,
            description: form.value.description,
            denominations: form.value.denominations,
            photos: form.value.photos,
            metaTitle: form.value.seo.metaTitle,
            metaDescription: form.value.seo.metaDescription,
            urlSlug: form.value.seo.slug
        };
        if (!isEditing.value) {
            delete productData.photos;
        }
        const response = await saveProductData(productData);
        const savedProductId = response.data?.id;
        if (!savedProductId) {
            toast.showError(t('product.giftCardSavedFailure'));
            return;
        }
        if (!isEditing.value) {
            if (form.value.photos.length > 0) {
                await productsApi.uploadGiftCardImage(savedProductId, form.value.photos);
            }
        }
        toast.showSuccess(t('product.giftCardSavedSuccessfully'));
        router.push('/products/gift-card');
    } catch (error) {
        toast.showError(t('product.giftCardSavedFailure'));
    } finally {
        saving.value = false;
    }
};

const saveProductData = async (productData) => {
    if (isEditing.value) {
        return await productsApi.updateGiftCard(giftCardId.value, productData);
    } else {
        return await productsApi.createGiftCard(productData);
    }
};

const cancel = () => {
    router.push("/products/gift-card")
}

const loadGiftCard = async () => {
    try {
        const response = await productsApi.getGiftCardById(giftCardId.value);
        const data = response.data;
        form.value = {
            name: data.name || '',
            description: data.description || '',
            denominations: data.denominations || [],
            photos: data.photos || [],
            seo: {
                metaTitle: data.metaTitle || '',
                metaDescription: data.metaDescription || '',
                slug: data.urlSlug || ''
            }
        };
    } catch (error) {
        toast.showError(t('product.loadGiftCardFailure'));
        router.push('/products/gift-card');
    }
};

function handlePhotoChange(photos) {
    form.value.photos = photos;
}

function generateSlug(text) {
    if (!text) return '';

    let slug = text
        .toLowerCase()
        .trim()
        .normalize('NFD')
        .replace(/[\u0300-\u036f]/g, '')
        .replace(/[^a-z0-9\s-]/g, '')
        .replace(/\s+/g, '-')
        .replace(/-+/g, '-')
        .replace(/^-+$/g, '');

    return slug;
}

watch(() => form.value.name, (newName) => {
        form.value.metaTitle = newName.trim();
        const generatedSlug = generateSlug(newName.trim());
        
        form.value.seo.metaTitle = newName.trim();
        form.value.seo.slug = generatedSlug;
});

onMounted(() => {
    if (isEditing.value) {
        loadGiftCard();
    }
});

</script>

<style scoped>
.giftCard-editor-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: var(--iluria-color-background);
    min-height: 100vh;
}

.editor-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: var(--iluria-color-background);
}

.inputs-text {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}
</style>