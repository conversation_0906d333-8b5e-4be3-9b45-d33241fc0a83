/**
 * ProductGrid Tool for Editor.js
 * Creates a grid of editable product cards.
 */
export default class ProductGridTool {
  /**
   * Get default data structure
   */
  static get defaultData() {
    return {
      cardColor: '#ffffff',
      buttonColor: '#1f2937',
      titleColor: '#1f2937',
      descriptionColor: '#6b7280',
      ratingColor: '#f59e0b',
      titleFontSize: '1.125',
      descriptionFontSize: '0.875',
      layoutType: 'grid',
      gridColumns: 3,
      cardBorders: true, // true = with borders, false = floating cards
      products: []
    };
  }

  /**
   * @param {object} tool - tool properties got from editor.js
   * @param {object} tool.data - previously saved data
   * @param {object} tool.config - user config for Tool
   * @param {object} tool.api - Editor.js API
   */
  constructor({ data, api }) {
    this.api = api;
    
    // Add CSS to ensure no card animations
    if (!document.getElementById('product-grid-no-animation')) {
      const style = document.createElement('style');
      style.id = 'product-grid-no-animation';
      style.textContent = `
        .product-card {
          transition: none !important;
          transform: none !important;
        }
        .product-card:hover {
          transition: none !important;
          transform: none !important;
        }
      `;
      document.head.appendChild(style);
    }
    
    // Merge incoming data with defaults, ensuring proper types
    const defaults = ProductGridTool.defaultData;
    this.data = {};
    
    // Merge with proper type checking
    Object.keys(defaults).forEach(key => {
      if (data && data.hasOwnProperty(key)) {
        this.data[key] = data[key];
      } else {
        this.data[key] = defaults[key];
      }
    });

    this.wrapper = undefined;
    this.columnsContainer = null; // Store reference to columns container
    this.settings = [
        {
            name: 'cardColor',
            title: 'Cor do Card',
            icon: `<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10.001,2.5C6.42,2.5,3.572,4.423,2.75,7.5h14.5C16.429,4.423,13.58,2.5,10.001,2.5z M2.5,9.25v1.25c0,2.209,1.791,4,4,4s4-1.791,4-4V9.25H2.5z M17.5,9.25v1.25c0,2.209-1.791,4-4,4s-4-1.791-4-4V9.25H17.5z"/></svg>`
        },
        {
            name: 'buttonColor',
            title: 'Cor do Botão',
            icon: `<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg"><path d="M10,3.375c-3.102,0-5.625,2.523-5.625,5.625c0,3.102,2.523,5.625,5.625,5.625c3.102,0,5.625-2.523,5.625-5.625C15.625,5.898,13.102,3.375,10,3.375z M10,13.125c-2.276,0-4.125-1.849-4.125-4.125S7.724,4.875,10,4.875S14.125,6.724,14.125,9S12.276,13.125,10,13.125z"/></svg>`
        }
    ];
  }

  /**
   * Tool's icon and title for displaying in the Toolbox
   */
  static get toolbox() {
    return {
      title: 'Grid de Produtos',
      icon: '<svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"><rect x="3" y="3" width="7" height="7"></rect><rect x="14" y="3" width="7" height="7"></rect><rect x="3" y="14" width="7" height="7"></rect><rect x="14" y="14" width="7" height="7"></rect></svg>'
    };
  }

  /**
   * Creates the Tool's view
   * @returns {HTMLElement}
   */
  render() {
    this.wrapper = document.createElement('div');
    this.wrapper.classList.add('product-grid-tool');

    // Ensure wrapper doesn't interfere with grid layout
    this.wrapper.style.cssText = `
      width: 100%;
      box-sizing: border-box;
      display: block;
    `;

    if (this.data.products.length === 0) {
      // Show product selection button when no products
      const selectButton = this._createProductSelectButton();
      this.wrapper.appendChild(selectButton);
    } else {
      // Show products based on layout type
      if (this.data.layoutType === 'slider') {
        const sliderContainer = this._createSliderLayout();
        this.wrapper.appendChild(sliderContainer);
      } else {
        // Default grid layout
        const gridContainer = this._createGridLayout();
        this.wrapper.appendChild(gridContainer);
      }
    }

    return this.wrapper;
  }
  
  /**
   * Helper to create a single product card
   * @param {object} product - product data
   * @returns {HTMLElement}
   */
  _createProductCard(product) {
      const card = document.createElement('div');
      card.classList.add('product-card');

      // Base card styling with conditional borders
      const hasBorders = this.data.cardBorders !== false; // Default to true if undefined
      card.style.cssText = `
        background-color: ${this.data.cardColor};
        ${hasBorders ? 'border: 1px solid var(--iluria-color-border, #e5e7eb);' : 'border: none;'}
        ${hasBorders ? 'border-radius: 8px;' : 'border-radius: 0;'}
        overflow: hidden;
        ${hasBorders ? 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);' : 'box-shadow: none;'}
        display: flex;
        flex-direction: column;
        height: 100%;
        min-height: 400px;
        box-sizing: border-box;
        position: relative;
        transition: none !important;
        transform: none !important;
      `;

      // Use data attributes to store non-editable data
      card.dataset.imageUrl = product.imageUrl;
      card.dataset.buttonUrl = product.buttonUrl;
      card.dataset.rating = product.rating;

      // Create stars with HTML entities for better compatibility
      const filledStars = '&#9733;'.repeat(product.rating); // ★
      const emptyStars = '&#9734;'.repeat(5 - product.rating); // ☆
      const starsHTML = filledStars + emptyStars;

      card.innerHTML = `
        <div class="product-image" style="
          width: 100%;
          height: 150px;
          overflow: hidden;
        ">
          <img src="${product.imageUrl}" alt="${product.title}" style="
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
          " />
        </div>
        <div class="product-info" style="
          padding: 1rem;
          display: flex;
          flex-direction: column;
          flex-grow: 1;
          justify-content: space-between;
        ">
          <h3 class="product-title" style="
            font-size: ${this.data.titleFontSize}rem;
            color: ${this.data.titleColor};
            font-weight: 600;
            margin: 0 0 0.5rem 0;
            line-height: 1.4;
          ">${product.title}</h3>
          <p class="product-description" style="
            font-size: ${this.data.descriptionFontSize}rem;
            color: ${this.data.descriptionColor};
            margin: 0 0 1rem 0;
            min-height: 40px;
            line-height: 1.5;
            flex-grow: 1;
          ">${product.description}</p>
          <div class="product-rating" style="
            color: ${this.data.ratingColor};
            margin-bottom: 1rem;
            font-size: 1.2rem;
            line-height: 1;
          ">${starsHTML}</div>
          <a href="${product.buttonUrl}" class="product-button" style="
            display: block;
            width: 100%;
            padding: 0.75rem 1rem;
            background-color: ${this.data.buttonColor};
            color: white;
            text-align: center;
            border-radius: 6px;
            text-decoration: none;
            transition: background-color 0.2s;
            box-sizing: border-box;
            margin-top: auto;
          ">${product.buttonText}</a>
        </div>
      `;

      return card;
  }
  
  /**
   * Applies intelligent hover behavior to Editor.js menus
   */
  _applyIntelligentMenuHover() {
    // Use MutationObserver to detect menu creation
    const observer = new MutationObserver((mutations) => {
      mutations.forEach((mutation) => {
        mutation.addedNodes.forEach((node) => {
          if (node.nodeType === Node.ELEMENT_NODE) {
            // Check if this is a settings menu
            if (node.classList && (node.classList.contains('ce-settings') || 
                                 node.classList.contains('ce-toolbar__settings'))) {
              this._interceptMenuEvents(node);
            }
            
            // Check for nested menus
            const nestedMenus = node.querySelectorAll ? node.querySelectorAll('.ce-settings, .ce-toolbar__settings') : [];
            nestedMenus.forEach(menu => this._interceptMenuEvents(menu));
          }
        });
      });
    });
    
    // Start observing
    observer.observe(document.body, {
      childList: true,
      subtree: true
    });
    
    // Also try to find existing menus
    const existingMenus = document.querySelectorAll('.ce-settings, .ce-toolbar__settings');
    existingMenus.forEach(menu => this._interceptMenuEvents(menu));
    
    // Store observer for cleanup
    this._menuObserver = observer;
  }
  
  /**
   * Intercepts and modifies menu hover behavior - Simplified approach
   */
  _interceptMenuEvents(menuContainer) {
    // Add powerful CSS override that works
    if (!document.getElementById('intelligent-menu-styles')) {
      const style = document.createElement('style');
      style.id = 'intelligent-menu-styles';
      style.textContent = `
        /* Force override of Editor.js hover behavior */
        .ce-settings__button:hover .ce-settings__button-children {
          display: none !important;
          opacity: 0 !important;
          visibility: hidden !important;
          pointer-events: none !important;
        }
        
        .ce-settings__button.show-submenu:hover .ce-settings__button-children,
        .ce-settings__button.show-submenu .ce-settings__button-children {
          display: block !important;
          opacity: 1 !important;
          visibility: visible !important;
          pointer-events: auto !important;
        }
        
        /* Bridge area to prevent menu closing */
        .ce-settings__button::after {
          content: '';
          position: absolute;
          top: 100%;
          left: 0;
          right: 0;
          height: 12px;
          background: transparent;
          pointer-events: auto;
          z-index: 999;
        }
        
        .ce-settings__button-children::before {
          content: '';
          position: absolute;
          top: -12px;
          left: 0;
          right: 0;
          height: 12px;
          background: transparent;
          pointer-events: auto;
        }
      `;
      document.head.appendChild(style);
    }
    
    // Process all menu buttons
    const processMenuButtons = () => {
      const menuButtons = document.querySelectorAll('.ce-settings__button');
      
      menuButtons.forEach(button => {
        if (button.hasAttribute('data-smart-hover')) return;
        button.setAttribute('data-smart-hover', 'true');
        
        let hoverTimer;
        let closeTimer;
        let mouseHistory = [];
        
        // Enhanced mouse tracking
        const trackMouse = (e) => {
          const now = Date.now();
          mouseHistory.push({ x: e.clientX, y: e.clientY, time: now });
          if (mouseHistory.length > 5) mouseHistory.shift();
        };
        
        // Smart delay calculation
        const getSmartDelay = () => {
          if (mouseHistory.length < 2) return 200;
          
          const recent = mouseHistory.slice(-2);
          const dx = recent[1].x - recent[0].x;
          const dy = recent[1].y - recent[0].y;
          const dt = recent[1].time - recent[0].time;
          
          const speed = Math.sqrt(dx*dx + dy*dy) / dt;
          const angle = Math.atan2(Math.abs(dy), Math.abs(dx)) * 180 / Math.PI;
          
          // Base delay
          let delay = 150;
          
          // Speed-based adjustment
          if (speed > 1.0) delay = 400;        // Very fast
          else if (speed > 0.5) delay = 300;   // Fast
          else if (speed > 0.2) delay = 200;   // Medium
          else delay = 100;                    // Slow/hover
          
          // Diagonal movement penalty
          if (angle > 25 && angle < 65) delay += 200;
          
          return delay;
        };
        
        // Mouse enter with smart delay
        button.addEventListener('mouseenter', (e) => {
          trackMouse(e);
          
          clearTimeout(hoverTimer);
          clearTimeout(closeTimer);
          
          const delay = getSmartDelay();
          
          hoverTimer = setTimeout(() => {
            button.classList.add('show-submenu');
          }, delay);
        });
        
        // Mouse leave with bridge detection
        button.addEventListener('mouseleave', (e) => {
          clearTimeout(hoverTimer);
          
          const submenu = button.querySelector('.ce-settings__button-children');
          if (submenu) {
            const rect = submenu.getBoundingClientRect();
            const bridge = 15; // Bridge area
            
            const movingToSubmenu = e.clientX >= rect.left - bridge && 
                                  e.clientX <= rect.right + bridge &&
                                  e.clientY >= rect.top - bridge && 
                                  e.clientY <= rect.bottom + bridge;
            
            const closeDelay = movingToSubmenu ? 300 : 100;
            
            closeTimer = setTimeout(() => {
              button.classList.remove('show-submenu');
            }, closeDelay);
          } else {
            button.classList.remove('show-submenu');
          }
        });
        
        // Track mouse movement
        button.addEventListener('mousemove', trackMouse);
        
        // Submenu event handlers
        const submenu = button.querySelector('.ce-settings__button-children');
        if (submenu) {
          submenu.addEventListener('mouseenter', () => {
            clearTimeout(closeTimer);
            button.classList.add('show-submenu');
          });
          
          submenu.addEventListener('mouseleave', () => {
            closeTimer = setTimeout(() => {
              button.classList.remove('show-submenu');
            }, 100);
          });
        }
      });
    };
    
    processMenuButtons();
    
    // Re-process if new buttons are added
    setTimeout(processMenuButtons, 100);
    setTimeout(processMenuButtons, 500);
  }

  /**
   * Renders the settings panel using Editor.js native menu system with children
   * @returns {Array} Menu configuration with submenus
   */
  renderSettings() {
    // Apply intelligent menu hover behavior
    setTimeout(() => {
      this._applyIntelligentMenuHover();
    }, 200);
    return [
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <path d="M9 3v18"/>
        </svg>`,
        title: 'Produtos',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <line x1="12" y1="8" x2="12" y2="16"/>
                <line x1="8" y1="12" x2="16" y2="12"/>
              </svg>`,
              title: 'Selecionar Produtos',
              type: 'html',
              element: this._createProductSelectorButton()
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="7" height="7"/>
          <rect x="14" y="3" width="7" height="7"/>
          <rect x="3" y="14" width="7" height="7"/>
          <rect x="14" y="14" width="7" height="7"/>
        </svg>`,
        title: 'Layout',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2" fill="none"/>
              </svg>`,
              title: 'Bordas dos Cards',
              type: 'html',
              element: this._createBordersToggle()
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="7" height="7"/>
                <rect x="14" y="3" width="7" height="7"/>
                <rect x="3" y="14" width="7" height="7"/>
                <rect x="14" y="14" width="7" height="7"/>
              </svg>`,
              title: 'Tipo de Layout',
              type: 'html',
              element: this._createLayoutSelector()
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M3 3h18v18H3zM9 9h6v6H9z"/>
              </svg>`,
              title: 'Colunas do Grid',
              type: 'html',
              element: this._createColumnsSelector()
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>`,
        title: 'Cores',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              </svg>`,
              title: 'Cor do Card',
              type: 'html',
              element: this._createColorPicker('cardColor', this.data.cardColor, 'Cor do Card')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
                <circle cx="12" cy="5" r="2"/>
              </svg>`,
              title: 'Cor do Botão',
              type: 'html',
              element: this._createColorPicker('buttonColor', this.data.buttonColor, 'Cor do Botão')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 6h16M4 12h16M4 18h16"/>
              </svg>`,
              title: 'Cor do Título',
              type: 'html',
              element: this._createColorPicker('titleColor', this.data.titleColor, 'Cor do Título')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
                <polyline points="10,9 9,9 8,9"/>
              </svg>`,
              title: 'Cor da Descrição',
              type: 'html',
              element: this._createColorPicker('descriptionColor', this.data.descriptionColor, 'Cor da Descrição')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
              </svg>`,
              title: 'Cor das Estrelas',
              type: 'html',
              element: this._createColorPicker('ratingColor', this.data.ratingColor, 'Cor das Estrelas')
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
        </svg>`,
        title: 'Fontes',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
              </svg>`,
              title: 'Tamanho do Título',
              type: 'html',
              element: this._createRangeSlider('titleFontSize', this.data.titleFontSize, 'Tamanho do Título', 0.8, 2, 0.1, 'rem')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
              </svg>`,
              title: 'Tamanho da Descrição',
              type: 'html',
              element: this._createRangeSlider('descriptionFontSize', this.data.descriptionFontSize, 'Tamanho da Descrição', 0.6, 1.5, 0.1, 'rem')
            }
          ]
        }
      }
    ];
  }
  
  /**
   * Creates a color picker element for the menu
   * @param {string} property - The property name to update
   * @param {string} currentValue - Current color value
   * @param {string} label - Label for the color picker
   * @returns {HTMLElement}
   */
  _createColorPicker(property, currentValue, label) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border: 1px solid var(--iluria-color-border, #e5e7eb);
      border-radius: 4px;
      transition: all 0.2s ease;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = label;
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `;

    const input = document.createElement('input');
    input.type = 'color';
    input.value = currentValue;
    input.style.cssText = `
      width: 32px;
      height: 24px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      cursor: pointer;
      background: var(--iluria-color-surface, #ffffff);
      flex-shrink: 0;
      transition: all 0.2s ease;
    `;

    // Add hover effects
    input.addEventListener('mouseenter', () => {
      input.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
      input.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.1)';
    });

    input.addEventListener('mouseleave', () => {
      input.style.borderColor = 'var(--iluria-color-border, #d1d5db)';
      input.style.boxShadow = 'none';
    });

    input.addEventListener('focus', () => {
      input.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
      input.style.boxShadow = '0 0 0 2px rgba(59, 130, 246, 0.1)';
    });

    input.addEventListener('blur', () => {
      input.style.borderColor = 'var(--iluria-color-border, #d1d5db)';
      input.style.boxShadow = 'none';
    });

    input.addEventListener('input', (event) => {
      this.data[property] = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(input);
    return container;
  }

  /**
   * Creates a range slider element for the menu
   * @param {string} property - The property name to update
   * @param {number} currentValue - Current value
   * @param {string} label - Label for the slider
   * @param {number} min - Minimum value
   * @param {number} max - Maximum value
   * @param {number} step - Step value
   * @param {string} unit - Unit to display
   * @returns {HTMLElement}
   */
  _createRangeSlider(property, currentValue, label, min, max, step, unit) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border: 1px solid var(--iluria-color-border, #e5e7eb);
      border-radius: 4px;
      transition: all 0.2s ease;
    `;

    const labelEl = document.createElement('div');
    labelEl.style.cssText = `
      display: flex;
      justify-content: space-between;
      align-items: center;
      margin-bottom: 6px;
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
    `;

    const labelText = document.createElement('span');
    labelText.textContent = label;
    labelText.style.cssText = `
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
      flex: 1;
      margin-right: 8px;
      color: var(--iluria-color-text-primary, #374151);
    `;

    const valueDisplay = document.createElement('span');
    valueDisplay.textContent = `${currentValue}${unit}`;
    valueDisplay.style.cssText = `
      font-size: 11px;
      color: var(--iluria-color-text-secondary, #6b7280);
      font-weight: 500;
      flex-shrink: 0;
      min-width: 40px;
      text-align: right;
    `;

    labelEl.appendChild(labelText);
    labelEl.appendChild(valueDisplay);

    const input = document.createElement('input');
    input.type = 'range';
    input.min = min;
    input.max = max;
    input.step = step;
    input.value = currentValue;
    input.className = 'product-grid-range-slider';
    input.style.cssText = `
      width: 100%;
      height: 4px;
      border-radius: 2px;
      background: var(--iluria-color-border, #e5e7eb);
      outline: none;
      cursor: pointer;
      margin: 0;
      accent-color: var(--iluria-color-primary, #3b82f6);
      -webkit-appearance: none;
      appearance: none;
      transition: all 0.2s ease;
    `;

    // Adicionar estilos CSS customizados para o slider
    if (!document.getElementById('product-grid-slider-styles')) {
      const style = document.createElement('style');
      style.id = 'product-grid-slider-styles';
      style.textContent = `
        .product-grid-range-slider::-webkit-slider-track {
          height: 4px;
          border-radius: 2px;
          background: var(--iluria-color-border, #e5e7eb);
          border: none;
        }
        .product-grid-range-slider::-webkit-slider-thumb {
          -webkit-appearance: none;
          appearance: none;
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: var(--iluria-color-primary, #3b82f6);
          cursor: pointer;
          border: 2px solid var(--iluria-color-surface, #ffffff);
          box-shadow: var(--iluria-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
        }
        .product-grid-range-slider::-webkit-slider-thumb:hover {
          background: var(--iluria-color-primary-hover, #2563eb);
          transform: scale(1.1);
          transition: all 0.2s ease;
          box-shadow: var(--iluria-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.15));
        }
        .product-grid-range-slider::-webkit-slider-thumb:active {
          transform: scale(0.95);
          box-shadow: var(--iluria-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
        }
        .product-grid-range-slider::-moz-range-track {
          height: 4px;
          border-radius: 2px;
          background: var(--iluria-color-border, #e5e7eb);
          border: none;
        }
        .product-grid-range-slider::-moz-range-thumb {
          height: 16px;
          width: 16px;
          border-radius: 50%;
          background: var(--iluria-color-primary, #3b82f6);
          cursor: pointer;
          border: 2px solid var(--iluria-color-surface, #ffffff);
          box-shadow: var(--iluria-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
        }
        .product-grid-range-slider::-moz-range-thumb:hover {
          background: var(--iluria-color-primary-hover, #2563eb);
          transform: scale(1.1);
          transition: all 0.2s ease;
          box-shadow: var(--iluria-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.15));
        }
        .product-grid-range-slider::-moz-range-thumb:active {
          transform: scale(0.95);
          box-shadow: var(--iluria-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
        }
      `;
      document.head.appendChild(style);
    }

    input.addEventListener('input', (event) => {
      this.data[property] = parseFloat(event.target.value);
      valueDisplay.textContent = `${event.target.value}${unit}`;
      this._updateView();
    });

    // Add focus states
    input.addEventListener('focus', () => {
      input.style.outline = '2px solid var(--iluria-color-primary, #3b82f6)';
      input.style.outlineOffset = '2px';
    });

    input.addEventListener('blur', () => {
      input.style.outline = 'none';
    });

    container.appendChild(labelEl);
    container.appendChild(input);
    return container;
  }

  /**
   * Creates a grid layout container
   * @returns {HTMLElement}
   */
  _createGridLayout() {
    const container = document.createElement('div');
    container.className = 'product-grid-container';

    // Use CSS Grid for uniform sizing with configurable columns
    const minCardWidth = Math.max(200, Math.floor(800 / this.data.gridColumns) - 20);
    container.style.cssText = `
      display: grid !important;
      grid-template-columns: repeat(${this.data.gridColumns}, 1fr) !important;
      gap: 20px !important;
      padding: 20px 0;
      width: 100% !important;
      box-sizing: border-box;
    `;

    this.data.products.forEach((product, index) => {
      const productCard = this._createProductCard(product);

      // Grid item styling - let CSS Grid handle sizing
      productCard.style.cssText += `
        width: 100% !important;
        height: 100% !important;
        min-height: 400px !important;
      `;

  
      container.appendChild(productCard);
    });

  

    return container;
  }

  /**
   * Creates a slider layout container
   * @returns {HTMLElement}
   */
  _createSliderLayout() {
    const container = document.createElement('div');
    container.style.cssText = `
      position: relative;
      padding: 20px 40px;
      overflow: hidden;
      width: 100%;
      box-sizing: border-box;
      min-height: 400px;
    `;

    // Slider wrapper
    const sliderWrapper = document.createElement('div');
    sliderWrapper.style.cssText = `
      display: flex !important;
      transition: transform 0.3s ease;
      gap: 20px !important;
      width: fit-content !important;
      align-items: flex-start;
    `;

    this.data.products.forEach((product, index) => {
      const productCard = this._createProductCard(product);
      productCard.style.cssText += `
        flex: 0 0 280px !important;
        width: 280px !important;
        max-width: 280px !important;
        min-width: 280px !important;
      `;
      sliderWrapper.appendChild(productCard);
    });


    // Navigation arrows
    const prevButton = this._createSliderButton('prev');
    const nextButton = this._createSliderButton('next');

    // Add navigation functionality
    let currentIndex = 0;
    const itemWidth = 280; // Card width
    const gap = 20; // Gap between items
    const totalItemWidth = itemWidth + gap;

    const updateSlider = () => {
      // Get the actual container width, fallback to a reasonable default
      const containerWidth = container.offsetWidth || container.clientWidth || 800;

      // Show at least 1 item, more if space allows, but don't exceed 3
      const minItems = 1;
      const maxItems = 3;
      const availableWidth = containerWidth - 80; // 80px for padding and buttons
      const possibleItems = Math.floor(availableWidth / totalItemWidth);
      const itemsPerView = Math.max(minItems, Math.min(maxItems, possibleItems));
      const maxIndex = Math.max(0, this.data.products.length - itemsPerView);


      // Ensure currentIndex doesn't exceed maxIndex
      if (currentIndex > maxIndex) {
        currentIndex = maxIndex;
      }

      const translateX = -(currentIndex * totalItemWidth);
      sliderWrapper.style.transform = `translateX(${translateX}px)`;

      prevButton.style.opacity = currentIndex === 0 ? '0.5' : '1';
      prevButton.style.pointerEvents = currentIndex === 0 ? 'none' : 'auto';
      nextButton.style.opacity = currentIndex >= maxIndex ? '0.5' : '1';
      nextButton.style.pointerEvents = currentIndex >= maxIndex ? 'none' : 'auto';
    };

    prevButton.addEventListener('click', () => {
      if (currentIndex > 0) {
        currentIndex--;
        updateSlider();
      }
    });

    nextButton.addEventListener('click', () => {
      const containerWidth = container.offsetWidth || container.clientWidth || 800;
      const minItems = 1;
      const maxItems = 3;
      const availableWidth = containerWidth - 80;
      const possibleItems = Math.floor(availableWidth / totalItemWidth);
      const itemsPerView = Math.max(minItems, Math.min(maxItems, possibleItems));
      const maxIndex = Math.max(0, this.data.products.length - itemsPerView);

      if (currentIndex < maxIndex) {
        currentIndex++;
        updateSlider();
      }
    });

    // Update on window resize
    const resizeHandler = () => {
      updateSlider();
    };
    window.addEventListener('resize', resizeHandler);

    // Store cleanup function for later removal
    container._cleanup = () => {
      window.removeEventListener('resize', resizeHandler);
    };

    container.appendChild(prevButton);
    container.appendChild(sliderWrapper);
    container.appendChild(nextButton);

    // Initial update with longer delay to ensure container is rendered
    setTimeout(() => {
      updateSlider();
      // Force a second update after DOM is fully rendered
      setTimeout(updateSlider, 100);
    }, 200);

    return container;
  }

  /**
   * Creates slider navigation buttons
   * @param {string} direction - 'prev' or 'next'
   * @returns {HTMLElement}
   */
  _createSliderButton(direction) {
    const button = document.createElement('button');
    button.style.cssText = `
      position: absolute;
      top: 50%;
      transform: translateY(-50%);
      ${direction === 'prev' ? 'left: 10px;' : 'right: 10px;'}
      width: 40px;
      height: 40px;
      border-radius: 50%;
      border: 1px solid var(--iluria-color-border, #e5e7eb);
      background-color: var(--iluria-color-surface, #ffffff);
      color: var(--iluria-color-text-primary, #374151);
      box-shadow: var(--iluria-shadow-md, 0 2px 8px rgba(0, 0, 0, 0.15));
      cursor: pointer;
      display: flex;
      align-items: center;
      justify-content: center;
      z-index: 10;
      transition: all 0.2s ease;
    `;

    const icon = direction === 'prev'
      ? `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
           <polyline points="15,18 9,12 15,6"></polyline>
         </svg>`
      : `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
           <polyline points="9,18 15,12 9,6"></polyline>
         </svg>`;

    button.innerHTML = icon;

    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = 'var(--iluria-color-surface-hover, #f3f4f6)';
      button.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
      button.style.color = 'var(--iluria-color-primary, #3b82f6)';
      button.style.transform = 'translateY(-50%) scale(1.1)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'var(--iluria-color-surface, #ffffff)';
      button.style.borderColor = 'var(--iluria-color-border, #e5e7eb)';
      button.style.color = 'var(--iluria-color-text-primary, #374151)';
      button.style.transform = 'translateY(-50%) scale(1)';
    });

    return button;
  }

  /**
   * Creates a columns selector for the grid layout
   * @returns {HTMLElement}
   */
  _createColumnsSelector() {
    const container = document.createElement('div');
    
    // Store reference to container for dynamic updates
    this.columnsContainer = container;
    
    container.style.cssText = `
      padding: 8px 12px;
      display: ${this.data.layoutType === 'slider' ? 'none' : 'flex'};
      flex-direction: column;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      background-color: transparent;
      border: none;
    `;

    const label = document.createElement('label');
    label.style.cssText = `
      font-size: 12px;
      color: var(--iluria-color-text-secondary, #6b7280);
      margin-bottom: 4px;
    `;
    label.textContent = 'Número de Colunas';

    const buttonGroup = document.createElement('div');
    buttonGroup.style.cssText = `
      display: flex;
      gap: 4px;
      flex-wrap: wrap;
    `;

    // Create buttons for 1-4 columns
    const columnButtons = [];
    for (let i = 1; i <= 4; i++) {
      const button = this._createColumnButton(i, this.data.gridColumns === i);
      button.addEventListener('click', () => {
        this.data.gridColumns = i;
        this._updateColumnButtons(columnButtons, i);
        this._updateView();
      });
      columnButtons.push(button);
      buttonGroup.appendChild(button);
    }

    container.appendChild(label);
    container.appendChild(buttonGroup);

    return container;
  }

  /**
   * Creates a column selection button
   * @param {number} columns - Number of columns
   * @param {boolean} active - Whether button is active
   * @returns {HTMLElement}
   */
  _createColumnButton(columns, active) {
    const button = document.createElement('button');
    button.style.cssText = `
      flex: 1;
      min-width: 32px;
      padding: 6px 8px;
      border: 2px solid ${active ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-border, #e5e7eb)'};
      border-radius: 6px;
      background: ${active ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)'};
      color: ${active ? 'var(--iluria-color-primary-contrast, #ffffff)' : 'var(--iluria-color-text, #1f2937)'};
      font-size: 12px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      outline: none;
    `;
    button.textContent = columns.toString();

    return button;
  }

  /**
   * Updates column button states
   * @param {HTMLElement[]} buttons - Array of column buttons
   * @param {number} activeColumns - The active number of columns
   */
  _updateColumnButtons(buttons, activeColumns) {
    buttons.forEach((button, index) => {
      const columns = index + 1;
      const isActive = columns === activeColumns;

      button.style.borderColor = isActive ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-border, #e5e7eb)';
      button.style.backgroundColor = isActive ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)';
      button.style.color = isActive ? 'var(--iluria-color-primary-contrast, #ffffff)' : 'var(--iluria-color-text, #1f2937)';
    });
  }

  /**
   * Creates a layout selector for the settings menu
   * @returns {HTMLElement}
   */
  _createLayoutSelector() {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: transparent;
      border: none;
      border-radius: 4px;
      transition: all 0.2s ease;
    `;

    const label = document.createElement('div');
    label.style.cssText = `
      font-size: 12px;
      color: var(--iluria-color-text-secondary, #6b7280);
      margin-bottom: 4px;
    `;
    label.textContent = 'Tipo de Layout';

    const buttonGroup = document.createElement('div');
    buttonGroup.style.cssText = `
      display: flex;
      gap: 4px;
    `;

    const gridButton = this._createLayoutButton('grid', 'Grid', this.data.layoutType === 'grid');
    const sliderButton = this._createLayoutButton('slider', 'Slider', this.data.layoutType === 'slider');

    gridButton.addEventListener('click', () => {
      this.data.layoutType = 'grid';
      this._updateLayoutButtons(gridButton, sliderButton);
      this._updateView();
      this._refreshSettings();
    });

    sliderButton.addEventListener('click', () => {
      this.data.layoutType = 'slider';
      this._updateLayoutButtons(sliderButton, gridButton);
      this._updateView();
      this._refreshSettings();
    });

    buttonGroup.appendChild(gridButton);
    buttonGroup.appendChild(sliderButton);

    container.appendChild(label);
    container.appendChild(buttonGroup);

    return container;
  }

  /**
   * Creates a layout selection button
   * @param {string} type - Layout type
   * @param {string} label - Button label
   * @param {boolean} active - Whether button is active
   * @returns {HTMLElement}
   */
  _createLayoutButton(type, label, active) {
    const button = document.createElement('button');
    button.style.cssText = `
      flex: 1;
      padding: 8px 12px;
      border: 2px solid ${active ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-border, #e5e7eb)'};
      background-color: ${active ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)'};
      color: ${active ? 'var(--iluria-color-primary-contrast, #ffffff)' : 'var(--iluria-color-text-secondary, #6b7280)'};
      border-radius: 6px;
      font-size: 12px;
      cursor: pointer;
      transition: all 0.2s ease;
      font-weight: ${active ? '600' : '400'};
      text-transform: uppercase;
      letter-spacing: 0.5px;
    `;
    button.textContent = label;
    button.dataset.type = type;

    // Apply hover effects based on active state
    this._applyButtonHoverEffects(button, active);

    return button;
  }

  /**
   * Updates layout button states
   * @param {HTMLElement} activeButton - The active button
   * @param {HTMLElement} inactiveButton - The inactive button
   */
  _updateLayoutButtons(activeButton, inactiveButton) {
    // Active button - usando variáveis do tema
    activeButton.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
    activeButton.style.borderWidth = '2px';
    activeButton.style.backgroundColor = 'var(--iluria-color-primary, #3b82f6)';
    activeButton.style.color = 'var(--iluria-color-primary-contrast, #ffffff)';
    activeButton.style.fontWeight = '600';

    // Inactive button - usando variáveis do tema
    inactiveButton.style.borderColor = 'var(--iluria-color-border, #e5e7eb)';
    inactiveButton.style.borderWidth = '2px';
    inactiveButton.style.backgroundColor = 'var(--iluria-color-surface, #ffffff)';
    inactiveButton.style.color = 'var(--iluria-color-text-secondary, #6b7280)';
    inactiveButton.style.fontWeight = '400';

    // Re-apply hover effects for new state
    this._applyButtonHoverEffects(activeButton, true);
    this._applyButtonHoverEffects(inactiveButton, false);
  }

  /**
   * Apply hover effects to layout buttons
   * @param {HTMLElement} button - The button element
   * @param {boolean} isActive - Whether the button is active
   */
  _applyButtonHoverEffects(button, isActive) {
    // Remove existing listeners
    button.removeEventListener('mouseenter', button._hoverEnter);
    button.removeEventListener('mouseleave', button._hoverLeave);

    // Create new listeners
    button._hoverEnter = () => {
      if (!isActive) {
        button.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
        button.style.backgroundColor = 'var(--iluria-color-primary-soft, #eff6ff)';
        button.style.color = 'var(--iluria-color-primary, #3b82f6)';
      } else {
        button.style.backgroundColor = 'var(--iluria-color-primary-hover, #2563eb)';
      }
    };

    button._hoverLeave = () => {
      if (!isActive) {
        button.style.borderColor = 'var(--iluria-color-border, #e5e7eb)';
        button.style.backgroundColor = 'var(--iluria-color-surface, #ffffff)';
        button.style.color = 'var(--iluria-color-text-secondary, #6b7280)';
      } else {
        button.style.backgroundColor = 'var(--iluria-color-primary, #3b82f6)';
      }
    };

    // Add new listeners
    button.addEventListener('mouseenter', button._hoverEnter);
    button.addEventListener('mouseleave', button._hoverLeave);
  }

  /**
   * Refresh the settings menu to update visibility of layout-dependent options
   */
  _refreshSettings() {
    // Update visibility of columns container
    if (this.columnsContainer) {
      this.columnsContainer.style.display = this.data.layoutType === 'slider' ? 'none' : 'flex';
    }
  }

  /**
   * Creates a toggle for card borders
   * @returns {HTMLElement}
   */
  _createBordersToggle() {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      background-color: transparent;
      border: none;
      border-radius: 4px;
      cursor: pointer;
      transition: background-color 0.2s ease;
    `;

    const checkbox = document.createElement('input');
    checkbox.type = 'checkbox';
    checkbox.checked = this.data.cardBorders !== false; // Default to true
    checkbox.style.cssText = `
      width: 16px;
      height: 16px;
      margin: 0;
      cursor: pointer;
      accent-color: var(--iluria-color-primary, #22c55e);
    `;
    
    // Adicionar estilos CSS customizados para garantir que funcione em todos os temas
    if (!document.getElementById('borders-checkbox-styles')) {
      const style = document.createElement('style');
      style.id = 'borders-checkbox-styles';
      style.textContent = `
        input[type="checkbox"] {
          accent-color: var(--iluria-color-primary, #22c55e) !important;
        }
        
        /* Fallback para navegadores que não suportam accent-color */
        input[type="checkbox"]:checked {
          background-color: var(--iluria-color-primary, #22c55e) !important;
          border-color: var(--iluria-color-primary, #22c55e) !important;
        }
        
        /* Tema escuro */
        .dark input[type="checkbox"]:checked {
          background-color: var(--iluria-color-primary, #22c55e) !important;
          border-color: var(--iluria-color-primary, #22c55e) !important;
        }
        
        /* Tema claro */
        .light input[type="checkbox"]:checked,
        :root input[type="checkbox"]:checked {
          background-color: var(--iluria-color-primary, #22c55e) !important;
          border-color: var(--iluria-color-primary, #22c55e) !important;
        }
      `;
      document.head.appendChild(style);
    }

    const label = document.createElement('label');
    label.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      cursor: pointer;
      user-select: none;
      flex: 1;
    `;
    label.textContent = 'Bordas dos Cards';

    // Add hover effect to container
    container.addEventListener('mouseenter', () => {
      container.style.backgroundColor = 'var(--iluria-color-surface, #f9fafb)';
    });

    container.addEventListener('mouseleave', () => {
      container.style.backgroundColor = 'transparent';
    });

    // Handle checkbox change
    const handleChange = () => {
      this.data.cardBorders = checkbox.checked;
      this._updateView();
    };

    checkbox.addEventListener('change', handleChange);
    container.addEventListener('click', (e) => {
      if (e.target !== checkbox) {
        checkbox.checked = !checkbox.checked;
        handleChange();
      }
    });

    container.appendChild(checkbox);
    container.appendChild(label);

    return container;
  }


  /**
   * Creates a product selection button for when no products are selected
   * @returns {HTMLElement}
   */
  _createProductSelectButton() {
    const container = document.createElement('div');
    container.style.cssText = `
      display: flex;
      flex-direction: column;
      align-items: center;
      justify-content: center;
      padding: 60px 20px;
      border: 2px dashed var(--iluria-color-border, #d1d5db);
      border-radius: 12px;
      background-color: var(--iluria-color-background, #f9fafb);
      min-height: 200px;
      text-align: center;
    `;

    const icon = document.createElement('div');
    icon.innerHTML = `
      <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="var(--iluria-color-text-secondary, #6b7280)" stroke-width="2">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <path d="M9 3v18"/>
      </svg>
    `;
    icon.style.marginBottom = '16px';

    const title = document.createElement('h3');
    title.textContent = 'Nenhum produto selecionado';
    title.style.cssText = `
      margin: 0 0 8px 0;
      color: var(--iluria-color-text-primary, #374151);
      font-size: 18px;
      font-weight: 600;
    `;

    const subtitle = document.createElement('p');
    subtitle.textContent = 'Use a aba "Produtos" na barra lateral para selecionar produtos';
    subtitle.style.cssText = `
      margin: 0 0 20px 0;
      color: var(--iluria-color-text-secondary, #6b7280);
      font-size: 14px;
    `;

    const button = document.createElement('button');
    button.textContent = 'Selecionar Produtos';
    button.style.cssText = `
      background-color: var(--iluria-color-primary, #3b82f6);
      color: var(--iluria-color-primary-contrast, white);
      border: 1px solid var(--iluria-color-primary, #3b82f6);
      padding: 12px 24px;
      border-radius: 6px;
      font-size: 14px;
      font-weight: 500;
      cursor: pointer;
      transition: all 0.2s ease;
      box-shadow: var(--iluria-shadow-sm, 0 1px 2px rgba(0, 0, 0, 0.05));
    `;

    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = 'var(--iluria-color-primary-hover, #2563eb)';
      button.style.borderColor = 'var(--iluria-color-primary-hover, #2563eb)';
      button.style.transform = 'translateY(-1px)';
      button.style.boxShadow = 'var(--iluria-shadow-md, 0 4px 6px rgba(0, 0, 0, 0.1))';
    });

    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'var(--iluria-color-primary, #3b82f6)';
      button.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
      button.style.transform = 'translateY(0)';
      button.style.boxShadow = 'var(--iluria-shadow-sm, 0 1px 2px rgba(0, 0, 0, 0.05))';
    });

    button.addEventListener('click', () => {
      this._openProductSelector();
    });

    container.appendChild(icon);
    container.appendChild(title);
    container.appendChild(subtitle);
    container.appendChild(button);

    return container;
  }

  /**
   * Creates a product selector button for the settings menu
   * @returns {HTMLElement}
   */
  _createProductSelectorButton() {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border: 1px solid var(--iluria-color-border, #e5e7eb);
      border-radius: 4px;
      transition: all 0.2s ease;
    `;

    const info = document.createElement('div');
    info.style.cssText = `
      font-size: 12px;
      color: var(--iluria-color-text-secondary, #6b7280);
      margin-bottom: 4px;
    `;
    info.textContent = `${this.data.products.length} produto(s) selecionado(s)`;

    const button = document.createElement('button');
    button.textContent = this.data.products.length > 0 ? 'Alterar Produtos' : 'Selecionar Produtos';
    button.style.cssText = `
      background-color: var(--iluria-color-primary, #3b82f6);
      color: var(--iluria-color-primary-contrast, white);
      border: 1px solid var(--iluria-color-primary, #3b82f6);
      padding: 8px 12px;
      border-radius: 4px;
      font-size: 13px;
      cursor: pointer;
      width: 100%;
      transition: all 0.2s ease;
      font-weight: 500;
      box-shadow: var(--iluria-shadow-sm, 0 1px 2px rgba(0, 0, 0, 0.05));
    `;

    button.addEventListener('mouseenter', () => {
      button.style.backgroundColor = 'var(--iluria-color-primary-hover, #2563eb)';
      button.style.borderColor = 'var(--iluria-color-primary-hover, #2563eb)';
    });

    button.addEventListener('mouseleave', () => {
      button.style.backgroundColor = 'var(--iluria-color-primary, #3b82f6)';
      button.style.borderColor = 'var(--iluria-color-primary, #3b82f6)';
    });

    button.addEventListener('click', () => {
      this._openProductSelector();
    });

    container.appendChild(info);
    container.appendChild(button);
    return container;
  }

  /**
   * Opens the product selector modal
   */
  _openProductSelector() {
    // Create and dispatch a custom event to open the product selector
    const event = new CustomEvent('openProductSelector', {
      detail: {
        onSelect: (selectedProducts) => {
          this._handleProductSelection(selectedProducts);
        }
      }
    });
    document.dispatchEvent(event);
  }

  /**
   * Handles the product selection from the modal
   * @param {Array} selectedProducts - Array of selected products
   */
  _handleProductSelection(selectedProducts) {
    // Convert selected products to the format expected by the grid
    this.data.products = selectedProducts.map(product => {
      // Function to get product image URL from photos array
      const getProductImageUrl = (product) => {
        // Check for variation photos first
        if (product.variations && product.variations.length > 0) {
          const variation = product.variations.find(v => v.photos && v.photos.length > 0);
          if (variation) {
            const photo = variation.photos.find(p => p.position === 0) || variation.photos[0];
            if (photo) {
              // Handle both URL string and object with url property
              if (typeof photo === 'string') {
                return photo;
              } else if (photo.url) {
                return photo.url;
              }
            }
          }
        }
        
        // Check for product photos
        if (product.photos && product.photos.length > 0) {
          const photo = product.photos.find(p => p.position === 0) || product.photos[0];
          if (photo) {
            // Handle both URL string and object with url property
            if (typeof photo === 'string') {
              return photo;
            } else if (photo.url) {
              return photo.url;
            }
          }
        }
        
        // Fallback to imageUrl property or placeholder
        return product.imageUrl || 'https://d3a1v57rabk2hm.cloudfront.net/outerbanksbox/betterman_mobile-copy-51/images/product_placeholder.jpg?ts=**********&host=www.outerbanksbox.com';
      };

      return {
        title: product.name || product.productName,
        description: product.description || 'Produto de qualidade premium',
        imageUrl: getProductImageUrl(product),
        rating: 5, // Default rating
        buttonText: 'Ver Detalhes',
        buttonUrl: `/products/${product.id}`,
        price: product.price,
        originalPrice: product.originalPrice,
        id: product.id
      };
    });

    this._updateView();
  }

  /**
   * Helper to update the view when settings change.
   */
  _updateView() {
    // Cleanup any existing event listeners
    const existingSlider = this.wrapper.querySelector('.product-slider');
    if (existingSlider && existingSlider._cleanup) {
      existingSlider._cleanup();
    }

    // Re-render the entire component
    this.wrapper.innerHTML = '';

    if (this.data.products.length === 0) {
      const selectButton = this._createProductSelectButton();
      this.wrapper.appendChild(selectButton);
    } else {
      // Show products based on layout type
      if (this.data.layoutType === 'slider') {
        const sliderContainer = this._createSliderLayout();
        this.wrapper.appendChild(sliderContainer);
      } else {
        // Default grid layout
        const gridContainer = this._createGridLayout();
        this.wrapper.appendChild(gridContainer);
      }
    }

    // Apply current styling
    const cards = this.wrapper.querySelectorAll('.product-card');
    cards.forEach(card => {
      // Apply styling
      card.style.backgroundColor = this.data.cardColor;
      const button = card.querySelector('.product-button');
      if (button) {
        button.style.backgroundColor = this.data.buttonColor;
      }
      const title = card.querySelector('.product-title');
      if (title) {
        title.style.color = this.data.titleColor;
        title.style.fontSize = `${this.data.titleFontSize}rem`;
      }
      const description = card.querySelector('.product-description');
      if (description) {
        description.style.color = this.data.descriptionColor;
        description.style.fontSize = `${this.data.descriptionFontSize}rem`;
      }
      const rating = card.querySelector('.product-rating');
      if (rating) {
        rating.style.color = this.data.ratingColor;
      }
    });
  }


  /**
   * Extracts Tool's data from the view
   * @param {HTMLElement} blockContent - Tool HTML element
   * @returns {object} saved data
   */
  save(blockContent) {
    // Since products are no longer editable inline, return current data
    return {
      cardColor: this.data.cardColor,
      buttonColor: this.data.buttonColor,
      titleColor: this.data.titleColor,
      descriptionColor: this.data.descriptionColor,
      ratingColor: this.data.ratingColor,
      titleFontSize: this.data.titleFontSize,
      descriptionFontSize: this.data.descriptionFontSize,
      layoutType: this.data.layoutType,
      gridColumns: this.data.gridColumns,
      cardBorders: this.data.cardBorders,
      products: this.data.products
    };
  }

  /**
   * Converts tool data to HTML for output
   * @returns {string} HTML representation
   */
  toHTML() {
    const hasBorders = this.data.cardBorders !== false; // Default to true if undefined
    
    const productsHTML = this.data.products.map(product => {
      // Use HTML entities for stars to ensure proper rendering
      const filledStars = '&#9733;'.repeat(product.rating); // ★
      const emptyStars = '&#9734;'.repeat(5 - product.rating); // ☆
      const starsHTML = filledStars + emptyStars;

      return `
        <div class="product-card" style="
          background-color: ${this.data.cardColor};
          ${hasBorders ? 'border: 1px solid var(--iluria-color-border, #e5e7eb);' : 'border: none;'}
          ${hasBorders ? 'border-radius: 8px;' : 'border-radius: 0;'}
          overflow: hidden;
          ${hasBorders ? 'box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);' : 'box-shadow: none;'}
          display: flex;
          flex-direction: column;
          height: 100%;
          min-height: 400px;
          ${this.data.layoutType === 'slider' ? 'flex: 0 0 280px; width: 280px; min-width: 280px; max-width: 280px;' : 'width: 100%; height: 100%; min-height: 400px;'}
        ">
          <div class="product-image">
            <img src="${product.imageUrl}" alt="${product.title}" style="
              width: 100%;
              height: 150px;
              object-fit: cover;
              display: block;
            " />
          </div>
          <div class="product-info" style="
            padding: 1rem;
            display: flex;
            flex-direction: column;
            flex-grow: 1;
            justify-content: space-between;
          ">
            <h3 class="product-title" style="
              font-size: ${this.data.titleFontSize}rem;
              font-weight: 600;
              color: ${this.data.titleColor};
              margin: 0 0 0.5rem 0;
              line-height: 1.4;
            ">${product.title}</h3>
            <p class="product-description" style="
              font-size: ${this.data.descriptionFontSize}rem;
              color: ${this.data.descriptionColor};
              margin: 0 0 1rem 0;
              min-height: 40px;
              line-height: 1.5;
              flex-grow: 1;
            ">${product.description}</p>
            <div class="product-rating" style="
              color: ${this.data.ratingColor};
              margin-bottom: 1rem;
              font-size: 1.2rem;
              line-height: 1;
            ">${starsHTML}</div>
            <a href="${product.buttonUrl}" class="product-button" style="
              display: block;
              width: 100%;
              padding: 0.75rem 1rem;
              background-color: ${this.data.buttonColor};
              color: white;
              text-align: center;
              border-radius: 6px;
              text-decoration: none;
              transition: background-color 0.2s;
              box-sizing: border-box;
              margin-top: auto;
            ">${product.buttonText}</a>
          </div>
        </div>
      `;
    }).join('');

    // Choose layout based on layoutType
    if (this.data.layoutType === 'slider') {
      return `
        <div class="product-slider" style="
          position: relative;
          padding: 20px 0;
          overflow-x: auto;
          overflow-y: hidden;
          margin: 1rem 0;
          width: 100%;
        ">
          <div style="
            display: flex;
            gap: 20px;
            padding: 0 20px;
            width: fit-content;
          ">
            ${productsHTML}
          </div>
        </div>
      `;
    } else {
      return `
        <div class="product-grid-tool" style="
          display: grid;
          grid-template-columns: repeat(${this.data.gridColumns || 3}, 1fr);
          gap: 1rem;
          margin: 1rem 0;
          width: 100%;
          box-sizing: border-box;
        ">
          ${productsHTML}
        </div>
      `;
    }
  }

  /**
   * Sanitizer config for data cleaning
   */
  static get sanitize() {
    return {
      title: {},
      description: {},
      buttonText: {},
      imageUrl: false, // Disallow HTML
      buttonUrl: false, // Disallow HTML
      rating: false, // Disallow HTML
      cardColor: false,
      buttonColor: false,
      layoutType: false, // Disallow HTML
      gridColumns: false, // Disallow HTML
    };
  }
}