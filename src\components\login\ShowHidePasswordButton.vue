<script setup>
import { ref } from 'vue'

defineProps({
    passwordInputType: String
})

const emit = defineEmits(['passwordTypeChange'])
const passwordIsVisible = ref(false)

const showPassword = () => {
    passwordIsVisible.value = true
    emit('passwordTypeChange', 'text')
}

const hidePassword = () => {
    passwordIsVisible.value = false
    emit('passwordTypeChange', 'password')
}
</script>

<template>
    <span id="show-password" @click="showPassword" v-show="!passwordIsVisible" style="display: block" class="password-toggle-btn noselect">{{ $t("login.showPassword") }}</span>
    <span id="hide-password" @click="hidePassword" v-show="passwordIsVisible" style="display: none" class="password-toggle-btn noselect">{{ $t("login.hidePassword") }}</span>
</template>

<style scoped>
span {
    cursor: pointer;
    color: #9ca3af;
    font-size: 12px;
    user-select: none;
}

span:hover {
    color: rgb(218, 18, 0);
    transition: color 200ms linear;
}

.password-toggle-btn {
    transition: all 0.15s ease;
    display: inline-block;
    padding: 2px 4px;
    border-radius: 4px;
}

.password-toggle-btn:active {
    transform: translateY(1px);
    color: rgb(180, 15, 0);
}
</style>
