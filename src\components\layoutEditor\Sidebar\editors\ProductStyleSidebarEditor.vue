<template>
  <div class="product-style-sidebar-editor">
    <!-- Header -->
    <div class="editor-header">
      <h3>{{ $t('layoutEditor.sidebar.productStyle') }}</h3>
      <IluriaButton 
        variant="ghost" 
        color="dark" 
        size="small" 
        @click="emit('close')"
      >
        <Cancel01Icon />
      </IluriaButton>
    </div>

    <!-- Grid Layout Settings -->
    <div class="style-section">
      <label class="section-label">{{ $t('productGrid.gridLayout') }}</label>
      
      <!-- Columns -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.columns') }}</label>
        <div class="columns-control">
          <IluriaButton 
            v-for="col in [1, 2, 3, 4, 5, 6]" 
            :key="col"
            :variant="columns === col ? 'solid' : 'outline'"
            color="primary"
            size="small"
            @click="updateColumns(col)"
            class="column-btn"
          >
            {{ col }}
          </IluriaButton>
        </div>
      </div>

      <!-- Gap -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.gap') }}</label>
        <IluriaInputText 
          v-model="gap" 
          type="range" 
          min="0" 
          max="4" 
          step="0.5"
          @input="updateGap"
        />
        <span class="range-value">{{ gap }}rem</span>
      </div>
    </div>

    <!-- Card Style Settings -->
    <div class="style-section">
      <label class="section-label">{{ $t('productGrid.cardStyle') }}</label>
      
      <!-- Card Padding -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.cardPadding') }}</label>
        <IluriaInputText 
          v-model="cardPadding" 
          type="range" 
          min="0.5" 
          max="3" 
          step="0.25"
          @input="updateCardPadding"
        />
        <span class="range-value">{{ cardPadding }}rem</span>
      </div>

      <!-- Border Radius -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.borderRadius') }}</label>
        <IluriaInputText 
          v-model="borderRadius" 
          type="range" 
          min="0" 
          max="24" 
          step="2"
          @input="updateBorderRadius"
        />
        <span class="range-value">{{ borderRadius }}px</span>
      </div>

      <!-- Shadow -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.shadow') }}</label>
        <select 
          v-model="shadowType" 
          @change="updateShadow"
          class="style-select"
        >
          <option value="none">{{ $t('productGrid.shadowNone') }}</option>
          <option value="small">{{ $t('productGrid.shadowSmall') }}</option>
          <option value="medium">{{ $t('productGrid.shadowMedium') }}</option>
          <option value="large">{{ $t('productGrid.shadowLarge') }}</option>
        </select>
      </div>
    </div>

    <!-- Color Settings -->
    <div class="style-section">
      <label class="section-label">{{ $t('productGrid.colors') }}</label>
      
      <!-- Background Color -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.backgroundColor') }}</label>
        <div class="color-input-group">
          <input 
            v-model="backgroundColor" 
            type="color"
            @input="updateBackgroundColor"
            class="color-picker"
          />
          <IluriaInputText 
            v-model="backgroundColor" 
            @input="updateBackgroundColor"
            class="color-text"
          />
        </div>
      </div>

      <!-- Text Color -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.textColor') }}</label>
        <div class="color-input-group">
          <input 
            v-model="textColor" 
            type="color"
            @input="updateTextColor"
            class="color-picker"
          />
          <IluriaInputText 
            v-model="textColor" 
            @input="updateTextColor"
            class="color-text"
          />
        </div>
      </div>

      <!-- Button Color -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.buttonColor') }}</label>
        <div class="color-input-group">
          <input 
            v-model="buttonColor" 
            type="color"
            @input="updateButtonColor"
            class="color-picker"
          />
          <IluriaInputText 
            v-model="buttonColor" 
            @input="updateButtonColor"
            class="color-text"
          />
        </div>
      </div>
    </div>

    <!-- Typography Settings -->
    <div class="style-section">
      <label class="section-label">{{ $t('productGrid.typography') }}</label>
      
      <!-- Title Font Size -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.titleFontSize') }}</label>
        <IluriaInputText 
          v-model="titleFontSize" 
          type="range" 
          min="0.75" 
          max="2" 
          step="0.125"
          @input="updateTitleFontSize"
        />
        <span class="range-value">{{ titleFontSize }}rem</span>
      </div>

      <!-- Price Font Size -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.priceFontSize') }}</label>
        <IluriaInputText 
          v-model="priceFontSize" 
          type="range" 
          min="0.75" 
          max="1.75" 
          step="0.125"
          @input="updatePriceFontSize"
        />
        <span class="range-value">{{ priceFontSize }}rem</span>
      </div>

      <!-- Font Weight -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.fontWeight') }}</label>
        <select 
          v-model="fontWeight" 
          @change="updateFontWeight"
          class="style-select"
        >
          <option value="normal">{{ $t('productGrid.fontWeightNormal') }}</option>
          <option value="500">{{ $t('productGrid.fontWeightMedium') }}</option>
          <option value="600">{{ $t('productGrid.fontWeightSemibold') }}</option>
          <option value="700">{{ $t('productGrid.fontWeightBold') }}</option>
        </select>
      </div>
    </div>

    <!-- Responsive Settings -->
    <div class="style-section">
      <label class="section-label">{{ $t('productGrid.responsive') }}</label>
      
      <!-- Mobile Columns -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.mobileColumns') }}</label>
        <div class="columns-control">
          <IluriaButton 
            v-for="col in [1, 2]" 
            :key="col"
            :variant="mobileColumns === col ? 'solid' : 'outline'"
            color="primary"
            size="small"
            @click="updateMobileColumns(col)"
            class="column-btn"
          >
            {{ col }}
          </IluriaButton>
        </div>
      </div>

      <!-- Tablet Columns -->
      <div class="form-group">
        <label class="field-label">{{ $t('productGrid.tabletColumns') }}</label>
        <div class="columns-control">
          <IluriaButton 
            v-for="col in [2, 3, 4]" 
            :key="col"
            :variant="tabletColumns === col ? 'solid' : 'outline'"
            color="primary"
            size="small"
            @click="updateTabletColumns(col)"
            class="column-btn"
          >
            {{ col }}
          </IluriaButton>
        </div>
      </div>
    </div>

    <!-- Reset Button -->
    <div class="reset-section">
      <IluriaButton 
        variant="outline" 
        color="danger" 
        @click="resetStyles"
        class="reset-btn"
      >
        {{ $t('productGrid.resetStyles') }}
      </IluriaButton>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import { Cancel01Icon } from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close'])

// Reactive data
const columns = ref(4)
const gap = ref(1.5)
const cardPadding = ref(1.5)
const borderRadius = ref(12)
const shadowType = ref('medium')
const backgroundColor = ref('#ffffff')
const textColor = ref('#1f2937')
const buttonColor = ref('#3b82f6')
const titleFontSize = ref(1.125)
const priceFontSize = ref(1.25)
const fontWeight = ref('600')
const mobileColumns = ref(2)
const tabletColumns = ref(3)

// Initialize from element data
onMounted(() => {
  const element = props.element
  columns.value = parseInt(element.getAttribute('data-columns')) || 4
  gap.value = parseFloat(element.getAttribute('data-gap')) || 1.5
  cardPadding.value = parseFloat(element.getAttribute('data-card-padding')) || 1.5
  borderRadius.value = parseInt(element.getAttribute('data-border-radius')) || 12
  shadowType.value = element.getAttribute('data-shadow') || 'medium'
  backgroundColor.value = element.getAttribute('data-bg-color') || '#ffffff'
  textColor.value = element.getAttribute('data-text-color') || '#1f2937'
  buttonColor.value = element.getAttribute('data-button-color') || '#3b82f6'
  titleFontSize.value = parseFloat(element.getAttribute('data-title-font-size')) || 1.125
  priceFontSize.value = parseFloat(element.getAttribute('data-price-font-size')) || 1.25
  fontWeight.value = element.getAttribute('data-font-weight') || '600'
  mobileColumns.value = parseInt(element.getAttribute('data-mobile-columns')) || 2
  tabletColumns.value = parseInt(element.getAttribute('data-tablet-columns')) || 3
})

// Update methods
const updateColumns = (value) => {
  columns.value = value
  props.element.setAttribute('data-columns', value.toString())
  emit('data-changed')
}

const updateGap = () => {
  props.element.setAttribute('data-gap', gap.value.toString())
  emit('data-changed')
}

const updateCardPadding = () => {
  props.element.setAttribute('data-card-padding', cardPadding.value.toString())
  emit('data-changed')
}

const updateBorderRadius = () => {
  props.element.setAttribute('data-border-radius', borderRadius.value.toString())
  emit('data-changed')
}

const updateShadow = () => {
  props.element.setAttribute('data-shadow', shadowType.value)
  emit('data-changed')
}

const updateBackgroundColor = () => {
  props.element.setAttribute('data-bg-color', backgroundColor.value)
  emit('data-changed')
}

const updateTextColor = () => {
  props.element.setAttribute('data-text-color', textColor.value)
  emit('data-changed')
}

const updateButtonColor = () => {
  props.element.setAttribute('data-button-color', buttonColor.value)
  emit('data-changed')
}

const updateTitleFontSize = () => {
  props.element.setAttribute('data-title-font-size', titleFontSize.value.toString())
  emit('data-changed')
}

const updatePriceFontSize = () => {
  props.element.setAttribute('data-price-font-size', priceFontSize.value.toString())
  emit('data-changed')
}

const updateFontWeight = () => {
  props.element.setAttribute('data-font-weight', fontWeight.value)
  emit('data-changed')
}

const updateMobileColumns = (value) => {
  mobileColumns.value = value
  props.element.setAttribute('data-mobile-columns', value.toString())
  emit('data-changed')
}

const updateTabletColumns = (value) => {
  tabletColumns.value = value
  props.element.setAttribute('data-tablet-columns', value.toString())
  emit('data-changed')
}

const resetStyles = () => {
  // Reset to defaults
  updateColumns(4)
  gap.value = 1.5
  updateGap()
  cardPadding.value = 1.5
  updateCardPadding()
  borderRadius.value = 12
  updateBorderRadius()
  shadowType.value = 'medium'
  updateShadow()
  backgroundColor.value = '#ffffff'
  updateBackgroundColor()
  textColor.value = '#1f2937'
  updateTextColor()
  buttonColor.value = '#3b82f6'
  updateButtonColor()
  titleFontSize.value = 1.125
  updateTitleFontSize()
  priceFontSize.value = 1.25
  updatePriceFontSize()
  fontWeight.value = '600'
  updateFontWeight()
  updateMobileColumns(2)
  updateTabletColumns(3)
}
</script>

<style scoped>
.product-style-sidebar-editor {
  padding: 20px;
  max-height: 100vh;
  overflow-y: auto;
}

.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid #e5e7eb;
}

.editor-header h3 {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: #1f2937;
}

.style-section {
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid #f3f4f6;
}

.style-section:last-child {
  border-bottom: none;
}

.section-label {
  display: block;
  font-size: 16px;
  font-weight: 600;
  color: #1f2937;
  margin-bottom: 16px;
}

.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: #374151;
  margin-bottom: 8px;
}

.form-group {
  margin-bottom: 20px;
}

.columns-control {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
}

.column-btn {
  min-width: 40px;
}

.range-value {
  display: inline-block;
  margin-left: 12px;
  font-size: 14px;
  font-weight: 500;
  color: #6b7280;
  min-width: 50px;
}

.style-select {
  width: 100%;
  padding: 8px 12px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  font-size: 14px;
  background: white;
  color: #374151;
}

.style-select:focus {
  outline: none;
  border-color: #3b82f6;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.1);
}

.color-input-group {
  display: flex;
  gap: 8px;
  align-items: center;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: 1px solid #d1d5db;
  border-radius: 6px;
  cursor: pointer;
  background: none;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: none;
  border-radius: 4px;
}

.color-text {
  flex: 1;
}

.reset-section {
  margin-top: 32px;
  padding-top: 24px;
  border-top: 1px solid #e5e7eb;
}

.reset-btn {
  width: 100%;
}

/* Custom range slider styling */
input[type="range"] {
  width: 100%;
  -webkit-appearance: none;
  appearance: none;
  height: 6px;
  border-radius: 3px;
  background: #e5e7eb;
  outline: none;
}

input[type="range"]::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

input[type="range"]::-moz-range-thumb {
  width: 18px;
  height: 18px;
  border-radius: 50%;
  background: #3b82f6;
  cursor: pointer;
  border: none;
  box-shadow: 0 2px 4px rgba(59, 130, 246, 0.3);
}

input[type="range"]:focus::-webkit-slider-thumb {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}

input[type="range"]:focus::-moz-range-thumb {
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.2);
}
</style> 
