{"title": "Variations", "variationName": "Variation name", "variationNamePlaceholder": "e.g. Color, Size...", "options": "Options", "addOption": "Add", "addOptionPlaceholder": "Add option for this variation", "showInSearch": "Show in search", "showInSearchRequired": "At least one variation must be enabled for 'Show in Search'. This setting determines which variation will be displayed in your product search results.", "addVariation": "Add variation (Color, Size, Material...)", "deleteVariation": "Delete", "groupBy": "Group by", "editVariations": "Edit Variations", "editVariation": "Edit Variation", "price": "Price", "stock": "Stock", "photos": "Photos", "selectPhotos": "Select Photos", "editPhotos": "Edit Photos", "toggle": "Toggle", "save": "Save", "cancel": "Cancel", "delete": "Delete", "uploadPhotos": "Upload Photos", "dragAndDropPhotos": "Drag and drop photos here or click to select", "selectFiles": "Select Files", "photosUploaded": "Photos uploaded successfully", "photoUploadError": "Error uploading photo", "confirmDeletePhoto": "Confirm photo deletion", "confirmDeletePhotoMessage": "Are you sure you want to delete this photo?", "maxPhotosReached": "Maximum photos reached", "invalidFileType": "Invalid file type. Select only images.", "fileTooLarge": "File too large. Maximum size: 5MB", "noVariationsCreated": "No variations created yet", "createVariationsFirst": "Create your variations first to get started", "variationOptions": "Variation Options", "combinedVariations": "Combined Variations", "variationTable": "Variations Table", "allVariations": "All Variations", "maxVariationsReached": "Maximum of 3 variations allowed", "searchVariations": "Search variations", "noVariationsFound": "No variations found with the selected filters.", "filter": "Filter", "actions": "Actions", "clear": "Clear", "clearAll": "Clear All", "selectAll": "Select All", "unselectAll": "Unselect All", "deleteSelected": "Delete Selected", "addPriceRange": "Add Price Range", "remove": "Remove", "originalPrice": "Original Price", "originalStock": "Original Stock", "originalPhotos": "Original Photos", "costPrice": "Cost Price", "originalCostPrice": "Original Cost Price", "weight": "Weight", "originalWeight": "Original Weight", "boxLength": "Box Length", "originalBoxLength": "Original Box Length", "boxWidth": "Box Width", "originalBoxWidth": "Original Box Width", "boxDepth": "Box Depth", "originalBoxDepth": "Original Box Depth", "copyOriginalPriceToAll": "Copy original price to all variations", "copyCostPriceToAll": "Copy cost price to all variations", "copyWeightToAll": "Copy weight to all variations", "copyLengthToAll": "Copy length to all variations", "copyWidthToAll": "Copy width to all variations", "copyDepthToAll": "Copy depth to all variations", "copyAllToAll": "Copy all to all variations", "allDataCleared": "All variation data has been cleared successfully", "confirmClearAllTitle": "Confirm Data Clearing", "confirmClearAllMessage": "Are you sure you want to clear all variation data? This action will remove prices, stock, photos and other information, but will keep the variation structure. This action cannot be undone.", "confirmClearAll": "Yes, clear all"}