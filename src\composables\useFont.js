import { ref, computed, watch } from 'vue'

// Definição das fontes disponíveis
const FONTS = {
  poppins: {
    id: 'poppins',
    cssName: '"Poppins", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Poppins:ital,wght@0,100;0,200;0,300;0,400;0,500;0,600;0,700;0,800;0,900;1,100;1,200;1,300;1,400;1,500;1,600;1,700;1,800;1,900&display=swap");'
  },
  inter: {
    id: 'inter',
    cssName: '"Inter", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Inter:ital,opsz,wght@0,14..32,100..900;1,14..32,100..900&display=swap");'
  },
  roboto: {
    id: 'roboto',
    cssName: '"Roboto", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Roboto:ital,wght@0,100;0,300;0,400;0,500;0,700;0,900;1,100;1,300;1,400;1,500;1,700;1,900&display=swap");'
  },
  nunito: {
    id: 'nunito',
    cssName: '"Nunito", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Nunito:ital,wght@0,200..1000;1,200..1000&display=swap");'
  },
  openSans: {
    id: 'openSans',
    cssName: '"Open Sans", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Open+Sans:ital,wght@0,300..800;1,300..800&display=swap");'
  },
  montserrat: {
    id: 'montserrat',
    cssName: '"Montserrat", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Montserrat:ital,wght@0,100..900;1,100..900&display=swap");'
  },
  lato: {
    id: 'lato',
    cssName: '"Lato", sans-serif',
    previewText: 'Ag',
    category: 'Sans-serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Lato:ital,wght@0,100;0,300;0,400;0,700;0,900;1,100;1,300;1,400;1,700;1,900&display=swap");'
  },
  playfair: {
    id: 'playfair',
    cssName: '"Playfair Display", serif',
    previewText: 'Ag',
    category: 'Serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Playfair+Display:ital,wght@0,400..900;1,400..900&display=swap");'
  },
  merriweather: {
    id: 'merriweather',
    cssName: '"Merriweather", serif',
    previewText: 'Ag',
    category: 'Serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Merriweather:ital,wght@0,300;0,400;0,700;0,900;1,300;1,400;1,700;1,900&display=swap");'
  },
  crimson: {
    id: 'crimson',
    cssName: '"Crimson Text", serif',
    previewText: 'Ag',
    category: 'Serif',
    import: '@import url("https://fonts.googleapis.com/css2?family=Crimson+Text:ital,wght@0,400;0,600;0,700;1,400;1,600;1,700&display=swap");'
  }
}

// Estado global da fonte
const currentFontId = ref('poppins')
const isLoadingFont = ref(false)

export function useFont() {
  // Computed para a fonte atual
  const currentFont = computed(() => FONTS[currentFontId.value] || FONTS.poppins)
  
  // Lista de fontes disponíveis
  const availableFonts = computed(() => Object.values(FONTS))
  
  // Fontes agrupadas por categoria
  const fontsByCategory = computed(() => {
    const categories = {}
    availableFonts.value.forEach(font => {
      if (!categories[font.category]) {
        categories[font.category] = []
      }
      categories[font.category].push(font)
    })
    return categories
  })
  
  // Função para aplicar fonte
  const applyFont = (fontId) => {
    const font = FONTS[fontId]
    if (!font) return
    
    isLoadingFont.value = true
    
    // Aplicar CSS variable no :root
    const root = document.documentElement
    root.style.setProperty('--iluria-font-family', font.cssName)
    
    // Carregar a fonte dinamicamente
    loadFontImport(font.import)
    
    // Salvar preferência
    localStorage.setItem('iluria-font', fontId)
    
    // Finalizar loading
    setTimeout(() => {
      isLoadingFont.value = false
    }, 500)
  }
  
  // Função para carregar import da fonte
  const loadFontImport = (importUrl) => {
    // Remove import anterior se existir
    const existingLink = document.querySelector('link[data-font-import]')
    if (existingLink) {
      existingLink.remove()
    }
    
    // Extrai a URL do import
    const urlMatch = importUrl.match(/url\("([^"]+)"\)/)
    if (urlMatch) {
      const link = document.createElement('link')
      link.rel = 'stylesheet'
      link.href = urlMatch[1]
      link.setAttribute('data-font-import', 'true')
      document.head.appendChild(link)
    }
  }
  
  // Função para mudar fonte
  const setFont = (fontId) => {
    if (currentFontId.value === fontId) return
    currentFontId.value = fontId
    applyFont(fontId)
  }
  
  // Função para carregar fonte salva
  const loadSavedFont = () => {
    const saved = localStorage.getItem('iluria-font')
    if (saved && FONTS[saved]) {
      return saved
    }
    return 'poppins' // fonte padrão
  }
  
  // Inicializar fonte
  const initFont = () => {
    const fontId = loadSavedFont()
    currentFontId.value = fontId
    applyFont(fontId)
  }
  
  // Watch para mudanças na fonte
  watch(currentFontId, (newFontId) => {
    applyFont(newFontId)
  })
  
  // Retornar interface pública
  return {
    // Estado
    currentFont,
    currentFontId: computed(() => currentFontId.value),
    availableFonts,
    fontsByCategory,
    isLoadingFont,
    
    // Métodos
    setFont,
    initFont
  }
}

// Instância global para uso em componentes que não usam composition API
let globalFontInstance = null

export function getGlobalFont() {
  if (!globalFontInstance) {
    globalFontInstance = useFont()
  }
  return globalFontInstance
} 