{"back": "Voltar", "backToMenu": "Voltar ao Menu", "save": "<PERSON><PERSON>", "saving": "Salvando...", "saved": "Salvo", "loading": "Carregando...", "settings": "Configurações", "openSettings": "<PERSON><PERSON>r configuraç<PERSON><PERSON>", "undo": "<PERSON><PERSON><PERSON>", "redo": "<PERSON><PERSON><PERSON>", "editMode": "Modo de Edição", "viewMode": "Modo de Visualização", "mobileView": "Modo de Visualização", "desktopView": "Modo de Visualização", "editProducts": "<PERSON><PERSON>", "editProductStyle": "Estilo de Produtos", "addAbove": "<PERSON><PERSON><PERSON><PERSON>", "delete": "Excluir", "error": {"title": "Erro ao carregar template", "message": "Não foi possível carregar o template.", "description": "O arquivo index.html não foi encontrado no ambiente.", "retry": "Tentar novamente"}, "pageSettings": "Configurações da Página", "pageInfo": "Informações da Página", "layoutSettings": "Configurações do Layout", "backgroundSettings": "Configurações de Fundo", "pageTitle": "<PERSON><PERSON><PERSON><PERSON>gin<PERSON>", "pageTitlePlaceholder": "Digite o título da página", "pageLogo": "Logo", "pageLogoPlaceholder": "URL do Logo", "pageFavicon": "Favicon", "pageFaviconPlaceholder": "URL do Favicon", "contentWidth": "Largura do Conteúdo", "backgroundColor": "<PERSON><PERSON> <PERSON>", "backgroundType": "Tipo de Fundo", "backgroundImage": "<PERSON><PERSON>", "backgroundImagePlaceholder": "URL da Imagem de Fundo", "backgroundPresets": "Presets de Fundo", "backgroundTypes": {"solid": "Cor Solida", "gradient": "Gradiente", "image": "Imagem", "preset": "Presets"}, "gradientSettings": "Configurações de Gradiente", "gradientType": "Tipo de Gradiente", "linear": "Linear", "radial": "Radial", "angle": "<PERSON><PERSON><PERSON>", "presets": "Presets", "resetDefaults": "Reiniciar para os valores padrão", "imageSize": "<PERSON><PERSON><PERSON>", "imagePosition": "Posição da Imagem", "cover": "Cobrir", "contain": "<PERSON><PERSON><PERSON>", "auto": "Auto", "center": "Centro", "topLeft": "Superior Esquerdo", "topCenter": "Topo Centro", "topRight": "Superior Direito", "textEditor": "Editor de Texto", "textContent": "Conteúdo do Texto", "enterText": "Digite o texto aqui...", "textColor": "<PERSON><PERSON> <PERSON>", "fontSize": "<PERSON><PERSON><PERSON>", "fontSizeUnit": "{size}px", "minFontSize": "12px", "mediumFontSize": "32px", "maxFontSize": "72px", "colorValue": "{value}", "editText": "<PERSON><PERSON>", "editFont": "<PERSON><PERSON>", "editSpacing": "<PERSON><PERSON>", "editBorder": "<PERSON><PERSON>", "editBackground": "<PERSON><PERSON>", "editAnimation": "<PERSON>ar <PERSON>", "editTransform": "Editar Transformação", "editFilter": "<PERSON><PERSON>", "editImage": "<PERSON><PERSON>", "editSize": "<PERSON><PERSON>", "editLink": "<PERSON><PERSON>", "editColor": "<PERSON><PERSON>", "editLabel": "Editar Label", "editCarouselContent": "<PERSON><PERSON>", "editCarouselDesign": "Editar Design do Carrossel", "editLocationContent": "<PERSON><PERSON> da Localização", "editLocationDesign": "Editar Design da Localização", "carouselContentEditor": "Editor de Conteúdo <PERSON>", "carouselDesignEditor": "Editor de Design do Carross<PERSON>", "locationContentEditor": "Editor de Conteúdo da Localização", "locationDesignEditor": "Editor de Design da Localização", "remove": "Remover", "carousel": "<PERSON><PERSON><PERSON>", "location": "Localização", "imageUrl": "URL da Imagem", "enterImageUrl": "Digite a URL da Imagem", "imageAlt": "Texto Alternativo", "enterImageAlt": "Digite o texto alternativo", "imageSizeContain": "Fit", "imageSizeCover": "Fill", "imageSizeFull": "100%", "linkUrl": "URL do Link", "enterLinkUrl": "Digite a URL do Link", "linkTarget": "Alvo do Link", "linkTargetSelf": "<PERSON><PERSON>", "linkTargetBlank": "Nova Janela", "spacingEditor": "Editor de Espaçamento", "borderEditor": "<PERSON> <PERSON>", "backgroundEditor": "Editor de Fundo", "animationEditor": "Editor de Animação", "transformEditor": "Editor de Transformação", "filterEditor": "Editor de Filtros", "sizeEditor": "<PERSON> <PERSON>", "padding": "Padding", "margin": "<PERSON><PERSON>", "borderRadius": "<PERSON><PERSON> da Borda", "content": "<PERSON><PERSON><PERSON><PERSON>", "quickLinks": "<PERSON><PERSON>", "resetSpacing": "Reiniciar E<PERSON>mento", "filters": {"blur": "Desfoque", "brightness": "Bril<PERSON>", "contrast": "Contraste", "saturate": "Saturação", "hueRotate": "Rotação de tom", "sepia": "Sepia", "grayscale": "Grayscale", "invert": "Inverter"}, "containerStyle": "<PERSON><PERSON><PERSON>tain<PERSON>", "containerPadding": "Espaçamento do Container", "gridGap": "Grid Gap", "productItemStyle": "Estilo do Item do Produto", "itemBackgroundColor": "Cor de Fundo do Item", "itemBorderColor": "<PERSON>r da Borda do Item", "itemBorderRadius": "Raio da Borda do Item", "itemPadding": "<PERSON><PERSON>", "shadow": "Sombra", "noShadow": "Se<PERSON>", "smallShadow": "Sombra Pequena", "mediumShadow": "Sombra Média", "largeShadow": "Sombra Grande", "textStyle": "Estilo do <PERSON>o", "titleColor": "Cor do Título", "titleSize": "Tamanho do Título", "priceColor": "Cor do Preço", "priceSize": "Tamanho do Preço", "buttonStyle": "Estilo do Botão", "buttonColor": "Cor do Botão", "buttonTextColor": "Cor do Texto do Botão", "productStyleEditor": "Editor de Estilo do Produto", "productSelectionEditor": "Editor de Seleção de Produto", "selectProducts": "Selecionar Produtos", "selectProductsDescription": "Escolha os produtos a serem exibidos nesta grade", "selectionMode": "Modo de Seleção", "automatic": "Automatico", "manual": "Manual", "cancel": "<PERSON><PERSON><PERSON>", "close": "<PERSON><PERSON><PERSON>", "addComponent": "Adicionar Compo<PERSON>e", "apply": "Aplicar", "applyAll": "Aplica<PERSON>", "fontEditor": "Editor de Fonte", "category": "Categoria", "allCategories": "<PERSON><PERSON> as Categoria<PERSON>", "sortBy": "Ordenar por", "newest": "<PERSON><PERSON>e", "oldest": "<PERSON><PERSON> anti<PERSON>", "priceAsc": "Preço mais baixo", "priceDesc": "Preço mais alto", "alphabetical": "Alfabético", "popularity": "Popularidade", "stockFilter": "Filtro de Estoque", "onlyInStock": "Apenas em Estoque", "searchProducts": "Procurar produtos...", "browse": "Navegar", "selectedProducts": "<PERSON>du<PERSON>", "noProductsSelected": "Nenhum produto selecionado", "removeProduct": "Remover produto", "insertBelow": "Inserir a<PERSON>o", "insertAbove": "Inserir a<PERSON>", "below": "Abaixo", "above": "Acima", "components": {"divider": "Divisoria", "spacer": "Espaçamento", "productGrid": "Grade de Produtos", "dynamicProductGrid": "Grade de Produtos Dinâmica", "location": "Localização"}, "categories": {"basic": "Básico", "media": "Media", "layout": "Layout", "interactive": "Interativo", "ecommerce": "E-commerce", "content": "<PERSON><PERSON><PERSON><PERSON>"}, "actions": {"addElement": "<PERSON><PERSON><PERSON><PERSON>", "deleteElement": "Deletar Elemento", "duplicateElement": "Duplicar Elemento", "moveUp": "Mover para Cima", "moveDown": "Mover para Baixo"}, "messages": {"elementAdded": "Elemento adicionado com sucesso", "elementDeleted": "Elemento deletado com sucesso", "elementDuplicated": "Elemento duplicado com sucesso", "changesSaved": "Alterações salvas com sucesso", "errorSaving": "Erro ao salvar alterações", "confirmDelete": "Tem certeza que deseja deletar este elemento?", "invalidHtml": "HTML inválido detectado", "loadingError": "Erro ao carregar template"}, "dragToMove": "<PERSON><PERSON><PERSON> para mover", "selectImage": "Selecionar Imagem", "slides": "Slides", "addSlide": "<PERSON><PERSON><PERSON><PERSON>", "slideTitle": "Título do Slide", "slideSubtitle": "Subtítulo do Slide", "showButton": "<PERSON><PERSON>", "buttonText": "Texto do Botão", "buttonLink": "Link do Botão", "layout": "Layout", "behavior": "Comportamento", "autoPlay": "Reprodução Automática", "autoPlayDescription": "Avançar slides automaticamente", "interval": "Intervalo", "seconds": "segundos", "showIndicators": "Mostrar Indicadores", "showNavigation": "Mostrar Navegação", "transitionType": "Tipo de Transição", "transitionTypeHelp": "Escolha como os slides fazem a transição entre eles", "transitionDuration": "Duração da Transição", "transitionDurationHelp": "Tempo em milissegundos para a animação entre slides", "slide": "<PERSON><PERSON><PERSON>", "fade": "Aparecer/Desaparecer", "cube": "Cubo 3D", "flip": "<PERSON><PERSON><PERSON>", "none": "Sem Animação", "height": "Altura", "primary": "<PERSON><PERSON><PERSON><PERSON>", "secondary": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "outline": "Contorno", "ghost": "Fantasma", "layoutStandard": "Padrão", "layoutFullScreen": "Full", "layoutCard": "Cartão", "layoutMinimal": "Mini", "dimensions": "Dimensões", "heightPlaceholder": "Ex: 450px, 50vh", "heightHelp": "Use px, vh, rem ou %", "borderRadiusPlaceholder": "Ex: 12px, 1rem", "textSettings": "Configurações de Texto", "textAlignment": "Alinhamento do Texto", "alignment": "Alinhamento", "overlayOpacity": "Opacidade do Overlay", "overlayOpacityHelp": "Controla a transparência do fundo escuro sobre as imagens", "buttonSettings": "Configurações dos Botões", "defaultButtonStyle": "Estilo <PERSON> dos Botões", "mobileLayout": "Layout Mobile", "mobileSettings": "Configurações Mobile", "stackTextOnMobile": "Empilhar Texto no Mobile", "mobileHeight": "Altura no Mobile", "mobileHeightPlaceholder": "Ex: 300px, 40vh", "left": "E<PERSON>rda", "right": "<PERSON><PERSON><PERSON>", "top": "Topo", "bottom": "Embaixo", "bottomLeft": "Inferior Esquerdo", "bottomRight": "Inferior Direito", "unit": "Unidade", "titleScale": "Escala do Título", "subtitleScale": "Escala do Subtítulo", "overlayColor": "<PERSON><PERSON> <PERSON>", "navigationSettings": "Configurações de Navegação", "indicatorColor": "Cor dos Indicadores", "navigationButtonColor": "Cor dos Botões de Navegação", "slideTitlePlaceholder": "Digite o título do slide", "slideSubtitlePlaceholder": "Digite o subtítulo do slide", "buttonTextPlaceholder": "Digite o texto do botão", "buttonLinkPlaceholder": "https://exemplo.com", "buttonUrlPlaceholder": "https://exemplo.com", "untitled": "<PERSON><PERSON> tí<PERSON>lo", "reset": "<PERSON><PERSON><PERSON>", "editCompanyInformation": "Editar Informações da Empresa", "videoConfigEditor": "Editor de Configuração de Vídeo", "videoContent": "<PERSON><PERSON><PERSON><PERSON>", "videoInfo": "Informações", "videoPlayback": "Reprodução", "videoSource": "Fonte do Vídeo", "videoInformation": "Informações do Vídeo", "videoUrl": "URL do Vídeo", "videoUrlLabel": "URL do Vídeo", "videoUrlPlaceholder": "https://www.youtube.com/watch?v=...", "videoType": {"youtube": "YouTube", "vimeo": "Vimeo", "direct": "Direto"}, "videoPreview": "Pré-visualização", "videoPreviewPlaceholder": "URL inválida ou não suportada", "videoTitleLabel": "Título do Vídeo", "videoTitlePlaceholder": "<PERSON><PERSON> Ví<PERSON>o <PERSON>", "videoDescriptionLabel": "Descrição", "videoDescriptionPlaceholder": "Descrição do vídeo...", "videoPosterLabel": "<PERSON><PERSON>", "videoPosterPlaceholder": "https://exemplo.com/imagem.jpg", "videoPosterHint": "URL da imagem que aparece antes do vídeo carregar", "playbackSettings": "Configurações de Reprodução", "autoplay": "Reprodução Automática", "autoplayDescription": "Iniciar reprodução automaticamente quando a página carregar", "loop": "Loop", "loopDescription": "Repetir o vídeo quando terminar", "muted": "<PERSON><PERSON><PERSON><PERSON>", "mutedDescription": "Iniciar o vídeo sem som", "controls": "Controles", "controlsDescription": "Mostrar controles de reprodução", "editVideoConfig": "Configu<PERSON>", "videoDimensions": "Dimensões do Vídeo", "videoWidth": "<PERSON><PERSON><PERSON>", "videoHeight": "Altura", "videoWidthPlaceholder": "100%, 500px, 50vw", "videoHeightPlaceholder": "400", "videoResponsive": "Responsivo", "videoResponsiveHint": "Ajustar automaticamente ao tamanho da tela (proporção 16:9)", "videoErrors": {"invalidUrl": "URL de vídeo inválida", "youtubeInvalidId": "ID do YouTube inválido na URL", "vimeoInvalidId": "ID do Vimeo inválido na URL", "directVideoError": "Erro ao carregar vídeo direto", "networkError": "Erro de conexão ao carregar vídeo", "unsupportedFormat": "Formato de vídeo não suportado"}, "responsive": "Responsivo", "videoStartTime": "Tempo de Início", "videoEndTime": "Tempo de Fim", "videoStartTimeHelp": "Tempo em segundos para iniciar o vídeo", "videoEndTimeHelp": "Tempo em segundos para parar o vídeo (0 = até o fim)", "confirmDelete": {"title": "Confirmar <PERSON>", "message": "Tem certeza que deseja remover este elemento?", "cancel": "<PERSON><PERSON><PERSON>", "confirm": "Excluir"}, "notifications": {"componentAdded": "Componente adicionado com sucesso", "componentDeleted": "Elemento removido com sucesso", "changesSaved": "Alterações salvas com sucesso", "saveSuccess": "Página salva com sucesso", "errorDeleting": "Erro ao remover elemento"}, "statementConfig": "Configuração do Comunicado", "editStatementConfig": "Editar Statement", "text": "Texto", "textPlaceholder": "Digite o texto aqui...", "button": "Botão", "preview": "Visualização", "textPreview": "Este é um texto de visualização para o comunicado", "buttonPreview": "Clique aqui", "title": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Digite o título...", "subtitle": "Subtítulo", "subtitlePlaceholder": "Digite o subtítulo...", "description": "Descrição", "descriptionPlaceholder": "Digite a descrição...", "general": "G<PERSON>", "colors": "Cores", "typography": "Tipografia", "spacing": "Espaçamento", "borders": "<PERSON><PERSON><PERSON>", "grid": "Grade", "list": "Lista", "cards": "<PERSON><PERSON><PERSON><PERSON>", "locations": "Localizações", "manageLocations": "Gerenciar Localizações", "addLocation": "Adicionar Localização", "newLocation": "Nova Localização", "locationName": "Nome da Localização", "locationNamePlaceholder": "Ex: <PERSON><PERSON>", "address": "Endereço", "addressPlaceholder": "Digite o endereço completo...", "phone": "Telefone", "phonePlaceholder": "+55 11 99999-9999", "email": "Email", "emailPlaceholder": "<EMAIL>", "hours": "Horário de Funcionamento", "hoursPlaceholder": "Seg-Sex: 9h às 18h", "image": "Imagem", "imagePlaceholder": "https://exemplo.com/imagem.jpg", "imageUrlPlaceholder": "https://exemplo.com/imagem.jpg", "imagePreview": "Visualização da Imagem", "edit": "<PERSON><PERSON>", "showMap": "<PERSON><PERSON><PERSON>", "socialLinks": "<PERSON><PERSON>", "moveUp": "Mover para Cima", "moveDown": "Mover para Baixo", "backgroundColors": "Cores de Fundo", "sectionBackground": "Fundo da Seção", "cardBackground": "Fundo dos Cartões", "textColors": "Cores do Texto", "accentColor": "<PERSON><PERSON> de <PERSON>", "sectionPadding": "Espaçamento da Seção", "cardPadding": "Espaçamento dos Cartões", "itemGap": "Espaçamento entre Itens", "shadowIntensity": "Intensidade da Sombra", "light": "<PERSON><PERSON>", "medium": "Médio", "heavy": "Forte", "maxWidth": "<PERSON><PERSON><PERSON>", "containerMaxWidth": "<PERSON><PERSON><PERSON> Máxima do Container", "titletypography": "Tipografia do Título", "titleFontSize": "<PERSON><PERSON><PERSON> da Fonte do Título", "titleFontWeight": "Peso da Fonte do Título", "locationNameTypography": "Tipografia do Nome da Localização", "locationNameFontSize": "<PERSON><PERSON><PERSON> da Fonte do Nome", "locationNameFontWeight": "Peso da Fonte do Nome", "updateMap": "<PERSON><PERSON><PERSON><PERSON>", "mapUpdateHint": "Clique para atualizar o mapa com o endereço atual", "bodyText": "Texto do Corpo", "bodyFontSize": "<PERSON><PERSON><PERSON> da Fonte do Corpo", "lineHeight": "<PERSON><PERSON>", "normal": "Normal", "semibold": "Semi-negrito", "bold": "Negrito", "statementEditor": "Editor de Statement", "companyInformationEditor": "Editor de Informações da Empresa", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer": "Rodapé", "component": "Componente", "htmlElement": "Elemento HTML", "selectElement": "Selecionar Elemento", "selectElementDesc": "Clique em um elemento na página para editá-lo", "backToComponents": "Voltar aos Componentes", "basicEditor": "Editor Básico", "editorLoadError": "Erro ao carregar editor", "headerEditor": "Editor <PERSON> Cabeça<PERSON>ho", "footerEditor": "Editor <PERSON> Rodapé", "footerContent": "Conteúdo do Rodapé", "companyName": "Nome da Empresa", "enterCompanyName": "Digite o nome da empresa", "createLayout": "C<PERSON><PERSON> Layout", "createLayoutSubtitle": "Crie um novo layout baseado em um existente", "layoutType": "<PERSON><PERSON><PERSON> de Layout", "layoutTypeHelp": "Escolha o tipo de layout que você quer criar", "staticLayout": "Layout E<PERSON><PERSON>", "productLayout": "Layout de Produto", "collectionLayout": "Layout de Coleção", "layoutName": "Nome do Layout", "layoutNamePlaceholder": "Ex: Produto Premium", "layoutNameHelp": "Digite um nome descritivo para o layout", "basedOn": "Baseado em", "basedOnHelp": "Este layout será criado como uma cópia do layout selecionado", "generatedId": "ID Gerado", "generatedIdHelp": "Este será o identificador único do layout", "searchLayouts": "Pesquisar layouts...", "layouts": "Layouts", "createNewLayout": "Criar novo layout", "createModel": "C<PERSON>r modelo", "system": "Sistema", "products": "<PERSON><PERSON><PERSON>", "collections": "Coleções", "defaultBadge": "Padrão", "duplicateLayout": "Duplicar layout", "copyright": "Copyright", "enterCopyright": "Digite o copyright", "enterAddress": "Digite o endereço", "enterPhone": "Digite o telefone", "logo": "Logo", "logoConfiguration": "Configuração do Logo", "logoText": "Texto do Logo", "enterLogoText": "Digite o texto do logo", "logoPosition": "Posição do Logo", "logoMobilePosition": "Posição do Logo (Mobile)", "menu": "<PERSON><PERSON>", "menuConfiguration": "Configuração do Menu", "menuPosition": "Posição do Menu", "menuType": "<PERSON><PERSON><PERSON>", "horizontal": "Horizontal", "dropdown": "Dropdown", "menuItems": "Itens do Menu", "addMenuItem": "<PERSON><PERSON><PERSON><PERSON>", "menuTitle": "Título do Menu", "menuUrl": "URL do Menu", "hasDropdown": "Tem Dropdown", "colorConfiguration": "Configuração de Cores", "layoutConfiguration": "Configuração de Layout", "spacingConfiguration": "Configuração de Espaçamento", "servicesConfiguration": "Configuração de Serviços", "showSeparator": "Mostrar Separador", "paddingTop": "Padding Superior", "paddingBottom": "Padding Inferior", "innerMargin": "Margem Interna", "countrySelector": "<PERSON><PERSON><PERSON>", "languageSelector": "<PERSON><PERSON><PERSON>", "accountAvatar": "<PERSON><PERSON> da <PERSON>ta", "services": "Serviços", "editPaymentBenefitsContent": "<PERSON><PERSON> Benefícios", "editPaymentBenefitsDesign": "Editar Design dos Benefícios", "paymentBenefitsContentEditor": "Editor de Conteúdo dos Benefícios", "paymentBenefitsDesignEditor": "Editor de Design dos Benefícios", "pontosDePagamento": "Pontos de Venda", "adicionarPontoDePagamento": "<PERSON><PERSON><PERSON><PERSON>", "pontoDePagamento": "Ponto <PERSON>end<PERSON>", "newBenefit": "Novo Benefício", "descricao": "Descrição", "enterDescription": "Digite a descrição...", "icone": "Ícone", "ativar": "Ativar", "customIcon": "Ícone Personalizado", "usePresetIcon": "Usar Ícone Predefinido", "enterSvgCode": "Digite o código SVG...", "sidebar": {"layoutEditor": "Editor de Layout", "addEditComponents": "Adicionar e editar componentes", "backToMenu": "Voltar ao menu", "closeSidebar": "<PERSON><PERSON><PERSON> barra lateral", "pageConfiguration": "Configuração da Página", "pageConfigurationDesc": "Configurações gerais da página, tema e layout", "components": "Componentes", "styleEditors": "Editores de Estilo", "noComponentsAvailable": "Nenhum componente disponível", "loadingComponents": "Carregando componentes...", "pageSections": "Seções da Página", "addComponent": "Adicionar Compo<PERSON>e", "selectElementToEdit": "Selecione um elemento para editar", "selectElementDesc": "Clique num elemento da página para editá-lo aqui", "backToComponents": "Voltar aos componentes", "loadingEditor": "Carregando editor...", "editorError": "Erro no editor", "tryAgain": "Tentar novamente", "basicEditor": "Editor Básico", "spacing": "Espaçamento", "spacingDesc": "Ajustar margens e espaçamentos", "border": "<PERSON><PERSON><PERSON>", "borderDesc": "Configurar bordas e cantos arredondados", "animation": "Animação", "animationDesc": "Adicionar efeitos de animação", "transform": "Transformação", "transformDesc": "Rotacionar, dimensionar e mover", "filter": "<PERSON><PERSON><PERSON>", "filterDesc": "Aplicar filtros visuais", "headerEditor": "Editor <PERSON> Cabeça<PERSON>ho", "footerEditor": "Editor <PERSON> Rodapé", "carouselEditor": "<PERSON> <PERSON>", "videoEditor": "Editor de Vídeo", "companyInfoEditor": "Editor de Informações da Empresa", "locationEditor": "Editor de Localização", "statementEditor": "Editor de Comunicado", "paymentBenefitsEditor": "Editor de Benefícios de Pagamento", "customerReviewEditor": "Editor de Avaliações", "specialOffersEditor": "Editor de Ofertas Especiais", "productSelection": "Seleção de Produtos", "productStyle": "Estilo de Produtos", "elementEditor": "Editor de Elemento", "editingComponent": "Editando componente {component}", "editingElement": "Editando elemento {element}", "customizeProperties": "<PERSON><PERSON><PERSON> propriedades", "customizePageSettings": "Personalizar configuraç<PERSON><PERSON> da página", "currentPageSections": "Seções atuais da página", "addNewSection": "Adicionar nova seção", "manageSections": "Gerenciar seções", "header": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "footer": "Rodapé", "carousel": "<PERSON><PERSON><PERSON>", "video": "Vídeo", "companyInfo": "Informações da Empresa", "location": "Localização", "statement": "Comunicado", "paymentBenefits": "Benefícios de Pagamento", "customerReview": "Avaliações de Clientes", "specialOffers": "<PERSON><PERSON><PERSON>", "component": "Componente", "htmlElement": "Elemento HTML", "heading1": "Título 1", "heading2": "Título 2", "heading3": "Título 3", "heading4": "Título 4", "heading5": "Título 5", "heading6": "Título 6", "paragraph": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "image": "Imagem", "link": "Link", "container": "Container", "text": "Texto", "editorLoadError": "Erro ao carregar editor", "selectElement": "Selecionar elemento", "addSection": "Adicionar <PERSON>", "noSectionsFound": "Nenhuma seção encontrada", "expandSidebar": "Expandir barra lateral", "elementSelected": "Elemento selecionado", "deleteElement": "Deletar Elemento", "deleteElementConfirmation": "Tem certeza que deseja deletar este elemento?", "deleteElementConfirmationMessage": "Esta ação não pode ser desfeita.", "deleteElementConfirmationButton": "Deletar", "deleteElementCancelButton": "<PERSON><PERSON><PERSON>", "general": "G<PERSON>", "design": "Design", "settings": "Configurações", "content": "<PERSON><PERSON><PERSON><PERSON>", "benefits": "Benefí<PERSON>s", "offers": "<PERSON><PERSON><PERSON>", "slides": "Slides", "locations": "Localizações", "title": "<PERSON><PERSON><PERSON><PERSON>", "titlePlaceholder": "Digite o título da seção", "subtitle": "Subtítulo", "subtitlePlaceholder": "Digite o subtítulo da seção", "layout": "Layout", "layoutGrid": "Grade", "layoutList": "Lista", "layoutCards": "<PERSON><PERSON><PERSON><PERSON>", "manageLocations": "Gerenciar Localizações", "addLocation": "Adicionar Localização", "newLocation": "Nova Localização", "locationName": "Nome da Localização", "locationNamePlaceholder": "Digite o nome da localização", "description": "Descrição", "descriptionPlaceholder": "Digite a descrição", "address": "Endereço", "addressPlaceholder": "Digite o endereço completo", "phone": "Telefone", "email": "Email", "hours": "Horário de Funcionamento", "hoursPlaceholder": "Ex: Seg-Sex: 9h às 18h", "imageUrl": "URL da Imagem", "selectImage": "Selecionar Imagem", "showMap": "<PERSON><PERSON><PERSON>", "socialLinks": "Redes Sociais", "moveUp": "Mover para Cima", "moveDown": "Mover para Baixo", "remove": "Remover", "colors": "Cores", "sectionBackground": "Fundo da Seção", "cardBackground": "Fundo dos Cartões", "titleColor": "Cor do Título", "textColor": "<PERSON><PERSON> <PERSON>", "iconColor": "Co<PERSON> <PERSON>", "accentColor": "<PERSON><PERSON> de <PERSON>", "badgeColor": "<PERSON><PERSON> <PERSON>", "overlayColor": "<PERSON><PERSON> <PERSON>", "descriptionColor": "<PERSON><PERSON> da Descrição", "buttonColor": "Cor do Botão", "sectionPadding": "Espaçamento da Seção", "cardPadding": "Espaçamento dos Cartões", "borderRadius": "<PERSON><PERSON> da Borda", "columns": "Colunas", "typography": "Tipografia", "titleFontSize": "<PERSON><PERSON><PERSON> da Fonte do Título", "bodyFontSize": "<PERSON><PERSON><PERSON> da Fonte do Corpo", "descriptionFontSize": "<PERSON><PERSON><PERSON> da Fonte da Descrição", "priceFontSize": "Tamanho da Fonte do Preço", "badgeFontSize": "<PERSON><PERSON><PERSON> da Fonte do Badge", "iconSize": "Tamanho do <PERSON>cone", "manageBenefits": "Gerenciar Benefícios", "addBenefit": "<PERSON><PERSON><PERSON><PERSON>", "newBenefit": "Novo Benefício", "benefitTitle": "Título do Benefício", "benefitTitlePlaceholder": "Digite o título do benefício", "benefitDescription": "Descrição do Benefício", "benefitDescriptionPlaceholder": "Digite a descrição do benefício", "benefitIcon": "Ícone do Benefício", "enableBenefit": "Ativar <PERSON>", "enabled": "Ativo", "disabled": "Inativo", "sectionTitle": "T<PERSON><PERSON><PERSON> da <PERSON>ção", "sectionTitlePlaceholder": "Digite o título da seção", "calculatedPrice": "Preço calculado automaticamente: R$", "manageOffers": "Gerenciar Ofertas", "addOffer": "<PERSON><PERSON><PERSON><PERSON>", "newOffer": "Nova Oferta", "offerTitle": "<PERSON><PERSON><PERSON><PERSON>", "offerTitleRequired": "T<PERSON><PERSON><PERSON> da oferta é obrigatório", "offerTitlePlaceholder": "Digite o título da oferta", "offerDescription": "Descrição da Oferta", "offerDescriptionPlaceholder": "Digite a descrição da oferta", "offerDescriptionRequired": "Descrição da oferta é obrigatória", "badge": "Badge", "badgePlaceholder": "Ex: 50% OFF", "discount": "Desconto", "originalPrice": "Preço Original", "finalPrice": "Preço Final", "offerLink": "<PERSON>", "offerLinkPlaceholder": "https://exemplo.com/oferta", "invalidUrl": "URL inválida", "linkText": "Texto do Link", "linkTextPlaceholder": "<PERSON><PERSON> mais", "linkTextHelp": "Texto que aparece no botão da oferta", "activeOfferHelp": "Desmarque para ocultar esta oferta", "activeOffer": "Oferta Ativa", "active": "Ativo", "inactive": "Inativo", "optional": "Opcional - aparece como destaque na oferta", "discountValue": "Valor do desconto aplicado", "offerPreview": "Preview da Oferta", "noOffersFound": "<PERSON><PERSON><PERSON><PERSON> of<PERSON>a encontrada", "createFirstOffer": "Crie sua primeira oferta especial para começar.", "selectionProblem": "Problema na Seleção", "availableOffers": "Ofertas disponíveis", "currentOfferIndex": "<PERSON><PERSON><PERSON> da Oferta Atual", "sectionTitleFontSize": "<PERSON><PERSON><PERSON> da Fonte do Título Principal", "cardTitleFontSize": "<PERSON><PERSON><PERSON> da Fonte dos Títulos dos Cartões", "specialOffersSubtitle": "Aproveite nossas promoções exclusivas com descontos imperdíveis", "finalPriceMustBeLessThanOriginalPrice": "Preço final deve ser menor que o preço original", "fixSelection": "<PERSON><PERSON><PERSON><PERSON>", "selectOffer": "Selecionar Oferta", "duplicate": "Duplicar", "reset": "<PERSON><PERSON><PERSON>", "manageSlides": "Gerenciar Slides", "addSlide": "<PERSON><PERSON><PERSON><PERSON>", "newSlide": "Novo Slide", "slideTitle": "Título do Slide", "slideTitlePlaceholder": "Digite o título do slide", "slideDescription": "Descrição do Slide", "slideDescriptionPlaceholder": "Digite a descrição do slide", "slideImage": "Imagem do Slide", "slideLink": "Link do Slide", "slideLinkPlaceholder": "https://exemplo.com", "activeSlide": "Slide Ativo", "autoPlay": "Reprodução Automática", "enableAutoPlay": "Ativar Reprodução Automática", "autoPlaySpeed": "Velocidade da Reprodução", "showArrows": "<PERSON><PERSON>", "enableArrows": "Ativar <PERSON>", "showDots": "<PERSON><PERSON>", "enableDots": "Ativar <PERSON>", "transition": "Transição", "dimensions": "Dimensões", "height": "Altura", "overlayOpacity": "Opacidade do Overlay"}, "iconeSettings": "Configurações de Ícone", "showIcons": "<PERSON><PERSON>", "iconPosition": "Posição do Ícone", "iconLeft": "E<PERSON>rda", "iconTop": "Topo", "iconRight": "<PERSON><PERSON><PERSON>", "iconBottom": "Embaixo", "standardLayout": "Layout <PERSON>", "invertedLayout": "Layout Invertido", "spacingBetweenItems": "Espaçamento entre Itens", "enterTitle": "Digite o título", "titulo": "<PERSON><PERSON><PERSON><PERSON>", "editHeader": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "editFooter": "<PERSON><PERSON><PERSON><PERSON>", "storeName": "<PERSON><PERSON>", "storeNamePlaceholder": "Digite o nome da loja", "navigation": "Navegação", "showNavigationLinks": "Mostrar Links de Navegação", "navigationLinks": "Links de Navegação", "addLink": "Adicionar <PERSON>", "linkTitle": "Título do <PERSON>", "showCopyright": "Mostrar Copyright", "copyrightText": "Texto do Copyright", "copyrightPlaceholder": "Digite o texto do copyright", "madeWith": "Feito com", "showMadeWith": "Mostrar 'Feito com'", "madeWithText": "Texto 'Feito com'", "madeWithPlaceholder": "Feito com", "brandName": "<PERSON>me da Marca", "brandNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON>", "brandLink": "<PERSON>", "brandLinkPlaceholder": "https://www.lightspeedhq.com/products/ecommerce/", "contact": "Contato", "contactInformation": "Informações de Contato", "showContactInfo": "Mostrar Informações de Contato", "contactEmail": "Email <PERSON>o", "contactEmailPlaceholder": "<EMAIL>", "contactPhone": "Telefone de Contato", "contactPhonePlaceholder": "(11) 99999-9999", "contactAddress": "Endereço", "contactAddressPlaceholder": "Rua <PERSON>ample, 123 - São Paulo, SP", "social": "Social", "socialNetworks": "Redes Sociais", "showSocialNetworks": "Mostrar Redes Sociais", "facebookUrl": "URL do Facebook", "facebookPlaceholder": "https://facebook.com/suapagina", "instagramUrl": "URL do Instagram", "instagramPlaceholder": "https://instagram.com/seuusuario", "twitterUrl": "URL do Twitter", "twitterPlaceholder": "https://twitter.com/seuusuario", "whatsappUrl": "URL do WhatsApp", "whatsappPlaceholder": "https://wa.me/5511999999999", "design": "Design", "layoutStyle": "Estilo do <PERSON>", "multiColumn": "Multi-colunas", "singleColumn": "Coluna única", "linkColor": "Cor dos Links", "linkHoverColor": "<PERSON><PERSON> dos <PERSON>s (Hover)", "benefits": "Benefí<PERSON>s", "generalConfiguration": "Configuração Geral", "sectionTitle": "T<PERSON><PERSON><PERSON> da <PERSON>ção", "sectionTitlePlaceholder": "Digite o título da seção", "enabled": "Ativo", "disabled": "Inativo", "addBenefit": "<PERSON><PERSON><PERSON><PERSON>", "benefitConfiguration": "Configuração do Benefício", "benefitTitle": "Título do Benefício", "benefitTitlePlaceholder": "Digite o título do benefício", "benefitDescription": "Descrição do Benefício", "benefitDescriptionPlaceholder": "Digite a descrição do benefício", "benefitIcon": "Ícone do Benefício", "enableBenefit": "Ativar <PERSON>", "iconConfiguration": "Configuração de Ícones", "iconSize": "Tamanho do <PERSON>cone", "icons": "Ícones", "style": "<PERSON><PERSON><PERSON>", "ourBenefits": "<PERSON><PERSON><PERSON>", "benefit": "<PERSON><PERSON><PERSON><PERSON>", "benefitDescriptionExample": "Descrição breve do benefício", "behaviorConfiguration": "Configuração de Comportamento", "dimensionsConfiguration": "Configuração de Dimensões", "textConfiguration": "Configuração de Texto", "buttonConfiguration": "Configuração de Botão", "navigationConfiguration": "Configuração de Navegação", "mobileConfiguration": "Configuração Mobile", "styleConfiguration": "Configuração de Estilo", "paddingConfiguration": "Configuração de Padding", "marginConfiguration": "Configuração de Margin", "borderRadiusConfiguration": "Configuração do Raio da Borda", "companyTitlePlaceholder": "<PERSON><PERSON><PERSON><PERSON> da empresa", "companyDescriptionPlaceholder": "Descrição da empresa", "layoutInverted": "Invertido", "descriptionColor": "<PERSON><PERSON> da Descrição", "solidColor": "<PERSON><PERSON>", "transparent": "Transparente", "boxShadow": "Sombra", "buttonPrimaryColor": "Cor Primária do Botão", "buttonScale": "Escala do Botão", "animationType": "Tipo de Animação", "animationSettings": "Configurações de Animação", "advanced": "Avançado", "animationTypeConfiguration": "Configuração do Tipo de Animação", "animationTrigger": "Gatilho da Animação", "fadeIn": "Fade In", "slideInLeft": "<PERSON><PERSON><PERSON>", "slideInRight": "<PERSON><PERSON><PERSON> Direita", "slideInUp": "<PERSON><PERSON><PERSON>", "slideInDown": "<PERSON><PERSON><PERSON>", "zoomIn": "Zoom In", "zoomOut": "Zoom Out", "rotateIn": "Rotacionar", "bounceIn": "Bounce In", "flipHorizontal": "<PERSON><PERSON>", "flipVertical": "Flip Vertical", "pulse": "Pulse", "shake": "Shake", "custom": "Personalizada", "onLoad": "<PERSON><PERSON>", "onHover": "Ao Passar o Mouse", "onScroll": "<PERSON>uando Aparecer na Tela", "onClick": "Ao Clicar", "continuous": "<PERSON><PERSON><PERSON><PERSON>", "animationSettingsConfiguration": "Configuração das Configurações de Animação", "duration": "Duração", "timingFunction": "Curva de Animação", "smooth": "Suave", "acceleration": "Aceleração", "deceleration": "Desaceleração", "smoothStartEnd": "Suave Início/Fim", "bounce": "<PERSON><PERSON><PERSON>", "delay": "Atraso", "iterations": "Repetições", "once": "1 vez", "twice": "2 vezes", "thrice": "3 vezes", "infinite": "Infinito", "advancedConfiguration": "Configuração Avançada", "customKeyframes": "Keyframes Personalizados", "keyframesPlaceholder": "0% { transform: scale(1); }\n50% { transform: scale(1.2); }\n100% { transform: scale(1); }", "keyframesHelp": "Digite os keyframes CSS personalizados", "animationPreview": "Visualização da Animação", "playPreview": "Reproduzir Preview", "removeAnimation": "Remover Animação", "descriptionSize": "<PERSON><PERSON><PERSON> da Descrição", "rotation": "Rotação", "scale": "Escala", "position": "Posição", "rotationConfiguration": "Configuração de Rotação", "rotationAngle": "Ângulo de Rotação", "quickAngles": "<PERSON><PERSON><PERSON>", "scaleConfiguration": "Configuração de Escala", "horizontalScale": "Escala <PERSON>", "verticalScale": "Escala Vertical", "maintainAspectRatio": "Manter Proporção", "positionConfiguration": "Configuração de Posição", "horizontalPosition": "Posição Horizontal", "verticalPosition": "Posição Vertical", "transformPreview": "Visualização da Transformação", "border": "<PERSON><PERSON>", "borderConfiguration": "Configuração de Borda", "borderWidth": "<PERSON><PERSON><PERSON>", "borderStyle": "<PERSON><PERSON><PERSON>", "borderColor": "<PERSON><PERSON>", "solid": "<PERSON><PERSON><PERSON><PERSON>", "dashed": "Trace<PERSON>da", "dotted": "<PERSON><PERSON><PERSON><PERSON>", "double": "<PERSON><PERSON><PERSON>", "radiusCorners": "Cantos do Raio", "shadowConfiguration": "Configuração de Sombra", "shadowOffsetX": "Deslocamento X da Sombra", "shadowOffsetY": "Deslocamento Y da Sombra", "shadowBlur": "Desfoque da Sombra", "shadowSpread": "Expansão da Sombra", "shadowColor": "<PERSON><PERSON> So<PERSON>ra", "shadowPresets": "Presets de Sombra", "basicFilters": "Filtros Básicos", "colorFilters": "Filtros de Cor", "effects": "Efeitos", "basicFiltersConfiguration": "Configuração de Filtros Básicos", "colorFiltersConfiguration": "Configuração de Filtros de Cor", "effectsConfiguration": "Configuração de Efeitos", "filterPreview": "Visualização dos Filtros", "filterPresets": "Presets de Filtros", "opacity": "Opacidade", "showTitle": "<PERSON><PERSON>", "showSubtitle": "Mostrar <PERSON>título", "elements": "Elementos", "showStars": "Mostrar Estrelas", "showAvatar": "Mostrar Ava<PERSON>", "starColor": "<PERSON><PERSON> das Estrelas", "cardBackgroundColor": "Cor de Fundo dos Cartões", "cardBorderRadius": "Raio da Borda dos Cartões", "cardShadow": "Sombra dos Cartões", "lightShadow": "Sombra Leve", "heavyShadow": "Sombra Forte", "styling": "Estilização", "column": "Coluna", "columns": "Colunas", "enterSubtitle": "Digite o subtítulo", "video": "Vídeo", "layoutCreatedSuccess": "Layout '{name}' criado com sucesso!", "layoutCreatedSuccessDesc": "O novo layout está disponível para edição", "layoutCreatedError": "Erro ao criar layout", "layoutCreatedErrorDesc": "Não foi possível criar o layout. Tente novamente."}