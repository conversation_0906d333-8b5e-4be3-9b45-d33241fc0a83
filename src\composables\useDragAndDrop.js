import { ref, nextTick } from 'vue'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import fileManagerService from '@/services/fileManager.service.js'

export function useDragAndDrop() {
  const { t } = useI18n()
  const toast = useToast()
  
  const isDragging = ref(false)
  const draggedItem = ref(null)
  const dragOverTarget = ref(null)
  const isMoving = ref(false)

  // Drag start handler
  const handleDragStart = (event, item) => {
    if (!item || item.source === 'SYSTEM') {
      event.preventDefault()
      return false
    }

    draggedItem.value = item
    isDragging.value = true
    
    // Set drag data
    event.dataTransfer.setData('application/json', JSON.stringify({
      id: item.id,
      name: item.name,
      type: item.type,
      parentFolderId: item.parentFolderId,
      environment: item.environment
    }))
    
    event.dataTransfer.effectAllowed = 'move'
    
    // Add visual feedback
    event.target.style.opacity = '0.5'
    event.target.classList.add('dragging')
  }

  // Drag end handler
  const handleDragEnd = (event) => {
    isDragging.value = false
    draggedItem.value = null
    dragOverTarget.value = null
    
    // Remove visual feedback
    event.target.style.opacity = '1'
    event.target.classList.remove('dragging')
    
    // Remove all drop zone indicators
    document.querySelectorAll('.drop-zone-active').forEach(el => {
      el.classList.remove('drop-zone-active')
    })
  }

  // Drag over handler for drop zones (folders)
  const handleDragOver = (event, targetFolder) => {
    if (!draggedItem.value || !targetFolder || targetFolder.type !== 'FOLDER') {
      return
    }

    // Prevent dropping on itself or its children
    if (draggedItem.value.id === targetFolder.id || 
        isChildOf(draggedItem.value, targetFolder)) {
      return
    }

    event.preventDefault()
    event.dataTransfer.dropEffect = 'move'
    
    dragOverTarget.value = targetFolder
    
    // Add visual feedback
    event.currentTarget.classList.add('drop-zone-active')
  }

  // Drag leave handler
  const handleDragLeave = (event) => {
    // Only remove if we're actually leaving the element
    if (!event.currentTarget.contains(event.relatedTarget)) {
      event.currentTarget.classList.remove('drop-zone-active')
      dragOverTarget.value = null
    }
  }

  // Drop handler
  const handleDrop = async (event, targetFolder) => {
    event.preventDefault()
    event.currentTarget.classList.remove('drop-zone-active')
    
    if (!draggedItem.value || !targetFolder || targetFolder.type !== 'FOLDER') {
      return
    }

    // Prevent dropping on itself or its children
    if (draggedItem.value.id === targetFolder.id || 
        isChildOf(draggedItem.value, targetFolder)) {
      const errorMessage = draggedItem.value.type === 'FOLDER' 
        ? t('fileManager.cannotMoveFolderToChild')
        : t('fileManager.cannotMoveToChild')
      toast.showError(errorMessage)
      return
    }

    // Prevent dropping in the same folder
    if (draggedItem.value.parentFolderId === targetFolder.id) {
      const infoMessage = draggedItem.value.type === 'FOLDER'
        ? t('fileManager.folderAlreadyInFolder')
        : t('fileManager.alreadyInFolder')
      toast.showInfo(infoMessage)
      return
    }

    await moveItem(draggedItem.value, targetFolder)
  }

  // Move item to target folder
  const moveItem = async (item, targetFolder) => {
    if (isMoving.value) return

    try {
      isMoving.value = true
      
      const updateData = {
        name: item.name,
        parentFolderId: targetFolder.id,
        environment: item.environment
      }

      await fileManagerService.update(item.id, updateData)
      
      const successMessage = item.type === 'FOLDER' 
        ? t('fileManager.folderMoveSuccess', { folder: item.name, target: targetFolder.name })
        : t('fileManager.moveSuccess', { item: item.name, folder: targetFolder.name })
      
      toast.showSuccess(successMessage)

      const result = {
        movedItem: {
          ...item,
          parentFolderId: targetFolder.id,
          parentId: targetFolder.id
        },
        targetFolder,
        originalParentId: item.parentFolderId
      }

      // Force immediate UI update
      await nextTick()

      return result
      
    } catch (error) {
      console.error('Error moving item:', error)
      toast.showError(t('fileManager.moveError'))
      throw error
    } finally {
      isMoving.value = false
    }
  }

  // Check if item is a child of target folder
  const isChildOf = (item, targetFolder) => {
    // Prevent moving folders into themselves or their children
    if (item.type === 'FOLDER' && item.id === targetFolder.id) {
      return true
    }
    
    // Prevent moving into a child folder of the dragged folder
    if (item.type === 'FOLDER' && targetFolder.parentFolderId) {
      let currentParent = targetFolder.parentFolderId
      while (currentParent) {
        if (currentParent === item.id) {
          return true
        }
        // This would need to be enhanced with actual parent traversal
        break
      }
    }
    
    return false
  }

  // Get drag data from event
  const getDragData = (event) => {
    try {
      const data = event.dataTransfer.getData('application/json')
      return data ? JSON.parse(data) : null
    } catch (error) {
      console.error('Error parsing drag data:', error)
      return null
    }
  }

  return {
    isDragging,
    draggedItem,
    dragOverTarget,
    isMoving,
    handleDragStart,
    handleDragEnd,
    handleDragOver,
    handleDragLeave,
    handleDrop,
    moveItem,
    getDragData
  }
}
