/**
 * Serviço centralizado para configuração e injeção de scripts
 * Remove a responsabilidade de configuração do Canvas e View
 */

import { injectProductGridScript } from '@/components/layoutEditor/scripts/ProductGridLoader.vue'
import { useComponentScripts } from '@/composables/useComponentScriptInjection'
import { useComponentRegistry } from '@/composables/useComponentRegistry'

// Importar as funções necessárias do registry
const { 
  getAllComponentSelectors,
  autoInitializeComponents,
  processElementForCanvas
} = useComponentRegistry()

export class ScriptInjectionService {
  constructor(authToken) {
    this.authToken = authToken
  }

  /**
   * Configuração centralizada para limpeza e injeção de scripts standalone
   */
  async processDocumentForExport(doc) {
    
    try {
      // 1. Limpar scripts do editor
      this.removeEditorScripts(doc)
      
      // 2. Limpar produtos estáticos dos grids
      this.cleanStaticProductGrids(doc)
      
      // 2b. Limpar mapas já renderizados em componentes de localização
      this.cleanStaticLocationComponents(doc)
      
      // 3. Injetar scripts standalone
      await this.injectStandaloneScripts(doc)
      
      // 4. Processar componentes automaticamente
      this.processComponentsAutomatically(doc)
      

    } catch (error) {
      console.error('❌ [ScriptInjectionService] Erro no processamento:', error)
      throw error
    }
  }

  /**
   * Remove scripts específicos do editor
   */
  removeEditorScripts(doc) {
    const editorScripts = doc.querySelectorAll('#product-grid-script, #location-component-script, #location-component-standalone-script, script[id*="editor"]')
    editorScripts.forEach(script => script.remove())
    
    const editorStyles = doc.querySelectorAll('#editor-mode-styles, style[id*="editor"]')
    editorStyles.forEach(style => style.remove())
    

  }

  /**
   * Limpa produtos estáticos dos grids
   */
  cleanStaticProductGrids(doc) {
    const productGrids = doc.querySelectorAll('[data-component="dynamic-grid-produtos"], [data-product-grid="true"]')
    
    productGrids.forEach(grid => {

      // Remove atributos de inicialização do editor
      grid.removeAttribute('data-initialized')
      // Limpa o conteúdo estático e deixa apenas comentário
      grid.innerHTML = '<!-- Produtos serão carregados dinamicamente pelo script -->'
    })
    

  }

  /**
   * Limpa conteúdo estático dos componentes de localização
   * Remove mapas já renderizados e marca para reinicialização
   */
  cleanStaticLocationComponents(doc) {
    const locationComponents = doc.querySelectorAll('[data-component="location"]')
    locationComponents.forEach(component => {
      // Remover flag de inicialização
      component.removeAttribute('data-maps-initialized')

      // Limpar containers de mapa para que sejam recriados pelo script standalone
      const maps = component.querySelectorAll('.location-map')
      maps.forEach(map => {
        map.innerHTML = ''
      })
    })

    if (locationComponents.length) {

    }
  }

  /**
   * Injeta scripts standalone para componentes específicos
   */
  async injectStandaloneScripts(doc) {
    // Detectar e injetar script para grids de produtos
    await this.injectProductGridScripts(doc)
    
    // Detectar e injetar script para componentes de localização
    await this.injectLocationScripts(doc)
    
    // Aqui podem ser adicionados outros tipos de scripts
    // await this.injectCarouselScripts(doc)
  }

  /**
   * Injeta scripts específicos para grids de produtos
   */
  async injectProductGridScripts(doc) {
    const productGrids = doc.querySelectorAll('[data-component="product-grid"], [data-component="dynamic-grid-produtos"], [data-product-grid="true"], .iluria-product-grid')

    if (productGrids.length > 0) {

      await injectProductGridScript(doc, this.authToken)

    }
  }

  /**
   * Injeta scripts específicos para componentes de localização
   */
  async injectLocationScripts(doc) {
    const locationComponents = doc.querySelectorAll('[data-component="location"], [data-element-type="location"]')
    if (locationComponents.length > 0) {

      const { injectLocationScript } = await import('@/components/layoutEditor/scripts/LocationLoader.vue')
      injectLocationScript(doc, this.authToken)

    }
  }

  /**
   * Processa componentes automaticamente usando o sistema de registro
   */
  processComponentsAutomatically(doc) {

    
    try {
      // Verificar se a função existe antes de usar
      if (typeof getAllComponentSelectors !== 'function') {
        

        return
      }

      // Processa todos os componentes automaticamente
      const componentSelector = getAllComponentSelectors()

      
      if (componentSelector && componentSelector.length > 0) {
        const allComponents = doc.querySelectorAll(componentSelector)
        
        allComponents.forEach((element, index) => {
          try {
            processElementForCanvas(element)
          } catch (error) {
            console.error(`  ❌ Erro ao processar componente ${index + 1}:`, error)
          }
        })
        
      } else {
        console.warn('⚠️ [ScriptInjectionService] Nenhum seletor de componente encontrado')
      }

      // Inicializa componentes automaticamente
      if (typeof autoInitializeComponents === 'function') {
        autoInitializeComponents(doc)
      } else {
        console.error('❌ [ScriptInjectionService] autoInitializeComponents não é uma função!')
      }
      
    } catch (error) {
      console.error('❌ [ScriptInjectionService] Erro no processamento automático:', error)
    }
  }

  /**
   * Remove atributos e classes específicos do editor
   */
  cleanEditorArtifacts(doc) {
    const allElements = doc.querySelectorAll('*')
    
    allElements.forEach(element => {
      // Remove atributos de edição
      element.removeAttribute('draggable')
      element.removeAttribute('data-editable-element')
      element.removeAttribute('contenteditable')
      
      // Remove classes específicas do editor
      element.classList.remove('editable-element', 'selected-element', 'hover-element', 'selected')
      
      // Remove estilos inline do editor
      this.removeEditorStyles(element)
    })
    
    // Remove classes de modo do body
    if (doc.body) {
      doc.body.classList.remove('edit-mode', 'preview-mode', 'view-mode')
    }
    
  }

  /**
   * Remove estilos inline específicos do editor
   */
  removeEditorStyles(element) {
    if (!element.style) return
    
    // Remove outline azul do editor
    const outlineStr = element.style.outline || ''
    if (outlineStr.includes('#3b82f6') || outlineStr.includes('rgb(59, 130, 246)')) {
      element.style.removeProperty('outline')
      element.style.removeProperty('outline-color')
      element.style.removeProperty('outline-style')
      element.style.removeProperty('outline-width')
      element.style.removeProperty('outline-offset')
    }
    
    // Remove bordas azuis do editor
    const borderStr = element.style.border || ''
    if (borderStr.includes('#3b82f6') || borderStr.includes('rgb(59, 130, 246)')) {
      element.style.removeProperty('border')
    }
    
    // Remove box-shadow azul do editor
    const boxShadowStr = element.style.boxShadow || ''
    if (boxShadowStr.includes('#3b82f6') || boxShadowStr.includes('rgb(59, 130, 246)')) {
      element.style.removeProperty('box-shadow')
    }
    
    // Remove background azul transparente do editor
    const backgroundStr = element.style.background || element.style.backgroundColor || ''
    if (backgroundStr.includes('rgba(59, 130, 246')) {
      element.style.removeProperty('background')
      element.style.removeProperty('background-color')
    }
  }

  /**
   * Processa documento completo para salvamento (usado no EditorView)
   */
  async processForSaving(doc) {
    
    try {
      // Usar sistema de scripts existente
      const { 
        initializeComponentScriptSystem,
        injectAllComponentScripts
      } = useComponentScripts()
      
      // Inicializar sistema de scripts
      initializeComponentScriptSystem()
      
      // Injetar todos os scripts necessários
      injectAllComponentScripts(doc, this.authToken)
        
      // Adicionar marcador de debug
      const debugComment = doc.createComment('ILURIA SCRIPT INJECTION SYSTEM ACTIVE - ' + new Date().toISOString())
      if (doc.body) {
        doc.body.appendChild(debugComment)
      }
      
    } catch (error) {
      console.error('❌ Erro ao processar para salvamento:', error)
      throw error
    }
  }
}

/**
 * Factory function para criar uma instância do serviço
 */
export function createScriptInjectionService(authToken) {
  return new ScriptInjectionService(authToken)
}

/**
 * Configurações padrão do serviço
 */
export const SCRIPT_INJECTION_CONFIG = {
  productGrid: {
    selectors: ['[data-component="dynamic-grid-produtos"]', '[data-product-grid="true"]'],
    cleanContent: '<!-- Produtos serão carregados dinamicamente pelo script -->',
    attributesToRemove: ['data-initialized']
  },
  editor: {
    scriptSelectors: ['#product-grid-script', 'script[id*="editor"]'],
    styleSelectors: ['#editor-mode-styles', 'style[id*="editor"]'],
    classesToRemove: ['editable-element', 'selected-element', 'hover-element', 'selected'],
    attributesToRemove: ['draggable', 'data-editable-element', 'contenteditable'],
    bodyClassesToRemove: ['edit-mode', 'preview-mode', 'view-mode']
  }
} 