import { ref, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth.store'

const progressData = ref(null)
const isConnected = ref(false)
const error = ref(null)

let stompClient = null
let subscription = null

export function useThemeProgress() {
  const authStore = useAuthStore()

  const connect = () => {
    if (!authStore.user?.email) {
      error.value = 'Usuário não autenticado'
      return
    }

    try {
      isConnected.value = true
      
    } catch (err) {
      error.value = err.message
      isConnected.value = false
    }
  }

  const disconnect = () => {
    if (subscription) {
      subscription.unsubscribe()
      subscription = null
    }
    
    if (stompClient && stompClient.connected) {
      stompClient.disconnect()
    }
    
    isConnected.value = false
    progressData.value = null
  }

  const simulateProgress = (operation, themeId) => {
    const steps = [
      { step: 'THEME_ACTIVATION_STARTED', progress: 10, message: 'Iniciando ativação do tema...' },
      { step: 'THEME_TEMPLATES_SYNCING', progress: 50, message: 'Sincronizando templates...' },
      { step: 'THEME_ACTIVATED', progress: 100, message: 'Tema ativado com sucesso!' }
    ]

    let currentStep = 0
    const interval = setInterval(() => {
      if (currentStep < steps.length) {
        progressData.value = {
          themeId,
          operation,
          ...steps[currentStep],
          timestamp: Date.now()
        }
        currentStep++
      } else {
        clearInterval(interval)
        setTimeout(() => {
          progressData.value = null
        }, 2000)
      }
    }, 1000)

    return interval
  }

  const resetProgress = () => {
    progressData.value = null
  }

  onMounted(() => {
    if (authStore.isAuthenticated) {
      connect()
    }
  })

  onUnmounted(() => {
    disconnect()
  })

  return {
    progressData,
    isConnected,
    error,

    connect,
    disconnect,
    simulateProgress,
    resetProgress
  }
}