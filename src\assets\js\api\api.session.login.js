import { <PERSON><PERSON><PERSON> } from "./api.js";
import userStore from "@/stores/auth.store.js"

export default class extends BaseApi {

    #endpoint = "http://localhost:8081/auth/login";
    #username = undefined;
    #password = undefined;

    withUsernameAndPassword(username, password) {
        this.#username = username;
        this.#password = password;
        return this;
    }

    async login() {
        const headers = new Headers({
            Authorization: "Basic " + btoa(this.#username + ":" + this.#password)
        });

        try {
            const response = await fetch(this.#endpoint, {
            method: 'POST',
            headers: headers
            });

            if (!response.ok) {
            throw new Error('Login failed');
            }

            const data = await response.json();
            this.loginSuccess(data);
        } catch (error) {
            this.loginError(error);
        }
    }

    loginSuccess(response) {
        localStorage.setItem('jwt', response.jwt);
        userStore().authenticate(response.userName, response.jwt)
        //localStorage.setItem(response);
        // Call the success callback if needed
        super.processOnSuccessCallback(response);
    }

    loginError(error) {
        localStorage.removeItem('jwt');
        this.errorMessage = error.message || 'An error occurred';
        super.processNetworkResponseError(error);
    }
}
