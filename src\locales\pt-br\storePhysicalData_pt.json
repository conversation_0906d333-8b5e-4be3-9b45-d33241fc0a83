{"title": "<PERSON><PERSON> da Loja", "subtitle": "<PERSON><PERSON><PERSON> as informações básicas da sua loja", "saveButton": "<PERSON><PERSON>", "loadError": "Erro ao carregar dados da loja", "saveSuccess": "Dados salvos com sucesso!", "saveError": "Erro ao salvar dados da loja", "storeDataTitle": "<PERSON><PERSON> da Loja", "storeDataSubtitle": "Informações básicas e de contato da loja", "storeName": "<PERSON><PERSON>", "storeType": "<PERSON><PERSON><PERSON>", "personType": {"fisica": "Pessoa Física", "juridica": "Pessoa <PERSON>í<PERSON>"}, "document": "Documento", "cpf": "CPF", "cnpj": "CNPJ", "address": "Endereço", "city": "Cidade", "state": "Estado", "zipCode": "CEP", "email": "Email", "phone": "Telefone", "responsibleTitle": "Pessoa Responsável pela <PERSON>", "responsibleSubtitle": "Informações da pessoa responsável", "responsibleName": "Nome <PERSON>to", "responsibleDocument": "CPF", "responsibleBirthdate": "Data de Nascimento", "responsibleEmail": "Email", "responsiblePhone": "Telefone", "complement": "Complemento", "requiredField": "Todos os campos são obrigatórios"}