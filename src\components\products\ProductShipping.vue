<template>
  <div class="shipping-container" :class="{ 'shipping-expanded': modelShippingFixed === 'YES' && modelShippingFree === 'NO' }">
      <div class="flex flex-row justify-between items-center">
          <IluriaLabel>{{ t('product.shipping.free') }}</IluriaLabel>
          <IluriaToggleSwitch
              id="shippingFree"
              :modelValue="modelShippingFree === 'YES'"
              @update:modelValue="updateShippingFree"
          />
      </div>

      <div v-if="modelShippingFree === 'NO'" class="flex flex-row justify-between items-center">
          <IluriaLabel>{{ t('product.shipping.fixed') }}</IluriaLabel>
          <IluriaToggleSwitch
              id="shippingFixed"
              :modelValue="modelShippingFixed === 'YES'"
              @update:modelValue="updateShippingFixed"
          />
      </div>

      <div v-if="modelShippingFixed === 'YES' && modelShippingFree === 'NO'" class="flex flex-col gap-4 shipping-fields-container">
          <!-- Primeira linha: Valor do frete fixo e Usar o frete fixo até (lado a lado) -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <IluriaInputText
                  id="shippingFixedValue"
                  :label="t('product.shipping.value')"
                  type="money"
                  prefix="R$"
                  :modelValue="modelShippingFixedValue"
                  @update:modelValue="updateShippingFixedValue"
                  :formContext="formContext"
                  :placeholder="t('product.shipping.valuePlaceholder')"
              />
              <IluriaInputText
                  id="shippingFixedMaxUnities"
                  :label="t('product.shipping.maxUnities')"
                  type="number"
                  class="units-input"
                  :suffix="getUnitText(modelShippingFixedMaxUnities)"
                  :modelValue="modelShippingFixedMaxUnities"
                  @update:modelValue="updateShippingFixedMaxUnities"
                  :formContext="formContext"
                  placeholder=""
              />
          </div>
          
          <!-- Segunda linha: Valor do frete combinado (mesmo tamanho dos outros campos) -->
          <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
              <IluriaInputText
                  id="shippingFixedCombinedValue"
                  :label="t('product.shipping.combinedValue')"
                  type="money"
                  prefix="R$"
                  :modelValue="modelShippingFixedCombinedValue"
                  @update:modelValue="updateShippingFixedCombinedValue"
                  :formContext="formContext"
                  :placeholder="t('product.shipping.combinedValuePlaceholder')"
              />
          </div>
      </div>
      

  </div>

</template>
<script setup>
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import IluriaInputText from '@/components/Iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import { useI18n } from 'vue-i18n'
import { computed } from 'vue'
const { t } = useI18n()
const props = defineProps({
formContext: {
  type: Object,
  required: true
},
shippingFree: {
  type: String,
  default: 'NO'
},
shippingFixed: {
  type: String,
  default: 'NO'
},
shippingFixedValue: {
  type: String,
  default: ''
},
shippingFixedCombinedValue: {
  type: String,
  default: ''
},
shippingFixedMaxUnities: {
  type: String,
  default: ''
},
boxLengthValue: {
  type: String,
  default: ''
},
boxWidthValue: {
  type: String,
  default: ''
},
boxHeightValue: {
  type: String,
  default: ''
},
weightValue: {
  type: String,
  default: ''
},
hasVariation: {
  type: Boolean,
  default: false
}
});
const emit = defineEmits([
'update:shippingFree',
'update:shippingFixed',
'update:shippingFixedValue',
'update:shippingFixedCombinedValue',
'update:shippingFixedMaxUnities',
'update:boxLengthValue',
'update:boxWidthValue',
'update:boxHeightValue',
'update:weightValue'
]);

// Função para retornar o texto da unidade baseado na quantidade
const getUnitText = (quantity) => {
  const numValue = Number(quantity) || 0;
  return numValue > 1 ? 'unidades' : 'unidade';
};

const modelShippingFree = computed(() => props.shippingFree);
const modelShippingFixed = computed(() => props.shippingFixed);
const modelShippingFixedValue = computed(() => props.shippingFixedValue);
const modelShippingFixedCombinedValue = computed(() => props.shippingFixedCombinedValue);
const modelShippingFixedMaxUnities = computed(() => props.shippingFixedMaxUnities);

function updateShippingFree(value) {
const stringValue = value ? 'YES' : 'NO';
emit('update:shippingFree', stringValue);

if (stringValue === 'YES') {
  // Apenas resetar campos de frete fixo, não as dimensões
  emit('update:shippingFixed', 'NO');
  resetFixedShippingFields();
}
}
function updateShippingFixed(value) {
const stringValue = value ? 'YES' : 'NO';
emit('update:shippingFixed', stringValue);

if (stringValue === 'NO') {
  resetFixedShippingFields();
}
// Não resetar dimensões quando frete fixo é habilitado - elas podem ser necessárias
}
function updateShippingFixedValue(value) {
emit('update:shippingFixedValue', value);
}
function updateShippingFixedCombinedValue(value) {
emit('update:shippingFixedCombinedValue', value);
}
function updateShippingFixedMaxUnities(value) {
emit('update:shippingFixedMaxUnities', value);
}
function resetFixedShippingFields() {
emit('update:shippingFixedValue', '');
emit('update:shippingFixedCombinedValue', '');
emit('update:shippingFixedMaxUnities', '');
}
</script>

<style scoped>
/* Container dinâmico que se adapta ao conteúdo */
.shipping-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: auto;
  height: auto;
}

/* Adicionar altura extra entre os toggles e os campos */
.shipping-fields-container {
  padding-top: 2.92rem;
}

/* Suffix para o campo de unidades - sem negrito */
.units-input :deep(.custom-suffix) {
  font-weight: normal !important;
  color: var(--iluria-color-input-text) !important;
  opacity: 1 !important;
  white-space: nowrap !important;
  overflow: hidden !important;
  min-width: fit-content !important;
  position: absolute !important;
  right: -8px !important;
  padding-left: 4px !important;
  padding-right: 8px !important;
  background-color: var(--iluria-color-input-bg) !important;
  height: 100% !important;
  display: flex !important;
  align-items: center !important;
  border-left: 1px solid var(--iluria-color-input-border) !important;
  z-index: 2 !important;
}

.units-input :deep(.custom-suffix span) {
  font-weight: normal !important;
  color: var(--iluria-color-input-text) !important;
  white-space: nowrap !important;
  padding: 0 4px !important;
}

/* Garantir que o container do input tenha espaço suficiente */
.units-input :deep(.custom-input-group) {
  width: 100% !important;
  min-width: 0 !important;
  overflow: hidden !important;
  position: relative !important;
}

/* Ajustar o input para dar espaço ao suffix */
.units-input :deep(.input-themed-custom) {
  padding-right: 95px !important;
  width: 100% !important;
  position: relative !important;
  z-index: 1 !important;
}

/* Placeholder em cinza que desaparece ao focar */
.units-input :deep(input::placeholder) {
  color: #9ca3af !important;
  opacity: 0.7 !important;
  font-weight: normal !important;
}

.units-input :deep(input::-webkit-input-placeholder) {
  color: #9ca3af !important;
  opacity: 0.7 !important;
  font-weight: normal !important;
}

.units-input :deep(input::-moz-placeholder) {
  color: #9ca3af !important;
  opacity: 0.7 !important;
  font-weight: normal !important;
}

.units-input :deep(input:-ms-input-placeholder) {
  color: #9ca3af !important;
  opacity: 0.7 !important;
  font-weight: normal !important;
}

/* Quando o campo tem foco, o placeholder desaparece automaticamente */
.units-input :deep(input:focus::placeholder) {
  opacity: 0 !important;
}
</style>

<style>
/* Estilos globais para o suffix - sem negrito */
.units-input .custom-suffix,
.units-input .custom-suffix span {
  font-weight: normal !important;
  color: var(--iluria-color-input-text, #1f2937) !important;
  white-space: nowrap !important;
  overflow: visible !important;
  min-width: fit-content !important;
}

/* Garantir visibilidade completa do suffix */
.units-input .custom-input-group {
  overflow: visible !important;
}

.units-input {
  position: relative;
}

.units-input .custom-suffix {
  background: transparent !important;
  padding-left: 8px !important;
  padding-right: 16px !important;
  margin-left: -60px !important;
  margin-right: 8px !important;
  position: relative !important;
  z-index: 1 !important;
}

.units-input :deep(.custom-input-group) {
  padding-right: 4px;
}

/* Placeholder em cinza - estilos globais */
.units-input input::placeholder,
.units-input input::-webkit-input-placeholder,
.units-input input::-moz-placeholder,
.units-input input:-ms-input-placeholder {
  color: #9ca3af !important;
  opacity: 0.7 !important;
  font-weight: normal !important;
}

.units-input .p-inputtext::placeholder,
.units-input .p-inputtext::-webkit-input-placeholder,
.units-input .p-inputtext::-moz-placeholder,
.units-input .p-inputtext:-ms-input-placeholder {
  color: #9ca3af !important;
  opacity: 0.7 !important;
  font-weight: normal !important;
}
</style>