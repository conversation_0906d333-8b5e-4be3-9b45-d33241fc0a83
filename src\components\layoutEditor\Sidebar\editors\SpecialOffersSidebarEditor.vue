<template>
  <div class="special-offers-sidebar-editor">
    <!-- Tabs para separar funcionalidades -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.id"
        :class="['tab-button', { active: activeTab === tab.id }]"
        @click="activeTab = tab.id"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Conteúdo das Tabs -->
    <div class="tab-content">
      <!-- Aba Geral -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        <div class="section">
          <IluriaLabel>{{ $t('layoutEditor.sidebar.sectionTitle') }}</IluriaLabel>
          <IluriaInputText
            v-model="config.title"
            :placeholder="$t('layoutEditor.sidebar.sectionTitlePlaceholder')"
            @input="onConfigDataChange"
          />
        </div>

        <div class="section">
          <IluriaLabel>{{ $t('layoutEditor.sidebar.subtitle') }}</IluriaLabel>
          <textarea
            v-model="config.subtitle"
            :placeholder="$t('layoutEditor.sidebar.subtitlePlaceholder')"
            @input="onConfigDataChange"
            :rows="2"
            class="iluria-textarea"
          />
        </div>

        <div class="offers-header">
          <h3>{{ $t('layoutEditor.sidebar.manageOffers') }}</h3>
          <IluriaButton @click="addOffer" variant="primary">
            {{ $t('layoutEditor.sidebar.addOffer') }}
          </IluriaButton>
        </div>

        <div class="offers-list" v-if="offers.length > 0">
          <div 
            v-for="(offer, index) in offers" 
            :key="offer.id"
            :class="['offer-item', { active: currentOfferIndex === index }]"
            @click="selectOffer(index)"
          >
            <div class="offer-preview">
              <div class="offer-badge" v-if="offer.badge">{{ offer.badge }}</div>
              <div class="offer-info">
                <h4>{{ offer.title || $t('layoutEditor.sidebar.newOffer') }}</h4>
                <p>{{ truncateText(offer.description, 60) }}</p>
                <span :class="['offer-status', { active: offer.active }]">
                  {{ offer.active ? $t('layoutEditor.sidebar.active') : $t('layoutEditor.sidebar.inactive') }}
                </span>
              </div>
            </div>
            <div class="offer-controls">
              <IluriaButton 
                @click.stop="moveOfferUp(index)"
                :disabled="index === 0"
                variant="secondary"
                :title="$t('layoutEditor.sidebar.moveUp')"
              >
                ↑
              </IluriaButton>
              <IluriaButton 
                @click.stop="moveOfferDown(index)"
                :disabled="index === offers.length - 1"
                variant="secondary"
                :title="$t('layoutEditor.sidebar.moveDown')" 
              >
                ↓
              </IluriaButton>
              <IluriaButton 
                @click.stop="removeOffer(index)"
                variant="danger"
                :title="$t('layoutEditor.sidebar.remove')"
              >
                ✕
              </IluriaButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Aba Editor de Oferta -->
      <div v-if="activeTab === 'offers'" class="tab-panel">
        <!-- Seletor de Oferta -->
        <div v-if="offers.length > 0" class="offer-selector">
          <div class="section">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.selectOffer') }}</IluriaLabel>
            <div class="offer-selector-wrapper">
              <IluriaSelect 
                v-model.number="currentOfferIndex" 
                @change="onOfferSelectionChange"
                class="offer-select"
                :options="offerSelectOptions"
              />
              <div class="offer-actions">
                <IluriaButton 
                  @click="duplicateCurrentOffer"
                  variant="secondary" 
                  size="sm"
                  :title="$t('layoutEditor.sidebar.duplicate')"
                >
                  📋
                </IluriaButton>
                <IluriaButton 
                  @click="resetCurrentOffer"
                  variant="secondary" 
                  size="sm"
                  :title="$t('layoutEditor.sidebar.reset')"
                >
                  🔄
                </IluriaButton>
                <IluriaButton 
                  @click="removeCurrentOffer"
                  variant="danger" 
                  size="sm"
                  :title="$t('layoutEditor.sidebar.remove')"
                  :disabled="offers.length <= 1"
                >
                  🗑️
                </IluriaButton>
              </div>
            </div>
          </div>
        </div>

        <!-- Editor da Oferta -->
        <div v-if="isCurrentOfferValid" class="offer-editor">
          <div class="section">
            <IluriaLabel>{{$t('layoutEditor.sidebar.offerTitle')}}</IluriaLabel>
            <IluriaInputText
              v-model="currentOffer.title"
              :placeholder="$t('layoutEditor.sidebar.offerTitlePlaceholder')"
              @input="onOfferDataChange"
              :class="{ 'error': !currentOffer.title?.trim() }"
            />
            <div v-if="!currentOffer.title?.trim()" class="field-error">
              {{ $t('layoutEditor.sidebar.offerTitleRequired') }}
            </div>
          </div>

          <div class="section">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.offerDescription') }}</IluriaLabel>
            <textarea
              v-model="currentOffer.description"
              :placeholder="$t('layoutEditor.sidebar.offerDescriptionPlaceholder')"
              @input="onOfferDataChange"
              :rows="3"
              class="iluria-textarea"
              :class="{ 'error': !currentOffer.description?.trim() }"
            />
            <div v-if="!currentOffer.description?.trim()" class="field-error">
              {{ $t('layoutEditor.sidebar.offerDescriptionRequired') }}
            </div>
          </div>

          <div class="section">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.badge') }}</IluriaLabel>
            <IluriaInputText
              v-model="currentOffer.badge"
             :placeholder="$t('layoutEditor.sidebar.badgePlaceholder')"
              @input="onOfferDataChange"
            />
            <small class="field-help">{{ $t('layoutEditor.sidebar.optional') }}</small>
          </div>

          <div class="section">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.discount') }}</IluriaLabel>
            <div class="discount-inputs">
              <IluriaInputText
                v-model.number="currentOffer.discount.value"
                type="number"
                placeholder="50"
                min="0"
                max="100"
                @input="onOfferDataChange"
                @blur="validateDiscount"
              />
              <IluriaSelect 
                v-model="currentOffer.discount.type" 
                @change="onOfferDataChange"
                :options="discountTypeOptions"
              />
            </div>
            <small class="field-help">{{ $t('layoutEditor.sidebar.discountValue') }}</small>
          </div>

          <div class="pricing-section">
            <div class="section">
              <IluriaLabel>{{ $t('layoutEditor.sidebar.originalPrice') }}</IluriaLabel>
              <IluriaInputText
                v-model.number="currentOffer.originalPrice"
                type="number"
                step="0.01"
                min="0"
                placeholder="199.90"
                @input="onOfferDataChange"
                @blur="calculateFinalPrice"
              />
            </div>

            <div class="section">
              <IluriaLabel>{{ $t('layoutEditor.sidebar.finalPrice') }}</IluriaLabel>
              <IluriaInputText
                v-model.number="currentOffer.finalPrice"
                type="number"
                step="0.01"
                min="0"
                placeholder="99.90"
                @input="onOfferDataChange"
              />
              <small class="field-help" v-if="calculatedPrice !== null">
                {{ $t('layoutEditor.sidebar.calculatedPrice') }} {{ calculatedPrice?.toFixed(2).replace('.', ',') }}
              </small>
            </div>

            <div v-if="priceValidationError" class="field-error">
              {{ priceValidationError }}
            </div>
          </div>

          <div class="section">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.offerLink') }}</IluriaLabel>
            <IluriaInputText
              v-model="currentOffer.linkUrl"
              type="url"
              :placeholder="$t('layoutEditor.sidebar.offerLinkPlaceholder')"
              @input="onOfferDataChange"
              :class="{ 'error': currentOffer.linkUrl && !isValidUrl(currentOffer.linkUrl) }"
            />
            <div v-if="currentOffer.linkUrl && !isValidUrl(currentOffer.linkUrl)" class="field-error">
              {{ $t('layoutEditor.sidebar.invalidUrl') }}
            </div>
          </div>

          <div class="section">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.linkText') }}</IluriaLabel>
            <IluriaInputText
              v-model="currentOffer.linkText"
              :placeholder="$t('layoutEditor.sidebar.linkTextPlaceholder')"
              @input="onOfferDataChange"
            />
            <small class="field-help">{{ $t('layoutEditor.sidebar.linkTextHelp') }}</small>
          </div>

          <div class="section">
            <IluriaCheckBox
              v-model="currentOffer.active"
              @update:modelValue="onOfferDataChange"
            >
              {{ $t('layoutEditor.sidebar.activeOffer') }}
            </IluriaCheckBox>
            <small class="field-help">{{ $t('layoutEditor.sidebar.activeOfferHelp') }}</small>
          </div>

          <!-- Preview da Oferta -->
          <div class="offer-preview-section">
            <h4>{{ $t('layoutEditor.sidebar.offerPreview') }}</h4>
            <div class="offer-preview-card" :style="previewCardStyle">
              <div v-if="currentOffer.badge" class="preview-badge" :style="previewBadgeStyle">
                {{ currentOffer.badge }}
              </div>
              <h3 class="preview-title" :style="previewTitleStyle">
                {{ currentOffer.title || 'Título da oferta' }}
              </h3>
              <p class="preview-description" :style="previewDescriptionStyle">
                {{ currentOffer.description || 'Descrição da oferta' }}
              </p>
              <div v-if="currentOffer.originalPrice > 0" class="preview-pricing">
                <span class="preview-original-price">
                  R$ {{ currentOffer.originalPrice?.toFixed(2).replace('.', ',') }}
                </span>
                <span class="preview-final-price" :style="previewPriceStyle">
                  R$ {{ currentOffer.finalPrice?.toFixed(2).replace('.', ',') }}
                </span>
              </div>
              <div v-if="currentOffer.linkText" class="preview-button" :style="previewButtonStyle">
                {{ currentOffer.linkText }}
              </div>
            </div>
          </div>
        </div>

        <!-- Mensagem quando não há ofertas -->
        <template v-else-if="offers.length === 0">
          <div class="no-offers">
            <h3>{{ $t('layoutEditor.sidebar.noOffersFound') }}</h3>
            <p>{{ $t('layoutEditor.sidebar.createFirstOffer') }}</p>
            <IluriaButton @click="addOffer" variant="primary">
              {{ $t('layoutEditor.sidebar.addOffer') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Fallback quando há ofertas mas currentOffer é inválido -->
        <template v-else>
          <div class="debug-info">
            <h3>{{ $t('layoutEditor.sidebar.selectionProblem') }}</h3>
            <p>{{ $t('layoutEditor.sidebar.availableOffers') }}: {{ offers.length }}</p>
            <p>{{ $t('layoutEditor.sidebar.currentOfferIndex') }}: {{ currentOfferIndex }}</p>
            <IluriaButton @click="resetOfferSelection" variant="primary">
              {{ $t('layoutEditor.sidebar.fixSelection') }}
            </IluriaButton>
          </div>
        </template>
      </div>

      <!-- Aba Design -->
      <div v-if="activeTab === 'design'" class="tab-panel">
        <!-- Layout -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.sidebar.layout') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.columns') }}</IluriaLabel>
            <IluriaSelect 
              v-model="design.columns" 
              @change="onDesignChange('columns', design.columns)"
              :options="columnOptions"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.spacing') }}</IluriaLabel>
            <IluriaRange
              v-model="design.spacing"
              :min="8"
              :max="64"
              :step="4"
              unit="px"
              @update:modelValue="onDesignChange('spacing', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.cardPadding') }}</IluriaLabel>
            <IluriaRange
              v-model="design.cardPadding"
              :min="12"
              :max="48"
              :step="4"
              unit="px"
              @update:modelValue="onDesignChange('cardPadding', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.borderRadius') }}</IluriaLabel>
            <IluriaRange
              v-model="design.borderRadius"
              :min="0"
              :max="24"
              :step="2"
              unit="px"
              @update:modelValue="onDesignChange('borderRadius', $event)"
            />
          </div>
        </div>

        <!-- Cores -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.sidebar.colors') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.sectionBackground') }}</IluriaLabel>
            <IluriaColorPicker
              v-model="design.sectionBackground"
              @update:modelValue="onDesignChange('sectionBackground', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.cardBackground') }}</IluriaLabel>
            <IluriaColorPicker
              v-model="design.cardBackground"
              @update:modelValue="onDesignChange('cardBackground', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.titleColor') }}</IluriaLabel>
            <IluriaColorPicker
              v-model="design.titleColor"
              @update:modelValue="onDesignChange('titleColor', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.textColor') }}</IluriaLabel>
            <IluriaColorPicker
              v-model="design.textColor"
              @update:modelValue="onDesignChange('textColor', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.accentColor') }}</IluriaLabel>
            <IluriaColorPicker
              v-model="design.accentColor"
              @update:modelValue="onDesignChange('accentColor', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.badgeColor') }}</IluriaLabel>
            <IluriaColorPicker
              v-model="design.badgeColor"
              @update:modelValue="onDesignChange('badgeColor', $event)"
            />
          </div>
        </div>

        <!-- Tipografia -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.sidebar.typography') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.sectionTitleFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="design.sectionTitleFontSize"
              :min="20"
              :max="64"
              :step="2"
              unit="px"
              @update:modelValue="onDesignChange('sectionTitleFontSize', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.cardTitleFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="design.cardTitleFontSize"
              :min="14"
              :max="32"
              :step="2"
              unit="px"
              @update:modelValue="onDesignChange('cardTitleFontSize', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.priceFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="design.priceFontSize"
              :min="16"
              :max="32"
              :step="2"
              unit="px"
              @update:modelValue="onDesignChange('priceFontSize', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.badgeFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="design.badgeFontSize"
              :min="10"
              :max="18"
              :step="1"
              unit="px"
              @update:modelValue="onDesignChange('badgeFontSize', $event)"
            />
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close'])

// Tabs
const activeTab = ref('content')
const tabs = [
  { id: 'content', label: t('layoutEditor.sidebar.content') },
  { id: 'offers', label: t('layoutEditor.sidebar.offers') },
  { id: 'design', label: t('layoutEditor.sidebar.design') }
]

// Estados reativos - Conteúdo
const config = ref({ 
  title: t('layoutEditor.sidebar.specialOffers'),
  subtitle: t('layoutEditor.sidebar.specialOffersSubtitle')
})
const offers = ref([])
const currentOfferIndex = ref(0)

const calculatedPrice = ref(null)
const priceValidationError = ref('')

// Estados reativos - Design
const design = ref({
  columns: '3',
  spacing: 24,
  cardPadding: 20,
  borderRadius: 12,
  sectionBackground: '#ffffff',
  cardBackground: '#f8f9fa',
  titleColor: '#1a1a1a',
  textColor: '#666666',
  accentColor: '#2563eb',
  badgeColor: '#ef4444',
  sectionTitleFontSize: 40,
  cardTitleFontSize: 24,
  priceFontSize: 20,
  badgeFontSize: 12
})

const currentOffer = computed(() => {
  return offers.value[currentOfferIndex.value] || null
})

const isCurrentOfferValid = computed(() => {
  return offers.value.length > 0 &&
         currentOfferIndex.value >= 0 && 
         currentOfferIndex.value < offers.value.length &&
         currentOffer.value !== null
})

// Opções para o select de ofertas
const offerSelectOptions = computed(() => {
  return offers.value.map((offer, index) => ({
    label: offer.title || `Oferta ${index + 1}`,
    value: index
  }))
})

// Opções para tipo de desconto
const discountTypeOptions = [
  { label: '%', value: 'percentage' },
  { label: 'R$', value: 'fixed' }
]

// Opções para colunas do layout
const columnOptions = [
  { label: '1 Coluna', value: '1' },
  { label: '2 Colunas', value: '2' },
  { label: '3 Colunas', value: '3' },
  { label: '4 Colunas', value: '4' }
]

const previewCardStyle = computed(() => ({
  background: design.value.cardBackground,
  borderRadius: design.value.borderRadius + 'px',
  padding: design.value.cardPadding + 'px',
  border: '1px solid #e9ecef',
  position: 'relative',
  transition: 'all 0.3s ease'
}))

const previewBadgeStyle = computed(() => ({
  position: 'absolute',
  top: '-8px',
  right: '16px',
  background: design.value.badgeColor,
  color: 'white',
  padding: '6px 12px',
  borderRadius: '20px',
  fontSize: design.value.badgeFontSize + 'px',
  fontWeight: '600',
  zIndex: '2'
}))

const previewTitleStyle = computed(() => ({
  fontSize: design.value.cardTitleFontSize + 'px',
  fontWeight: '700',
  margin: '0 0 0.5rem',
  color: design.value.titleColor,
  lineHeight: '1.3'
}))

const previewDescriptionStyle = computed(() => ({
  color: design.value.textColor,
  margin: '0 0 1rem',
  lineHeight: '1.4',
  fontSize: '0.95rem'
}))

const previewPriceStyle = computed(() => ({
  color: design.value.accentColor,
  fontSize: design.value.priceFontSize + 'px',
  fontWeight: '700'
}))

const previewButtonStyle = computed(() => ({
  background: design.value.accentColor,
  color: 'white',
  padding: '12px 24px',
  border: 'none',
  borderRadius: '8px',
  fontSize: '0.9rem',
  fontWeight: '600',
  textDecoration: 'none',
  display: 'inline-block',
  transition: 'all 0.3s ease',
  width: '100%',
  textAlign: 'center',
  boxSizing: 'border-box',
  marginTop: '1rem'
}))

const loadData = () => {
  offers.value = getDefaultOffers()
  currentOfferIndex.value = 0
  
  if (!props.element) {
    return
  }
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'special-offers') {
      targetElement = props.element.closest('[data-component="special-offers"]')
    }
    
    if (!targetElement) {
      return
    }
    
    config.value.title = targetElement.getAttribute('data-title') || t('layoutEditor.sidebar.specialOffers')
    config.value.subtitle = targetElement.getAttribute('data-subtitle') || t('layoutEditor.sidebar.specialOffersSubtitle')
    const offersData = targetElement.getAttribute('data-offers')
    if (offersData) {
      try {
        const parsedOffers = JSON.parse(offersData)
        if (Array.isArray(parsedOffers) && parsedOffers.length > 0) {
          offers.value = parsedOffers
        }
      } catch (error) {
        // Manter ofertas padrão em caso de erro
      }
    }

          if (offers.value.length > 0) {
        if (currentOfferIndex.value >= offers.value.length) {
          currentOfferIndex.value = 0
        }
      }
    design.value.columns = targetElement.getAttribute('data-columns') || '3'
    design.value.spacing = parseInt(targetElement.getAttribute('data-spacing')) || 24
    design.value.cardPadding = parseInt(targetElement.getAttribute('data-card-padding')) || 20
    design.value.borderRadius = parseInt(targetElement.getAttribute('data-border-radius')) || 12
    design.value.sectionBackground = targetElement.getAttribute('data-section-background') || '#ffffff'
    design.value.cardBackground = targetElement.getAttribute('data-card-background') || '#f8f9fa'
    design.value.titleColor = targetElement.getAttribute('data-title-color') || '#1a1a1a'
    design.value.textColor = targetElement.getAttribute('data-text-color') || '#666666'
    design.value.accentColor = targetElement.getAttribute('data-accent-color') || '#2563eb'
    design.value.badgeColor = targetElement.getAttribute('data-badge-color') || '#ef4444'
    const sectionTitleSize = targetElement.getAttribute('data-section-title-font-size')
    design.value.sectionTitleFontSize = sectionTitleSize ? parseInt(sectionTitleSize) : 40
    
    initialFontSize.value = design.value.sectionTitleFontSize
    
    const cardTitleSize = targetElement.getAttribute('data-card-title-font-size')
    design.value.cardTitleFontSize = cardTitleSize ? parseInt(cardTitleSize) : 24
    
    const priceSize = targetElement.getAttribute('data-price-font-size')
    design.value.priceFontSize = priceSize ? parseInt(priceSize) : 20
    
    const badgeSize = targetElement.getAttribute('data-badge-font-size')
    design.value.badgeFontSize = badgeSize ? parseInt(badgeSize) : 12
    
  } catch (error) {
  }
}

const saveData = () => {
  if (!props.element) return false
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'special-offers') {
      targetElement = props.element.closest('[data-component="special-offers"]')
    }
    
    if (!targetElement) return false
    
    targetElement.setAttribute('data-title', config.value.title)
    targetElement.setAttribute('data-subtitle', config.value.subtitle)
    targetElement.setAttribute('data-offers', JSON.stringify(offers.value))
    targetElement.setAttribute('data-columns', design.value.columns)
    targetElement.setAttribute('data-spacing', String(design.value.spacing))
    targetElement.setAttribute('data-card-padding', String(design.value.cardPadding))
    targetElement.setAttribute('data-border-radius', String(design.value.borderRadius))
    targetElement.setAttribute('data-section-background', design.value.sectionBackground)
    targetElement.setAttribute('data-card-background', design.value.cardBackground)
    targetElement.setAttribute('data-title-color', design.value.titleColor)
    targetElement.setAttribute('data-text-color', design.value.textColor)
    targetElement.setAttribute('data-accent-color', design.value.accentColor)
    targetElement.setAttribute('data-badge-color', design.value.badgeColor)
    targetElement.setAttribute('data-section-title-font-size', String(design.value.sectionTitleFontSize))
    targetElement.setAttribute('data-card-title-font-size', String(design.value.cardTitleFontSize))
    targetElement.setAttribute('data-price-font-size', String(design.value.priceFontSize))
    targetElement.setAttribute('data-badge-font-size', String(design.value.badgeFontSize))
    
    updateElementContent(targetElement)
    applyStyles(targetElement)
    
    return true
  } catch (error) {
    return false
  }
}

const getDefaultOffers = () => [
  {
    id: 'offer-1',
    title: t('layoutEditor.sidebar.offerTitle'),
    description: t('layoutEditor.sidebar.offerDescription'),
    badge: '50% OFF',
    discount: { value: 50, type: 'percentage' },
    originalPrice: 199.90,
    finalPrice: 99.90,
    linkUrl: '',
    linkText: t('layoutEditor.sidebar.linkText'),
    active: true
  },
  {
    id: 'offer-2',
    title: t('layoutEditor.sidebar.offerTitle'),
    description: t('layoutEditor.sidebar.offerDescription'),
    badge: 'FRETE GRÁTIS',
    discount: { value: 0, type: 'percentage' },
    originalPrice: 0,
    finalPrice: 0,
    linkUrl: '',
    linkText: t('layoutEditor.sidebar.linkText'),
    active: true
  }
]

const selectOffer = (index) => {
  currentOfferIndex.value = index
  activeTab.value = 'offers'
}

const addOffer = () => {
  try {
    const newOffer = {
      id: `offer-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      title: `Oferta ${offers.value.length + 1}`,
      description: t('layoutEditor.sidebar.offerDescription'),
      badge: 'NOVO',
      discount: { value: 10, type: 'percentage' },
      originalPrice: 100,
      finalPrice: 90,
      linkUrl: '',
      linkText: t('layoutEditor.sidebar.linkText'),
      active: true
    }
    
    offers.value.push(newOffer)
    currentOfferIndex.value = offers.value.length - 1
    activeTab.value = 'offers'
    
    setTimeout(() => {
      if (!isUpdating.value) {
        onOfferDataChange()
      }
    }, 100)
  } catch (error) {
    // Falha silenciosa ao adicionar oferta
  }
}

const removeOffer = (index) => {
  try {
    if (offers.value.length <= 1) return
    if (index < 0 || index >= offers.value.length) return
    
    offers.value.splice(index, 1)
    
    if (offers.value.length === 0) {
      currentOfferIndex.value = 0
    } else if (currentOfferIndex.value >= offers.value.length) {
      currentOfferIndex.value = offers.value.length - 1
    } else if (currentOfferIndex.value > index) {
      currentOfferIndex.value = Math.max(0, currentOfferIndex.value - 1)
    }
    
    setTimeout(() => {
      if (!isUpdating.value) {
        onOfferDataChange()
      }
    }, 100)
  } catch (error) {
    // Falha silenciosa ao remover oferta
  }
}

const moveOfferUp = (index) => {
  if (index === 0) return
  
  const offer = offers.value.splice(index, 1)[0]
  offers.value.splice(index - 1, 0, offer)
  
  if (currentOfferIndex.value === index) {
    currentOfferIndex.value = index - 1
  } else if (currentOfferIndex.value === index - 1) {
    currentOfferIndex.value = index
  }
  
  setTimeout(() => onOfferDataChange(), 50)
}

const moveOfferDown = (index) => {
  if (index === offers.value.length - 1) return
  
  const offer = offers.value.splice(index, 1)[0]
  offers.value.splice(index + 1, 0, offer)
  
  if (currentOfferIndex.value === index) {
    currentOfferIndex.value = index + 1
  } else if (currentOfferIndex.value === index + 1) {
    currentOfferIndex.value = index
  }
  
  setTimeout(() => onOfferDataChange(), 50)
}

const onOfferSelectionChange = () => {
  try {
    calculatedPrice.value = null
    priceValidationError.value = ''
    
    if (offers.value.length === 0) {
      currentOfferIndex.value = 0
      return
    }
    
    if (currentOfferIndex.value >= offers.value.length) {
      currentOfferIndex.value = Math.max(0, offers.value.length - 1)
    }
    if (currentOfferIndex.value < 0) {
      currentOfferIndex.value = 0
    }
    
    setTimeout(() => {
      if (isCurrentOfferValid.value) {
        calculateFinalPrice()
      }
    }, 50)
  } catch (error) {
    currentOfferIndex.value = 0
  }
}

const duplicateCurrentOffer = () => {
  if (!currentOffer.value) return
  
  const duplicatedOffer = {
    ...JSON.parse(JSON.stringify(currentOffer.value)),
    id: `offer-${Date.now()}`,
    title: `${currentOffer.value.title} (Cópia)`
  }
  
  offers.value.push(duplicatedOffer)
  currentOfferIndex.value = offers.value.length - 1
  
  setTimeout(() => onOfferDataChange(), 50)
}

const resetCurrentOffer = () => {
  if (!currentOffer.value) return
  
  const defaultOffer = {
    id: currentOffer.value.id,
    title: `Oferta ${currentOfferIndex.value + 1}`,
    description: t('layoutEditor.sidebar.offerDescription'),
    badge: '',
    discount: { value: 10, type: 'percentage' },
    originalPrice: 100,
    finalPrice: 90,
    linkUrl: '',
    linkText: t('layoutEditor.sidebar.linkText'),
    active: true
  }
  
  Object.assign(currentOffer.value, defaultOffer)
  
  setTimeout(() => onOfferDataChange(), 50)
}

const removeCurrentOffer = () => {
  if (offers.value.length <= 1) return
  
  removeOffer(currentOfferIndex.value)
}

const resetOfferSelection = () => {
  if (offers.value.length > 0) {
    currentOfferIndex.value = 0
  }
}

const validateDiscount = () => {
  if (!currentOffer.value) return
  
  const discount = currentOffer.value.discount
  if (discount.type === 'percentage' && discount.value > 100) {
    discount.value = 100
  }
  if (discount.value < 0) {
    discount.value = 0
  }
  
  calculateFinalPrice()
}

const calculateFinalPrice = () => {
  if (!currentOffer.value) return
  
  const originalPrice = parseFloat(currentOffer.value.originalPrice) || 0
  const discount = currentOffer.value.discount
  
  if (originalPrice > 0 && discount.value > 0) {
    let finalPrice
    
    if (discount.type === 'percentage') {
      finalPrice = originalPrice * (1 - discount.value / 100)
    } else {
      finalPrice = originalPrice - parseFloat(discount.value)
    }
    
    finalPrice = Math.max(0, finalPrice)
    calculatedPrice.value = finalPrice
    
    if (finalPrice >= originalPrice) {
      priceValidationError.value = t('layoutEditor.sidebar.finalPriceMustBeLessThanOriginalPrice')
    } else {
      priceValidationError.value = ''
    }
  } else {
    calculatedPrice.value = null
    priceValidationError.value = ''
  }
}

const isValidUrl = (url) => {
  if (!url) return true
  
  try {
    new URL(url)
    return true
  } catch {
    try {
      new URL('http://' + url)
      return true
    } catch {
      return false
    }
  }
}

const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

let saveTimeout = null
let isUpdating = ref(false)
let initialFontSize = ref(40)

watch(() => design.value.sectionTitleFontSize, (newVal, oldVal) => {
  if (oldVal === 40 && newVal === 24 && !isUpdating.value) {
    nextTick(() => {
      design.value.sectionTitleFontSize = initialFontSize.value
    })
  }
})

const onConfigDataChange = () => {
  if (isUpdating.value) return
  
  try {
    emit('data-changed', { property: 'config', value: config.value })
    clearTimeout(saveTimeout)
    saveTimeout = setTimeout(() => {
      if (!isUpdating.value) {
        isUpdating.value = true
        saveData()
        isUpdating.value = false
      }
    }, 150)
  } catch (error) {
    isUpdating.value = false
  }
}

const onOfferDataChange = () => {
  if (isUpdating.value) return
  
  try {
    emit('data-changed', { property: 'offers', value: offers.value })
    clearTimeout(saveTimeout)
    saveTimeout = setTimeout(() => {
      if (!isUpdating.value) {
        isUpdating.value = true
        saveData()
        isUpdating.value = false
      }
    }, 150)
  } catch (error) {
    isUpdating.value = false
  }
}

const onDesignChange = (property, value) => {
  if (isUpdating.value) return
  
  try {
    emit('data-changed', { property, value })
    clearTimeout(saveTimeout)
    saveTimeout = setTimeout(() => {
      if (!isUpdating.value) {
        isUpdating.value = true
        saveData()
        isUpdating.value = false
      }
    }, 150)
  } catch (error) {
    isUpdating.value = false
  }
}

const updateElementContent = (targetElement) => {
  if (!targetElement) return
  
  try {
    requestAnimationFrame(() => {
      const titleEl = targetElement.querySelector('.special-offers-title')
      if (titleEl && titleEl.textContent !== config.value.title) {
        titleEl.textContent = config.value.title
      }
      
      const subtitleEl = targetElement.querySelector('.special-offers-subtitle')
      if (subtitleEl && subtitleEl.textContent !== config.value.subtitle) {
        subtitleEl.textContent = config.value.subtitle
      }
      
      const gridEl = targetElement.querySelector('.offers-grid')
      if (gridEl) {
        const activeOffers = offers.value.filter(offer => offer.active)
        const newContent = activeOffers.map(offer => createOfferHTML(offer)).join('')
        
        if (gridEl.innerHTML !== newContent) {
          gridEl.innerHTML = newContent
        }
        
        gridEl.style.gridTemplateColumns = `repeat(${design.value.columns}, 1fr)`
        gridEl.style.gap = design.value.spacing + 'px'
        gridEl.style.display = 'grid'
        gridEl.style.gridAutoRows = 'max-content'
      }
    })
  } catch (error) {
    // Falha silenciosa na atualização de conteúdo
  }
}

const createOfferHTML = (offer) => {
  const safeTitle = offer.title?.replace(/[<>&"]/g, '') || ''
  const safeDescription = offer.description?.replace(/[<>&"]/g, '') || ''
  const safeBadge = offer.badge?.replace(/[<>&"]/g, '') || ''
  const safeLinkText = offer.linkText?.replace(/[<>&"]/g, '') || ''
  const safeUrl = offer.linkUrl?.replace(/[<>&"]/g, '') || '#'
  
  return `
    <div class="offer-item" data-offer-id="${offer.id}" style="background: ${design.value.cardBackground}; border-radius: ${design.value.borderRadius}px; padding: ${design.value.cardPadding}px; position: relative; transition: all 0.3s ease; border: 1px solid #e9ecef; display: flex; flex-direction: column; min-height: 280px;">
      ${safeBadge ? `<div class="offer-badge" style="position: absolute; top: -8px; right: 16px; background: ${design.value.badgeColor}; color: white; padding: 6px 12px; border-radius: 20px; font-size: ${design.value.badgeFontSize}px; font-weight: 600; z-index: 2;">${safeBadge}</div>` : ''}
      <div class="offer-content" style="display: flex; flex-direction: column; flex: 1;">
        <h3 class="offer-title" style="font-size: ${design.value.cardTitleFontSize}px; font-weight: 700; margin: 0 0 0.5rem; color: ${design.value.titleColor}; line-height: 1.3;">${safeTitle}</h3>
        <p class="offer-description" style="color: ${design.value.textColor}; margin: 0 0 1rem; line-height: 1.4; font-size: 0.95rem; flex: 1;">${safeDescription}</p>
        ${offer.originalPrice > 0 ? `
          <div class="offer-pricing" style="margin: 1rem 0;">
            <span class="original-price" style="text-decoration: line-through; color: #999; font-size: 0.9rem; margin-right: 0.5rem;">R$ ${offer.originalPrice.toFixed(2).replace('.', ',')}</span>
            <span class="final-price" style="color: ${design.value.accentColor}; font-size: ${design.value.priceFontSize}px; font-weight: 700;">R$ ${offer.finalPrice.toFixed(2).replace('.', ',')}</span>
          </div>
        ` : `
          <div class="offer-pricing" style="margin: 1rem 0; min-height: 28px;">
            <span class="offer-highlight" style="color: ${design.value.accentColor}; font-size: 16px; font-weight: 600;">Oferta especial</span>
          </div>
        `}
        ${safeLinkText ? `<a href="${safeUrl}" class="offer-button" style="background: ${design.value.accentColor}; color: white; padding: 12px 24px; border: none; border-radius: 8px; font-size: 0.9rem; font-weight: 600; text-decoration: none; display: inline-block; transition: all 0.3s ease; width: 100%; text-align: center; box-sizing: border-box; margin-top: auto;">${safeLinkText}</a>` : ''}
      </div>
    </div>
  `
}

const applyStyles = (targetElement) => {
  if (!targetElement) return
  
  const titleEl = targetElement.querySelector('.special-offers-title')
  const subtitleEl = targetElement.querySelector('.special-offers-subtitle')
  const gridEl = targetElement.querySelector('.offers-grid')
  
  targetElement.style.background = design.value.sectionBackground
  
  if (gridEl) {
    gridEl.style.gridTemplateColumns = `repeat(${design.value.columns}, 1fr)`
    gridEl.style.gap = design.value.spacing + 'px'
    gridEl.style.display = 'grid'
    gridEl.style.gridAutoRows = 'max-content'
  }
  
  if (titleEl) {
    titleEl.style.color = design.value.titleColor
    const currentFontSize = parseInt(titleEl.style.fontSize) || 40
    const newFontSize = design.value.sectionTitleFontSize
    if (currentFontSize !== newFontSize) {
      titleEl.style.fontSize = newFontSize + 'px'
    }
  }
  
  if (subtitleEl) {
    subtitleEl.style.color = design.value.textColor
  }
}

onMounted(async () => {
  loadData()
  
  if (offers.value.length > 0) {
    if (currentOfferIndex.value < 0 || currentOfferIndex.value >= offers.value.length) {
      currentOfferIndex.value = 0
    }
  }
  
  await nextTick()
  const targetElement = props.element?.getAttribute('data-component') === 'special-offers' 
    ? props.element 
    : props.element?.closest('[data-component="special-offers"]')
    
  if (targetElement) {
    const savedFontSize = parseInt(targetElement.getAttribute('data-section-title-font-size')) || 40
    if (design.value.sectionTitleFontSize !== savedFontSize) {
      design.value.sectionTitleFontSize = savedFontSize
    }
  }
})

onUnmounted(() => {
  try {
    if (saveTimeout) {
      clearTimeout(saveTimeout)
      saveTimeout = null
    }
    
    isUpdating.value = false
    

  } catch (error) {
    // Falha silenciosa na limpeza
  }
})
</script>

<style scoped>
.special-offers-sidebar-editor {
  height: 100%;
  overflow-y: auto;
  background: var(--iluria-color-surface);
}

/* Tabs System */
.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  margin-bottom: 1rem;
  background: var(--iluria-color-container-bg);
}

.tab-button {
  flex: 1;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  cursor: pointer;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  border-bottom: 2px solid transparent;
  transition: all 0.2s ease;
  min-height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background-color: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background-color: var(--iluria-color-primary-bg);
}

/* Content */
.tab-content {
  min-height: 300px;
  padding: 1rem;
}

.tab-panel {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.section {
  margin-bottom: 1.5rem;
}

.section-title {
  margin: 0 0 1rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.form-group {
  margin-bottom: 1rem;
}

/* Offers Management */
.offers-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border-radius: 8px;
}

.offers-header h3 {
  margin: 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.offers-list {
  max-height: 400px;
  overflow-y: auto;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-background);
}

.offer-item {
  border-bottom: 1px solid var(--iluria-color-border);
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  background: var(--iluria-color-surface);
}

.offer-item:last-child {
  border-bottom: none;
}

.offer-item:hover {
  background: var(--iluria-color-hover);
}

.offer-item.active {
  background: var(--iluria-color-primary-bg);
  border-left: 3px solid var(--iluria-color-primary);
}

.offer-preview {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  margin-bottom: 0.75rem;
}

.offer-badge {
  background: var(--iluria-color-danger);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 4px;
  font-size: 0.625rem;
  font-weight: 600;
  white-space: nowrap;
  flex-shrink: 0;
}

.offer-info {
  flex: 1;
  min-width: 0;
}

.offer-info h4 {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
}

.offer-info p {
  margin: 0 0 0.25rem 0;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.offer-status {
  font-size: 0.6875rem;
  font-weight: 500;
  padding: 0.125rem 0.375rem;
  border-radius: 12px;
  background: var(--iluria-color-danger-bg);
  color: var(--iluria-color-danger);
}

.offer-status.active {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
}

.offer-controls {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

.offer-selector {
  background: var(--iluria-color-container-bg);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  margin-bottom: 1rem;
}

.offer-selector-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: flex-end;
}

.offer-select {
  flex: 1;
}

.offer-actions {
  display: flex;
  gap: 0.25rem;
}

/* Offer Editor */
.offer-editor {
  background: var(--iluria-color-container-bg);
  padding: 1.5rem;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.discount-inputs {
  display: grid;
  grid-template-columns: 1fr auto;
  gap: 0.5rem;
  align-items: end;
}

.pricing-section {
  background: var(--iluria-color-surface);
  padding: 1rem;
  border-radius: 6px;
  border: 1px solid var(--iluria-color-border);
  margin: 1rem 0;
}

/* Validation and Help Text */
.field-error {
  color: var(--iluria-color-danger);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  font-weight: 500;
}

.field-help {
  color: var(--iluria-color-text-secondary);
  font-size: 0.75rem;
  margin-top: 0.25rem;
  display: block;
}

.iluria-textarea.error,
.iluria-input.error {
  border-color: var(--iluria-color-danger);
  box-shadow: 0 0 0 3px var(--iluria-color-danger-bg);
}

/* Offer Preview */
.offer-preview-section {
  margin-top: 2rem;
  padding: 1rem;
  background: var(--iluria-color-surface);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.offer-preview-section h4 {
  margin: 0 0 1rem 0;
  font-size: 0.9rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.offer-preview-card {
  max-width: 300px;
  margin: 0 auto;
}

.preview-badge {
  font-size: 0.6875rem;
}

.preview-title {
  margin: 0.5rem 0;
}

.preview-description {
  font-size: 0.8125rem;
}

.preview-pricing {
  margin: 1rem 0;
}

.preview-original-price {
  text-decoration: line-through;
  color: #999;
  font-size: 0.8rem;
  margin-right: 0.5rem;
}

.preview-final-price {
  font-weight: 700;
}

.preview-button {
  cursor: pointer;
  text-align: center;
  border-radius: 6px;
}

.preview-button:hover {
  opacity: 0.9;
}

/* No Offers State */
.no-offers {
  text-align: center;
  padding: 2rem;
  color: var(--iluria-color-text-secondary);
}

.no-offers h3 {
  margin: 0 0 0.5rem 0;
  color: var(--iluria-color-text-primary);
}

.no-offers p {
  margin-bottom: 1rem;
}

/* Debug Info */
.debug-info {
  padding: 1.5rem;
  background: var(--iluria-color-warning-bg);
  border: 1px solid var(--iluria-color-warning);
  border-radius: 8px;
  margin: 1rem;
  text-align: center;
}

.debug-info h3 {
  margin: 0 0 1rem 0;
  color: var(--iluria-color-warning-text);
  font-size: 1rem;
}

.debug-info p {
  margin: 0.5rem 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

/* Textarea component following Iluria design system */
.iluria-textarea {
  width: 100%;
  min-height: 80px;
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
}

.iluria-textarea::placeholder {
  color: var(--iluria-color-text-secondary);
  opacity: 0.7;
}

.iluria-textarea:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
  background: var(--iluria-color-surface);
}

.iluria-textarea:hover:not(:focus) {
  border-color: var(--iluria-color-primary-border);
}

.iluria-textarea:disabled {
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-secondary);
  cursor: not-allowed;
  opacity: 0.6;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-button {
    padding: 0.5rem 0.75rem;
    font-size: 0.8125rem;
  }
  
  .tab-content {
    padding: 0.75rem;
  }
  
  .offers-header {
    padding: 0.75rem;
  }
  
  .offer-item {
    padding: 0.75rem;
  }
  
  .offer-editor {
    padding: 1rem;
  }
  
  .section-title {
    font-size: 0.9375rem;
  }
}

/* Scrollbar customization */
.special-offers-sidebar-editor::-webkit-scrollbar,
.offers-list::-webkit-scrollbar {
  width: 6px;
}

.special-offers-sidebar-editor::-webkit-scrollbar-track,
.offers-list::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.special-offers-sidebar-editor::-webkit-scrollbar-thumb,
.offers-list::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.special-offers-sidebar-editor::-webkit-scrollbar-thumb:hover,
.offers-list::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-secondary);
}
</style> 
