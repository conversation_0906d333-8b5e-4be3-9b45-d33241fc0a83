import { ref, computed } from 'vue'
import { useLayoutManager } from '@/composables/useLayoutManager'
import { useThemeWebSocket } from '@/composables/useThemeWebSocket'
import { themeCategoryService } from '@/services/themeCategoryService'
import { themeService } from '@/services/themeService'
import { useToast } from '@/services/toast.service'

const availableThemes = ref([])
const availableCategories = ref([])
const currentTheme = ref(null)
const isLoading = ref(false)
const templateLoading = ref(false)

const isThemeChanging = ref(false)
const themeProgress = ref({
  stage: '',
  message: '',
  progress: 0
})


export function useThemeManager() {
  const layoutManager = useLayoutManager()
  const toast = useToast()
  const { connectForThemeOperation, onProgress, onCompleted, onError, progressData, isConnected } = useThemeWebSocket()

  const loadCategories = async () => {
    try {
      const categories = await themeCategoryService.getCategories(true)
      availableCategories.value = categories
      return categories
    } catch (error) {
      availableCategories.value = []
      return []
    }
  }

  const loadThemes = async () => {
    isLoading.value = true

    try {
      await loadCategories()
      const storeThemes = await themeService.getThemes()
      const allThemes = [
        ...(storeThemes || [])
      ]

      availableThemes.value = allThemes


      availableThemes.value = availableThemes.value.map(theme => {
        let categoryIds = []

        if (theme.categoryIds && theme.categoryIds.length > 0) {
          categoryIds = theme.categoryIds
        }
        else if (theme.tags && theme.tags.length > 0) {
          categoryIds = theme.tags.map(tag => {
            const category = availableCategories.value.find(cat =>
              cat.slug === tag || cat.name.toLowerCase() === tag.toLowerCase()
            )
            return category?.id || tag
          })
        }
        else {
          const themeNameLower = theme.name.toLowerCase()
          if (themeNameLower.includes('moderno') || themeNameLower.includes('clean')) {
            const modernoCategory = availableCategories.value.find(cat => cat.slug === 'moderno')
            if (modernoCategory) categoryIds = [modernoCategory.id]
          } else if (themeNameLower.includes('fashion') || themeNameLower.includes('elegante')) {
            const fashionCategory = availableCategories.value.find(cat => cat.slug === 'fashion')
            if (fashionCategory) categoryIds = [fashionCategory.id]
          } else if (themeNameLower.includes('minimalista') || themeNameLower.includes('zen')) {
            const minimalistaCategory = availableCategories.value.find(cat => cat.slug === 'minimalista')
            if (minimalistaCategory) categoryIds = [minimalistaCategory.id]
          } else if (themeNameLower.includes('dark') || themeNameLower.includes('premium')) {
            const darkCategory = availableCategories.value.find(cat => cat.slug === 'dark')
            if (darkCategory) categoryIds = [darkCategory.id]
          } else if (themeNameLower.includes('tech') || themeNameLower.includes('vibrante')) {
            const techCategory = availableCategories.value.find(cat => cat.slug === 'tecnologia')
            if (techCategory) categoryIds = [techCategory.id]
          }
        }

        return {
          ...theme,
          categoryIds
        }
      })


      try {
        const activeTheme = await themeService.getActiveTheme()
        currentTheme.value = activeTheme || null
        
        availableThemes.value = availableThemes.value.map(theme => ({
          ...theme,
          isActive: activeTheme ? theme.id === activeTheme.id : false
        }))
      } catch (error) {
        currentTheme.value = null
        
        availableThemes.value = availableThemes.value.map(theme => ({
          ...theme,
          isActive: false
        }))
      }

    } catch (error) {
      toast.showError('Não foi possível conectar com o servidor.', {
        title: 'Erro ao carregar temas'
      })
      availableThemes.value = []
      currentTheme.value = null
    } finally {
      isLoading.value = false
    }
  }

  const setCurrentTheme = async (theme) => {
   

    // Só retorna se for exatamente o mesmo tema (mesmo ID E mesmo nome) E não for tema padrão
    if (currentTheme.value?.id === theme.id && 
        currentTheme.value?.name === theme.name && 
        !theme.isDefault) {
     
      return currentTheme.value
    }

   
    if (isThemeChanging.value) {
      toast.showWarning('Uma operação de tema já está em andamento. Aguarde a conclusão.', {
        title: 'Operação em andamento'
      })
      return
    }

    isThemeChanging.value = true
    
    const webSocketConnected = await connectForThemeOperation()
    
    onProgress((data) => {
      themeProgress.value = {
        stage: data.type.toLowerCase(),
        message: data.message,
        progress: data.progress
      }
    })
    
    onCompleted((data) => {
      themeProgress.value = {
        stage: 'completed',
        message: data.message,
        progress: 100
      }
    })
    
    onError((data) => {
      themeProgress.value = {
        stage: 'error',
        message: data.message,
        progress: 0
      }
    })
    
    try {
      if (!webSocketConnected) {
        themeProgress.value = {
          stage: 'starting',
          message: 'Iniciando aplicação do tema...',
          progress: 5
        }
      }
      
      let activatedTheme

      if (theme.isDefault) {
        activatedTheme = await themeService.useDefaultTheme(theme.id)
      } else {
        activatedTheme = await themeService.activateTheme(theme.id)
      }

     

      const existingThemeIndex = availableThemes.value.findIndex(t => t.id === activatedTheme.id)

      if (existingThemeIndex >= 0) {
        availableThemes.value = availableThemes.value.map((t, index) => ({
          ...t,
          isActive: index === existingThemeIndex
        }))
      } else {
        availableThemes.value = [
          { ...activatedTheme, isActive: true },
          ...availableThemes.value.map(t => ({ ...t, isActive: false }))
        ]
      }

      currentTheme.value = activatedTheme

      if (layoutManager && layoutManager.setCurrentLayout) {
        layoutManager.setCurrentLayout({
          layoutId: activatedTheme.id,
          name: activatedTheme.name,
          filename: activatedTheme.filename,
          type: activatedTheme.type
        })
      }

      if (!webSocketConnected) {
        themeProgress.value = {
          stage: 'completed',
          message: `Tema "${activatedTheme.name}" ativado com sucesso!`,
          progress: 100
        }
        
        toast.showSuccess(`O tema "${activatedTheme.name}" foi ativado com sucesso.`, {
          title: 'Tema ativado'
        })
      }

      return activatedTheme

    } catch (error) {
      
      if (!webSocketConnected) {
        themeProgress.value = {
          stage: 'error',
          message: error.message || 'Erro ao ativar tema',
          progress: 0
        }
        
        toast.showError(error.message || 'Não foi possível ativar o tema.', {
          title: 'Erro ao ativar tema'
        })
      }
      throw error
    } finally {
      setTimeout(() => {
        isThemeChanging.value = false
        themeProgress.value = {
          stage: '',
          message: '',
          progress: 0
        }
      }, 3000)
    }
  }

  const createTheme = async (themeData) => {
    try {
      const { baseThemeId, name, description, colors, layout, categoryIds, setAsCurrent } = themeData
      
      const baseTheme = availableThemes.value.find(t => t.id === baseThemeId)
      if (!baseTheme) {
        throw new Error(`Tema base ${baseThemeId} não encontrado`)
      }
      
      const themeCreateData = {
        name,
        description: description || `Tema ${name} baseado em ${baseTheme.name}`,
        categoryIds: categoryIds || [...(baseTheme.categoryIds || [])],
        colors: colors || { ...baseTheme.colors },
        layout: layout || baseTheme.layout,
        baseThemeId: baseThemeId
      }
      
      const newTheme = await themeService.createTheme(themeCreateData)
      
      availableThemes.value.push(newTheme)
      
      if (setAsCurrent) {
        await setCurrentTheme(newTheme)
      }
      
      toast.showSuccess(`O tema "${newTheme.name}" foi criado com sucesso.`, {
        title: 'Tema criado'
      })
      
      return newTheme
      
    } catch (error) {
      toast.showError(error.message || 'Não foi possível criar o tema.', {
        title: 'Erro ao criar tema'
      })
      throw error
    }
  }

  const createThemeCategory = async (categoryData)  => {
    try {
      const newCategory = await themeCategoryService.createCategory(categoryData)
      availableCategories.value.push(newCategory)
      return newCategory
    } catch (error) {
      throw error;
    }
  }

  const duplicateTheme = async (theme) => {
    try {
      // Usar o novo endpoint de duplicação do backend
      const newTheme = await themeService.duplicateTheme(theme.id)

      // Recarregar a lista completa para garantir sincronização
      await loadThemes()

      toast.showSuccess(`O tema "${newTheme.name}" foi duplicado com sucesso.`, {
        title: 'Tema duplicado'
      })

      return newTheme

    } catch (error) {
      console.error('Erro ao duplicar tema:', error)
      toast.showError(error.response?.data?.message || error.message || 'Não foi possível duplicar o tema.', {
        title: 'Erro ao duplicar tema'
      })
      throw error
    }
  }

  const deleteTheme = async (theme) => {
    try {
      if (theme.isDefault) {
        throw new Error('Não é possível excluir temas padrão')
      }
      
      await themeService.deleteTheme(theme.id)
      
      const index = availableThemes.value.findIndex(t => t.id === theme.id)
      if (index > -1) {
        availableThemes.value.splice(index, 1)
        
        if (currentTheme.value?.id === theme.id) {
          await setCurrentTheme(availableThemes.value[0])
        }
      }
      
      toast.showSuccess(`O tema "${theme.name}" foi excluído com sucesso.`, {
        title: 'Tema excluído'
      })
      
    } catch (error) {
      toast.showError(error.message || 'Não foi possível excluir o tema.', {
        title: 'Erro ao excluir tema'
      })
      throw error
    }
  }

  const updateTheme = async (themeId, updates) => {
    try {
      const updatedTheme = await themeService.updateTheme(themeId, updates)
      
      const index = availableThemes.value.findIndex(t => t.id === themeId)
      if (index > -1) {
        availableThemes.value[index] = updatedTheme
        
        if (currentTheme.value?.id === themeId) {
          currentTheme.value = updatedTheme
        }
      }
      
      toast.showSuccess('O tema foi atualizado com sucesso.', {
        title: 'Tema atualizado'
      })
      
      return updatedTheme
      
    } catch (error) {
      toast.showError(error.message || 'Não foi possível atualizar o tema.', {
        title: 'Erro ao atualizar tema'
      })
      throw error
    }
  }

  const findThemeById = (themeId) => {
    return availableThemes.value.find(theme => theme.id === themeId)
  }

  const generateThemeId = (name) => {
    const slug = name.toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9]/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')

    return `${slug}-${Date.now()}`
  }

  const initializeDefaultThemes = async () => {
    try {
      const defaultThemes = await themeService.initializeDefaultThemes()

      defaultThemes.forEach(defaultTheme => {
        const exists = availableThemes.value.find(t => t.id === defaultTheme.id)
        if (!exists) {
          availableThemes.value.push(defaultTheme)
        }
      })

      toast.showSuccess(`${defaultThemes.length} temas padrão foram adicionados.`, {
        title: 'Temas padrão inicializados'
      })

      return defaultThemes
    } catch (error) {
      toast.showError('Não foi possível inicializar os temas padrão.', {
        title: 'Erro ao inicializar temas'
      })
      throw error
    }
  }


  const getAllTags = computed(() => {
    const allTags = availableThemes.value.flatMap(theme => theme.tags || [])
    return [...new Set(allTags)]
  })




  const getThemesByCategories = (categoryIds) => {
    if (!categoryIds || categoryIds.length === 0) {
      return availableThemes.value
    }

    return availableThemes.value.filter(theme =>
      categoryIds.some(categoryId => theme.categoryIds?.includes(categoryId))
    )
  }

  const getCategoryById = (categoryId) => {
    return availableCategories.value.find(cat => cat.id === categoryId)
  }

  const getCategoryBySlug = (slug) => {
    return availableCategories.value.find(cat => cat.slug === slug)
  }

  const getCategoryNames = (categoryIds) => {
    if (!categoryIds || categoryIds.length === 0) return []

    return categoryIds.map(id => {
      const category = getCategoryById(id)
      return category?.name || id
    })
  }

  const getAllCategories = computed(() => {
    return availableCategories.value
  })

  const isNewStore = computed(() => {
    if (isLoading.value) {
      return false
    }

    if (!availableThemes.value || availableThemes.value.length === 0) {
      return true
    }

    if (!currentTheme.value) {
      return true
    }

    const customThemes = availableThemes.value.filter(theme => !theme.isDefault)
    const hasActiveCustomTheme = currentTheme.value && !currentTheme.value.isDefault

    return customThemes.length === 0 && !hasActiveCustomTheme
  })


  const applyThemeTemplate = async (themeId) => {
    templateLoading.value = true
    
    try {
      const result = await themeService.applyThemeTemplate(themeId)
      
      const themeIndex = availableThemes.value.findIndex(t => t.id === themeId)
      if (themeIndex > -1) {
        availableThemes.value[themeIndex] = {
          ...availableThemes.value[themeIndex],
          ...result
        }
      }
      
      toast.showSuccess('O template foi aplicado e os arquivos foram sincronizados.', {
        title: 'Template aplicado com sucesso'
      })
      
      return result
    } catch (error) {
      toast.showError(error.message || 'Ocorreu um erro ao aplicar o template.', {
        title: 'Erro ao aplicar template'
      })
      throw error
    } finally {
      templateLoading.value = false
    }
  }

  const syncThemeTemplate = async (themeId) => {
    templateLoading.value = true
    
    try {
      const result = await themeService.syncTemplateFiles(themeId)
      
      const themeIndex = availableThemes.value.findIndex(t => t.id === themeId)
      if (themeIndex > -1) {
        availableThemes.value[themeIndex] = {
          ...availableThemes.value[themeIndex],
          ...result
        }
      }
      
      toast.showSuccess('Os arquivos do template foram sincronizados com sucesso.', {
        title: 'Template sincronizado'
      })
      
      return result
    } catch (error) {
      toast.showError(error.message || 'Ocorreu um erro ao sincronizar os templates.', {
        title: 'Erro ao sincronizar template'
      })
      throw error
    } finally {
      templateLoading.value = false
    }
  }

  const getThemesWithTemplates = computed(() => {
    return availableThemes.value.filter(theme => theme.hasTemplateFiles)
  })

  const getThemesWithoutTemplates = computed(() => {
    return availableThemes.value.filter(theme => !theme.hasTemplateFiles)
  })

  return {
    availableThemes,
    availableCategories,
    currentTheme,
    isLoading,
    templateLoading,
    isThemeChanging,
    themeProgress,
    isConnected, // Estado da conexão WebSocket

    getAllTags,
    getAllCategories,
    getThemesWithTemplates,
    getThemesWithoutTemplates,
    isNewStore,

    loadThemes,
    loadCategories,
    setCurrentTheme,
    createTheme,
    createThemeCategory,
    duplicateTheme,
    deleteTheme,
    updateTheme,
    findThemeById,
    generateThemeId,
    initializeDefaultThemes,
    getThemesByCategories,
    getCategoryById,
    getCategoryBySlug,
    getCategoryNames,

    applyThemeTemplate,
    syncThemeTemplate
  }
}
