<script setup>
import { ref } from 'vue'
import { useRouter } from 'vue-router'
import { useAuthStore } from '@/stores/auth.store'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'

const emit = defineEmits(['showMfaForm'])
const props = defineProps({
    email: {
        type: String,
        required: true
    },
    password: {
        type: String,
        required: true
    },
    rememberMe: {
        type: Boolean,
        required: true
    }
})

const router = useRouter()
const authStore = useAuthStore()
const { addToast } = useToast()
const { t } = useI18n()

const loginButtonIsVisible = ref(true)
const spinnerIsVisible = ref(false)

const showLoginButton = () => {
    loginButtonIsVisible.value = true
    spinnerIsVisible.value = false
}

const hideLoginButton = () => {
    loginButtonIsVisible.value = false
    spinnerIsVisible.value = true
}

const doLogin = async () => {
    try {
        hideLoginButton()
        const response = await authStore.login(props.email, props.password, props.rememberMe ?? false);
        showLoginButton()
        if (response.mfaEnabled === "true") {
            emit('showMfaForm', response.mfaType || 'EMAIL_VERIFICATION')
        } else {
            router.push("/stores")
        }
    } catch (error) {
        showLoginButton()
        if (error.response?.status === 401) {
            addToast(t('login.invalidCredentials'), 'error')
        } else if (error.message === 'Network Error') {
            addToast(t('login.networkError'), 'error')
        } else {
            addToast(t('login.genericError'), 'error')
        }
    }
}
</script>

<template>
    <button
        id="login-button"
        @click="doLogin"
        v-show="loginButtonIsVisible"
        type="button"
        class="login-btn w-full text-white font-bold py-3 px-4 rounded-lg"
    >
        {{ t('login.signIn') }}
    </button>
    <div v-show="spinnerIsVisible" class="loading-spinner-container flex justify-center items-center p-3">
        <div class="spinner-border animate-spin h-8 w-8 border-4 border-t-transparent rounded-full"></div>
    </div>
</template>

<style scoped>
.login-btn {
  background-color: rgb(218, 18, 0);
  transition: all 0.15s ease;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

.login-btn:hover:not(:disabled) {
  background-color: rgb(180, 15, 0);
}

.login-btn:active:not(:disabled) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  background-color: rgb(160, 13, 0);
}

.login-btn:disabled {
  box-shadow: none;
  background-color: rgb(150, 150, 150);
}

/* Spinner com cor rosa do Iluria */
.spinner-border {
  border-color: rgb(218, 18, 0) !important;
  border-top-color: transparent !important;
}
</style>
