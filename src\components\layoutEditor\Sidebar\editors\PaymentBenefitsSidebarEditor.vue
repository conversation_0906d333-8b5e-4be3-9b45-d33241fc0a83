<template>
  <div class="payment-benefits-sidebar-editor">
    <!-- Tabs para separar funcionalidades -->
    <div class="editor-tabs">
      <IluriaButton 
        v-for="tab in tabs" 
        :key="tab.id"
        :color="activeTab === tab.id ? 'primary' : 'secondary'"
        :variant="activeTab === tab.id ? 'solid' : 'ghost'"
        size="small"
        @click="activeTab = tab.id"
        class="tab-button"
      >
        {{ tab.label }}
      </IluriaButton>
    </div>

    <!-- Conte<PERSON><PERSON> das Tabs -->
    <div class="tab-content">
      <!-- Aba Geral -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        <div class="form-group">
          <IluriaInputText
            v-model="config.title"
            :label="$t('layoutEditor.sidebar.sectionTitle')"
            :placeholder="$t('layoutEditor.sidebar.sectionTitlePlaceholder')"
            @update:model-value="onConfigDataChange"
          />
        </div>

        <div class="form-group">
          <IluriaInputText
            v-model="config.subtitle"
            :label="$t('layoutEditor.sidebar.subtitle')"
            :placeholder="$t('layoutEditor.sidebar.subtitlePlaceholder')"
            @update:model-value="onConfigDataChange"
          />
        </div>

        <div class="benefits-header">
          <IluriaTitle class="benefits-title">{{ $t('layoutEditor.sidebar.manageBenefits') }}</IluriaTitle>
          <IluriaButton @click="addBenefit" color="primary" size="small">
            + {{ $t('layoutEditor.sidebar.addBenefit') }}
          </IluriaButton>
        </div>

        <div class="benefits-list" v-if="benefits.length > 0">
          <div 
            v-for="(benefit, index) in benefits" 
            :key="`${benefit.id}-${benefit.icon}`"
            :class="['benefit-item', { active: currentBenefitIndex === index }]"
            @click="selectBenefit(index)"
          >
            <div class="benefit-preview">
              <div 
                class="benefit-icon-preview" 
                v-html="getIconSvg(benefit.icon)"
                :key="benefit.icon"
              ></div>
              <div class="benefit-info">
                <h4>{{ benefit.title || $t('layoutEditor.sidebar.newBenefit') }}</h4>
                <p>{{ truncateText(benefit.description, 60) }}</p>
                <span :class="['benefit-status', { enabled: benefit.enabled }]">
                  {{ benefit.enabled ? $t('layoutEditor.sidebar.enabled') : $t('layoutEditor.sidebar.disabled') }}
                </span>
              </div>
            </div>
            <div class="benefit-controls">
              <IluriaButton 
                @click.stop="moveBenefitUp(index)"
                :disabled="index === 0"
                color="secondary"
                size="small"
                variant="ghost"
                :title="$t('layoutEditor.sidebar.moveUp')"
              >
                ↑
              </IluriaButton>
              <IluriaButton 
                @click.stop="moveBenefitDown(index)"
                :disabled="index === benefits.length - 1"
                color="secondary"
                size="small"
                variant="ghost"
                :title="$t('layoutEditor.sidebar.moveDown')" 
              >
                ↓
              </IluriaButton>
              <IluriaButton 
                @click.stop="removeBenefit(index)"
                color="danger"
                size="small"
                variant="ghost"
                :title="$t('layoutEditor.sidebar.remove')"
              >
                ✕
              </IluriaButton>
            </div>
          </div>
        </div>
      </div>

      <!-- Aba Editor de Benefício -->
      <div v-if="activeTab === 'benefits' && currentBenefit" class="tab-panel">
        <div class="benefit-editor">
          <div class="form-group">
            <IluriaInputText
              v-model="currentBenefit.title"
              :label="$t('layoutEditor.sidebar.benefitTitle')"
              :placeholder="$t('layoutEditor.sidebar.benefitTitlePlaceholder')"
              @update:model-value="onBenefitDataChange"
            />
          </div>

          <div class="form-group">
            <IluriaInputText
              v-model="currentBenefit.description"
              :label="$t('layoutEditor.sidebar.benefitDescription')"
              :placeholder="$t('layoutEditor.sidebar.benefitDescriptionPlaceholder')"
              @update:model-value="onBenefitDataChange"
              type="textarea"
              :rows="3"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sidebar.benefitIcon') }}</IluriaLabel>
            <div class="icon-selector">
              <button
                v-for="(iconSvg, iconName) in filteredIcons"
                :key="iconName"
                :class="['icon-option', { active: currentBenefit.icon === iconName }]"
                @click="updateBenefitIcon(iconName)"
                :title="getIconDisplayName(iconName)"
                type="button"
              >
                <div class="icon-preview" v-html="iconSvg"></div>
                <span class="icon-name">{{ getIconDisplayName(iconName) }}</span>
              </button>
            </div>
          </div>

          <div class="form-group">
            <IluriaToggleSwitch
                v-model="currentBenefit.enabled"
              :label="$t('layoutEditor.sidebar.enableBenefit')"
              @update:model-value="onBenefitDataChange"
              />
          </div>
        </div>
      </div>

      <!-- Aba Design -->
      <div v-if="activeTab === 'design'" class="tab-panel design-tab">
        <div class="design-scroll-container">
          <!-- Layout -->
          <div class="design-section priority-section">
            <h3>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="7" height="7"/>
                <rect x="14" y="3" width="7" height="7"/>
                <rect x="3" y="14" width="7" height="7"/>
                <rect x="14" y="14" width="7" height="7"/>
              </svg>
              {{ $t('layoutEditor.sidebar.layout') }}
            </h3>
          
          <div class="form-group">
              <IluriaLabel>{{ $t('layoutEditor.sidebar.columns') }}</IluriaLabel>
              <div class="columns-selector">
                <IluriaButton 
                  v-for="col in [1, 2, 3, 4]" 
                  :key="col"
                  :color="columns == col ? 'primary' : 'secondary'"
                  :variant="columns == col ? 'solid' : 'outline'"
                  size="small"
                  @click="columns = col; onStyleChange('columns', col)"
                  class="column-btn"
                >
                  <div class="column-preview" :data-columns="col">
                    <div v-for="i in col" :key="i" class="column-item"></div>
          </div>
                  <span>{{ col }}</span>
                </IluriaButton>
              </div>
          </div>

          <div class="form-group">
              <IluriaRange
                v-model="spacing"
                :label="$t('layoutEditor.sidebar.spacing')"
                :min="8"
                :max="64"
                :step="4"
                unit="px"
                @update:model-value="onStyleChange('spacing', $event + 'px')"
            />
          </div>

          <div class="form-group">
              <IluriaRange
                v-model="cardPadding"
                :label="$t('layoutEditor.sidebar.cardPadding')"
                :min="12"
                :max="48"
                :step="4"
                unit="px"
                @update:model-value="onStyleChange('cardPadding', $event + 'px')"
            />
          </div>

          <div class="form-group">
              <IluriaRange
                v-model="borderRadius"
                :label="$t('layoutEditor.sidebar.borderRadius')"
                :min="0"
                :max="24"
                :step="2"
                unit="px"
                @update:model-value="onStyleChange('borderRadius', $event + 'px')"
            />
          </div>
        </div>

        <!-- Cores -->
        <div class="design-section">
            <h3>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="12" cy="12" r="10"/>
                <path d="M2 12h20"/>
                <path d="M12 2a10 10 0 0 1 0 20"/>
              </svg>
              {{ $t('layoutEditor.sidebar.colors') }}
            </h3>
            
            <div class="color-grid">
          <div class="form-group">
            <IluriaColorPicker
              v-model="sectionBackground"
              :label="$t('layoutEditor.sidebar.sectionBackground')"
              @update:model-value="onStyleChange('sectionBackground', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaColorPicker
              v-model="cardBackground"
              :label="$t('layoutEditor.sidebar.cardBackground')"
              @update:model-value="onStyleChange('cardBackground', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaColorPicker
              v-model="titleColor"
              :label="$t('layoutEditor.sidebar.titleColor')"
              @update:model-value="onStyleChange('titleColor', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaColorPicker
              v-model="textColor"
              :label="$t('layoutEditor.sidebar.textColor')"
              @update:model-value="onStyleChange('textColor', $event)"
            />
          </div>

          <div class="form-group">
            <IluriaColorPicker
              v-model="iconColor"
              :label="$t('layoutEditor.sidebar.iconColor')"
              @update:model-value="onStyleChange('iconColor', $event)"
            />
          </div>
          </div>
        </div>

        <!-- Tipografia -->
        <div class="design-section">
            <h3>
              <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
              </svg>
              {{ $t('layoutEditor.sidebar.typography') }}
            </h3>
          
          <div class="form-group">
            <IluriaRange
              v-model="titleFontSize"
              :label="$t('layoutEditor.sidebar.titleFontSize')"
              :min="16"
              :max="32"
              :step="2"
              unit="px"
              @update:model-value="onStyleChange('titleFontSize', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaRange
              v-model="descriptionFontSize"
              :label="$t('layoutEditor.sidebar.descriptionFontSize')"
              :min="12"
              :max="18"
              :step="1"
              unit="px"
              @update:model-value="onStyleChange('descriptionFontSize', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaRange
              v-model="iconSize"
              :label="$t('layoutEditor.sidebar.iconSize')"
              :min="20"
              :max="48"
              :step="4"
              unit="px"
              @update:model-value="onStyleChange('iconSize', $event + 'px')"
            />
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'

// Import Iluria components
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaTitle from '@/components/iluria/IluriaTitle.vue'



const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close', 'element-updated'])

// Tabs
const activeTab = ref('content')
const tabs = [
  { id: 'content', label: t('layoutEditor.sidebar.content') },
  { id: 'benefits', label: t('layoutEditor.sidebar.benefits') },
  { id: 'design', label: t('layoutEditor.sidebar.design') }
]

// Estados reativos - Conteúdo
const config = ref({ 
  title: 'Nossos benefícios',
  subtitle: 'Benefícios exclusivos para você'
})
const benefits = ref([])
const currentBenefitIndex = ref(0)

// Estados reativos - Design  
const sectionBackground = ref('#f9fafb')
const cardBackground = ref('#ffffff')
const titleColor = ref('#1f2937')
const textColor = ref('#6b7280')
const iconColor = ref('#3b82f6')
const columns = ref('3')
const spacing = ref(24)
const cardPadding = ref(20)
const borderRadius = ref(8)
const titleFontSize = ref(18)
const descriptionFontSize = ref(14)
const iconSize = ref(32)

// Ícones disponíveis para benefícios (alinhados com PaymentBenefitsLoader)
const benefitIcons = {
  'shield-check': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
    <path d="M9 12l2 2 4-4"/>
  </svg>`,
  'truck': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="1" y="3" width="15" height="13"/>
    <path d="M16 8h4l3 3v5h-7V8zM16 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM7 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
  </svg>`,
  'refresh': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
    <path d="M3 3v5h5M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/>
    <path d="M16 16h5v5"/>
  </svg>`,
  'headset': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
    <path d="M3 17v-4a9 9 0 0 1 18 0v4"/>
    <rect x="17" y="16" width="4" height="5" rx="1"/>
    <rect x="3" y="16" width="4" height="5" rx="1"/>
  </svg>`,
  'credit-card': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="1" y="4" width="22" height="16" rx="2" ry="2"/>
    <line x1="1" y1="10" x2="23" y2="10"/>
  </svg>`,
  'gift': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <polyline points="20,12 20,22 4,22 4,12"/>
    <rect x="2" y="7" width="20" height="5"/>
    <line x1="12" y1="22" x2="12" y2="7"/>
    <path d="M12 7H7.5a2.5 2.5 0 0 1 0-5C11 2 12 7 12 7z"/>
    <path d="M12 7h4.5a2.5 2.5 0 0 0 0-5C13 2 12 7 12 7z"/>
  </svg>`,
  'clock': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <circle cx="12" cy="12" r="10"/>
    <polyline points="12,6 12,12 16,14"/>
  </svg>`,
  'star': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/>
  </svg>`,
  // Ícones de compatibilidade (mantidos para componentes antigos)
  'money-back': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M21 12a9 9 0 0 0-9-9 9.75 9.75 0 0 0-6.74 2.74L3 8"/>
    <path d="M3 3v5h5M3 12a9 9 0 0 0 9 9 9.75 9.75 0 0 0 6.74-2.74L21 16"/>
    <path d="M16 16h5v5"/>
  </svg>`,
  'security': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M12 22s8-4 8-10V5l-8-3-8 3v7c0 6 8 10 8 10z"/>
    <path d="M9 12l2 2 4-4"/>
  </svg>`,
  'support': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <path d="M3 11v3a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-5a1 1 0 0 0-1-1H4a8 8 0 0 1 16 0h-1a1 1 0 0 0-1 1v5a1 1 0 0 0 1 1h1a1 1 0 0 0 1-1v-3"/>
    <path d="M19 14v4a2 2 0 0 1-2 2h-1"/>
  </svg>`,
  'shipping': `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
    <rect x="1" y="3" width="15" height="13"/>
    <path d="M16 8h4l3 3v5h-7V8zM16 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4zM7 21a2 2 0 1 0 0-4 2 2 0 0 0 0 4z"/>
  </svg>`
}

// Remover ícones duplicados/alias para evitar repetição no seletor
const duplicateAliases = {
  'shipping': 'truck',
  'support': 'headset',
  'money-back': 'refresh',
  'security': 'shield-check'
}

// Computed para exibir apenas ícones únicos (sem aliases duplicados)
const filteredIcons = computed(() => {
  return Object.fromEntries(
    Object.entries(benefitIcons).filter(([key]) => !Object.keys(duplicateAliases).includes(key))
  )
})

const currentBenefit = computed(() => {
  return benefits.value[currentBenefitIndex.value] || null
})

// Carrega dados do elemento DOM
const loadData = () => {
  if (!props.element) return
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'payment-benefits') {
      targetElement = props.element.closest('[data-component="payment-benefits"]')
    }
    
    if (!targetElement) return
    
    // Carregar título e subtítulo
    config.value.title = targetElement.getAttribute('data-title') || 'Por que escolher nossa loja?'
    config.value.subtitle = targetElement.getAttribute('data-subtitle') || 'Benefícios exclusivos para você'
    
    // Carregar benefícios com validação
    const benefitsData = targetElement.getAttribute('data-benefits')
    if (benefitsData && benefitsData.trim() !== '') {
      try {
        const parsedBenefits = JSON.parse(benefitsData)
        // Validar se é um array válido e não está vazio
        if (Array.isArray(parsedBenefits) && parsedBenefits.length > 0) {
          // Validar cada benefício
          benefits.value = parsedBenefits.filter(benefit => 
            benefit && 
            typeof benefit === 'object' && 
            benefit.id && 
            benefit.title && 
            benefit.description
          )
          
          // Se após filtragem não sobrou nenhum benefício válido, usar defaults
          if (benefits.value.length === 0) {
            benefits.value = getDefaultBenefits()
          }
        } else {
          benefits.value = getDefaultBenefits()
        }
      } catch (error) {
        console.warn('Erro ao fazer parse dos benefícios, usando padrões:', error)
        benefits.value = getDefaultBenefits()
      }
    } else {
      benefits.value = getDefaultBenefits()
    }

    // Garantir que currentBenefitIndex seja válido
    if (currentBenefitIndex.value >= benefits.value.length) {
      currentBenefitIndex.value = Math.max(0, benefits.value.length - 1)
    }

    // Carregar estilos com valores padrão em hex (compatíveis com color picker)
    sectionBackground.value = targetElement.getAttribute('data-section-background') || '#f9fafb'
    cardBackground.value = targetElement.getAttribute('data-card-background') || '#ffffff'
    titleColor.value = targetElement.getAttribute('data-title-color') || '#1f2937'
    textColor.value = targetElement.getAttribute('data-text-color') || '#6b7280'
    iconColor.value = targetElement.getAttribute('data-icon-color') || '#3b82f6'
    columns.value = targetElement.getAttribute('data-columns') || '3'
    spacing.value = parseInt(targetElement.getAttribute('data-spacing')) || 24
    cardPadding.value = parseInt(targetElement.getAttribute('data-card-padding')) || 20
    borderRadius.value = parseInt(targetElement.getAttribute('data-border-radius')) || 8
    titleFontSize.value = parseInt(targetElement.getAttribute('data-title-font-size')) || 18
    descriptionFontSize.value = parseInt(targetElement.getAttribute('data-description-font-size')) || 14
    iconSize.value = parseInt(targetElement.getAttribute('data-icon-size')) || 32
    
  } catch (error) {
    console.error('Erro ao carregar dados:', error)
    // Fallback para estado limpo em caso de erro
    benefits.value = getDefaultBenefits()
    currentBenefitIndex.value = 0
  }
}

// Salva dados no elemento DOM
const saveData = () => {
  if (!props.element) {
    console.warn('⚠️ [PaymentBenefits] Elemento não encontrado para salvar')
    return false
  }
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'payment-benefits') {
      targetElement = props.element.closest('[data-component="payment-benefits"]')
    }
    
    if (!targetElement) {
      console.warn('⚠️ [PaymentBenefits] Target element não encontrado')
      return false
    }
    

    
    // Salvar dados básicos
    targetElement.setAttribute('data-title', config.value.title)
    targetElement.setAttribute('data-subtitle', config.value.subtitle)
    targetElement.setAttribute('data-benefits', JSON.stringify(benefits.value))
    
    // Salvar estilos
    targetElement.setAttribute('data-section-background', sectionBackground.value)
    targetElement.setAttribute('data-card-background', cardBackground.value)
    targetElement.setAttribute('data-title-color', titleColor.value)
    targetElement.setAttribute('data-text-color', textColor.value)
    targetElement.setAttribute('data-icon-color', iconColor.value)
    targetElement.setAttribute('data-columns', columns.value)
    targetElement.setAttribute('data-spacing', spacing.value.toString())
    targetElement.setAttribute('data-card-padding', cardPadding.value.toString())
    targetElement.setAttribute('data-border-radius', borderRadius.value.toString())
    targetElement.setAttribute('data-title-font-size', titleFontSize.value.toString())
    targetElement.setAttribute('data-description-font-size', descriptionFontSize.value.toString())
    targetElement.setAttribute('data-icon-size', iconSize.value.toString())
    

    
    // Atualizar conteúdo visual
    updateElementContent(targetElement)
    applyStyles(targetElement)
    

    return true
  } catch (error) {
    console.error('❌ [PaymentBenefits] Erro ao salvar dados:', error)
    return false
  }
}

const getDefaultBenefits = () => {
  const timestamp = Date.now()
  return [
  {
      id: `benefit-${timestamp}-1`,
    title: 'Frete Grátis',
      description: 'Entrega gratuita para compras acima de R$ 99 em todo o Brasil',
      icon: 'truck',
    enabled: true
  },
  {
      id: `benefit-${timestamp}-2`, 
      title: 'Pagamento 100% Seguro',
      description: 'Suas informações protegidas com criptografia SSL de última geração',
      icon: 'shield-check',
    enabled: true
  },
  {
      id: `benefit-${timestamp}-3`,
      title: 'Garantia de Reembolso',
      description: '30 dias para devolução com reembolso total sem complicações',
      icon: 'refresh',
    enabled: true
  }
]
}

// Gerenciamento de benefícios
const selectBenefit = (index) => {
  currentBenefitIndex.value = index
  activeTab.value = 'benefits'
}

const addBenefit = () => {
  // Criar ID único baseado em timestamp + random para evitar conflitos
  const uniqueId = `benefit-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
  
  const newBenefit = {
    id: uniqueId,
    title: `Novo Benefício ${benefits.value.length + 1}`,
    description: 'Clique para editar a descrição deste benefício',
    icon: 'star',
    enabled: true
  }
  
  // Adicionar o novo benefício
  benefits.value.push(newBenefit)
  
  // Selecionar o novo benefício
  currentBenefitIndex.value = benefits.value.length - 1
  
  // Ir para a aba de edição
  activeTab.value = 'benefits'
  
  // Atualizar dados após um pequeno delay para garantir que o DOM foi atualizado
  setTimeout(() => {
    onBenefitDataChange()
  }, 100)
}

const removeBenefit = (index) => {
  if (benefits.value.length <= 1) return
  
  benefits.value.splice(index, 1)
  
  if (currentBenefitIndex.value >= benefits.value.length) {
    currentBenefitIndex.value = benefits.value.length - 1
  } else if (currentBenefitIndex.value > index) {
    currentBenefitIndex.value = currentBenefitIndex.value - 1
  }
  
  setTimeout(() => onBenefitDataChange(), 50)
}

const moveBenefitUp = (index) => {
  if (index === 0) return
  
  const benefit = benefits.value.splice(index, 1)[0]
  benefits.value.splice(index - 1, 0, benefit)
  
  if (currentBenefitIndex.value === index) {
    currentBenefitIndex.value = index - 1
  } else if (currentBenefitIndex.value === index - 1) {
    currentBenefitIndex.value = index
  }
  
  setTimeout(() => onBenefitDataChange(), 50)
}

const moveBenefitDown = (index) => {
  if (index === benefits.value.length - 1) return
  
  const benefit = benefits.value.splice(index, 1)[0]
  benefits.value.splice(index + 1, 0, benefit)
  
  if (currentBenefitIndex.value === index) {
    currentBenefitIndex.value = index + 1
  } else if (currentBenefitIndex.value === index + 1) {
    currentBenefitIndex.value = index
  }
  
  setTimeout(() => onBenefitDataChange(), 50)
}

const updateBenefitIcon = (iconName) => {
  if (currentBenefit.value) {

    
    currentBenefit.value.icon = iconName
    
    // Forçar atualização imediata da interface
    forceUpdateInterface()
    
    // Chamar a função de mudança de dados
    onBenefitDataChange()
  }
}

// Helpers
const getIconSvg = (iconName) => {
  return benefitIcons[iconName] || benefitIcons['star']
}

const getIconDisplayName = (iconName) => {
  const names = {
    'shield-check': 'Segurança',
    'truck': 'Frete',
    'refresh': 'Reembolso',
    'headset': 'Suporte',
    'credit-card': 'Pagamento',
    'gift': 'Presente',
    'clock': 'Tempo',
    'star': 'Qualidade',
    // Compatibilidade
    'money-back': 'Reembolso',
    'security': 'Segurança',
    'support': 'Suporte',
    'shipping': 'Frete'
  }
  return names[iconName] || iconName
}

const truncateText = (text, length) => {
  if (!text) return ''
  return text.length > length ? text.substring(0, length) + '...' : text
}

// Handlers
const onConfigDataChange = () => {
  emit('data-changed', { property: 'config', value: config.value })
  
  // NOVO: Emitir evento para o sistema de undo/redo
  emit('element-updated', {
    element: props.element,
    data: { config: config.value },
    description: null,
    type: 'config-change'
  })
  
  // Atualização imediata + backup com delay
  forceUpdateInterface()
  setTimeout(() => saveData(), 50)
}

// Função para forçar atualização imediata da interface
const forceUpdateInterface = () => {
  if (!props.element) return
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'payment-benefits') {
      targetElement = props.element.closest('[data-component="payment-benefits"]')
    }
    
    if (!targetElement) return
    

    
    // Atualização imediata sem delay
    updateElementContent(targetElement)
    applyStyles(targetElement)
    
    // Forçar re-render dos ícones na lista lateral
    const benefitPreviews = document.querySelectorAll('.benefit-icon-preview')
    benefitPreviews.forEach((preview, index) => {
      if (benefits.value[index] && benefits.value[index].icon) {
        preview.innerHTML = getIconSvg(benefits.value[index].icon)
      }
    })
    

  } catch (error) {
    console.error('❌ [PaymentBenefits] Erro ao forçar atualização:', error)
  }
}

const onBenefitDataChange = () => {
  emit('data-changed', { property: 'benefits', value: benefits.value })
  
  // NOVO: Emitir evento para o sistema de undo/redo
  emit('element-updated', {
    element: props.element,
    data: { benefits: benefits.value },
    description: null,
    type: 'content-change'
  })
  
  // Reduzir delay para atualização mais rápida
  setTimeout(() => saveData(), 50)
}

const onStyleChange = (property, value) => {

  
  emit('data-changed', { property, value })
  
  // NOVO: Emitir evento para o sistema de undo/redo
  emit('element-updated', {
    element: props.element,
    data: { [property]: value },
    description: null, // Será detectado automaticamente baseado na propriedade
    type: 'style-change'
  })
  
  // Atualização imediata + backup com delay
  forceUpdateInterface()
  setTimeout(() => saveData(), 50)
}

// Atualização do conteúdo visual
const updateElementContent = (targetElement) => {
  if (!targetElement) return
  
  try {

  
  // Atualizar título
    const titleEl = targetElement.querySelector('.benefits-title, .payment-benefits-title')
    if (titleEl) {
      titleEl.textContent = config.value.title

    }
    
    // Atualizar subtítulo
    const subtitleEl = targetElement.querySelector('.benefits-subtitle, .payment-benefits-subtitle')
    if (subtitleEl) {
      subtitleEl.textContent = config.value.subtitle

    }
    
    // Atualizar grid de benefícios - compatível com loader
  const gridEl = targetElement.querySelector('.benefits-grid')
  if (gridEl) {
      const enabledBenefits = benefits.value.filter(benefit => benefit.enabled)

      
      const newHTML = enabledBenefits.map(benefit => {
        const iconSvg = getIconSvg(benefit.icon)

        
        return `
          <article class="benefit-card" data-benefit-id="${benefit.id}">
            <div class="benefit-icon" style="color: ${iconColor.value}; background: ${iconColor.value};">
              ${iconSvg}
        </div>
        <div class="benefit-content">
          <h3 class="benefit-title">${benefit.title}</h3>
          <p class="benefit-description">${benefit.description}</p>
        </div>
          </article>
        `
      }).join('')
      
      gridEl.innerHTML = newHTML

      
      // Forçar re-aplicação dos estilos nos ícones
      setTimeout(() => {
        const iconElements = gridEl.querySelectorAll('.benefit-icon svg')
        iconElements.forEach((svg, index) => {
          svg.style.color = 'white'
          svg.style.width = iconSize.value + 'px'
          svg.style.height = iconSize.value + 'px'
        })
      }, 10)
    } else {
      console.warn('⚠️ Grid element não encontrado')
    }
    

  } catch (error) {
    console.error('❌ [PaymentBenefits] Erro ao atualizar conteúdo:', error)
  }
}

// Aplicação de estilos visuais
const applyStyles = (targetElement) => {
  if (!targetElement) return
  
  try {
    // Seletores compatíveis com loader
    const container = targetElement.querySelector('.benefits-container, .payment-benefits-container')
    const benefitItems = targetElement.querySelectorAll('.benefit-card, .benefit-item')
    const titleEl = targetElement.querySelector('.benefits-title, .payment-benefits-title')
  const gridEl = targetElement.querySelector('.benefits-grid')
  const benefitTitles = targetElement.querySelectorAll('.benefit-title')
  const benefitDescriptions = targetElement.querySelectorAll('.benefit-description')
    const benefitIcons = targetElement.querySelectorAll('.benefit-icon svg, .benefit-icon')
  
  // Aplicar estilos de seção
  targetElement.style.background = sectionBackground.value
  
  // Aplicar estilos de grid
  if (gridEl) {
      gridEl.setAttribute('data-columns', columns.value)
    gridEl.style.gridTemplateColumns = `repeat(${columns.value}, 1fr)`
    gridEl.style.gap = spacing.value + 'px'
  }
  
  // Aplicar estilos de cards
  benefitItems.forEach(item => {
    item.style.background = cardBackground.value
    item.style.borderRadius = borderRadius.value + 'px'
    item.style.padding = cardPadding.value + 'px'
      item.style.overflow = 'visible'
  })
  
  // Aplicar estilos de texto
  if (titleEl) {
    titleEl.style.color = titleColor.value
      titleEl.style.fontSize = titleFontSize.value + 'px'
  }
  
  benefitTitles.forEach(el => {
    el.style.color = titleColor.value
    el.style.fontSize = titleFontSize.value + 'px'
    el.style.whiteSpace = 'normal'
    el.style.wordBreak = 'break-word'
    el.style.padding = '0 8px'
  })
  
  benefitDescriptions.forEach(el => {
    el.style.color = textColor.value
    el.style.fontSize = descriptionFontSize.value + 'px'
  })
  
  // Aplicar estilos de ícones
    benefitIcons.forEach(iconContainer => {
      if (iconContainer.tagName === 'svg') {
        iconContainer.style.color = iconColor.value
        iconContainer.style.width = iconSize.value + 'px'
        iconContainer.style.height = iconSize.value + 'px'
      } else {
        const svg = iconContainer.querySelector('svg')
        if (svg) {
          svg.style.color = iconColor.value
          svg.style.width = iconSize.value + 'px'
          svg.style.height = iconSize.value + 'px'
        }
        iconContainer.style.padding = '8px'
        iconContainer.style.boxSizing = 'border-box'
      }
    })
    

  } catch (error) {
    console.error('❌ [PaymentBenefits] Erro ao aplicar estilos:', error)
  }
}

// Inicialização
onMounted(() => {

  
  loadData()
  
  // Validação adicional para prevenir duplicações
  setTimeout(() => {
    // Remover benefícios duplicados se houver
    const uniqueBenefits = []
    const seenIds = new Set()
    
    benefits.value.forEach(benefit => {
      if (benefit && benefit.id && !seenIds.has(benefit.id)) {
        seenIds.add(benefit.id)
        uniqueBenefits.push(benefit)
      }
    })
    
    // Se encontrou duplicatas, corrigir
    if (uniqueBenefits.length !== benefits.value.length) {
      console.warn('Benefícios duplicados detectados e removidos')
      benefits.value = uniqueBenefits
      
      // Ajustar índice se necessário
      if (currentBenefitIndex.value >= uniqueBenefits.length) {
        currentBenefitIndex.value = Math.max(0, uniqueBenefits.length - 1)
      }
    }
    
    // Garantir que há pelo menos 1 benefício
    if (benefits.value.length === 0) {
      console.warn('Nenhum benefício válido encontrado, carregando padrões')
      benefits.value = getDefaultBenefits()
      currentBenefitIndex.value = 0
    }
    
    // Forçar primeira atualização da interface

    forceUpdateInterface()
  }, 200)
  
  // Debug: Monitorar mudanças nos benefícios
  benefits.value.forEach((benefit, index) => {
      
  })
})
</script>

<style scoped>
.payment-benefits-sidebar-editor {
  padding: 0;
  background: var(--iluria-color-background);
  color: var(--iluria-color-text-primary);
}

.editor-tabs {
  display: flex;
  gap: 8px;
  border-bottom: 1px solid var(--iluria-color-border);
  margin-bottom: 16px;
  background: var(--iluria-color-surface);
  border-radius: 8px 8px 0 0;
  padding: 8px;
}

.tab-button {
  flex: 1;
}

.tab-content {
  min-height: 300px;
  background: var(--iluria-color-container-bg);
  border-radius: 0 0 12px 12px;
  box-shadow: var(--iluria-shadow-md);
}

.tab-panel {
  padding: 20px;
}

.form-group {
  margin-bottom: 20px;
}

.benefits-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  padding: 16px;
  background: var(--iluria-color-surface);
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
}

.benefits-title {
  margin: 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.75rem 0;
  line-height: 1.3;
  letter-spacing: -0.01em;
  white-space: normal;
  word-break: break-word;
  padding: 0 8px;
}

.benefits-list {
  max-height: 400px;
  overflow-y: auto;
  padding: 4px;
}

/* Scrollbar moderno */
.benefits-list::-webkit-scrollbar {
  width: 6px;
}

.benefits-list::-webkit-scrollbar-track {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.benefits-list::-webkit-scrollbar-thumb {
  background: var(--iluria-color-primary);
  border-radius: 3px;
}

.benefits-list::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-secondary);
}

.benefit-item {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 16px;
  margin-bottom: 16px;
  background: var(--iluria-color-surface);
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  box-shadow: var(--iluria-shadow-sm);
  position: relative;
  overflow: hidden;
}

.benefit-item::before {
  content: '';
  position: absolute;
  left: 0;
  top: 0;
  bottom: 0;
  width: 4px;
  background: transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.benefit-item:hover {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-hover);
  transform: none;
  box-shadow: var(--iluria-shadow-md);
}

.benefit-item:hover::before {
  background: var(--iluria-color-primary);
}

.benefit-item.active {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-container-bg);
  transform: none;
  box-shadow: var(--iluria-shadow-lg);
}

.benefit-item.active::before {
  background: var(--iluria-color-primary);
  width: 6px;
}

.benefit-preview {
  display: flex;
  align-items: center;
  gap: 16px;
}

.benefit-icon-preview {
  width: 48px;
  height: 48px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-md);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.benefit-item:hover .benefit-icon-preview {
  transform: rotate(5deg) scale(1.1);
  box-shadow: var(--iluria-shadow-lg);
}

.benefit-info {
  flex: 1;
  min-width: 0;
}

.benefit-info h4 {
  margin: 0 0 6px 0;
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.4;
  word-break: break-word;
}

.benefit-info p {
  margin: 0 0 8px 0;
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.5;
  word-break: break-word;
}

.benefit-status {
  font-size: 11px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 20px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  background: var(--iluria-color-error);
  color: var(--iluria-color-primary-contrast);
  display: inline-block;
  box-shadow: var(--iluria-shadow-sm);
}

.benefit-status.enabled {
  background: var(--iluria-color-success);
  color: var(--iluria-color-primary-contrast);
}

.benefit-controls {
  display: flex;
  gap: 8px;
  margin-top: 12px;
  padding-top: 12px;
  border-top: 1px solid var(--iluria-color-border);
}

.benefit-editor {
  background: var(--iluria-color-surface);
  padding: 24px;
  border-radius: 16px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-lg);
  margin: 16px 0;
}

.icon-selector {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 12px;
  margin-top: 16px;
  padding: 16px;
  background: var(--iluria-color-background);
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
}

.icon-option {
  padding: 16px 12px;
  border: 2px solid var(--iluria-color-border);
  border-radius: 12px;
  cursor: pointer;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: var(--iluria-color-surface);
  box-shadow: var(--iluria-shadow-sm);
  position: relative;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
}

.icon-option::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(135deg, var(--iluria-color-primary), var(--iluria-color-secondary));
  opacity: 0;
  transition: opacity 0.3s ease;
}

.icon-option:hover {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-hover);
  transform: none;
  box-shadow: var(--iluria-shadow-md);
}

.icon-option:hover::before {
  opacity: 0.1;
}

.icon-option.active {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-container-bg);
  transform: none;
  box-shadow: var(--iluria-shadow-lg);
}

.icon-option.active::before {
  opacity: 0.15;
}

.icon-preview {
  color: var(--iluria-color-primary);
  margin: 0;
  position: relative;
  z-index: 1;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.icon-option:hover .icon-preview,
.icon-option.active .icon-preview {
  transform: scale(1.1);
}

.icon-name {
  display: none;
}

.design-section {
  margin-bottom: 32px;
  padding: 20px;
  background: var(--iluria-color-surface);
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
  box-shadow: var(--iluria-shadow-sm);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.design-section:hover {
  box-shadow: var(--iluria-shadow-md);
}

.design-section:last-child {
  margin-bottom: 0;
}

.design-section h3 {
  margin: 0 0 20px 0;
  font-size: 18px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  letter-spacing: -0.025em;
  padding-bottom: 12px;
  border-bottom: 2px solid var(--iluria-color-primary);
  position: relative;
}

.design-section h3::after {
  content: '';
  position: absolute;
  bottom: -2px;
  left: 0;
  width: 40px;
  height: 2px;
  background: var(--iluria-color-secondary);
  border-radius: 1px;
}

/* Estilos para a aba Design melhorada */
.design-tab {
  padding: 0 !important;
}

.design-scroll-container {
  max-height: 500px;
  overflow-y: auto;
  overflow-x: visible;
  padding: 20px 20px 40px 20px;
  scroll-behavior: smooth;
}

.design-scroll-container::-webkit-scrollbar {
  width: 6px;
}

.design-scroll-container::-webkit-scrollbar-track {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.design-scroll-container::-webkit-scrollbar-thumb {
  background: var(--iluria-color-primary);
  border-radius: 3px;
}

.design-scroll-container::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-secondary);
}

.design-scroll-container > * {
  overflow: visible;
}

.priority-section {
  border: 2px solid var(--iluria-color-primary);
  position: relative;
  background: linear-gradient(135deg, var(--iluria-color-surface) 0%, var(--iluria-color-container-bg) 100%);
}

.priority-section::before {
  content: '⭐ Prioridade';
  position: absolute;
  top: -12px;
  left: 16px;
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast);
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 11px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.design-section h3 {
  display: flex;
  align-items: center;
  gap: 12px;
}

.design-section h3 svg {
  color: var(--iluria-color-primary);
  flex-shrink: 0;
}

/* Seletor visual de colunas */
.columns-selector {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 8px;
  margin-top: 12px;
}

.column-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 8px;
}

.column-preview {
  display: flex;
  gap: 2px;
}

.column-preview[data-columns="1"] {
  width: 20px;
}

.column-preview[data-columns="2"] {
  width: 24px;
}

.column-preview[data-columns="3"] {
  width: 28px;
}

.column-preview[data-columns="4"] {
  width: 32px;
}

.column-item {
  width: 6px;
  height: 16px;
  background: var(--iluria-color-primary);
  border-radius: 2px;
  transition: all 0.3s ease;
}

/* Grid de cores melhorado */
.color-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px 16px;
}

/* Extra spacing between sections to avoid overlap when widgets open */
.design-section + .design-section {
  margin-top: 32px;
}

.icon-preview svg {
  width: 28px;
  height: 28px;
}
</style> 
