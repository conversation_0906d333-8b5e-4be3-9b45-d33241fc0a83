{"title": "SEO Settings", "storeSectionTitle": "Store Information", "searchEnginesSectionTitle": "Search Engines Information", "useCategoryImage": "Use category image as SEO image", "metaTitle": "Page Title (meta-title)", "metaDescription": "Page Description (meta-description)", "metaDescriptionPlaceholder": "", "metaKeywords": "Keywords (comma-separated)", "metaRobots": "Instructions for search engine robots", "canonicalUrl": "Canonical URL", "ogTitle": "Social Media Title (og:title)", "ogDescription": "Social Media Description (og:description)", "ogType": "Content Type (og:type)", "ogImage": "Social Media Image (og:image)", "twitterCard": "Twitter Card Type", "twitterTitle": "Twitter Title", "twitterDescription": "Twitter Description", "twitterImage": "Twitter Image", "saveSuccess": "SEO settings saved successfully", "saveError": "Error saving SEO settings", "loadError": "Error loading SEO settings", "generateFromContent": "Generate automatically from content", "preview": "Preview", "seoPreview": "Search Result Preview", "pageTitle": "Page Title (meta-title)", "pageUrl": "Page URL", "pageDescription": "Page Description", "characters": "characters", "recommendedLengths": {"title": "Recommended: 50-60 characters", "description": "Recommended: 150-160 characters"}, "socialPreview": "Social Media Preview", "socialTitle": "Title", "socialDescription": "Description", "socialImage": "Image", "uploadImage": "Upload Image", "changeImage": "Change Image", "removeImage": "Remove Image", "imageRequirements": "Image should be at least 1200x630 pixels", "advancedSettings": "Advanced Settings", "schemaMarkup": "Schema.org Markup", "customMetaTags": "Custom Meta Tags", "addCustomTag": "Add Custom Tag", "tagName": "Tag Name", "tagContent": "Tag Content", "removeTag": "Remove Tag", "saveBeforePreview": "Save changes to preview updates", "slug": "Product URL (slug)", "slugHelp": "Informações da página para melhorar visibilidade em buscadores.", "slugPlaceholder": "Esse texto fará parte da URL"}