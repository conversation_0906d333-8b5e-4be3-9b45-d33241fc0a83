<template>
  <div class="domain-form-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="isEditing ? t('domainUrl.editFormTitle') : t('domainUrl.formTitle')"
      :subtitle="isEditing ? t('domainUrl.editFormSubtitle') : t('domainUrl.formSubtitle')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="isEditing ? t('update') : t('save')"
      @cancel-click="router.push('/settings/domain-manager')"
      @save-click="handleSubmit"
    />

    <!-- Main Content -->
    <Form 
      @submit="handleSubmit" 
      v-slot="$form" 
      :resolver="resolver" 
      :validate-on-value-update="false" 
      :validate-on-blur="true"
      class="form-container"
    >
      
      <!-- Configurações do Domínio -->
      <ViewContainer 
        :title="t('domainUrl.domainSettings')"
        :subtitle="t('domainUrl.domainSettingsSubtitle')"
        :icon="WebDesignIcon"
        iconColor="blue"
      >
        <div class="form-grid">
          <div class="form-field">
            <IluriaInputText
              id="name"
              name="name"
              :label="t('domainUrl.name')"
              v-model="form.name"
              :formContext="$form.name"
              :placeholder="t('domainUrl.namePlaceholder')"
            />
          </div>

          <div class="form-field">
            <IluriaRadioGroup
              id="domainUrlType"
              name="domainUrlType"
              :label="t('domainUrl.type')"
              v-model="form.domainUrlType"
              :formContext="$form.domainUrlType"
              :options="[
                { label: t('domainUrl.main'), value: 'MAIN' },
                { label: t('domainUrl.redirection'), value: 'REDIRECTION' },
              ]"
            />
          </div>
        </div>
      </ViewContainer>

      <!-- Containers de informação lado a lado -->
      <div class="grid grid-cols-1 lg:grid-cols-2 gap-5">
        <!-- Domínio Principal -->
        <ViewContainer 
          :title="t('domainUrl.mainDomainTitle')"
          :subtitle="t('domainUrl.mainDomainSubtitle')"
          :icon="StarIcon"
          iconColor="yellow"
        >
          <div class="tip-text">
            {{ t('domainUrl.mainDomainTip') }}
          </div>
        </ViewContainer>

        <!-- Redirecionamento -->
        <ViewContainer 
          :title="t('domainUrl.redirectionTitle')"
          :subtitle="t('domainUrl.redirectionSubtitle')"
          :icon="Link01Icon"
          iconColor="blue"
        >
          <div class="tip-text">
            {{ t('domainUrl.redirectionTip') }}
          </div>
        </ViewContainer>
      </div>
    </Form>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { useRouter } from 'vue-router'
import { ref, onMounted, computed } from 'vue'
import { useToast } from '@/services/toast.service'
import domainsService from '@/services/domain.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import { Form } from '@primevue/forms'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { requiredText } from '@/services/validation.service'
import { z } from 'zod'
import { 
  FloppyDiskIcon,
  WebDesignIcon,
  StarIcon,
  Link01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const toast = useToast()
const router = useRouter()
const { t } = useI18n()

const saving = ref(false)
const form = ref({
    name: '',
  domainUrlType: 'MAIN',
    status: false,
})

const props = defineProps({
    id: {
        type: String,
        required: false
    }
})

const isEditing = computed(() => !!props.id)

const resolver = zodResolver(
    z.object({
        name: requiredText(t('domainUrl.name')),
    })
)

async function handleSubmit() {
  saving.value = true
  
  try {
    if (props.id) {
            await domainsService.updateDomainUrl(props.id, form.value)
            toast.showSuccess(t('domainUrl.updateSuccess'))
    } else {
            await domainsService.createDomainUrl(form.value)
            toast.showSuccess(t('domainUrl.createSuccess'))
    }
            router.push('/settings/domain-manager')
  } catch {
    if (props.id) {
      toast.showError(t('domainUrl.updateError'))
    } else {
            toast.showError(t('domainUrl.createError'))
        }
  } finally {
    saving.value = false
    }
}

async function loadDomainUrl() {
  try {
    const domainUrl = await domainsService.getDomainUrl(props.id)
    form.value = domainUrl
  } catch (error) {
    console.error('Error loading domain:', error)
    toast.showError('Erro ao carregar domínio')
    router.push('/settings/domain-manager')
  }
}

onMounted(() => {
    if (props.id) {
        loadDomainUrl()
  } else {
    // Para novos domínios, garantir que MAIN seja o padrão
    form.value.domainUrlType = 'MAIN'
    }
})
</script>

<style scoped>
.domain-form-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



/* Form Container */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Form Grid */
.form-grid {
  display: grid;
  grid-template-columns: 1fr;
  gap: 20px;
}

.form-field {
  display: flex;
  flex-direction: column;
}

/* Tip Text */
.tip-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

/* Responsive */
@media (max-width: 768px) {
  .domain-form-container {
    padding: 16px;
  }
  
  .form-container {
    gap: 20px;
  }
}

@media (max-width: 480px) {
  .domain-form-container {
    padding: 12px;
  }
}

/* Form validation styles */
:deep(.p-form-invalid) {
  border-color: #ef4444 !important;
}

:deep(.p-form-error) {
  color: #ef4444;
  font-size: 12px;
  margin-top: 4px;
}
</style>
