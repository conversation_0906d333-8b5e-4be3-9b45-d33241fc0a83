<template>
  <LoginPage>
    <div v-if="recoveryId && email">
      <VeeForm @submit="changePassword" :validation-schema="schema">
        <div class="mb-4 relative">
          <IluriaInputText
            name="newPassword"
            :type="newPasswordType"
            v-model="newPassword"
            :label="$t('passwordRecovery.newPassword')"
            :class="{'error': !isPasswordValid}"
            required
          >
            <template #append>
              <ShowHidePasswordButton 
                :passwordInputType="newPasswordType" 
                @passwordTypeChange="changePasswordType('new')"
              />
            </template>
          </IluriaInputText>
          <ErrorMessage name="newPassword" v-slot="{ message }">
            <div v-html="message" class="error-message"></div>
          </ErrorMessage>
        </div>

        <div class="mb-4 relative">
          <IluriaInputText
            name="confirmPassword"
            :type="confirmPasswordType"
            v-model="confirmPassword"
            :label="$t('passwordRecovery.confirmPassword')"
            :class="{'error': passwordMismatch}"
            required
            inputClass="bg-white pr-10"
          >
            <template #append>
              <ShowHidePasswordButton 
                :passwordInputType="confirmPasswordType" 
                @passwordTypeChange="changePasswordType('confirm')"
              />
            </template>
          </IluriaInputText>
          <ErrorMessage name="confirmPassword" class="error-message" />
        </div>

        <p class="password-example mb-4">{{ $t('passwordRecovery.passwordExample') }}</p>

        <IluriaButton 
          type="submit" 
          :disabled="loading || !isPasswordValid || passwordMismatch"
        >
          {{ loading ? $t('passwordRecovery.loading') : $t('passwordRecovery.submit') }}
        </IluriaButton>
      </VeeForm>

      <div v-if="error" class="error-message mt-4 p-2 bg-red-100 rounded-md">{{ error }}</div>
    </div>

    <div v-else>
      <p>{{ $t('passwordRecovery.invalidLink') }}</p>
    </div>
  </LoginPage>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue';
import { useToast } from '@/services/toast.service';
import { useAuthStore } from '@/stores/auth.store';
import { useRoute } from 'vue-router';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import ShowHidePasswordButton from '@/components/login/ShowHidePasswordButton.vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import LoginPage from '@/components/login/LoginPage.vue';
import { Form as VeeForm, Field as VeeField, ErrorMessage } from 'vee-validate';
import * as yup from 'yup';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const router = useRouter();
const { t } = useI18n();
const { addToast } = useToast();
const authStore = useAuthStore();
const route = useRoute();

const newPassword = ref('');
const confirmPassword = ref('');
const loading = ref(false);
const error = ref('');
const recoveryId = ref(null);
const email = ref(null);
const newPasswordType = ref('password');
const confirmPasswordType = ref('password');

const passwordPattern = /^(?=.*[A-Za-z])(?=.*\d)(?=.*[!@#$%^&*(),.?":{}|<>])[A-Za-z\d!@#$%^&*(),.?":{}|<>]{8,}$/;

const schema  = yup.object({
    newPassword: yup.string()
    .required(() => t('userSettings.newPasswordRequired'))
    .test('password-strength', '', function (value) {
      const errors = [];
      if (!/[A-Z]/.test(value)) errors.push(t('passwordRecovery.passwordUpperCase'));
      if (!/[a-z]/.test(value)) errors.push(t('passwordRecovery.passwordLowerCase'));
      if (!/\d/.test(value)) errors.push(t('passwordRecovery.passwordNumber'));
      if (!/[\W_]/.test(value)) errors.push(t('passwordRecovery.passwordSpecialChar'));
      if (value.length < 8) errors.push(t('passwordRecovery.passwordMinLength'));
    
      return errors.length ? this.createError({ message: errors.join('<br>') }) : true;
    }),

    confirmPassword: yup.string()
      .oneOf([yup.ref('newPassword')], () => t('passwordRecovery.passwordMismatch'))
      .required(() => t('userSettings.confirmNewPasswordRequired'))
  });

const passwordMismatch = computed(() => newPassword.value !== confirmPassword.value);

const isPasswordValid = computed(() => {
  return passwordPattern.test(newPassword.value);
});

onMounted(() => {
  const { recoveryId: recoveryIdParam, email: emailParam } = route.query;

  if (recoveryIdParam && emailParam) {
    recoveryId.value = recoveryIdParam;
    email.value = emailParam;
  } else {
    error.value = t('passwordRecovery.invalidLink');
  }
});

const changePasswordType = (type) => {
  if (type === 'new') {
    newPasswordType.value = newPasswordType.value === 'password' ? 'text' : 'password';
  } else if (type === 'confirm') {
    confirmPasswordType.value = confirmPasswordType.value === 'password' ? 'text' : 'password';
  }
};

const changePassword = async () => {
  if (newPassword.value !== confirmPassword.value) {
    error.value = t('passwordRecovery.passwordMismatch');
    return;
  }

  if (!isPasswordValid.value) {
    error.value = t('passwordRecovery.weakPassword');
    return;
  }

  loading.value = true;
  error.value = '';

  try {
    const response = await authStore.RequestedchangePassword(
      recoveryId.value,
      email.value,
      newPassword.value
    );

    if (response.success) {
      addToast(t('passwordRecovery.passwordChangedSuccess'), 'success');
      router.push('/login');
    } else {
      addToast(response.message || t('passwordRecovery.errorChangingPassword'), 'error');
    }
  } catch (err) {
    addToast(t('passwordRecovery.passwordChangeFailed'), 'error');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.error-message {
  color: red;
  font-size: 0.9em;
}

.password-example {
  font-size: 0.9em;
  color: #555;
}

</style>
