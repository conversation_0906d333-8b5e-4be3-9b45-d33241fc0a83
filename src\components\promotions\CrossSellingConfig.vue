<template>
  <div class="mb-6">
    <!-- Discount to Apply -->
    <div class="mb-6">
      <IluriaLabel>{{ t('promotions.editor.discountToApply') }}</IluriaLabel>
      <p class="text-sm text-gray-500 mb-3">{{ t('promotions.editor.discountToApplyDescription') }}</p>
      <div class="relative">
        <IluriaInputText
          v-model.number="modelValue.discountValue"
          type="number"
          :min="0"
          :max="100"
          :step="0.01"
          suffix="%"
          class="w-full md:w-1/3"
          @update:modelValue="emitUpdate"
        />
      </div>
    </div>

    <!-- Product to Receive Discount -->
    <div class="mb-6">
      <IluriaRadioGroup
        id="target-config"
        v-model="modelValue.targetConfig"
        :options="[
          { value: 'HIGHEST_VALUE', label: t('promotions.editor.highestValueProduct') },
          { value: 'LOWEST_VALUE', label: t('promotions.editor.lowestValueProduct') }
        ]"
        :label="t('promotions.editor.productToReceiveDiscount')"
        direction="horizontal"
        @update:modelValue="emitUpdate"
      />
    </div>

    <!-- Trigger Type - Define o escopo do cross-selling (não confundir com trigger_type do backend) -->
    <div class="mb-6">
      <IluriaRadioGroup
        id="trigger-type"
        v-model="modelValue.triggerType"
        :options="[
          { value: 'ALL_STORE', label: t('promotions.editor.allStore') },
          { value: 'CATEGORIES', label: t('promotions.editor.categories') },
          { value: 'PRODUCTS', label: t('promotions.editor.products') }
        ]"
        :label="t('promotions.editor.trigger')"
        direction="horizontal"
        @update:modelValue="emitUpdate"
      />
      <p class="text-sm text-gray-500 mt-1 mb-3">{{ t('promotions.editor.triggerDescription') }}</p>

      <!-- Category Selector -->
      <div v-if="modelValue.triggerType === 'CATEGORIES'" class="mb-4">
        <IluriaMultipleCategorySelector
          v-model="selectedCategoryIds"
          :placeholder="t('promotions.editor.selectCategories')"
          class="border border-gray-200 rounded-md p-2"
        />
      </div>

      <!-- Product Selector -->
      <div v-if="modelValue.triggerType === 'PRODUCTS'" class="mb-4">
        <div class="flex flex-wrap gap-2 mb-2">
          <div v-for="product in modelValue.selectedTriggerProducts.filter(p => p.type === 'TRIGGER' || !p.type)" :key="product.id" 
               class="inline-flex items-center px-3 py-2 rounded-md bg-gray-100 border border-gray-200 shadow-sm">
            <!-- Imagem do produto se disponível -->
            <div v-if="product.imageUrl" class="w-8 h-8 mr-2 bg-white rounded-md overflow-hidden flex-shrink-0">
              <img :src="product.imageUrl" :alt="product.name" class="w-full h-full object-cover" />
            </div>
            <div v-else class="w-8 h-8 mr-2 bg-gray-200 rounded-md flex items-center justify-center flex-shrink-0">
              <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4V5h12v10z" clip-rule="evenodd" />
              </svg>
            </div>
            <!-- Informações do produto -->
            <div class="flex flex-col">
              <span class="text-sm font-medium">{{ product.name || product.productName || `Produto ${product.id || product.productId || ''}` }}</span>
              <span v-if="product.sku" class="text-xs text-gray-500">SKU: {{ product.sku }}</span>
              <span v-if="product.price" class="text-xs text-gray-500">{{ formatPrice(product.price) }}</span>
            </div>
            <!-- Botão de remover -->
            <IluriaButton
              size="small"
              color="ghost"
              :hugeIcon="Cancel01Icon"
              @click="removeSelectedTriggerProduct(product)"
              class="ml-2"
              :title="'Remover produto'"
            />
          </div>
        </div>
        <IluriaButton
          color="primary"
          @click="openTriggerProductSelector"
        >
          {{ t('promotions.editor.selectProducts') }}
        </IluriaButton>
      </div>
    </div>

    <!-- Suggested Products -->
    <div class="mb-6">
      <IluriaLabel>{{ t('promotions.editor.suggestedProducts') }}</IluriaLabel>
      <p class="text-sm text-gray-500 mb-3">{{ t('promotions.editor.suggestedProductsDescription') }}</p>
      <div class="flex flex-wrap gap-2 mb-2">
        <div v-for="product in modelValue.selectedSuggestedProducts" :key="product.id" 
             class="inline-flex items-center px-3 py-2 rounded-md bg-gray-100 border border-gray-200 shadow-sm">
          <!-- Imagem do produto se disponível -->
          <div v-if="product.imageUrl" class="w-8 h-8 mr-2 bg-white rounded-md overflow-hidden flex-shrink-0">
            <img :src="product.imageUrl" :alt="product.name" class="w-full h-full object-cover" />
          </div>
          <div v-else class="w-8 h-8 mr-2 bg-gray-200 rounded-md flex items-center justify-center flex-shrink-0">
            <svg class="w-4 h-4 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
              <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4V5h12v10z" clip-rule="evenodd" />
            </svg>
          </div>
          <!-- Informações do produto -->
          <div class="flex flex-col">
            <span class="text-sm font-medium">{{ product.name }}</span>
            <span v-if="product.sku" class="text-xs text-gray-500">SKU: {{ product.sku }}</span>
            <span v-if="product.price" class="text-xs text-gray-500">{{ formatPrice(product.price) }}</span>
          </div>
          <!-- Botão de remover -->
          <IluriaButton
            size="small"
            color="ghost"
            :hugeIcon="Cancel01Icon"
            @click="removeSelectedSuggestedProduct(product)"
            class="ml-2"
            :title="'Remover produto'"
          />
        </div>
      </div>
      <IluriaButton
        @click="openSuggestedProductSelector"
        color="primary"
      >
        {{ t('promotions.editor.selectProducts') }}
      </IluriaButton>
    </div>

    <!-- Product Selector Modal -->
    <ProductSelectorModal
      :visible="showTriggerProductSelector"
      @update:visible="closeTriggerProductSelector"
      @select="selectTriggerProduct"
    />

    <ProductSelectorModal
      :visible="showSuggestedProductSelector"
      @update:visible="closeSuggestedProductSelector"
      @select="selectSuggestedProduct"
    />
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
import { categoryService } from '@/services/category.service';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import ProductSelectorModal from '@/components/products/ProductSelectorModal.vue';
import IluriaMultipleCategorySelector from '@/components/iluria/form/IluriaMultipleCategorySelector.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { Cancel01Icon } from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const showTriggerProductSelector = ref(false);
const showSuggestedProductSelector = ref(false);
const selectedCategoryIds = ref([]);
const allCategories = ref([]);

onMounted(async () => {
  try {
    allCategories.value = await categoryService.fetchCategories() || [];
  } catch (error) {
    console.error('Error loading categories:', error);
  }
});

function findCategoryNameById(categoryId) {
  if (!categoryId) return '';
  
  function searchInCategories(categories, id) {
    if (!categories || !Array.isArray(categories)) return null;
    
    for (const category of categories) {
      if (category.id && category.id.toString() === id.toString()) {
        return category.title || '';
      }
      
      if (category.children && Array.isArray(category.children)) {
        const found = searchInCategories(category.children, id);
        if (found) return found;
      }
    }
    
    return null;
  }
  
  return searchInCategories(allCategories.value, categoryId) || '';
}

const openTriggerProductSelector = () => {
  showTriggerProductSelector.value = true;
};

const closeTriggerProductSelector = (value) => {
  showTriggerProductSelector.value = value;
  if (!value) {
    showTriggerProductSelector.value = false;
  }
};

const selectTriggerProduct = (products) => {
  if (Array.isArray(products)) {
    products.forEach(product => {
      const exists = props.modelValue.selectedTriggerProducts.some(p => 
        p.id === product.id || p.productId === product.id
      );
      
      if (!exists) {
        const formattedProduct = {
          id: product.id,
          productId: product.id,
          name: product.name,
          productName: product.name,
          price: product.price || 0,
          sku: product.sku || '',
          imageUrl: product.imageUrl || '',
          position: product.position || 0,
          variation: product.variation || null
        };
        
        props.modelValue.selectedTriggerProducts.push(formattedProduct);
      }
    });
    
    emitUpdate();
  }
  showTriggerProductSelector.value = false;
};

const removeSelectedTriggerProduct = (product) => {
  const index = props.modelValue.selectedTriggerProducts.findIndex(p => p.id === product.id);
  if (index !== -1) {
    props.modelValue.selectedTriggerProducts.splice(index, 1);
    emitUpdate();
  }
};

const openSuggestedProductSelector = () => {
  showSuggestedProductSelector.value = true;
};

const closeSuggestedProductSelector = (value) => {
  showSuggestedProductSelector.value = value;
  if (!value) {
    showSuggestedProductSelector.value = false;
  }
};

const selectSuggestedProduct = (products) => {
  if (Array.isArray(products)) {
    products.forEach(product => {
      const exists = props.modelValue.selectedSuggestedProducts.some(p => 
        p.id === product.id || p.productId === product.id
      );
      
      if (!exists) {
        const formattedProduct = {
          id: product.id,
          productId: product.id,
          name: product.name,
          productName: product.name,
          price: product.price || 0,
          sku: product.sku || '',
          imageUrl: product.imageUrl || '',
          position: product.position || 0,
          variation: product.variation || null
        };
        
        props.modelValue.selectedSuggestedProducts.push(formattedProduct);
      }
    });
    
    emitUpdate();
  }
  showSuggestedProductSelector.value = false;
};

const removeSelectedSuggestedProduct = (product) => {
  const index = props.modelValue.selectedSuggestedProducts.findIndex(p => p.id === product.id);
  if (index !== -1) {
    props.modelValue.selectedSuggestedProducts.splice(index, 1);
    emitUpdate();
  }
};

const emitUpdate = () => {

  if (!Array.isArray(props.modelValue.selectedCategories)) {
    props.modelValue.selectedCategories = [];
  }
  
  if (!Array.isArray(props.modelValue.selectedTriggerProducts)) {
    props.modelValue.selectedTriggerProducts = [];
  }
  
  if (!Array.isArray(props.modelValue.selectedSuggestedProducts)) {
    props.modelValue.selectedSuggestedProducts = [];
  }
  
  emit('update:modelValue', props.modelValue);
};

const unselectCategory = (categoryId) => {
  if (!Array.isArray(props.modelValue.selectedCategories)) {
    props.modelValue.selectedCategories = [];
    return;
  }
  
  props.modelValue.selectedCategories = props.modelValue.selectedCategories.filter(category => {
    const catId = category.id ? category.id.toString() : '';
    const catCategoryId = category.categoryId ? category.categoryId.toString() : '';
    const targetId = categoryId ? categoryId.toString() : '';
    
    return catId !== targetId && catCategoryId !== targetId;
  });
  
  emit('update:modelValue', {
    ...props.modelValue,
    selectedCategories: props.modelValue.selectedCategories
  });
};

const formatPrice = (price) => {
  if (price === undefined || price === null) return '';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(price);
};

watch(() => props.modelValue.selectedCategories, (newCategories) => {
  if (Array.isArray(newCategories)) {
    selectedCategoryIds.value = newCategories.map(cat => cat.id || cat.categoryId).filter(Boolean);
  } else {
    selectedCategoryIds.value = [];
  }
}, { immediate: true, deep: true });

watch(selectedCategoryIds, (newIds) => {
  if (!Array.isArray(newIds)) return;
  
  const categories = newIds.map(id => {
    const realCategoryName = findCategoryNameById(id);
    
    return {
      id: id,
      categoryId: id,
      name: realCategoryName || '',
      categoryName: realCategoryName || ''
    };
  });
  
  if (JSON.stringify(categories) !== JSON.stringify(props.modelValue.selectedCategories || [])) {
    props.modelValue.selectedCategories = categories;
    emitUpdate();
  }
}, { deep: true });

onMounted(() => {
  if (!props.modelValue.triggerType) {
    props.modelValue.triggerType = 'ALL_STORE';
  }
  
  if (!Array.isArray(props.modelValue.selectedCategories)) {
    props.modelValue.selectedCategories = [];
  }
  
  if (!Array.isArray(props.modelValue.selectedTriggerProducts)) {
    props.modelValue.selectedTriggerProducts = [];
  }
  
  if (!Array.isArray(props.modelValue.selectedSuggestedProducts)) {
    props.modelValue.selectedSuggestedProducts = [];
  }
  
  if (!props.modelValue.targetConfig) {
    props.modelValue.targetConfig = 'LOWEST_VALUE';
  }
  
  if (Array.isArray(props.modelValue.selectedCategories)) {
    selectedCategoryIds.value = props.modelValue.selectedCategories
      .map(cat => cat.id || cat.categoryId)
      .filter(Boolean);
  }
});
</script>
