<template>
    <div class="blog-post-editor-container">
        <!-- Header with actions -->
        <div class="editor-header">
            <div class="header-content">
                <h1 class="page-title">
                    {{ isEditing ? $t('blogPost.edit') : $t('blogPost.new') }}
                </h1>
                <p class="page-subtitle">
                    {{ isEditing ? $t('blogPost.form.basicDataDescription') : $t('blogPost.subtitle') }}
                </p>
            </div>
            <div class="header-actions">
                <IluriaButton 
                    color="secondary"
                    variant="outline" 
                    @click="router.push('/blog/posts')"
                    :loading="loading"
                >
                    ← {{ $t('blogPost.form.backToPosts') }}
                </IluriaButton>
                <IluriaButton 
                    color="primary"
                    :hugeIcon="FloppyDiskIcon" 
                    @click="savePost"
                    :loading="loading"
                    :disabled="!form.title?.trim()"
                >
                    {{ loading ? $t('blogPost.form.saving') : $t('blogPost.form.save') }}
                </IluriaButton>
            </div>
        </div>

        <!-- Form -->
        <div class="editor-content">
            <form @submit.prevent="savePost" class="space-y-8">
                <!-- Basic Information -->
                <ViewContainer 
                    :title="$t('blogPost.form.basicData')"
                    :subtitle="$t('blogPost.form.basicDataDescription')"
                    :icon="DocumentValidationIcon"
                    iconColor="blue"
                >
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Title -->
                        <div class="lg:col-span-2">
                            <IluriaInputText
                                v-model="form.title"
                                :label="$t('blogPost.form.title')"
                                :placeholder="$t('blogPost.form.titlePlaceholder')"
                                :required="true"
                                :maxlength="255"
                            />
                        </div>

                        <!-- Slug -->
                        <div class="lg:col-span-2">
                            <IluriaInputText
                                v-model="form.slug"
                                :label="$t('blogPost.form.slug')"
                                :placeholder="$t('blogPost.form.slugPlaceholder')"
                                :maxlength="255"
                            />
                            <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                {{ $t('blogPost.form.slugHelp') }}
                            </p>
                        </div>

                        <!-- Category -->
                        <div class="lg:col-span-2">
                            <IluriaSelect
                                v-model="form.categoryId"
                                :label="$t('blogPost.form.category')"
                                :options="categoryOptions"
                                :placeholder="$t('blogPost.form.categoryPlaceholder')"
                                class="w-full"
                            />
                        </div>
                    </div>

                    <!-- Content -->
                    

                    <!-- Excerpt -->
                    <div class="mt-6">
                        <IluriaTextarea
                            v-model="form.excerpt"
                            :label="$t('blogPost.form.excerpt')"
                            :placeholder="$t('blogPost.form.excerptPlaceholder')"
                            :rows="3"
                            :maxlength="500"
                            class="w-full"
                        />
                        <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                            {{ $t('blogPost.form.excerptHelp') }}
                        </p>
                    </div>

                    <div class="mt-6">
                        <IluriaLabel>
                            {{ $t('blogPost.form.content') }}:
                        </IluriaLabel>
                        <ContentPreview
                            v-model="editorData"
                            :placeholder="$t('blogPost.form.contentPlaceholder')"
                            :height="'400px'"
                        />
                    </div>
                </ViewContainer>

                <!-- Featured Image -->
                <ViewContainer 
                    :title="$t('blogPost.form.featuredImage')"
                    :subtitle="$t('blogPost.form.featuredImageHelp')"
                    :icon="ImageUploadIcon"
                    iconColor="purple"
                >
                    <div class="space-y-4">
                        <IluriaSimpleImageUpload
                            id="featuredImage"
                            v-model="form.featuredImageUrl"
                            :label="$t('blogPost.form.featuredImage')"
                            :add-button-text="$t('blogPost.form.selectImage')"
                            :change-button-text="$t('blogPost.form.changeImage')"
                            :remove-button-text="$t('blogPost.form.removeImage')"
                            :format-hint="$t('blogPost.form.imageFormat')"
                            accept="image/*"
                            :prevent-cache="true"
                            @change="onImageChange"
                            class="w-full"
                        />
                    </div>
                </ViewContainer>

                <!-- Settings -->
                <ViewContainer 
                    :title="$t('blogPost.form.settingsTitle')"
                    :subtitle="$t('blogPost.form.settingsSubtitle')"
                    :icon="Settings02Icon"
                    iconColor="gray"
                >
                    <div class="grid grid-cols-1 lg:grid-cols-2 gap-6">
                        <!-- Published -->
                        <div>
                            <IluriaCheckbox
                                v-model="form.published"
                                id="published"
                                :label="$t('blogPost.form.published')"
                                :description="$t('blogPost.form.publishedHelp')"
                            />
                        </div>

                        <!-- Featured -->
                        <div>
                            <IluriaCheckbox
                                v-model="form.featured"
                                id="featured"
                                :label="$t('blogPost.form.featured')"
                                :description="$t('blogPost.form.featuredHelp')"
                            />
                        </div>
                    </div>
                </ViewContainer>

                <!-- SEO -->
                <ViewContainer 
                    :title="$t('blogPost.form.seoConfig')"
                    :subtitle="$t('blogPost.form.seoConfigDescription')"
                    :icon="DocumentValidationIcon"
                    iconColor="blue"
                >
                    <div class="space-y-6">
                        <!-- Meta Title -->
                        <div class="space-y-2">
                            <IluriaInputText
                                v-model="form.metaTitle"
                                :label="$t('blogPost.form.metaTitle')"
                                :placeholder="$t('blogPost.form.metaTitlePlaceholder')"
                                :maxlength="60"
                                class="w-full"
                            />
                            <div class="flex justify-between items-center">
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    {{ $t('blogPost.form.metaTitleHelp') }}
                                </p>
                                <span class="character-counter" :class="{ 'text-red-500': metaTitleLength > 60 }">
                                    {{ metaTitleLength }}/60 {{ $t('blogPost.form.characters') }}
                                </span>
                            </div>
                        </div>

                        <!-- Meta Description -->
                        <div class="space-y-2">
                            <IluriaTextarea
                                v-model="form.metaDescription"
                                :label="$t('blogPost.form.metaDescription')"
                                :placeholder="$t('blogPost.form.metaDescriptionPlaceholder')"
                                :rows="3"
                                :maxlength="160"
                                class="w-full"
                            />
                            <div class="flex justify-between items-center">
                                <p class="mt-1 text-sm text-gray-500 dark:text-gray-400">
                                    {{ $t('blogPost.form.metaDescriptionHelp') }}
                                </p>
                                <span class="character-counter" :class="{ 'text-red-500': metaDescriptionLength > 160 }">
                                    {{ metaDescriptionLength }}/160 {{ $t('blogPost.form.characters') }}
                                </span>
                            </div>
                        </div>
                    </div>
                </ViewContainer>

                <!-- Google Preview -->
                <ViewContainer 
                    :title="$t('blogPost.form.googlePreviewTitle')"
                    :subtitle="$t('blogPost.form.googlePreviewSubtitle')"
                    :icon="SearchIcon"
                    iconColor="green"
                >
                    <div class="google-preview-container">
                        <div class="google-result">
                            <div class="google-title">
                                {{ form.metaTitle || form.title || $t('blogPost.form.googlePreviewDefaultTitle') }}
                            </div>
                            <div class="google-url">
                                {{ form.slug ? `${$t('blogPost.form.googlePreviewUrl').split('/')[0]}/blog/${form.slug}` : $t('blogPost.form.googlePreviewUrl') }}
                            </div>
                            <div class="google-description">
                                {{ form.metaDescription || form.excerpt || $t('blogPost.form.googlePreviewDefaultDescription') }}
                            </div>
                        </div>
                    </div>
                </ViewContainer>
            </form>
        </div>
    </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { 
    FloppyDiskIcon,
    DocumentValidationIcon,
    Settings02Icon,
    ImageUploadIcon,
    SearchIcon
} from '@hugeicons-pro/core-stroke-rounded'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaTextarea from '@/components/iluria/form/IluriaTextarea.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaCheckbox from '@/components/iluria/form/IluriaCheckbox.vue'
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import ContentPreview from '@/components/editor/ContentEditorPreview.vue'
import BlogPostService from '@/services/blogPost.service'
import blogCategoryService from '@/services/blogCategory.service'
import { useToast } from 'primevue/usetoast'
import { useI18n } from 'vue-i18n'
import { useTheme } from '@/composables/useTheme'

const route = useRoute()
const router = useRouter()
const toast = useToast()
const { t } = useI18n()

// Initialize theme system
const { initTheme } = useTheme()
initTheme()

const loading = ref(false)
const categories = ref([])
const editorData = ref({
    time: Date.now(),
    blocks: [],
    version: "2.28.2"
})

const form = reactive({
    title: '',
    slug: '',
    // content removed - will be managed by ContentEditor
    excerpt: '',
    featuredImageUrl: '',
    metaTitle: '',
    metaDescription: '',
    published: false,
    featured: false,
    categoryId: '',
    newImageFile: null
})

const isEditing = computed(() => !!route.params.id)

// Category options for select
const categoryOptions = computed(() => 
    categories.value.map(category => ({
        label: category.name,
        value: category.id
    }))
)

const metaTitleLength = computed(() => form.metaTitle?.length || 0)
const metaDescriptionLength = computed(() => form.metaDescription?.length || 0)



// UTF-8 safe base64 encoding/decoding functions
const encodeToBase64 = (str) => {
    try {
        // Convert string to UTF-8 bytes, then to base64
        return btoa(encodeURIComponent(str).replace(/%([0-9A-F]{2})/g, (match, p1) => {
            return String.fromCharCode(parseInt(p1, 16))
        }))
    } catch (e) {
        console.error('Error encoding to base64:', e)
        return btoa('{}') // fallback to empty JSON
    }
}

const decodeFromBase64 = (str) => {
    try {
        // Decode from base64, then from UTF-8 bytes
        return decodeURIComponent(Array.prototype.map.call(atob(str), (c) => {
            return '%' + ('00' + c.charCodeAt(0).toString(16)).slice(-2)
        }).join(''))
    } catch (e) {
        console.error('Error decoding from base64:', e)
        return '{}' // fallback to empty JSON
    }
}

// Watch for title changes to auto-generate slug
watch(() => form.title, () => {
    // Auto-generate slug if it's empty or hasn't been manually edited
    if (!form.slug || !isEditing.value) {
        form.slug = BlogPostService.generateSlugFromTitle(form.title)
    }
})

const onImageChange = (file) => {
    if (!file || file === null) {
        // If file is null/undefined, user removed the image
        form.newImageFile = null
        form.featuredImageUrl = '' // Clear the URL as well
        return;
    }
    
    if (file instanceof File) {
        // User selected a new file - store it for upload
        form.newImageFile = file
    } else {
        // This is likely a string URL (existing image) - no new file to upload
        form.newImageFile = null
        form.featuredImageUrl = file // Set the URL string
    }
}

const loadCategories = async () => {
    try {
        const response = await blogCategoryService.listBlogCategories('', 0, 1000)
        categories.value = response.content || []
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: t('blogPost.messages.errorTitle'),
            detail: t('blogPost.messages.loadCategoriesError'),
            life: 3000
        })
    }
}

const loadPost = async (id) => {
    try {
        const post = await BlogPostService.getBlogPost(id)
        
        // Clear form first
        Object.keys(form).forEach(key => {
            if (key === 'published' || key === 'featured') {
                form[key] = false
            } else {
                form[key] = ''
            }
        })
        form.newImageFile = null
        
        // Map post data to form, being careful with field names
        form.title = post.title || ''
        form.slug = post.slug || ''
        // Content will be set in the editor
        form.excerpt = post.excerpt || ''
        form.featuredImageUrl = post.featuredImageUrl || ''
        form.metaTitle = post.metaTitle || ''
        form.metaDescription = post.metaDescription || ''
        form.published = Boolean(post.published)
        form.featured = Boolean(post.featured)
        form.categoryId = post.blogCategoryId || '' // Map blogCategoryId to categoryId for the form
        
        // Load content into editor
        if (post.content) {
            let dataToLoad = null

            // Check if content has embedded Editor.js JSON data
            const editorJsonMatch = post.content.match(/<!-- EDITOR_JS_DATA:([^-]+) -->/)

            if (editorJsonMatch) {
                // Extract and decode the JSON data
                try {
                    const encodedJson = editorJsonMatch[1]
                    const decodedJson = decodeFromBase64(encodedJson)
                    const parsedContent = JSON.parse(decodedJson)

                    if (parsedContent.blocks) {
                        dataToLoad = parsedContent
                    } else {
                        dataToLoad = convertHtmlToEditorData(post.content)
                    }
                } catch (e) {
                    console.warn('Failed to parse embedded Editor.js data, falling back to HTML parsing')
                    dataToLoad = convertHtmlToEditorData(post.content)
                }
            } else {
                // Try to parse as direct JSON first, then fallback to HTML conversion
                try {
                    const parsedContent = JSON.parse(post.content)
                    if (parsedContent.blocks) {
                        dataToLoad = parsedContent
                    } else {
                        dataToLoad = convertHtmlToEditorData(post.content)
                    }
                } catch (e) {
                    // Content is HTML, convert to Editor.js format
                    dataToLoad = convertHtmlToEditorData(post.content)
                }
            }

            // Set the data
            if (dataToLoad) {
                editorData.value = dataToLoad
            }
        }
        
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: t('blogPost.messages.errorTitle'),
            detail: t('blogPost.messages.loadPostError'),
            life: 3000
        })
        router.push('/blog/posts')
    }
}

// Convert HTML back to Editor.js format (intelligent parsing)
const convertHtmlToEditorData = (html) => {
    if (!html || html.trim() === '') {
        return {
            time: Date.now(),
            blocks: [],
            version: "2.28.2"
        }
    }

    const blocks = []
    let blockId = 0

    // Create a temporary DOM element to parse HTML
    const tempDiv = document.createElement('div')
    tempDiv.innerHTML = html

    // Function to generate unique block ID
    const getBlockId = () => `block-${blockId++}`

    // Function to parse child nodes recursively
    const parseNodes = (nodes) => {
        Array.from(nodes).forEach(node => {
            if (node.nodeType === Node.TEXT_NODE) {
                const text = node.textContent.trim()
                if (text) {
                    blocks.push({
                        id: getBlockId(),
                        type: "paragraph",
                        data: { text }
                    })
                }
                return
            }

            if (node.nodeType !== Node.ELEMENT_NODE) return

            const tagName = node.tagName.toLowerCase()

            switch (tagName) {
                case 'p':
                    const pText = node.textContent.trim()
                    if (pText) {
                        blocks.push({
                            id: getBlockId(),
                            type: "paragraph",
                            data: { text: pText }
                        })
                    }
                    break

                case 'h1':
                case 'h2':
                case 'h3':
                case 'h4':
                case 'h5':
                case 'h6':
                    const level = parseInt(tagName.charAt(1))
                    const hText = node.textContent.trim()
                    if (hText) {
                        blocks.push({
                            id: getBlockId(),
                            type: "header",
                            data: {
                                text: hText,
                                level: level
                            }
                        })
                    }
                    break

                case 'div':
                    // Check if this is a product grid
                    if (node.classList.contains('product-grid') || node.querySelector('.product-card')) {
                        const productGridData = parseProductGridFromHtml(node)
                        if (productGridData) {
                            blocks.push({
                                id: getBlockId(),
                                type: "productGrid",
                                data: productGridData
                            })
                        }
                    } else {
                        // Parse child nodes for regular divs
                        parseNodes(node.childNodes)
                    }
                    break

                default:
                    // For other elements, parse their children
                    parseNodes(node.childNodes)
                    break
            }
        })
    }

    parseNodes(tempDiv.childNodes)

    // If no blocks were created, create a single paragraph with the text content
    if (blocks.length === 0) {
        const textContent = tempDiv.textContent.trim()
        if (textContent) {
            blocks.push({
                id: getBlockId(),
                type: "paragraph",
                data: { text: textContent }
            })
        }
    }

    return {
        time: Date.now(),
        blocks: blocks,
        version: "2.28.2"
    }
}

// Parse ProductGrid from HTML
const parseProductGridFromHtml = (gridElement) => {
    const productCards = gridElement.querySelectorAll('.product-card')
    if (productCards.length === 0) return null

    const products = Array.from(productCards).map(card => {
        const title = card.querySelector('.product-title')?.textContent.trim() || ''
        const description = card.querySelector('.product-description')?.textContent.trim() || ''
        const button = card.querySelector('.product-button')
        const buttonText = button?.textContent.trim() || 'Ver Detalhes'
        const buttonUrl = button?.getAttribute('href') || '#'
        const img = card.querySelector('img')
        const imageUrl = img?.getAttribute('src') || ''

        // Parse rating from stars
        const ratingElement = card.querySelector('.product-rating')
        let rating = 5 // default
        if (ratingElement) {
            const starsText = ratingElement.textContent
            const filledStars = (starsText.match(/★/g) || []).length
            rating = filledStars
        }

        return {
            title,
            description,
            buttonText,
            buttonUrl,
            imageUrl,
            rating
        }
    })

    // Extract colors and styles from the first card (if available)
    const firstCard = productCards[0]
    const cardColor = firstCard?.style.backgroundColor || '#ffffff'
    const button = firstCard?.querySelector('.product-button')
    const buttonColor = button?.style.backgroundColor || '#1f2937'
    const title = firstCard?.querySelector('.product-title')
    const titleColor = title?.style.color || '#1f2937'
    const titleFontSize = parseFloat(title?.style.fontSize) || 1.125
    const description = firstCard?.querySelector('.product-description')
    const descriptionColor = description?.style.color || '#6b7280'
    const descriptionFontSize = parseFloat(description?.style.fontSize) || 0.875
    const rating = firstCard?.querySelector('.product-rating')
    const ratingColor = rating?.style.color || '#f59e0b'

    return {
        cardColor,
        buttonColor,
        titleColor,
        descriptionColor,
        ratingColor,
        titleFontSize,
        descriptionFontSize,
        products,
        html: gridElement.outerHTML // Preserve original HTML
    }
}

// Convert Editor.js data to HTML
const convertEditorDataToHtml = (data) => {
    if (!data?.blocks) return ''

    return data.blocks.map(block => {
        switch (block.type) {
            case 'paragraph':
                return `<p>${block.data?.text || ''}</p>`
            case 'header':
                const level = block.data?.level || 1
                return `<h${level}>${block.data?.text || ''}</h${level}>`
            case 'list':
                const tag = block.data?.style === 'ordered' ? 'ol' : 'ul'
                const items = block.data?.items?.map(item => `<li>${item}</li>`).join('') || ''
                return `<${tag}>${items}</${tag}>`
            case 'quote':
                return `<blockquote><p>${block.data?.text || ''}</p><cite>${block.data?.caption || ''}</cite></blockquote>`
            case 'code':
                return `<pre><code>${block.data?.code || ''}</code></pre>`
            case 'delimiter':
                return '<hr>'
            case 'image':
                const caption = block.data?.caption ? `<figcaption>${block.data.caption}</figcaption>` : ''
                return `<figure><img src="${block.data?.file?.url || ''}" alt="${block.data?.caption || ''}"/>${caption}</figure>`
            case 'checklist':
                const checkItems = block.data?.items?.map(item =>
                    `<li><input type="checkbox" ${item.checked ? 'checked' : ''} disabled> ${item.text}</li>`
                ).join('') || ''
                return `<ul class="checklist">${checkItems}</ul>`
            case 'table':
                const rows = block.data?.content?.map(row =>
                    `<tr>${row.map(cell => `<td>${cell}</td>`).join('')}</tr>`
                ).join('') || ''
                return `<table><tbody>${rows}</tbody></table>`
            case 'warning':
                return `<div class="warning"><h4>${block.data?.title || ''}</h4><p>${block.data?.message || ''}</p></div>`
            case 'embed':
                return `<div class="embed">${block.data?.embed || ''}</div>`
            case 'attaches':
                return `<div class="attachment"><a href="${block.data?.file?.url || ''}" download>${block.data?.title || 'Download'}</a></div>`
            case 'raw':
                return block.data?.html || ''
            case 'productGrid':
                return generateProductGridHtml(block.data)
            default:
                return ''
        }
    }).join('')
}

// Generate ProductGrid HTML
const generateProductGridHtml = (data) => {
    if (!data?.products || data.products.length === 0) return ''

    const productsHtml = data.products.map(product => {
        const filledStars = '★'.repeat(product.rating || 5)
        const emptyStars = '☆'.repeat(5 - (product.rating || 5))
        const starsHTML = filledStars + emptyStars

        return `
            <div class="product-card" style="background-color: ${data.cardColor || '#ffffff'};">
                <img src="${product.imageUrl || ''}" alt="${product.title || ''}" style="width: 100%; height: 200px; object-fit: cover; border-radius: 8px;">
                <h3 class="product-title" style="color: ${data.titleColor || '#1f2937'}; font-size: ${data.titleFontSize || 1.125}rem; margin: 16px 0 8px 0;">${product.title || ''}</h3>
                <p class="product-description" style="color: ${data.descriptionColor || '#6b7280'}; font-size: ${data.descriptionFontSize || 0.875}rem; margin: 0 0 12px 0;">${product.description || ''}</p>
                <div class="product-rating" style="color: ${data.ratingColor || '#f59e0b'}; margin: 0 0 16px 0;">${starsHTML}</div>
                <a href="${product.buttonUrl || '#'}" class="product-button" style="background-color: ${data.buttonColor || '#1f2937'}; color: white; padding: 12px 24px; border-radius: 8px; text-decoration: none; display: inline-block; font-weight: 500;">${product.buttonText || 'Ver Detalhes'}</a>
            </div>
        `
    }).join('')

    return `<div class="product-grid" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); gap: 24px; margin: 24px 0;">${productsHtml}</div>`
}

const savePost = async () => {
    // Validação básica
    if (!form.title?.trim()) {
        toast.add({
            severity: 'error',
            summary: t('blogPost.messages.errorTitle'),
            detail: t('blogPost.messages.titleRequired'),
            life: 3000
        })
        return
    }
    
    // Ensure content is up to date from editor
    if (editorData.value && editorData.value.blocks && editorData.value.blocks.length > 0) {
        // Check if there's actual content (not just empty blocks)
        const hasContent = editorData.value.blocks.some(block => {
            if (block.type === 'paragraph' && block.data?.text?.trim()) return true
            if (block.type === 'header' && block.data?.text?.trim()) return true
            if (block.type === 'list' && block.data?.items?.length > 0) return true
            if (block.type === 'quote' && block.data?.text?.trim()) return true
            if (block.type === 'code' && block.data?.code?.trim()) return true
            if (block.type === 'image' && block.data?.file?.url) return true
            if (block.type === 'productGrid' && block.data?.products?.length > 0) return true
            if (block.type === 'checklist' && block.data?.items?.length > 0) return true
            if (block.type === 'table' && block.data?.content?.length > 0) return true
            if (block.type === 'warning' && block.data?.message?.trim()) return true
            if (block.type === 'delimiter') return true
            if (block.type === 'embed' && block.data?.source) return true
            if (block.type === 'attaches' && block.data?.file?.url) return true
            if (block.type === 'raw' && block.data?.html?.trim()) return true
            return false
        })

        if (!hasContent) {
            toast.add({
                severity: 'error',
                summary: t('blogPost.messages.errorTitle'),
                detail: t('blogPost.messages.contentRequired'),
                life: 3000
            })
            return
        }

        // Convert Editor.js data to HTML for storage
        const editorJson = JSON.stringify(editorData.value)
        const htmlContent = convertEditorDataToHtml(editorData.value)

        // Save JSON format with HTML fallback comment (using UTF-8 safe encoding)
        form.content = `<!-- EDITOR_JS_DATA:${encodeToBase64(editorJson)} -->\n${htmlContent}`
    } else {
        toast.add({
            severity: 'error',
            summary: t('blogPost.messages.errorTitle'),
            detail: t('blogPost.messages.contentRequired'),
            life: 3000
        })
        return
    }

    loading.value = true
    try {
        // Prepare data - usa excerpt como metaDescription se estiver vazio
        const postData = {
            title: form.title,
            slug: form.slug,
            content: form.content, // Use content from editor
            excerpt: form.excerpt,
            metaTitle: form.metaTitle,
            metaDescription: form.metaDescription || form.excerpt || '', // Usa excerpt se metaDescription estiver vazio
            published: form.published,
            featured: form.featured,
            blogCategoryId: form.categoryId || null,
            // Remove featuredImageUrl from post data - será definida pelo upload da imagem
            featuredImageUrl: form.newImageFile ? null : form.featuredImageUrl
        }
        
        let savedPost;
        if (isEditing.value) {
            savedPost = await BlogPostService.updateBlogPost(route.params.id, postData)
            toast.add({
                severity: 'success',
                summary: t('blogPost.messages.successTitle'),
                detail: t('blogPost.messages.updateSuccess'),
                life: 3000
            })
        } else {
            savedPost = await BlogPostService.createBlogPost(postData)
            toast.add({
                severity: 'success',
                summary: t('blogPost.messages.successTitle'),
                detail: t('blogPost.messages.createSuccess'),
                life: 3000
            })
        }
        
        // Upload da imagem se há um novo arquivo (independente do salvamento do post)
        
        if (form.newImageFile && form.newImageFile instanceof File && savedPost?.id) {
            try {
                const uploadResponse = await BlogPostService.uploadBlogPostImage(savedPost.id, form.newImageFile)
                
                // Atualiza o form com a URL da imagem retornada
                if (uploadResponse && uploadResponse.featuredImageUrl) {
                    form.featuredImageUrl = uploadResponse.featuredImageUrl
                }
                
                toast.add({
                    severity: 'success',
                    summary: t('blogPost.messages.successTitle'),
                    detail: t('blogPost.messages.imageUploadSuccess'),
                    life: 3000
                })
            } catch (imageError) {
                toast.add({
                    severity: 'warn',
                    summary: t('blogPost.messages.warningTitle'),
                    detail: t('blogPost.messages.imageUploadError'),
                    life: 3000
                })
            }
        }
        
        // Sempre redireciona após salvar, independente do upload da imagem
        router.push('/blog/posts')
    } catch (error) {
        toast.add({
            severity: 'error',
            summary: t('blogPost.messages.errorTitle'),
            detail: t('blogPost.messages.saveError'),
            life: 3000
        })
    } finally {
        loading.value = false
    }
}

onMounted(async () => {
    await loadCategories()
    
    if (isEditing.value) {
        // Wait a bit for editor to initialize
        setTimeout(async () => {
            await loadPost(route.params.id)
        }, 100)
    }
})
</script>

<style scoped>
.blog-post-editor-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: var(--iluria-color-background);
    min-height: 100vh;
}

/* Header */
.editor-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 24px;
    padding-bottom: 20px;
    border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
    font-size: 28px;
    font-weight: 700;
    color: var(--iluria-color-text);
    margin: 0;
    line-height: 1.2;
}

.header-content .page-subtitle {
    font-size: 16px;
    color: var(--iluria-color-text-muted);
    margin: 4px 0 0 0;
}

.header-actions {
    display: flex;
    gap: 12px;
    align-items: center;
}

/* Main Content */
.editor-content {
    display: flex;
    flex-direction: column;
    gap: 20px;
    background: var(--iluria-color-background);
}

.character-counter {
    font-size: 12px;
    color: var(--iluria-color-text-secondary);
}

/* Google Preview */
.google-preview-container {
    padding: 20px;
    background: #ffffff;
    border: 1px solid #e5e7eb;
    border-radius: 12px;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.google-result {
    font-family: Arial, sans-serif;
    max-width: 600px;
}

.google-title {
    font-size: 20px;
    line-height: 1.3;
    color: #1a0dab;
    text-decoration: none;
    margin-bottom: 6px;
    cursor: pointer;
    font-weight: 400;
}

.google-title:hover {
    text-decoration: underline;
}

.google-url {
    font-size: 14px;
    color: #006621;
    margin-bottom: 6px;
    word-break: break-all;
    line-height: 1.3;
}

.google-description {
    font-size: 14px;
    color: #4d5156;
    line-height: 1.4;
    max-height: none;
    overflow: visible;
}

/* Responsive */
@media (max-width: 768px) {
    .blog-post-editor-container {
        padding: 16px;
    }
    
    .editor-header {
        flex-direction: row;
        align-items: flex-start;
        justify-content: space-between;
        gap: 12px;
    }
    
    .header-content {
        flex: 1;
        min-width: 0;
    }
    
    .header-content h1.page-title {
        font-size: 24px;
    }
    
    .header-content .page-subtitle {
        font-size: 15px;
    }
    
    .header-actions {
        flex-direction: row;
        gap: 8px;
        flex-shrink: 0;
        align-items: flex-start;
    }
}

/* Content Editor Wrapper */
.content-editor-wrapper {
    border: 1px solid var(--iluria-color-border);
    border-radius: 8px;
    background: var(--iluria-color-surface);
    overflow: hidden;
}

.content-editor-wrapper :deep(.editor-instance) {
    padding: 1rem;
}
</style> 