import { defineStore } from 'pinia';
import { ref } from 'vue';
import storeService from '@/services/store.service';
import storeBrandAssetsService from '@/services/storeBrandAssets.service';

export const useStoreStore = defineStore('store', () => {
    const selectedStore = ref(null);
    const userStores = ref([]);
    const loading = ref(false);
    const brandAssets = ref({
        logoUrl: null,
        faviconUrl: null
    });

    // Actions
    const setSelectedStore = (store) => {
        selectedStore.value = store;
        // Store in localStorage for persistence
        if (store) {
            localStorage.setItem('selectedStore', JSON.stringify(store));
        } else {
            localStorage.removeItem('selectedStore');
        }
    };

    const loadSelectedStoreFromStorage = () => {
        const stored = localStorage.getItem('selectedStore');
        if (stored) {
            try {
                selectedStore.value = JSON.parse(stored);
            } catch (error) {
                console.error('Error parsing stored store:', error);
                localStorage.removeItem('selectedStore');
            }
        }
    };

    const loadUserStores = async () => {
        loading.value = true;
        try {
            const stores = await storeService.getUserStores();
            userStores.value = stores;
            return stores;
        } catch (error) {
            console.error('Error loading user stores:', error);
            throw error;
        } finally {
            loading.value = false;
        }
    };

    const createStore = async (storeData) => {
        try {
            const response = await storeService.createStore(storeData);
            
            // Extract store token and user info from the response
            const storeToken = response.jwt || response.token;
            const user = response.user || response;
            
            // Set store token for immediate use
            const { useAuthStore } = await import('@/stores/auth.store');
            const authStore = useAuthStore();
            authStore.setStoreToken(storeToken);
            
            // Reload user stores to get the updated list with the new store
            await loadUserStores();
            
            return response;
        } catch (error) {
            console.error('Error creating store:', error);
            throw error;
        }
    };

    const clearStore = () => {
        selectedStore.value = null;
        userStores.value = [];
        brandAssets.value = {
            logoUrl: null,
            faviconUrl: null
        };
        localStorage.removeItem('selectedStore');
    };

    // Brand Assets Actions
    const loadBrandAssets = async () => {
        try {
            const response = await storeBrandAssetsService.getStoreBrandAssets();
            if (response) {
                brandAssets.value = {
                    logoUrl: response.logoUrl || null,
                    faviconUrl: response.faviconUrl || null
                };
            }
        } catch (error) {
            console.error('Erro ao carregar brand assets:', error);
            brandAssets.value = {
                logoUrl: null,
                faviconUrl: null
            };
        }
    };

    const updateBrandAssets = (newBrandAssets) => {
        brandAssets.value = {
            ...brandAssets.value,
            ...newBrandAssets
        };
    };

    // Getters
    const hasSelectedStore = () => selectedStore.value !== null;
    const getSelectedStoreId = () => selectedStore.value?.id;
    const getSelectedStoreName = () => selectedStore.value?.name;
    const getStoreLogo = () => brandAssets.value?.logoUrl;

    return {
        // State
        selectedStore,
        userStores,
        loading,
        brandAssets,

        // Actions
        setSelectedStore,
        loadSelectedStoreFromStorage,
        loadUserStores,
        createStore,
        clearStore,
        loadBrandAssets,
        updateBrandAssets,

        // Getters
        hasSelectedStore,
        getSelectedStoreId,
        getSelectedStoreName,
        getStoreLogo
    };
}, {
    persist: {
        key: 'store-store',
        storage: localStorage,
        paths: ['selectedStore', 'brandAssets']
    }
});
