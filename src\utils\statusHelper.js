export function useStatus() {
  const getStatusClass = (status) => {
    const statusClasses = {
      'PENDING': 'bg-yellow-100 text-yellow-800',
      'PROCESSING': 'bg-blue-100 text-blue-800',
      'SHIPPED': 'bg-purple-100 text-purple-800',
      'DELIVERED': 'bg-green-100 text-green-800',
      'CANCELLED': 'bg-red-100 text-red-800',
      'PAID': 'bg-green-100 text-green-800',
      'UNPAID': 'bg-red-100 text-red-800',
      'REFUNDED': 'bg-orange-100 text-orange-800',
      'FAILED': 'bg-red-100 text-red-800'
    }
    return statusClasses[status] || 'bg-gray-100 text-gray-800'
  }

  const getStatusLabel = (status) => {
    const statusLabels = {
      'PENDING': 'Pendente',
      'PROCESSING': 'Em processamento',
      'SHIPPED': 'Enviado',
      'DELIVERED': 'Entregue',
      'CANCELLED': 'Cancelado',
      'PAID': 'Pago',
      'UNPAID': 'Não pago',
      'REFUNDED': 'Reembolsado',
      'FAILED': 'Falhou'
    }
    return statusLabels[status] || status
  }

  return { getStatusClass, getStatusLabel }
}

export const SHIPPING_STATUS = {
  PENDING: 'PENDING',
  PROCESSING: 'PROCESSING',
  SHIPPED: 'SHIPPED',
  DELIVERED: 'DELIVERED',
  CANCELLED: 'CANCELLED'
}

export const PAYMENT_STATUS = {
  PENDING: 'PENDING',
  PAID: 'PAID',
  REFUNDED: 'REFUNDED',
  FAILED: 'FAILED'
}