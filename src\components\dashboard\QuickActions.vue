<template>
  <div class="quick-actions">
    <div class="actions-grid">
      <div 
        v-for="action in actions" 
        :key="action.id"
        class="action-card"
        :class="{ disabled: action.disabled }"
        @click="handleAction(action)"
      >
        <div class="action-icon" :class="`icon-${action.color}`">
          <HugeiconsIcon :icon="action.icon" :size="20" class="icon" />
        </div>
        <div class="action-content">
          <h3 class="action-title">{{ action.title }}</h3>
          <p class="action-description">{{ action.description }}</p>
        </div>
        <div class="action-arrow">
          <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" class="arrow-icon" />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  PlusSignIcon,
  ShoppingCart01Icon,
  DiscountIcon,
  Settings02Icon,
  Analytics02Icon,
  UserMultiple02Icon,
  ArrowRight01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const router = useRouter()
const { t } = useI18n()

const actions = computed(() => [
  {
    id: 'add-product',
    title: t('dashboard.actions.addProduct'),
    description: t('dashboard.actions.addProductDesc'),
    icon: PlusSignIcon,
    color: 'green',
    route: '/products/new',
    disabled: false
  },
  {
    id: 'view-orders',
    title: t('dashboard.actions.viewOrders'),
    description: t('dashboard.actions.viewOrdersDesc'),
    icon: ShoppingCart01Icon,
    color: 'blue',
    route: '/orders',
    disabled: false
  },
  {
    id: 'create-coupon',
    title: t('dashboard.actions.createCoupon'),
    description: t('dashboard.actions.createCouponDesc'),
    icon: DiscountIcon,
    color: 'purple',
    route: '/customer/coupon-manager',
    disabled: false
  },
  {
    id: 'manage-customers',
    title: t('dashboard.actions.manageCustomers'),
    description: t('dashboard.actions.manageCustomersDesc'),
    icon: UserMultiple02Icon,
    color: 'orange',
    route: '/customer-list',
    disabled: false
  },
  {
    id: 'store-settings',
    title: t('dashboard.actions.storeSettings'),
    description: t('dashboard.actions.storeSettingsDesc'),
    icon: Settings02Icon,
    color: 'gray',
    route: '/settings',
    disabled: false
  },
  {
    id: 'view-analytics',
    title: t('dashboard.actions.viewAnalytics'),
    description: t('dashboard.actions.viewAnalyticsDesc'),
    icon: Analytics02Icon,
    color: 'indigo',
    route: '/analytics',
    disabled: true
  }
])

const handleAction = (action) => {
  if (action.disabled) return
  
  if (action.route) {
    router.push(action.route)
  }
  
  // Emit event for parent component to handle if needed
  emit('action-click', action)
}

const emit = defineEmits(['action-click'])
</script>

<style scoped>
.quick-actions {
  height: 100%;
}

.actions-grid {
  display: flex;
  flex-direction: column;
  gap: 12px;
  height: 100%;
}

.action-card {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
  overflow: hidden;
}

.action-card:hover:not(.disabled) {
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-md);
  transform: translateY(-1px);
}

.action-card.disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.action-card.disabled .action-icon {
  opacity: 0.5;
}

.action-icon {
  width: 40px;
  height: 40px;
  border-radius: 10px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.icon {
  width: 20px;
  height: 20px;
}

.icon-green {
  background: rgba(16, 185, 129, 0.1);
  color: var(--iluria-color-success);
}

.icon-blue {
  background: rgba(59, 130, 246, 0.1);
  color: var(--iluria-color-info);
}

.icon-purple {
  background: rgba(147, 51, 234, 0.1);
  color: #9333ea;
}

.icon-orange {
  background: rgba(249, 115, 22, 0.1);
  color: #f97316;
}

.icon-gray {
  background: rgba(107, 114, 128, 0.1);
  color: var(--iluria-color-text-secondary);
}

.icon-indigo {
  background: rgba(99, 102, 241, 0.1);
  color: #6366f1;
}

.action-content {
  flex: 1;
  min-width: 0;
}

.action-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.2;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.3;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.action-arrow {
  width: 20px;
  height: 20px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-left: 12px;
  flex-shrink: 0;
  opacity: 0.4;
  transition: all 0.2s ease;
}

.action-card:hover:not(.disabled) .action-arrow {
  opacity: 1;
  transform: translateX(4px);
}

.arrow-icon {
  width: 14px;
  height: 14px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 768px) {
  .action-card {
    padding: 12px;
  }
  
  .action-icon {
    width: 36px;
    height: 36px;
    margin-right: 12px;
  }
  
  .icon {
    width: 18px;
    height: 18px;
  }
  
  .action-title {
    font-size: 0.8rem;
  }
  
  .action-description {
    font-size: 0.7rem;
  }
  
  .action-arrow {
    width: 18px;
    height: 18px;
    margin-left: 8px;
  }
  
  .arrow-icon {
    width: 12px;
    height: 12px;
  }
}
</style> 
