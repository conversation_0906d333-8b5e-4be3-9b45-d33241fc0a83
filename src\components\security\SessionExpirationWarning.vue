<template>
  <Toast position="top-center" group="security" class="security-toast">
    <template #container="{ message, closeCallback }">
      <div class="session-warning-container">
        <div class="warning-content">
          <div class="warning-icon">
            <i class="pi pi-exclamation-triangle"></i>
          </div>
          <div class="warning-text">
            <h4>{{ $t('security.sessionExpiring') }}</h4>
            <p>{{ message.detail }}</p>
          </div>
        </div>
        <div class="warning-actions">
          <Button
            :label="$t('security.extendSession')"
            size="small"
            @click="handleExtendSession"
            :loading="extending"
            class="extend-btn"
          />
          <Button
            :label="$t('security.dismissWarning')"
            size="small"
            text
            @click="closeCallback"
            class="dismiss-btn"
          />
        </div>
      </div>
    </template>
  </Toast>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from 'primevue/usetoast'
import Button from 'primevue/button'
import Toast from 'primevue/toast'
import { useSessionTimeout } from '@/composables/useSessionTimeout'

const { t } = useI18n()
const toast = useToast()
const { extendSession } = useSessionTimeout()
const extending = ref(false)

const handleSessionWarning = (event) => {
  const { tokenType, minutes } = event.detail
  
  const message = t('security.sessionWillExpire', {
    tokenType: t(`security.${tokenType}Token`),
    minutes
  })
  
  toast.add({
    severity: 'warn',
    summary: t('security.sessionExpiring'),
    detail: message,
    life: 60000, // Show for 1 minute
    group: 'security',
    closable: true
  })
}

const handleExtendSession = async () => {
  extending.value = true
  try {
    const success = await extendSession()
    if (success) {
      toast.add({
        severity: 'success',
        summary: t('security.sessionExtended'),
        detail: t('security.sessionExtendedMessage'),
        life: 3000,
        group: 'security'
      })
    } else {
      toast.add({
        severity: 'error',
        summary: t('security.sessionExtendFailed'),
        detail: t('security.sessionExtendFailedMessage'),
        life: 5000,
        group: 'security'
      })
    }
  } catch (error) {
    console.error('Failed to extend session:', error)
    toast.add({
      severity: 'error',
      summary: t('security.sessionExtendFailed'),
      detail: t('security.sessionExtendFailedMessage'),
      life: 5000,
      group: 'security'
    })
  } finally {
    extending.value = false
  }
}

onMounted(() => {
  if (typeof window !== 'undefined') {
    window.addEventListener('session-warning', handleSessionWarning)
  }
})

onUnmounted(() => {
  if (typeof window !== 'undefined') {
    window.removeEventListener('session-warning', handleSessionWarning)
  }
})
</script>

<style scoped>
.security-toast {
  z-index: 9999;
}

.session-warning-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border: 2px solid var(--p-orange-500);
  border-radius: var(--iluria-border-radius-lg);
  box-shadow: 0 8px 32px rgba(0, 0, 0, 0.15);
  min-width: 400px;
  max-width: 500px;
}

.warning-content {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
}

.warning-icon {
  flex-shrink: 0;
  width: 32px;
  height: 32px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--p-orange-100);
  border-radius: 50%;
  color: var(--p-orange-600);
  font-size: 1.125rem;
}

.warning-text {
  flex: 1;
}

.warning-text h4 {
  margin: 0 0 0.5rem 0;
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.warning-text p {
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

.warning-actions {
  display: flex;
  gap: 0.75rem;
  justify-content: flex-end;
}

.extend-btn {
  background: var(--p-orange-500);
  border-color: var(--p-orange-500);
  color: white;
}

.extend-btn:hover {
  background: var(--p-orange-600);
  border-color: var(--p-orange-600);
}

.dismiss-btn {
  color: var(--iluria-color-text-secondary);
}

.dismiss-btn:hover {
  color: var(--iluria-color-text-primary);
}

@media (max-width: 768px) {
  .session-warning-container {
    min-width: 300px;
    max-width: 90vw;
  }
  
  .warning-actions {
    flex-direction: column;
  }
}
</style>