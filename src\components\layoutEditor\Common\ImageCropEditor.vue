<template>
      <div class="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-[1100]" @click.self="closeEditor">
    <div class="bg-gray-900 rounded-lg shadow-2xl max-w-6xl w-full mx-4 max-h-[90vh] overflow-hidden" @click.stop>
          <!-- Header -->
        <div class="flex items-center justify-between p-4 border-b border-gray-700">
          <div class="flex items-center space-x-2">
            <svg class="w-5 h-5 text-blue-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"></path>
                </svg>
            <h2 class="text-lg font-semibold text-white">{{ $t('media.imageEditor.title') }}</h2>
              </div>
          <button @click="closeEditor" class="text-gray-400 hover:text-white transition-colors">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
              </svg>
            </button>
          </div>

        <div class="flex h-[600px]">
          <!-- Main Crop Area -->
          <div class="flex-1 bg-gray-800 p-4 relative" style="min-height: 500px;">
            <div class="w-full h-full" style="height: 520px;">
              <div class="crop-container" style="width: 100%; height: 100%; position: relative;" @click.stop @mousedown.stop @mousemove.stop @mouseup.stop>
                <img
                  ref="cropperImage"
                  :src="imageUrl"
                  :alt="$t('media.imageEditor.title')"
                  style="display: block; width: 100%; height: 100%; object-fit: contain;"
                    @load="onImageLoad"
                  @error="onImageError"
                />
              </div>
                </div>
              </div>

          <!-- Sidebar Controls -->
          <div class="w-80 bg-gray-850 border-l border-gray-700 p-6 space-y-6">
                <!-- Preview -->
            <div>
              <div class="flex items-center justify-between mb-3">
                <h3 class="text-sm font-medium text-white">PREVIEW</h3>
                <span class="text-xs text-gray-400">{{ targetWidth }}×{{ targetHeight }}</span>
                  </div>
              <div class="bg-gray-700 rounded-lg p-4 flex items-center justify-center">
                <div 
                  class="preview-container border-2 border-dashed border-gray-500 flex items-center justify-center bg-gray-600"
                  :style="{ width: previewWidth + 'px', height: previewHeight + 'px' }"
                >
                  <canvas
                    ref="previewCanvas"
                    :width="targetWidth"
                    :height="targetHeight"
                    :style="{ width: previewWidth + 'px', height: previewHeight + 'px' }"
                    class="rounded"
                  ></canvas>
                  </div>
                  </div>
                </div>

                <!-- Position Controls -->
            <div>
              <h3 class="text-sm font-medium text-white mb-3">{{ $t('layoutEditor.imagePosition') }}</h3>
              <div class="flex space-x-2">
                    <button 
                  @click="centerImage"
                  class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors flex items-center justify-center space-x-2"
                    >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
                  </svg>
                  <span>{{ $t('layoutEditor.center') }}</span>
                    </button>
                    <button 
                  @click="resetCrop"
                  class="flex-1 bg-gray-700 hover:bg-gray-600 text-white px-3 py-2 rounded text-sm transition-colors flex items-center justify-center space-x-2"
                >
                  <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 4v5h.582m15.356 2A8.001 8.001 0 004.582 9m0 0H9m11 11v-5h-.581m0 0a8.003 8.003 0 01-15.357-2m15.357 2H15"></path>
                  </svg>
                  <span>{{ $t('media.imageEditor.reset') }}</span>
                    </button>
                  </div>
                </div>

            <!-- File Info -->
            <div class="space-y-3 text-sm">
              <div class="flex justify-between">
                <span class="text-gray-400">{{ $t('media.fileInfo.name') }}:</span>
                <span class="text-white truncate ml-2">{{ fileName }}</span>
                  </div>
              <div class="flex justify-between">
                <span class="text-gray-400">{{ $t('layoutEditor.imagePosition') }}:</span>
                <span class="text-white">{{ Math.round(cropData.x) }}, {{ Math.round(cropData.y) }}</span>
                  </div>
              <div class="flex justify-between">
                <span class="text-gray-400">{{ $t('media.imageEditor.crop') }}:</span>
                <span class="text-white">{{ Math.round(cropData.width) }}×{{ Math.round(cropData.height) }}</span>
                </div>
              </div>
            </div>
          </div>

          <!-- Footer -->
        <div class="flex justify-end space-x-3 p-4 border-t border-gray-700">
          <button 
            @click="closeEditor" 
            class="px-6 py-2 bg-gray-600 hover:bg-gray-500 text-white rounded transition-colors"
          >
            {{ $t('layoutEditor.cancel') }}
            </button>
          <button 
            @click="applyCrop" 
            class="px-6 py-2 bg-blue-600 hover:bg-blue-500 text-white rounded transition-colors"
          >
            {{ $t('layoutEditor.apply') }}
            </button>
          </div>
        </div>
      </div>
</template>

<script setup>
  import { ref, onMounted, onUnmounted, computed, watch, nextTick } from 'vue'
  import Cropper from 'cropperjs'
  import { useI18n } from 'vue-i18n'
  
  const { t } = useI18n()

const props = defineProps({
    imageUrl: {
      type: String,
      required: true
    },
    fileName: {
      type: String,
      default: 'image.png'
    },
    targetWidth: {
      type: Number,
      default: 300
    },
    targetHeight: {
      type: Number,
      default: 250
  },
  aspectRatio: {
    type: Number,
    default: null
  },
    quality: {
      type: Number,
      default: 0.9
    }
  })
  
  const emit = defineEmits(['close', 'apply'])
  
  const cropperImage = ref(null)
  const previewCanvas = ref(null)
  let cropper = null
  
  const cropData = ref({
    x: 0,
    y: 0,
    width: 0,
    height: 0
  })
  
  const previewWidth = computed(() => {
    const maxSize = 200
    const ratio = props.targetWidth / props.targetHeight
    if (ratio > 1) {
      return Math.min(maxSize, maxSize * ratio)
    } else {
      return maxSize / ratio
    }
  })
  
  const previewHeight = computed(() => {
  const maxSize = 200
    const ratio = props.targetWidth / props.targetHeight
    if (ratio > 1) {
      return maxSize / ratio
    } else {
      return Math.min(maxSize, maxSize / ratio)
    }
  })
  
const initCropper = async () => {
  await nextTick()
  
  if (cropper) {
    cropper.destroy()
    cropper = null
  }

  if (!cropperImage.value) {
    return
  }

  const aspectRatio = props.aspectRatio || (props.targetWidth / props.targetHeight)

  setTimeout(() => {
    try {
      cropper = new Cropper(cropperImage.value, {
        aspectRatio: aspectRatio,
        viewMode: 1,
        dragMode: 'move',
        autoCropArea: 0.9,
        responsive: true,
        restore: false,
        center: true,
        highlight: true,
        cropBoxMovable: true,
        cropBoxResizable: true,
        toggleDragModeOnDblclick: false,
        checkCrossOrigin: false,
        zoomable: false,
        rotatable: false,
        scalable: true, 
        minCropBoxWidth: 100,
        minCropBoxHeight: 100,
        crop: updateCropData,
        ready: () => {
          optimizeInitialCrop()
          updatePreview()
          updateCropData()
        },
        zoom: updatePreview,
        cropmove: updatePreview
      })
  } catch (error) {
      console.error('Erro ao criar cropper:', error)
    }
    }, 300)
}

const onImageLoad = () => {
  // Não inicializar aqui para evitar duplicação
}

const onImageError = (error) => {
  // Handle image load error
}

const updateCropData = () => {
    if (!cropper) return
    
    const data = cropper.getData()
    cropData.value = {
      x: data.x,
      y: data.y,
      width: data.width,
      height: data.height
    }
    
    updatePreview()
  }
  
  const updatePreview = () => {
    if (!cropper || !previewCanvas.value || !cropperImage.value) return
  
    const canvas = previewCanvas.value
    const ctx = canvas.getContext('2d')
    
    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)
    
    try {
      // 🎯 USAR O MESMO MÉTODO QUE SERÁ APLICADO NO RESULTADO FINAL
      const croppedCanvas = cropper.getCroppedCanvas({
        width: props.targetWidth,
        height: props.targetHeight,
        imageSmoothingEnabled: true,
        imageSmoothingQuality: 'high'
      })
      
      if (croppedCanvas) {
        // Draw the cropped result to the preview canvas - EXATAMENTE como será aplicado
        ctx.drawImage(croppedCanvas, 0, 0, props.targetWidth, props.targetHeight)
      }
    } catch (error) {
      // Fallback method if getCroppedCanvas fails
      const cropDataResult = cropper.getData()
      const imageData = cropper.getImageData()
      const imageElement = cropperImage.value
      
      if (!imageElement || !imageElement.complete) return
      
      // Calculate scale factors more carefully
      const displayedImageWidth = imageData.width
      const displayedImageHeight = imageData.height
      const naturalImageWidth = imageElement.naturalWidth
      const naturalImageHeight = imageElement.naturalHeight
      
      // Scale from displayed crop area to natural image coordinates
      const scaleX = naturalImageWidth / displayedImageWidth
      const scaleY = naturalImageHeight / displayedImageHeight
      
      const sourceX = Math.max(0, cropDataResult.x * scaleX)
      const sourceY = Math.max(0, cropDataResult.y * scaleY)
      const sourceWidth = Math.min(naturalImageWidth - sourceX, cropDataResult.width * scaleX)
      const sourceHeight = Math.min(naturalImageHeight - sourceY, cropDataResult.height * scaleY)
      
      // Ensure we don't go outside image bounds
      if (sourceWidth > 0 && sourceHeight > 0) {
        ctx.drawImage(
          imageElement,
          sourceX, sourceY, sourceWidth, sourceHeight,
          0, 0, props.targetWidth, props.targetHeight
        )
      }
    }
  }
  
  // 🎯 Otimizar crop inicial baseado no target dimensions
  const optimizeInitialCrop = () => {
    if (!cropper || !cropperImage.value) return
    
    try {
      const imageData = cropper.getImageData()
      const containerData = cropper.getContainerData()
      const targetAspectRatio = props.targetWidth / props.targetHeight
      
      // Calcular dimensões ideais do crop box para o aspect ratio do target
      let idealCropWidth, idealCropHeight
      
      if (imageData.aspectRatio > targetAspectRatio) {
        // Imagem mais larga que o target - limitar pela altura
        idealCropHeight = Math.min(imageData.height * 0.8, containerData.height * 0.8)
        idealCropWidth = idealCropHeight * targetAspectRatio
      } else {
        // Imagem mais alta que o target - limitar pela largura
        idealCropWidth = Math.min(imageData.width * 0.8, containerData.width * 0.8)
        idealCropHeight = idealCropWidth / targetAspectRatio
      }
      
      // Centralizar o crop box
      const left = (containerData.width - idealCropWidth) / 2
      const top = (containerData.height - idealCropHeight) / 2
      
      cropper.setCropBoxData({
        left: Math.max(0, left),
        top: Math.max(0, top),
        width: idealCropWidth,
        height: idealCropHeight
      })
      

      
    } catch (error) {
      console.warn('⚠️ Erro ao otimizar crop inicial:', error)
    }
  }

  const centerImage = () => {
    if (!cropper) return
    
    const containerData = cropper.getContainerData()
    const cropBoxData = cropper.getCropBoxData()
    
    cropper.setCropBoxData({
      left: (containerData.width - cropBoxData.width) / 2,
      top: (containerData.height - cropBoxData.height) / 2
    })
  }
  
  const resetCrop = () => {
    if (!cropper) return
    
    cropper.reset()
    updatePreview()
}

const applyCrop = async () => {
    if (!cropper || !cropperImage.value) {
        return
    }

    try {
        // 🎯 SOLUÇÃO HÍBRIDA: Canvas + CSS para imagens grandes
        
        // 1. Gerar imagem cortada real usando Canvas (MELHOR QUALIDADE)
        const croppedCanvas = cropper.getCroppedCanvas({
            width: props.targetWidth,
            height: props.targetHeight,
            imageSmoothingEnabled: true,
            imageSmoothingQuality: 'high'
        })
        
        if (croppedCanvas) {
            // Converter canvas para blob e URL
            const blob = await new Promise(resolve => {
                croppedCanvas.toBlob(resolve, 'image/webp', 0.95)
            })
            const croppedImageUrl = URL.createObjectURL(blob)
            
            
            
            const result = {
                // 🚀 IMAGEM CORTADA REAL - Melhor performance e qualidade
                croppedImageUrl: croppedImageUrl,
                croppedBlob: blob,
                
                // Estilos simples para imagem cortada
                readyToUseCSS: {
                    width: '100%',
                    height: '100%',
                    objectFit: 'cover',
                    objectPosition: 'center'
                },
                
                containerCSS: {
                    position: 'relative',
                    overflow: 'hidden',
                    width: `${props.targetWidth}px`,
                    height: `${props.targetHeight}px`
                },
                
                // Informações da imagem cortada
                width: props.targetWidth,
                height: props.targetHeight,
                
                // Original image info para fallback
                originalUrl: props.imageUrl,
                imageData: {
                    naturalWidth: cropperImage.value.naturalWidth,
                    naturalHeight: cropperImage.value.naturalHeight
                },
                
                // Target dimensions
                targetDimensions: { 
                    width: props.targetWidth, 
                    height: props.targetHeight 
                }
            }
            
            
            
            emit('apply', result)
            return
        }
    } catch (error) {
        console.warn('⚠️ Erro ao gerar imagem cortada via Canvas, usando CSS fallback:', error)
    }
    
    // 📋 FALLBACK: CSS Transform (método original)
    
    const cropDataResult = cropper.getData()
    const imageData = cropper.getImageData()
    const imageElement = cropperImage.value
    
    if (!imageElement) return
    
    
    
    // 🎯 CÁLCULOS CORRIGIDOS PARA POSICIONAMENTO CSS
    
    // 1. Converter coordenadas do cropper para coordenadas da imagem natural
    const scaleX = imageElement.naturalWidth / imageData.naturalWidth
    const scaleY = imageElement.naturalHeight / imageData.naturalHeight
    
    // 2. Coordenadas reais do crop na imagem natural
    const realCropX = cropDataResult.x * scaleX
    const realCropY = cropDataResult.y * scaleY
    const realCropWidth = cropDataResult.width * scaleX
    const realCropHeight = cropDataResult.height * scaleY
    
    
    
    // 3. Escala necessária para que a região cortada preencha o container target
    const scaleToFitX = props.targetWidth / realCropWidth
    const scaleToFitY = props.targetHeight / realCropHeight
    const finalScale = Math.max(scaleToFitX, scaleToFitY)
    
    
    
    // 4. Tamanho final da imagem quando escalada
    const scaledImageWidth = imageElement.naturalWidth * finalScale
    const scaledImageHeight = imageElement.naturalHeight * finalScale
    
    // 5. Posição da região de crop na imagem escalada
    const scaledCropX = realCropX * finalScale
    const scaledCropY = realCropY * finalScale
    const scaledCropWidth = realCropWidth * finalScale
    const scaledCropHeight = realCropHeight * finalScale
    
    // 6. 🎯 OFFSET CORRIGIDO: A região cortada deve ficar na posição (0,0) do container
    // Como usamos Math.max() na escala, uma das dimensões vai preencher exatamente o target
    const offsetX = -scaledCropX
    const offsetY = -scaledCropY
    

    
    const result = {
        // CSS Transform como fallback
        readyToUseCSS: {
            position: 'absolute',
            top: '0',
            left: '0',
            width: `${scaledImageWidth}px`,
            height: `${scaledImageHeight}px`,
            transform: `translate(${offsetX}px, ${offsetY}px)`,
            objectFit: 'none',
            objectPosition: 'initial'
        },
        
        containerCSS: {
            position: 'relative',
            overflow: 'hidden',
            width: `${props.targetWidth}px`,
            height: `${props.targetHeight}px`
        },
        
        // Legacy support
        objectPosition: `${((realCropX + realCropWidth/2) / imageElement.naturalWidth) * 100}% ${((realCropY + realCropHeight/2) / imageElement.naturalHeight) * 100}%`,
        objectFit: 'cover',
        scale: finalScale,
        
        // Crop data original para persistência
        cropData: {
            x: realCropX,
            y: realCropY,
            width: realCropWidth,
            height: realCropHeight
        },
        
        // Target dimensions
        width: props.targetWidth,
        height: props.targetHeight,
        
        // Original image info
        originalUrl: props.imageUrl,
        imageData: {
            naturalWidth: imageElement.naturalWidth,
            naturalHeight: imageElement.naturalHeight
        },
        
        targetDimensions: { 
            width: props.targetWidth, 
            height: props.targetHeight 
        }
    }
    
    
    emit('apply', result)
}

const closeEditor = () => {
    emit('close')
}

// Watchers
  watch(() => props.imageUrl, () => {
    if (cropperImage.value) {
      initCropper()
    }
  })
  
  // Lifecycle
onMounted(async () => {
  await nextTick()
  
  if (props.imageUrl) {
    // Aguardar a imagem carregar completamente
    setTimeout(() => {
      if (cropperImage.value) {
        initCropper()
      }
    }, 200)
  }
})
  
  onUnmounted(() => {
    if (cropper) {
      cropper.destroy()
    }
})
</script>

<style scoped>
  .crop-container {
    max-width: 100%;
    max-height: 100%;
  }
  
  .slider::-webkit-slider-thumb {
    appearance: none;
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3B82F6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .slider::-moz-range-thumb {
    width: 20px;
    height: 20px;
    border-radius: 50%;
    background: #3B82F6;
    cursor: pointer;
    border: 2px solid #ffffff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  }
  
  .bg-gray-850 {
    background-color: #1f2937;
  }
  
  /* Cropper.js custom styles */
  :deep(.cropper-container) {
    direction: ltr;
    font-size: 0;
    line-height: 0;
  position: relative;
    touch-action: none;
    user-select: none;
    width: 100% !important;
    height: 100% !important;
    min-height: 400px;
  }
  
  :deep(.cropper-wrap-box) {
    position: relative;
    width: 100%;
    height: 100%;
  }
  
  :deep(.cropper-canvas) {
    position: absolute;
    overflow: hidden;
  }
  
  :deep(.cropper-crop-box) {
    border: 2px solid #3B82F6 !important;
    box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2), 0 0 20px rgba(59, 130, 246, 0.4) !important;
    position: absolute;
    z-index: 200;
  }
  
  :deep(.cropper-view-box) {
    border: 1px solid rgba(59, 130, 246, 0.5) !important;
    outline: 1px solid rgba(0, 0, 0, 0.1);
    outline-color: rgba(255, 255, 255, 0.3);
    overflow: hidden;
  }
  
  :deep(.cropper-dashed) {
    border: 0 dashed #ffffff;
    opacity: 0.3;
    position: absolute;
  }
  
  :deep(.cropper-dashed.dashed-h) {
    border-bottom-width: 1px;
    border-top-width: 1px;
    height: calc(100% / 3);
    left: 0;
    top: calc(100% / 3);
    width: 100%;
  }
  
  :deep(.cropper-dashed.dashed-v) {
    border-left-width: 1px;
    border-right-width: 1px;
  height: 100%;
    left: calc(100% / 3);
  top: 0;
    width: calc(100% / 3);
  }
  
  :deep(.cropper-face) {
    background-color: inherit;
    opacity: 0.1;
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  }
  
  :deep(.cropper-line) {
    background-color: #3B82F6;
    opacity: 0.6;
    position: absolute;
  }
  
  :deep(.cropper-line.line-e) {
    cursor: ew-resize;
    right: -3px;
    top: 0;
    width: 5px;
  height: 100%;
  }
  
  :deep(.cropper-line.line-n) {
    cursor: ns-resize;
    height: 5px;
    left: 0;
    top: -3px;
    width: 100%;
  }
  
  :deep(.cropper-line.line-w) {
    cursor: ew-resize;
    left: -3px;
    top: 0;
    width: 5px;
  height: 100%;
  }
  
  :deep(.cropper-line.line-s) {
    bottom: -3px;
    cursor: ns-resize;
    height: 5px;
    left: 0;
  width: 100%;
  }
  
  :deep(.cropper-point) {
    background-color: #3B82F6;
    border: 2px solid #ffffff;
  border-radius: 50%;
    height: 12px;
    width: 12px;
    opacity: 0.9;
    position: absolute;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.3);
  }
  
  :deep(.cropper-point.point-e) {
    cursor: ew-resize;
    margin-top: -6px;
    right: -6px;
    top: 50%;
  }
  
  :deep(.cropper-point.point-n) {
    cursor: ns-resize;
    left: 50%;
    margin-left: -6px;
    top: -6px;
  }
  
  :deep(.cropper-point.point-w) {
    cursor: ew-resize;
    left: -6px;
    margin-top: -6px;
    top: 50%;
  }
  
  :deep(.cropper-point.point-s) {
    bottom: -6px;
    cursor: ns-resize;
    left: 50%;
    margin-left: -6px;
  }
  
  :deep(.cropper-point.point-ne) {
    cursor: nesw-resize;
    right: -6px;
    top: -6px;
  }
  
  :deep(.cropper-point.point-nw) {
    cursor: nwse-resize;
    left: -6px;
    top: -6px;
  }
  
  :deep(.cropper-point.point-sw) {
    bottom: -6px;
    cursor: nesw-resize;
    left: -6px;
  }
  
  :deep(.cropper-point.point-se) {
    bottom: -6px;
    cursor: nwse-resize;
    right: -6px;
  }
  
  :deep(.cropper-modal) {
    background-color: rgba(0, 0, 0, 0.6);
    opacity: 1;
    pointer-events: none;
  }
  
  :deep(.cropper-crop-box),
  :deep(.cropper-point),
  :deep(.cropper-line),
  :deep(.cropper-face) {
    pointer-events: auto;
}
</style> 
