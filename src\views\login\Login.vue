<template>
  <LoginPage>
    <form action="#" id="login-form" v-if="!viewMfaForm">
      <div class="mb-4">
        <IluriaInputText
          id="userEmail"
          v-model="email"
          type="email"
          :label="$t('login.email')"
          placeholder="<EMAIL>"
          autocomplete="username"
          required
          inputClass="login-input"
        />
      </div>

      <div class="mb-4 relative">
        <IluriaInputText
          id="userPassword"
          v-model="password"
          :type="passwordInputType"
          :label="$t('login.password')"
          placeholder="********"
          autocomplete="current-password"
          required
          inputClass="login-input"
        />
        <div class="absolute right-3 top-1/2  flex items-center">
          <ShowHidePasswordButton
            :passwordInputType="passwordInputType"
            @passwordTypeChange="changePasswordType"
          />
        </div>
      </div>

      <div class="flex mb-4">
        <input
          type="checkbox"
          id="rememberMe"
          v-model="rememberMe"
          class="login-checkbox w-4 h-4 mr-2 rounded"
        />
        <label for="rememberMe" class="login-label mb-2 cursor-pointer block text-sm font-normal">
          Manter-me conectado
        </label>
      </div>

      <div id="login-button-container" class="mb-4">
        <LoginButton
          ref="loginButtonRef"
          :password="password"
          :email="email"
          :rememberMe="rememberMe"
          @showMfaForm="showMfaForm"
        />
      </div>

      <div id="forgot-password-create-account" class="flex justify-center gap-4 text-sm">
        <a href="/forgot-password" class="login-link">{{ $t("login.forgotPassword") }}</a>
        <a href="/request-signup" class="login-link">{{ $t("login.createAccount") }}</a>
      </div>
    </form>

    <MfaForm v-if="viewMfaForm" :email="email" :mfaType="mfaType" />
  </LoginPage>
</template>

<script setup>
import { ref } from 'vue';
import MfaForm from '@/components/login/MfaForm.vue';
import ShowHidePasswordButton from "@/components/login/ShowHidePasswordButton.vue";
import LoginButton from '@/components/login/LoginButton.vue';
import LoginPage from '@/components/login/LoginPage.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const passwordInputType = ref('password');
const email = ref('');
const password = ref('');
const rememberMe = ref(false);
const viewMfaForm = ref(false);
const mfaType = ref('EMAIL_VERIFICATION');

const changePasswordType = (type) => {
  passwordInputType.value = type;
};

const showMfaForm = (mfaTypeFromLogin) => {
  if (mfaTypeFromLogin && mfaTypeFromLogin !== "false") {
    mfaType.value = mfaTypeFromLogin;
    viewMfaForm.value = true;
  }
};
</script>
<style scoped>
#login-button-container {
  height: 48px;
  overflow: visible;
  margin-top: 30px;
  margin-bottom: 40px;
}

#forgot-password-create-account {
  margin: auto;
  text-align: center;
}

button {
  border: 1px solid transparent;
  border-radius: 8px;
  padding: 10px 16px;
  outline: none;
  width: 100%;
}

/* Estilos específicos para login - sobrescrever temas */
.login-label {
  color: #374151 !important;
}

.login-link {
  color: #374151 !important;
  text-decoration: none;
}

.login-link:hover {
  color: #1f2937 !important;
  text-decoration: underline;
}

/* Checkbox customizado para login */
.login-checkbox {
  appearance: none;
  background-color: #ffffff !important;
  border: 2px solid #d1d5db !important;
  border-radius: 4px;
  cursor: pointer;
  position: relative;
}

.login-checkbox:checked {
  background-color: rgb(218, 18, 0) !important;
  border-color: rgb(218, 18, 0) !important;
}

.login-checkbox:checked::after {
  content: '✓';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: white;
  font-size: 12px;
  font-weight: bold;
}

.login-checkbox:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(218, 18, 0, 0.3) !important;
}

.login-checkbox:hover {
  border-color: #9ca3af !important;
}

/* Posicionamento do botão mostrar/esconder senha */
#login-form .password-toggle-btn {
  color: #9ca3af !important;
  font-size: 12px !important;
}

#login-form .password-toggle-btn:hover {
  color: rgb(218, 18, 0) !important;
}
</style>

<style>
/* Estilos globais para sobrescrever temas nos inputs de login */
#login-form .custom-input-group {
  background-color: #f9fafb !important;
  background: #f9fafb !important;
  border-color: #d1d5db !important;
}

#login-form .custom-input-group:focus-within {
  border-color: rgb(218, 18, 0) !important;
  box-shadow: 0 0 0 2px rgba(218, 18, 0, 0.3) !important;
}

#login-form .input-themed-custom {
  color: #374151 !important;
  background: transparent !important;
}

#login-form .input-themed-custom::placeholder {
  color: #9ca3af !important;
}

/* Sobrescrever variáveis CSS de tema especificamente para login */
#login-form {
  --iluria-color-input-bg: #f9fafb !important;
  --iluria-color-input-border: #d1d5db !important;
  --iluria-color-input-border-focus: rgb(218, 18, 0) !important;
  --iluria-color-input-text: #374151 !important;
  --iluria-color-input-placeholder: #9ca3af !important;
  --iluria-color-focus-ring: rgba(218, 18, 0, 0.3) !important;
}

/* Labels dos inputs */
#login-form label {
  color: #374151 !important;
}

/* Força aplicação nos inputs do IluriaInputText */
#login-form .custom-input-group,
#login-form .custom-input-group:focus-within,
#login-form .custom-input-group:hover {
  background-color: #f9fafb !important;
  background: #f9fafb !important;
  border-color: #d1d5db !important;
}

#login-form .custom-input-group:focus-within {
  border-color: rgb(218, 18, 0) !important;
  box-shadow: 0 0 0 2px rgba(218, 18, 0, 0.3) !important;
}

/* Força cor do texto nos inputs */
#login-form .input-themed-custom,
#login-form .p-inputtext,
#login-form input[type="email"],
#login-form input[type="password"],
#login-form input[type="text"] {
  color: #374151 !important;
}

/* Força cor do placeholder */
#login-form .input-themed-custom::placeholder,
#login-form .p-inputtext::placeholder,
#login-form input::placeholder {
  color: #9ca3af !important;
}
</style>
