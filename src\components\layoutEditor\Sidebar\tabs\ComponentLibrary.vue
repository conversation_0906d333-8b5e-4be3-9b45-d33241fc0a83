<template>
  <div class="component-library">
    <!-- Header da biblioteca -->
    <div class="library-header">
      <h3 class="library-title">{{ $t('layoutEditor.componentLibrary') }}</h3>
      <p class="library-subtitle">{{ $t('layoutEditor.componentLibraryDesc') }}</p>
    </div>

    <!-- Search/Filter -->
    <div class="library-search">
      <div class="search-input-wrapper">
        <svg class="search-icon" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
        <input 
          v-model="searchQuery"
          type="text" 
          class="search-input"
          :placeholder="$t('layoutEditor.searchComponents')"
        />
      </div>
    </div>

    <!-- Lista de categorias com componentes -->
    <div class="categories-container">
      <div 
        v-for="(category, categoryId) in filteredCategories" 
        :key="categoryId"
        class="category"
      >
        <!-- Header da categoria -->
        <button 
          class="category-header"
          @click="toggleCategory(categoryId)"
          :aria-expanded="isExpanded(categoryId)"
        >
          <div class="category-info">
            <div class="category-icon" :style="{ color: getCategoryColor(categoryId) }">
              <component :is="getCategoryIcon(categoryId)" />
            </div>
            <span class="category-title">{{ category.title }}</span>
          </div>
          <div class="expand-icon" :class="{ expanded: isExpanded(categoryId) }">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M6 9L12 15L18 9" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
            </svg>
          </div>
        </button>

        <!-- Componentes da categoria -->
        <Transition name="category-expand">
          <div 
            v-if="isExpanded(categoryId)"
            class="category-content"
          >
            <div 
              v-for="(component, componentId) in category.components" 
              :key="componentId"
              class="component-item"
              @click="selectComponent(componentId, component)"
              :title="component.description"
            >
              <div class="component-icon">
                <component :is="getComponentIcon(componentId)" />
              </div>
              <div class="component-info">
                <h4 class="component-title">{{ component.title }}</h4>
                <p class="component-description">{{ component.description }}</p>
              </div>
              <div class="add-icon">
                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                  <line x1="12" y1="5" x2="12" y2="19" stroke-width="2"/>
                  <line x1="5" y1="12" x2="19" y2="12" stroke-width="2"/>
                </svg>
              </div>
            </div>
          </div>
        </Transition>
      </div>
    </div>

    <!-- Estado vazio -->
    <div v-if="!hasResults" class="empty-state">
      <div class="empty-icon">
        <svg width="48" height="48" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      </div>
      <h4>{{ $t('layoutEditor.noComponentsFound') }}</h4>
      <p>{{ $t('layoutEditor.tryDifferentSearch') }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useComponentLibrary } from '@/views/layoutEditor/composables/useComponentLibrary'
import { useI18n } from 'vue-i18n'
import {
  TextIcon,
  Image01Icon,
  ShoppingBag01Icon,
  Touch02Icon,
  GridViewIcon,
  Add01Icon,
  Layout01Icon,
  PlayCircleIcon,
  StarIcon,
  LocationIcon,
  BuildingIcon
} from '@hugeicons-pro/core-bulk-rounded'

const { t } = useI18n()
const emit = defineEmits(['component-select'])

// Props
const props = defineProps({
  layoutType: {
    type: String,
    default: null
  }
})

// Composables
const { componentCategories, initializeIfNeeded, setLayoutType } = useComponentLibrary(props.layoutType)

// Estado interno
const searchQuery = ref('')
const expandedState = ref({})

// Inicializa a biblioteca
initializeIfNeeded()

// ✅ NOVO: Observar mudanças no tipo de layout
watch(() => props.layoutType, (newLayoutType) => {
  setLayoutType(newLayoutType)
}, { immediate: true })

// Inicializa apenas a primeira categoria como expandida
watch(() => componentCategories.value, (newCategories) => {
  if (newCategories && Object.keys(newCategories).length > 0) {
    const categoryKeys = Object.keys(newCategories)
    const newState = {}
    
    categoryKeys.forEach((key, index) => {
      newState[key] = index === 0 // Apenas a primeira categoria aberta
    })
    
    expandedState.value = newState
  }
}, { immediate: true })

// Filtro de componentes baseado na busca
const filteredCategories = computed(() => {
  const categories = componentCategories.value
  if (!categories || Object.keys(categories).length === 0) {
    return {}
  }

  if (!searchQuery.value.trim()) {
    return categories
  }

  const query = searchQuery.value.toLowerCase().trim()
  const filtered = {}

  Object.entries(categories).forEach(([categoryId, category]) => {
    const filteredComponents = {}
    let hasMatches = false

    Object.entries(category.components).forEach(([componentId, component]) => {
      const matchesTitle = component.title.toLowerCase().includes(query)
      const matchesDescription = component.description.toLowerCase().includes(query)
      
      if (matchesTitle || matchesDescription) {
        filteredComponents[componentId] = component
        hasMatches = true
      }
    })

    if (hasMatches) {
      filtered[categoryId] = {
        ...category,
        components: filteredComponents
      }
    }
  })

  return filtered
})

// Verifica se há resultados
const hasResults = computed(() => {
  return Object.keys(filteredCategories.value).length > 0
})

// Controle de expansão
const toggleCategory = (categoryId) => {
  const currentState = expandedState.value[categoryId]
  
  if (currentState) {
    // Se está aberta, apenas fecha ela
    expandedState.value[categoryId] = false
  } else {
    // Se está fechada, fecha todas e abre apenas esta
    const newState = {}
    Object.keys(expandedState.value).forEach(key => {
      newState[key] = key === categoryId
    })
    expandedState.value = newState
  }
}

const isExpanded = (categoryId) => {
  return expandedState.value[categoryId] || false
}

// Seleção de componente
const selectComponent = (componentId, component) => {

  
  emit('component-select', {
    type: componentId,
    data: component
  })
}

// Ícones das categorias
const getCategoryIcon = (categoryId) => {
  const icons = {
    text: TextIcon,
    media: Image01Icon,
    products: ShoppingBag01Icon,
    ecommerce: ShoppingBag01Icon,
    interactive: Touch02Icon,
    layout: GridViewIcon,
    content: Layout01Icon,
    custom: Add01Icon,
    marketing: StarIcon,
    testimonials: LocationIcon
  }
  return icons[categoryId] || Layout01Icon
}

// Ícones dos componentes
const getComponentIcon = (componentId) => {
  const icons = {
    'video': PlayCircleIcon,
    'carousel': Image01Icon,
    'header': Layout01Icon,
    'footer': Layout01Icon,
    'dynamic-grid-produtos': ShoppingBag01Icon,
    'product-grid': ShoppingBag01Icon,
            'product-checkout': ShoppingBag01Icon, // ✅ Ícone para componente de checkout
    'company-information': BuildingIcon,
    'location': LocationIcon,
    'statement': TextIcon,
    'payment-benefits': StarIcon,
    'customer-review': StarIcon,
    'special-offers': StarIcon
  }
  return icons[componentId] || Layout01Icon
}

// Cores das categorias
const getCategoryColor = (categoryId) => {
  const colors = {
    text: '#3b82f6',
    media: '#10b981',
    products: '#f59e0b',
    ecommerce: '#f59e0b',
    interactive: '#8b5cf6',
    content: '#0ea5e9',
    layout: '#64748b',
    marketing: '#ef4444',
    testimonials: '#84cc16'
  }
  return colors[categoryId] || '#64748b'
}
</script>

<style scoped>
.component-library {
  height: 100%;
  display: flex;
  flex-direction: column;
  background: var(--iluria-color-surface);
}

/* Header */
.library-header {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.library-title {
  margin: 0 0 0.25rem 0;
  font-size: 1.125rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.library-subtitle {
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

/* Search */
.library-search {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.search-input-wrapper {
  position: relative;
}

.search-icon {
  position: absolute;
  left: 0.75rem;
  top: 50%;
  transform: translateY(-50%);
  color: var(--iluria-color-text-secondary);
  pointer-events: none;
}

.search-input {
  width: 100%;
  padding: 0.75rem 0.75rem 0.75rem 2.5rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-background);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: all 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
}

.search-input::placeholder {
  color: var(--iluria-color-text-secondary);
}

/* Categories */
.categories-container {
  flex: 1;
  overflow-y: auto;
  padding: 0.5rem 0;
}

.categories-container::-webkit-scrollbar {
  width: 6px;
}

.categories-container::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.categories-container::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.category {
  margin: 0;
}

.category-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  padding: 0.875rem 1rem;
  background: transparent;
  border: none;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  text-align: left;
}

.category-header:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

.category-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.category-icon {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
}

.category-title {
  font-weight: 600;
  font-size: 0.875rem;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  color: var(--iluria-color-text-primary);
}

.expand-icon {
  width: 16px;
  height: 16px;
  color: var(--iluria-color-text-secondary);
  transition: transform 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  flex-shrink: 0;
}

.expand-icon.expanded {
  transform: rotate(180deg);
}

/* Category Content */
.category-content {
  background: var(--iluria-color-background);
  border-top: 1px solid var(--iluria-color-border);
  border-bottom: 1px solid var(--iluria-color-border);
  margin: 0 0.5rem;
  border-radius: 8px;
  overflow: hidden;
}

/* Component Items */
.component-item {
  display: flex;
  align-items: center;
  gap: 0.875rem;
  padding: 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-background);
}

.component-item:last-child {
  border-bottom: none;
}

.component-item:hover {
  background: var(--iluria-color-hover);
  transform: translateX(2px);
}

.component-item:active {
  transform: translateX(1px);
  background: var(--iluria-color-primary-bg);
}

.component-icon {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-primary);
  flex-shrink: 0;
  transition: all 0.2s ease;
}

.component-item:hover .component-icon {
  background: var(--iluria-color-primary-bg);
  border-color: var(--iluria-color-primary-border);
  color: var(--iluria-color-primary);
}

.component-info {
  flex: 1;
  min-width: 0;
}

.component-title {
  margin: 0 0 0.25rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
}

.component-description {
  margin: 0;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  opacity: 0.8;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.add-icon {
  width: 16px;
  height: 16px;
  color: var(--iluria-color-text-secondary);
  flex-shrink: 0;
  opacity: 0;
  transition: all 0.2s ease;
}

.component-item:hover .add-icon {
  opacity: 1;
  color: var(--iluria-color-primary);
}

/* Animations */
.category-expand-enter-active {
  transition: all 0.3s cubic-bezier(0.34, 1.56, 0.64, 1);
  overflow: hidden;
}

.category-expand-leave-active {
  transition: all 0.2s cubic-bezier(0.4, 0.0, 0.2, 1);
  overflow: hidden;
}

.category-expand-enter-from,
.category-expand-leave-from {
  opacity: 0;
  max-height: 0;
  transform: translateY(-8px);
}

.category-expand-enter-to,
.category-expand-leave-to {
  opacity: 1;
  max-height: 600px;
  transform: translateY(0);
}

/* Empty State */
.empty-state {
  flex: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

.empty-icon {
  width: 48px;
  height: 48px;
  margin-bottom: 1rem;
  opacity: 0.5;
}

.empty-state h4 {
  margin: 0 0 0.5rem 0;
  color: var(--iluria-color-text-primary);
}

.empty-state p {
  margin: 0;
  font-size: 0.875rem;
}

/* Responsividade */
@media (max-width: 768px) {
  .library-header {
    padding: 0.75rem;
  }
  
  .library-search {
    padding: 0.75rem;
  }
  
  .component-item {
    padding: 0.875rem 0.75rem;
  }
  
  .component-icon {
    width: 28px;
    height: 28px;
  }
  
  .component-title {
    font-size: 0.8rem;
  }
  
  .component-description {
    font-size: 0.7rem;
  }
}
</style> 
