<template>
    <div class="section-container bg-white rounded-2xl p-6 shadow-md mb-3">
        <div v-if="title" class="section-header mb-3 flex flex-row border-b-solid border-b-1 border-b-gray-200">
            <h2 class="text-sm font-semibold pl-0.5">{{ title }}</h2>
            <span v-if="subtitle" class="text-xs/5 text-gray-500 ml-2">{{ subtitle }}</span>
        </div>
        <slot></slot>
    </div>
</template>

<script setup>
defineProps({
    title: {
        type: String,
        default: undefined
    },
    subtitle: {
        type: String,
        default: undefined
    }
});
</script>

<style scoped>

</style>
