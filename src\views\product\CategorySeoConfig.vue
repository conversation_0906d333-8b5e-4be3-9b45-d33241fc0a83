<template>
  <div class="flex justify-between items-center mb-6">
    <div>
      <h1 class="text-2xl font-bold text-gray-800">{{ t('seoConfig.title') }}</h1>
    </div>
    <IluriaButton 
        type="button" 
        severity="success" 
        :hugeIcon="FloppyDiskIcon"
        @click="saveSeoData"
        :loading="isSaving"
      >
        {{ t('save') }}
    </IluriaButton>
  </div>
  <Form v-slot="$form" @submit="saveSeoData" :resolver="resolver" :initialValues="seoData.value">
    <ViewContainer :title="t('seoConfig.storeSectionTitle')" class="mb-8" background-color="#C1B8E3">
      <div class="space-y-2">
        <div class="space-y-2 mb-4">
          <IluriaInputText 
            id="displayTitle"
            :label="t('seoConfig.categoryTitle')"
            v-model="seoData.displayTitle" 
            :placeholder="t('seoConfig.categoryTitlePlaceholder')"
            class="w-full"
            :formContext="$form.displayTitle"
          />
        </div>
        <div class="space-y-2 mb-4">
          <label class="block text-sm font-medium text-gray-700">{{ t('seoConfig.categoryDescription') }}</label>
          <IluriaInputText
            id="displayDescription"
            v-model="seoData.displayDescription" 
            :placeholder="t('seoConfig.categoryDescriptionPlaceholder')"
            class="w-full"
            :formContext="$form.displayDescription"
          />
        </div>
        <div class="space-y-2 mb-4">
          <IluriaFileUpload 
            id="category-image-uploader"
            ref="categoryImageUploader"
            :label="t('seoConfig.categoryImage')"
            :existingImages="seoData.displayImageUrl ? [{ id: 'displayImage', url: seoData.displayImageUrl }] : []"
            :maxFiles="1"
            @select="(event) => onImageSelect(event, 'displayImage')"
            @error="onImageUploadError"
            @remove-image="removeCategoryImage"
            :formContext="$form.displayImageUrl"
          />
        </div>
        <div class="space-y-2">
          <IluriaFileUpload 
          id="category-icon-uploader"
          ref="categoryIconUploader"
          :existingImages="seoData.displayIconUrl ? [{ id: 'displayIcon', url: seoData.displayIconUrl }] : []"
          :label="t('seoConfig.categoryIcon')"
          :maxFiles="1"
          @select="(event) => onImageSelect(event, 'displayIcon')"
          @error="onImageUploadError"
          @remove-image="removeCategoryImage"
          :formContext="$form.displayIconUrl"
        />
          <p class="text-xs text-gray-500">{{ t('seoConfig.categoryIconHint') }}</p>
        </div>
      </div>
    </ViewContainer>
    <ViewContainer :title="t('seoConfig.searchEnginesSectionTitle')" background-color="#C1B8E3"> 
      <div class="space-y-2">
        <div class="space-y-2 mb-4">
          <label class="block text-sm font-medium text-gray-700">{{ t('seoConfig.metaTitle') }}</label>
          <IluriaInputText 
            id="metaTitle"
            v-model="seoData.metaTitle" 
            :placeholder="t('seoConfig.metaTitlePlaceholder')"
            class="w-full"
            :formContext="$form.metaTitle"
          />
          <p class="text-xs text-gray-500">{{ t('seoConfig.metaTitleHint') }}</p>
        </div>
        <div class="space-y-2 mb-4">
          <label class="block text-sm font-medium text-gray-700">{{ t('seoConfig.metaDescription') }}</label>
          <IluriaInputText
            id="metaDescription"
            v-model="seoData.metaDescription" 
            :placeholder="t('seoConfig.metaDescriptionPlaceholder')"
            class="w-full"
            :formContext="$form.metaDescription"
          />
          <p class="text-xs text-gray-500">{{ t('seoConfig.metaDescriptionHint') }}</p>
        </div>
        <div class="space-y-2 mb-4">
          <label class="block text-sm font-medium text-gray-700">{{ t('seoConfig.categoryUrl') }}</label>
          <IluriaInputText 
            id="categoryUrl"
            v-model="seoData.categoryUrl" 
            class="w-full"
            :formContext="$form.categoryUrl"
            @input="formatCategoryUrl"
          />
          <div class="mt-1">
            <span class="text-sm text-gray-500">{{ t('seoConfig.preview') }}:</span>
            <span class="text-sm text-blue-600 ml-2">{{ fullCategoryUrl }}</span>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <label class="block text-sm font-medium text-gray-700">{{ t('seoConfig.googlePreview') }}</label>
          <div class="border p-4 rounded bg-gray-50">
            <h3 class="text-lg text-blue-700">{{ seoData.metaTitle || t('seoConfig.sampleTitle') }}</h3>
            <p class="text-sm text-green-700">{{ fullCategoryUrl }}</p>
            <p class="text-sm text-gray-600">{{ seoData.metaDescription || t('seoConfig.sampleDescription') }}</p>
          </div>
        </div>
        <div class="space-y-2 mb-4">
          <IluriaInputTags 
            :label="t('seoConfig.keywords')"
            id="metaKeywords"
            v-model="seoData.metaKeywords"
            :placeholder="t('seoConfig.keywordsPlaceholder')"
            class="w-full"
          />
          <p class="text-xs text-gray-500">{{ t('seoConfig.keywordsHint') }}</p>
        </div>
        <div class="space-y-2 mb-4">
        <div class="flex items-center mb-2">
          <div class="flex items-center text-sm text-gray-500">
            <span class="text-lg text-black mr-2">{{ t('seoConfig.useCategoryImage') }}</span>
          </div>
          <IluriaToggleSwitch 
            v-model="useCategoryImageForSeo"
            @update:modelValue="toggleSeoImageSource"
            class="mt-2" 
            />
          </div>
        </div>
        <IluriaFileUpload
          id="seo-image-uploader"
          v-if="!useCategoryImageForSeo"
          ref="seoImageUploadRef"
          :label="t('seoConfig.seoImage')"
          :existingImages="seoData.seoImageUrl ? [{ id: 'seoImage', url: seoData.seoImageUrl }] : []"
          :maxFiles="1"
          @select="(event) => onImageSelect(event, 'seoImage')"
          @error="onImageUploadError"
          @remove-image="removeCategoryImage"
        />
      </div>
      <div class="space-y-2 mb-4"></div>
        <div class="flex items-center space-x-2">
          <Checkbox
            v-model="seoData.enableSEOIndexation" 
            inputId="enableSEOIndexation"
            :binary="true"
          />
          <label for="enableSEOIndexation" class="text-sm font-medium text-gray-700">
            {{ t('seoConfig.allowIndexing') }}
          </label>
        </div>
    </ViewContainer>
  </Form>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { FloppyDiskIcon } from '@hugeicons-pro/core-bulk-rounded'
import ViewContainer from '../../components/layout/ViewContainer.vue'
import { useToast } from '@/services/toast.service'
import { categoryService } from '@/services/category.service'
import imageService from '@/services/image.service'
import { Form } from '@primevue/forms'
import IluriaToggleSwitch from '../../components/iluria/IluriaToggleSwitch.vue'
import { IluriaInputText, IluriaInputTags, IluriaFileUpload } from '@/components/iluria/form'
import Checkbox from 'primevue/checkbox'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { z } from 'zod'
import { requiredText } from '@/services/validation.service'

const isSaving = ref(false);
const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const { addToast } = useToast();

const categoryImageUploader = ref(null);
const categoryIconUploader = ref(null);
const seoImageUploadRef = ref(null);

const selectedCategoryImage = ref(null);
const selectedCategoryIcon = ref(null);
const selectedSeoImage = ref(null);

const categoryId = ref(route.params.id)
const useCategoryImageForSeo = ref(true) 
const categoryName = ref(route.query.name || '');
const seoData = ref({
    storeId: '',
    displayTitle: categoryName.value || '',
    displayDescription: '',
    metaTitle: categoryName.value || '',
    metaDescription: '',
    categoryUrl: '',
    metaKeywords: [],
    enableSEOIndexation: true,
    displayImage: false,
    displayIcon: false,
    displaySEOImage: false,
    displayImageUrl: null,
    displayIconUrl: null,
    seoImageUrl: null
})

const props = defineProps({
  id: {
    type: String,
    required: false
  }
})


const resolver = zodResolver(
  z.object({
    displayTitle: requiredText(t('seoConfig.categoryTitle')),
    displayDescription: requiredText(t('seoConfig.categoryDescription')),
    categoryTitle: requiredText(t('seoConfig.categoryTitle')),
    categoryDescription: requiredText(t('seoConfig.categoryDescription')),
    categoryUrl: requiredText(t('seoConfig.categoryUrl')),
    metaTitle: requiredText(t('seoConfig.metaTitle')),
    keywords: requiredText(t('seoConfig.keywords')),
  })
)

const formatCategoryUrl = () => {
  if (seoData.value.categoryUrl) {
    seoData.value.categoryUrl = seoData.value.categoryUrl.replace(/\s+/g, '-');
  }
}

const fullCategoryUrl = computed(() => {
    const basePath = (t('seoConfig.basePath'))
    const slug = seoData.value.categoryUrl 
        ? seoData.value.categoryUrl
        : categoryName.value 
            ? categoryName.value.toLowerCase().replace(/\s+/g, '-')
            : categoryId.value
    return `${basePath}${slug}`
})

const loadCategoryImages = () => {
  if (!categoryId.value) return;
  if (seoData.value.displayImage) {
    seoData.value.displayImageUrl = imageService.getCategoryImageUrl(
      seoData.value.storeId, 
      categoryId.value, 
      'displayImage'
    );
  } else {
    seoData.value.displayImageUrl = null;
  }
  
  if (seoData.value.displayIcon) {
    seoData.value.displayIconUrl = imageService.getCategoryImageUrl(
      seoData.value.storeId, 
      categoryId.value, 
      'displayIcon'
    );
  } else {
    seoData.value.displayIconUrl = null;
  }
  
  if (seoData.value.displaySEOImage) {
    seoData.value.seoImageUrl = imageService.getCategoryImageUrl(
      seoData.value.storeId, 
      categoryId.value, 
      'displaySEOImage'
    );
  } else {
    seoData.value.seoImageUrl = null;
  }
};

const loadSeoData = async () => {
  if (!categoryId.value) return
  
  try {
    const response = await categoryService.getSeoData(categoryId.value);
    
    if (response) {
      seoData.value = {
        ...seoData.value,
        storeId: response.storeId,
        displayTitle: response.displayTitle || '',
        displayDescription: response.displayDescription || '',
        metaTitle: response.metaTitle || '',
        metaDescription: response.metaDescription || '',
        categoryUrl: response.categoryUrl || '',
        metaKeywords: Array.isArray(response.metaKeywords) ? response.metaKeywords : [],
        enableSEOIndexation: !!response.enableSEOIndexation,
        displayImage: !!response.displayImage,
        displayIcon: !!response.displayIcon,
        displaySEOImage: !!response.displaySEOImage,
      };
      

      loadCategoryImages();
    }
  } catch (error) {
    console.error('Error loading SEO data:', error);
  }
}

const saveSeoData = async () => {
  isSaving.value = true;
  try {
    const seoDataToSend = {
      displayTitle: seoData.value.displayTitle || '',
      displayDescription: seoData.value.displayDescription || '',
      displayImage: !!seoData.value.displayImage,
      displayIcon: !!seoData.value.displayIcon,
      displaySEOImage: !!seoData.value.displaySEOImage,
      metaTitle: seoData.value.metaTitle || '',
      metaDescription: seoData.value.metaDescription || '',
      categoryUrl: seoData.value.categoryUrl || '',
      metaKeywords: Array.isArray(seoData.value.metaKeywords) 
        ? seoData.value.metaKeywords 
        : [],
      enableSEOIndexation: !!seoData.value.enableSEOIndexation
    };
    
    await categoryService.saveSeoData(categoryId.value, seoDataToSend);

    const uploadPromises = [];
    let mainCategoryImage = null;

    if (selectedCategoryImage.value) {
      mainCategoryImage = selectedCategoryImage.value;
      uploadPromises.push(
        uploadImage(selectedCategoryImage.value, 'displayImage')
      );
    } else if (categoryImageUploader.value?.files?.length > 0) {
      mainCategoryImage = categoryImageUploader.value.files[0];
      uploadPromises.push(
        uploadImage(categoryImageUploader.value.files[0], 'displayImage')
      );
    }
    
    if (selectedCategoryIcon.value) {
      uploadPromises.push(
        uploadImage(selectedCategoryIcon.value, 'displayIcon')
      );
    } else if (categoryIconUploader.value?.files?.length > 0) {
      uploadPromises.push(
        uploadImage(categoryIconUploader.value.files[0], 'displayIcon')
      );
    }
    
    if (useCategoryImageForSeo.value) {
      if (mainCategoryImage) {
        uploadPromises.push(
          uploadImage(mainCategoryImage, 'seoImage')
        );
      }
    } else {
      if (selectedSeoImage.value) {
        uploadPromises.push(
          uploadImage(selectedSeoImage.value, 'seoImage')
        );
      } else if (seoImageUploadRef.value?.files?.length > 0) {
        uploadPromises.push(
          uploadImage(seoImageUploadRef.value.files[0], 'seoImage')
        );
      }
    }
    
    if (uploadPromises.length > 0) {
      await Promise.all(uploadPromises);
    }
    
    addToast(t('seoConfig.saveSuccess'), 'success');
  } catch (error) {
    addToast(t('seoConfig.saveError'), 'error');
  } finally {
    isSaving.value = false;
    router.push('/product/category-manager');
  }
};

function toggleSeoImageSource(value) {
  useCategoryImageForSeo.value = value;
  
  if (value) {
    selectedSeoImage.value = null;

    if (seoImageUploadRef.value && typeof seoImageUploadRef.value.clear === 'function') {
      seoImageUploadRef.value.clear();
    }
  }
}

async function uploadImage(file, imageType) {
  try {
    if (!['displayImage', 'displayIcon', 'seoImage'].includes(imageType)) {
      throw new Error(`Invalid image type: ${imageType}`);
    }

    const response = await categoryService.uploadImage(categoryId.value, file, imageType);
    
    if (imageType === 'displayImage') {
      seoData.value.displayImage = true;
    } else if (imageType === 'displayIcon') {
      seoData.value.displayIcon = true;
    } else if (imageType === 'seoImage') {
      seoData.value.displaySEOImage = true;
    }
    
    const imageUrl = imageService.getCategoryImageUrl(
      seoData.value.storeId,
      categoryId.value,
      imageType
    );
    
    seoData.value[`${imageType}Url`] = imageUrl;
    
    if (response && response.imageId) {
      const imageIdField = `${imageType}Id`;
      seoData.value[imageIdField] = response.imageId;
    }
    
    if (imageType === 'displayImage') {
      selectedCategoryImage.value = null;

      if (categoryImageUploader.value && typeof categoryImageUploader.value.clear === 'function') {
        categoryImageUploader.value.clear();
      }
    } else if (imageType === 'displayIcon') {
      selectedCategoryIcon.value = null;

      if (categoryIconUploader.value && typeof categoryIconUploader.value.clear === 'function') {
        categoryIconUploader.value.clear();
      }
    } else if (imageType === 'seoImage') {
      selectedSeoImage.value = null;
      
      if (seoImageUploadRef.value && typeof seoImageUploadRef.value.clear === 'function') {
        seoImageUploadRef.value.clear();
      }
    }
    
    return true;
  } catch (error) {
    console.error(`Error uploading ${imageType}:`, error);
    throw error;
  }
}

function onImageSelect(event, imageTypeParam) {
  if (event.files && event.files.length > 0) {
    if (imageTypeParam === 'seoImage') {
      selectedSeoImage.value = event.files[0];
    } else if (imageTypeParam === 'displayIcon') {
      selectedCategoryIcon.value = event.files[0];
    } else if (imageTypeParam === 'displayImage') {
      selectedCategoryImage.value = event.files[0];
    }
  }
}

function onImageUploadError(event) {
}

function removeCategoryImage(imageType) {
  if (categoryId.value) {
    categoryService.deleteImage(categoryId.value, imageType)
      .then(() => {
        if (imageType === 'displayImage') {
          seoData.value.displayImageUrl = null;
          seoData.value.displayImage = false;
          seoData.value.displayImageId = null;
        } else if (imageType === 'displayIcon') {
          seoData.value.displayIconUrl = null;
          seoData.value.displayIcon = false;
          seoData.value.displayIconId = null;
        } else if (imageType === 'seoImage') {
         seoData.value.seoImageUrl = null;
          seoData.value.displaySEOImage = false;
          seoData.value.seoImageId = null;
        }
      })
      .catch(error => {});
  }
}

onMounted(() => {
  loadSeoData()
})
</script>
