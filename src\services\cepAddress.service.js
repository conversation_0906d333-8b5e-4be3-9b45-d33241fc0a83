import axios from 'axios';
import { API_URLS } from '@/config/api.config';

class CEPAddressService {
    #baseUrl = `${API_URLS.CEP_ADDRESS_BASE_URL}`;

    async getCEPAddress(cep) {
        try {
            const response = await axios.get(`${this.#baseUrl}/${cep}`, {
                validateStatus: function (status) {
                    return status < 500 || status === 500;
                }
            });
            if (response.status === 404 || response.status === 400 || response.status === 500) {
                return null;
            }
            
            return response.data;
        } catch (error) {
            console.error('Unexpected error fetching CEP address:', error);
            return null;
        }
    }
}

export default new CEPAddressService();