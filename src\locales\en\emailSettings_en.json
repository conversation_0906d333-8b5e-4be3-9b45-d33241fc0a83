{"title": "Contact Email", "subtitle": "Manage your primary contact email", "currentEmailLabel": "Current Email", "newEmailLabel": "New Email", "newEmailPlaceholder": "Enter your new email", "currentPasswordLabel": "Current Password", "currentPasswordPlaceholder": "Enter your current password to confirm", "submitButton": "Change Email", "notLoggedIn": "Error: user not logged in", "invalidEmail": "Invalid email", "sameEmail": "New email must be different from current", "invalidPassword": "Incorrect current password", "changeError": "Error changing email", "unexpectedError": "Unexpected error changing email", "noEmailData": "Error: email data not found", "mfaSendError": "Error sending MFA code", "mfaVerificationError": "Error verifying MFA code", "mfaResendError": "Error resending MFA code", "verificationError": "Error verifying new email", "verificationSent": "Verification code sent to new email", "emailChanged": "Email changed successfully!", "sessionsInvalidated": "Other sessions have been disconnected for security. You remain logged in on this session.", "codeSent": "MFA code sent to your email", "codeResent": "MFA code resent successfully", "cancelled": "Email change cancelled", "verificationCancelled": "Email verification cancelled", "restartProcess": "To resend the code, you need to restart the email change process", "confirmTitle": "Confirm Your New Email", "confirmInfoText": "A verification code has been sent to {email}. Enter the code below to confirm your email change.", "oldEmailLabel": "Current Email", "codeLabel": "Verification Code", "codePlaceholder": "Enter the 6-digit code", "verifyButton": "Verify and Change Email", "resendText": "Didn't receive the code?", "resendButton": "Resend code", "resendCooldown": "Resend in {seconds}s", "warningText": "After confirming the change, all other sessions will be terminated for security. You will remain logged in on this session.", "pendingRequestFound": "A pending email change request was found. Please check your new email for verification.", "resendError": "Error resending verification code"}