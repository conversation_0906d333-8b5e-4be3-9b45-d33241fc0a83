<template>
  <div class="statement-editor">
    <!-- Abas -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { 'active': activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Conte<PERSON>do das Abas -->
    <div class="tab-content">
      <!-- Aba: Conteúdo -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        <div class="form-group">
          <IluriaLabel>Texto do Comunicado</IluriaLabel>
          <textarea
            v-model="formData.text"
            @input="applyChanges"
            placeholder="Digite o texto do comunicado..."
            rows="3"
            class="iluria-textarea"
          />
        </div>

        <div class="form-group">
          <IluriaCheckBox
            v-model="formData.showButton"
            @update:modelValue="applyChanges"
          >
            <PERSON>rar Botão
          </IluriaCheckBox>
        </div>

        <div v-if="formData.showButton" class="button-section">
          <div class="form-group">
            <IluriaLabel>Texto do Botão</IluriaLabel>
            <IluriaInputText
              v-model="formData.buttonText"
              @update:modelValue="applyChanges"
              placeholder="Ex: Saiba mais, Ver ofertas..."
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Link do Botão</IluriaLabel>
            <IluriaInputText
              v-model="formData.buttonUrl"
              @update:modelValue="applyChanges"
              type="url"
              placeholder="https://exemplo.com"
            />
          </div>
        </div>
      </div>

      <!-- Aba: Design -->
      <div v-if="activeTab === 'design'" class="tab-panel">
        <div class="form-group">
          <IluriaLabel>Cor de Fundo</IluriaLabel>
          <IluriaColorPicker
            v-model="formData.backgroundColor"
            @update:modelValue="applyChanges"
          />
        </div>

        <div class="form-group">
          <IluriaLabel>Cor do Texto</IluriaLabel>
          <IluriaColorPicker
            v-model="formData.textColor"
            @update:modelValue="applyChanges"
          />
        </div>

        <div v-if="formData.showButton" class="form-group">
          <IluriaLabel>Cor do Botão</IluriaLabel>
          <IluriaColorPicker
            v-model="formData.buttonColor"
            @update:modelValue="applyChanges"
          />
        </div>

        <div v-if="formData.showButton" class="form-group">
          <IluriaLabel>Cor do Texto do Botão</IluriaLabel>
          <IluriaColorPicker
            v-model="formData.buttonTextColor"
            @update:modelValue="applyChanges"
          />
        </div>

        <div v-if="formData.showButton" class="form-group">
          <IluriaLabel>Posição do Botão</IluriaLabel>
          <IluriaSelect 
            v-model="formData.buttonPosition" 
            :options="buttonPositionOptions"
            @update:modelValue="applyChanges"
          />
        </div>

        <div v-if="formData.showButton" class="button-style-section">
          <h5 class="subsection-title">Estilização do Botão</h5>
          
          <div class="form-group">
            <IluriaLabel>Raio da Borda</IluriaLabel>
            <IluriaRange 
              v-model="formData.buttonBorderRadius" 
              :min="0" 
              :max="50"
              :step="1"
              unit="px"
              @update:modelValue="applyChanges"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Padding do Botão</IluriaLabel>
            <div class="padding-controls">
              
                <label>Vertical</label>
                <IluriaRange 
                  v-model="formData.buttonPaddingY" 
                  :min="8" 
                  :max="32"
                  :step="1"
                  unit="px"
                  @update:modelValue="applyChanges"
                />
     
            
                <label>Horizontal</label>
                <IluriaRange 
                  v-model="formData.buttonPaddingX" 
                  :min="12" 
                  :max="60"
                  :step="1"
                  unit="px"
                  @update:modelValue="applyChanges"
                />
              
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>Tamanho da Fonte</IluriaLabel>
            <IluriaRange 
              v-model="formData.buttonFontSize" 
              :min="10" 
              :max="20"
              :step="1"
              unit="px"
              @update:modelValue="applyChanges"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>Peso da Fonte</IluriaLabel>
            <IluriaSelect 
              v-model="formData.buttonFontWeight" 
              :options="fontWeightOptions"
              @update:modelValue="applyChanges"
            />
          </div>

          <div class="form-group">
            <IluriaCheckBox
              v-model="formData.buttonHoverEffect"
              @update:modelValue="applyChanges"
            >
              Efeito Hover
            </IluriaCheckBox>
          </div>
        </div>

        <div class="form-group">
          <IluriaLabel>Alinhamento</IluriaLabel>
          <IluriaSelect 
            v-model="formData.textAlign" 
            :options="alignmentOptions"
            @update:modelValue="applyChanges"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'close'])

// Estado da aba ativa
const activeTab = ref('content')

// Abas disponíveis
const tabs = computed(() => [
  { value: 'content', label: 'Conteúdo' },
  { value: 'design', label: 'Design' }
])

// Opções para o select de alinhamento
const alignmentOptions = [
  { label: 'Esquerda', value: 'left' },
  { label: 'Centro', value: 'center' },
  { label: 'Direita', value: 'right' }
]

// Opções para posição do botão
const buttonPositionOptions = [
  { label: 'Inline com o texto', value: 'inline' },
  { label: 'Embaixo do texto', value: 'below' }
]

// Opções para peso da fonte
const fontWeightOptions = [
  { label: 'Normal', value: 'normal' },
  { label: 'Médio', value: '500' },
  { label: 'Semi-bold', value: '600' },
  { label: 'Bold', value: 'bold' }
]

// Estado do formulário
const formData = ref({
  text: '🎉 Oferta especial! Aproveite até 50% OFF em produtos selecionados por tempo limitado.',
  showButton: true,
  buttonText: 'Ver ofertas',
  buttonUrl: '',
  backgroundColor: '#3b82f6',
  textColor: '#ffffff',
  buttonColor: '#ffffff',
  buttonTextColor: '#3b82f6',
  textAlign: 'center',
  buttonPosition: 'below',
  buttonBorderRadius: 6,
  buttonPaddingY: 12,
  buttonPaddingX: 24,
  buttonFontSize: 14,
  buttonFontWeight: '600',
  buttonHoverEffect: true
})

// Encontrar o elemento statement
const findStatementElement = () => {
  let targetElement = props.element
  
  if (!targetElement) return null
  
  // Se já é o elemento statement
  if (targetElement.getAttribute('data-component') === 'statement') {
    return targetElement
  }
  
  // Procurar o elemento pai
  const parentStatement = targetElement.closest('[data-component="statement"]')
  if (parentStatement) return parentStatement
  
  // Se tem a classe iluria-statement
  if (targetElement.classList.contains('iluria-statement')) {
    return targetElement
  }
  
  // Procurar descendente
  const childStatement = targetElement.querySelector('[data-component="statement"], .iluria-statement')
  if (childStatement) return childStatement
  
  return null
}

// Carregar dados do elemento
const loadData = () => {
  const element = findStatementElement()
  if (!element) return
  
  try {
    // Carregar texto
    const textElement = element.querySelector('.statement-text')
    if (textElement) {
      formData.value.text = textElement.textContent || formData.value.text
    }
    
    // Carregar dados do botão
    const buttonElement = element.querySelector('.statement-button')
    if (buttonElement) {
      formData.value.showButton = buttonElement.style.display !== 'none'
      formData.value.buttonText = buttonElement.textContent || formData.value.buttonText
    } else {
      formData.value.showButton = false
    }
    
    // Carregar URL do botão
    const buttonUrl = element.getAttribute('data-button-url')
    if (buttonUrl) {
      formData.value.buttonUrl = buttonUrl
    }
    
    // Carregar estilos
    const computedStyle = getComputedStyle(element)
    formData.value.backgroundColor = rgbToHex(computedStyle.backgroundColor) || formData.value.backgroundColor
    formData.value.textAlign = computedStyle.textAlign || formData.value.textAlign
    
    if (textElement) {
      const textStyle = getComputedStyle(textElement)
      formData.value.textColor = rgbToHex(textStyle.color) || formData.value.textColor
    }
    
    if (buttonElement) {
      const buttonStyle = getComputedStyle(buttonElement)
      formData.value.buttonColor = rgbToHex(buttonStyle.backgroundColor) || formData.value.buttonColor
      formData.value.buttonTextColor = rgbToHex(buttonStyle.color) || formData.value.buttonTextColor
      formData.value.buttonBorderRadius = parseInt(buttonStyle.borderRadius) || formData.value.buttonBorderRadius
      formData.value.buttonFontSize = parseInt(buttonStyle.fontSize) || formData.value.buttonFontSize
      formData.value.buttonFontWeight = buttonStyle.fontWeight || formData.value.buttonFontWeight
      
      // Extrair padding
      const paddingTop = parseInt(buttonStyle.paddingTop) || formData.value.buttonPaddingY
      const paddingLeft = parseInt(buttonStyle.paddingLeft) || formData.value.buttonPaddingX
      formData.value.buttonPaddingY = paddingTop
      formData.value.buttonPaddingX = paddingLeft
      
      // Verificar se tem efeito hover
      formData.value.buttonHoverEffect = buttonElement.hasAttribute('onmouseover')
    }
    
    // Verificar posicionamento do botão
    const contentElement = element.querySelector('.statement-content')
    if (contentElement) {
      const contentStyle = getComputedStyle(contentElement)
      formData.value.buttonPosition = contentStyle.display === 'flex' ? 'inline' : 'below'
    }
    
  } catch (error) {
    console.error('Erro ao carregar dados:', error)
  }
}

// Aplicar mudanças no elemento
const applyChanges = () => {
  const element = findStatementElement()
  if (!element) {
    console.error('Elemento statement não encontrado')
    return
  }
  
  try {
    // Atualizar texto
    const textElement = element.querySelector('.statement-text')
    if (textElement) {
      textElement.textContent = formData.value.text
      textElement.style.color = formData.value.textColor
    }
    
    // Atualizar botão
    const buttonElement = element.querySelector('.statement-button')
    if (buttonElement) {
      if (formData.value.showButton) {
        buttonElement.style.display = 'inline-block'
        buttonElement.textContent = formData.value.buttonText
        buttonElement.style.backgroundColor = formData.value.buttonColor
        buttonElement.style.color = formData.value.buttonTextColor
        buttonElement.style.borderRadius = `${formData.value.buttonBorderRadius}px`
        buttonElement.style.padding = `${formData.value.buttonPaddingY}px ${formData.value.buttonPaddingX}px`
        buttonElement.style.fontSize = `${formData.value.buttonFontSize}px`
        buttonElement.style.fontWeight = formData.value.buttonFontWeight
        
        // Aplicar efeito hover se ativado
        if (formData.value.buttonHoverEffect) {
          buttonElement.style.transition = 'all 0.2s ease'
          buttonElement.setAttribute('onmouseover', 
            `this.style.transform='translateY(-2px)'; this.style.boxShadow='0 4px 12px rgba(0,0,0,0.15)'`
          )
          buttonElement.setAttribute('onmouseout', 
            `this.style.transform='translateY(0)'; this.style.boxShadow='none'`
          )
        } else {
          buttonElement.style.transition = 'none'
          buttonElement.removeAttribute('onmouseover')
          buttonElement.removeAttribute('onmouseout')
          buttonElement.style.transform = 'none'
          buttonElement.style.boxShadow = 'none'
        }
      } else {
        buttonElement.style.display = 'none'
      }
    }
    
    // Aplicar posicionamento do botão
    const contentElement = element.querySelector('.statement-content')
    if (contentElement && buttonElement) {
      if (formData.value.buttonPosition === 'inline') {
        // Inline: texto e botão na mesma linha
        contentElement.style.display = 'flex'
        contentElement.style.flexDirection = 'row'
        contentElement.style.alignItems = 'center'
        contentElement.style.gap = '16px'
        contentElement.style.justifyContent = formData.value.textAlign === 'center' ? 'center' : 
                                             formData.value.textAlign === 'right' ? 'flex-end' : 'flex-start'
        
        if (textElement) {
          textElement.style.margin = '0'
        }
      } else {
        // Below: botão embaixo do texto
        contentElement.style.display = 'block'
        contentElement.style.flexDirection = 'unset'
        contentElement.style.alignItems = 'unset'
        contentElement.style.gap = 'unset'
        contentElement.style.justifyContent = 'unset'
        
        if (textElement) {
          textElement.style.margin = '0 0 16px 0'
        }
      }
    }
    
    // Atualizar URL do botão
    element.setAttribute('data-button-url', formData.value.buttonUrl || '')
    
    // Atualizar estilos do container
    element.style.backgroundColor = formData.value.backgroundColor
    element.style.textAlign = formData.value.textAlign
    
    // Garantir que não há margens estranhas
    const elementContent = element.closest('.element-content')
    if (elementContent) {
      elementContent.style.padding = '0'
      elementContent.style.margin = '0'
    }
    
    // Garantir que o statement seja full-width
    element.style.margin = '0'
    element.style.width = '100%'
    element.style.boxSizing = 'border-box'
    
    // Emitir evento de atualização
    emit('data-changed', { element, data: formData.value })
    
  } catch (error) {
    console.error('Erro ao aplicar mudanças:', error)
  }
}

// Função utilitária para converter RGB para HEX
const rgbToHex = (rgb) => {
  if (!rgb || rgb === 'transparent' || rgb === 'rgba(0, 0, 0, 0)') return null
  
  if (rgb.startsWith('#')) return rgb
  
  const rgbMatch = rgb.match(/rgba?\((\d+),\s*(\d+),\s*(\d+)(?:,\s*[\d.]+)?\)/)
  if (rgbMatch) {
    const r = parseInt(rgbMatch[1])
    const g = parseInt(rgbMatch[2])
    const b = parseInt(rgbMatch[3])
    return `#${((1 << 24) + (r << 16) + (g << 8) + b).toString(16).slice(1)}`
  }
  
  return null
}

// Inicialização
onMounted(() => {
  loadData()
})
</script>

<style scoped>
.statement-editor {
  display: flex;
  flex-direction: column;
  height: 100%;
  background: var(--iluria-color-surface);
}

.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  overflow-x: auto;
  scrollbar-width: none;
  -ms-overflow-style: none;
}

.editor-tabs::-webkit-scrollbar {
  display: none;
}

.tab-button {
  display: flex;
  align-items: center;
  padding: 0.75rem 1rem;
  border: none;
  background: transparent;
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  cursor: pointer;
  white-space: nowrap;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.button-section {
  background: var(--iluria-color-container-bg);
  padding: 1rem;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.button-style-section {
  background: var(--iluria-color-surface);
  padding: 0.75rem;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  margin-top: 0.5rem;
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.subsection-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.padding-controls {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
  width: 100%;
}

.padding-input {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 0.5rem;
  justify-content: space-between;
}

.padding-input label {
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  min-width: 60px;
  flex-shrink: 0;
}

/* Textarea component */
.iluria-textarea {
  width: 100%;
  min-height: 80px;
  padding: 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text-primary);
  font-family: inherit;
  font-size: 0.875rem;
  line-height: 1.5;
  resize: vertical;
  transition: all 0.2s ease;
}

.iluria-textarea::placeholder {
  color: var(--iluria-color-text-secondary);
  opacity: 0.7;
}

.iluria-textarea:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 3px var(--iluria-color-primary-bg);
}

.iluria-textarea:hover:not(:focus) {
  border-color: var(--iluria-color-primary-border);
}

/* Responsive */
@media (max-width: 768px) {
  .tab-panel {
    padding: 0.75rem;
  }
  
  .button-section {
    padding: 0.75rem;
  }
  
  .button-style-section {
    padding: 0.75rem;
  }
  
  .padding-controls {
    gap: 0.5rem;
  }
}

/* Scrollbar */
.tab-content::-webkit-scrollbar {
  width: 6px;
}

.tab-content::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.tab-content::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.tab-content::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-secondary);
}
</style> 