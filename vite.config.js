import { fileURLToPath, URL } from "node:url";
import { defineConfig, loadEnv } from "vite";
import { resolve } from "path";
import vue from "@vitejs/plugin-vue";
import vueDevTools from "vite-plugin-vue-devtools";
import tailwindcss from "@tailwindcss/vite";

export default defineConfig(({ command, mode }) => {
  const env = loadEnv(mode, process.cwd(), "");

  return {
    root: "src",
    plugins: [vue(), tailwindcss(), vueDevTools()],
    resolve: {
      alias: {
        "@": fileURLToPath(new URL("./src", import.meta.url)),
      },
    },

    define: {
      "process.env": env,
      global: "globalThis",
    },

    server: {
      proxy: {
        '/api/storefront': {
          target: 'http://localhost:8080',
          changeOrigin: true,
          configure: (proxy, options) => {
            proxy.on('proxyReq', (proxyReq, req, res) => {
              // Extrair storeId do contexto do frontend e criar domínio temporário
              const storeId = req.headers['x-store-id'];
              if (storeId) {
                // Criar domínio temporário no formato esperado pelo StorefrontApiFilter
                const tempDomain = `loja.${storeId.toLowerCase()}.iluria.com`;
                proxyReq.setHeader('Host', tempDomain);
                
              }
            });
          }
        }
      }
    },

    build: {
      outDir: resolve(__dirname, "dist"),
      rollupOptions: {
        input: {
          root: resolve(__dirname, "src/index.html"),
        },
      },
    },
  };
});
