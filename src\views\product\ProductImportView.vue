<template>
  <div class="product-import-container">
    <!-- Header com ações -->
    <IluriaHeader
      :title="t('product.import.title')"
      :subtitle="t('product.import.subtitle')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="step === 2"
      :saveText="t('product.import.startImport')"
      :saveIcon="Upload01Icon"
      @cancel-click="goBack"
      @save-click="openConfirm"
    />

    <!-- Tabs Navigation -->
    <div class="tabs-container">
      <div class="tabs-nav">
        <IluriaButton
          v-for="tab in tabs"
          :key="tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
          :color="activeTab === tab.id ? 'primary' : 'ghost'"
          :variant="activeTab === tab.id ? 'solid' : 'ghost'"
          :hugeIcon="tab.icon"
          size="medium"
          @click="activeTab = tab.id"
        >
          {{ t(tab.label) }}
        </IluriaButton>
      </div>
    </div>

    <!-- Tab Content: Import Process -->
    <div v-if="activeTab === 'import'" class="tab-content">
      <!-- Step 1: File Selection -->
      <div v-if="step === 1" class="import-form-container">
      <ViewContainer
        :title="t('product.import.fileSelection')"
        :subtitle="t('product.import.fileSelectionDescription')"
        :icon="File01Icon"
        icon-color="blue"
      >
        <div class="space-y-6">
          <!-- File Upload Area -->
          <div class="file-upload-area" :class="{ 'has-file': selectedFile }">
            <input
              ref="fileInput"
              type="file"
              accept=".csv"
              @change="handleFileSelect"
              class="hidden"
            />

            <div v-if="!selectedFile" class="upload-prompt">
              <HugeiconsIcon :icon="CloudUploadIcon" :size="48" class="upload-icon" />
              <h3 class="upload-title">{{ t('product.import.selectFile') }}</h3>
              <p class="upload-description">{{ t('product.import.selectFileDescription') }}</p>
              <IluriaButton color="primary" @click="$refs.fileInput.click()">
                {{ t('product.import.chooseFile') }}
              </IluriaButton>
            </div>

            <div v-else class="file-selected">
              <div class="file-info">
                <HugeiconsIcon :icon="File01Icon" :size="32" class="file-icon" />
                <div class="file-details">
                  <h4 class="file-name">{{ selectedFile.name }}</h4>
                  <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
              </div>
              <IluriaButton color="outline" @click="removeFile">
                {{ t('product.import.removeFile') }}
              </IluriaButton>
            </div>
          </div>

          <!-- File Validation Errors -->
          <div v-if="fileErrors.length > 0" class="error-list">
            <div v-for="error in fileErrors" :key="error" class="error-item">
              <HugeiconsIcon :icon="AlertCircleIcon" :size="16" />
              <span>{{ error }}</span>
            </div>
          </div>

          <!-- Next Button -->
          <div class="step-actions">
            <IluriaButton
              color="primary"
              :disabled="!selectedFile || fileErrors.length > 0"
              :loading="parsingHeaders"
              @click="parseFileHeaders"
            >
              {{ t('product.import.nextStep') }}
            </IluriaButton>
          </div>
        </div>
      </ViewContainer>
    </div>

    <!-- Step 2: Field Mapping -->
    <div v-if="step === 2" class="import-form-container">
      <ViewContainer
        :title="t('product.import.fieldMapping')"
        :subtitle="t('product.import.fieldMappingDescription')"
        :icon="CheckmarkSquare03Icon"
        icon-color="purple"
      >
        <!-- Loading State -->
        <div v-if="!availableFields.basic.length && !availableFields.variations.length" class="loading-fields">
          <div class="loading-spinner"></div>
          <span>{{ t('product.import.loadingFields') }}</span>
        </div>
        <div class="space-y-6">
          <!-- Status Summary -->
          <div v-if="mappedFieldsCount > 0 || csvHeaders.length > validCsvHeaders.length" class="mapping-summary">
            <div v-if="mappedFieldsCount > 0" class="summary-item success">
              <HugeiconsIcon :icon="Tick01Icon" :size="16" />
              <span>{{ mappedFieldsCount }}/{{ validCsvHeaders.length }} campos mapeados</span>
            </div>
            <div v-if="csvHeaders.length > validCsvHeaders.length" class="summary-item info">
              <HugeiconsIcon :icon="InformationCircleIcon" :size="16" />
              <span>{{ csvHeaders.length - validCsvHeaders.length }} campos sem dados válidos foram ocultados</span>
            </div>
          </div>

          <!-- CSV Preview -->
          <div class="csv-preview">
            <div class="preview-header">
              <h4 class="preview-title">{{ t('product.import.csvPreview') }}</h4>
              <div class="preview-info">
                <HugeiconsIcon :icon="AttachmentIcon" :size="16" />
                <span>{{ csvAnalysis.columnCount }} {{ t('product.import.columns') }} • {{ (csvPreviewRows || []).length }} linhas</span>
              </div>
            </div>
            <div class="preview-table-wrapper">
              <IluriaDataTable
                :value="paginatedPreviewData"
                :columns="previewColumns"
                :loading="parsingHeaders"
                :show-header="true"
                class="preview-datatable"
              >
                <!-- Template para células com texto longo -->
                <template v-for="header in csvHeaders" :key="header" #[`column-${header}`]="{ data }">
                  <div
                    class="cell-content"
                    :title="data[header] && String(data[header]).length > 30 ? data[header] : ''"
                  >
                    {{ data[header] || '-' }}
                  </div>
                </template>
              </IluriaDataTable>
            </div>
            <div v-if="(csvPreviewRows || []).length > pageSize" class="preview-pagination-wrapper">
              <IluriaPagination
                :current-page="currentPage"
                :total-pages="totalPages"
                @go-to-page="handlePageChange"
                class="preview-pagination"
              />
            </div>
          </div>

          <!-- Field Mapping -->
          <div class="field-mapping">
            <div class="mapping-header">
              <h4 class="mapping-title">{{ t('product.import.mapFields') }}</h4>
              <div class="mapping-status-badge">
                <span class="status-text">{{ mappedFieldsCount }}/{{ validCsvHeaders.length }} {{ t('product.import.fieldsMapped') }}</span>
              </div>
            </div>

            <div class="mapping-list">
              <div v-for="header in validCsvHeaders" :key="header" class="mapping-item">
                <div class="csv-field">
                  <div class="field-icon-wrapper">
                    <HugeiconsIcon :icon="getFieldIcon('csv')" :size="20" class="field-icon" />
                  </div>
                  <div class="field-info">
                    <span class="field-name">{{ header }}</span>
                  </div>
                </div>

                <div class="mapping-arrow">
                  <HugeiconsIcon :icon="ArrowRight01Icon" :size="18" />
                </div>

                <div class="system-field">
                  <div class="select-wrapper">
                    <div class="select-icon-wrapper">
                      <HugeiconsIcon
                        :icon="getFieldIcon(fieldMapping[header])"
                        :size="20"
                        class="select-icon"
                      />
                    </div>
                    <IluriaSelect
                      v-model="fieldMapping[header]"
                      :options="getFieldOptions(header)"
                      :loading="!availableFields.basic.length && !availableFields.variations.length"
                      :placeholder="isIgnoredField(header) ? t('product.import.fieldIgnored') : t('product.import.selectField')"
                      class="field-select"
                      :disabled="!hasValidData(header) || isIgnoredField(header)"
                      appendTo="body"
                    />
                  </div>
                  <div class="mapped-check-wrapper" :class="{
                    'has-error': !hasValidData(header) && !isIgnoredField(header),
                    'is-ignored': isIgnoredField(header)
                  }">
                    <HugeiconsIcon
                      v-if="hasValidData(header) && fieldMapping[header] && !isIgnoredField(header)"
                      :icon="Tick01Icon"
                      :size="20"
                      class="mapped-check success"
                    />
                    <HugeiconsIcon
                      v-else-if="isIgnoredField(header)"
                      :icon="InformationCircleIcon"
                      :size="20"
                      class="mapped-check ignored"
                    />
                    <HugeiconsIcon
                      v-else-if="!hasValidData(header)"
                      :icon="Cancel01Icon"
                      :size="20"
                      class="mapped-check error"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>

          <!-- Step Actions -->
          <div class="step-actions">
            <IluriaButton color="outline" @click="goBackToFileSelection">
              {{ t('product.import.backToFile') }}
            </IluriaButton>
          </div>
        </div>
      </ViewContainer>

      <!-- Preview dos Dados Mapeados -->
      <ViewContainer
        v-if="mappedFieldsCount > 0"
        :title="t('product.import.mappingPreview')"
        :subtitle="t('product.import.howWillBeSaved')"
        :icon="Database01Icon"
        icon-color="green"
      >
        <template #header-actions>
          <div class="mapping-stats">
            <span class="stat-badge success">
              <HugeiconsIcon :icon="CheckmarkCircle01Icon" :size="14" />
              {{ mappedFieldsCount }} campos mapeados
            </span>
            <span class="stat-badge info">
              <HugeiconsIcon :icon="ViewIcon" :size="14" />
              {{ Math.min(paginatedPreviewData.length, 3) }} {{ t('product.import.exampleRecords') }}
            </span>
          </div>
        </template>

        <!-- Preview Unificado -->
        <div class="unified-preview-container">
          <div class="preview-cards">
            <div v-for="(row, index) in paginatedPreviewData.slice(0, 3)" :key="index" class="product-preview-card">
              <div class="card-header">
                <HugeiconsIcon :icon="PackageIcon" :size="18" />
                <span class="card-title">{{ t('product.import.product') }} {{ index + 1 }}</span>
              </div>

              <div class="card-content">
                <!-- Dados Básicos -->
                <div v-if="hasBasicMappings" class="data-section">
                  <div class="section-header">
                    <HugeiconsIcon :icon="PackageIcon" :size="16" />
                    <h6 class="section-title">{{ t('product.import.productData') }}</h6>
                  </div>
                  <div class="field-grid">
                    <div v-for="field in basicFields" :key="field.value" class="field-item">
                      <div v-if="getPreviewValue(row, field.value)" class="field-row">
                        <div class="field-label">
                          <HugeiconsIcon :icon="getFieldIcon(field.value)" :size="14" />
                          {{ field.label }}:
                        </div>
                        <div class="field-value">{{ getPreviewValue(row, field.value) }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Dados de Variações -->
                <div v-if="hasVariationMappings" class="data-section">
                  <div class="section-header">
                    <HugeiconsIcon :icon="LayersIcon" :size="16" />
                    <h6 class="section-title">{{ t('product.import.productVariations') }}</h6>
                  </div>
                  <div class="field-grid">
                    <div v-for="field in variationFields" :key="field.value" class="field-item">
                      <div class="field-row">
                        <div class="field-label">
                          <HugeiconsIcon :icon="getFieldIcon(field.value)" :size="14" />
                          {{ field.label }}:
                        </div>
                        <div class="field-value">
                          {{ getPreviewValue(row, field.value) || '-' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ViewContainer>
    </div>
    </div>

    <!-- Tab Content: Guide -->
    <div v-if="activeTab === 'guide'" class="tab-content">
      <div class="guide-container">
        <!-- Preparação do CSV -->
        <ViewContainer
          :title="t('product.import.guide.csvPreparation')"
          :subtitle="t('product.import.guide.csvPreparationDescription')"
          :icon="FileEditIcon"
          icon-color="blue"
        >
          <div class="guide-section">
            <div class="guide-cards">
              <div class="guide-card">
                <div class="guide-card-header">
                  <HugeiconsIcon :icon="Table01Icon" :size="20" />
                  <h4>{{ t('product.import.guide.csvStructure') }}</h4>
                </div>
                <div class="guide-card-content">
                  <p>{{ t('product.import.guide.csvStructureDescription') }}</p>
                  <div class="code-example">
                    <pre>name,sku,description,price,cost,stock
Produto A,PROD-001,Descrição do produto,19.99,10.00,100
Produto B,PROD-002,Outro produto,29.99,15.00,50</pre>
                  </div>
                </div>
              </div>

              <div class="guide-card">
                <div class="guide-card-header">
                  <HugeiconsIcon :icon="CheckmarkCircle01Icon" :size="20" />
                  <h4>{{ t('product.import.guide.bestPractices') }}</h4>
                </div>
                <div class="guide-card-content">
                  <ul class="guide-list">
                    <li>{{ t('product.import.guide.practice1') }}</li>
                    <li>{{ t('product.import.guide.practice2') }}</li>
                    <li>{{ t('product.import.guide.practice3') }}</li>
                    <li>{{ t('product.import.guide.practice4') }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </ViewContainer>

        <!-- Mapeamento de Campos -->
        <ViewContainer
          :title="t('product.import.guide.fieldMapping')"
          :subtitle="t('product.import.guide.fieldMappingDescription')"
          :icon="ConnectIcon"
          icon-color="purple"
        >
          <div class="guide-section">
            <div class="mapping-examples">
              <div class="mapping-example">
                <div class="example-header">
                  <HugeiconsIcon :icon="PackageIcon" :size="18" />
                  <h5>{{ t('product.import.guide.productFields') }}</h5>
                </div>
                <div class="field-examples">
                  <div class="field-example">
                    <span class="csv-example">nome</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Nome do Produto</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">sku</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">SKU</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">preco</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Preço</span>
                  </div>
                </div>
              </div>

              <div class="mapping-example">
                <div class="example-header">
                  <HugeiconsIcon :icon="LayersIcon" :size="18" />
                  <h5>{{ t('product.import.guide.variationFields') }}</h5>
                </div>
                <div class="field-examples">
                  <div class="field-example">
                    <span class="csv-example">cor</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Atributo: Cor</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">tamanho</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Atributo: Tamanho</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">estoque</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Quantidade em Estoque</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ViewContainer>
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import ProductImportService from '@/services/productImport.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Cancel01Icon,
  Upload01Icon,
  File01Icon,
  CloudUploadIcon,
  AlertCircleIcon,
  CheckmarkSquare03Icon,
  AttachmentIcon,
  Tick01Icon,
  InformationCircleIcon,
  Database01Icon,
  PackageIcon,
  LayersIcon,
  ArrowRight01Icon,
  CheckmarkCircle01Icon,
  ViewIcon,
  Location01Icon,
  Mail01Icon,
  TelephoneIcon,
  Calendar01Icon,
  LanguageCircleIcon,
  MarketingIcon,
  ToggleOnIcon,
  CsvIcon,
  FileEditIcon,
  Table01Icon,
  ConnectIcon,
  BookOpenIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

const activeTab = ref('import')

const tabs = [
  {
    id: 'import',
    label: 'product.import.tabs.import',
    icon: Upload01Icon
  },
  {
    id: 'guide',
    label: 'product.import.tabs.guide',
    icon: BookOpenIcon
  }
]

const step = ref(1)
const selectedFile = ref(null)
const fileErrors = ref([])
const parsingHeaders = ref(false)
const importing = ref(false)

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('')
const confirmModalCancelText = ref('')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

// CSV data
const csvHeaders = ref([])
const csvPreviewRows = ref([])
const fieldMapping = ref({})
const availableFields = ref({ basic: [], variations: [] })
const mappedProductsPreview = ref([])

// Pagination
const currentPage = ref(1)
const pageSize = ref(10)
const csvAnalysis = ref({
  hasExtraColumns: false,
  hasMissingColumns: false,
  columnCount: 0,
  recommendedColumns: []
})

// Computed
const canImport = computed(() => {
  return Object.values(fieldMapping.value).some(value => value !== '')
})

const mappedFieldsCount = computed(() => {
  return Object.values(fieldMapping.value).filter(value => value && value.trim() !== '').length
})

// Headers filtrados - apenas campos com dados válidos
const validCsvHeaders = computed(() => {
  return csvHeaders.value.filter(header => hasValidData(header))
})

// Preview table columns
const previewColumns = computed(() => {
  return csvHeaders.value.map(header => {
    return {
      field: header,
      class: 'text-left column-normal-text',
      style: 'width: 120px; max-width: 120px; min-width: 100px;'
    }
  })
})

// Pagination computed properties
const totalPages = computed(() => {
  return Math.ceil((csvPreviewRows.value || []).length / pageSize.value)
})

const paginatedPreviewData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  return (csvPreviewRows.value || []).slice(start, end).map(row => {
    const rowData = {}
    csvHeaders.value.forEach((header, index) => {
      rowData[header] = row[index] || '-'
    })
    return rowData
  })
})

// Preview de dados mapeados
const hasVariationMappings = computed(() => {
  return Object.values(fieldMapping.value).some(field =>
    field && availableFields.value.variations.includes(field)
  )
})

const hasBasicMappings = computed(() => {
  return Object.values(fieldMapping.value).some(field =>
    field && availableFields.value.basic.includes(field)
  )
})

// Campos básicos para preview
const basicFields = computed(() => {
  return [
    { value: 'name', label: t('product.import.fields.name') },
    { value: 'sku', label: t('product.import.fields.sku') },
    { value: 'description', label: t('product.import.fields.description') },
    { value: 'price', label: t('product.import.fields.price') },
    { value: 'cost', label: t('product.import.fields.cost') },
    { value: 'stock', label: t('product.import.fields.stock') },
    { value: 'status', label: t('product.import.fields.status') },
    { value: 'category', label: t('product.import.fields.category') },
    { value: 'tags', label: t('product.import.fields.tags') }
  ]
})

// Campos de variação para preview
const variationFields = computed(() => {
  return [
    { value: 'color', label: t('product.import.fields.color') },
    { value: 'size', label: t('product.import.fields.size') },
    { value: 'material', label: t('product.import.fields.material') },
    { value: 'variationPrice', label: t('product.import.fields.variationPrice') },
    { value: 'variationStock', label: t('product.import.fields.variationStock') },
    { value: 'variationSku', label: t('product.import.fields.variationSku') }
  ]
})

// Método para obter ícone do campo
const getFieldIcon = (fieldType) => {
  const iconMap = {
    // Campos CSV
    'csv': CsvIcon,

    // Campos básicos
    'name': PackageIcon,
    'sku': File01Icon,
    'description': File01Icon,
    'price': Mail01Icon,
    'cost': Mail01Icon,
    'stock': PackageIcon,
    'status': ToggleOnIcon,
    'category': PackageIcon,
    'tags': PackageIcon,

    // Campos de variação
    'color': LayersIcon,
    'size': LayersIcon,
    'material': LayersIcon,
    'variationPrice': Mail01Icon,
    'variationStock': PackageIcon,
    'variationSku': File01Icon
  }

  return iconMap[fieldType] || File01Icon
}

// Função para obter valor do preview baseado no mapeamento
const getPreviewValue = (row, fieldType) => {
  // Primeiro, verificar se há um campo CSV mapeado diretamente para este fieldType
  const directCsvField = Object.keys(fieldMapping.value).find(key => fieldMapping.value[key] === fieldType)

  if (directCsvField && row[directCsvField]) {
    const value = row[directCsvField]

    // Formatação especial para alguns campos
    if (fieldType === 'status') {
      return value === 'true' || value === '1' || value === 'ativo' ? 'Ativo' : 'Inativo'
    }

    if (fieldType === 'price' || fieldType === 'cost' || fieldType === 'variationPrice') {
      return new Intl.NumberFormat('pt-BR', {
        style: 'currency',
        currency: 'BRL'
      }).format(value)
    }

    return value
  }

  return null
}

// Methods
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

const processFile = (file) => {
  selectedFile.value = file
  fileErrors.value = ProductImportService.validateCSVFile(file)
}

const removeFile = () => {
  selectedFile.value = null
  fileErrors.value = []
  csvHeaders.value = []
  csvPreviewRows.value = []
  fieldMapping.value = {}
  currentPage.value = 1
  step.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
}

const formatFileSize = (bytes) => {
  return ProductImportService.formatFileSize(bytes)
}

const getFieldDisplayName = (fieldName) => {
  return ProductImportService.getFieldDisplayName(fieldName)
}

// Função para verificar se um campo deve ser ignorado (não mapeado)
const isIgnoredField = (header) => {
  const ignoredFields = ['creationdate', 'created_at', 'data_criacao', 'datacriacao']
  return ignoredFields.includes(header.toLowerCase().trim())
}

// Função para verificar se um campo tem dados válidos no CSV
const hasValidData = (header) => {
  if (!csvHeaders.value || csvHeaders.value.length === 0) return false

  const headerIndex = csvHeaders.value.indexOf(header)
  if (headerIndex === -1) return false

  // Se não há dados de preview, considera o campo válido se existe no header
  if (!csvPreviewRows.value || csvPreviewRows.value.length === 0) return true

  // Campos estruturais importantes são sempre considerados válidos
  const cleanHeader = header.replace(/^"(.*)"$/, '$1').toLowerCase().trim()
  const structuralFields = [
    'name', 'nome', 'produto',
    'sku', 'codigo', 
    'description', 'descricao',
    'price', 'preco', 'valor',
    'stock', 'estoque', 'quantidade',
    'costprice', 'custo',
    'weight', 'peso',
    'type', 'status', 'ativo',
    'shortdescription',
    'originalprice',
    'stockquantitytotal',
    'boxlength', 'boxwidth', 'boxdepth',
    'hasvariation', 'highlight', 'newtag',
    'barcode', 'suppliername', 'supplierlink', 'suppliernotes',
    'metatitle', 'metadescription', 'createdat', 'updatedat'
  ]
  
  const isStructuralField = structuralFields.includes(cleanHeader) || 
                           structuralFields.some(sf => cleanHeader.includes(sf))

  // Verifica se pelo menos 1 linha tem dados não nulos/vazios para este campo
  const hasData = csvPreviewRows.value.some(row => {
    const value = row[headerIndex]
    return value !== null && value !== undefined && value !== '' && String(value).trim() !== ''
  })
  
  // Se é campo estrutural ou tem dados, considera válido
  return hasData || isStructuralField
}

// Função para obter opções de campos filtradas
const getFieldOptions = (csvHeader) => {
  // Se o campo CSV não tem dados válidos, não mostrar opções
  if (!hasValidData(csvHeader)) {
    return []
  }

  const options = []

  // Adicionar opção vazia
  options.push({
    label: 'Selecione um campo...',
    value: ''
  })

  // Campos básicos
  const basicFields = availableFields.value.basic || []
  if (basicFields.length > 0) {
    const basicOptions = basicFields
      .filter(field => field && field.trim() !== '')
      .map(field => ({
        label: getFieldDisplayName(field),
        value: field
      }))
    
    if (basicOptions.length > 0) {
      basicOptions.forEach(option => {
        options.push(option)
      })
    }
  }

  // Campos de variação
  const variationFields = availableFields.value.variations || []
  if (variationFields.length > 0) {
    const variationOptions = variationFields
      .filter(field => field && field.trim() !== '')
      .map(field => ({
        label: `🔀 ${getFieldDisplayName(field)}`,
        value: field
      }))
    
    variationOptions.forEach(option => {
      options.push(option)
    })
  }

  return options
}

const parseFileHeaders = async () => {
  if (!selectedFile.value) return
  
  try {
    parsingHeaders.value = true
    
    const result = await ProductImportService.parseCSVHeaders(selectedFile.value)
    csvHeaders.value = result.headers
    csvPreviewRows.value = result.previewRows
    
    // Initialize field mapping first
    fieldMapping.value = {}
    csvHeaders.value.forEach(header => {
      fieldMapping.value[header] = ''
    })
    
    // Analyze CSV structure and auto-map fields
    analyzeCsvStructure()

    step.value = 2
    
  } catch (error) {
    console.error('Error parsing CSV headers:', error)
    toast.showError(t('product.import.parseError'))
  } finally {
    parsingHeaders.value = false
  }
}

const analyzeCsvStructure = () => {
 
  
  const expectedColumns = ['name', 'sku', 'price', 'stock']
  
  csvAnalysis.value.columnCount = csvHeaders.value.length
  csvAnalysis.value.hasExtraColumns = csvHeaders.value.length > expectedColumns.length
  csvAnalysis.value.hasMissingColumns = csvHeaders.value.length < expectedColumns.length
  
  // Mapeamento inteligente usando mapa de equivalências
  const fieldMappingRules = {
    // Mapeamento exato em inglês
    'name': 'name',
    'sku': 'sku', 
    'description': 'description',
    'shortdescription': 'shortDescription',
    'price': 'price',
    'originalprice': 'originalPrice',
    'costprice': 'costPrice',
    'stock': 'stock',
    'stockquantitytotal': 'stockQuantityTotal',
    'weight': 'weight',
    'boxlength': 'boxLength',
    'boxwidth': 'boxWidth',
    'boxdepth': 'boxDepth',
    'type': 'type',
    'status': 'status',
    'hasvariation': 'hasVariation',
    'highlight': 'highlight',
    'newtag': 'newTag',
    'barcode': 'barCode',
    'suppliername': 'supplierName',
    'supplierlink': 'supplierLink',
    'suppliernotes': 'supplierNotes',
    'metatitle': 'metaTitle',
    'metadescription': 'metaDescription',
    'createdat': 'createdAt',
    'updatedat': 'updatedAt',
    
    // Mapeamento em português
    'nome': 'name',
    'produto': 'name',
    'codigo': 'sku',
    'descricao': 'description',
    'preco': 'price',
    'valor': 'price',
    'custo': 'costPrice',
    'estoque': 'stock',
    'quantidade': 'stock',
    'peso': 'weight',
    'ativo': 'status',
    
    // Campos de variação - novos campos padronizados
    'variation_id': 'variation_id',
    'variationid': 'variation_id',
    'id_variacao': 'variation_id',
    'idvariacao': 'variation_id',
    
    'variation_sku': 'variation_sku',
    'variationsku': 'variation_sku',
    'sku_variacao': 'variation_sku',
    'skuvariacao': 'variation_sku',
    
    'variation_price': 'variation_price',
    'variationprice': 'variation_price',
    'preco_variacao': 'variation_price',
    'precovariacao': 'variation_price',
    
    'variation_originalprice': 'variation_originalPrice',
    'variationoriginalprice': 'variation_originalPrice',
    'preco_original_variacao': 'variation_originalPrice',
    'precooriginalvariacao': 'variation_originalPrice',
    
    'variation_costprice': 'variation_costPrice',
    'variationcostprice': 'variation_costPrice',
    'preco_custo_variacao': 'variation_costPrice',
    'precocustovariacao': 'variation_costPrice',
    
    'variation_stock': 'variation_stock',
    'variationstock': 'variation_stock',
    'estoque_variacao': 'variation_stock',
    'estoquevariacao': 'variation_stock',
    
    'variation_weight': 'variation_weight',
    'variationweight': 'variation_weight',
    'peso_variacao': 'variation_weight',
    'pesovariacao': 'variation_weight',
    
    'variation_boxlength': 'variation_boxLength',
    'variationboxlength': 'variation_boxLength',
    'comprimento_variacao': 'variation_boxLength',
    'comprimentovariacao': 'variation_boxLength',
    
    'variation_boxwidth': 'variation_boxWidth',
    'variationboxwidth': 'variation_boxWidth',
    'largura_variacao': 'variation_boxWidth',
    'larguravariacao': 'variation_boxWidth',
    
    'variation_boxdepth': 'variation_boxDepth',
    'variationboxdepth': 'variation_boxDepth',
    'profundidade_variacao': 'variation_boxDepth',
    'profundidadevariacao': 'variation_boxDepth',
    
    'variation_attributes': 'variation_attributes',
    'variationattributes': 'variation_attributes',
    'atributos_variacao': 'variation_attributes',
    'atributosvariacao': 'variation_attributes',
    'attributes_variacao': 'variation_attributes',
    'attributesvariacao': 'variation_attributes'
  }
  
  // Auto-mapear campos usando regras
  let mappedCount = 0
  csvHeaders.value.forEach(header => {
    // Remove aspas duplas e normaliza
    const cleanHeader = header.replace(/^"(.*)"$/, '$1').toLowerCase().trim()
   
    // Busca mapeamento direto
    if (fieldMappingRules[cleanHeader]) {
      const mappedField = fieldMappingRules[cleanHeader]
      fieldMapping.value[header] = mappedField
      mappedCount++
     
    } 
    // Busca por substring para casos complexos
    else {
      Object.keys(fieldMappingRules).forEach(rule => {
        if (cleanHeader.includes(rule) && !fieldMapping.value[header]) {
          const mappedField = fieldMappingRules[rule]
          fieldMapping.value[header] = mappedField
          mappedCount++
         
        }
      })
    }
  })
  
 
}

const goBackToFileSelection = () => {
  step.value = 1
}

const startImport = async () => {
  if (!selectedFile.value) return
  
  try {
    importing.value = true

    await ProductImportService.createImport(selectedFile.value, fieldMapping.value)

    toast.showSuccess(
      t('product.import.importStartedMessage'),
      {
        title: t('product.import.importStarted'),
        duration: 5000
      }
    )

    // Navegar para lista de produtos
    router.push('/products')
    
  } catch (error) {
    console.error('Error starting import:', error)

    let errorMessage = t('product.import.importError')

    if (error.response?.data?.errors?.length > 0) {
      errorMessage = error.response.data.errors[0].message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    toast.showError(errorMessage, {
      title: t('product.import.importError'),
      duration: 8000
    })
  } finally {
    importing.value = false
  }
}

const goBack = () => {
  router.push('/products')
}

const openConfirm = () => {
  confirmModalMessage.value = t('product.import.confirmMessage')
  confirmModalTitle.value = t('product.import.confirmTitle')
  confirmModalConfirmText.value = t('product.import.startImport')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'info'
  confirmCallback.value = startImport
  showConfirmModal.value = true
}

// Modal control functions
const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const loadAvailableFields = async () => {
  try {
    const fields = await ProductImportService.getAvailableFields()
    
    // Garantir estrutura válida
    availableFields.value = {
      basic: fields?.basic || [
        'name', 'sku', 'description', 'price', 'cost', 'stock', 'status', 'category', 'tags'
      ],
      variations: fields?.variations || [
        'color', 'size', 'material', 'variationPrice', 'variationStock', 'variationSku'
      ]
    }
    
  } catch (error) {
    console.error('Error loading available fields:', error)
    // Fallback robusto para garantir funcionamento
    availableFields.value = {
      basic: [
        'name', 'sku', 'description', 'price', 'cost', 'stock', 'status', 'category', 'tags'
      ],
      variations: [
        'color', 'size', 'material', 'variationPrice', 'variationStock', 'variationSku'
      ]
    }
  }
}

// Lifecycle
onMounted(() => {
  loadAvailableFields()
})
</script>

<style scoped>
.product-import-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Import Form Container */
.import-form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Tabs */
.tabs-container {
  margin-bottom: 24px;
}

.tabs-nav {
  display: flex;
  gap: 4px;
  background: var(--iluria-color-surface-secondary);
  padding: 4px;
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
}

.tab-button {
  flex: 1;
  justify-content: center;
}

/* Remove box-shadow dos botões de tabs para evitar conflito com o design */
.tabs-nav .tab-button.btn {
  box-shadow: none !important;
}

.tabs-nav .tab-button.btn:active {
  transform: none !important;
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* File Upload Area */
.file-upload-area {
  border: 2px dashed var(--iluria-color-border);
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.3s ease;
  background: var(--iluria-color-surface-secondary);
}

.file-upload-area.has-file {
  border-style: solid;
  border-color: var(--iluria-color-success);
  background: rgba(34, 197, 94, 0.05);
}

.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  color: var(--iluria-color-text-secondary);
}

.upload-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.upload-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.file-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--iluria-color-surface-primary);
  border-radius: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  color: var(--iluria-color-primary);
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.file-size {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Error List */
.error-list {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 16px;
}

.error-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc2626;
  font-size: 14px;
  margin-bottom: 8px;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* CSV Preview */
.csv-preview {
  margin-bottom: 24px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  background: var(--iluria-color-surface-secondary);
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
}

.preview-table-wrapper {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  background: var(--iluria-color-surface-primary);
  margin-bottom: 16px;
  overflow-x: auto;
}

.preview-datatable {
  border: none;
  border-radius: 0;
  min-width: 100%;
}

/* Field Mapping */
.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.mapping-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.mapping-status-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 12px;
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mapping-item {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 16px;
  align-items: center;
  padding: 16px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
}

.csv-field,
.system-field {
  display: flex;
  align-items: center;
  gap: 12px;
}

.field-icon-wrapper,
.select-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--iluria-color-surface-primary);
  border-radius: 6px;
  color: var(--iluria-color-text-secondary);
}

.field-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.select-wrapper {
  display: flex;
  align-items: center;
  gap: 8px;
  flex: 1;
}

.field-select {
  flex: 1;
}

.mapped-check-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
}

.mapped-check.success {
  color: var(--iluria-color-success);
}

.mapped-check.ignored {
  color: var(--iluria-color-text-secondary);
}

.mapped-check.error {
  color: var(--iluria-color-danger);
}

.mapping-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-tertiary);
}

/* Preview Cards */
.product-preview-card {
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  margin-bottom: 16px;
}

.card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--iluria-color-surface-secondary);
  border-bottom: 1px solid var(--iluria-color-border);
}

.card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.data-section {
  padding: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.data-section:last-child {
  border-bottom: none;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
}

.section-title {
  font-size: 12px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  margin: 0;
}

.field-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.field-row {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 6px 0;
  font-size: 12px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 6px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.field-value {
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

/* Step Actions */
.step-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid var(--iluria-color-border);
}

/* Loading */
.loading-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Summary and Stats */
.mapping-summary {
  display: flex;
  gap: 12px;
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  border-radius: 8px;
  font-size: 12px;
  font-weight: 500;
}

.summary-item.success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.summary-item.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.mapping-stats {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stat-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
}

.stat-badge.success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.stat-badge.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Guide Styles */
.guide-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.guide-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.guide-card {
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
}

.guide-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px;
  background: var(--iluria-color-surface-secondary);
  border-bottom: 1px solid var(--iluria-color-border);
}

.guide-card-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.guide-card-content {
  padding: 16px;
}

.code-example {
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 12px;
  margin-top: 12px;
}

.code-example pre {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  color: var(--iluria-color-text-primary);
  margin: 0;
  white-space: pre-wrap;
}

.guide-list {
  margin: 0;
  padding-left: 20px;
  color: var(--iluria-color-text-primary);
}

.guide-list li {
  margin-bottom: 8px;
  font-size: 14px;
}

.mapping-examples {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.mapping-example {
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
}

.example-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.example-header h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.field-examples {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-example {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 0;
}

.csv-example {
  font-family: 'Courier New', monospace;
  background: var(--iluria-color-surface-secondary);
  padding: 4px 8px;
  border-radius: 4px;
  font-size: 12px;
  color: var(--iluria-color-text-primary);
  min-width: 100px;
}

.system-example {
  font-size: 12px;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

@media (max-width: 768px) {
  .mapping-item {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .mapping-arrow {
    transform: rotate(90deg);
  }

  .guide-cards {
    grid-template-columns: 1fr;
  }

  .field-grid {
    grid-template-columns: 1fr;
  }
}
</style>