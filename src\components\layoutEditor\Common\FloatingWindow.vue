<template>
  <div 
    v-if="visible"
    class="floating-window"
    :class="{ 'is-dragging': isDragging }"
    :style="windowStyles"
  >
    <div 
      class="window-header" 
      @mousedown="startDrag"
      :title="$t('layoutEditor.dragToMove')"
    >
      <div class="header-content">
        <div class="drag-handle">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 8h16M4 16h16"/>
          </svg>
        </div>
        <h3 class="window-title">{{ title }}</h3>
      </div>
      <button v-if="closeable" @click="$emit('close')" class="close-button">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
      </button>
    </div>

    <div class="window-content">
      <slot></slot>
    </div>

    <div v-if="$slots.footer" class="window-footer">
      <slot name="footer"></slot>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onUnmounted, watch, onMounted } from 'vue'

const props = defineProps({
  title: {
    type: String,
    required: true
  },
  width: {
    type: Number,
    default: 400
  },
  maxWidth: {
    type: Number,
    default: null // null = sem limite específico, usa o cálculo automático
  },
  minHeight: {
    type: Number,
    default: 300
  },
  maxHeight: {
    type: Number,
    default: null // null = sem limite específico, usa o cálculo automático
  },
  position: {
    type: Object,
    required: true,
    validator: (value) => {
      return typeof value.x === 'number' && typeof value.y === 'number'
    }
  },
  visible: {
    type: Boolean,
    default: true
  },
  closeable: {
    type: Boolean,
    default: true
  },
  responsive: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close', 'update:position'])

// Estado para controle do drag
const isDragging = ref(false)
const dragOffset = ref({ x: 0, y: 0 })
const windowPosition = ref({ x: 0, y: 0 })
const viewportSize = ref({ width: 0, height: 0 })

// Atualiza tamanho do viewport
const updateViewportSize = () => {
  viewportSize.value = {
    width: window.innerWidth,
    height: window.innerHeight
  }
}

// Calcula dimensões responsivas
const responsiveDimensions = computed(() => {
  const viewport = viewportSize.value
  
  // Largura responsiva
  let calculatedWidth = props.width
  let calculatedMaxWidth = props.maxWidth
  
  if (props.responsive) {
    // Em telas pequenas, usar 90% da largura
    if (viewport.width <= 768) {
      calculatedWidth = Math.min(props.width, viewport.width * 0.9)
      calculatedMaxWidth = viewport.width * 0.95
    } else if (viewport.width <= 1024) {
      // Em tablets, usar no máximo 70% da largura
      calculatedMaxWidth = props.maxWidth || viewport.width * 0.7
    } else {
      // Em desktops, usar no máximo 50% da largura (reduzido de 60%)
      calculatedMaxWidth = props.maxWidth || viewport.width * 0.5
    }
  }
  
  // Altura responsiva - MUITO mais restritiva
  let calculatedMaxHeight = props.maxHeight
  
  if (props.responsive) {
    // Altura máxima muito menor - nunca maior que 65% do viewport
    calculatedMaxHeight = props.maxHeight || Math.min(
      viewport.height * 0.65,  // Máximo 65% da altura da tela
      650  // Altura absoluta máxima de 650px
    )
  }
  
  // Aplicar limites
  calculatedWidth = Math.min(calculatedWidth, calculatedMaxWidth || calculatedWidth)
  
  return {
    width: calculatedWidth,
    maxWidth: calculatedMaxWidth,
    minHeight: Math.min(props.minHeight, 400), // Altura mínima máxima de 400px
    maxHeight: calculatedMaxHeight
  }
})

// Estilos computados da janela
const windowStyles = computed(() => {
  const dimensions = responsiveDimensions.value
  const viewport = viewportSize.value
  
  let styles = {
    width: `${dimensions.width}px`,
    minHeight: `${dimensions.minHeight}px`
  }
  
  // Adicionar altura máxima se definida
  if (dimensions.maxHeight) {
    styles.maxHeight = `${dimensions.maxHeight}px`
  }
  
  // Adicionar largura máxima se definida
  if (dimensions.maxWidth) {
    styles.maxWidth = `${dimensions.maxWidth}px`
  }
  
  // Posicionamento responsivo
  if (props.responsive && viewport.width <= 768) {
    // Em mobile, centralizar
    styles.left = '50%'
    styles.top = '50%'
    styles.transform = 'translate(-50%, -50%)'
  } else {
    // Desktop - usar posição calculada
    styles.left = `${windowPosition.value.x}px`
    styles.top = `${windowPosition.value.y}px`
  }
  
  return styles
})

// Calcula posição segura (dentro dos limites da tela)
const getSafePosition = (x, y) => {
  const dimensions = responsiveDimensions.value
  const viewport = viewportSize.value
  
  // Margem mínima das bordas da tela
  const margin = 20
  
  const safeX = Math.max(
    margin, 
    Math.min(viewport.width - dimensions.width - margin, x)
  )
  
  const safeY = Math.max(
    margin, 
    Math.min(viewport.height - (dimensions.maxHeight || dimensions.minHeight) - margin, y)
  )
  
  return { x: safeX, y: safeY }
}

// Atualiza a posição inicial quando a prop position muda
watch(() => props.position, (newPos) => {
  if (!isDragging.value && props.responsive && viewportSize.value.width > 768) {
    const safePos = getSafePosition(newPos.x, newPos.y)
    windowPosition.value = safePos
  }
}, { immediate: true })

// Observa mudanças no viewport
watch(viewportSize, () => {
  if (!isDragging.value && props.responsive && viewportSize.value.width > 768) {
    // Reposiciona se necessário quando o viewport muda
    const safePos = getSafePosition(windowPosition.value.x, windowPosition.value.y)
    windowPosition.value = safePos
  }
}, { deep: true })

// Inicia o processo de arrastar
const startDrag = (e) => {
  // Não permitir drag em mobile
  if (props.responsive && viewportSize.value.width <= 768) return
  
  // Ignora se o clique foi em um botão ou seus filhos
  if (e.target.closest('button') || e.target.closest('.close-button')) return

  isDragging.value = true
  
  // Calcula o offset do mouse em relação à posição da janela
  dragOffset.value = {
    x: e.clientX - windowPosition.value.x,
    y: e.clientY - windowPosition.value.y
  }

  // Adiciona os event listeners
  window.addEventListener('mousemove', handleDrag)
  window.addEventListener('mouseup', stopDrag)
  
  // Previne seleção de texto durante o drag
  document.body.style.userSelect = 'none'
  document.body.style.cursor = 'move'
}

// Manipula o movimento durante o drag
const handleDrag = (e) => {
  if (!isDragging.value) return

  const newX = e.clientX - dragOffset.value.x
  const newY = e.clientY - dragOffset.value.y
  
  // Usa posição segura
  const safePos = getSafePosition(newX, newY)
  
  // Atualiza a posição
  windowPosition.value = safePos
  emit('update:position', safePos)
}

// Finaliza o processo de arrastar
const stopDrag = () => {
  isDragging.value = false
  
  // Remove os event listeners
  window.removeEventListener('mousemove', handleDrag)
  window.removeEventListener('mouseup', stopDrag)
  
  // Restaura o cursor e a seleção de texto
  document.body.style.userSelect = ''
  document.body.style.cursor = ''
}

// Inicialização
onMounted(() => {
  updateViewportSize()
  window.addEventListener('resize', updateViewportSize)
})

// Limpa os event listeners quando o componente é destruído
onUnmounted(() => {
  window.removeEventListener('mousemove', handleDrag)
  window.removeEventListener('mouseup', stopDrag)
  window.removeEventListener('resize', updateViewportSize)
})
</script>

<style scoped>
.floating-window {
  position: fixed;
  z-index: 1000;
  background-color: var(--iluria-color-surface);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  border: 1px solid var(--iluria-color-border);
  transition: opacity 0.2s ease;
}

.floating-window.is-dragging {
  opacity: 0.95;
  transition: none;
}

.window-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem;
  background-color: var(--iluria-color-surface);
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: move;
  user-select: none;
  flex-shrink: 0;
}

.header-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.drag-handle {
  display: flex;
  align-items: center;
  color: var(--iluria-color-text-secondary);
}

.window-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.close-button {
  background: transparent;
  border: none;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  padding: 0.25rem;
  border-radius: 4px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.close-button:hover {
  color: var(--iluria-color-text-primary);
  background-color: var(--iluria-color-hover);
}

.window-content {
  flex: 1;
  overflow-y: auto;
  scrollbar-width: thin;
  scrollbar-color: var(--iluria-color-border) var(--iluria-color-surface);
  min-height: 0; /* Permite que o flex funcione corretamente */
}

.window-content::-webkit-scrollbar {
  width: 8px;
}

.window-content::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.window-content::-webkit-scrollbar-thumb {
  background-color: var(--iluria-color-border);
  border-radius: 4px;
}

.window-content::-webkit-scrollbar-thumb:hover {
  background-color: var(--iluria-color-border-hover);
}

.window-footer {
  flex-shrink: 0;
  border-top: 1px solid var(--iluria-color-border);
  background-color: var(--iluria-color-container-bg);
}

/* Responsividade melhorada */
@media (max-width: 768px) {
  .floating-window {
    position: fixed !important;
    top: 50% !important;
    left: 50% !important;
    transform: translate(-50%, -50%) !important;
    width: 95vw !important;
    height: 90vh !important;
    max-width: none !important;
    max-height: none !important;
    border-radius: 12px;
  }

  .window-header {
    padding: 1rem;
    cursor: default; /* Não permite drag em mobile */
  }
  
  .drag-handle {
    display: none; /* Esconde handle de drag em mobile */
  }
}

@media (max-width: 480px) {
  .floating-window {
    width: 100vw !important;
    height: 100vh !important;
    border-radius: 0 !important;
    top: 0 !important;
    left: 0 !important;
    transform: none !important;
  }
}

/* Melhorias para temas escuros */
@media (prefers-color-scheme: dark) {
  .window-content::-webkit-scrollbar-track {
    background: var(--iluria-color-container-bg);
  }
}
</style>
