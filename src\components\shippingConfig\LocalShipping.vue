<template>
  <div>
    <!-- Enable Local Shipping Label with Toggle -->
    <div class="flex items-start justify-between w-full mb-6">
      <div class="flex-1">
        <IluriaLabel>{{ t('shippingConfig.localShipping.enableLabel') }}</IluriaLabel>
        <IluriaText class="font-semibold">Oferecer opção personalizada de entrega</IluriaText>
      </div>
      <div class="flex justify-end">
        <IluriaToggleSwitch
          id="localShippingEnabled"
          name="localShippingEnabled"
          v-model="localShippingEnabled"
        />
      </div>
    </div>
    
    <!-- Description -->
    <div class="mb-6">
      <IluriaLabel>{{ t('shippingConfig.localShipping.description') }}</IluriaLabel>
    </div>
    
    <div class="space-y-6">
      
      <!-- Local Shipping Description -->
      <div v-if="localShippingEnabled" class="space-y-2">
        <div class="flex items-center gap-2">
          <IluriaText>Descrição:</IluriaText>
        </div>
        <div class="mt-2">
          <IluriaInputText
            id="localShippingDescription"
            name="localShippingDescription"
            type="text"
            placeholder="Ex: motoboy"
            v-model="localShippingDescription"
            :formContext="formContext?.localShippingDescription"
          />
        </div>
      </div>
      
      <!-- Upload File Section -->
      <div v-if="localShippingEnabled" class="space-y-2">
        <IluriaFileUpload
          id="localShippingRangesFile"
          name="localShippingRangesFile"
          accept=".csv"
          :multiple="false"
          chooseLabel="Escolher arquivo"
          emptyMessage=" "
          @select="onFileSelect"
          @error="onFileError"
          ref="fileUploadRef"
          :showPreviewBox="false"
        />
        
        <!-- Local Shipping Ranges Table -->
        <div v-if="localShippingRanges.length > 0" class="mt-6">
          <IluriaTitle>Faixas de CEP cadastradas</IluriaTitle>
          <IluriaDataTable :value="localShippingRanges"
            :columns="[
              { field: 'startZipcode', header: 'CEP inicial' },
              { field: 'endZipcode', header: 'CEP final' },
              { field: 'startWeight', header: 'Peso (kg)' },
              { field: 'price', header: 'Preço' },
              { field: 'deliveryTime', header: 'Prazo' }
            ]"
          >
            <template #column-startZipcode="{ data }">
              {{ formatZipcode(data.startZipcode) }}
            </template>
            <template #column-endZipcode="{ data }">
              {{ formatZipcode(data.endZipcode) }}
            </template>
            <template #column-startWeight="{ data }">
              {{ data.startWeight }} - {{ data.endWeight }} kg
            </template>
            <template #column-price="{ data }">
              {{ formatPrice(data.price) }}
            </template>
            <template #column-deliveryTime="{ data }">
              {{ data.deliveryTime }} {{ data.deliveryTime === 1 ? 'dia' : 'dias' }}
            </template>
          </IluriaDataTable>
        </div>
      </div>
      
      <!-- Regras de desconto Section -->
      <div v-if="localShippingEnabled" class="space-y-4">
        <IluriaTitle>Regras de desconto</IluriaTitle>
        
        <div class="discount-options-container">
          <IluriaRadioGroup
            id="localShippingDiscountType"
            name="localShippingDiscountType"
            label="Tipo de desconto"
            v-model="localShippingDiscountType"
            :options="discountOptions"
            option-label="label"
            option-value="value"
            direction="vertical"
          />
        </div>
        
        <!-- Campos fixos de valor mínimo e desconto -->
        <div class="mt-6">
          <div class="fields-container">
            <div class="form-fields-container">
              <div class="form-field">
                <IluriaInputText
                  id="localShippingMinimumValueDiscount"
                  name="localShippingMinimumValueDiscount"
                  type="money"
                  prefix="R$"
                  label="Valor mínimo do pedido"
                  v-model="localShippingMinimumValueDiscount"
                  :formContext="formContext?.localShippingMinimumValueDiscount"
                />
              </div>
              
              <div class="form-field">
                <IluriaInputText
                  id="localShippingPercentageDiscount"
                  name="localShippingPercentageDiscount"
                  type="number"
                  label="Desconto"
                  suffix="%"
                  v-model="displayDiscountValue"
                  @input="handleDiscountChange"
                  :formContext="formContext?.localShippingPercentageDiscount"
                  :disabled="localShippingDiscountType === 'FREE_SHIPPING_MININUM_VALUE'"
                  :readonly="localShippingDiscountType === 'FREE_SHIPPING_MININUM_VALUE'"
                />
              </div>
            </div>
            
            <!-- Option descriptions -->
            <div class="option-descriptions">
              <div v-for="option in discountOptions" :key="option.value" v-show="localShippingDiscountType === option.value">
                <p class="discount-description">{{ option.description }}</p>
              </div>
            </div>
            
            <!-- Free Shipping with Minimum Value Input -->
            <div v-if="localShippingDiscountType === 'FREE_SHIPPING_MININUM_VALUE'" class="conditional-input grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <IluriaInputText
                  id="localShippingMinimumValueDiscount"
                  name="localShippingMinimumValueDiscount"
                  type="money"
                  :label="t('shippingConfig.localShipping.minOrderValue')"
                  v-model="localShippingMinimumValueDiscount"
                  :formContext="formContext?.localShippingMinimumValueDiscount"
                  prefix="R$"
                />
              </div>
              
              <div>
                <IluriaInputText
                  id="localShippingDiscountPercentage"
                  name="localShippingDiscountPercentage"
                  type="number"
                  :label="t('shippingConfig.localShipping.discountPercentage')"
                  v-model="localShippingPercentageDiscount"
                  :formContext="formContext?.localShippingPercentageDiscount"
                  suffix="%"
                />
              </div>
            </div>
            
            <!-- Percentage Discount Inputs -->
            <div v-if="localShippingDiscountType === 'MININUM_VALUE'" class="conditional-input grid grid-cols-1 md:grid-cols-2 gap-4">
              <div>
                <IluriaInputText
                  id="localShippingMinimumValueDiscount"
                  name="localShippingMinimumValueDiscount"
                  type="money"
                  prefix="R$"
                  v-model="localShippingMinimumValueDiscount"
                  :label="t('shippingConfig.localShipping.minOrderValue')"
                  :formContext="formContext?.localShippingMinimumValueDiscount"
                />
              </div>
              
              <div>
                <IluriaInputText
                  id="localShippingPercentageDiscount"
                  name="localShippingPercentageDiscount"
                  type="number"
                  v-model="localShippingPercentageDiscount"
                  :label="t('shippingConfig.localShipping.discountPercentage')"
                  :formContext="formContext?.localShippingPercentageDiscount"
                  suffix="%"
                />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { requiredText } from '@/services/validation.service';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaFileUpload from '@/components/iluria/form/IluriaFileUpload.vue';
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
import shippingService from '@/services/shipping.service';
import { useToast } from '@/services/toast.service';
import IluriaText from '@/components/iluria/IluriaText.vue';
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue';
import IluriaTitle from '../iluria/IluriaTitle.vue';

const { t } = useI18n();
const toast = useToast();
const fileUploadRef = ref(null);

const discountOptions = computed(() => [
  {
    label: 'Sem desconto',
    value: 'NO_DISCOUNT'
  },
  {
    label: 'Entrega grátis',
    value: 'FREE_SHIPPING'
  },
  {
    label: 'Entrega grátis para pedidos acima de um valor',
    value: 'FREE_SHIPPING_MININUM_VALUE'
  },
  {
    label: 'Percentual de desconto',
    value: 'MININUM_VALUE'
  }
]);

const localShippingEnabled = defineModel('localShippingEnabled');
const localShippingDescription = defineModel('localShippingDescription');
const localShippingDiscountType = defineModel('localShippingDiscountType');
const localShippingMinimumValueDiscount = defineModel('localShippingMinimumValueDiscount');
const localShippingPercentageDiscount = defineModel('localShippingPercentageDiscount');
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

const localShippingRanges = ref([]);

const displayDiscountValue = ref('');

const originalDiscountValue = ref('');

const handleDiscountChange = (event) => {
  const value = event.target ? event.target.value : event;
  if (localShippingDiscountType.value !== 'FREE_SHIPPING_MININUM_VALUE') {
    localShippingPercentageDiscount.value = value;
  }
};

const getDiscountType = (data) => {
  if (!data) return 'NO_DISCOUNT';
  
  if (data.localShippingMinimumValueDiscount && data.localShippingPercentageDiscount === 100) {
    return 'FREE_SHIPPING_ABOVE_VALUE';
  } else if (data.localShippingPercentageDiscount === 100) {
    return 'FREE_SHIPPING';
  } else if (data.localShippingMinimumValueDiscount && data.localShippingPercentageDiscount > 0 && data.localShippingPercentageDiscount < 100) {
    return 'DISCOUNT_ABOVE_VALUE';
  } else {
    return 'NO_DISCOUNT';
  }
};

const syncDisplayValue = () => {
  if (localShippingDiscountType.value === 'FREE_SHIPPING_MININUM_VALUE') {
    displayDiscountValue.value = 100;
  } else {
    displayDiscountValue.value = localShippingPercentageDiscount.value || '';
  }
};


const validationRules = {
  localShippingDescription: requiredText('Descrição')
};

const validateAndSetDiscountType = () => {
  const hasMinimumValue = localShippingMinimumValueDiscount.value && localShippingMinimumValueDiscount.value > 0;
  const hasPercentageValue = localShippingPercentageDiscount.value && localShippingPercentageDiscount.value > 0;
  
  if (localShippingDiscountType.value === 'NO_DISCOUNT' && (hasMinimumValue || hasPercentageValue)) {
    localShippingDiscountType.value = 'MININUM_VALUE';
  }
};

defineExpose({ 
  validationRules,
  localShippingRanges,
  validateAndSetDiscountType
});

const onFileSelect = async (event) => {
  try {
    if (event.files && event.files.length > 0) {
      const file = event.files[0];
      const response = await shippingService.uploadLocalShippingRanges(file);
      localShippingRanges.value = response;
      toast.showSuccess(t('shippingConfig.localShipping.uploadSuccess'));
    }
  } catch (error) {
    console.error('Error uploading file:', error);
    toast.showError(t('shippingConfig.localShipping.uploadError'));
  }
};

const onFileError = (event) => {
  console.error('Error uploading file:', event);
  toast.showError(t('shippingConfig.localShipping.uploadError'));
};

const formatZipcode = (zipcode) => {
  return zipcode ? zipcode.replace(/^(\d{5})(\d{3})$/, '$1-$2') : '';
};

const formatPrice = (price) => {
  return price ? `R$ ${parseFloat(price).toFixed(2).replace('.', ',')}` : '';
};

watch(localShippingEnabled, async (newValue) => {
  if (newValue) {
    try {
      const ranges = await shippingService.getLocalShippingRanges();
      localShippingRanges.value = ranges;
    } catch (error) {
      console.error('Error loading shipping ranges:', error);
    }
  } else {
    localShippingRanges.value = [];
  }
}, { immediate: true });

watch(localShippingDiscountType, (newValue, oldValue) => {
  if (newValue === 'FREE_SHIPPING_MININUM_VALUE') {
    originalDiscountValue.value = localShippingPercentageDiscount.value || '';
    localShippingPercentageDiscount.value = 100;
  }
  else if (oldValue === 'FREE_SHIPPING_MININUM_VALUE') {
    localShippingPercentageDiscount.value = originalDiscountValue.value || '';
  }
  
  syncDisplayValue();
}, { immediate: true });

watch(localShippingPercentageDiscount, (newValue) => {
  syncDisplayValue();
});

watch([localShippingMinimumValueDiscount, localShippingPercentageDiscount, localShippingDiscountType], 
  ([minimumValue, percentage, discountType]) => {
  }, { immediate: true });

watch(localShippingMinimumValueDiscount, (newValue, oldValue) => {
}, { immediate: true });

watch([localShippingMinimumValueDiscount, localShippingPercentageDiscount], 
  ([minimumValue, percentageValue]) => {
    if (localShippingDiscountType.value === 'NO_DISCOUNT') {
      if ((minimumValue && minimumValue > 0) || (percentageValue && percentageValue > 0)) {
        localShippingDiscountType.value = 'MININUM_VALUE';
      }
    }
  }
);
</script>

<style scoped>
.discount-options-container {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.form-fields-container {
  display: flex;
  gap: 1rem;
  align-items: flex-start;
}

.form-field {
  flex: 1;
  min-width: 0;
}

.fields-container {
  background: #f8f9fa;
  border: 1px solid #e9ecef;
  border-radius: 8px;
  padding: 1.5rem;
}

@media (max-width: 768px) {
  .form-fields-container {
    flex-direction: column;
  }
  
  .fields-container {
    padding: 1rem;
  }
}
</style>
