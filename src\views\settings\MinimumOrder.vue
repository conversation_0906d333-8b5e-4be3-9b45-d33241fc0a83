<template>
  <div class="minimum-order-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('minimumOrder.title')"
      :subtitle="t('minimumOrder.subtitle')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="t('save')"
      @cancel-click="goBack"
      @save-click="saveSettings"
    />

    <!-- Main Content -->
    <div class="form-content">
      <ViewContainer 
        :title="t('minimumOrder.configurationTitle')"
        :subtitle="t('minimumOrder.configurationSubtitle')"
        :icon="ShoppingBasket01Icon"
        iconColor="blue"
      >
        <div class="settings-grid">
          <!-- Enable/Disable Toggle -->
          <div class="toggle-card">
            <div class="toggle-content">
              <div class="toggle-info">
                <h3 class="toggle-title">{{ t('minimumOrder.enableQuestion') }}</h3>
                <p class="toggle-description">{{ t('minimumOrder.enableDescription') }}</p>
              </div>
              <IluriaToggleSwitch
                id="enable-minimum-order"
                v-model="enableMinimumOrder"
                class="toggle-switch"
              />
            </div>
          </div>

          <!-- Minimum Value Input -->
          <div class="form-group" v-if="enableMinimumOrder">
            <IluriaInputText
              id="minimum-order-value"
              v-model="minimumOrderValue"
              :label="t('minimumOrder.valueLabel')"
              :placeholder="t('minimumOrder.valuePlaceholder')"
              type="money"
              prefix="R$"
              class="value-input"
            />
            <p class="help-text">{{ t('minimumOrder.valueHelp') }}</p>
          </div>

          <!-- Preview Section -->
          <div class="preview-section" v-if="enableMinimumOrder && minimumOrderValue">
            <div class="preview-card">
              <div class="preview-header">
                <ShoppingBasket01Icon class="preview-icon" />
                <span class="preview-title">{{ t('minimumOrder.previewTitle') }}</span>
              </div>
              <div class="preview-content">
                <p class="preview-text">
                  {{ t('minimumOrder.previewMessage', { value: formatCurrency(minimumOrderValue) }) }}
                </p>
              </div>
            </div>
          </div>
        </div>
      </ViewContainer>


    </div>
  </div>
</template>

<script setup>
import { ref } from 'vue';
import { useRouter } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useToast } from 'primevue/usetoast';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';

import {
  ShoppingBasket01Icon
} from '@hugeicons-pro/core-stroke-rounded';

const router = useRouter();
const { t } = useI18n();
const toast = useToast();

// Reactive state for form values
const enableMinimumOrder = ref(false);
const minimumOrderValue = ref('');
const saving = ref(false);

// Methods
const goBack = () => {
  router.back();
};

const formatCurrency = (value) => {
  if (!value) return 'R$ 0,00';
  const numValue = parseFloat(value);
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(numValue);
};

const saveSettings = async () => {
  try {
    saving.value = true;
    
    // Aqui você implementaria a lógica de salvamento
    // const settingsData = {
    //   enableMinimumOrder: enableMinimumOrder.value,
    //   minimumOrderValue: minimumOrderValue.value
    // };
    // await settingsService.saveMinimumOrder(settingsData);
    
    // Simular delay de salvamento
    await new Promise(resolve => setTimeout(resolve, 1000));
    
    toast.add({ 
      severity: 'success', 
      summary: t('success'), 
      detail: t('minimumOrder.saveSuccess'), 
      life: 3000 
    });
  } catch (error) {
    console.error('Erro ao salvar configurações:', error);
    toast.add({ 
      severity: 'error', 
      summary: t('error'), 
      detail: t('minimumOrder.saveError'), 
      life: 3000 
    });
  } finally {
    saving.value = false;
  }
};

// Load existing settings on mount
const loadSettings = async () => {
  try {
    // Aqui você implementaria a lógica de carregamento
    // const settings = await settingsService.getMinimumOrder();
    // enableMinimumOrder.value = settings.enableMinimumOrder || false;
    // minimumOrderValue.value = settings.minimumOrderValue || '';
  } catch (error) {
    console.error('Erro ao carregar configurações:', error);
  }
};

// Initialize
loadSettings();
</script>

<style scoped>
.minimum-order-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  
  min-height: 100vh;
}



/* Main Content */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Settings Grid */
.settings-grid {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-group {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

/* Toggle Card */
.toggle-card {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.toggle-card:hover {
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-sm);
}

.toggle-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 20px;
}

.toggle-info {
  flex: 1;
}

.toggle-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.toggle-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.5;
}

.toggle-switch {
  flex-shrink: 0;
}

/* Value Input */
.value-input {
  max-width: 300px;
}

.help-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Preview Section */
.preview-section {
  margin-top: 24px;
}

.preview-card {
  background: linear-gradient(135deg, var(--iluria-color-primary-50) 0%, var(--iluria-color-primary-25) 100%);
  border: 1px solid var(--iluria-color-primary-200);
  border-radius: 16px;
  padding: 24px;
  position: relative;
  overflow: hidden;
}

.preview-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--iluria-color-primary) 0%, var(--iluria-color-primary-600) 100%);
}

.preview-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.preview-icon {
  width: 24px;
  height: 24px;
  color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-100);
  padding: 8px;
  border-radius: 8px;
  box-sizing: content-box;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.preview-content {
  background: var(--iluria-color-surface);
  border-radius: 12px;
  padding: 16px;
  border: 1px solid var(--iluria-color-border);
}

.preview-text {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  margin: 0;
  font-weight: 500;
}



/* Responsive */
@media (max-width: 768px) {
  .minimum-order-container {
    padding: 16px;
  }



  .value-input {
    max-width: none;
  }

  .toggle-content {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .toggle-switch {
    align-self: flex-end;
  }

  .preview-card {
    padding: 20px;
  }

  .preview-icon {
    width: 20px;
    height: 20px;
    padding: 6px;
  }
}

@media (max-width: 480px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
  
  .header-content h1.page-title {
    font-size: 22px;
  }
}
</style>
