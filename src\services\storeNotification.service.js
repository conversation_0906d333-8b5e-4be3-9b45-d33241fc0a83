import axios from 'axios'
import { API_URLS } from '@/config/api.config'
import { useAuthStore } from '@/stores/auth.store'

class StoreNotificationService {
  constructor() {
    this.baseURL = API_URLS.STORE_BASE_URL
  }

  async getStoreNotificationSettings(storeId) {
    try {
      const response = await axios.get(`${this.baseURL}/store/${storeId}/notification-settings`)
      return response.data
    } catch (error) {
      if (error.response?.status === 404) {
        return this.getDefaultStoreSettings(storeId)
      }
      throw error
    }
  }

  async updateStoreNotificationSettings(storeId, settings) {
    try {
      const cleanSettings = this.validateAndCleanStoreSettings(settings)
      
      const response = await axios.put(`${this.baseURL}/store/${storeId}/notification-settings`, cleanSettings, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      })

      return response.data
    } catch (error) {
      throw error
    }
  }

  async getUserNotificationPermissions(storeId) {
    try {
      const response = await axios.get(`${this.baseURL}/store/${storeId}/user-permissions`)
      return response.data
    } catch (error) {
      if (error.response?.status === 404) {
        return []
      }
      throw error
    }
  }

  async updateUserNotificationPermission(storeId, userId, permission) {
    try {
      const response = await axios.put(`${this.baseURL}/store/${storeId}/user-permissions/${userId}`, permission, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return response.data
    } catch (error) {
      throw error
    }
  }

  async grantAllPermissions(storeId, userId) {
    try {
      const response = await axios.post(`${this.baseURL}/store/${storeId}/user-permissions/${userId}/grant-all`, {}, {
        headers: {
          'Content-Type': 'application/json'
        }
      })

      return response.data
    } catch (error) {
      throw error
    }
  }

  async revokeAllPermissions(storeId, userId) {
    try {
      await axios.post(`${this.baseURL}/store/${storeId}/user-permissions/${userId}/revoke-all`, {}, {
        headers: {
          'Content-Type': 'application/json'
        }
      })
    } catch (error) {
      throw error
    }
  }

  validateAndCleanStoreSettings(settings) {
    const validFields = [
      'notificationsEnabled', 'emailNotificationsEnabled', 'notificationFrequency',
      'quietHoursEnabled', 'quietHoursStart', 'quietHoursEnd', 'quietHoursTimezone',
      'newSalesEnabled', 'productReviewsEnabled', 'productQuestionsEnabled',
      'newsletterSubscriptionsEnabled', 'newCustomerRegistrationsEnabled',
      'lowStockAlertsEnabled', 'orderUpdatesEnabled', 'paymentUpdatesEnabled'
    ]

    const cleanSettings = {}

    for (const [key, value] of Object.entries(settings)) {
      if (validFields.includes(key)) {
        if (typeof value === 'boolean' || typeof value === 'string') {
          cleanSettings[key] = value
        }
      }
    }

    return cleanSettings
  }

  getDefaultStoreSettings(storeId) {
    return {
      storeId: storeId,
      notificationsEnabled: true,
      emailNotificationsEnabled: true,
      notificationFrequency: 'IMMEDIATE',
      quietHoursEnabled: false,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
      quietHoursTimezone: 'America/Sao_Paulo',
      
      // Store notification types - all enabled by default
      newSalesEnabled: true,
      productReviewsEnabled: true,
      productQuestionsEnabled: true,
      newsletterSubscriptionsEnabled: true,
      newCustomerRegistrationsEnabled: true,
      lowStockAlertsEnabled: true,
      orderUpdatesEnabled: true,
      paymentUpdatesEnabled: true
    }
  }

  // Notification types mapping for UI
  getStoreNotificationTypes() {
    return {
      NEW_SALES: {
        key: 'newSalesEnabled',
        label: 'notifications.store.types.newSales',
        description: 'notifications.store.types.newSalesDesc',
        critical: false
      },
      PRODUCT_REVIEWS: {
        key: 'productReviewsEnabled',
        label: 'notifications.store.types.productReviews',
        description: 'notifications.store.types.productReviewsDesc',
        critical: false
      },
      PRODUCT_QUESTIONS: {
        key: 'productQuestionsEnabled',
        label: 'notifications.store.types.productQuestions',
        description: 'notifications.store.types.productQuestionsDesc',
        critical: false
      },
      NEWSLETTER_SUBSCRIPTIONS: {
        key: 'newsletterSubscriptionsEnabled',
        label: 'notifications.store.types.newsletterSubscriptions',
        description: 'notifications.store.types.newsletterSubscriptionsDesc',
        critical: false
      },
      NEW_CUSTOMER_REGISTRATIONS: {
        key: 'newCustomerRegistrationsEnabled',
        label: 'notifications.store.types.newCustomerRegistrations',
        description: 'notifications.store.types.newCustomerRegistrationsDesc',
        critical: false
      },
      LOW_STOCK_ALERTS: {
        key: 'lowStockAlertsEnabled',
        label: 'notifications.store.types.lowStockAlerts',
        description: 'notifications.store.types.lowStockAlertsDesc',
        critical: true
      },
      ORDER_UPDATES: {
        key: 'orderUpdatesEnabled',
        label: 'notifications.store.types.orderUpdates',
        description: 'notifications.store.types.orderUpdatesDesc',
        critical: true
      },
      PAYMENT_UPDATES: {
        key: 'paymentUpdatesEnabled',
        label: 'notifications.store.types.paymentUpdates',
        description: 'notifications.store.types.paymentUpdatesDesc',
        critical: true
      }
    }
  }

  // Frequency options for UI
  getFrequencyOptions() {
    return [
      {
        value: 'IMMEDIATE',
        label: 'notifications.frequency.immediate',
        description: 'notifications.frequency.immediateDesc'
      },
      {
        value: 'DAILY',
        label: 'notifications.frequency.daily',
        description: 'notifications.frequency.dailyDesc'
      },
      {
        value: 'WEEKLY',
        label: 'notifications.frequency.weekly',
        description: 'notifications.frequency.weeklyDesc'
      }
    ]
  }

  /**
   * Busca notificações não lidas da loja.
   */
  async getUnreadNotifications(storeId, page = 1, pageSize = 10) {
    try {
      const response = await axios.get(`${this.baseURL}/store/${storeId}/notifications`, {
        params: {
          page,
          size: pageSize,
          onlyUnread: true
        }
      })
      return {
        notifications: response.data.content || [],
        unreadCount: response.data.totalUnread || 0,
        hasMore: response.data.hasNext || false,
        totalPages: response.data.totalPages || 1
      }
    } catch (error) {
      console.error('Error fetching store notifications:', error)
      throw error
    }
  }

  /**
   * Marca todas as notificações da loja como lidas.
   */
  async markAllAsRead(storeId) {
    try {
      const response = await axios.post(`${this.baseURL}/store/${storeId}/notifications/mark-all-read`)
      return response.data
    } catch (error) {
      console.error('Error marking all store notifications as read:', error)
      throw error
    }
  }

  /**
   * Marca uma notificação específica da loja como lida.
   */
  async markAsRead(notificationId, storeId) {
    try {
      const response = await axios.post(`${this.baseURL}/store/${storeId}/notifications/${notificationId}/read`)
      return response.data
    } catch (error) {
      console.error('Error marking store notification as read:', error)
      throw error
    }
  }
}

export default new StoreNotificationService()