<template>
  <div class="combined-product-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="$t('combinedProduct.title')"
      :subtitle="$t('combinedProduct.subtitle')"
      :showSearch="true"
      :showAdd="true"
      :addText="$t('combinedProduct.actions.add')"
      :searchPlaceholder="$t('combinedProduct.searchPlaceholder')"
      @search="handleSearch"
      @add-click="goToCreateCombined"
    />

    <!-- Table Wrapper -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="sortedProducts"
        :columns="mainTableColumns"
        :loading="loading"
        dataKey="id"
        class="combined-products-table"
      >
        <!-- Header Slots -->
        <template #header-image>
          <span class="column-header">{{ $t('combinedProduct.table.headers.image') }}</span>
        </template>
        <template #header-name>
          <span class="column-header" data-sortable="true" @click="toggleSort('name')">
            {{ $t('combinedProduct.table.headers.name') }}
            <span v-if="sortField === 'name'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-status>
          <span class="column-header" data-sortable="true" @click="toggleSort('status')">
            {{ $t('combinedProduct.table.headers.status') }}
            <span v-if="sortField === 'status'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
          </span>
        </template>
        <template #header-actions>
          <span class="column-header">{{ $t('combinedProduct.table.headers.actions') }}</span>
        </template>

        <!-- Column Templates -->
        <template #column-image="{ data }">
          <div class="product-image-container">
            <div class="product-image-wrapper">
              <img 
                v-if="getProductImage(data)" 
                :src="getProductImage(data)" 
                alt="Produto" 
                class="product-image" 
              />
              <div v-else class="product-image-placeholder">
                <i class="fas fa-image"></i>
              </div>
            </div>
          </div>
        </template>

        <!-- Name Column -->
        <template #column-name="{ data }">
          <div class="product-info">
            <div class="product-title">{{ data.name }}</div>
            <div class="product-description">
              {{ data.description ? (data.description.substring(0, 80) + (data.description.length > 80 ? '...' : '')) : '' }}
            </div>
          </div>
        </template>

        <!-- Status Column -->
        <template #column-status="{ data }">
          <span class="status-badge" :class="getStatusClass(data.status)">
            {{ getStatusLabel(data.status) }}
          </span>
        </template>

        <!-- Actions Column -->
        <template #column-actions="{ data }">
          <div class="action-buttons">
            <IluriaButton 
              color="text-primary" 
              size="small" 
              :hugeIcon="PencilEdit01Icon" 
              @click="editProduct(data)"
              :title="$t('combinedProduct.actions.edit')"
            />
            <IluriaButton 
              color="text-danger" 
              size="small" 
              :hugeIcon="Delete01Icon" 
              @click="confirmDelete(data)"
              :title="$t('combinedProduct.actions.delete')"
            />
          </div>
        </template>

        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <i class="fas fa-box-open"></i>
            </div>
            <h3 class="empty-title">{{ $t('combinedProduct.empty.message') }}</h3>
            <p class="empty-description">{{ $t('combinedProduct.empty.description', { max: MAX_COMBINED_PRODUCTS }) }}</p>
            <IluriaButton
              color="dark"
              :hugeIcon="PlusSignSquareIcon"
              @click="goToCreateCombined"
              :disabled="hasReachedLimit"
              class="mt-4"
            >
              {{ $t('combinedProduct.empty.action') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ $t('combinedProduct.loading') }}</span>
          </div>
        </template>
      </IluriaDataTable>
    </div>
    
    <!-- Pagination -->
    <div class="pagination-container" v-if="totalPages > 0">
      <IluriaPagination 
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />
    </div>
    
    <IluriaConfirmationModal 
      :isVisible="showConfirmDialog"
      :title="confirmData.title" 
      :message="confirmData.message"
      :type="confirmData.type || 'error'"
      @confirm="handleConfirm"
      @cancel="showConfirmDialog = false"
    />
  </div>
</template>

<script setup>
import { PencilEdit01Icon, Delete01Icon, PlusSignSquareIcon } from '@hugeicons-pro/core-bulk-rounded';
import { useRouter } from 'vue-router'
import { ref, onMounted, computed } from 'vue'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import { combinedProductService } from '@/services/combined-product.service';

const router = useRouter()
const toast = useToast()
const { t } = useI18n()
const loading = ref(false)

// Confirmation dialog state
const showConfirmDialog = ref(false)
const confirmData = ref({ title: '', message: '', type: 'error', onConfirm: null })
const products = ref([])
const totalPages = ref(1)
const currentPage = ref(0)
const pageSize = ref(10)
const searchTerm = ref('')
const sortField = ref(null)
const sortOrder = ref(null)

const toggleSort = (field) => {
  if (sortField.value === field) {
    sortOrder.value *= -1
  } else {
    sortField.value = field
    sortOrder.value = 1
  }
}

const sortedProducts = computed(() => {
  if (!sortField.value) {
    return products.value
  }
  return [...products.value].sort((a, b) => {
    const valA = a[sortField.value]
    const valB = b[sortField.value]
    if (valA < valB) return -1 * sortOrder.value
    if (valA > valB) return 1 * sortOrder.value
    return 0
  })
})

const MAX_COMBINED_PRODUCTS = 6

const hasReachedLimit = computed(() => {
  return products.value.length >= MAX_COMBINED_PRODUCTS
})

// Table columns configuration
const mainTableColumns = [
  { field: 'image', headerClass: 'col-image', class: 'col-image' },
  { field: 'name', headerClass: 'col-flex', class: 'col-flex' },
  { field: 'status', headerClass: 'col-small', class: 'col-small' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
];

let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadProducts()
  }, 500)
}

const handleSearch = (searchValue) => {
  searchTerm.value = searchValue
  debouncedSearch()
}

function getProductImage(product) {
  if (product.photos && product.photos.length > 0) {
    const firstPhoto = product.photos[0]
    return firstPhoto.url || firstPhoto
  }
  return '/images/default-product.jpg'
}

function getStatusLabel(status) {
  if (status === 'ACTIVE') {
    return t('combinedProduct.status.active')
  } else if (status === 'INACTIVE') {
    return t('combinedProduct.status.inactive')
  }
  return status || t('combinedProduct.status.inactive')
}

function getStatusClass(status) {
  if (status === 'ACTIVE') {
    return 'status-active'
  } else if (status === 'INACTIVE') {
    return 'status-inactive'
  }
  return 'status-inactive'
}

function editProduct(product) {
  if (product.id) {
    router.push(`/products/combined/edit/${product.id}`)
  } else {
    toast.showError('Erro: produto sem ID válido')
  }
}

function goToCreateCombined() {
  if (hasReachedLimit.value) {
    toast.showWarning(t('combinedProduct.messages.limitReached', { max: MAX_COMBINED_PRODUCTS }))
    return
  }
  
  router.push('/products/combined/edit')
}

function confirmDelete(product) {
  confirmData.value = {
    title: 'Confirmar Exclusão',
    message: t('combinedProduct.messages.deleteConfirm', { name: product.name }),
    type: 'error',
    onConfirm: () => deleteProduct(product)
  }
  showConfirmDialog.value = true
}

async function deleteProduct(product) {
  if (!product || !product.id) {
    toast.showError(t('combinedProduct.messages.invalidProduct'));
    return;
  }

  try {
    loading.value = true;
    
    await combinedProductService.deleteCombinedProduct(product.id);
    
    toast.showSuccess(t('combinedProduct.messages.deleteSuccess', { name: product.name }));
    
    await loadProducts();
    
  } catch (error) {
    
    let errorMessage = t('combinedProduct.messages.deleteError');
    
    if (error.response?.status === 404) {
      errorMessage = t('combinedProduct.messages.deleteNotFound');
    } else if (error.response?.status === 403) {
      errorMessage = t('combinedProduct.messages.deleteNoPermission');
    } else if (error.response?.status >= 500) {
      errorMessage = t('combinedProduct.messages.deleteServerError');
    } else if (error.code === 'ERR_NETWORK') {
      errorMessage = t('combinedProduct.messages.deleteNetworkError');
    }
    
    toast.showError(errorMessage);
  } finally {
    loading.value = false;
  }
}

function changePage(page) {
  currentPage.value = page
  loadProducts()
}

async function loadProducts() {
  try {
    loading.value = true
    
    const result = await combinedProductService.getAllCombinedProducts(
      currentPage.value, 
      pageSize.value,
      searchTerm.value
    )
    
    products.value = result.content || []
    totalPages.value = result.totalPages || 1
    
  } catch (error) {
    toast.showError(t('combinedProduct.messages.loadError'))
  } finally {
    loading.value = false
  }
}

// Confirmation dialog handler
const handleConfirm = () => {
  if (confirmData.value.onConfirm) {
    confirmData.value.onConfirm()
  }
  showConfirmDialog.value = false
}

onMounted(() => {
  loadProducts()
})
</script>

<style scoped>
.combined-product-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  min-width: 250px;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-image) { width: 80px; text-align: center; }
:deep(.col-flex) { width: auto; text-align: left; }
:deep(.col-small) { width: 120px; text-align: center; }
:deep(.col-actions) { width: 120px; text-align: center; }

/* Column header style */
:deep(.combined-products-table th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.combined-products-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* General table styling */
:deep(.combined-products-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.combined-products-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.combined-products-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.combined-products-table .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.combined-products-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.combined-products-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.combined-products-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.combined-products-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}

/* Product Elements */
.product-image-container {
  display: flex;
  justify-content: center;
}

.product-image-wrapper {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  background: var(--iluria-color-sidebar-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.product-image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.product-image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-muted);
  font-size: 18px;
  transition: color 0.3s ease;
}

.product-info {
  text-align: left;
}

.product-title {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
  transition: color 0.3s ease;
}

.product-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  transition: color 0.3s ease;
}

/* Status badges */
.status-badge {
  margin: 0 auto;
  padding: 6px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
  transition: all 0.3s ease;
}

.status-active {
  background: rgba(34, 197, 94, 0.15);
  color: var(--iluria-color-success);
  border: 1px solid rgba(34, 197, 94, 0.3);
}

.status-inactive {
  background: rgba(239, 68, 68, 0.15);
  color: var(--iluria-color-error);
  border: 1px solid rgba(239, 68, 68, 0.3);
}

/* Action buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
}

.empty-icon {
  font-size: 48px;
  color: var(--iluria-color-text-muted);
  margin-bottom: 16px;
  opacity: 0.6;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 24px 0;
  line-height: 1.5;
  transition: color 0.3s ease;
}

/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 48px 24px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* Responsividade */
@media (max-width: 768px) {
  .combined-product-container {
    padding: 16px;
  }

  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }

  .header-actions {
    flex-direction: column;
    gap: 12px;
  }

  .search-input {
    min-width: auto;
    width: 100%;
  }

  :deep(.combined-products-table .p-datatable-thead > tr > th),
  :deep(.combined-products-table .p-datatable-tbody > tr > td) {
    padding: 12px 16px;
    font-size: 14px;
  }

  .product-title {
    font-size: 14px;
  }

  .product-description {
    font-size: 12px;
  }
}

/* Tema escuro - força aplicação */
.theme-dark .combined-product-container {
  color: var(--iluria-color-text) !important;
}

.theme-dark .page-title {
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .page-subtitle {
  color: var(--iluria-color-text-secondary) !important;
}

.theme-dark .product-image-wrapper {
  background: var(--iluria-color-sidebar-bg) !important;
}

.theme-dark .product-image-placeholder {
  color: var(--iluria-color-text-muted) !important;
}

.theme-dark .product-title {
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .product-description {
  color: var(--iluria-color-text-secondary) !important;
}

.theme-dark .empty-title {
  color: var(--iluria-color-text-primary) !important;
}

.theme-dark .empty-description {
  color: var(--iluria-color-text-secondary) !important;
}

.theme-dark .loading-state {
  color: var(--iluria-color-text-secondary) !important;
}

.theme-dark .status-active {
  background: rgba(34, 197, 94, 0.2) !important;
  border-color: rgba(34, 197, 94, 0.4) !important;
}

.theme-dark .status-inactive {
  background: rgba(239, 68, 68, 0.2) !important;
  border-color: rgba(239, 68, 68, 0.4) !important;
}
</style> 
