<template>
  <div class="change-password-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="$t('userSettings.changePassword')"
      :subtitle="$t('userSettings.changePasswordInstructions')"
      :showSave="true"
      :saveText="$t('userSettings.updatePassword')"
      @save-click="handleSubmit"
    />

    <!-- Main Content -->
    <Form
      @submit="handleSubmit"
      :validation-schema="schema"
      class="form-container"
    >
      <!-- Se<PERSON><PERSON><PERSON> da <PERSON>ta -->
      <ViewContainer
        :title="$t('userSettings.securityNoticeTitle')"
        :subtitle="$t('userSettings.securityNoticeText')"
        :icon="SecurityCheckIcon"
        iconColor="blue"
      >
        <div class="form-grid">
          <!-- Senha Atual -->
          <div class="form-field">
            <IluriaInputText
              id="currentPassword"
              :label="$t('userSettings.currentPassword')"
              v-model="currentPassword"
              :type="currentPasswordType"
              :placeholder="$t('userSettings.currentPasswordPlaceholder')"
              :error="errors.currentPassword"
            >
              <template #suffix>
                <button
                  type="button"
                  @click="togglePasswordVisibility('current')"
                  class="password-toggle-btn"
                >
                  <HugeiconsIcon
                    :icon="currentPasswordType === 'password' ? ViewIcon : ViewOffIcon"
                    :size="18"
                    :strokeWidth="1.5"
                  />
                </button>
              </template>
            </IluriaInputText>
          </div>

          <!-- Nova Senha -->
          <div class="form-field">
            <IluriaInputText
              id="newPassword"
              :label="$t('userSettings.newPassword')"
              v-model="newPassword"
              :type="newPasswordType"
              :placeholder="$t('userSettings.newPasswordPlaceholder')"
              :error="errors.newPassword"
            >
              <template #suffix>
                <button
                  type="button"
                  @click="togglePasswordVisibility('new')"
                  class="password-toggle-btn"
                >
                  <HugeiconsIcon
                    :icon="newPasswordType === 'password' ? ViewIcon : ViewOffIcon"
                    :size="18"
                    :strokeWidth="1.5"
                  />
                </button>
              </template>
            </IluriaInputText>

            <!-- Password Strength Indicator -->
            <div class="password-strength" v-if="newPassword">
              <div class="strength-bar">
                <div
                  class="strength-fill"
                  :class="passwordStrengthClass"
                  :style="{ width: passwordStrengthPercentage + '%' }"
                ></div>
              </div>
              <span class="strength-text" :class="passwordStrengthClass">
                {{ passwordStrengthText }}
              </span>
            </div>
          </div>

          <!-- Confirmar Nova Senha -->
          <div class="form-field">
            <IluriaInputText
              id="confirmNewPassword"
              :label="$t('userSettings.confirmNewPassword')"
              v-model="confirmNewPassword"
              :type="confirmNewPasswordType"
              :placeholder="$t('userSettings.confirmNewPasswordPlaceholder')"
              :error="errors.confirmNewPassword"
            >
              <template #suffix>
                <button
                  type="button"
                  @click="togglePasswordVisibility('confirm')"
                  class="password-toggle-btn"
                >
                  <HugeiconsIcon
                    :icon="confirmNewPasswordType === 'password' ? ViewIcon : ViewOffIcon"
                    :size="18"
                    :strokeWidth="1.5"
                  />
                </button>
              </template>
            </IluriaInputText>
          </div>
        </div>
      </ViewContainer>

      <!-- Requisitos da Senha -->
      <ViewContainer
        :title="$t('userSettings.passwordRequirements')"
        subtitle="Sua senha deve atender aos seguintes critérios de segurança"
        :icon="CheckListIcon"
        iconColor="green"
        class="mt-6"
      >
        <div class="requirements-grid">
          <div class="requirement-item" :class="{ 'met': hasUpperCase }">
            <HugeiconsIcon :icon="hasUpperCase ? CheckmarkCircle02Icon : CancelCircleIcon" :size="16" />
            <span>{{ $t('userSettings.requirementUpperCase') }}</span>
          </div>
          <div class="requirement-item" :class="{ 'met': hasLowerCase }">
            <HugeiconsIcon :icon="hasLowerCase ? CheckmarkCircle02Icon : CancelCircleIcon" :size="16" />
            <span>{{ $t('userSettings.requirementLowerCase') }}</span>
          </div>
          <div class="requirement-item" :class="{ 'met': hasNumber }">
            <HugeiconsIcon :icon="hasNumber ? CheckmarkCircle02Icon : CancelCircleIcon" :size="16" />
            <span>{{ $t('userSettings.requirementNumber') }}</span>
          </div>
          <div class="requirement-item" :class="{ 'met': hasSpecialChar }">
            <HugeiconsIcon :icon="hasSpecialChar ? CheckmarkCircle02Icon : CancelCircleIcon" :size="16" />
            <span>{{ $t('userSettings.requirementSpecialChar') }}</span>
          </div>
          <div class="requirement-item" :class="{ 'met': hasMinLength }">
            <HugeiconsIcon :icon="hasMinLength ? CheckmarkCircle02Icon : CancelCircleIcon" :size="16" />
            <span>{{ $t('userSettings.requirementMinLength') }}</span>
          </div>
        </div>
      </ViewContainer>
    </Form>
  </div>
</template>
  
<script setup>
import { ref, computed } from 'vue';
import { useAuthStore } from '@/stores/auth.store';
import { useRouter } from 'vue-router';
import { useForm } from 'vee-validate';
import { Form } from 'vee-validate';
import * as yup from 'yup';
import { useI18n } from 'vue-i18n';
import { useToast } from '@/services/toast.service';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import {
  SecurityCheckIcon,
  CheckListIcon,
  CancelCircleIcon,
  ViewIcon,
  ViewOffIcon
} from '@hugeicons-pro/core-stroke-standard';

// Reactive data
const currentPassword = ref('');
const newPassword = ref('');
const confirmNewPassword = ref('');
const currentPasswordType = ref('password');
const newPasswordType = ref('password');
const confirmNewPasswordType = ref('password');
const loading = ref(false);

// Composables
const authStore = useAuthStore();
const router = useRouter();
const { t } = useI18n();
const toast = useToast();
  
// Form validation
const schema = yup.object({
  currentPassword: yup.string().required(() => t('userSettings.confirmPassword')),
  newPassword: yup.string()
    .required(() => t('userSettings.newPasswordRequired'))
    .test('password-strength', '', function (value) {
      const errors = [];
      if (!/[A-Z]/.test(value)) errors.push(t('passwordRecovery.passwordUpperCase'));
      if (!/[a-z]/.test(value)) errors.push(t('passwordRecovery.passwordLowerCase'));
      if (!/\d/.test(value)) errors.push(t('passwordRecovery.passwordNumber'));
      if (!/[\W_]/.test(value)) errors.push(t('passwordRecovery.passwordSpecialChar'));
      if (value.length < 8) errors.push(t('passwordRecovery.passwordMinLength'));
      return errors.length ? this.createError({ message: errors.join('<br>') }) : true;
    }),
  confirmNewPassword: yup.string()
    .oneOf([yup.ref('newPassword')], () => t('passwordRecovery.passwordMismatch'))
    .required(() => t('userSettings.confirmNewPasswordRequired'))
});

const { errors } = useForm({ schema });

// Password strength indicators
const hasUpperCase = computed(() => /[A-Z]/.test(newPassword.value));
const hasLowerCase = computed(() => /[a-z]/.test(newPassword.value));
const hasNumber = computed(() => /\d/.test(newPassword.value));
const hasSpecialChar = computed(() => /[\W_]/.test(newPassword.value));
const hasMinLength = computed(() => newPassword.value.length >= 8);

const passwordStrengthScore = computed(() => {
  let score = 0;
  if (hasUpperCase.value) score++;
  if (hasLowerCase.value) score++;
  if (hasNumber.value) score++;
  if (hasSpecialChar.value) score++;
  if (hasMinLength.value) score++;
  return score;
});

const passwordStrengthPercentage = computed(() => (passwordStrengthScore.value / 5) * 100);

const passwordStrengthClass = computed(() => {
  const score = passwordStrengthScore.value;
  if (score <= 2) return 'strength-weak';
  if (score <= 3) return 'strength-medium';
  if (score <= 4) return 'strength-good';
  return 'strength-strong';
});

const passwordStrengthText = computed(() => {
  const score = passwordStrengthScore.value;
  if (score <= 2) return t('userSettings.strengthWeak');
  if (score <= 3) return t('userSettings.strengthMedium');
  if (score <= 4) return t('userSettings.strengthGood');
  return t('userSettings.strengthStrong');
});

const isFormValid = computed(() => {
  return currentPassword.value &&
         newPassword.value &&
         confirmNewPassword.value &&
         passwordStrengthScore.value >= 4 &&
         newPassword.value === confirmNewPassword.value;
});
// Methods
const togglePasswordVisibility = (field) => {
  if (field === 'current') {
    currentPasswordType.value = currentPasswordType.value === 'password' ? 'text' : 'password';
  } else if (field === 'new') {
    newPasswordType.value = newPasswordType.value === 'password' ? 'text' : 'password';
  } else if (field === 'confirm') {
    confirmNewPasswordType.value = confirmNewPasswordType.value === 'password' ? 'text' : 'password';
  }
};

const handleSubmit = async () => {
  if (!isFormValid.value) return;

  loading.value = true;

  try {
    const response = await authStore.changePassword(
      currentPassword.value,
      newPassword.value,
      confirmNewPassword.value
    );

    if (response.success) {
      toast.addToast(t('passwordRecovery.passwordChangedSuccess'), 'success');
      router.push({ name: 'Home' });
    } else {
      if (response.code === '10012') {
        toast.addToast(t('userSettings.invalidCurrentPassword'), 'error');
      } else {
        toast.addToast(response.message || t('passwordRecovery.errorChangingPassword'), 'error');
      }
    }
  } catch (err) {
    toast.addToast(t('passwordRecovery.passwordChangeFailed'), 'error');
  } finally {
    loading.value = false;
  }
};

const cancelChangePassword = () => {
  router.push({ name: 'Home' });
};
  </script>
  
<style scoped>
.change-password-container {
  padding: 24px;
  background: var(--iluria-color-body-bg);
  min-height: 100vh;
}



/* Form */
.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 24px;
}

.form-field {
  display: flex;
  flex-direction: column;
}

/* Password Toggle Button */
.password-toggle-btn {
  background: none;
  border: none;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  padding: 4px;
  border-radius: 4px;
  transition: color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.password-toggle-btn:hover {
  color: var(--iluria-color-text);
}

/* Password Strength Indicator */
.password-strength {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-top: 8px;
}

.strength-bar {
  flex: 1;
  height: 4px;
  background: var(--iluria-color-border);
  border-radius: 2px;
  overflow: hidden;
}

.strength-fill {
  height: 100%;
  transition: all 0.3s ease;
  border-radius: 2px;
}

.strength-fill.strength-weak {
  background: #ef4444;
}

.strength-fill.strength-medium {
  background: #f59e0b;
}

.strength-fill.strength-good {
  background: #3b82f6;
}

.strength-fill.strength-strong {
  background: #10b981;
}

.strength-text {
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  min-width: 60px;
}

.strength-text.strength-weak {
  color: #ef4444;
}

.strength-text.strength-medium {
  color: #f59e0b;
}

.strength-text.strength-good {
  color: #3b82f6;
}

.strength-text.strength-strong {
  color: #10b981;
}

/* Requirements Grid */
.requirements-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 16px;
}

.requirement-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.2s ease;
  padding: 8px 0;
}

.requirement-item.met {
  color: var(--iluria-color-success);
}

.requirement-item svg {
  flex-shrink: 0;
  width: 16px;
  height: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .change-password-container {
    padding: 16px;
  }



  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }

  .requirements-grid {
    grid-template-columns: 1fr;
  }

  .page-title {
    font-size: 24px;
  }
}
</style>
