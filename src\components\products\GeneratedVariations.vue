<template>
  <div class="generated-variations">
    <div v-if="modelValue.length > 0">
      <div v-if="variationsArray.length > 1" class="group-selector">
        <label>{{ t('product.groupBy') }}</label>
        <Select :options="variationsArray" optionLabel="title" optionValue="title" v-model="selectedGrouping">
        </Select>
      </div>

      <div class="list-header font-bold">
        <div>{{ t('product.variant') }}</div>
        <div>{{ t('price') }}</div>
        <div>{{ t('stock') }}</div>
        <div>{{ t('actions') }}</div>
      </div>

      <div v-for="group in groupedBySelected" :key="group.title" class="variation-group">
        <div class="list-header group-header" v-if="variationsArray.length > 1" @click="toggleGroup(group.title)">
          <div class="group-title">
            <HugeiconsIcon size="16" :icon="isGroupExpanded(group.title) ? ArrowDownDoubleIcon : ArrowRightDoubleIcon"></HugeiconsIcon>
            <label>{{ group.title }}</label>
            <span class="variant-count">{{ group.variations.length }} {{ t('product.variants') }}</span>
          </div>
          <div class="price-input">
            <IluriaInputText
              :id="`group-price-${group.title}`"
              label=""
              type="money"
              :placeholder="getPriceRange(group.variations)"
              prefix="R$"
              @update:modelValue="(value) => updateGroupPrice(group.variations, value)"
              @click.stop
            />
          </div>
          <div class="stock-input">
            <IluriaInputText 
              :id="`group-stock-${group.title}`"
              label=""
              type="number" 
              :suffix="getUnitText(getTotalStock(group.variations))"
              :placeholder="getTotalStock(group.variations)"
              @update:modelValue="(value) => updateGroupStock(group.variations, value)"
              @click.stop
            />
          </div>
          <div class="flex">
            <IluriaButton color="text-primary" size="small" :hugeIcon="PencilEdit01Icon" @click.stop="openEditModalForGroup(group.variations)"></IluriaButton>
            <IluriaButton color="text-danger" size="small" :hugeIcon="Delete01Icon" @click.stop="deleteGroup(group.title)"></IluriaButton>
          </div>
        </div>

        <div v-if="isGroupExpanded(group.title)" class="variation-items">
          <div v-for="variation in group.variations" :key="getVariationTitle(variation.attributes)" class="variation-item">
            <div>
              <span>{{ getVariationTitle(variation.attributes) }}</span>
            </div>
            <div class="row-price">
              <div class="price-input">
                <IluriaInputText
                  :id="`variation-price-${getVariationTitle(variation.attributes)}`"
                  label=""
                  v-model="variation.price"
                  type="money"
                  prefix="R$"
                />
              </div>
            </div>
            <div class="row-stock">
              <IluriaInputText 
                :id="`variation-stock-${getVariationTitle(variation.attributes)}`"
                label=""
                v-model="variation.stockQuantity"
                type="number" 
                :suffix="getUnitText(variation.stockQuantity)"
              />
            </div>
            <div class="flex">
              <IluriaButton color="text-primary" size="small" :hugeIcon="PencilEdit01Icon" @click.stop="openEditModal(variation)"></IluriaButton>
              <IluriaButton color="text-danger" size="small" :hugeIcon="Delete01Icon" @click.stop="deleteVariation(variation)"></IluriaButton>
            </div>
          </div>
        </div>
      </div>
    </div>

    <VariationEditModal
      v-model="editingVariation"
      @save="saveVariationChanges"
    />
  </div>
</template>

<script setup>
import { ref, computed, watch, inject } from 'vue';
import VariationEditModal from './VariationEditModal.vue';
import { Select } from 'primevue';
import { HugeiconsIcon } from '@hugeicons/vue';
import { ArrowDownDoubleIcon, ArrowRightDoubleIcon, Delete01Icon, PencilEdit01Icon } from '@hugeicons-pro/core-bulk-rounded';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

// Use injected data from parent components
const productForm = inject('productForm');
const variationsArray = inject('variations', ref([]));

// Use defineModel for the model value
const modelValue = defineModel();

const selectedGrouping = ref('');
const expandedGroups = ref(new Set());
const editingVariation = ref(null);

// Função para retornar o texto da unidade baseado na quantidade
const getUnitText = (quantity) => {
  const numValue = Number(quantity) || 0;
  return numValue > 1 ? 'unidades' : 'unidade';
};

// Watch for changes in variations array
watch(() => variationsArray.value, (newVal) => {
  if (newVal.length > 0) {
    selectedGrouping.value = newVal[0].title || '';
  }
}, { immediate: true, deep: true });

// Watch for changes in the first variation's title
watch(() => variationsArray.value[0]?.title, (newTitle) => {
  if (newTitle) {
    selectedGrouping.value = newTitle;
  }
}, { immediate: true });

const toggleGroup = (groupTitle) => {
  if (expandedGroups.value.has(groupTitle)) {
    expandedGroups.value.delete(groupTitle);
  } else {
    expandedGroups.value.add(groupTitle);
  }
};

const isGroupExpanded = (groupTitle) => {
  return variationsArray.value.length === 1 || expandedGroups.value.has(groupTitle);
};

const groupedBySelected = computed(() => {
  if (!selectedGrouping.value || !modelValue.value) return [];

  // If there's only one variation, return all variations in a single group
  if (variationsArray.value.length === 1) {
    return [{
      title: variationsArray.value[0].title,
      variations: modelValue.value
    }];
  }

  const groups = {};
  
  modelValue.value.forEach(variation => {
    const groupValue = variation.attributes[selectedGrouping.value];
    if (!groups[groupValue]) {
      groups[groupValue] = [];
    }
    groups[groupValue].push(variation);
  });

  return Object.entries(groups).map(([title, variations]) => ({
    title,
    variations
  }));
});

function getPriceRange(variations) {
  const prices = variations.map(v => v.price).filter(p => p !== undefined && p !== null);
  if (prices.length === 0) return '0,00';
  if (allEqual(prices)) return `${prices[0].toFixed(2)}`;
  return `${Math.min(...prices).toFixed(2)} - ${Math.max(...prices).toFixed(2)}`;
}

function getTotalStock(variations) {
  const stocks = variations.map(v => v.stockQuantity).filter(s => s !== undefined && s !== null);
  return stocks.length ? stocks.reduce((a, b) => a + b, 0) : 0;
}

function getVariationTitle(attributes) {
  if (!attributes) return '';
  return Object.entries(attributes)
    .map(([key, value]) => `${value}`)
    .join(' / ');
}

function updateGroupPrice(variations, price) {
  const numPrice = Number(price);
  if (!isNaN(numPrice)) {
    // Obter o atributo pelo qual estamos agrupando
    const groupKey = selectedGrouping.value;
    
    if (!groupKey || variations.length === 0) return;
    
    // Obter o valor do grupo atual (ex: 'Azul' se agrupado por cor)
    const groupValue = variations[0].attributes[groupKey];
    
    // Criar uma nova lista de variações para evitar modificar as referências originais
    const updatedVariations = modelValue.value.map(variation => {
      // Verificar se esta variação pertence ao grupo atual
      if (variation.attributes[groupKey] === groupValue) {
        // Retornar uma nova cópia com o preço atualizado
        return {
          ...variation,
          price: numPrice
        };
      }
      // Manter as outras variações inalteradas
      return variation;
    });
    
    // Atualizar o array completo de variações
    modelValue.value = updatedVariations;
  }
}

function updateGroupStock(variations, stock) {
  const numStock = Number(stock);
  if (!isNaN(numStock)) {
    // Obter o atributo pelo qual estamos agrupando
    const groupKey = selectedGrouping.value;
    
    if (!groupKey || variations.length === 0) return;
    
    // Obter o valor do grupo atual (ex: 'Azul' se agrupado por cor)
    const groupValue = variations[0].attributes[groupKey];
    
    // Criar uma nova lista de variações para evitar modificar as referências originais
    const updatedVariations = modelValue.value.map(variation => {
      // Verificar se esta variação pertence ao grupo atual
      if (variation.attributes[groupKey] === groupValue) {
        // Retornar uma nova cópia com o estoque atualizado
        return {
          ...variation,
          stockQuantity: numStock
        };
      }
      // Manter as outras variações inalteradas
      return variation;
    });
    
    // Atualizar o array completo de variações
    modelValue.value = updatedVariations;
  }
}

function openEditModal(variation) {
  editingVariation.value = {
    ...variation,
    price: variation.price,
    stockQuantity: variation.stockQuantity,
    originalPrice: variation.originalPrice,
    costPrice: variation.costPrice,
    weight: variation.weight,
    boxLength: variation.boxLength,
    boxWidth: variation.boxWidth,
    boxDepth: variation.boxDepth
  };
}

function openEditModalForGroup(variations) {
  const fields = ['price', 'stockQuantity', 'originalPrice', 'costPrice', 'weight', 'boxLength', 'boxWidth', 'boxDepth'];
  const groupData = {};
  
  fields.forEach(field => {
    const values = variations.map(v => v[field]).filter(v => v !== undefined);
    if (values.length === 0 || !allEqual(values)) {
      groupData[field] = undefined;
    } else {
      groupData[field] = values[0];
    }
  });
  
  editingVariation.value = {
    ...groupData,
    isGroupEdit: true,
    groupVariations: variations,
    groupItems: variations
  };
}

// Função auxiliar para verificar se todos os elementos de um array são iguais
const allEqual = arr => arr.every(val => val === arr[0]);

function deleteVariation(variation) {
  const index = modelValue.value.findIndex(v => 
    Object.entries(v.attributes).every(([key, value]) => 
      variation.attributes[key] === value
    )
  );
  
  if (index >= 0) {
    modelValue.value.splice(index, 1);
  }
}

function deleteGroup(groupTitle) {
  modelValue.value = modelValue.value.filter(v => v.attributes[selectedGrouping.value] !== groupTitle);
}

function saveVariationChanges(updatedVariation) {
  if (updatedVariation.isGroupEdit) {
    // Update all variations in the group
    const groupVariations = updatedVariation.groupVariations;
    const fieldsToUpdate = ['price', 'stockQuantity', 'originalPrice', 'costPrice', 'weight', 'boxLength', 'boxWidth', 'boxDepth'];
    
    groupVariations.forEach(variation => {
      fieldsToUpdate.forEach(field => {
        if (updatedVariation[field] !== undefined) {
          const numValue = Number(updatedVariation[field]);
          variation[field] = !isNaN(numValue) ? numValue : updatedVariation[field];
        }
      });
    });
  } else {
    // Find and update a single variation
    const variationToUpdate = modelValue.value.find(v => 
      Object.entries(v.attributes).every(([key, value]) => 
        updatedVariation.attributes[key] === value
      )
    );
    
    if (variationToUpdate) {
      const fieldsToUpdate = ['price', 'stockQuantity', 'originalPrice', 'costPrice', 'weight', 'boxLength', 'boxWidth', 'boxDepth'];
      
      fieldsToUpdate.forEach(field => {
        const numValue = Number(updatedVariation[field]);
        variationToUpdate[field] = !isNaN(numValue) ? numValue : updatedVariation[field];
      });
    }
  }
}
</script>

<style scoped>
.generated-variations {
  margin-top: 1rem;
}

.group-selector {
  margin-bottom: 1rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.list-header {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 0.5rem;
  border-bottom: 1px solid #eee;
}

.group-header {
  background-color: #f8f9fa;
  margin-top: 0.5rem;
  border-radius: 4px;
  cursor: pointer;
}

.group-title {
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.variant-count {
  font-size: 0.75rem;
  color: #6c757d;
  font-weight: normal;
}

.variation-items {
  padding: 0.5rem 0;
}

.variation-item {
  display: grid;
  grid-template-columns: 3fr 1fr 1fr 1fr;
  gap: 1rem;
  padding: 0.5rem;
  border-bottom: 1px solid #f1f1f1;
  align-items: center;
}

.price-input {
  position: relative;
}

.currency {
  position: absolute;
  left: 0.5rem;
  top: 0.45rem;
  color: #6c757d;
  font-size: 0.875rem;
  z-index: 1;
}

.price-input input {
  padding-left: 2rem;
}

.row-price, .row-stock {
  display: flex;
  align-items: center;
}

.delete-group {
  color: #dc3545;
  background: none;
  border: none;
  cursor: pointer;
  font-size: 0.875rem;
}
</style>
