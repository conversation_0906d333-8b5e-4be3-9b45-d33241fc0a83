<template>
  <div class="photo-upload-container">
    <!-- Upload Area -->
    <div
      class="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center hover:border-gray-400 transition-colors mb-4"
      @dragover.prevent @drop.prevent="handleDrop">
      <HugeiconsIcon :icon="CloudUploadIcon" class="text-4xl text-gray-400 mb-3 mx-auto" />
      <IluriaButton @click="triggerFileInput" color="outline" :huge-icon="AddSquareIcon" class="mb-2">
        {{ t('productVariations.selectFiles') }}
      </IluriaButton>
      <input type="file" ref="fileInput" class="hidden" accept="image/*" multiple @change="handleFileSelect">
      <p class="text-gray-600 text-sm">{{ t('productVariations.dragAndDropPhotos') }}</p>
    </div>

    <!-- Image Grid -->
    <draggable v-if="selectedFiles.length > 0" v-model="selectedFiles"
      class="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-4" :animation="200" item-key="name"
      @start="dragStart" @end="dragEnd" @change="onOrderChange">
      <template #item="{ element: file, index }">
        <div class="relative group">
          <div
            class="aspect-square bg-gray-100 rounded-lg overflow-hidden cursor-pointer border-2 transition-all duration-200"
            :class="{
              'border-blue-500 ring-2 ring-blue-200': selectedIndexes.includes(index),
              'border-transparent hover:border-blue-300': !selectedIndexes.includes(index)
            }" @click="handlePhotoClick(index)" style="min-height: 120px; position: relative; background: #f3f4f6;">
            <img :src="getPreviewUrl(file)" class="w-full h-full object-cover" alt="Preview"
              @load="handleImageLoad(index)" @error="handleImageError(index)"
              :class="{ 'opacity-50': imageErrors[index] }" v-show="isFileObject(file) || imageLoaded[index]"
              :style="{ 'background-color': 'transparent' }" />
            <!-- Loading indicator - only for existing photos from backend -->
            <div v-if="!isFileObject(file) && !imageLoaded[index] && !imageErrors[index]"
              class="absolute inset-0 flex items-center justify-center bg-gray-200">
              <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-500"></div>
            </div>
            <!-- Error indicator -->
            <div v-if="imageErrors[index]" class="absolute inset-0 flex items-center justify-center bg-red-200">
              <HugeiconsIcon :icon="CloudUploadIcon" class="text-4xl text-red-400" />
              <span class="text-xs text-red-500 mt-1">Erro</span>
            </div>
            <!-- Selection overlay -->
            <div v-if="selectedIndexes.includes(index)" class="absolute top-2 right-2 z-10">
              <div class="bg-blue-500 rounded-full p-1 shadow-lg">
                <HugeiconsIcon :icon="CheckmarkCircle02Icon" class="text-sm text-white" />
              </div>
            </div>

            <!-- Hover overlay -->
            <div class="absolute inset-0  group-hover:bg-opacity-10 transition-opacity"></div>

            <!-- Selection checkbox -->
            <IluriaCheckBox :modelValue="selectedIndexes.includes(index)"
              @update:modelValue="(val) => toggleSelection(index)" class="absolute top-2 left-2 z-10" @click.stop />

            <!-- Delete button - always visible on hover -->
            <div class="absolute bottom-2 right-2 z-10 opacity-100 transition-opacity">
              <IluriaButton @click.stop="removePhoto(index)" color="danger" size="small"
                class="!p-1 !min-w-0 !w-6 !h-6 rounded-full bg-red-500 hover:bg-red-600 text-white shadow-lg">
                <HugeiconsIcon :icon="Delete03Icon" class="w-3 h-3" />
              </IluriaButton>
            </div>

            <!-- Position indicator -->
            <div class="absolute bottom-1 left-1 bg-black bg-opacity-70 text-white text-xs px-2 py-1 rounded">
              {{ index + 1 }}
            </div>
          </div>
          <div class="mt-1 text-center text-sm text-gray-600 truncate">
            {{ getFileName(file) }}
          </div>
          <div class="text-center text-sm text-gray-500">
            {{ getFileInfo(file) }}
          </div>
        </div>
      </template>
    </draggable>

    <!-- Actions -->
    <div v-if="selectedFiles.length > 0" class="mt-4 flex flex-wrap gap-2">
      <IluriaButton @click="selectAll" color="text-primary" size="small" :huge-icon="CheckmarkSquare03Icon">
        {{ t('productVariations.selectAll') }}
      </IluriaButton>
      <IluriaButton v-if="selectedIndexes.length > 0" @click="unselectAll" color="text-primary" size="small"
        :huge-icon="CancelCircleIcon">
        {{ t('productVariations.unselectAll') }}
      </IluriaButton>
      <IluriaButton v-if="selectedIndexes.length > 0" @click="deleteSelected" color="text-danger" size="small"
        :huge-icon="Delete03Icon">
        {{ t('productVariations.deleteSelected') }}
      </IluriaButton>
      <IluriaButton @click="clearAll" color="outline" size="small" :huge-icon="RefreshIcon">
        {{ t('productVariations.clearAll') }}
      </IluriaButton>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import {
  CloudUploadIcon,
  AddSquareIcon,
  CheckmarkSquare03Icon,
  CancelCircleIcon,
  Delete03Icon,
  RefreshIcon,
  CheckmarkCircle02Icon
} from '@hugeicons-pro/core-bulk-rounded';
import draggable from 'vuedraggable';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';
import { productsApi } from '@/services/product.service';
import { useToast } from '@/services/toast.service';

const { t } = useI18n();
const toast = useToast();

const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  },
  maxFiles: {
    type: Number,
    default: 10
  },
  productId: {
    type: String,
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  },
  isGiftCard: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const fileInput = ref(null);
const selectedFiles = ref([]);
const selectedIndexes = ref([]);
const isDragging = ref(false);
const imageLoaded = ref([]);
const imageErrors = ref([]);
const objectUrls = ref(new Map());
const objectUrlsCleanupQueue = new Set();
const isUpdatingFromParent = ref(false);

watch(() => props.modelValue, (newValue, oldValue) => {
  if (isUpdatingFromParent.value) {
    isUpdatingFromParent.value = false;
    return;
  }

  if (newValue && newValue.length > 0) {
    const existingPhotos = newValue.map(photo => {
      if (photo.url) {
        return {
          ...photo,
          isExisting: true
        };
      }
      return photo;
    });

    const hasContentChanged = !oldValue ||
      oldValue.length !== newValue.length ||
      !arraysEqual(oldValue, newValue);

    if (hasContentChanged) {
      const oldImageLoadedStates = [...imageLoaded.value];
      const oldImageErrorStates = [...imageErrors.value];

      selectedFiles.value = [...existingPhotos];

      const isReorder = oldValue &&
        oldValue.length === newValue.length &&
        oldValue.every(oldItem => newValue.some(newItem =>
          (oldItem.id && oldItem.id === newItem.id) ||
          (oldItem.url && oldItem.url === newItem.url)
        ));

      if (isReorder) {
        imageLoaded.value = oldImageLoadedStates.slice(0, selectedFiles.value.length);
        imageErrors.value = oldImageErrorStates.slice(0, selectedFiles.value.length);
      } else {
        imageLoaded.value = Array(selectedFiles.value.length).fill(false);
        imageErrors.value = Array(selectedFiles.value.length).fill(false);

        selectedFiles.value.forEach((file, index) => {
          if (file.isExisting && file.url) {
            imageLoaded.value[index] = true;
          }
        });
      }
    }
  } else {
    selectedFiles.value = [];
    imageLoaded.value = [];
    imageErrors.value = [];
  }
}, { immediate: true });

const arraysEqual = (arr1, arr2) => {
  if (arr1.length !== arr2.length) return false;

  return arr1.every((item1, index) => {
    const item2 = arr2[index];
    return (
      item1.id === item2.id &&
      item1.url === item2.url &&
      item1.position === item2.position &&
      item1.name === item2.name
    );
  });
};

const cleanupObjectUrls = () => {
  objectUrls.value.forEach(url => {
    URL.revokeObjectURL(url);
  });
  objectUrls.value.clear();
};

const getPreviewUrl = (file) => {
  try {
    if (file.url && file.isExisting) {
      return file.url;
    }
    if (file instanceof File && file.type && file.type.startsWith('image/')) {
      if (objectUrls.value.has(file)) {
        return objectUrls.value.get(file);
      }
      const url = URL.createObjectURL(file);
      objectUrls.value.set(file, url);

      setTimeout(() => {
        if (objectUrls.value.has(file)) {
          objectUrlsCleanupQueue.add(file);
        }
      }, 30000);

      return url;
    }
    return '';
  } catch (error) {
    return '';
  }
};

const cleanupUnusedUrls = () => {
  objectUrlsCleanupQueue.forEach(file => {
    if (objectUrls.value.has(file) && !selectedFiles.value.includes(file)) {
      URL.revokeObjectURL(objectUrls.value.get(file));
      objectUrls.value.delete(file);
      objectUrlsCleanupQueue.delete(file);
    }
  });
};

const cleanupInterval = setInterval(cleanupUnusedUrls, 10000);

onUnmounted(() => {
  clearInterval(cleanupInterval);
  if (emitTimeout) {
    clearTimeout(emitTimeout);
  }
  cleanupObjectUrls();
});

const dragStart = () => {
  isDragging.value = true;
};

const dragEnd = () => {
  setTimeout(() => {
    isDragging.value = false;
    debouncedEmitChange();
  }, 0);
};

const handlePhotoClick = (index) => {
  if (!isDragging.value) {
    toggleSelection(index);
  }
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event) => {
  const input = event.target;
  if (input.files) {
    addFiles(Array.from(input.files));
  }
};

const handleDrop = (event) => {
  const files = event.dataTransfer?.files;
  if (files) {
    addFiles(Array.from(files));
  }
};

const addFiles = async (files) => {
  const imageFiles = files.filter(file => {
    return file.type.startsWith('image/') &&
      ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(file.type);
  });

  const remainingSlots = props.maxFiles - selectedFiles.value.length;

  if (remainingSlots > 0) {
    const newFiles = imageFiles.slice(0, remainingSlots);

    if (props.isEditing && props.productId && newFiles.length > 0) {
      try {
        if (props.isGiftCard) {
          await productsApi.uploadGiftCardImage(props.productId, newFiles);
          const updatedGiftCard = await productsApi.getGiftCardById(props.productId);
          updatePhotosFromResponse(updatedGiftCard?.data?.photos);
        } else {
          await productsApi.uploadProductImages(props.productId, newFiles);
          const updatedProduct = await productsApi.getById(props.productId);
          updatePhotosFromResponse(updatedProduct?.photos);
        }

        toast.showSuccess(t('product.imagesUploadedSuccessfully'));
        debouncedEmitChange();
      } catch (error) {
        toast.showError(t('product.errorUploadingImages'));
      }
    } else {
      const startIndex = selectedFiles.value.length;

      selectedFiles.value.push(...newFiles);
      selectedIndexes.value = [];

      for (let i = 0; i < newFiles.length; i++) {
        const index = startIndex + i;
        imageLoaded.value[index] = true;
        imageErrors.value[index] = false;
      }

      debouncedEmitChange();
    }
  }
};

const updatePhotosFromResponse = (photos) => {
  if (photos) {
    const sortedPhotos = photos.sort((a, b) => (a.position || 0) - (b.position || 0));
    selectedFiles.value = sortedPhotos.map(photo => ({
      ...photo,
      isExisting: true
    }));

    imageLoaded.value = Array(selectedFiles.value.length).fill(true);
    imageErrors.value = Array(selectedFiles.value.length).fill(false);
  }
};

const isFileObject = (file) => {
  return file instanceof File;
};

const getFileExtension = (filename) => {
  return filename.split('.').pop() || '';
};

const toggleSelection = (index) => {
  const currentIndex = selectedIndexes.value.indexOf(index);
  if (currentIndex === -1) {
    selectedIndexes.value.push(index);
  } else {
    selectedIndexes.value.splice(currentIndex, 1);
  }
};

const selectAll = () => {
  selectedIndexes.value = Array.from({ length: selectedFiles.value.length }, (_, i) => i);
};

const unselectAll = () => {
  selectedIndexes.value = [];
};

const removePhoto = async (index) => {
  const file = selectedFiles.value[index];

  if (file.isExisting && file.url && props.isEditing && props.productId) {
    if(props.isGiftCard){
      try {
        await productsApi.deleteGiftCardImage(props.productId, file.url);
      } catch (error) {
        toast.showError(t('product.errorDeletingImage'));
        return;
      }
    } else {
      try {
        await productsApi.deleteProductImage(props.productId, file.url);
      } catch (error) {
        toast.showError(t('product.errorDeletingImage'));
        return;
      }
    }
  }

  if (objectUrls.value.has(file)) {
    URL.revokeObjectURL(objectUrls.value.get(file));
    objectUrls.value.delete(file);
  }

  selectedFiles.value.splice(index, 1);
  imageLoaded.value.splice(index, 1);
  imageErrors.value.splice(index, 1);

  selectedIndexes.value = selectedIndexes.value
    .filter(i => i !== index)
    .map(i => i > index ? i - 1 : i);

  debouncedEmitChange();
};

const deleteSelected = () => {
  selectedIndexes.value.forEach(index => {
    const file = selectedFiles.value[index];
    if (objectUrls.value.has(file)) {
      URL.revokeObjectURL(objectUrls.value.get(file));
      objectUrls.value.delete(file);
    }
  });

  const remainingFiles = selectedFiles.value.filter((_, index) => !selectedIndexes.value.includes(index));
  selectedFiles.value = remainingFiles;
  selectedIndexes.value = [];
  imageLoaded.value = Array(remainingFiles.length).fill(false);
  imageErrors.value = Array(remainingFiles.length).fill(false);

  debouncedEmitChange();
};

const clearAll = () => {
  cleanupObjectUrls();
  selectedFiles.value = [];
  selectedIndexes.value = [];
  imageLoaded.value = [];
  imageErrors.value = [];
  debouncedEmitChange();
};

const onOrderChange = () => {
  // Não emitir durante drag, só no final
  // A mudança será emitida no dragEnd
};

const emitChange = () => {
  const photosWithPositions = selectedFiles.value.map((item, index) => {
    if (item.isExisting) {
      if (item.position !== index) {
        return {
          ...item,
          position: index
        };
      }
      return item;
    }
    return item;
  });

  isUpdatingFromParent.value = true;

  emit('update:modelValue', photosWithPositions);
  emit('change', photosWithPositions);
};

let emitTimeout = null;
const debouncedEmitChange = () => {
  if (emitTimeout) {
    clearTimeout(emitTimeout);
  }

  emitTimeout = setTimeout(() => {
    emitChange();
    emitTimeout = null;
  }, 50);
};

const handleImageLoad = (index) => {
  imageLoaded.value[index] = true;
};

const handleImageError = (index) => {
  imageErrors.value[index] = true;
};

const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

const getFileName = (file) => {
  if (file.isExisting) {
    return file.name || `Photo ${file.position + 1}`;
  } else if (file instanceof File) {
    return file.name;
  }
  return '';
};

const getFileInfo = (file) => {
  if (file.isExisting) {
    const extension = file.name ? getFileExtension(file.name).toUpperCase() : 'IMG';
    const size = file.size ? formatFileSize(file.size) : 'Unknown size';
    return `${extension} • ${size}`;
  } else if (file instanceof File) {
    return getFileExtension(file.name).toUpperCase() + ' • ' + formatFileSize(file.size);
  }
  return '';
};
</script>

<style scoped>
img {
  display: block !important;
  max-width: 100% !important;
  max-height: 100% !important;
  object-fit: cover !important;
}

.aspect-square {
  position: relative !important;
  background-color: #f3f4f6 !important;
}

.aspect-square img {
  position: absolute !important;
  top: 0 !important;
  left: 0 !important;
  width: 100% !important;
  height: 100% !important;
  object-fit: cover !important;
  z-index: 1 !important;
}

/* Ensure proper layering for overlays */
.aspect-square>.absolute {
  z-index: 2 !important;
}

.aspect-square>.absolute.z-10 {
  z-index: 10 !important;
}
</style>
