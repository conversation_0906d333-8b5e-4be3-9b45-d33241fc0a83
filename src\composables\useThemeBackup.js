import { ref, computed } from 'vue'
import { themeBackupService } from '@/services/themeBackupService'
import { useToast } from '@/services/toast.service'

const backups = ref([])
const backupStats = ref({})
const isLoading = ref(false)
const isCreatingBackup = ref(false)
const isRestoringBackup = ref(false)

export function useThemeBackup() {
  const toast = useToast()

  /**
   * Carrega todos os backups da loja
   */
  const loadBackups = async () => {
    isLoading.value = true
    try {
      const data = await themeBackupService.getBackups()
      backups.value = data
      return data
    } catch (error) {
      console.error('Erro ao carregar backups:', error)
      toast.showError('Não foi possível carregar o histórico de backups.', {
        title: 'Erro ao carregar backups'
      })
      backups.value = []
      return []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Carrega backups de um tema específico
   */
  const loadBackupsByTheme = async (themeId) => {
   
    isLoading.value = true
    try {
    
      const data = await themeBackupService.getBackupsByTheme(themeId)
    
      return data
    } catch (error) {
      console.error('❌ useThemeBackup: Erro ao carregar backups do tema:', error)
      toast.showError('Não foi possível carregar os backups do tema.', {
        title: 'Erro ao carregar backups'
      })
      return []
    } finally {
      isLoading.value = false
    }
  }

  /**
   * Cria backup manual
   */
  const createManualBackup = async (themeId, themeName) => {
    isCreatingBackup.value = true
    try {
      const backup = await themeBackupService.createManualBackup(themeId)
      
      // Adicionar backup à lista local
      backups.value.unshift(backup)
      
      toast.showSuccess(`Backup manual do tema "${themeName}" criado com sucesso.`, {
        title: 'Backup criado'
      })
      
      return backup
    } catch (error) {
      console.error('Erro ao criar backup manual:', error)
      const message = error.response?.data?.error || 'Não foi possível criar o backup.'
      toast.showError(message, {
        title: 'Erro ao criar backup'
      })
      throw error
    } finally {
      isCreatingBackup.value = false
    }
  }

  /**
   * Restaura um backup
   */
  const restoreBackup = async (backup) => {
    isRestoringBackup.value = true
    try {
      await themeBackupService.restoreBackup(backup.id)
      
      // Atualizar contador de restaurações localmente
      const backupIndex = backups.value.findIndex(b => b.id === backup.id)
      if (backupIndex > -1) {
        backups.value[backupIndex].restorationCount++
        backups.value[backupIndex].lastRestoredAt = new Date().toISOString()
      }
      
      toast.showSuccess(`Tema "${backup.themeName}" restaurado com sucesso.`, {
        title: 'Backup restaurado'
      })
      
      return true
    } catch (error) {
      console.error('Erro ao restaurar backup:', error)
      const message = error.response?.data?.error || 'Não foi possível restaurar o backup.'
      toast.showError(message, {
        title: 'Erro ao restaurar backup'
      })
      return false
    } finally {
      isRestoringBackup.value = false
    }
  }

  /**
   * Remove um backup
   */
  const deleteBackup = async (backup) => {
    try {
      await themeBackupService.deleteBackup(backup.id)
      
      // Remover backup da lista local
      const backupIndex = backups.value.findIndex(b => b.id === backup.id)
      if (backupIndex > -1) {
        backups.value.splice(backupIndex, 1)
      }
      
      toast.showSuccess(`Backup do tema "${backup.themeName}" removido com sucesso.`, {
        title: 'Backup removido'
      })
      
      return true
    } catch (error) {
      console.error('Erro ao remover backup:', error)
      const message = error.response?.data?.error || 'Não foi possível remover o backup.'
      toast.showError(message, {
        title: 'Erro ao remover backup'
      })
      return false
    }
  }

  /**
   * Carrega estatísticas de backups
   */
  const loadBackupStats = async () => {
    try {
      const stats = await themeBackupService.getBackupStats()
      backupStats.value = stats
      return stats
    } catch (error) {
      console.error('Erro ao carregar estatísticas de backup:', error)
      backupStats.value = {}
      return {}
    }
  }

  /**
   * Agrupa backups por tema
   */
  const backupsByTheme = computed(() => {
    const grouped = {}
    
    backups.value.forEach(backup => {
      const themeId = backup.themeId
      if (!grouped[themeId]) {
        grouped[themeId] = {
          themeId,
          themeName: backup.themeName,
          backups: []
        }
      }
      grouped[themeId].backups.push(backup)
    })
    
    return Object.values(grouped)
  })

  /**
   * Backups recentes (últimos 10)
   */
  const recentBackups = computed(() => {
    return backups.value.slice(0, 10)
  })

  /**
   * Backups próximos do vencimento
   */
  const backupsNearExpiration = computed(() => {
    return backups.value.filter(backup => 
      themeBackupService.isBackupNearExpiration(backup.expiresAt)
    )
  })

  /**
   * Total de espaço ocupado pelos backups
   */
  const totalBackupSize = computed(() => {
    return backups.value.reduce((total, backup) => total + backup.backupSizeBytes, 0)
  })

  /**
   * Formata dados do backup para exibição
   */
  const formatBackupForDisplay = (backup) => {
    return {
      ...backup,
      formattedSize: themeBackupService.formatFileSize(backup.backupSizeBytes),
      formattedCreatedAt: themeBackupService.formatDate(backup.createdAt),
      formattedExpiresAt: themeBackupService.formatDate(backup.expiresAt),
      formattedLastRestoredAt: themeBackupService.formatDate(backup.lastRestoredAt),
      typeIcon: themeBackupService.getBackupTypeIcon(backup.backupReason),
      typeLabel: themeBackupService.getBackupTypeLabel(backup.backupReason),
      typeColor: themeBackupService.getBackupTypeColor(backup.backupReason),
      isNearExpiration: themeBackupService.isBackupNearExpiration(backup.expiresAt),
      isExpired: themeBackupService.isBackupExpired(backup.expiresAt),
      daysUntilExpiration: themeBackupService.getDaysUntilExpiration(backup.expiresAt)
    }
  }

  return {
    // Estado
    backups,
    backupStats,
    isLoading,
    isCreatingBackup,
    isRestoringBackup,

    // Computed
    backupsByTheme,
    recentBackups,
    backupsNearExpiration,
    totalBackupSize,

    // Métodos
    loadBackups,
    loadBackupsByTheme,
    createManualBackup,
    restoreBackup,
    deleteBackup,
    loadBackupStats,
    formatBackupForDisplay,

    // Utilitários do serviço
    formatFileSize: themeBackupService.formatFileSize,
    formatDate: themeBackupService.formatDate,
    isBackupNearExpiration: themeBackupService.isBackupNearExpiration,
    isBackupExpired: themeBackupService.isBackupExpired
  }
}