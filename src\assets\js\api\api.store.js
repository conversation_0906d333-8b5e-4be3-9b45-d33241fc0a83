import { BaseApi } from "./api"

export default class extends BaseApi{
    api = "http://localhost:8081/store";

    async listAll(success, error){
        super.authenticated()
            .onSuccess(success)
            .onApiError(error)
            .__get(this.api)
    }

    async createStore(store, success, error){
        super.authenticated()
            .withBody(store)
            .onSuccess(success)
            .onApiError(error)
            .__post(this.api)
    }

    async getStore(id, success, error){
        super.authenticated()
            .onSuccess(success)
            .onApiError(error)
            .__get(`${this.api}/${id}`)
    }

    async updateStore(store, success, error){
        super.authenticated()
            .withBody(store)
            .onSuccess(success)
            .onApiError(error)
            .__put(this.api)
    }

    async deleteStore(id, success, error) {
        super.authenticated()
            .onSuccess(success)
            .onApiError(error)
            .__delete(`${this.api}/${id}`)
    }
}