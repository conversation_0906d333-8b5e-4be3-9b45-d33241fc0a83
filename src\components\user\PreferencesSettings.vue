<template>
  <div class="preferences-content">
    <!-- Language and Timezone Preferences -->
    <ViewContainer
      :title="t('userSettings.sections.profile.preferences')"
      :icon="Settings02Icon"
      iconColor="orange"
      class="preferences-container"
    >
      <div class="preferences-grid">
        <div class="preference-item">
          <div class="preference-info">
            <h4 class="preference-title">{{ t('userSettings.sections.profile.language') }}</h4>
            <p class="preference-description">{{ t('userSettings.sections.profile.languageDescription') }}</p>
          </div>
          <IluriaSelect
            v-model="preferencesData.language"
            :options="languageOptions"
            class="preference-select-wrapper"
          />
        </div>

        <div class="preference-item">
          <div class="preference-info">
            <h4 class="preference-title">{{ t('userSettings.sections.profile.timezone') }}</h4>
            <p class="preference-description">{{ t('userSettings.sections.profile.timezoneDescription') }}</p>
          </div>
          <IluriaSelect
            v-model="preferencesData.timezone"
            :options="timezoneOptions"
            class="preference-select-wrapper"
          />
        </div>
      </div>

      <div class="form-actions">
        <IluriaButton 
          @click="savePreferences"
          :loading="isSaving"
          :loadingText="t('userSettings.sections.profile.saving')"
          :icon="FloppyDiskIcon"
          variant="primary"
        >
          {{ t('userSettings.sections.profile.saveChanges') }}
        </IluriaButton>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Settings02Icon,
  FloppyDiskIcon
} from '@hugeicons-pro/core-stroke-standard'
import userService from '@/services/user.service'
import { convertToI18nLocale, convertToBackendLocale } from '@/utils/localeUtils'

// Composables
const { t, locale } = useI18n()
const toast = useToast()

// State
const isSaving = ref(false)

const preferencesData = reactive({
  language: 'pt-br',
  timezone: 'America/Sao_Paulo'
})

// Options for selects
const languageOptions = [
  { label: 'Português (Brasil)', value: 'pt-br' },
  { label: 'English', value: 'en' }
]

const timezoneOptions = [
  { label: 'São Paulo (GMT-3)', value: 'America/Sao_Paulo' },
  { label: 'New York (GMT-5)', value: 'America/New_York' },
  { label: 'London (GMT+0)', value: 'Europe/London' }
]

// Methods
const loadPreferences = async () => {
  try {
    const userPreferences = await userService.getUserPreferences()

    // Carregar preferências do usuário ou usar padrões
    preferencesData.language = userPreferences.language || 'pt-br'
    preferencesData.timezone = userPreferences.timezone || 'America/Sao_Paulo'

    // Aplicar idioma carregado (converter para formato do i18n)
    locale.value = convertToI18nLocale(preferencesData.language)
  } catch (error) {
    console.error('Error loading preferences:', error)
    toast.addToast(t('userSettings.sections.profile.profileLoadError'), 'error')

    // Usar valores padrão em caso de erro
    preferencesData.language = 'pt-br'
    preferencesData.timezone = 'America/Sao_Paulo'
    locale.value = 'pt'
  }
}

const savePreferences = async () => {
  isSaving.value = true

  try {
    // Garantir que enviamos o formato correto para o backend
    const backendLanguage = convertToBackendLocale(preferencesData.language)

    // Salvar preferências no backend
    await userService.updateUserPreferences({
      language: backendLanguage,
      timezone: preferencesData.timezone
    })

    // Aplicar mudança de idioma imediatamente no i18n
    locale.value = convertToI18nLocale(preferencesData.language)

    toast.addToast(t('userSettings.sections.profile.profileUpdated'), 'success')
  } catch (error) {
    console.error('Error saving preferences:', error)
    toast.addToast(t('userSettings.sections.profile.profileUpdateError'), 'error')
  } finally {
    isSaving.value = false
  }
}

// Watch para mudanças no idioma
watch(() => preferencesData.language, (newLanguage) => {
  // Aplicar mudança de idioma em tempo real no i18n
  locale.value = convertToI18nLocale(newLanguage)
})

// Lifecycle
onMounted(() => {
  loadPreferences()
})
</script>

<style scoped>
.preferences-content {
  width: 100%;
  max-width: none;
}

.preferences-container :deep(.container-content) {
  padding: 24px 32px;
  overflow-x: hidden;
}

.preferences-grid {
  display: grid;
  gap: 20px;
  margin-bottom: 24px;
  width: 100%;
  box-sizing: border-box;
}

.preference-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 20px 24px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
  overflow: hidden;
}

.preference-item:hover {
  border-color: var(--iluria-color-border-hover);
  background: var(--iluria-color-hover);
}

.preference-info {
  flex: 1;
  min-width: 200px;
  word-wrap: break-word;
}

.preference-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
}

.preference-description {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.preference-select-wrapper {
  min-width: 250px;
  max-width: 300px;
  flex-shrink: 0;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  padding-top: 20px;
  margin-top: 8px;
}

@media (max-width: 1200px) {
  .preference-select-wrapper {
    min-width: 200px;
    max-width: 250px;
  }
}

@media (max-width: 768px) {
  .preferences-content {
    padding: 0;
  }

  .preferences-container :deep(.container-content) {
    padding: 20px;
  }

  .preference-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px 20px;
    overflow: hidden;
  }

  .preference-select-wrapper {
    min-width: auto;
    width: 100%;
    max-width: 100%;
  }

  .form-actions {
    justify-content: stretch;
    margin-top: 16px;
    padding-top: 16px;
  }
}
</style>
