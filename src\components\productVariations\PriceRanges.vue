<template>
  <div class="mb-6">
      <div class="grid grid-cols-12 gap-4 mb-2">
        <div class="col-span-4">
          <span>{{ t('product.fromQuantity') }}</span>
        </div>

        <div class="col-span-4">
          <span>{{ t('product.price') }}</span>
        </div>
      </div>

      <div v-for="(range, index) in localPriceRanges" :key="index" class="grid grid-cols-12 gap-4 mb-2">
        <div class="col-span-4">
          <div class="flex items-center gap-3">
            <IluriaInputText
              :suffix="getUnitText(range.quantity)"
              type="number"
              v-model.number="range.quantity"
              class="input-field flex-1"
              min="1"
              @update:modelValue="updatePriceRanges"
            />
          </div>
        </div>

        <div class="col-span-4">
          <div class="flex items-center gap-3">
            <IluriaInputText
              prefix="R$"
              type="money"
              v-model="range.price"
              class="input-field flex-1"
              @update:modelValue="updatePriceRanges"
            />
          </div>
        </div>

        <div class="col-span-4 flex items-center">

          <IluriaButton
            @click="onRemoveRange(index)"
            color="danger"
            variant="ghost"
            size="small"
          >
            {{ t('product.remove') }}
          </IluriaButton>
        </div>
      </div>

      <IluriaButton
        @click="onAddPriceRange"
        color="dark"
        variant="outline"
        class="mt-2"
        :disabled="!canAddPriceRange"
      >
        {{ t('product.addPriceRangeButton') }}
      </IluriaButton>
  </div>
  </template>

  <script setup>
  import { ref, computed, watch } from 'vue';
  import { useI18n } from 'vue-i18n';
  import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
  import IluriaButton from '@/components/iluria/IluriaButton.vue';

  const { t } = useI18n();

  const emit = defineEmits(['addPriceRange', 'removeRange', 'updatePriceRanges']);

  const props = defineProps({
    priceRanges: {
      type: Array,
      default: () => []
    }
  });

  const localPriceRanges = ref([]);

  watch(() => props.priceRanges, (newValue) => {
    if (newValue && newValue.length > 0) {
      localPriceRanges.value = JSON.parse(JSON.stringify(newValue));
    } else{
      localPriceRanges.value = [{ quantity: 1, price: 0 }];
    }
  }, { immediate: true, deep: true });
  
  const canAddPriceRange = computed(() => {
    return localPriceRanges.value.length < 5;
  });

  // Função para retornar o texto da unidade baseado na quantidade
  const getUnitText = (quantity) => {
    return quantity > 1 ? 'unidades' : 'unidade';
  };

  const onAddPriceRange = () => {
    if (canAddPriceRange.value) {
      localPriceRanges.value.push({ quantity: 0, price: 0 });
      emit('addPriceRange');
      updatePriceRanges();
    }
  };

  const onRemoveRange = (index) => {
    localPriceRanges.value.splice(index, 1);
    emit('removeRange', index);
    updatePriceRanges();
  };

  const updatePriceRanges = () => {
    emit('updatePriceRanges', JSON.parse(JSON.stringify(localPriceRanges.value)));
  };
  </script>