<template>
  <div>
    <div class="mb-4">
      <h3 class="text-lg font-semibold mb-3">{{ t('order.shipping') }}</h3>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <IluriaInputText
          id="shippingType"
          v-model="localForm.shippingType"
          :label="t('order.shippingType')"
          placeholder="Correios"
        />
        <IluriaInputText
          id="shippingValue"
          v-model="localForm.shippingValue"
          type="money"
          :label="t('order.shippingValue')"
          placeholder="19,90"
          prefix="R$"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <IluriaInputText
          id="shippingDescription"
          v-model="localForm.shippingDescription"
          :label="t('order.shippingDescription')"
          placeholder="PAC - Entrega em até 7 dias úteis"
        />
        <IluriaInputText
          id="deliveryTime"
          v-model="localForm.deliveryTime"
          type="number"
          :label="t('order.deliveryDays')"
          placeholder="7"
          suffix=" dias"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
        <IluriaInputText
          id="trackingCode"
          v-model="localForm.trackingCode"
          :label="t('order.trackingCode')"
          placeholder="BR123456789"
        />
        <IluriaInputText
          id="shippingDiscount"
          v-model="localForm.shippingDiscount"
          type="money"
          :label="t('order.discountShipping')"
          placeholder="0,00"
          prefix="R$"
        />
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
        <IluriaInputText
          id="shippingExternalId"
          v-model="localForm.shippingExternalId"
          :label="t('order.shippingExternalId')"
        />
        <IluriaInputText
          id="shippingExternalProtocol"
          v-model="localForm.shippingExternalProtocol"
          :label="t('order.shippingExternalProtocol')"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { reactive, watch, defineProps, defineEmits } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'

const { t } = useI18n()

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({
      shippingType: '',
      shippingValue: '',
      shippingDescription: '',
      deliveryTime: '',
      trackingCode: '',
      shippingExternalId: '',
      shippingExternalProtocol: '',
      shippingDiscount: '',
    })
  }
})

const emit = defineEmits(['update:modelValue'])

const localForm = reactive({ ...props.modelValue })

watch(
  () => props.modelValue,
  (newVal) => {
    Object.assign(localForm, {
      shippingType: newVal.shippingType || '',
      shippingValue: newVal.shippingValue || '',
      shippingDescription: newVal.shippingDescription || '',
      deliveryTime: newVal.deliveryTime || '',
      trackingCode: newVal.trackingCode || '',
      shippingExternalId: newVal.shippingExternalId || '',
      shippingExternalProtocol: newVal.shippingExternalProtocol || '',
      shippingDiscount: newVal.shippingDiscount || '',
    })
  },
  { immediate: true, deep: true }
)

watch(
  () => localForm,
  (newVal) => {
    const shippingUpdates = {
      shippingType: newVal.shippingType,
      shippingValue: newVal.shippingValue,
      shippingDescription: newVal.shippingDescription,
      deliveryTime: newVal.deliveryTime,
      trackingCode: newVal.trackingCode,
      shippingExternalId: newVal.shippingExternalId,
      shippingExternalProtocol: newVal.shippingExternalProtocol,
      shippingDiscount: newVal.shippingDiscount,
    }
    
    emit('update:modelValue', {
      ...props.modelValue,
      ...shippingUpdates
    })
  },
  { deep: true }
)
</script>
