<template>
  <div class="theme-card-skeleton">
    <!-- Preview Section -->
    <div class="skeleton-preview">
      <div class="skeleton-shimmer skeleton-preview-content"></div>
      
      <!-- Badge skeletons -->
      <div class="skeleton-badge skeleton-badge--top-left"></div>
      <div class="skeleton-badge skeleton-badge--bottom-right"></div>
    </div>
    
    <!-- Theme Info Section -->
    <div class="skeleton-info">
      <!-- Theme header -->
      <div class="skeleton-header">
        <div class="skeleton-shimmer skeleton-title"></div>
        <div class="skeleton-shimmer skeleton-description"></div>
      </div>
      
      <!-- Tags -->
      <div class="skeleton-tags">
        <div class="skeleton-shimmer skeleton-tag" v-for="i in 3" :key="i"></div>
      </div>

      <!-- Actions -->
      <div class="skeleton-actions">
        <div class="skeleton-shimmer skeleton-button skeleton-button--primary"></div>
        <div class="skeleton-shimmer skeleton-button skeleton-button--secondary"></div>
        <div class="skeleton-shimmer skeleton-button skeleton-button--icon"></div>
      </div>
      
      <!-- Meta info -->
      <div class="skeleton-meta">
        <div class="skeleton-shimmer skeleton-meta-item" v-for="i in 3" :key="i"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
// No props needed for skeleton
</script>

<style scoped>
.theme-card-skeleton {
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 16px;
  overflow: hidden;
  position: relative;
  animation: skeleton-fade-in 0.4s ease-out;
}

@keyframes skeleton-fade-in {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Preview Section */
.skeleton-preview {
  position: relative;
  aspect-ratio: 16/10;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  overflow: hidden;
}

.skeleton-preview-content {
  width: 100%;
  height: 100%;
  border-radius: 0;
}

.skeleton-badge {
  position: absolute;
  border-radius: 25px;
  height: 28px;
}

.skeleton-badge--top-left {
  top: 0.75rem;
  left: 0.75rem;
  width: 80px;
}

.skeleton-badge--bottom-right {
  bottom: 0.75rem;
  right: 0.75rem;
  width: 100px;
}

/* Theme Info */
.skeleton-info {
  padding: 1.75rem;
  display: flex;
  flex-direction: column;
  gap: 1.25rem;
}

.skeleton-header {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.skeleton-title {
  height: 1.75rem;
  width: 70%;
  border-radius: 8px;
}

.skeleton-description {
  height: 1rem;
  width: 90%;
  border-radius: 6px;
}

/* Tags */
.skeleton-tags {
  display: flex;
  gap: 0.6rem;
  flex-wrap: wrap;
}

.skeleton-tag {
  height: 24px;
  width: 60px;
  border-radius: 25px;
}

.skeleton-tag:nth-child(2) {
  width: 80px;
}

.skeleton-tag:nth-child(3) {
  width: 45px;
}

/* Actions */
.skeleton-actions {
  display: flex;
  gap: 0.85rem;
  padding-top: 0.5rem;
  border-top: 1px solid var(--iluria-color-border);
}

.skeleton-button {
  height: 36px;
  border-radius: 8px;
}

.skeleton-button--primary {
  width: 90px;
}

.skeleton-button--secondary {
  width: 70px;
}

.skeleton-button--icon {
  width: 36px;
}

/* Meta */
.skeleton-meta {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
  padding-top: 1rem;
  border-top: 1px solid var(--iluria-color-border);
}

.skeleton-meta-item {
  height: 12px;
  width: 80px;
  border-radius: 4px;
}

.skeleton-meta-item:nth-child(2) {
  width: 60px;
}

.skeleton-meta-item:nth-child(3) {
  width: 90px;
}

/* Shimmer animation */
.skeleton-shimmer {
  background: linear-gradient(
    90deg,
    var(--iluria-color-background) 25%,
    rgba(var(--iluria-color-primary-rgb), 0.05) 50%,
    var(--iluria-color-background) 75%
  );
  background-size: 200% 100%;
  animation: skeleton-shimmer 1.5s infinite;
  position: relative;
  overflow: hidden;
}

.skeleton-shimmer::before {
  content: '';
  position: absolute;
  inset: 0;
  background: linear-gradient(
    90deg,
    transparent 0%,
    rgba(255, 255, 255, 0.4) 50%,
    transparent 100%
  );
  animation: skeleton-shine 2s infinite;
}

@keyframes skeleton-shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes skeleton-shine {
  0% {
    transform: translateX(-100%);
  }
  100% {
    transform: translateX(100%);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .skeleton-info {
    padding: 1.5rem;
  }
  
  .skeleton-actions {
    flex-direction: column;
  }
  
  .skeleton-meta {
    justify-content: center;
  }
}
</style>