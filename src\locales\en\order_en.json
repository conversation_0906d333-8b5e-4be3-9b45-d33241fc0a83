{"title": "Orders", "orders": "Orders", "ordersListTitle": "Orders List", "ordersListSubtitle": "View and manage all orders from your store", "ordersManagement": "Manage all orders placed by customers", "loadingOrders": "Loading orders...", "noOrdersDescription": "You don't have any orders yet. When customers make purchases, they will appear here.", "createFirstOrder": "Create First Order", "editOrder": "Edit order", "deleteOrder": "Delete order", "confirmDeleteTitle": "Confirm deletion", "confirmDeleteMessage": "Are you sure you want to delete order #{orderNumber}? This action cannot be undone.", "customerDataTitle": "Customer Data", "customerDataSubtitle": "Select the customer who will place the order", "shippingAddressSubtitle": "Configure where the order will be delivered", "addNewAddress": "Add New Address", "productsTitle": "Order Products", "productsSubtitle": "Add and configure the order products", "addProduct": "Add Product", "noProductsDescription": "Add products to the order to continue", "shippingTitle": "Shipping Configuration", "shippingSubtitle": "Configure the shipping method and value", "discountTitle": "Discount", "discountSubtitle": "Configure discounts applied to the order", "notesTitle": "Notes", "notesSubtitle": "Add internal notes about the order", "orderStatusTitle": "Order Status", "orderStatusSubtitle": "Configure the current order status", "createOrderSubtitle": "Create a new order manually for your customers", "updateOrderSubtitle": "Update this order information", "viewOrder": "View Order", "viewOrderSubtitle": "View the details of this order", "new": "New Order", "edit": "Edit Order", "delete": "Delete Order", "noOrders": "No orders found", "searchPlaceholder": "Search orders...", "selectCustomer": "Select a customer", "searchCustomer": "Search customer", "selectCustomerFirst": "Select a customer first to configure the shipping address", "selectShippingAddressFirst": "Select a shipping address", "all": "All Orders", "loadError": "Error loading orders", "viewOrders": "View Orders", "pending": "Pending", "shipped": "Shipped", "newOrder": "New Order", "code": "Code", "customerName": "Customer Name", "customerEmail": "Customer <PERSON><PERSON>", "customerPhone": "Customer Mobile", "createdAt": "Created At", "total": "Total", "statusTitle": "Status", "selectStatus": "Select a status", "active": "Active", "inactive": "Inactive", "confirmDelete": "Are you sure you want to delete this order?", "deleteSuccess": "Order deleted successfully", "deleteError": "Error deleting order", "processing": "Processing", "delivered": "Delivered", "cancelled": "Cancelled", "orderSummary": "Order Summary", "manualOrderCreation": "Manual Order Creation", "createSuccess": "Order created successfully", "createError": "Error creating order", "updateSuccess": "Order updated successfully", "updateError": "Error updating order", "selectStore": "Select a store", "price": "Price", "shippingAddress": "Shipping Address", "selectAddress": "Select Address", "searchAddress": "Search Address", "shipping": "Shipping", "shippingType": "Shipping Type", "shippingMethod": "Shipping Method", "selectShippingMethod": "Select a shipping method", "shippingValue": "Shipping Value", "deliveryDays": "Delivery Days", "pickupInstructions": "Pickup Instructions", "discount": "Discount", "discountType": "Discount Type", "discountValue": "Discount Value", "discountPercentage": "Discount Percentage", "discountDescription": "Discount Description", "discountFixed": "Fixed Value (R$)", "discountPercent": "Percentage (%)", "discountProduct": "Discount on Products", "discountShipping": "Discount on Shipping", "discountTotal": "Discount on Total", "discountFreeShipping": "Free Shipping", "free": "Free", "selectShippingAddressPrompt": "Select a shipping address", "notes": "Notes", "orderDescription": "Order Description", "internalNotes": "Internal Notes", "customerNotes": "Customer Notes", "subtotal": "Subtotal", "pac": "PAC", "sedex": "SEDEX", "pickup": "Pickup", "free_shipping": "Free Shipping", "shippingDiscountDescription": "Shipping Discount Description", "productsDiscountValue": "Products Discount Value", "annotation": "Annotation", "shippingStatus": "Shipping Status", "paymentStatus": "Payment Status", "sourceDevice": "Source Device", "shippingExternalProtocol": "External Shipping Protocol", "shippingExternalId": "External Shipping ID", "trackingCode": "Tracking Code", "shippingDescription": "Shipping Description", "unpaid": "Unpaid", "paid": "Paid", "action": "Actions", "productsDiscountDescription": "Products Discount Description", "productsDiscountDescriptionPlaceholder": "Free shipping for purchases over R$ 100", "annotationPlaceholder": "Deliver in the afternoon period", "shippingDiscount": "Shipping Discount", "noProductsAdded": "No products added", "exceedingStock": "Insufficient stock for the product", "status": {"pending": "Pending", "processing": "Processing", "shipped": "Shipped", "delivered": "Delivered", "cancelled": "Cancelled", "refunded": "Refunded"}, "fields": {"orderNumber": "Order #", "date": "Date", "customer": "Customer", "total": "Total", "status": "Status", "shippingStatus": "Shipping Status", "paymentStatus": "Payment Status", "actions": "Actions", "billingAddress": "Billing Address", "shippingAddress": "Shipping Address", "paymentMethod": "Payment Method", "shippingMethod": "Shipping Method", "items": "Items", "quantity": "Qty", "price": "Price", "subtotal": "Subtotal", "discount": "Discount", "shipping": "Shipping", "tax": "Tax"}, "messages": {"loadError": "Error loading orders", "deleteConfirm": "Are you sure you want to delete this order?", "deleteSuccess": "Order deleted successfully", "deleteError": "Error deleting order", "statusUpdated": "Order status updated successfully", "statusUpdateError": "Error updating order status"}, "details": {"title": "Order Details", "customerInfo": "Customer Information", "contactInfo": "Contact Information", "shippingInfo": "Shipping Information", "billingInfo": "Billing Information", "paymentInfo": "Payment Information", "orderSummary": "Order Summary"}, "actions": {"updateStatus": "Update Status", "print": "Print", "email": "Send Invoice by Email", "refund": "Issue Refund", "cancel": "Cancel Order"}}