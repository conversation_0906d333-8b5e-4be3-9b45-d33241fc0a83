{"name": "Name", "type": "Type", "status": "Status", "sslStatus": "SSL Status", "actions": "Actions", "title": "Domain Manager", "subtitle": "Manage your domains and redirections", "formTitle": "Add Domain", "editFormTitle": "Edit Domain", "formSubtitle": "Configure a new domain for your store", "editFormSubtitle": "Edit domain settings", "new": "New Domain", "empty": "No domains registered", "emptyDescription": "Configure your first domain to get started", "loadingDomains": "Loading domains...", "mainWarningDescription": "You need to configure a main domain for the store to function", "confirmDeleteMessage": "Are you sure you want to delete this domain: ", "confirmDeleteTitle": "Delete Domain", "createSuccess": "Domain created successfully", "createError": "Error creating domain", "updateSuccess": "Domain updated successfully", "updateError": "Error updating domain", "deleteSuccess": "Domain deleted successfully", "deleteError": "Error deleting domain", "loadError": "Error loading domain", "loadListError": "Error loading domain list", "mainWarning": "You must have at least one main domain, edit or add", "connected": "Connected", "notConnected": "Not connected", "main": "Main", "redirection": "Redirection", "validate": "Validate", "validation": "Validation", "validateTitle": "Validating", "validateSubtitle": "Verifying the domain configuration", "validateError": "Domain validation failed", "validateSuccess": "Domain validated successfully", "validateNS": "Server of names for the domain ...", "validateCNAME": "CNAME record for the domain ...", "tryAgain": "Try again", "back": "Back", "success": "Success", "failed": "Failed", "verifying": "Verifying...", "domainSettings": "Domain Settings", "domainSettingsSubtitle": "Define the domain name and type", "mainDomainTitle": "Main Domain", "mainDomainSubtitle": "The main domain is used as the primary address for your store", "mainDomainTip": "Only one domain can be main. All redirections will point to this domain.", "redirectionTitle": "Redirection", "redirectionSubtitle": "Redirection domains automatically direct to the main domain", "redirectionTip": "Configure alternative domains that will redirect visitors to your main domain.", "namePlaceholder": "example.com", "ssl": {"processing": "SSL Processing", "certificate": "SSL Certificate", "active": "SSL Active", "error": "SSL Error", "pending": "Processing", "activeStatus": "Active", "failed": "Failed", "inactive": "Inactive", "processingMessage": "SSL certificate being processed", "processingMultipleMessage": "SSL certificates being processed", "generatingMessage": "Generating SSL certificate for", "successMessage": "SSL certificate generated successfully for", "failedMessage": "Failed to generate SSL certificate for"}}