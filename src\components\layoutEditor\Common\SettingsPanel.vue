<template>
  <div class="settings-panel">
    <h3 class="settings-title">{{ $t('layoutEditor.pageSettings') }}</h3>
    
    <!-- Switcher de Categorias -->
    <div class="category-switcher">
      <button 
        @click="selectCategory('basic')"
        :class="['category-btn', { active: currentCategory === 'basic' }]"
      >
        {{ $t('layoutEditor.pageInfo') }}
      </button>
      <button 
        @click="selectCategory('layout')"
        :class="['category-btn', { active: currentCategory === 'layout' }]"
      >
        {{ $t('layoutEditor.layoutSettings') }}
      </button>
      <button 
        @click="selectCategory('background')"
        :class="['category-btn', { active: currentCategory === 'background' }]"
      >
        {{ $t('layoutEditor.backgroundSettings') }}
      </button>
    </div>

    <!-- Categoria Página -->
    <div v-if="currentCategory === 'basic'" class="category-content">
      <div class="settings-section">
        <h4 class="section-title">{{ $t('layoutEditor.pageInfo') }}</h4>
        
        <!-- Título da Página -->
        <div class="setting-group">
          <label for="pageTitle" class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.pageTitle') }}</span>
          </label>
          <IluriaInputText 
            id="pageTitle"
            v-model="localSettings.pageTitle"
            @update:modelValue="updateSettings"
            :placeholder="$t('layoutEditor.pageTitlePlaceholder')"
          />
        </div>

        <!-- Logo da Página -->
        <div class="setting-group">
          <label for="pageLogo" class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.pageLogo') }}</span>
          </label>
          <div class="logo-field">
            <IluriaInputText 
              id="pageLogo"
              v-model="localSettings.pageLogo"
              @update:modelValue="updateSettings"
              :placeholder="$t('layoutEditor.pageLogoPlaceholder')"
            />
            <div v-if="localSettings.pageLogo" class="logo-preview">
              <img :src="localSettings.pageLogo" alt="Logo Preview" />
            </div>
          </div>
        </div>

        <!-- Favicon -->
        <div class="setting-group">
          <label for="pageFavicon" class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.pageFavicon') }}</span>
          </label>
          <div class="favicon-field">
            <IluriaInputText 
              id="pageFavicon"
              v-model="localSettings.pageFavicon"
              @update:modelValue="updateSettings"
              :placeholder="$t('layoutEditor.pageFaviconPlaceholder')"
            />
            <div v-if="localSettings.pageFavicon" class="favicon-preview">
              <img :src="localSettings.pageFavicon" alt="Favicon Preview" />
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Categoria Layout -->
    <div v-if="currentCategory === 'layout'" class="category-content">
      <div class="settings-section">
        <h4 class="section-title">{{ $t('layoutEditor.layoutSettings') }}</h4>
        
        <!-- Largura do Conteúdo -->
        <div class="setting-group">
          <label for="contentWidth" class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.contentWidth') }}</span>
            <span class="value-display">
              {{ localSettings.contentWidth <= 100 ? localSettings.contentWidth + '%' : localSettings.contentWidth + 'px' }}
            </span>
          </label>
          
          <!-- Botões de largura rápida -->
          <div class="width-quick-actions">
            <button 
              @click="setContentWidth(25)" 
              :class="['quick-btn', { active: localSettings.contentWidth === 25 }]"
            >
              25%
            </button>
            <button 
              @click="setContentWidth(50)" 
              :class="['quick-btn', { active: localSettings.contentWidth === 50 }]"
            >
              50%
            </button>
            <button 
              @click="setContentWidth(75)" 
              :class="['quick-btn', { active: localSettings.contentWidth === 75 }]"
            >
              75%
            </button>
            <button 
              @click="setContentWidth(100)" 
              :class="['quick-btn', { active: localSettings.contentWidth === 100 }]"
            >
              100%
            </button>
          </div>
          
          <IluriaRange
            id="contentWidth"
            v-model="localSettings.contentWidth"
            :min="10"
            :max="100"
            :step="5"
            @change="updateSettings"
            show-value
            unit="%"
          />
        </div>
      </div>
    </div>

    <!-- Categoria Background -->
    <div v-if="currentCategory === 'background'" class="category-content">
      <div class="settings-section">
        <h4 class="section-title">{{ $t('layoutEditor.backgroundSettings') }}</h4>
        
        <!-- Tipo de Background -->
        <div class="setting-group">
          <label class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.backgroundType') }}</span>
          </label>
          <div class="background-type-selector">
            <button 
              v-for="type in backgroundTypes"
              :key="type.value"
              @click="selectBackgroundType(type.value)"
              :class="['type-btn', { active: localSettings.backgroundType === type.value }]"
            >
              {{ type.label }}
            </button>
          </div>
        </div>

        <!-- Cor Sólida -->
        <div v-if="localSettings.backgroundType === 'solid'" class="setting-group">
          <label for="bgColor" class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.backgroundColor') }}</span>
          </label>
          <IluriaColorPicker
            id="bgColor"
            v-model="localSettings.backgroundColor"
            @change="updateSettings"
          />
        </div>

        <!-- Gradiente -->
        <div v-if="localSettings.backgroundType === 'gradient'" class="setting-group">
          <label class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.gradientSettings') }}</span>
          </label>
          
          <!-- Tipo de Gradiente -->
          <div class="control-group">
            <label class="sub-label">{{ $t('layoutEditor.gradientType') }}</label>
            <IluriaSelect 
              v-model="localSettings.gradientType" 
              @update:modelValue="updateSettings"
              :options="[
                { value: 'linear', label: $t('layoutEditor.linear') },
                { value: 'radial', label: $t('layoutEditor.radial') }
              ]"
            />
          </div>

          <!-- Ângulo (apenas para linear) -->
          <div v-if="localSettings.gradientType === 'linear'" class="control-group">
            <label class="sub-label">{{ $t('layoutEditor.angle') }}</label>
            <IluriaRange
              v-model="localSettings.gradientAngle"
              @change="updateSettings"
              :min="0"
              :max="360"
              :step="1"
              show-value
              suffix="°"
            />
          </div>

          <!-- Cores do Gradiente -->
          <div class="gradient-colors">
            <div v-for="(stop, index) in localSettings.gradientStops" :key="index" class="gradient-stop">
              <IluriaColorPicker
                v-model="stop.color"
                @change="updateSettings"
              />
              <IluriaRange
                v-model="stop.position"
                @change="updateSettings"
                :min="0"
                :max="100"
                show-value
                suffix="%"
              />
            </div>
          </div>
        </div>

        <!-- Imagem de Fundo -->
        <div v-if="localSettings.backgroundType === 'image'" class="setting-group">
          <label class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.backgroundImage') }}</span>
          </label>
          
          <!-- URL da Imagem -->
          <IluriaInputText
            v-model="localSettings.backgroundImage"
            @update:modelValue="updateSettings"
            :placeholder="$t('layoutEditor.backgroundImagePlaceholder')"
          />
          
          <!-- Preview da Imagem -->
          <div v-if="localSettings.backgroundImage" class="image-preview" :style="imagePreviewStyle"></div>
          
          <!-- Configurações da Imagem -->
          <div class="image-controls">
            <div class="control-group">
              <label class="sub-label">{{ $t('layoutEditor.imageSize') }}</label>
              <IluriaSelect 
                v-model="localSettings.imageSize" 
                @update:modelValue="updateSettings"
                :options="[
                  { value: 'cover', label: $t('layoutEditor.cover') },
                  { value: 'contain', label: $t('layoutEditor.contain') },
                  { value: 'auto', label: $t('layoutEditor.auto') }
                ]"
              />
            </div>
            
            <div class="control-group">
              <label class="sub-label">{{ $t('layoutEditor.imagePosition') }}</label>
              <IluriaSelect 
                v-model="localSettings.imagePosition" 
                @update:modelValue="updateSettings"
                :options="[
                  { value: 'center center', label: $t('layoutEditor.center') },
                  { value: 'top left', label: $t('layoutEditor.topLeft') },
                  { value: 'top center', label: $t('layoutEditor.topCenter') },
                  { value: 'top right', label: $t('layoutEditor.topRight') }
                ]"
              />
            </div>
          </div>
        </div>

        <!-- Presets de Background -->
        <div v-if="localSettings.backgroundType === 'preset'" class="setting-group">
          <label class="setting-label">
            <span class="label-text">{{ $t('layoutEditor.backgroundPresets') }}</span>
          </label>
          <div class="presets-grid">
            <div 
              v-for="preset in backgroundPresets"
              :key="preset.id"
              @click="selectPreset(preset)"
              :class="['preset-item', { active: localSettings.selectedPreset === preset.id }]"
              :style="preset.style"
            >
              <span class="preset-name">{{ preset.name }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- Botões de Ação -->
    <div class="form-actions">
      <button @click="resetToDefaults" class="reset-btn">
        {{ $t('layoutEditor.resetDefaults') }}
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, inject, computed, onMounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import { useTheme } from '@/composables/useTheme.js'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'

const emit = defineEmits(['update:settings'])
const { t } = useI18n()

// Inicializar tema
useTheme()

// Estado da categoria atual
const currentCategory = ref('basic')

// Injeta as configurações globais da página
const globalPageSettings = inject('pageSettings', ref({
  backgroundColor: '#ffffff',
  contentWidth: 75,
  pageTitle: '',
  pageLogo: '',
  pageFavicon: '',
  backgroundType: 'solid',
  gradientType: 'linear',
  gradientAngle: 90,
  gradientStops: [
    { color: '#ffffff', position: 0 },
    { color: '#000000', position: 100 }
  ],
  backgroundImage: '',
  imageSize: 'cover',
  imagePosition: 'center center',
  selectedPreset: null
}))

// Settings locais para o componente
const localSettings = ref({
  backgroundColor: '#ffffff',
  contentWidth: 75,
  pageTitle: '',
  pageLogo: '',
  pageFavicon: '',
  backgroundType: 'solid',
  gradientType: 'linear',
  gradientAngle: 90,
  gradientStops: [
    { color: '#ffffff', position: 0 },
    { color: '#000000', position: 100 }
  ],
  backgroundImage: '',
  imageSize: 'cover',
  imagePosition: 'center center',
  selectedPreset: null
})

// Tipos de background disponíveis - usando traduções
const backgroundTypes = computed(() => [
  { label: t('layoutEditor.backgroundTypes.solid'), value: 'solid' },
  { label: t('layoutEditor.backgroundTypes.gradient'), value: 'gradient' },
  { label: t('layoutEditor.backgroundTypes.image'), value: 'image' },
  { label: t('layoutEditor.backgroundTypes.preset'), value: 'preset' }
])

// Presets de background creativos - usando traduções
const backgroundPresets = computed(() => [
  {
    id: 'sunset',
    name: t('layoutEditor.presets.sunset'),
    style: {
      background: 'linear-gradient(135deg, #ff9a8b 0%, #a8e6cf 100%)'
    },
    css: 'linear-gradient(135deg, #ff9a8b 0%, #a8e6cf 100%)'
  },
  {
    id: 'ocean',
    name: t('layoutEditor.presets.ocean'),
    style: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    css: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    id: 'forest',
    name: t('layoutEditor.presets.forest'),
    style: {
      background: 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)'
    },
    css: 'linear-gradient(135deg, #56ab2f 0%, #a8e6cf 100%)'
  },
  {
    id: 'galaxy',
    name: t('layoutEditor.presets.galaxy'),
    style: {
      background: 'linear-gradient(135deg, #2c3e50 0%, #4a6741 50%, #f093fb 100%)'
    },
    css: 'linear-gradient(135deg, #2c3e50 0%, #4a6741 50%, #f093fb 100%)'
  },
  {
    id: 'warm',
    name: t('layoutEditor.presets.warm'),
    style: {
      background: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
    },
    css: 'linear-gradient(135deg, #fa709a 0%, #fee140 100%)'
  },
  {
    id: 'cool',
    name: t('layoutEditor.presets.cool'),
    style: {
      background: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
    },
    css: 'linear-gradient(135deg, #a8edea 0%, #fed6e3 100%)'
  },
  {
    id: 'corporate',
    name: t('layoutEditor.presets.corporate'),
    style: {
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    },
    css: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
  },
  {
    id: 'minimal',
    name: t('layoutEditor.presets.minimal'),
    style: {
      background: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
    },
    css: 'linear-gradient(135deg, #f5f7fa 0%, #c3cfe2 100%)'
  }
])

// Computed para preview da imagem
const imagePreviewStyle = computed(() => {
  return {
    backgroundImage: `url(${localSettings.value.backgroundImage})`,
    backgroundSize: localSettings.value.imageSize,
    backgroundPosition: localSettings.value.imagePosition,
    backgroundRepeat: 'no-repeat'
  }
})

// Métodos
const selectCategory = (category) => {
  currentCategory.value = category
}

const selectBackgroundType = (type) => {
  localSettings.value.backgroundType = type
  updateSettings()
}

const selectPreset = (preset) => {
  localSettings.value.selectedPreset = preset.id
  localSettings.value.backgroundType = 'preset'
  // Simula o CSS do preset como background personalizado
  localSettings.value.presetCss = preset.css
  updateSettings()
}

const setContentWidth = (width) => {
  // Converter valores antigos para o novo sistema de porcentagem
  if (width >= 1000) {
    if (width >= 9999) width = 100
    else if (width >= 8500) width = 75
    else if (width >= 8000) width = 50
    else if (width >= 2000) width = 75
    else width = 50
  }
  
  localSettings.value.contentWidth = width
  updateSettings()
}

const updateSettings = () => {
  // Aplica as configurações locais nas globais
  Object.assign(globalPageSettings.value, localSettings.value)
  
  // Emite as configurações atualizadas
  emit('update:settings', { ...localSettings.value })
}

const resetToDefaults = () => {
  currentCategory.value = 'basic'
  localSettings.value = {
    backgroundColor: '#ffffff',
    contentWidth: 75,
    pageTitle: '',
    pageLogo: '',
    pageFavicon: '',
    backgroundType: 'solid',
    gradientType: 'linear',
    gradientAngle: 90,
    gradientStops: [
      { color: '#ffffff', position: 0 },
      { color: '#000000', position: 100 }
    ],
    backgroundImage: '',
    imageSize: 'cover',
    imagePosition: 'center center',
    selectedPreset: null
  }
  updateSettings()
}

// Carrega os valores atuais quando o componente é montado
const loadCurrentSettings = () => {
  if (globalPageSettings.value) {
    // Copia os valores globais para o local
    Object.assign(localSettings.value, globalPageSettings.value)
    
    // Converter valores antigos para o novo sistema de porcentagem
    if (localSettings.value.contentWidth >= 1000) {
      if (localSettings.value.contentWidth >= 9999) localSettings.value.contentWidth = 100
      else if (localSettings.value.contentWidth >= 8500) localSettings.value.contentWidth = 75
      else if (localSettings.value.contentWidth >= 8000) localSettings.value.contentWidth = 50
      else if (localSettings.value.contentWidth >= 2000) localSettings.value.contentWidth = 75
      else localSettings.value.contentWidth = 50
      
      // Atualizar também o valor global
      globalPageSettings.value.contentWidth = localSettings.value.contentWidth
    }
  }
}

onMounted(() => {
  nextTick(() => {
    loadCurrentSettings()
  })
})

// Observa mudanças nas configurações globais
watch(() => globalPageSettings.value, (newSettings) => {
  if (newSettings) {
    Object.assign(localSettings.value, newSettings)
    
    // Converter valores antigos para o novo sistema de porcentagem
    if (localSettings.value.contentWidth >= 1000) {
      if (localSettings.value.contentWidth >= 9999) localSettings.value.contentWidth = 100
      else if (localSettings.value.contentWidth >= 8500) localSettings.value.contentWidth = 75
      else if (localSettings.value.contentWidth >= 8000) localSettings.value.contentWidth = 50
      else if (localSettings.value.contentWidth >= 2000) localSettings.value.contentWidth = 75
      else localSettings.value.contentWidth = 50
    }
  }
}, { deep: true, immediate: true })

defineExpose({
  localSettings,
  loadCurrentSettings
})
</script>

<style scoped>
.settings-panel {
  padding: 1.5rem;
  color: var(--iluria-color-text-primary);
  background-color: var(--iluria-color-surface);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg);
  max-height: 600px;
  overflow-y: auto;
}

.settings-title {
  font-size: 1.25rem;
  font-weight: 600;
  margin-bottom: 2rem;
  color: var(--iluria-color-text-primary);
  border-bottom: 1px solid var(--iluria-color-border);
  padding-bottom: 0.75rem;
}

.settings-section {
  margin-bottom: 2rem;
}

.section-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 1rem;
}

.setting-group {
  margin-bottom: 1.5rem;
  background: var(--iluria-color-background);
  border-radius: 8px;
  padding: 1rem;
  border: 1px solid var(--iluria-color-border);
}

.setting-label {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.label-text {
  font-size: 0.875rem;
}

.sub-label {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin-bottom: 0.5rem;
}

.value-display {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  font-family: monospace;
}

.text-input {
  width: 100%;
  padding: 0.5rem;
  background: var(--iluria-color-input-bg);
  border: 1px solid var(--iluria-color-input-border);
  border-radius: 0.375rem;
  color: var(--iluria-color-input-text);
  font-size: 0.875rem;
}

.text-input:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus);
}

.logo-field,
.favicon-field {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.logo-preview,
.favicon-preview {
  display: flex;
  justify-content: center;
  align-items: center;
  height: 60px;
  background: var(--iluria-color-background);
  border-radius: 0.375rem;
  border: 1px solid var(--iluria-color-border);
}

.width-quick-actions {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 1rem;
  flex-wrap: wrap;
}

.quick-btn {
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-background);
  color: var(--iluria-color-text-secondary);
  border-radius: 0.375rem;
  font-size: 0.75rem;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 60px;
  text-align: center;
}

.quick-btn:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-border-hover);
}

.quick-btn.active {
  background: var(--iluria-color-primary, #3b82f6);
  color: white;
  border-color: var(--iluria-color-primary, #3b82f6);
}



.logo-preview img {
  max-height: 50px;
  max-width: 100px;
  object-fit: contain;
}

.favicon-preview img {
  width: 32px;
  height: 32px;
  object-fit: contain;
}

.background-type-selector {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 0.5rem;
}

.type-btn {
  padding: 0.5rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.375rem;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
}

.type-btn.active {
  background: var(--iluria-color-button-primary-bg);
  border-color: var(--iluria-color-button-primary-bg-hover);
  color: var(--iluria-color-button-primary-fg);
}

.type-btn:hover:not(.active) {
  background: var(--iluria-color-hover);
}

.color-preview {
  width: 24px;
  height: 24px;
  border-radius: 4px;
  border: 2px solid var(--iluria-color-border);
}

.color-picker-container {
  display: flex;
  align-items: center;
  gap: 1rem;
  background: var(--iluria-color-background);
  padding: 0.5rem;
  border-radius: 6px;
}

.color-picker {
  width: 40px;
  height: 40px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  padding: 0;
  background-color: transparent;
}

.color-picker::-webkit-color-swatch-wrapper {
  padding: 0;
}

.color-picker::-webkit-color-swatch {
  border: 2px solid var(--iluria-color-border);
  border-radius: 4px;
}

.color-value {
  font-family: monospace;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
}

.control-group {
  margin-bottom: 1rem;
}

.select-input {
  width: 100%;
  padding: 0.5rem;
  background: var(--iluria-color-input-bg);
  border: 1px solid var(--iluria-color-input-border);
  border-radius: 0.375rem;
  color: var(--iluria-color-input-text);
  cursor: pointer;
}

.angle-control {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.range-container {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.range-input {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--iluria-color-background);
  outline: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  width: 16px;
  height: 16px;
  border-radius: 50%;
  background: var(--iluria-color-button-primary-bg);
  cursor: pointer;
  border: 2px solid var(--iluria-color-surface);
  box-shadow: var(--iluria-shadow-sm);
}

.range-input::-webkit-slider-thumb:hover {
  transform: scale(1.1);
}

.range-marks {
  display: flex;
  justify-content: space-between;
  padding: 0 8px;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  font-family: monospace;
}

.gradient-colors {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.gradient-stop {
  display: flex;
  align-items: center;
  gap: 0.75rem;
}

.color-input-small {
  width: 40px;
  height: 30px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
}

.position-range {
  flex: 1;
  height: 4px;
}

.position-value {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  min-width: 35px;
  font-family: monospace;
}

.image-preview {
  height: 100px;
  border-radius: 0.375rem;
  border: 1px solid var(--iluria-color-border);
  margin-top: 0.75rem;
  background-color: var(--iluria-color-background);
}

.image-controls {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
  margin-top: 1rem;
}

.presets-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 1rem;
}

.preset-item {
  height: 80px;
  border-radius: 0.5rem;
  border: 2px solid var(--iluria-color-border);
  cursor: pointer;
  transition: all 0.2s;
  display: flex;
  align-items: end;
  padding: 0.75rem;
  position: relative;
  overflow: hidden;
}

.preset-item.active {
  border-color: var(--iluria-color-button-primary-bg);
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring);
}

.preset-item:hover {
  transform: scale(1.02);
  border-color: var(--iluria-color-border-hover);
}

.preset-name {
  background: rgba(0, 0, 0, 0.7);
  color: white;
  padding: 0.25rem 0.5rem;
  border-radius: 0.25rem;
  font-size: 0.75rem;
  font-weight: 500;
}

.form-actions {
  display: flex;
  justify-content: flex-end;
  margin-top: 2rem;
  padding-top: 1rem;
  border-top: 1px solid var(--iluria-color-border);
}

.reset-btn {
  padding: 0.5rem 1rem;
  background: var(--iluria-color-button-secondary-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.375rem;
  color: var(--iluria-color-button-secondary-fg);
  cursor: pointer;
  transition: all 0.2s;
}

.reset-btn:hover {
  background: var(--iluria-color-button-secondary-bg-hover);
}

.category-switcher {
  display: flex;
  gap: 0.5rem;
  margin-bottom: 2rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 4px;
  background: var(--iluria-color-background);
  overflow: hidden;
}

.category-btn {
  flex: 1;
  padding: 0.75rem 1rem;
  background: transparent;
  border: none;
  border-radius: 6px;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s;
  font-size: 0.875rem;
  font-weight: 500;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  min-width: 0;
  text-align: center;
}

.category-btn.active {
  background: var(--iluria-color-button-primary-bg);
  color: var(--iluria-color-button-primary-fg);
}

.category-btn:hover:not(.active) {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

.category-content {
  animation: fadeIn 0.2s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@media (max-width: 768px) {
  .settings-panel {
    padding: 1rem;
  }
  
  .category-switcher {
    gap: 0.25rem;
    padding: 2px;
  }
  
  .category-btn {
    padding: 0.5rem 0.25rem;
    font-size: 0.75rem;
    border-radius: 4px;
  }
  
  .setting-group {
    padding: 0.75rem;
  }
  
  .background-type-selector {
    grid-template-columns: 1fr;
  }
  
  .presets-grid {
    grid-template-columns: 1fr;
  }
  
  .image-controls {
    grid-template-columns: 1fr;
  }
}
</style>
