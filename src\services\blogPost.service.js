import axios from 'axios';
import { API_URLS } from '@/config/api.config';

export const BlogPostService = {
  async listBlogPosts(filter = '', page = 0, size = 10, categoryId = '', published = null, featured = null) {
    try {
      const params = { 
        search: filter, 
        page, 
        size,
        ...(categoryId && { blogCategoryId: categoryId }),
        ...(published !== null && { published }),
        ...(featured !== null && { featured })
      };
      
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}`, { params });
      
      return {
        content: response.data.content || [],
        totalPages: response.data.totalPages || 0,
        totalElements: response.data.totalElements || 0
      };
    } catch (error) {
      console.error('Erro ao listar posts do blog:', error);
      throw error;
    }
  },

  async getBlogPost(postId) {
    try {
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}/${postId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter post do blog:', error);
      throw error;
    }
  },

  async getBlogPostBySlug(slug) {
    try {
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}/slug/${slug}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter post do blog por slug:', error);
      throw error;
    }
  },

  async createBlogPost(postData) {
    try {
      const response = await axios.post(`${API_URLS.BLOG_POST_BASE_URL}`, postData);
      return response.data;
    } catch (error) {
      console.error('Erro ao criar post do blog:', error);
      throw error;
    }
  },

  async updateBlogPost(postId, postData) {
    try {
      const response = await axios.put(`${API_URLS.BLOG_POST_BASE_URL}/${postId}`, postData);
      return response.data;
    } catch (error) {
      console.error('Erro ao atualizar post do blog:', error);
      throw error;
    }
  },

  async deleteBlogPost(postId) {
    try {
      const response = await axios.delete(`${API_URLS.BLOG_POST_BASE_URL}/${postId}`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir post do blog:', error);
      throw error;
    }
  },

  async getAllBlogPosts() {
    try {
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}/all`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter todos os posts do blog:', error);
      throw error;
    }
  },

  async getPublishedCount() {
    try {
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}/stats/published`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter contagem de posts publicados:', error);
      throw error;
    }
  },

  async getFeaturedCount() {
    try {
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}/stats/featured`);
      return response.data;
    } catch (error) {
      console.error('Erro ao obter contagem de posts em destaque:', error);
      throw error;
    }
  },

  generateSlugFromTitle(title) {
    if (!title) return '';
    return title
      .toLowerCase()
      .normalize('NFD')
      .replace(/[\u0300-\u036f]/g, '')
      .replace(/[^a-z0-9\s-]/g, '')
      .trim()
      .replace(/\s+/g, '-');
  },

  async uploadBlogPostImage(postId, file) {
    try {
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await axios.post(`${API_URLS.BLOG_POST_BASE_URL}/${postId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });
      return response.data;
    } catch (error) {
      console.error('Erro ao fazer upload da imagem do post:', error);
      throw error;
    }
  },

  async getBlogPostImageUrl(postId) {
    try {
      const response = await axios.get(`${API_URLS.BLOG_POST_BASE_URL}/${postId}/images`);
      return response.headers.location;
    } catch (error) {
      console.error('Erro ao obter URL da imagem do post:', error);
      return null;
    }
  },

  async deleteBlogPostImage(postId) {
    try {
      const response = await axios.delete(`${API_URLS.BLOG_POST_BASE_URL}/${postId}/images`);
      return response.data;
    } catch (error) {
      console.error('Erro ao excluir imagem do post:', error);
      throw error;
    }
  }
};

export default BlogPostService; 