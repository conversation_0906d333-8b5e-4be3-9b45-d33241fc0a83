import axios from 'axios';

class CustomerImportService {
    #endpoint = 'http://localhost:8081/customers/imports';

    // Parse CSV file and extract headers
    async parseCSVHeaders(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await axios.post(`${this.#endpoint}/parse-headers`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Process import directly
    async createImport(file, fieldMapping) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('fieldMapping', JSON.stringify(fieldMapping));

            const response = await axios.post(this.#endpoint, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            return response.data; // Agora retorna apenas uma string de sucesso
        } catch (error) {
            throw error;
        }
    }



    // Get available fields for import mapping
    async getAvailableFields() {
        try {
            const response = await axios.get(`${this.#endpoint}/fields`);
            return response.data;
        } catch (error) {
            return {
                basic: [
                    'name',
                    'email',
                    'phone',
                    'document',
                    'documentType',
                    'dayOfBirth',
                    'defaultLanguage',
                    'allowsEmailMarketing',
                    'active'
                ],
                addresses: [
                    'street',
                    'number', 
                    'complement',
                    'district',
                    'city',
                    'state',
                    'country',
                    'zipCode',
                    'type',
                    'label'
                ]
            };
        }
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Get status display text
    getStatusText(status) {
        const statusMap = {
            'PENDING': 'Pendente',
            'IN_PROGRESS': 'Em andamento',
            'COMPLETED': 'Concluído',
            'FAILED': 'Falhou'
        };
        return statusMap[status] || status;
    }

    // Get status color for UI
    getStatusColor(status) {
        const colorMap = {
            'PENDING': 'warning',
            'IN_PROGRESS': 'info',
            'COMPLETED': 'success',
            'FAILED': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    // Check if import can be deleted
    canDelete(importJob) {
        return ['COMPLETED', 'FAILED'].includes(importJob.status);
    }

    // Get field display name
    getFieldDisplayName(fieldName) {
        const fieldMap = {
            'name': 'Nome Completo',
            'email': 'E-mail',
            'phone': 'Telefone',
            'document': 'CPF/CNPJ',
            'documentType': 'Tipo de Documento',
            'dayOfBirth': 'Data de Nascimento',
            'defaultLanguage': 'Idioma Padrão',
            'allowsEmailMarketing': 'Permite Marketing por E-mail',
            'active': 'Ativo',
            'nickname': 'Apelido',
            'street': 'Rua/Endereço',
            'number': 'Número',
            'complement': 'Complemento',
            'district': 'Bairro',
            'city': 'Cidade',
            'state': 'Estado',
            'country': 'País',
            'zipCode': 'CEP',
            'type': 'Tipo de Endereço',
            'label': 'Rótulo'
        };
        return fieldMap[fieldName] || fieldName;
    }

    // Validate CSV file
    validateCSVFile(file) {
        const errors = [];
        
        if (!file) {
            errors.push('Nenhum arquivo selecionado');
            return errors;
        }

        // Check file type
        if (!file.type.includes('csv') && !file.name.toLowerCase().endsWith('.csv')) {
            errors.push('Apenas arquivos CSV são suportados');
        }

        // Check file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            errors.push('Arquivo muito grande. Tamanho máximo: 10MB');
        }

        return errors;
    }
}

export default new CustomerImportService();
