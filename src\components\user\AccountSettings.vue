<template>
  <div class="account-content">
    <!-- Privacy Settings -->
    <ViewContainer 
      title="Privacidade"
      :icon="SecurityLockIcon"
      iconColor="purple"
    >
      <div class="privacy-list">
        <div class="privacy-item">
          <div class="privacy-info">
            <h4 class="privacy-title">Perfil Público</h4>
            <p class="privacy-description">Permitir que outros usuários vejam suas informações básicas</p>
          </div>
          <div class="privacy-toggle">
            <IluriaToggleSwitch
              v-model="privacySettings.publicProfile"
              @change="savePrivacySetting"
            />
          </div>
        </div>
        
        <div class="privacy-item">
          <div class="privacy-info">
            <h4 class="privacy-title">Dados de Atividade</h4>
            <p class="privacy-description">Permitir coleta de dados para melhorar sua experiência</p>
          </div>
          <div class="privacy-toggle">
            <IluriaToggleSwitch
              v-model="privacySettings.activityData"
              @change="savePrivacySetting"
            />
          </div>
        </div>
        
        <div class="privacy-item">
          <div class="privacy-info">
            <h4 class="privacy-title">Compartilhamento com Terceiros</h4>
            <p class="privacy-description">Permitir compartilhamento de dados com parceiros selecionados</p>
          </div>
          <div class="privacy-toggle">
            <IluriaToggleSwitch
              v-model="privacySettings.thirdPartySharing"
              @change="savePrivacySetting"
            />
          </div>
        </div>
      </div>
    </ViewContainer>

    <!-- Data Management -->
    <ViewContainer 
      title="Gerenciamento de Dados"
      :icon="Database01Icon"
      iconColor="blue"
    >
      <div class="data-actions">
        <div class="data-action-item">
          <div class="data-action-info">
            <div class="data-action-icon">
              <HugeiconsIcon :icon="Download01Icon" size="18" :strokeWidth="1.5" />
            </div>
            <div class="data-action-content">
              <h4 class="data-action-title">Exportar Dados</h4>
              <p class="data-action-description">Baixe uma cópia de todos os seus dados pessoais</p>
            </div>
          </div>
          <IluriaButton
            @click="exportData"
            variant="outline"
            color="primary"
            :disabled="isExporting"
            :loading="isExporting"
            :hugeIcon="Download01Icon"
          >
            {{ isExporting ? 'Exportando...' : 'Exportar' }}
          </IluriaButton>
        </div>
        
        <div class="data-action-item">
          <div class="data-action-info">
            <div class="data-action-icon">
              <HugeiconsIcon :icon="Delete01Icon" size="18" :strokeWidth="1.5" />
            </div>
            <div class="data-action-content">
              <h4 class="data-action-title">Limpar Dados de Atividade</h4>
              <p class="data-action-description">Remove histórico de navegação e atividades</p>
            </div>
          </div>
          <IluriaButton
            @click="clearActivityData"
            variant="outline"
            color="primary"
            :disabled="isClearing"
            :loading="isClearing"
            :hugeIcon="Delete01Icon"
          >
            {{ isClearing ? 'Limpando...' : 'Limpar' }}
          </IluriaButton>
        </div>
      </div>
    </ViewContainer>

    <!-- Account Status -->
    <ViewContainer 
      title="Status da Conta"
      :icon="UserCheck01Icon"
      iconColor="green"
    >
      <div class="status-info">
        <div class="status-item">
          <div class="status-label">Tipo de Conta</div>
          <div class="status-value">Usuário Premium</div>
        </div>
        
        <div class="status-item">
          <div class="status-label">Membro desde</div>
          <div class="status-value">Janeiro 2023</div>
        </div>
        
        <div class="status-item">
          <div class="status-label">Última Atividade</div>
          <div class="status-value">{{ formatDate(new Date()) }}</div>
        </div>
        
        <div class="status-item">
          <div class="status-label">Verificação</div>
          <div class="status-value verified">
            <HugeiconsIcon :icon="CheckmarkCircle02Icon" size="16" :strokeWidth="1.5" />
            Conta Verificada
          </div>
        </div>
      </div>
    </ViewContainer>

    <!-- Danger Zone -->
    <ViewContainer 
      title="Zona de Perigo"
      :icon="Alert02Icon"
      iconColor="red"
    >
      <div class="danger-actions">
        <div class="danger-action-item">
          <div class="danger-action-info">
            <h4 class="danger-action-title">Desativar Conta</h4>
            <p class="danger-action-description">Sua conta será desativada temporariamente. Você pode reativá-la a qualquer momento.</p>
          </div>
          <IluriaButton
            @click="deactivateAccount"
            variant="outline"
            color="danger"
            :hugeIcon="UserRemove01Icon"
          >
            Desativar
          </IluriaButton>
        </div>
        
        <div class="danger-action-item">
          <div class="danger-action-info">
            <h4 class="danger-action-title">Excluir Conta</h4>
            <p class="danger-action-description">Esta ação é irreversível. Todos os seus dados serão permanentemente removidos.</p>
          </div>
          <IluriaButton
            @click="deleteAccount"
            color="danger"
            :hugeIcon="Delete01Icon"
          >
            Excluir Conta
          </IluriaButton>
        </div>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted } from 'vue'
import { useToast } from '@/services/toast.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  UserSettingsIcon,
  SecurityLockIcon,
  Database01Icon,
  UserCheck01Icon,
  Alert02Icon,
  Download01Icon,
  Delete01Icon,
  UserRemove01Icon,
  CheckmarkCircle02Icon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const toast = useToast()

// State
const isExporting = ref(false)
const isClearing = ref(false)

const privacySettings = reactive({
  publicProfile: false,
  activityData: true,
  thirdPartySharing: false
})

// Methods
const savePrivacySetting = async () => {
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 500))
    
    toast.addToast('Configurações de privacidade atualizadas', 'success')
  } catch (error) {
    toast.addToast('Erro ao salvar configurações', 'error')
  }
}

const exportData = async () => {
  isExporting.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 3000))
    
    toast.addToast('Dados exportados com sucesso! Verifique seu email.', 'success')
  } catch (error) {
    toast.addToast('Erro ao exportar dados', 'error')
  } finally {
    isExporting.value = false
  }
}

const clearActivityData = async () => {
  if (!confirm('Tem certeza que deseja limpar todos os dados de atividade? Esta ação não pode ser desfeita.')) {
    return
  }
  
  isClearing.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast.addToast('Dados de atividade removidos com sucesso', 'success')
  } catch (error) {
    toast.addToast('Erro ao limpar dados de atividade', 'error')
  } finally {
    isClearing.value = false
  }
}

const deactivateAccount = async () => {
  if (!confirm('Tem certeza que deseja desativar sua conta? Você pode reativá-la fazendo login novamente.')) {
    return
  }
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    toast.addToast('Conta desativada com sucesso', 'success')
  } catch (error) {
    toast.addToast('Erro ao desativar conta', 'error')
  }
}

const deleteAccount = async () => {
  const confirmation = prompt(
    'Esta ação é irreversível! Digite "EXCLUIR" para confirmar a exclusão permanente da sua conta:'
  )
  
  if (confirmation !== 'EXCLUIR') {
    toast.addToast('Exclusão cancelada', 'info')
    return
  }
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 2000))
    
    toast.addToast('Conta excluída permanentemente', 'success')
    // Redirect to login or home page
  } catch (error) {
    toast.addToast('Erro ao excluir conta', 'error')
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const loadAccountSettings = async () => {
  try {
    // Simulate API call to load settings
    await new Promise(resolve => setTimeout(resolve, 1000))
  } catch (error) {
    toast.addToast('Erro ao carregar configurações', 'error')
  }
}

// Lifecycle
onMounted(() => {
  loadAccountSettings()
})
</script>

<style scoped>
.account-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.danger-zone .section-title {
  color: var(--iluria-color-danger);
}

.danger-card {
  border-color: var(--iluria-color-danger);
  background: rgba(220, 38, 38, 0.05);
}

.privacy-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.privacy-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.privacy-info {
  flex: 1;
}

.privacy-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.25rem 0;
}

.privacy-description {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.privacy-toggle {
  flex-shrink: 0;
}



.data-actions, .danger-actions {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.data-action-item, .danger-action-item {
  display: flex;
  align-items: flex-start;
  justify-content: space-between;
  gap: 1rem;
  padding: 1rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.data-action-info, .danger-action-info {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
  flex: 1;
}

.data-action-icon {
  flex-shrink: 0;
  color: var(--iluria-color-primary);
}

.data-action-content, .danger-action-info {
  flex: 1;
}

.data-action-title, .danger-action-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.5rem 0;
}

.data-action-description, .danger-action-description {
  font-size: 0.8rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.5;
}

.danger-action-title {
  color: var(--iluria-color-danger);
}

.status-info {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 0.75rem 1rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.status-label {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.status-value {
  font-size: 0.875rem;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

.status-value.verified {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  color: #10b981;
}



/* Responsive */
@media (max-width: 768px) {
  .privacy-item, .data-action-item, .danger-action-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }

  .data-action-info, .danger-action-info {
    gap: 0.5rem;
  }

  .privacy-toggle {
    align-self: flex-end;
  }

  .data-action-info {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }

  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
}
</style>