<template>
  <div 
    class="iluria-toast"
    :class="[
      `toast-${type}`,
      { 'toast-dismissing': isDismissing }
    ]"
    @mouseenter="pauseTimer"
    @mouseleave="resumeTimer"
  >
    <!-- <PERSON><PERSON> de progresso (se houver duração) -->
    <div 
      v-if="duration > 0 && !persistent"
      class="toast-progress"
      :style="{ width: progressWidth + '%' }"
    ></div>
    
    <!-- <PERSON><PERSON><PERSON><PERSON> do toast -->
    <div class="toast-content">
      <!-- Ícone -->
      <div class="toast-icon">
        <HugeiconsIcon 
          :icon="iconComponent" 
          :size="20"
          class="icon"
        />
      </div>
      
      <!-- Texto -->
      <div class="toast-text">
        <div v-if="title" class="toast-title">{{ title }}</div>
        <div class="toast-message">{{ message }}</div>
      </div>
      
      <!-- Ações -->
      <div class="toast-actions">
        <!-- Bot<PERSON> de ação personalizada -->
        <button
          v-if="action"
          @click="handleAction"
          class="toast-action-button"
        >
          {{ action.label }}
        </button>
        
        <!-- <PERSON><PERSON><PERSON> de fechar -->
        <button
          v-if="dismissible"
          @click="handleClose"
          class="toast-close-button"
          :title="t('toast.close')"
        >
          <HugeiconsIcon 
            :icon="Cancel01Icon" 
            :size="16"
            class="close-icon"
          />
        </button>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useI18n } from 'vue-i18n'
import { HugeiconsIcon } from '@hugeicons/vue'
import { 
  CheckmarkCircle02Icon,
  Alert02Icon, 
  Cancel01Icon,
  InformationCircleIcon
} from '@hugeicons-pro/core-bulk-rounded'

const { t } = useI18n()

const props = defineProps({
  id: {
    type: [String, Number],
    required: true
  },
  type: {
    type: String,
    default: 'info',
    validator: (value) => ['success', 'error', 'warning', 'info'].includes(value)
  },
  message: {
    type: String,
    required: true
  },
  title: {
    type: String,
    default: ''
  },
  duration: {
    type: Number,
    default: 4000
  },
  action: {
    type: Object,
    default: null
    // { label: 'Desfazer', handler: () => {} }
  },
  persistent: {
    type: Boolean,
    default: false
  },
  dismissible: {
    type: Boolean,
    default: true
  }
})

const emit = defineEmits(['close', 'action'])

// Estados do componente
const isDismissing = ref(false)
const progressWidth = ref(100)
const timer = ref(null)
const progressTimer = ref(null)
const isPaused = ref(false)
const remainingTime = ref(props.duration)

// Ícones para cada tipo de toast
const iconComponent = computed(() => {
  const icons = {
    success: CheckmarkCircle02Icon,
    error: Cancel01Icon,
    warning: Alert02Icon,
    info: InformationCircleIcon
  }
  return icons[props.type] || InformationCircleIcon
})

// Funções de controle de timer
const pauseTimer = () => {
  if (props.duration > 0 && !props.persistent) {
    isPaused.value = true
    clearTimeout(timer.value)
    clearInterval(progressTimer.value)
  }
}

const resumeTimer = () => {
  if (props.duration > 0 && !props.persistent && isPaused.value) {
    isPaused.value = false
    startTimer()
  }
}

const startTimer = () => {
  if (props.duration <= 0 || props.persistent) return
  
  const startTime = Date.now()
  const initialRemaining = remainingTime.value
  
  // Timer para fechar o toast
  timer.value = setTimeout(() => {
    handleClose()
  }, remainingTime.value)
  
  // Timer para atualizar a barra de progresso
  progressTimer.value = setInterval(() => {
    const elapsed = Date.now() - startTime
    const remaining = Math.max(0, initialRemaining - elapsed)
    remainingTime.value = remaining
    progressWidth.value = (remaining / props.duration) * 100
    
    if (remaining <= 0) {
      clearInterval(progressTimer.value)
    }
  }, 50)
}

// Função para fechar o toast
const handleClose = () => {
  isDismissing.value = true
  clearTimeout(timer.value)
  clearInterval(progressTimer.value)
  
  // Pequeno delay para animação
  setTimeout(() => {
    emit('close', props.id)
  }, 100)
}

// Função para lidar com ação personalizada
const handleAction = () => {
  emit('action', { 
    id: props.id, 
    action: props.action 
  })
}

// Inicializar timer quando o componente for montado
onMounted(() => {
  startTimer()
})

// Limpar timers quando o componente for desmontado
onUnmounted(() => {
  clearTimeout(timer.value)
  clearInterval(progressTimer.value)
})
</script>

<style scoped>
.iluria-toast {
  position: relative;
  display: flex;
  align-items: flex-start;
  min-height: 60px;
  padding: 16px 20px;
  border-radius: 12px;
  box-shadow: 
    0 4px 6px -1px rgba(0, 0, 0, 0.1),
    0 2px 4px -1px rgba(0, 0, 0, 0.06),
    0 0 0 1px rgba(0, 0, 0, 0.05);
  backdrop-filter: blur(8px);
  border: 1px solid;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  overflow: hidden;
  max-width: 100%;
}

.iluria-toast:hover {
  transform: translateY(-1px);
  box-shadow: 
    0 10px 15px -3px rgba(0, 0, 0, 0.1),
    0 4px 6px -2px rgba(0, 0, 0, 0.05),
    0 0 0 1px rgba(0, 0, 0, 0.05);
}

.iluria-toast.toast-dismissing {
  opacity: 0.8;
  transform: scale(0.98);
}

/* Barra de progresso */
.toast-progress {
  position: absolute;
  top: 0;
  left: 0;
  height: 3px;
  border-radius: 12px 12px 0 0;
  transition: width 0.05s linear;
  z-index: 1;
}

/* Conteúdo do toast */
.toast-content {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  width: 100%;
}

.toast-icon {
  flex-shrink: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-top: 2px;
}

.toast-text {
  flex: 1;
  min-width: 0;
}

.toast-title {
  font-size: 14px;
  font-weight: 600;
  line-height: 1.4;
  margin-bottom: 2px;
}

.toast-message {
  font-size: 13px;
  line-height: 1.4;
  word-wrap: break-word;
}

.toast-actions {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-left: 12px;
  flex-shrink: 0;
}

.toast-action-button {
  padding: 4px 8px;
  font-size: 12px;
  font-weight: 500;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.toast-close-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  border: none;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.2s ease;
  opacity: 0.7;
}

.toast-close-button:hover {
  opacity: 1;
  transform: scale(1.05);
}

/* Temas para diferentes tipos de toast */

/* Success Toast - Verde */
.toast-success {
  background: color-mix(in srgb, var(--iluria-color-success) 10%, var(--iluria-color-surface));
  border-color: color-mix(in srgb, var(--iluria-color-success) 20%, transparent);
}

.toast-success .toast-progress {
  background: var(--iluria-color-success);
}

.toast-success .toast-icon .icon {
  color: var(--iluria-color-success);
}

.toast-success .toast-title {
  color: var(--iluria-color-text-primary);
}

.toast-success .toast-message {
  color: var(--iluria-color-text-secondary);
}

.toast-success .toast-action-button {
  background: var(--iluria-color-success);
  color: white;
}

.toast-success .toast-action-button:hover {
  background: color-mix(in srgb, var(--iluria-color-success) 85%, black);
}

.toast-success .toast-close-button {
  background: color-mix(in srgb, var(--iluria-color-success) 15%, transparent);
  color: var(--iluria-color-success);
}

.toast-success .toast-close-button:hover {
  background: color-mix(in srgb, var(--iluria-color-success) 25%, transparent);
}

/* Error Toast - Vermelho */
.toast-error {
  background: color-mix(in srgb, var(--iluria-color-error) 10%, var(--iluria-color-surface));
  border-color: color-mix(in srgb, var(--iluria-color-error) 20%, transparent);
}

.toast-error .toast-progress {
  background: var(--iluria-color-error);
}

.toast-error .toast-icon .icon {
  color: var(--iluria-color-error);
}

.toast-error .toast-title {
  color: var(--iluria-color-text-primary);
}

.toast-error .toast-message {
  color: var(--iluria-color-text-secondary);
}

.toast-error .toast-action-button {
  background: var(--iluria-color-error);
  color: white;
}

.toast-error .toast-action-button:hover {
  background: color-mix(in srgb, var(--iluria-color-error) 85%, black);
}

.toast-error .toast-close-button {
  background: color-mix(in srgb, var(--iluria-color-error) 15%, transparent);
  color: var(--iluria-color-error);
}

.toast-error .toast-close-button:hover {
  background: color-mix(in srgb, var(--iluria-color-error) 25%, transparent);
}

/* Warning Toast - Amarelo/Laranja */
.toast-warning {
  background: color-mix(in srgb, var(--iluria-color-warning) 10%, var(--iluria-color-surface));
  border-color: color-mix(in srgb, var(--iluria-color-warning) 20%, transparent);
}

.toast-warning .toast-progress {
  background: var(--iluria-color-warning);
}

.toast-warning .toast-icon .icon {
  color: var(--iluria-color-warning);
}

.toast-warning .toast-title {
  color: var(--iluria-color-text-primary);
}

.toast-warning .toast-message {
  color: var(--iluria-color-text-secondary);
}

.toast-warning .toast-action-button {
  background: var(--iluria-color-warning);
  color: white;
}

.toast-warning .toast-action-button:hover {
  background: color-mix(in srgb, var(--iluria-color-warning) 85%, black);
}

.toast-warning .toast-close-button {
  background: color-mix(in srgb, var(--iluria-color-warning) 15%, transparent);
  color: var(--iluria-color-warning);
}

.toast-warning .toast-close-button:hover {
  background: color-mix(in srgb, var(--iluria-color-warning) 25%, transparent);
}

/* Info Toast - Azul/Cinza */
.toast-info {
  background: color-mix(in srgb, var(--iluria-color-info) 10%, var(--iluria-color-surface));
  border-color: color-mix(in srgb, var(--iluria-color-info) 20%, transparent);
}

.toast-info .toast-progress {
  background: var(--iluria-color-info);
}

.toast-info .toast-icon .icon {
  color: var(--iluria-color-info);
}

.toast-info .toast-title {
  color: var(--iluria-color-text-primary);
}

.toast-info .toast-message {
  color: var(--iluria-color-text-secondary);
}

.toast-info .toast-action-button {
  background: var(--iluria-color-info);
  color: white;
}

.toast-info .toast-action-button:hover {
  background: color-mix(in srgb, var(--iluria-color-info) 85%, black);
}

.toast-info .toast-close-button {
  background: color-mix(in srgb, var(--iluria-color-info) 15%, transparent);
  color: var(--iluria-color-info);
}

.toast-info .toast-close-button:hover {
  background: color-mix(in srgb, var(--iluria-color-info) 25%, transparent);
}

/* Responsividade */
@media (max-width: 768px) {
  .iluria-toast {
    padding: 14px 16px;
    min-height: 56px;
  }
  
  .toast-content {
    gap: 10px;
  }
  
  .toast-message {
    font-size: 12px;
  }
  
  .toast-title {
    font-size: 13px;
  }
  
  .toast-actions {
    gap: 6px;
    margin-left: 8px;
  }
}

/* Redução de movimento */
@media (prefers-reduced-motion: reduce) {
  .iluria-toast,
  .toast-progress,
  .toast-action-button,
  .toast-close-button {
    transition: none;
  }
}
</style>
