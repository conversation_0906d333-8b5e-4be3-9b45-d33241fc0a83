<template>
  <div class="no-permission-container">
    <div class="error-content">
      <!-- Ícone principal -->
      <div class="error-icon">
        <HugeiconsIcon :icon="SecurityLockIcon" size="80" class="error-icon-color" />
      </div>
      
      <!-- <PERSON><PERSON><PERSON> de erro -->
      <div class="error-code">
        <h1 class="error-code-text">403</h1>
      </div>
      
      <!-- Título -->
      <div class="error-title">
        <h2 class="error-title-text">
          Acesso Negado
        </h2>
      </div>
      
      <!-- Descrição -->
      <div class="error-description">
        <p class="error-description-text">
          {{ customMessage || 'Você não possui as permissões necessárias para acessar esta página. Entre em contato com o administrador da loja se você acredita que deveria ter acesso.' }}
        </p>
      </div>
      
      <!-- Detalhes das permissões (se fornecidas) -->
      <div v-if="finalPermissions && finalPermissions.length > 0" class="permissions-details">
        <div class="permissions-box">
          <h3 class="permissions-title">
            Permissões necessárias:
          </h3>
          <ul class="permissions-list">
            <li v-for="permission in finalPermissions" :key="permission" class="permission-item">
              <HugeiconsIcon :icon="PinIcon" size="12" class="permission-icon" />
              {{ getPermissionDescription(permission) }}
            </li>
          </ul>
        </div>
      </div>
      
      <!-- Ações -->
      <div class="error-actions">
        <IluriaButton 
          @click="goBack" 
          :hugeIcon="ArrowLeft02Icon"
          color="primary"
        >
          Voltar
        </IluriaButton>
        
        <IluriaButton 
          @click="goHome"
          :hugeIcon="Home01Icon"
          variant="outline"
          color="primary"
        >
          Home
        </IluriaButton>
      </div>
      
      <!-- Informação de contato (opcional) -->
      <div v-if="showContactInfo" class="contact-info">
        <p class="contact-text">
          Se você acredita que deveria ter acesso, entre em contato com o administrador.
        </p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import { 
  ArrowLeft02Icon, 
  Home01Icon,
  PinIcon 
} from '@hugeicons-pro/core-stroke-standard';

import { 
  SecurityLockIcon
} from '@hugeicons-pro/core-solid-rounded';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { getPermissionByCode } from '@/constants/permissions';

const props = defineProps({
  /**
   * Mensagem customizada para substituir a padrão
   */
  customMessage: {
    type: String,
    default: null
  },
  
  /**
   * Lista de permissões necessárias que o usuário não possui
   */
  requiredPermissions: {
    type: Array,
    default: () => []
  },
  
  /**
   * Se deve mostrar informações de contato
   */
  showContactInfo: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

// Get permissions from URL query parameters if coming from router guard
const urlPermissions = computed(() => {
  const queryPerms = route.query.requiredPermissions;
  if (queryPerms && typeof queryPerms === 'string') {
    return queryPerms.split(',').filter(Boolean);
  }
  return [];
});

// Combine props and URL permissions
const finalPermissions = computed(() => {
  return props.requiredPermissions.length > 0 
    ? props.requiredPermissions 
    : urlPermissions.value;
});

/**
 * Volta para a página anterior
 */
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    goHome();
  }
};

/**
 * Vai para a página inicial
 */
const goHome = () => {
  router.push('/');
};

/**
 * Obtém a descrição de uma permissão pelo código
 */
const getPermissionDescription = (permissionCode) => {
  const permission = getPermissionByCode(permissionCode);
  return permission ? permission.description : permissionCode;
};
</script>

<style scoped>
.no-permission-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--iluria-color-body-bg) 0%, var(--iluria-color-container-bg) 100%);
  padding: 3rem 1rem;
  font-family: var(--iluria-font-family);
}

.error-content {
  text-align: center;
  max-width: 32rem;
  margin: 0 auto;
  background: var(--iluria-color-container-bg);
  border-radius: 1rem;
  padding: 3rem 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--iluria-color-border);
}

.error-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  animation: fadeInUp 0.6s ease-out;
}

.error-icon-color {
  color: #ef4444;
}

.error-code {
  margin-bottom: 1rem;
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.error-code-text {
  font-size: 4rem;
  font-weight: 800;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1;
}

.error-title {
  margin-bottom: 1rem;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.error-title-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.error-description {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.error-description-text {
  color: var(--iluria-color-text-secondary);
  line-height: 1.6;
  max-width: 28rem;
  margin: 0 auto;
}

.permissions-details {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.permissions-box {
  background: var(--iluria-color-body-bg);
  border-radius: 0.75rem;
  padding: 1.5rem;
  max-width: 28rem;
  margin: 0 auto;
  border: 1px solid var(--iluria-color-border);
}

.permissions-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
}

.permissions-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
}

.permission-item {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  text-align: center;
}

.permission-icon {
  margin-right: 0.5rem;
  color: var(--iluria-color-text-tertiary);
}

.error-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

@media (min-width: 640px) {
  .error-actions {
    flex-direction: row;
    gap: 1rem;
  }
}

.contact-info {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--iluria-color-border);
}

.contact-text {
  font-size: 0.875rem;
  color: var(--iluria-color-text-tertiary);
  margin: 0;
}

/* Responsividade aprimorada */
@media (max-width: 640px) {
  .error-content {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .error-code-text {
    font-size: 3rem;
  }
  
  .error-title-text {
    font-size: 1.25rem;
  }
}

/* Animações melhoradas */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects para interatividade */
.error-content:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}

.permissions-box:hover {
  background: var(--iluria-color-container-bg);
  transition: background-color 0.3s ease;
}
</style>