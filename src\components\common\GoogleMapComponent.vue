<template>
  <div class="google-map-container">
    <!-- Debug info -->
    <div v-if="useGoogleMaps" class="debug-info">
      🗺️ Modo: Google Maps Ativo | 📍 Local: {{ location.name }}
    </div>
    
    <!-- Google Maps real quando disponível -->
    <div 
      v-if="useGoogleMaps" 
      ref="mapContainer"
      class="google-maps-native"
    ></div>
    
    <!-- Fallback estilizado quando Google Maps não está disponível -->
    <div v-else class="map-fallback">
      <div class="map-fallback-content">
        <div class="map-icon">
          <svg width="80" height="80" viewBox="0 0 24 24" fill="currentColor">
            <path d="M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5s1.12-2.5 2.5-2.5 2.5 1.12 2.5 2.5-1.12 2.5-2.5 2.5z"/>
          </svg>
        </div>
        <h3>📍 {{ location.name }}</h3>
        <p>{{ location.address }}</p>
        <div class="map-actions">
          <a 
            v-if="location.phone"
            :href="`tel:${location.phone}`"
            class="map-action-btn phone"
          >
            📞 Ligar
          </a>
          <a 
            :href="`https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location.address)}`"
            target="_blank"
            class="map-action-btn directions"
          >
            🗺️ Google Maps
          </a>
          <a 
            :href="`https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(location.address)}`"
            target="_blank"
            class="map-action-btn directions"
          >
            🧭 Direções
          </a>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, nextTick } from 'vue'
import { GOOGLE_MAPS_API_KEY } from '@/config/api.config.js'

const props = defineProps({
  location: {
    type: Object,
    required: true,
    validator: (location) => {
      return location && location.address && location.name
    }
  },
  showInfoWindow: {
    type: Boolean,
    default: false
  }
})

const mapContainer = ref(null)
const useGoogleMaps = ref(false)

const loadGoogleMaps = async () => {
  try {
    if (!GOOGLE_MAPS_API_KEY || GOOGLE_MAPS_API_KEY === 'undefined') {
      return false
    }

    if (window.google && window.google.maps) {
      return true
    }

    const existingScript = document.querySelector('script[src*="maps.googleapis.com"]')
    if (existingScript) {
      return new Promise((resolve) => {
        const checkGoogle = () => {
          if (window.google && window.google.maps) {
            resolve(true)
          } else {
            setTimeout(checkGoogle, 100)
          }
        }
        checkGoogle()
      })
    }

    const script = document.createElement('script')
    script.src = `https://maps.googleapis.com/maps/api/js?key=${GOOGLE_MAPS_API_KEY}&libraries=places,geometry`
    script.async = true
    script.defer = true

    return new Promise((resolve) => {
      script.onload = () => {
        setTimeout(() => {
          if (window.google && window.google.maps) {
            resolve(true)
          } else {
            resolve(false)
          }
        }, 500)
      }
      script.onerror = () => {
        resolve(false)
      }
      document.head.appendChild(script)
    })
  } catch (error) {
    return false
  }
}

// Renderizar mapa do Google Maps
const renderGoogleMap = () => {
  if (!mapContainer.value) {
    return
  }
  
  if (!window.google || !window.google.maps) {
    return
  }

  try {
    // Limpar container
    mapContainer.value.innerHTML = ''
    
    // Coordenadas padrão (São Paulo)
    const defaultCoords = { lat: -23.5505, lng: -46.6333 }
    
    // Criar o mapa
    const map = new window.google.maps.Map(mapContainer.value, {
      zoom: 15,
      center: defaultCoords,
      mapTypeId: 'roadmap',
      gestureHandling: 'auto',
      zoomControl: true,
      streetViewControl: false,
      fullscreenControl: false
    })

    // Criar marcador
    const marker = new window.google.maps.Marker({
      position: defaultCoords,
      map: map,
      title: props.location.name,
      animation: window.google.maps.Animation.DROP
    })

    // Tentar geocodificar o endereço
    const geocoder = new window.google.maps.Geocoder()
    geocoder.geocode({ address: props.location.address }, (results, status) => {
      if (status === 'OK' && results[0]) {
        const location = results[0].geometry.location
        const coords = {
          lat: location.lat(),
          lng: location.lng()
        }
        map.setCenter(coords)
        marker.setPosition(coords)
      } else {
      }
    })

    // Info Window
    if (props.showInfoWindow) {
      const infoWindow = new window.google.maps.InfoWindow({
        content: `
          <div style="padding: 12px; max-width: 250px; font-family: -apple-system, BlinkMacSystemFont, &quot;Segoe UI&quot;, Roboto, sans-serif;">
            <h4 style="margin: 0 0 8px 0; color: #1a1a1a; font-size: 1.1rem; font-weight: 600;">
              📍 ${props.location.name}
            </h4>
            <p style="margin: 0 0 12px 0; color: #666; font-size: 0.9rem; line-height: 1.4;">
              ${props.location.address}
            </p>
            <div style="display: flex; gap: 8px; flex-wrap: wrap;">
              ${props.location.phone ? `
                <a href="tel:${props.location.phone}" 
                   style="background: #3b82f6; color: white; text-decoration: none; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 500; display: inline-block;">
                  📞 Ligar
                </a>
              ` : ''}
              <a href="https://www.google.com/maps/dir/?api=1&destination=${encodeURIComponent(props.location.address)}" 
                 target="_blank"
                 style="background: #10b981; color: white; text-decoration: none; padding: 6px 12px; border-radius: 16px; font-size: 0.8rem; font-weight: 500; display: inline-block;">
                🧭 Direções
              </a>
            </div>
          </div>
        `
      })

      marker.addListener('click', () => {
        infoWindow.open(map, marker)
      })
      
      // Abrir automaticamente se showInfoWindow for true
      setTimeout(() => {
        infoWindow.open(map, marker)
      }, 1000)
    }

    // Trigger resize para garantir renderização correta
    setTimeout(() => {
      window.google.maps.event.trigger(map, 'resize')
    }, 100)
    
  } catch (error) {
    useGoogleMaps.value = false
  }
}

// Inicialização
onMounted(async () => {
  const mapsLoaded = await loadGoogleMaps()
  if (mapsLoaded) {
    useGoogleMaps.value = true
    
    // Aguardar o Vue atualizar o DOM
    await nextTick()
    
    // Múltiplas tentativas de renderização
    let attempts = 0
    const maxAttempts = 5
    
    const tryRender = () => {
      attempts++    
      
      if (mapContainer.value) {
        renderGoogleMap()
      } else if (attempts < maxAttempts) {
        setTimeout(tryRender, 200)
      } else {
        console.warn('❌ Falha ao encontrar container após', maxAttempts, 'tentativas')
        useGoogleMaps.value = false
      }
    }
    
    setTimeout(tryRender, 100)
  } else {

  }
})
</script>

<style scoped>
.google-map-container {
  padding: 1.5rem;
  background: white;
  border-radius: 12px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  margin: 1.5rem 0;
  border: 1px solid #e2e8f0;
}

.debug-info {
  background: #e3f2fd;
  color: #1976d2;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  margin-bottom: 12px;
  border: 1px solid #bbdefb;
}

.google-maps-native {
  width: 100%;
  height: 350px;
  border-radius: 8px;
  overflow: hidden;
  background: #f0f0f0;
  position: relative;
  min-height: 350px;
}

.google-maps-native::before {
  content: 'Carregando mapa...';
  position: absolute;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
  color: #666;
  font-size: 14px;
  z-index: 1;
}

.map-fallback {
  width: 100%;
  height: 350px;
  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  position: relative;
  overflow: hidden;
}

.map-fallback::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grid" width="10" height="10" patternUnits="userSpaceOnUse"><path d="M 10 0 L 0 0 0 10" fill="none" stroke="rgba(255,255,255,0.1)" stroke-width="0.5"/></pattern></defs><rect width="100" height="100" fill="url(%23grid)"/></svg>') repeat;
}

.map-fallback-content {
  position: relative;
  z-index: 1;
  text-align: center;
  color: white;
  max-width: 400px;
  padding: 2rem;
}

.map-icon {
  margin-bottom: 1.5rem;
  color: rgba(255, 255, 255, 0.9);
}

.map-fallback-content h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.3rem;
  font-weight: 600;
  color: white;
}

.map-fallback-content p {
  margin: 0 0 2rem 0;
  font-size: 1rem;
  line-height: 1.5;
  opacity: 0.9;
}

.map-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

.map-action-btn {
  background: rgba(255, 255, 255, 0.2);
  color: white;
  text-decoration: none;
  padding: 0.875rem 1.5rem;
  border-radius: 30px;
  font-weight: 600;
  font-size: 0.9rem;
  transition: all 0.3s ease;
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.3);
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.map-action-btn:hover {
  background: rgba(255, 255, 255, 0.3);
  transform: translateY(-3px);
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.map-action-btn.phone:hover {
  background: rgba(59, 130, 246, 0.8);
}

.map-action-btn.directions:hover {
  background: rgba(16, 185, 129, 0.8);
}

/* Responsividade */
@media (max-width: 768px) {
  .google-map-container {
    padding: 1rem;
    margin: 1rem 0;
  }
  
  .map-fallback-content {
    padding: 1.5rem;
  }
  
  .map-actions {
    flex-direction: column;
    align-items: center;
    gap: 0.75rem;
  }
  
  .map-action-btn {
    min-width: 150px;
    justify-content: center;
  }
}
</style> 
