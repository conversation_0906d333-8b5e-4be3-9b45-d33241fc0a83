import axios from 'axios';
import { useAuthStore } from '@/stores/auth.store';
import { API_URLS } from './api.config';
import tokenManager from '@/services/tokenManager.service';
import deviceDetection from '@/utils/deviceDetection';

// Configura a base URL padrão para o axios
axios.defaults.baseURL = API_URLS.SETTINGS_BASE_URL;

// Generate unique request ID for tracking
const generateRequestId = () => {
  return `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
};

// Adiciona um interceptor para incluir o token JWT em todas as requisições
axios.interceptors.request.use(async (request) => {
  // Obtém o store de autenticação
  const authStore = useAuthStore();

  // Generate request ID for tracking and security
  const requestId = generateRequestId();
  request.headers['X-Request-ID'] = requestId;
  request.headers['X-Client-Version'] = '1.0.0'; // You can get this from package.json
  request.headers['X-Timestamp'] = new Date().toISOString();

  // Add device fingerprint headers for session validation
  try {
    const deviceFingerprint = deviceDetection.generateDeviceFingerprint();
    const sessionInfo = deviceDetection.getSessionIdentificationInfo();

    request.headers['X-Device-Fingerprint'] = deviceFingerprint;
    request.headers['X-Device-Info'] = sessionInfo.deviceInfo;
    request.headers['X-User-Agent'] = sessionInfo.userAgent;
    request.headers['X-Screen-Resolution'] = sessionInfo.screenResolution;
    request.headers['X-Timezone'] = sessionInfo.timezone;
  } catch (error) {
    console.warn('Failed to add device fingerprint headers:', error);
  }

  // Determine which token to use based on the request URL
  let token = null;
  let tokenType = 'none';
  
  // For user operations (store listing, user info, user profile), use userToken
  if (request.url && (request.url.includes('/user/stores') || request.url.includes('/user/') || request.url.includes('/auth/user/'))) {
    token = authStore.userToken;
    tokenType = 'user';
  }
  // For store authentication endpoint, use userToken
  else if (request.url && request.url.includes('/authenticate-store')) {
    token = authStore.userToken;
    tokenType = 'user';
  }
  // For all other store operations, use storeToken
  else {
    token = authStore.storeToken;
    tokenType = 'store';
    if (!token) {
      // Fallback to userToken if no storeToken (for initial login flow)
      token = authStore.userToken;
      tokenType = 'user';
    }
  }

  // Token validation and refresh logic
  if (token) {
    const validation = tokenManager.validateToken(token);
    
    if (!validation.isValid) {
      console.warn(`Invalid ${tokenType} token detected, redirecting to login`);
      authStore.logout();
      return Promise.reject(new Error('Invalid token'));
    }
    
    // Check if token needs refresh
    if (tokenManager.needsRefresh(token)) {
      // Token needs refresh
      
      try {
        let newToken = null;
        
        if (tokenType === 'user') {
          newToken = await tokenManager.refreshUserToken(token);
          if (newToken) {
            authStore.userToken = newToken;
            token = newToken;
          }
        } else if (tokenType === 'store') {
          // For store token refresh, we need the current store ID and a valid user token
          const storeStore = await import('@/stores/store.store');
          const selectedStore = storeStore.useStoreStore().selectedStore;
          
          if (selectedStore?.id && authStore.userToken) {
            newToken = await tokenManager.refreshStoreToken(selectedStore.id, authStore.userToken);
            if (newToken) {
              authStore.setStoreToken(newToken);
              token = newToken;
            }
          }
        }
        
        if (!newToken) {
          console.error(`Failed to refresh ${tokenType} token`);
          // Don't logout immediately, let the request proceed and handle 401 in response interceptor
        }
      } catch (error) {
        console.error(`Error refreshing ${tokenType} token:`, error);
        // Don't logout immediately, let the request proceed and handle 401 in response interceptor
      }
    }

    // Add authorization header
    request.headers['Authorization'] = `Bearer ${token}`;
    
    // Add store ID header if using store token
    if (tokenType === 'store') {
      const userInfo = tokenManager.extractUserInfo(token);
      if (userInfo?.storeId) {
        request.headers['X-Store-ID'] = userInfo.storeId;
      }
    }
  }

  // Reset session timeout on any authenticated request
  if (token && tokenType === 'user') {
    tokenManager.resetSessionTimeout('user_session', () => {
      // User session timed out
      authStore.logout();
    });
  }

  return request;
}, error => {
  return Promise.reject(error);
});

// Adiciona um interceptor para tratar respostas de erro
axios.interceptors.response.use(
  response => {
    // Request completed successfully (logging disabled)
    return response;
  },
  async error => {
    const originalRequest = error.config;
    const requestId = originalRequest?.headers?.['X-Request-ID'];
    
    if (error.response?.status === 401 && !originalRequest._retryAttempted) {
      console.warn(`Unauthorized request ${requestId} - attempting token refresh`);
      
      // Mark request as retried to prevent infinite loops
      originalRequest._retryAttempted = true;
      
      const authStore = useAuthStore();
      
      // Try to refresh token before giving up
      try {
        let refreshSuccess = false;
        
        // Determine token type from original request
        const isUserRequest = originalRequest.url?.includes('/user/') || 
                             originalRequest.url?.includes('/authenticate-store');
        
        if (isUserRequest && authStore.userToken) {
          // Try refreshing user token
          const newUserToken = await tokenManager.refreshUserToken(authStore.userToken);
          if (newUserToken) {
            authStore.userToken = newUserToken;
            originalRequest.headers['Authorization'] = `Bearer ${newUserToken}`;
            refreshSuccess = true;
          }
        } else if (!isUserRequest) {
          // Try refreshing store token
          const storeStore = await import('@/stores/store.store');
          const selectedStore = storeStore.useStoreStore().selectedStore;
          
          if (selectedStore?.id && authStore.userToken) {
            const newStoreToken = await tokenManager.refreshStoreToken(selectedStore.id, authStore.userToken);
            if (newStoreToken) {
              authStore.setStoreToken(newStoreToken);
              originalRequest.headers['Authorization'] = `Bearer ${newStoreToken}`;
              originalRequest.headers['X-Store-ID'] = selectedStore.id;
              refreshSuccess = true;
            } else {
              console.warn(`Store token refresh failed for request ${requestId} - no new token received`);
            }
          } else {
            console.warn(`Store token refresh failed for request ${requestId} - missing store ID or user token`);
          }
        }
        
        if (refreshSuccess) {
          // Token refresh successful, retrying request
          return axios(originalRequest);
        } else {
          // Token refresh failed - force logout
          console.error(`Token refresh failed for request ${requestId} - forcing logout`);
          authStore.logout();
          return Promise.reject(error);
        }
      } catch (refreshError) {
        console.error(`Token refresh error for request ${requestId}:`, refreshError);
        // On refresh error, also force logout
        authStore.logout();
        return Promise.reject(refreshError);
      }
    }
    
    // Log other errors for monitoring
    if (error.response?.status >= 500) {
      console.error(`Server error ${error.response.status} for request ${requestId}:`, error.response.data);
    } else if (error.response?.status >= 400) {
      console.warn(`Client error ${error.response.status} for request ${requestId}:`, error.response.data);
    }
    
    return Promise.reject(error);
  }
);