<template>
  <div class="metric-card">
    <div class="metric-content">
      <div class="metric-value">
        <span v-if="currency" class="currency">R$</span>
        {{ formattedValue }}
      </div>
      <div class="metric-change" :class="changeClass">
        <component :is="trendIcon" class="trend-icon" />
        <span>{{ Math.abs(change) }}%</span>
        <span class="change-label">{{ changeLabel }}</span>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { AnalyticsUpIcon, AnalyticsDownIcon, MinusSignCircleIcon } from '@hugeicons-pro/core-stroke-rounded'

const props = defineProps({
  value: {
    type: Number,
    required: true
  },
  change: {
    type: Number,
    default: 0
  },
  trend: {
    type: String,
    default: 'up', // 'up', 'down', 'neutral'
    validator: value => ['up', 'down', 'neutral'].includes(value)
  },
  currency: {
    type: Boolean,
    default: false
  },
  showDecimal: {
    type: Boolean,
    default: false
  }
})

const { t } = useI18n()

const formattedValue = computed(() => {
  if (props.currency) {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    }).format(props.value)
  }
  if (props.showDecimal) {
    return new Intl.NumberFormat('pt-BR', {
      minimumFractionDigits: 1,
      maximumFractionDigits: 1
    }).format(props.value)
  }
  return new Intl.NumberFormat('pt-BR').format(props.value)
})

const trendIcon = computed(() => {
  switch (props.trend) {
    case 'up': return AnalyticsUpIcon
    case 'down': return AnalyticsDownIcon
    default: return MinusSignCircleIcon
  }
})

const changeClass = computed(() => {
  return {
    'trend-up': props.trend === 'up',
    'trend-down': props.trend === 'down',
    'trend-neutral': props.trend === 'neutral'
  }
})

const changeLabel = computed(() => {
  switch (props.trend) {
    case 'up': return t('dashboard.increase')
    case 'down': return t('dashboard.decrease')
    default: return t('dashboard.stable')
  }
})
</script>

<style scoped>
.metric-card {
  height: 100%;
  display: flex;
  flex-direction: column;
  justify-content: center;
}

.metric-content {
  text-align: center;
}

.metric-value {
  font-size: 2.5rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin-bottom: 8px;
  line-height: 1;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.currency {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  margin-right: 4px;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.metric-change {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  transition: all 0.2s ease;
}

.trend-icon {
  width: 16px;
  height: 16px;
}

.change-label {
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.trend-up {
  color: var(--iluria-color-success);
}

.trend-up .trend-icon {
  color: var(--iluria-color-success);
}

.trend-down {
  color: var(--iluria-color-error);
}

.trend-down .trend-icon {
  color: var(--iluria-color-error);
}

.trend-neutral {
  color: var(--iluria-color-text-secondary);
}

.trend-neutral .trend-icon {
  color: var(--iluria-color-text-secondary);
}

@media (max-width: 768px) {
  .metric-value {
    font-size: 2rem;
  }
  
  .currency {
    font-size: 1.25rem;
  }
  
  .metric-change {
    font-size: 0.8rem;
  }
  
  .trend-icon {
    width: 14px;
    height: 14px;
  }
}
</style> 
