// Configuração de autenticação para o Layout Editor
import { useAuthStore } from '@/stores/auth.store'
import { API_URLS } from './api.config'

// Configurações específicas do Layout Editor
export const AUTH_CONFIG = {
  ENDPOINTS: {
    // Endpoint correto do DevEnvFilter
    DEV_ENV: API_URLS.SETTINGS_BASE_URL + '/dev-env'
  }
}

// Função para obter o token de autenticação
export const getAuthToken = () => {
  const authStore = useAuthStore()
  return authStore.jwtToken || ''
}

// Função para verificar se o usuário está autenticado
export const isAuthenticated = () => {
  const authStore = useAuthStore()
  return authStore.userLoggedIn && !!authStore.jwtToken
}

// Função para obter informações do usuário
export const getUserInfo = () => {
  const authStore = useAuthStore()
  return {
    email: authStore.userEmail,
    name: authStore.userName,
    isLoggedIn: authStore.userLoggedIn
  }
}

export default {
  AUTH_CONFIG,
  getAuthToken,
  isAuthenticated,
  getUserInfo
} 