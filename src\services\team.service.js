import axios from 'axios';
import { API_URLS } from '../config/api.config';

const endpoint = API_URLS.TEAM_BASE_URL;

export const teamApi = {
  // Buscar todos os membros da equipe da loja
  getTeamMembers: (params = {}) => {
    return axios.get(`${endpoint}/members`, { params });
  },

  // Convidar novo membro
  inviteMember: (inviteData) => {
    return axios.post(`${endpoint}/invite`, inviteData);
  },

  // Remover membro da equipe
  removeMember: (memberId) => {
    return axios.delete(`${endpoint}/members/${memberId}`);
  },

  // Buscar convites pendentes da loja
  getPendingInvitations: () => {
    return axios.get(`${endpoint}/invitations`);
  },

  // Cancelar convite pendente
  cancelInvitation: async (invitationId) => {
    try {
      const response = await axios.delete(`${endpoint}/invitations/${invitationId}`);
      
      // Also cancel the related notification
      try {
        const notificationService = (await import('./notification.service')).default;
        await notificationService.cancelNotificationByInvitation(invitationId);
      } catch (notifError) {
        console.warn('Failed to cancel notification for invitation:', invitationId, notifError);
      }
      
      return response;
    } catch (error) {
      console.error('Error canceling invitation:', error);
      throw error;
    }
  },

  // Buscar cargos disponíveis dinamicamente
  getAvailableRoles: () => {
    return axios.get(`${endpoint}/available-roles`);
  },

  // Verificar se um usuário existe no sistema
  checkUserExists: (email) => {
    return axios.get(`${endpoint}/check-user-exists`, { params: { email } });
  }
};