<template>
  <div class="store-physical-data-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="$t('storePhysicalData.title')"
      :subtitle="$t('storePhysicalData.subtitle')"
      :showSave="true"
      :saveText="$t('storePhysicalData.saveButton')"
      @save-click="saveData"
    />

    <!-- Main Content -->
    <Form 
      v-if="dataLoaded" 
      v-slot="$form" 
      @submit="saveData" 
      :resolver="resolver" 
      :validate-on-submit="true" 
      :validate-on-blur="true" 
      :validate-on-value-update="false" 
      :initial-values="formData" 
      class="form-container"
    >
      
      <!-- Dados da Loja -->
      <ViewContainer 
        :title="$t('storePhysicalData.storeDataTitle')"
        :subtitle="$t('storePhysicalData.storeDataSubtitle')"
        :icon="Store01Icon"
        iconColor="blue"
      >
        <div class="form-grid">
          <!-- Nome da Loja -->
          <div class="form-field">
            <IluriaInputText 
              id="storeName"
              :label="$t('storePhysicalData.storeName')"
              v-model="formData.storeName" 
              :placeholder="$t('storePhysicalData.storeNamePlaceholder')"
              :formContext="$form.storeName"
            />
          </div>

          <!-- Tipo da Loja -->
          <div class="form-field">
            <IluriaLabel :for="'storeType'" labelClass="mb-2 block">
              {{ $t('storePhysicalData.storeType') }}
            </IluriaLabel>
            <IluriaSelect
              id="storeType"
              v-model="formData.storeType"
              :options="storeTypeOptions"
              :placeholder="$t('storePhysicalData.storeTypePlaceholder')"
              optionLabel="label"
              optionValue="value"
              :formContext="$form.storeType"
            />
          </div>

          <!-- Documento (CPF/CNPJ) -->
          <div class="form-field">
            <IluriaInputText 
              id="document"
              :label="getDocumentLabel()"
              v-model="formData.document" 
              :placeholder="getDocumentPlaceholder()"
              :mask="getDocumentMask()"
              :formContext="$form.document"
            />
          </div>

          <!-- Endereço -->
          <div class="form-field">
            <IluriaInputText 
              id="address"
              :label="$t('storePhysicalData.address')"
              v-model="formData.address" 
              :placeholder="$t('storePhysicalData.addressPlaceholder')"
              :formContext="$form.address"
            />
          </div>

          <!-- Cidade -->
          <div class="form-field">
            <IluriaInputText 
              id="city"
              :label="$t('storePhysicalData.city')"
              v-model="formData.city" 
              :placeholder="$t('storePhysicalData.cityPlaceholder')"
              :formContext="$form.city"
            />
          </div>

          <!-- Estado -->
          <div class="form-field">
            <IluriaInputText 
              id="state"
              :label="$t('storePhysicalData.state')"
              v-model="formData.state" 
              :placeholder="$t('storePhysicalData.statePlaceholder')"
              :formContext="$form.state"
            />
          </div>

          <!-- CEP -->
          <div class="form-field">
            <IluriaInputText 
              id="zipCode"
              :label="$t('storePhysicalData.zipCode')"
              v-model="formData.zipCode" 
              :placeholder="$t('storePhysicalData.zipCodePlaceholder')"
              mask="cep"
              :formContext="$form.zipCode"
            />
          </div>

          <!-- Email -->
          <div class="form-field">
            <IluriaInputText 
              id="email"
              :label="$t('storePhysicalData.email')"
              v-model="formData.email" 
              :placeholder="$t('storePhysicalData.emailPlaceholder')"
              type="email"
              :formContext="$form.email"
            />
          </div>

          <!-- Telefone -->
          <div class="form-field">
            <IluriaInputText 
              id="phone"
              :label="$t('storePhysicalData.phone')"
              v-model="formData.phone" 
              :placeholder="$t('storePhysicalData.phonePlaceholder')"
              mask="phone"
              :formContext="$form.phone"
            />
          </div>
        </div>
      </ViewContainer>



    </Form>

    <!-- Loading State -->
    <div v-else class="loading-container">
      <div class="loading-spinner"></div>
      <p class="loading-text">{{ $t('common.loading') }}</p>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { z } from 'zod';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import { Form } from '@primevue/forms';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaInputDatePicker from '@/components/iluria/form/IluriaInputDatePicker.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import { useToast } from '@/services/toast.service';
import storePhysicalDataService from '@/services/storePhysicalData.service';
import { requiredText, emailValidator } from '@/services/validation.service';
import {
  Store01Icon
} from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();
const toast = useToast();
const dataLoaded = ref(false);
const isSaving = ref(false);

const formData = reactive({
  storeName: '',
  storeType: 'PESSOA_FISICA',
  document: '',
  address: '',
  city: '',
  state: '',
  zipCode: '',
  email: '',
  phone: ''
});

// Opções para tipo de loja
const storeTypeOptions = computed(() => [
  { label: t('storePhysicalData.personType.fisica'), value: 'PESSOA_FISICA' },
  { label: t('storePhysicalData.personType.juridica'), value: 'PESSOA_JURIDICA' }
]);

// Funções para documentos dinâmicos
const getDocumentLabel = () => {
  return formData.storeType === 'PESSOA_JURIDICA' 
    ? t('storePhysicalData.cnpj') 
    : t('storePhysicalData.cpf');
};

const getDocumentPlaceholder = () => {
  return formData.storeType === 'PESSOA_JURIDICA' 
    ? t('storePhysicalData.cnpjPlaceholder') 
    : t('storePhysicalData.cpfPlaceholder');
};

const getDocumentMask = () => {
  return formData.storeType === 'PESSOA_JURIDICA' ? 'cnpj' : 'cpf';
};

// Validação com Zod
const resolver = zodResolver(
  z.object({
    storeName: requiredText(t('storePhysicalData.storeName')),
    storeType: z.enum(['PESSOA_FISICA', 'PESSOA_JURIDICA'], {
      required_error: t('validation.fieldRequired', { field: t('storePhysicalData.storeType') })
    }),
    document: requiredText(t('storePhysicalData.document')),
    address: requiredText(t('storePhysicalData.address')),
    city: requiredText(t('storePhysicalData.city')),
    state: requiredText(t('storePhysicalData.state')),
    zipCode: requiredText(t('storePhysicalData.zipCode')),
    email: emailValidator(t('storePhysicalData.email')),
    phone: requiredText(t('storePhysicalData.phone'))
  })
);

// Carregar dados existentes
const loadData = async () => {
  try {
    const data = await storePhysicalDataService.getStorePhysicalData();
    
    if (data) {
      // Preencher formulário com dados existentes
      Object.assign(formData, {
        storeName: data.storeName || '',
        storeType: data.storeType || 'PESSOA_FISICA',
        document: data.document || '',
        address: data.address || '',
        city: data.city || '',
        state: data.state || '',
        zipCode: data.zipCode || '',
        email: data.email || '',
        phone: data.phone || ''
      });
    }
  } catch (error) {
    console.error('Erro ao carregar dados:', error);
    toast.showError(t('storePhysicalData.loadError'));
  } finally {
    dataLoaded.value = true;
  }
};

// Salvar dados
const saveData = async () => {
  if (isSaving.value) return;
  
  isSaving.value = true;
  
  try {
    // Preparar dados para envio
    const dataToSave = {
      ...formData
    };
    
    await storePhysicalDataService.saveStorePhysicalData(dataToSave);
    
    toast.showSuccess(t('storePhysicalData.saveSuccess'));
  } catch (error) {
    console.error('Erro ao salvar dados:', error);
    toast.showError(t('storePhysicalData.saveError'));
  } finally {
    isSaving.value = false;
  }
};

// Carregar dados ao montar componente
onMounted(() => {
  loadData();
});
</script>

<style scoped>
.store-physical-data-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
}



.form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(2, 1fr);
  gap: 20px;
  align-items: start;
}

.form-field {
  display: flex;
  flex-direction: column;
}

.form-field-full {
  grid-column: 1 / -1;
}

.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 20px;
  gap: 16px;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 768px) {
  .store-physical-data-container {
    padding: 16px;
  }
  

  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
}
</style> 