import axios from 'axios';
import { API_URLS } from '@/config/api.config';

const BASE_URL = `${API_URLS.PRODUCT_BASE_URL}/combined`;

export class CombinedProductService {
    #separatePhotosForUpload(photos) {
        if (!photos || photos.length === 0) return { forJson: [], forUpload: [] };
        
        const forJson = [];
        const forUpload = [];
        
        photos.slice(0, 5).forEach((photo, index) => {
            if (photo instanceof File) {
                forUpload.push({ file: photo, position: index });
                forJson.push({
                    filename: photo.name,
                    position: index,
                    isNewUpload: true
                });
            } else if (photo && photo.url) {
                forJson.push({
                    url: photo.url,
                    position: index,
                    isNewUpload: false
                });
            }
        });
        
        return { forJson, forUpload };
    }

    async getAllCombinedProducts(page = 0, size = 10) {
        const response = await axios.get(BASE_URL, {
            params: { page, size }
        });
        
        return {
            content: response.data.content || [],
            totalPages: response.data.totalPages || 1,
            totalElements: response.data.totalElements || 0,
            size: response.data.size || size,
            number: response.data.number || page
        };
    }

    async getCombinedProduct(id) {
        try {
            const response = await axios.get(`${BASE_URL}/${id}`);
            return response.data;
        } catch {
            // Fallback: tentar carregar dados salvos localmente
            const localData = localStorage.getItem(`combined-product-${id}`);
            if (localData) {
                const parsedData = JSON.parse(localData);
                return parsedData;
            }
            
            // Se não há dados locais, retornar estrutura básica
            return {
                id: id,
                name: '',
                description: '',
                photos: [],
                stickers: []
            };
        }
    }

    async createCombinedProduct(data) {
        const { forJson, forUpload } = this.#separatePhotosForUpload(data.photos);
        
        const productData = {
            name: data.name,
            description: data.description,
            photos: forJson, 
            stickers: data.stickers || [], 
            type: 'COMBINED',
            status: data.status || 'ACTIVE',
            maxImages: 5,
            allowStickerPlacement: true
        };
        
        try {
            const response = await axios.post(BASE_URL, productData);
            
            if (forUpload.length > 0) {
                const updatedProduct = await this.#uploadImagesAndUpdateProduct(response.data.id, forUpload);
                return updatedProduct;
            }
            
            return response.data;
        } catch {
            // Fallback: salvar localmente
            const productId = Date.now().toString();
            const fullProductData = {
                ...productData,
                id: productId,
                photos: data.photos,
                createdAt: new Date().toISOString()
            };
            
            localStorage.setItem(`combined-product-${productId}`, JSON.stringify(fullProductData));
            
            return fullProductData;
        }
    }

    async updateCombinedProduct(id, data) {
        const { forJson, forUpload } = this.#separatePhotosForUpload(data.photos);
        
        const productData = {
            name: data.name,
            description: data.description,
            photos: forJson, 
            stickers: data.stickers || [], 
            type: 'COMBINED',
            status: data.status || 'ACTIVE',
            maxImages: 5,
            allowStickerPlacement: true
        };
        
        
        try {
            const response = await axios.put(`${BASE_URL}/${id}`, productData);
            
            if (forUpload.length > 0) {
                const updatedProduct = await this.#uploadImagesAndUpdateProduct(id, forUpload);
                return updatedProduct;
            }
            
            return response.data;
        } catch (error) {
            
            
            // Fallback: atualizar dados locais
            const fullProductData = {
                ...productData,
                id: id,
                photos: data.photos,
                updatedAt: new Date().toISOString()
            };
            
            localStorage.setItem(`combined-product-${id}`, JSON.stringify(fullProductData));
            
            return fullProductData;
        }
    }

    async #uploadImagesAndUpdateProduct(productId, filesToUpload) {
        if (!filesToUpload || filesToUpload.length === 0) return null;
        
        const formData = new FormData();
        
        filesToUpload.forEach((fileData) => {
            formData.append('files', fileData.file);
        });
        
        const response = await axios.post(`${BASE_URL}/${productId}/images`, formData, {
            headers: {
                'Content-Type': 'multipart/form-data'
            }
        });
        
        return response.data;
    }

    async deleteCombinedProduct(id) {
        
        if (!id) {
            throw new Error('ID do produto é obrigatório para exclusão');
        }
        
        
        try {
            const response = await axios.delete(`${BASE_URL}/${id}`);
            
            
            // Remover dados locais se existirem
            localStorage.removeItem(`combined-product-${id}`);
            
            return response.data;
        } catch (error) {
            
            // Se o produto existe apenas localmente, remover do localStorage
            const localData = localStorage.getItem(`combined-product-${id}`);
            if (localData) {    
                localStorage.removeItem(`combined-product-${id}`);
                return { success: true, message: 'Produto removido localmente' };
            }
            
            throw error;
        }
    }
}

export const combinedProductService = new CombinedProductService();