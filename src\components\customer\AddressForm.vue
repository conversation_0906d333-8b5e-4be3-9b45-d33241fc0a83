<template>
  <div class="address-form-container">
    <div v-if="props.addresses.length" class="addresses-grid">
      <div 
        v-for="address in props.addresses" 
        :key="address.id || address.tempId || `temp-${address.zipCode}-${address.street}`" 
        class="address-card"
        :class="{ 'primary-address': address.mainAddress }"
      >
        <div class="address-header">
          <div class="address-info">
            <h4 class="address-title">
              {{ address.label || address.street }}
            </h4>
          </div>

          <span 
            v-if="address.mainAddress" 
            class="primary-badge"
          >
            {{ t('addressList.primary') }}
          </span>
        </div>

        <div class="address-details">
          <div class="detail-item">
            <Location01Icon class="detail-icon" />
            <span>{{ address.street }}, {{ address.number }}</span>
          </div>
          
          <div v-if="address.complement" class="detail-item">
            <Building01Icon class="detail-icon" />
            <span>{{ address.complement }}</span>
          </div>
          
          <div class="detail-item">
            <LocationUser03Icon class="detail-icon" />
            <span>{{ address.neighborhood }} - {{ address.city }}/{{ address.state }}</span>
          </div>
          
          <div class="detail-item">
            <DeliveryBox01Icon class="detail-icon" />
            <span>{{ address.zipCode }}</span>
          </div>
          
          <div class="detail-item">
            <UserIcon class="detail-icon" />
            <span>{{ address.recipient }}</span>
          </div>
        </div>

        <div class="address-actions">
          <IluriaButton 
            color="text-primary" 
            size="small" 
            :hugeIcon="PencilEdit01Icon"
            @click="editAddress(address)"
          >
            {{ t('addressList.editAddress') }}
          </IluriaButton>
          
          <IluriaButton 
            color="text-danger" 
            size="small" 
            :hugeIcon="Delete01Icon"
            @click="removeAddress(address)"
          >
            {{ t('addressList.removeAddress') }}
          </IluriaButton>
          
          <IluriaButton 
            v-if="!address.mainAddress" 
            color="text-green-600" 
            size="small" 
            :hugeIcon="StarIcon"
            @click="setPrimaryAddress(address)"
          >
            {{ t('addressList.makePrimary') }}
          </IluriaButton>
        </div>
      </div>
    </div>

    <div v-else class="empty-state">
      <div class="empty-icon">
        <Location01Icon class="w-12 h-12" />
      </div>
      <h3 class="empty-title">{{ t('addressList.noAddresses') }}</h3>
      <p class="empty-description">{{ t('addressList.noAddressesDescription') }}</p>
      <IluriaButton 
        color="dark" 
        :hugeIcon="PlusSignIcon"
        @click="addNewAddress"
        class="mt-4"
      >
        {{ t('addressList.addAddress') }}
      </IluriaButton>
    </div>

    <div v-if="props.addresses.length" class="add-address-section">
      <IluriaButton 
        color="outline" 
        :hugeIcon="PlusSignIcon"
        @click="addNewAddress"
        class="add-address-btn"
      >
        {{ t('addressList.addAddress') }}
      </IluriaButton>
    </div>

    <IluriaModal
      v-model="showModal"
      :title="editingAddressId ? t('addressList.editAddress') : t('addressList.newAddress')"
      @save="saveAddress"
      @cancel="cancelEdit"
      :save-label="t('addressList.addAddress')"
      :cancel-label="t('addressList.cancel')"
      class="address-modal"
    >
      <div class="modal-content">
        <Form 
          @submit="saveAddress" 
          v-slot="$form" 
          :validate-on-blur="true" 
          :validate-on-value-update="false" 
          :initial-values="form"
        >
          <div class="form-field col-span-2 form-grid">
            <div class="zip-code-field">
              <IluriaInputMask
                id="zipCode"
                name="zipCode"
                :label="t('addressList.zip')"
                mask="99999-999"
                placeholder="00000-000"
                v-model="form.zipCode"
                :disabled="isLoadingCEP"
              />
              <div v-if="isLoadingCEP" class="loading-indicator">
                <div class="spinner"></div>
                <span class="loading-text">Buscando CEP...</span>
              </div>
            </div>
          </div>

          <div class="form-grid">
            <div class="form-field col-span-2">
              <IluriaSelect
                name="type"
                :label="t('addressList.addressType')"
                v-model="form.type"
                :options="addressTypes"
                :placeholder="t('addressList.selectType')"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="label"
                name="label"
                :label="t('addressList.nickname')"
                v-model="form.label"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="recipient"
                name="recipient"
                :label="t('addressList.recipient')"
                v-model="form.recipient"
              />
            </div>

            <div class="form-field col-span-4">
              <IluriaInputText
                id="street"
                name="street"
                :label="t('addressList.street')"
                v-model="form.street"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="number"
                name="number"
                :label="t('addressList.number')"
                v-model="form.number"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="complement"
                name="complement"
                :label="t('addressList.complement')"
                v-model="form.complement"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="neighborhood"
                name="neighborhood"
                :label="t('addressList.neighborhood')"
                v-model="form.neighborhood"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="city"
                name="city"
                :label="t('addressList.city')"
                v-model="form.city"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="state"
                name="state"
                :label="t('addressList.state')"
                v-model="form.state"
              />
            </div>

            <div class="form-field col-span-2">
              <IluriaInputText
                id="country"
                name="country"
                :label="t('addressList.country')"
                v-model="form.country"
              />
            </div>


          </div>
        </Form>
      </div>
    </IluriaModal>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from 'primevue/usetoast'
import { Form } from '@primevue/forms'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaInputMask from '@/components/iluria/form/IluriaInputMask.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import CEPAddressService from '@/services/cepAddress.service.js'
import { 
  Location01Icon,
  Building01Icon,
  LocationUser03Icon,
  DeliveryBox01Icon,
  UserIcon,
  PencilEdit01Icon,
  Delete01Icon,
  StarIcon,
  PlusSignIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const toast = useToast()

const requiredFields = [
  { field: 'type', label: t('addressList.addressType') },
  { field: 'label', label: t('addressList.nickname') },
  { field: 'zipCode', label: t('addressList.zip') },
  { field: 'street', label: t('addressList.street') },
  { field: 'number', label: t('addressList.number') },
  { field: 'city', label: t('addressList.city') },
  { field: 'state', label: t('addressList.state') },
  { field: 'country', label: t('addressList.country') },
]

const props = defineProps({
  addresses: {
    type: Array,
    default: () => []
  },
  customerId: {
    type: [String, null],
    required: false,
    default: null
  },
  storeId: {
    type: String,
    required: true
  }
})

const emit = defineEmits(['update:addresses'])

const showModal = ref(false)
const editingAddressId = ref(null)
const hasZIPcode = ref(false)
const isLoadingCEP = ref(false)

const generateTempId = () => {
  return `temp-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`
}

const form = ref({
  id: null,
  tempId: null,
  type: 'residential',
  label: '',
  recipient: '',
  street: '',
  number: '',
  neighborhood: '',
  city: '',
  state: '',
  zipCode: '',
  complement: '',
  country: 'Brasil',
  isPrimary: props.addresses.length === 0
})

watch(() => form.value.zipCode, async (newZipCode, oldZipCode) => {
  if (newZipCode && newZipCode !== oldZipCode) {
    const cleanZipCode = newZipCode.replace(/\D/g, '')
    
    if (cleanZipCode.length === 8) {
      await fullfillAddress(cleanZipCode)
    }
  }
}, { immediate: false })

const addressTypes = ref([
  { label: t('addressList.residential'), value: 'residential' },
  { label: t('addressList.commercial'), value: 'commercial' },
  { label: t('addressList.other'), value: 'other' },
])

const getTypeLabel = (type) => {
  const typeMap = {
    residential: t('addressList.residential'),
    commercial: t('addressList.commercial'),
    other: t('addressList.other')
  }
  return typeMap[type] || type
}

const validateAddressForm = () => {
  for (const { field, label } of requiredFields) {
    if (!form.value[field] || form.value[field].trim() === '') {
      throw new Error(t('validation.required', { field: label }))
    }
  }
}

const shouldSetAsPrimary = () => {
  if (props.addresses.length === 0) {
    return true
  }
  
  if (editingAddressId.value) {
    const otherAddresses = props.addresses.filter(a => 
      (a.id !== editingAddressId.value) && (a.tempId !== editingAddressId.value)
    )
    return !otherAddresses.some(a => a.mainAddress)
  }
  
  return form.value.isPrimary
}

const updateExistingAddress = (updatedAddresses) => {
  const index = updatedAddresses.findIndex(a => (a.id === editingAddressId.value) || (a.tempId === editingAddressId.value))
  if (index !== -1) {
    updatedAddresses[index] = {
      ...updatedAddresses[index],
      type: form.value.type,
      label: form.value.label,
      recipient: form.value.recipient,
      street: form.value.street,
      number: form.value.number,
      complement: form.value.complement,
      neighborhood: form.value.neighborhood,
      city: form.value.city,
      state: form.value.state,
      zipCode: form.value.zipCode,
      country: form.value.country,
      mainAddress: form.value.isPrimary,
      id: updatedAddresses[index].id,
      tempId: updatedAddresses[index].tempId,
      customerId: updatedAddresses[index].customerId,
    }
  }
  return 'update'
}

const createNewAddress = (updatedAddresses) => {
  const newAddress = {
    id: null,
    tempId: generateTempId(),
    customerId: props.customerId,
    type: form.value.type,
    label: form.value.label,
    recipient: form.value.recipient,
    street: form.value.street,
    number: form.value.number,
    complement: form.value.complement,
    neighborhood: form.value.neighborhood,
    city: form.value.city,
    state: form.value.state,
    zipCode: form.value.zipCode,
    country: form.value.country,
    mainAddress: form.value.isPrimary,
  }
  
  updatedAddresses.push(newAddress)
  return 'create'
}

const unmarkOtherPrimaryAddresses = (updatedAddresses, operationType) => {
  if (!form.value.isPrimary) return
  
  if (operationType === 'update') {
    updatedAddresses.forEach(addr => {
      if ((addr.id !== editingAddressId.value) && (addr.tempId !== editingAddressId.value)) {
        addr.mainAddress = false
      }
    })
  } else {
    updatedAddresses.forEach((addr, index) => {
      if (index !== updatedAddresses.length - 1) {
        addr.mainAddress = false
      }
    })
  }
}

const saveAddress = async () => {
  try {
    validateAddressForm()
    const updatedAddresses = [...props.addresses]
    form.value.isPrimary = shouldSetAsPrimary()
    
    const operationType = editingAddressId.value 
      ? updateExistingAddress(updatedAddresses)
      : createNewAddress(updatedAddresses)

    unmarkOtherPrimaryAddresses(updatedAddresses, operationType)

    emit('update:addresses', updatedAddresses)
    
    toast.add({ 
      severity: 'success', 
      summary: t('success'), 
      detail: operationType === 'update' ? t('addressList.updateSuccess') : t('addressList.createSuccess'),
      life: 3000 
    })
    
    showModal.value = false
    resetForm()
    return true

  } catch (error) {
    if (error instanceof Error) {
      console.error('Validation errors:', error.message)
      toast.add({
        severity: 'warn',
        summary: t('validation.title') || 'Validação',
        detail: error.message,
        life: 4000
      })
      return false
    }
    console.error('Error saving address:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('error'), 
      detail: t('addressList.saveError'), 
      life: 3000 
    })
    return false
  }
}


const addNewAddress = () => {
  resetForm()
  showModal.value = true
}

const editAddress = (address) => {
  editingAddressId.value = address.id || address.tempId
  form.value = {
    id: address.id,
    tempId: address.tempId,
    type: address.type,
    label: address.label || '',
    recipient: address.recipient || '',
    street: address.street || '',
    number: address.number || '',
    neighborhood: address.neighborhood || '',
    city: address.city || '',
    state: address.state || '',
    zipCode: address.zipCode || '',
    complement: address.complement || '',
    country: address.country || 'Brasil',
    isPrimary: address.mainAddress || false,
  }
  showModal.value = true
}

const removeAddress = async (addressToRemove) => {
  try {
    const addressId = typeof addressToRemove === 'object' ? (addressToRemove.id || addressToRemove.tempId) : addressToRemove
    
    const removedAddress = props.addresses.find(a => (a.id === addressId) || (a.tempId === addressId))
    const updatedAddresses = props.addresses.filter(a => (a.id !== addressId) && (a.tempId !== addressId))
    
    if (removedAddress?.mainAddress && updatedAddresses.length > 0) {
      updatedAddresses[0].mainAddress = true
    }
    
    emit('update:addresses', updatedAddresses)
    
    toast.add({ 
      severity: 'success', 
      summary: t('success'), 
      detail: t('addressList.deleteSuccess'), 
      life: 3000 
    })
  } catch (error) {
    console.error('Error deleting address:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('error'), 
      detail: t('addressList.deleteError'), 
      life: 3000 
    })
  }
}

const setPrimaryAddress = async (address) => {
  try {
    const targetId = address.id || address.tempId
    const updatedAddresses = props.addresses.map(addr => ({
      ...addr,
      mainAddress: (addr.id === targetId) || (addr.tempId === targetId)
    }))
    
    emit('update:addresses', updatedAddresses)
    
    toast.add({ 
      severity: 'success', 
      summary: t('success'), 
      detail: t('addressList.primarySuccess'), 
      life: 3000 
    })
  } catch (error) {
    console.error('Error setting primary address:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('error'), 
      detail: t('addressList.primaryError'), 
      life: 3000 
    })
  }
}

const cancelEdit = () => {
  showModal.value = false
  resetForm()
}

const resetForm = () => {
  form.value = {
    id: null,
    tempId: null,
    type: 'residential',
    label: '',
    recipient: '',
    street: '',
    number: '',
    neighborhood: '',
    city: '',
    state: '',
    zipCode: '',
    complement: '',
    country: 'Brasil',
    isPrimary: props.addresses.length === 0,
  }
  editingAddressId.value = null
}

const fullfillAddress = async (cep) => {
  if (!cep || cep.length !== 8) {
    return
  }

  try {
    isLoadingCEP.value = true

    const response = await getCEPAddress(cep)

    if (response?.city) { 
      form.value.label = response.name || ''
      form.value.street = response.publicArea || ''
      form.value.neighborhood = response.neighborhood || ''
      form.value.city = response.city || ''
      form.value.state = response.state || ''
      form.value.number = response.complement || ''
      form.value.complement = ''
      form.value.recipient = ''

      hasZIPcode.value = true

      toast.add({
        severity: 'success',
        summary: t('success'),
        detail: 'Endereço encontrado no banco e preenchido automaticamente.',
        life: 3000
      })
    }
  } catch (error) {
    console.error('Error fetching CEP address:', error)
    toast.add({
      severity: 'error',
      summary: t('error'),
      detail: 'Erro ao conectar com o serviço de CEP. Tente novamente.',
      life: 3000
    })
  } finally {
    isLoadingCEP.value = false
  }
}

const getCEPAddress = async (cep) => {
  const response = await CEPAddressService.getCEPAddress(cep)
  return response
}
</script>

<style scoped>
.address-form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
}

.addresses-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.address-card {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
  position: relative;
  background: var(--iluria-color-container-bg);
  display: flex;
  flex-direction: column;
  min-height: 280px;
}

.address-card:hover {
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-md);
}

.address-card.primary-address {
  border-color: var(--iluria-color-primary);
  background: color-mix(in srgb, var(--iluria-color-primary) 8%, transparent);
  background: rgba(59, 130, 246, 0.05);
}

.address-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 16px;
}

.address-info {
  display: flex;
  flex-direction: column;
  gap: 6px;
}

.address-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.3;
}

.primary-badge {
  position: absolute;
  top: 16px;
  right: 16px;
  border: 1px solid var(--iluria-color-primary);
  color: var(--iluria-color-primary);
  background: color-mix(in srgb, var(--iluria-color-primary) 20%, transparent);
  background: rgba(59, 130, 246, 0.15);
  font-size: 11px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 9999px;
  text-transform: uppercase;
  letter-spacing: 0.6px;
}

.address-details {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 20px;
}

.detail-item {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

.detail-icon {
  width: 14px;
  height: 14px;
  color: var(--iluria-color-text-muted);
  flex-shrink: 0;
}

.address-actions {
  display: flex;
  gap: 8px;
  flex-wrap: wrap;
  margin-top: auto;
  padding-top: 16px;
  border-top: 1px solid var(--iluria-color-border);
}

.address-actions :deep(button) {
  border: 1px solid var(--iluria-color-border);
}

.empty-state {
  text-align: center;
  padding: 48px 24px;
  color: var(--iluria-color-text-secondary);
  background: var(--iluria-color-surface);
  border: 1px dashed var(--iluria-color-border);
  border-radius: 12px;
}

.empty-icon {
  display: flex;
  justify-content: center;
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.add-address-section {
  display: flex;
  justify-content: center;
  padding: 16px 0;
}

.add-address-btn {
  min-width: 200px;
}

.address-modal {
  width: 90vw;
  max-width: 800px;
}

.modal-content {
  padding: 0;
}

.form-grid {
  display: grid;
  grid-template-columns: repeat(6, 1fr);
  gap: 20px;
  align-items: start;
  padding-top: 16px;
}

.form-field {
  display: flex;
  flex-direction: column;
}


:deep(.form-field label),
:deep(.iluria-input-label) {
  color: var(--iluria-color-text-primary) !important;
  opacity: 1 !important;
}

.col-span-2 {
  grid-column: span 2;
}

.col-span-3 {
  grid-column: span 3;
}

.col-span-4 {
  grid-column: span 4;
}

.col-span-6 {
  grid-column: span 6;
}

.zip-code-field {
  position: relative;
}

.loading-indicator {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-top: 8px;
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

.spinner {
  width: 14px;
  height: 14px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-text {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}



@media (max-width: 768px) {
  .addresses-grid {
    grid-template-columns: 1fr;
  }
  
  .address-card {
    padding: 16px;
  }
  
  .address-header {
    flex-direction: column;
    gap: 12px;
    align-items: flex-start;
  }
  
  .address-actions {
    flex-direction: column;
  }
  
  .form-grid {
    grid-template-columns: 1fr;
    gap: 16px;
  }
  
  .col-span-2,
  .col-span-3,
  .col-span-4,
  .col-span-6 {
    grid-column: span 1;
  }
  
  .address-modal {
    width: 95vw;
  }
}

@media (max-width: 480px) {
  .empty-state {
    padding: 32px 16px;
  }
  
  .address-form-container {
    gap: 16px;
  }
}
</style>
