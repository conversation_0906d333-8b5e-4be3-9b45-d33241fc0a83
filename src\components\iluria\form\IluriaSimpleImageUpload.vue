<template>
  <div class="simple-image-upload">
    <label v-if="label" :for="inputId" class="upload-label">
      {{ label }}
      <span v-if="required" class="text-red-500">*</span>
    </label>

    <div :class="['upload-container', { 'with-border': showBorder }]">
      <div v-if="!previewUrl && !modelValue" class="upload-empty-state">
        <svg class="upload-icon" stroke="currentColor" fill="none" viewBox="0 0 48 48" aria-hidden="true">
          <path d="M28 8H12a4 4 0 00-4 4v20m32-12v8m0 0v8a4 4 0 01-4 4H12a4 4 0 01-4-4v-4m32-4l-3.172-3.172a4 4 0 00-5.656 0L28 28M8 32l9.172-9.172a4 4 0 015.656 0L28 28m0 0l4 4m4-24h8m-4-4v8m-12 4h.02" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" />
        </svg>
        <div class="upload-button-container">
          <label class="upload-button">
            <span>{{ addButtonText || 'Escolher Arquivo' }}</span>
            <input
              :id="inputId"
              type="file"
              class="sr-only"
              :accept="accept"
              :multiple="multiple"
              @change="onFileSelect"
            >
          </label>
        </div>
        <p v-if="formatHint" class="upload-hint">
          {{ formatHint }}
        </p>
      </div>

      <div v-else class="relative w-full h-full flex flex-col items-center justify-center">
        <div class="image-container" style="width: 100%; text-align: center; min-height: 180px; display: flex; align-items: center; justify-content: center;">
          <img
            :key="previewUrl || modelValue"
            :src="previewUrl || modelValue"
            alt="Visualização"
            class="mx-auto object-contain"
            style="max-width: 100%; max-height: 180px;"
          >
        </div>
      </div>
    </div>
    
    <!-- Botões posicionados fora da grid quando há imagem -->
    <div v-if="previewUrl || modelValue" class="action-buttons">
      <button
        v-if="!disabled"
        type="button"
        @click="openFileDialog"
        class="action-button action-button-change"
      >
        {{ changeButtonText || 'Alterar' }}
      </button>
      <button
        v-if="!disabled"
        type="button"
        @click="removeImage"
        class="action-button action-button-remove"
      >
        {{ removeButtonText || 'Remover' }}
      </button>
    </div>

    <p v-if="errorMessage" class="mt-1 text-sm text-red-600">
      {{ errorMessage }}
    </p>
  </div>
</template>

<script setup>
import { ref, watch } from 'vue';

const inputId = `file-upload-${Math.random().toString(36).substring(2, 11)}`;

const props = defineProps({
  modelValue: {
    type: [String, File],
    default: ''
  },
  label: {
    type: String,
    default: ''
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  multiple: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  errorMessage: {
    type: String,
    default: ''
  },
  addButtonText: {
    type: String,
    default: ''
  },
  changeButtonText: {
    type: String,
    default: ''
  },
  removeButtonText: {
    type: String,
    default: ''
  },
  formatHint: {
    type: String,
    default: ''
  },
  preventCache: {
    type: Boolean,
    default: true
  },
  showBorder: {
    type: Boolean,
    default: true
  }
});

const emit = defineEmits(['update:modelValue', 'change']);

const selectedFile = ref(null);
const previewUrl = ref('');

function addCacheBuster(url) {
  if (!url || typeof url !== 'string' || !props.preventCache) return url;

  if (url.startsWith('data:')) return url;

  // Remove existing cache buster if present
  const baseUrl = url.split('?t=')[0];

  const timestamp = new Date().getTime();
  return `${baseUrl}?t=${timestamp}`;
}

watch(() => props.modelValue, (newValue, oldValue) => {
  if (!newValue) {
    previewUrl.value = '';
    selectedFile.value = null;
  } else if (newValue instanceof File) {
    createPreview(newValue);
  } else if (typeof newValue === 'string' && newValue) {
    // Always apply cache buster, especially when URL changes
    const newUrlWithCache = addCacheBuster(newValue);
    previewUrl.value = newUrlWithCache;

  }
}, { immediate: true });

function onFileSelect(event) {
  const files = event.target.files;
  if (!files || files.length === 0) return;

  const file = files[0];
  
  if (!file.type.startsWith('image/')) {
    emit('update:modelValue', null);
    return;
  }

  selectedFile.value = file;
  createPreview(file);
  emit('update:modelValue', file);
  emit('change', file);
  
  event.target.value = '';
}

function openFileDialog() {
  const fileInput = document.createElement('input');
  fileInput.type = 'file';
  fileInput.accept = props.accept;
  fileInput.style.display = 'none';
  document.body.appendChild(fileInput);
  
  fileInput.onchange = (event) => {
    onFileSelect(event);
    document.body.removeChild(fileInput);
  };
  
  fileInput.click();
}

function createPreview(file) {
  if (!file) {
    previewUrl.value = '';
    return;
  }
  
  const reader = new FileReader();
  reader.onload = (e) => {
    previewUrl.value = e.target.result;
  };
  reader.readAsDataURL(file);
}

function removeImage() {
  selectedFile.value = null;
  previewUrl.value = '';
  emit('update:modelValue', null);
  emit('change', null);
}
</script>

<style scoped>
.simple-image-upload {
  width: 100%;
}

.upload-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

.upload-container {
  padding: 1rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 200px;
  background: var(--iluria-color-surface);
  border-radius: 0.5rem;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.upload-container.with-border {
  border: 2px dashed var(--iluria-color-border);
}

.upload-empty-state {
  text-align: center;
  width: 100%;
}

.upload-icon {
  margin: 0 auto;
  height: 3rem;
  width: 3rem;
  color: var(--iluria-color-text-tertiary);
}

.upload-button-container {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
}

.upload-button {
  position: relative;
  cursor: pointer;
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  border: 1px solid var(--iluria-color-border);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  color: var(--iluria-color-text-primary);
  background-color: var(--iluria-color-surface);
  transition: all 0.2s ease;
}

.upload-button:hover {
  background-color: var(--iluria-color-surface-hover);
  border-color: var(--iluria-color-border-hover);
  transform: translateY(-1px);
}

.upload-button:active {
  transform: translateY(0);
}

.upload-hint {
  margin-top: 0.5rem;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
}

.image-container {
  width: 100%;
  text-align: center;
  min-height: 180px;
  display: flex;
  align-items: center;
  justify-content: center;
}

.action-buttons {
  margin-top: 1rem;
  display: flex;
  justify-content: center;
  gap: 0.75rem;
}

.action-button {
  display: inline-flex;
  align-items: center;
  padding: 0.5rem 1rem;
  font-size: 0.875rem;
  font-weight: 500;
  border-radius: 0.375rem;
  transition: all 0.2s ease;
  cursor: pointer;
  border: 1px solid;
  outline: none;
}

.action-button:focus {
  outline: 2px solid;
  outline-offset: 2px;
}

.action-button:hover {
  transform: translateY(-1px);
}

.action-button:active {
  transform: translateY(0);
}

.action-button-change {
  border-color: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
  background-color: var(--iluria-color-surface);
  box-shadow: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
}

.action-button-change:hover {
  background-color: var(--iluria-color-surface-hover);
  border-color: var(--iluria-color-border-hover);
}

.action-button-change:focus {
  outline-color: var(--iluria-color-primary);
}

.action-button-remove {
  border-color: var(--iluria-color-button-danger-bg);
  color: var(--iluria-color-button-danger-fg);
  background-color: var(--iluria-color-button-danger-bg);
}

.action-button-remove:hover {
  background-color: var(--iluria-color-button-danger-bg-hover);
  border-color: var(--iluria-color-button-danger-bg-hover);
}

.action-button-remove:focus {
  outline-color: var(--iluria-color-button-danger-bg);
}

.simple-image-upload input[type="file"] {
  display: none;
}

/* Tema escuro - ajustes específicos */
:deep(.theme-dark) .upload-container {
  background: var(--iluria-color-surface);
  border-color: var(--iluria-color-border);
}

:deep(.theme-dark) .upload-button {
  background-color: var(--iluria-color-surface);
  border-color: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
}

:deep(.theme-dark) .upload-button:hover {
  background-color: var(--iluria-color-surface-hover);
  border-color: var(--iluria-color-border-hover);
}

:deep(.theme-dark) .action-button-change {
  background-color: var(--iluria-color-surface);
  border-color: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
}

:deep(.theme-dark) .action-button-change:hover {
  background-color: var(--iluria-color-surface-hover);
  border-color: var(--iluria-color-border-hover);
}
</style>
