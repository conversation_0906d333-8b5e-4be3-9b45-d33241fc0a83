import axios from 'axios';
import { API_URLS } from '../config/api.config';

const endpoint = API_URLS.ATTRIBUTE_BASE_URL;
const productsEndpoint = API_URLS.PRODUCT_BASE_URL;

export const attributesApi = {
  // Listar todos os atributos
  getAll: (params = {}) => {
    const { page = 0, size = 50, name = '', categoryId = null } = params;
    
    return axios.get(endpoint, {
      params: {
        page,
        size,
        name: name || undefined,
        categoryId: categoryId || undefined
      }
    });
  },

  // Buscar atributos por múltiplas categorias
  getAllByMultipleCategories: async (params = {}) => {
    const { page = 0, size = 50, name = '', categoryIds = [] } = params;
    
    if (!categoryIds || categoryIds.length === 0) {
      return attributesApi.getAll(params);
    }
    
    if (categoryIds.length === 1) {
      return attributesApi.getAll({ ...params, categoryId: categoryIds[0] });
    }
    
    // Para múltiplas categorias, fazer chamadas paralelas e consolidar
    try {
      const promises = categoryIds.map(categoryId => 
        axios.get(endpoint, {
          params: {
            page: 0, // Buscar todas as páginas para cada categoria
            size: 1000, // Tamanho grande para pegar todos os atributos
            name: name || undefined,
            categoryId
          }
        })
      );
      
      const responses = await Promise.all(promises);
      
      // Consolidar resultados
      const allAttributes = [];
      const attributeIds = new Set();
      
      responses.forEach(response => {
        const attributes = response.data?.content || response.data || [];
        attributes.forEach(attr => {
          if (!attributeIds.has(attr.id)) {
            allAttributes.push(attr);
            attributeIds.add(attr.id);
          }
        });
      });
      
      // Simular paginação no frontend
      const startIndex = page * size;
      const endIndex = startIndex + size;
      const paginatedAttributes = allAttributes.slice(startIndex, endIndex);
      
      return {
        data: {
          content: paginatedAttributes,
          totalElements: allAttributes.length,
          totalPages: Math.ceil(allAttributes.length / size),
          size: size,
          number: page
        }
      };
      
    } catch (error) {
      console.error('Erro ao buscar atributos por múltiplas categorias:', error);
      throw error;
    }
  },

  // Buscar atributos por categoria
  getByCategoryId: (categoryId) => {
    return axios.get(`${endpoint}/category/${categoryId}`).then(response => response.data);
  },

  // Buscar atributo por ID
  getById: (id) => {
    return axios.get(`${endpoint}/${id}`).then(response => response.data);
  },

  // Criar novo atributo
  create: (attribute) => {
    return axios.post(endpoint, attribute);
  },

  // Atualizar atributo
  update: (id, attribute) => {
    return axios.put(`${endpoint}/${id}`, attribute);
  },

  // Deletar atributo
  delete: (id, force = false) => {
    return axios.delete(`${endpoint}/${id}`, {
      params: { force }
    });
  },

  // Buscar produtos que usam um atributo
  getUsage: (id) => {
    return axios.get(`${endpoint}/${id}/usage`).then(response => response.data);
  },

  // Criar novo valor para um atributo
  createValue: (valueData) => {
    return axios.post(`${endpoint}/values`, valueData);
  },

  // Atualizar valor de um atributo
  updateValue: (valueId, valueData) => {
    return axios.put(`${endpoint}/values/${valueId}`, valueData);
  },

  // Deletar valor de um atributo
  deleteValue: (valueId) => {
    return axios.delete(`${endpoint}/values/${valueId}`);
  },

  // Buscar valores sugeridos para um atributo
  getSuggestedValues: (categoryId, query = '') => {
    return axios.get(`${endpoint}/values/suggestions`, {
      params: {
        categoryId,
        query: query || undefined
      }
    }).then(response => response.data);
  },

  // Buscar atributo completo com valores (substitui getValuesByAttributeId)
  getAttributeWithValues: (attributeId) => {
    return axios.get(`${endpoint}/${attributeId}`).then(response => response.data);
  },

  // Salvar valores de atributos para um produto
  saveProductAttributes: (productId, attributes) => {
    return axios.post(`${productsEndpoint}/${productId}/attributes`, {
      attributes
    });
  },

  // Buscar atributos de um produto
  getProductAttributes: (productId) => {
    return axios.get(`${productsEndpoint}/${productId}/attributes`)
      .then(response => response.data);
  },

  // Buscar estatísticas de uso dos atributos
  getAttributeStats: (categoryId) => {
    return axios.get(`${endpoint}/stats`, {
      params: { categoryId }
    }).then(response => response.data);
  }
};

export default attributesApi; 