<script setup>
import { ref, onMounted, watch, inject, onUnmounted, nextTick } from 'vue'

import ComponentMenu from '../ComponentMenu.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import { useComponentLibrary } from '@/views/layoutEditor/composables/useComponentLibrary'
import { setupDragAndDrop } from '@/utils/dragDropUtils'
import { initializeEditorSystem } from '@/components/layoutEditor/registry/EditorAutoLoader.js'
import { useComponentScriptInjection } from '@/composables/useComponentScriptInjection.js'
import { useElementSelection } from '@/composables/useElementSelection.js'
import { processForEditing } from '@/composables/useComponentProcessing.js'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import { useComponentRegistry } from '@/composables/useComponentRegistry.js'
import { createScriptInjectionService } from '@/services/scriptInjectionService.js'


const emit = defineEmits(['update:viewportSize', 'retry'])

const { t } = useI18n()
const toast = useToast()
const { findSelectableElement } = useElementSelection()
const { 
  detectComponentFromElement, 
  generateCleanHtmlCode, 
  shouldPreserveDraggable,
  getAllComponents,
  generateSelectorForComponent,
  autoInitializeComponents,
  processElementForCanvas,
  getAllComponentSelectors,
  getComponentConfig
} = useComponentRegistry()

const props = defineProps({
  mode: {
    type: String,
    default: 'edit'
  },
  viewportSize: {
    type: String,
    default: 'desktop'
  },
  settings: {
    type: Object,
    default: () => ({
      backgroundColor: '#f8fafc',
      contentWidth: 75
    })
  },
  isLoading: {
    type: Boolean,
    default: false
  },
  hasError: {
    type: Boolean,
    default: false
  },
  saveEditorState: {
    type: Function,
    default: () => {}
  },
  // Novos props para integração com sidebar
  onElementSelect: {
    type: Function,
    default: null
  },
  onToolbarAction: {
    type: Function,
    default: null
  }
})

const editorFrame = ref(null)
const selectedElement = ref(null)
const activePropertyEditor = ref(null)
const componentMenuOpen = ref(false)
const componentMenuPosition = ref({ x: 0, y: 0 })
const targetContainer = ref(null)
const targetPosition = ref('after')
const insertPosition = ref('below')
const editorKey = ref(0)

// Confirmation modal system
const showDeleteConfirm = ref(false)
const deleteTargetElement = ref(null)

const { componentLibrary, componentCategories, initializeIfNeeded } = useComponentLibrary()
// Já importado acima - removido duplicata
const initialHtml = inject('initialHtml', ref(''))
const authToken = inject('authToken', '')

// Adiciona variável para controlar o estado de drag
const isDragging = ref(false)

const applySettings = () => {
  const iframe = editorFrame.value
  if (!iframe || !iframe.contentDocument) return
  
  const doc = iframe.contentDocument
  
  // Remove o estilo antigo se existir
  const oldStyle = doc.querySelector('#editor-settings-style')
  if (oldStyle) {
    oldStyle.remove()
  }

  // Aplica estilos de produção
  const style = doc.createElement('style')
  style.id = 'editor-settings-style'
  style.textContent = applyProductionStyles(doc, props.settings)
  doc.head.appendChild(style)

  // Aplica estilos de edição se estiver no modo edit
  if (props.mode === 'edit') {
    // Remove estilos de edição antigos se existirem
    const oldEditorStyle = doc.querySelector('#editor-mode-styles')
    if (oldEditorStyle) {
      oldEditorStyle.remove()
    }
    applyEditorStyles(doc)
  }

  // Garante que o wrapper de conteúdo existe
  let contentWrapper = doc.querySelector('.content-wrapper')
  if (!contentWrapper) {
    contentWrapper = doc.createElement('div')
    contentWrapper.className = 'content-wrapper'
    
    while (doc.body.firstChild) {
      contentWrapper.appendChild(doc.body.firstChild)
    }
    
    doc.body.appendChild(contentWrapper)
  }
}

// Observar mudanças nas configurações
watch(() => props.settings, (newSettings) => {
  if (newSettings) {
    applySettings()
  }
}, { deep: true, immediate: true })

// Aplicar configurações quando o iframe estiver pronto
watch(editorFrame, (newFrame) => {
  if (newFrame) {
    applySettings()
  }
}, { immediate: true })

// Função para aplicar estilos de produção (sem estilos de edição)
const applyProductionStyles = (doc, settings) => {

  
  // Gera CSS do background
  let backgroundCSS = ''
  
  // Processamento avançado de background com base no tipo
  if (settings.backgroundType) {

    
    switch (settings.backgroundType) {
      case 'solid':
        backgroundCSS = `background-color: ${settings.backgroundColor || '#ffffff'};`
        break
        
      case 'gradient':
        if (settings.gradientStops && settings.gradientStops.length >= 2) {
          const stops = settings.gradientStops
            .sort((a, b) => a.position - b.position)
            .map(stop => `${stop.color} ${stop.position}%`)
            .join(', ')
          
          if (settings.gradientType === 'radial') {
            backgroundCSS = `background: radial-gradient(circle, ${stops});`
          } else {
            const angle = settings.gradientAngle || 90
            backgroundCSS = `background: linear-gradient(${angle}deg, ${stops});`
          }

        }
        break
        
      case 'image':
        if (settings.backgroundImage) {
          const imageSize = settings.imageSize || 'cover'
          const imagePosition = settings.imagePosition || 'center center'
          const imageRepeat = settings.imageRepeat || 'no-repeat'
          
          backgroundCSS = `
            background-image: url(${settings.backgroundImage});
            background-size: ${imageSize};
            background-position: ${imagePosition};
            background-repeat: ${imageRepeat};
          `

        }
        break
        
      case 'preset':
        if (settings.presetCss) {
          backgroundCSS = `background: ${settings.presetCss};`
        } else {
          // Fallback para cor sólida se o preset não tiver CSS
          backgroundCSS = `background-color: ${settings.backgroundColor || '#ffffff'};`
        }
        break
        
      default:
        // Fallback para cor sólida
        backgroundCSS = `background-color: ${settings.backgroundColor || '#ffffff'};`

    }
  } else {
    // Se não houver tipo de fundo definido, usa cor sólida
    backgroundCSS = `background-color: ${settings.backgroundColor || '#ffffff'};`

  }

  return `
    /* SCROLLBAR GLOBAIS - ILURIA DESIGN SYSTEM IFRAME */
    * {
      scrollbar-width: thin;
      scrollbar-color: #d1d5db #ffffff;
    }

    ::-webkit-scrollbar {
      width: 8px;
      height: 8px;
    }

    ::-webkit-scrollbar-track {
      background: #ffffff;
      border-radius: 4px;
    }

    ::-webkit-scrollbar-thumb {
      background: #d1d5db;
      border-radius: 4px;
      transition: background-color 0.2s ease;
    }

    ::-webkit-scrollbar-thumb:hover {
      background: #3b82f6;
    }

    ::-webkit-scrollbar-corner {
      background: #ffffff;
    }

    body {
      ${backgroundCSS}
      margin: 0;
      padding: 0;
      font-family: system-ui, sans-serif;
      line-height: 1.5;
    }
    .content-wrapper {
      max-width: ${
        (() => {
          let width = settings.contentWidth || 75;
          // Converter valores antigos para porcentagem se necessário
          if (width >= 1000) {
            if (width >= 9999) width = 100;
            else if (width >= 8500) width = 75;
            else if (width >= 8000) width = 50;
            else if (width >= 2000) width = 75;
            else width = 50;
          }
          return width <= 100 ? width + '%' : width + 'px';
        })()
      };
      margin: 0 auto;
      padding: 0 ${
        (() => {
          let width = settings.contentWidth || 75;
          // Converter valores antigos
          if (width >= 1000) {
            if (width >= 9999) width = 100;
            else if (width >= 8500) width = 75;
            else if (width >= 8000) width = 50;
            else if (width >= 2000) width = 75;
            else width = 50;
          }
          return width === 100 ? '0' : width >= 75 ? '0.5rem' : '1rem';
        })()
      };
      width: 100%;
      box-sizing: border-box;
    }
    
    @media (max-width: 768px) {
      .content-wrapper {
        padding: 0 ${
          (() => {
            let width = settings.contentWidth || 75;
            if (width >= 1000) {
              if (width >= 9999) width = 100;
              else if (width >= 8500) width = 75;
              else if (width >= 8000) width = 50;
              else if (width >= 2000) width = 75;
              else width = 50;
            }
            return width === 100 ? '0' : width >= 75 ? '0.25rem' : '0.75rem';
          })()
        };
        max-width: 100%;
      }
    }
    
    @media (max-width: 480px) {
      .content-wrapper {
        padding: 0 ${
          (() => {
            let width = settings.contentWidth || 75;
            if (width >= 1000) {
              if (width >= 9999) width = 100;
              else if (width >= 8500) width = 75;
              else if (width >= 8000) width = 50;
              else if (width >= 2000) width = 75;
              else width = 50;
            }
            return width === 100 ? '0' : width >= 75 ? '0.125rem' : '0.5rem';
          })()
        };
      }
    }
    
    .component-container {
      position: relative;
      border-radius: 4px;
    }

    /* Estilos para grids de produtos */
    [data-component="dynamic-grid-produtos"] {
      display: grid;
      grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
      gap: 1.5rem;
      padding: 2rem 0;
    }

    @media (max-width: 768px) {
      [data-component="dynamic-grid-produtos"] {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
        gap: 1rem;
        padding: 1.5rem 0;
      }
    }

    @media (max-width: 480px) {
      [data-component="dynamic-grid-produtos"] {
        grid-template-columns: 1fr;
        gap: 1rem;
        padding: 1rem 0;
      }
    }

    /* Estilos para cards de produto */
    .product-card {
      background: white;
      border-radius: 12px;
      overflow: hidden;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      transition: all 0.3s ease;
      border: 1px solid #e5e7eb;
      position: relative;
    }

    .product-card:hover {
      transform: translateY(-4px);
      box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    }

    .product-image {
      width: 100%;
      height: 250px;
      object-fit: cover;
      background: #f3f4f6;
    }

    .product-info {
      padding: 1.5rem;
    }

    .product-title {
      font-size: 1.125rem;
      font-weight: 600;
      color: #111827;
      margin: 0 0 0.75rem 0;
      line-height: 1.4;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
      overflow: hidden;
    }

    .product-price {
      display: flex;
      align-items: center;
      gap: 0.5rem;
      margin-bottom: 1rem;
    }

    .current-price {
      font-size: 1.25rem;
      font-weight: 700;
      color: #3b82f6;
    }

    .original-price {
      font-size: 0.875rem;
      color: #6b7280;
      text-decoration: line-through;
    }

    .product-button {
      width: 100%;
      background-color: #3b82f6;
      color: white;
      border: none;
      border-radius: 8px;
      padding: 0.75rem 1rem;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
    }

    .product-button:hover {
      background-color: #2563eb;
      transform: translateY(-1px);
    }

    .product-button:active {
      transform: translateY(0);
    }

    /* Loading placeholder */
    .loading-placeholder {
      background: #f3f4f6;
      border-radius: 12px;
      overflow: hidden;
      position: relative;
    }

    .loading-placeholder::before {
      content: '';
      position: absolute;
      top: 0;
      left: -100%;
      width: 100%;
      height: 100%;
      background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.6), transparent);
      animation: loading 1.5s infinite;
    }

    @keyframes loading {
      0% { left: -100%; }
      100% { left: 100%; }
    }

    /* Estilos para componentes de informações da empresa */
    [data-component="company-information"],
    .iluria-company-information {
      display: flex;
      gap: 2rem;
      align-items: center;
      padding: 2rem;
      background: white;
      border-radius: 0;
      box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
      margin: 1.5rem 0;
    }

    .company-info-content {
      flex: 1;
      padding-right: 1rem;
    }

    .company-info-title {
      font-size: 2rem;
      font-weight: 700;
      color: #111827;
      margin: 0 0 1rem 0;
      line-height: 1.2;
    }

    .company-info-description {
      margin-bottom: 1.5rem;
    }

    .company-info-description p {
      font-size: 1rem;
      color: #6b7280;
      line-height: 1.6;
      margin: 0;
    }

    .company-info-action {
      margin-top: 1.5rem;
    }

    .company-info-button {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.75rem 1.5rem;
      background-color: #3b82f6;
      color: white;
      border: none;
      border-radius: 8px;
      font-size: 0.875rem;
      font-weight: 600;
      cursor: pointer;
      transition: all 0.2s ease;
      text-decoration: none;
    }

    .company-info-button:hover {
      background-color: #2563eb;
      transform: translateY(-1px);
    }

    .company-info-button:active {
      transform: translateY(0);
    }

    .company-info-image {
      flex-shrink: 0;
      width: 300px;
      height: 250px;
      overflow: hidden;
      border-radius: 8px;
    }

    .company-info-image img {
      width: 100%;
      height: 100%;
      object-fit: cover;
      display: block;
    }

    @media (max-width: 768px) {
      [data-component="company-information"],
      .iluria-company-information {
        flex-direction: column;
        text-align: center;
        gap: 1.5rem;
        padding: 1.5rem;
      }

      .company-info-content {
        padding-right: 0;
      }

      .company-info-title {
        font-size: 1.5rem;
      }

      .company-info-image {
        width: 100%;
        max-width: 300px;
        height: 200px;
      }
    }

    @media (max-width: 480px) {
      [data-component="company-information"],
      .iluria-company-information {
        padding: 1rem;
        gap: 1rem;
      }

      .company-info-title {
        font-size: 1.25rem;
      }

      .company-info-image {
        height: 180px;
      }
    }

    /* Elementos de texto */
    h1, h2, h3, h4, h5, h6 {
      margin: 0 0 1rem 0;
      line-height: 1.2;
    }

    h1 { font-size: 2.5rem; font-weight: 700; }
    h2 { font-size: 2rem; font-weight: 600; }
    h3 { font-size: 1.75rem; font-weight: 600; }
    h4 { font-size: 1.5rem; font-weight: 600; }
    h5 { font-size: 1.25rem; font-weight: 500; }
    h6 { font-size: 1.125rem; font-weight: 500; }

    p {
      margin: 0 0 1rem 0;
      line-height: 1.6;
    }

    /* Botões */
    button, .btn {
      display: inline-flex;
      align-items: center;
      justify-content: center;
      padding: 0.5rem 1rem;
      border-radius: 6px;
      font-weight: 500;
      text-decoration: none;
      transition: all 0.2s ease;
      cursor: pointer;
      border: none;
    }

    /* Links */
    a {
      color: #3b82f6;
      text-decoration: none;
      transition: color 0.2s ease;
    }

    a:hover {
      color: #2563eb;
      text-decoration: underline;
    }

    /* Imagens */
    img {
      max-width: 100%;
      height: auto;
      border-radius: 4px;
    }
  `
}

// Função para aplicar estilos de edição
const applyEditorStyles = (doc) => {
  const editorStyle = doc.createElement('style')
  editorStyle.id = 'editor-mode-styles'
  editorStyle.textContent = `
    .edit-mode .component-container {
      pointer-events: auto;
      cursor: pointer;
      position: relative;
      z-index: 1;
      transition: all 0.2s ease;
    }
    
    .edit-mode .component-container:hover {
      background: rgba(59, 130, 246, 0.04);
      outline: 1px solid rgba(59, 130, 246, 0.4);
    }
    
    .edit-mode .component-container.selected {
      background: rgba(59, 130, 246, 0.08);
      outline: 2px solid #3b82f6;
    }
    
    .edit-mode [data-component="dynamic-grid-produtos"] {
      pointer-events: auto;
      cursor: pointer;
      position: relative;
      z-index: 2;
      min-height: 200px;
      display: block !important;
    }
    

    
    .edit-mode [data-component="dynamic-grid-produtos"] * {
      pointer-events: none;
    }
    
    .edit-mode [data-component="dynamic-grid-produtos"] button,
    .edit-mode [data-component="dynamic-grid-produtos"] a {
      pointer-events: none;
    }
    
    .edit-mode [data-component="dynamic-grid-produtos"]::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10;
      pointer-events: auto;
      background: transparent;
    }
    
    .edit-mode img, 
    .edit-mode video, 
    .edit-mode iframe {
      max-width: 100%;
      height: auto;
      display: block;
    }
    
    .edit-mode .content-wrapper a,
    .edit-mode .content-wrapper button,
    .edit-mode .content-wrapper [data-interactive="true"] {
      pointer-events: none !important;
      cursor: default !important;
    }
    
    .preview-mode {
      user-select: text;
    }
    
    .preview-mode * {
      pointer-events: auto !important;
      -webkit-user-drag: none !important;
      -moz-user-drag: none !important;
      -ms-user-drag: none !important;
      user-drag: none !important;
    }
    
    .preview-mode .component-container {
      pointer-events: none;
      border: none;
      outline: none;
      background: none !important;
    }
    
    .preview-mode .component-container * {
      -webkit-user-drag: none !important;
      -moz-user-drag: none !important;
      -ms-user-drag: none !important;
      user-drag: none !important;
    }
    
    .preview-mode [draggable],
    .preview-mode [draggable] * {
      -webkit-user-drag: none !important;
      -moz-user-drag: none !important;
      -ms-user-drag: none !important;
      user-drag: none !important;
      draggable: false !important;
    }
    
    .preview-mode a,
    .preview-mode button,
    .preview-mode [data-interactive="true"] {
      pointer-events: auto !important;
      cursor: pointer !important;
    }
    
    .preview-mode img,
    .preview-mode video,
    .preview-mode iframe,
    .preview-mode canvas {
      -webkit-user-drag: none !important;
      -moz-user-drag: none !important;
      -ms-user-drag: none !important;
      user-drag: none !important;
      pointer-events: none !important;
    }
    
    .dragging {
      opacity: 0.5;
    }
    
    .drop-target {
      outline: 2px dashed #3b82f6 !important;
      background: rgba(59, 130, 246, 0.1);
    }
    

    
    .edit-mode [data-component="video"]::before {
      content: '';
      position: absolute;
      top: 0;
      left: 0;
      right: 0;
      bottom: 0;
      z-index: 10;
      pointer-events: auto;
      background: transparent;
      cursor: pointer;
    }
    
    /* Estilos para componentes de vídeo no modo de edição */
    .edit-mode [data-component="video"],
    .edit-mode .iluria-video {
      pointer-events: auto;
      cursor: pointer;
    }
    
    .edit-mode [data-component="video"] *,
    .edit-mode .iluria-video * {
      pointer-events: none;
    }
  `
  doc.head.appendChild(editorStyle)
}

const writeHtml = () => {
  // ✋ SIMPLES: Só não escrever quando há erro
  if (props.hasError) {
    return
  }

  const iframe = editorFrame.value
  if (!iframe) {
    console.error('❌ [EditorCanvas] iframe não encontrado');
    return
  }
  
  const doc = iframe.contentDocument || iframe.contentWindow.document
  
  const tempDiv = document.createElement('div')
  tempDiv.innerHTML = initialHtml.value
  
  // 🔧 SISTEMA UNIFICADO: Apenas processa elementos registrados no ComponentRegistry
  processForEditing(tempDiv)
  
  const existingContentWrapper = tempDiv.querySelector('.content-wrapper')
  if (existingContentWrapper) {
    const innerContent = existingContentWrapper.innerHTML
    tempDiv.innerHTML = innerContent
  }
  
  const productionCSS = applyProductionStyles(doc, props.settings)
  doc.open()
  doc.write(`<!DOCTYPE html>
<html>
  <head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <style id="editor-settings-style">
      ${productionCSS}
    </style>
  </head>
  <body class="${props.mode}-mode">
    <div class="content-wrapper">
      ${tempDiv.innerHTML}
    </div>
  </body>
</html>`)
  doc.close()
  
  if (doc.readyState === 'complete') {
    setupEditorEvents()
    if (props.mode === 'edit') {
      applyEditorStyles(doc)
    }
    injectProductsScript(doc)
  } else {
    iframe.onload = () => {
      setupEditorEvents()
      if (props.mode === 'edit') {
        applyEditorStyles(doc)
      }
      injectProductsScript(doc)
    }
  }
}

const injectProductsScript = (doc) => {
  if (!doc) {
    console.error('❌ [EditorCanvas] Documento não fornecido para injectProductsScript');
    return
  }
  
  try {
    // Usa apenas o sistema centralizado de injeção de scripts
    useComponentScriptInjection(doc, authToken)

  } catch (error) {
    console.error('❌ [EditorCanvas] Erro ao injetar scripts de componentes:', error)
  }
}

watch(() => initialHtml.value, async (newVal) => {
  if (newVal && !props.hasError) {
    await nextTick()
    if (editorFrame.value) {
      writeHtml()
    }
  }
}, { immediate: false })

const handleElementSelect = (element, event) => {

  componentMenuOpen.value = false
  activePropertyEditor.value = null
  
  if (props.mode !== 'edit' || !element || !editorFrame.value) {
    selectedElement.value = null
    
    // Notificar sidebar sobre desmarcação
    if (props.onElementSelect) {
      props.onElementSelect(null)
    }
    return
  }
  
  try {
    const doc = editorFrame.value.contentDocument
    
    doc.querySelectorAll('.selected').forEach(el => {
      el.classList.remove('selected')
    })
    
    let targetElement = element
    
    // Se clicou em um component-container, procura o filho editável
    if (targetElement.classList.contains('component-container')) {
      const editableChild = targetElement.querySelector('[data-element-type], [data-component]')
      if (editableChild) {
        targetElement = editableChild
      }
    }
    
    const editableSelectors = [
      '[data-element-type]',
      '[data-component]',
      '[data-vue-component]',
      '.component-container'
    ]
    
    // Procura o elemento editável mais próximo subindo na árvore
    while (targetElement && !editableSelectors.some(selector => targetElement.matches && targetElement.matches(selector))) {
      targetElement = targetElement.parentElement
    }
    
    
    if (!targetElement) {

      return
    }
    
    const containerElement = targetElement.closest('.component-container')
    if (containerElement) {
      containerElement.classList.add('selected')
    }
    
    selectedElement.value = targetElement
    
    editorKey.value++
    
    // Notificar sidebar sobre a seleção
    if (props.onElementSelect) {
      props.onElementSelect(targetElement)
    }
    
    activePropertyEditor.value = null
  } catch (error) {
    console.error('Erro ao selecionar elemento:', error)
  }
}

// Manipulador genérico de ações do editor
const handleEditorAction = (actionData) => {
  
  componentMenuOpen.value = false
  
  const { type, data, element } = actionData
  
  // Se estiver usando sidebar, notificar a sidebar sobre a ação
  if (props.onToolbarAction) {
    props.onToolbarAction(actionData)
    return
  }
  
  // Sistema legado: ações especiais que requerem lógica customizada
  switch (type) {
    case 'add-above':
      handleAddElement('above')
      break
      
    case 'delete':
      handleDeleteElement()
      break
      
    case 'apply-image':
      handleApplyImage(data)
      break
      
    default:
      // Para a maioria das ações, apenas abre o property editor apropriado
      activePropertyEditor.value = type
      break
  }
}

const handleApplyImage = (imageData) => {
  
  
  if (!selectedElement.value) {

    return
  }
  
  const element = selectedElement.value
  
  // Se for uma tag IMG, aplicar diretamente
  if (element.tagName === 'IMG') {
    element.src = imageData.url
    element.alt = imageData.name || 'Imagem'
    
    // Aplicar dados de crop se existirem
    if (imageData.cropData || imageData.style) {
      const cropData = imageData.cropData || {}
      const style = imageData.style || {}
      
      // Aplicar object-position para crop
      if (cropData.objectPosition || style.objectPosition) {
        element.style.objectPosition = cropData.objectPosition || style.objectPosition
      }
      
      // Aplicar object-fit
      if (cropData.objectFit || style.objectFit) {
        element.style.objectFit = cropData.objectFit || style.objectFit
      } else {
        element.style.objectFit = 'cover'
      }
      
      // Garantir que a imagem ocupe todo o container
      if (style.width) element.style.width = style.width
      if (style.height) element.style.height = style.height
      

    }
    

  }
  // Se for um elemento com background-image
  else if (element.style.backgroundImage || getComputedStyle(element).backgroundImage !== 'none') {
    element.style.backgroundImage = `url(${imageData.url})`
    
    // Para background-image, aplicar transform no próprio elemento
    if (imageData.cropData?.transform || imageData.style?.transform) {
      element.style.transform = imageData.cropData?.transform || imageData.style?.transform
    }
    

  }
  // Procurar por IMG dentro do elemento
  else {
    const imgElement = element.querySelector('img')
    if (imgElement) {
      imgElement.src = imageData.url
      imgElement.alt = imageData.name || 'Imagem'
      
      // Aplicar dados de crop na imagem filha
      if (imageData.cropData || imageData.style) {
        const cropData = imageData.cropData || {}
        const style = imageData.style || {}
        
        // Aplicar object-position para crop
        if (cropData.objectPosition || style.objectPosition) {
          imgElement.style.objectPosition = cropData.objectPosition || style.objectPosition
        }
        
        // Aplicar object-fit
        if (cropData.objectFit || style.objectFit) {
          imgElement.style.objectFit = cropData.objectFit || style.objectFit
        } else {
          imgElement.style.objectFit = 'cover'
        }
        
        // Garantir que a imagem ocupe todo o container
        if (style.width) imgElement.style.width = style.width
        if (style.height) imgElement.style.height = style.height
      }
      

    } else {

    }
  }
  
  // Salva estado após aplicar imagem (imediato)
  props.saveEditorState('Imagem alterada', { immediate: true })
  
  // Fechar qualquer modal ou editor aberto
  activePropertyEditor.value = null
}

const handleTogglePosition = () => {
  insertPosition.value = insertPosition.value === 'below' ? 'above' : 'below'
  
  targetPosition.value = insertPosition.value === 'below' ? 'after' : 'before'
  
  if (targetContainer.value && editorFrame.value) {
    const rect = targetContainer.value.getBoundingClientRect()
    const iframeRect = editorFrame.value.getBoundingClientRect()
    const editorContainer = document.querySelector('.editor-canvas-container')
    const scrollTop = editorContainer.scrollTop
    
    componentMenuPosition.value = {
      x: rect.left - iframeRect.left + (rect.width / 2),
      y: insertPosition.value === 'above' ? 
         rect.top - iframeRect.top - 10 - scrollTop : 
         rect.bottom - iframeRect.top + 10 - scrollTop
    }
  }
}

const handleAddElement = (position) => {

  if (!selectedElement.value || !editorFrame.value) return
  
  try {
    const container = selectedElement.value.closest('.component-container')
    if (!container) return
    
    activePropertyEditor.value = null
    
    targetContainer.value = container
    targetPosition.value = position === 'above' ? 'before' : 'after'
    insertPosition.value = position === 'above' ? 'above' : 'below'
    
    // Abre o menu de componentes na posição correta
    const rect = container.getBoundingClientRect()
    const iframeRect = editorFrame.value.getBoundingClientRect()
    const editorContainer = document.querySelector('.editor-canvas-container')
    const editorRect = editorContainer.getBoundingClientRect()
    
    // Calcula a posição considerando o scroll do editor
    const scrollTop = editorContainer.scrollTop
    
    // Ajusta a posição X e Y considerando o scroll e os limites do editor
    let x = rect.left - iframeRect.left + (rect.width / 2)
    let y = position === 'above' ? 
            rect.top - iframeRect.top - 10 : 
            rect.bottom - iframeRect.top + 10
            
    // Ajusta Y considerando o scroll
    y -= scrollTop
    
    // Garante que o menu não fique cortado nas bordas
    const menuWidth = 280 // Largura aproximada do menu
    const menuHeight = 300 // Altura aproximada do menu
    
    // Ajusta X para não ultrapassar as bordas laterais
    x = Math.max(menuWidth / 2, Math.min(x, iframeRect.width - menuWidth / 2))
    
    // Se estiver muito próximo do topo, inverte a posição
    if (y < menuHeight && position === 'above') {
      y = rect.bottom - iframeRect.top + 10 - scrollTop
    }
    
    // Se estiver muito próximo do fundo, inverte a posição
    if (y + menuHeight > editorRect.height && position === 'below') {
      y = rect.top - iframeRect.top - 10 - scrollTop
    }
    
    componentMenuPosition.value = { x, y }
    componentMenuOpen.value = true

  } catch (error) {
    console.error('Erro ao adicionar elemento:', error)
  }
}

const handleDeleteElement = () => {
  if (!selectedElement.value || !editorFrame.value) return
  
  try {
    const container = selectedElement.value.closest('.component-container')
    if (!container) return
    
    confirmDelete(selectedElement.value)
  } catch (error) {
    toast.showError(t('layoutEditor.notifications.errorDeleting'))
  }
}

// Função removida - não é mais necessária com o sistema de sidebar

const handleTextUpdate = (newText) => {
  if (selectedElement.value) {
    selectedElement.value.textContent = newText
    
    // Salva estado após atualizar texto
    // Salva estado quando o texto é alterado (com debounce longo para typing contínuo)
    props.saveEditorState('Texto alterado', { 
      immediate: false, 
      debounceMs: 1000,
      debounceKey: `text-editing-${target.tagName.toLowerCase()}`
    })
  }
}

// Função removida - não é mais necessária com o sistema de sidebar

const createComponent = async (type) => {
  const doc = editorFrame.value?.contentDocument
  if (!doc) {
    return null
  }

  await initializeIfNeeded()

  // Usar o sistema de configurações centralizadas
  const componentConfig = getComponentConfig(type)
  
  if (!componentConfig) {
    console.error(`❌ [EditorCanvas] Configuração não encontrada para o tipo: ${type}`)
    return null
  }

  const container = doc.createElement('div')
  container.className = 'component-container'
  container.setAttribute('data-editable-element', 'true')
  container.setAttribute('data-element-id', 'el-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
  
  const contentWrapper = doc.createElement('div')
  contentWrapper.className = 'element-content'
  
  const tempDiv = doc.createElement('div')
  tempDiv.innerHTML = componentConfig.html
  const element = tempDiv.firstChild
  
  if (element) {
    // Sistema unificado: apenas processa através do ComponentRegistry
    contentWrapper.appendChild(element)
    container.appendChild(contentWrapper)
    
    if (props.mode === 'edit') {
      container.setAttribute('draggable', 'true')
      setupDragAndDrop(container, doc, handleElementSelect, props.mode)
    }
    

    return container
  }

  return null
}

const handleAddComponent = async (type) => {
  componentMenuOpen.value = false
  const iframe = editorFrame.value
  if (!iframe) return
  
  const doc = iframe.contentDocument
  if (!doc) return
  
  const newComponent = await createComponent(type)
  if (!newComponent) return
  
  if (!targetContainer.value) {
    const contentWrapper = doc.querySelector('.content-wrapper')
    if (contentWrapper) {
      contentWrapper.appendChild(newComponent)
    } else {
      doc.body.appendChild(newComponent)
    }
  } else {
    const parent = targetContainer.value.parentNode
    if (targetPosition.value === 'before') {
      parent.insertBefore(newComponent, targetContainer.value)
    } else {
      if (targetContainer.value.nextSibling) {
        parent.insertBefore(newComponent, targetContainer.value.nextSibling)
      } else {
        parent.appendChild(newComponent)
      }
    }
  }
  

  
  // ✅ INICIALIZAÇÃO AUTOMÁTICA IMEDIATA APÓS ADICIONAR COMPONENTE
  setTimeout(() => {
    try {
      
      // Detecta e inicializa automaticamente o componente recém-adicionado
      const component = detectComponentFromElement(newComponent.querySelector('.element-content > *'))
      if (component && component.initialization?.autoInit) {
        const element = newComponent.querySelector('.element-content > *')
        if (element && !element.hasAttribute('data-initialized')) {
          if (component.initialization.scriptLoader) {
            component.initialization.scriptLoader(element, doc)
          }
          element.setAttribute('data-initialized', 'true')

        }
      }
      
      // Re-aplica autoInitializeComponents para garantir que tudo funcione
      autoInitializeComponents(doc)
      
    } catch (error) {
      console.error(`❌ [EditorCanvas] Erro ao inicializar componente ${type}:`, error)
    }
  }, 50) // Tempo mínimo para o DOM se atualizar
  
  const elementToSelect = newComponent.querySelector('.element-content > *')
  if (elementToSelect) {
    if (!elementToSelect.hasAttribute('data-element-type')) {
      elementToSelect.setAttribute('data-element-type', type)
    }
    
    handleElementSelect(elementToSelect)
  }
  
  setupDragAndDrop(newComponent, doc, handleElementSelect, props.mode)
  
  // Salva estado após adicionar componente (imediato)
  setTimeout(() => {
    props.saveEditorState(`Componente adicionado: ${type}`, { immediate: true })
  }, 100)
}

const setupMutationObserver = (iframe) => {
  try {
    const config = { childList: true, subtree: true };
    const iframeDoc = iframe.contentDocument || iframe.contentWindow?.document;
    if (!iframeDoc || !iframeDoc.body) {
      console.warn('MutationObserver: iframeDoc.body not available');
      return null;
    }
    
    const observer = new MutationObserver((mutations) => {
    });
    
    observer.observe(iframeDoc.body, config);
    return observer;
  } catch (error) {
    console.error('Error setting up MutationObserver:', error);
    return null;
  }
};

const initEditor = async () => {
  const iframe = editorFrame.value;
  if (!iframe) {
    setTimeout(initEditor, 100);
    return;
  }

  await nextTick();
  
  if (initialHtml.value) {
    writeHtml();
  }
  
  setupMutationObserver(iframe);
};

const preventDragInPreviewMode = (e) => {
  if (props.mode === 'view') {
    e.preventDefault()
    e.stopPropagation()
    return false
  }
}

// Adiciona função para inicializar automaticamente todos os componentes quando o iframe carrega
const setupAutoInitialization = () => {
  const iframe = editorFrame.value
  if (!iframe) return
  
  iframe.addEventListener('load', () => {
    
    // autoInitializeComponents já importado acima
    
    // Aguarda um momento para o conteúdo ser renderizado
    setTimeout(() => {
      try {
        autoInitializeComponents(iframe.contentDocument || document)

      } catch (error) {
        console.error('❌ [EditorCanvas] Erro na auto-inicialização:', error)
      }
    }, 200)
  })
}

onMounted(() => {

  setupAutoInitialization()
  
  initEditor();
  
  initializeEditorSystem().catch(error => {
    console.error('Erro ao inicializar sistema de editores:', error)
  })
  
  const handleDocumentClick = (e) => {
    if (props.mode !== 'edit') return
    if (isDragging.value) return
    const clickedElement = e.target

    const isPropertyPanel = clickedElement.closest('.property-panel')
    const isComponentMenu = clickedElement.closest('.component-menu')
    const isFloatingWindow = clickedElement.closest('.floating-window')
    const isEditableElement = clickedElement.closest('[data-element-type], [data-component="dynamic-grid-produtos"], .component-container')
    const isEditorFrame = clickedElement.closest('#editor-frame')
    const isEditor = clickedElement.closest('.font-editor, .spacing-editor, .border-editor, .background-editor, .animation-editor, .transform-editor, .filter-editor')
    const isProductEditor = clickedElement.closest('.product-style-editor, .product-selection-editor')
    const isProductModal = clickedElement.closest('.product-selector-modal')
    const isCompanyInformation = clickedElement.closest('[data-component="company-information"], [data-element-type="company-information"], .iluria-company-information')
    if (!isPropertyPanel && !isComponentMenu && !isFloatingWindow && !isEditableElement && !isEditorFrame && !isEditor && !isProductEditor && !isProductModal && !isCompanyInformation) {
      const doc = editorFrame.value?.contentDocument
      if (doc) {
        doc.querySelectorAll('.selected').forEach(el => {
          el.classList.remove('selected')
        })
      }
      selectedElement.value = null
      componentMenuOpen.value = false
      activePropertyEditor.value = null
    }
  }
  
  document.addEventListener('click', handleDocumentClick)
  
  const editorElement = editorFrame.value
  if (editorElement) {
    editorElement.addEventListener('dragstart', preventDragInPreviewMode, true)
    editorElement.addEventListener('drop', preventDragInPreviewMode, true)
    editorElement.addEventListener('dragover', preventDragInPreviewMode, true)
  }
  
  return () => {
    document.removeEventListener('click', handleDocumentClick)
    if (editorElement) {
      editorElement.removeEventListener('dragstart', preventDragInPreviewMode, true)
      editorElement.removeEventListener('drop', preventDragInPreviewMode, true)
      editorElement.removeEventListener('dragover', preventDragInPreviewMode, true)
    }
  }
})

onUnmounted(() => {
  const editorElement = editorFrame.value
  if (editorElement) {
    editorElement.removeEventListener('dragstart', preventDragInPreviewMode, true)
    editorElement.removeEventListener('drop', preventDragInPreviewMode, true)
    editorElement.removeEventListener('dragover', preventDragInPreviewMode, true)
  }
})

watch(() => props.mode, (newMode) => {
  const iframe = editorFrame.value
  if (!iframe) return
  
  const doc = iframe.contentDocument
  if (!doc) return
  
  doc.body.className = newMode + '-mode'
  
  const oldEditorStyle = doc.querySelector('#editor-mode-styles')
  if (oldEditorStyle) {
    oldEditorStyle.remove()
  }
  
  if (newMode === 'edit') {
    applyEditorStyles(doc)
  }
  
  activePropertyEditor.value = null
  componentMenuOpen.value = false
  
  setupEditorEvents()
})

const getCleanHtml = async () => {
  const iframe = editorFrame.value
  if (!iframe || !iframe.contentDocument) return ''

  const doc = iframe.contentDocument
  const containerClone = doc.cloneNode(true)

  // 🚀 USAR SERVIÇO CENTRALIZADO: Delega toda a configuração/injeção para o serviço
  try {
    const currentAuthToken = authToken.value || authToken || ''
    const scriptService = createScriptInjectionService(currentAuthToken)
    
    // Processar documento para export usando serviço centralizado
    await scriptService.processDocumentForExport(containerClone)
    
    // Limpar artefatos do editor
    scriptService.cleanEditorArtifacts(containerClone)
    
    // Limpar indicadores de componentes protegidos
    const protectedIndicators = containerClone.querySelectorAll('.protected-component-indicator')
    protectedIndicators.forEach(indicator => indicator.remove())
    
    
  } catch (error) {
    console.error('❌ [getCleanHtml] Erro no serviço centralizado:', error)
    // Fallback básico se o serviço falhar
    autoInitializeComponents(containerClone)
    
    // Limpar indicadores mesmo no fallback
    const protectedIndicators = containerClone.querySelectorAll('.protected-component-indicator')
    protectedIndicators.forEach(indicator => indicator.remove())
  }

  return containerClone.documentElement.outerHTML
}

const handleClick = (e) => {
      if (props.mode === 'view') {
    // No modo preview, permite apenas cliques em elementos interativos
    const isInteractive = e.target.closest('a, button, [data-interactive="true"]')
    if (!isInteractive) {
      e.preventDefault()
    }
    return
  }
  
  // Se estiver em modo de edição
  if (props.mode === 'edit') {

    
    // Previne cliques em elementos interativos no modo de edição
    const isInteractive = e.target.closest('a, button, [data-interactive="true"]')
    if (isInteractive) {
      e.preventDefault()
      e.stopPropagation()
    }
    
    // Se estiver em drag, não processa o click
    if (isDragging.value) {
      return
    }
    
    // 🎯 NOVA LÓGICA CENTRALIZADA: Usa o sistema de seleção automatizada
    const elementToSelect = findSelectableElement(e.target)
    
 
    
    if (elementToSelect) {

      handleElementSelect(elementToSelect, e)
      } else {
      // Se não encontrou elemento editável, remove a seleção
      const doc = editorFrame.value?.contentDocument
      if (doc) {
          doc.querySelectorAll('.selected').forEach(el => {
            el.classList.remove('selected')
                  })
        selectedElement.value = null
        componentMenuOpen.value = false
      }
    }
  }
}

const setupEditorEvents = () => {
  const iframe = editorFrame.value
  if (!iframe) return () => {}
  
  const doc = iframe.contentDocument
  if (!doc) return () => {}
  
  
  doc.body.className = `${props.mode}-mode`
  
  // Adiciona event listener de click
  doc.addEventListener('click', handleClick)
  
  // Previne drag no modo preview mesmo durante seleção de texto
  const preventDrag = (e) => {
    if (props.mode === 'view') {
      e.preventDefault()
      e.stopPropagation()
      return false
    }
  }
  
  // Adiciona eventos de drag apenas no modo edit
  if (props.mode === 'edit') {
    const handleDragStart = (e) => {
      isDragging.value = true
      // Previne que o clique seja processado durante o drag
      e.stopPropagation()
    }

    const handleDragEnd = () => {
      // Adiciona um pequeno delay para garantir que o estado seja atualizado após o evento de click
      setTimeout(() => {
        isDragging.value = false
      }, 100)
    }
    
    doc.addEventListener('dragstart', handleDragStart)
    doc.addEventListener('dragend', handleDragEnd)
    // Adiciona também eventos para casos onde o drag é cancelado
    doc.addEventListener('dragleave', handleDragEnd)
    doc.addEventListener('drop', handleDragEnd)
    
    // Configura drag and drop nos containers existentes
    doc.querySelectorAll('.component-container').forEach(container => {
      container.setAttribute('draggable', 'true')
      setupDragAndDrop(container, doc, handleElementSelect, props.mode)
    })
    
    // Cleanup function para modo edit
    return () => {
      doc.removeEventListener('click', handleClick)
      doc.removeEventListener('dragstart', handleDragStart)
      doc.removeEventListener('dragend', handleDragEnd)
      doc.removeEventListener('dragleave', handleDragEnd)
      doc.removeEventListener('drop', handleDragEnd)
    }
  } else {
    // No modo preview/view, remove atributos de drag e adiciona prevenção
    doc.querySelectorAll('.component-container').forEach(container => {
      container.removeAttribute('draggable')
      container.setAttribute('draggable', 'false')
      container.ondragstart = preventDrag
      container.ondrag = preventDrag
      container.ondragend = preventDrag
      container.ondragover = preventDrag
      container.ondrop = preventDrag
      
      // Remove indicadores de componentes protegidos no modo visualização
      const protectedIndicator = container.querySelector('.protected-component-indicator')
      if (protectedIndicator) {
        protectedIndicator.remove()
      }
      
      // Remove também dos elementos filhos
      container.querySelectorAll('*').forEach(element => {
        element.removeAttribute('draggable')
        element.setAttribute('draggable', 'false')
        element.ondragstart = preventDrag
        element.ondrag = preventDrag
        element.ondragend = preventDrag
        element.ondragover = preventDrag
        element.ondrop = preventDrag
      })
    })
    
    // Cleanup function para modo preview/view
    return () => {
      doc.removeEventListener('click', handleClick)
    }
  }
  

}

// Expõe a função para obter HTML limpo
defineExpose({
  getCleanHtml,
  setupEditorEvents
})

// Confirmation modal functions
const confirmDelete = (element) => {
  deleteTargetElement.value = element
  showDeleteConfirm.value = true
}

const executeDelete = () => {
  if (!deleteTargetElement.value) return
  
  try {
    const container = deleteTargetElement.value.closest('.component-container')
    if (!container) return
    
    // Salva estado antes de deletar para permitir undo (imediato)
    props.saveEditorState('Elemento removido', { immediate: true })
    
    container.remove()
    selectedElement.value = null
    activePropertyEditor.value = null
    
  } catch (error) {
    toast.showError(t('layoutEditor.notifications.errorDeleting'))
  }
  
  showDeleteConfirm.value = false
  deleteTargetElement.value = null
}

const cancelDelete = () => {
  showDeleteConfirm.value = false
  deleteTargetElement.value = null
}




</script>

<template>
  <div 
    class="editor-canvas-container" 
    :class="{
      'mobile-view': viewportSize === 'mobile',
      'edit-mode': mode === 'edit'
    }"
  >
    <div class="canvas-wrapper">        
      <iframe
        ref="editorFrame"
        id="editor-frame"
        class="editor-frame"
        :class="{
          'mobile-frame': viewportSize === 'mobile'
        }"
        title="Editor Frame"
      ></iframe>

      <!-- Loading/Error Overlay -->
      <div v-if="isLoading || hasError" class="loading-overlay">
        <template v-if="hasError">
          <!-- Error Screen -->
          <div class="error-icon">
            <svg viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <circle cx="12" cy="12" r="10"/>
              <line x1="12" y1="8" x2="12" y2="12"/>
              <line x1="12" y1="16" x2="12.01" y2="16"/>
            </svg>
          </div>
          <h3>{{ $t('layoutEditor.error.title') }}</h3>
          <p class="error-details">
            {{ $t('layoutEditor.error.message') }}
          </p>
          <button @click="$emit('retry')" class="retry-button">
            {{ $t('layoutEditor.error.retry') }}
          </button>
        </template>
        <template v-else>
          <!-- Loading Screen -->
          <div class="loading-spinner"></div>
          <span>{{ $t('layoutEditor.loading') }}</span>
        </template>
      </div>
    </div>

    <!-- FloatingToolbar e PropertyPanel removidos - agora usamos somente a sidebar -->
    
    <!-- Component Menu -->
    <ComponentMenu
      v-if="mode === 'edit' && componentMenuOpen"
      :position="componentMenuPosition"
      :categories="componentCategories"
      :is-open="componentMenuOpen"
      :insert-position="insertPosition"
      :selected-element="selectedElement"
      @select="handleAddComponent"
      @toggle-position="handleTogglePosition"
      @close="componentMenuOpen = false"
    />

    <!-- Confirmation Modal -->
    <IluriaConfirmationModal
      :is-visible="showDeleteConfirm"
      @confirm="executeDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<style scoped>
.editor-canvas-container {
  flex-grow: 1;
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 1rem 0;
  background-color: #f1f5f9;
  min-height: 0;
  overflow: auto;
  position: relative;
}

.canvas-wrapper {
  position: relative;
  width: 100%;
  max-width: 98%;
  margin: 0 auto;
  background: white;
  border-radius: 8px;
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  transition: all 0.3s ease;
}

.editor-frame {
  width: 100%;
  height: calc(100vh - 100px);
  border: none;
  border-radius: 8px;
  background: white;
  transition: all 0.3s ease;
}

.mobile-view .canvas-wrapper {
  max-width: 375px;
  margin: 2rem auto;
  border-radius: 32px;
  padding: 16px 0;
  background: #1a1a1a;
  box-shadow: 
    0 25px 50px -12px rgba(0, 0, 0, 0.25),
    0 8px 24px -4px rgba(0, 0, 0, 0.15);
}

.mobile-frame {
  width: 375px;
  height: 667px;
  border-radius: 16px;
  transform-origin: top;
  transition: all 0.3s ease;
}

.edit-mode .canvas-wrapper {
  border: 2px solid transparent;
}

.edit-mode .canvas-wrapper:hover {
  border-color: #3b82f6;
}

.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 1rem;
  font-size: 0.875rem;
  color: #64748b;
  border-radius: 8px;
  z-index: 50;
  text-align: center;
  padding: 2rem;
}

.loading-spinner {
  width: 40px;
  height: 40px;
  border: 3px solid #e2e8f0;
  border-top-color: #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.error-icon {
  width: 64px;
  height: 64px;
  color: #ef4444;
  margin-bottom: 0.5rem;
}

.error-icon svg {
  width: 100%;
  height: 100%;
}

.loading-overlay h3 {
  color: #bdbdbd;
  margin: 0 0 0.75rem 0;
  font-size: 1.25rem;
  font-weight: 600;
}

.loading-overlay p {
  color: #6b7280;
  margin: 0 0 0.75rem 0;
  line-height: 1.5;
  max-width: 400px;
}

.error-details {
  font-size: 0.8rem;
  margin-bottom: 1.5rem !important;
}

.retry-button {
  background-color: #3b82f6;
  color: white;
  border: none;
  border-radius: 6px;
  padding: 0.75rem 1.5rem;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: background-color 0.2s;
}

.retry-button:hover {
  background-color: #2563eb;
}

.retry-button:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(59, 130, 246, 0.3);
}

.confirmation-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
  backdrop-filter: blur(4px);
}

.confirmation-modal {
  background: white;
  border-radius: 12px;
  padding: 2rem;
  max-width: 400px;
  width: 90%;
  text-align: center;
  box-shadow: 0 25px 50px -12px rgba(0, 0, 0, 0.25);
  animation: modalSlideIn 0.3s ease-out;
}

.confirmation-icon {
  width: 64px;
  height: 64px;
  color: #ef4444;
  margin: 0 auto 1rem auto;
}

.confirmation-icon svg {
  width: 100%;
  height: 100%;
}

.confirmation-modal h3 {
  margin: 0 0 0.75rem 0;
  font-size: 1.25rem;
  font-weight: 600;
  color: #1f2937;
}

.confirmation-modal p {
  margin: 0 0 2rem 0;
  color: #6b7280;
  line-height: 1.5;
}

.confirmation-buttons {
  display: flex;
  gap: 0.75rem;
  justify-content: center;
}

.cancel-button,
.delete-button {
  padding: 0.75rem 1.5rem;
  border-radius: 8px;
  font-weight: 500;
  font-size: 0.875rem;
  cursor: pointer;
  transition: all 0.2s ease;
  border: none;
  min-width: 100px;
}

.cancel-button {
  background: #f3f4f6;
  color: #374151;
}

.cancel-button:hover {
  background: #e5e7eb;
}

.delete-button {
  background: #ef4444;
  color: white;
}

.delete-button:hover {
  background: #dc2626;
}

.toast-notification {
  position: fixed;
  top: 20px;
  right: 20px;
  z-index: 1001;
  max-width: 400px;
  border-radius: 8px;
  box-shadow: 0 10px 25px -5px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
  animation: toastSlideIn 0.3s ease-out;
}

.toast-notification.success {
  background: #10b981;
  color: white;
}

.toast-notification.error {
  background: #ef4444;
  color: white;
}

.toast-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 1rem 1.25rem;
}

.toast-icon {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.toast-icon svg {
  width: 100%;
  height: 100%;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

@keyframes modalSlideIn {
  from {
    opacity: 0;
    transform: scale(0.9) translateY(-10px);
  }
  to {
    opacity: 1;
    transform: scale(1) translateY(0);
  }
}

@keyframes toastSlideIn {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@media (max-width: 768px) {
  .editor-canvas-container {
    padding: 0.5rem 0;
  }
  
  .mobile-view .canvas-wrapper {
    margin: 0.5rem auto;
  }
  
  .editor-frame {
    height: calc(100vh - 100px);
  }

  .toast-notification {
    top: 10px;
    right: 10px;
    left: 10px;
    max-width: none;
  }

  .confirmation-modal {
    margin: 1rem;
    width: calc(100% - 2rem);
  }
}

@media (prefers-color-scheme: dark) {
  .editor-canvas-container {
    background-color: #1a1a1a;
  }
  
  .canvas-wrapper {
    background: #424242;
  }
  
  .loading-overlay {
    background: rgba(27, 27, 27, 0.9);
    color: #94a3b8;
  }
  
  .loading-spinner {
    border-color: #334155;
    border-top-color: #ffffff;
  }

  .confirmation-modal {
    background: #1f2937;
    color: white;
  }

  .confirmation-modal h3 {
    color: white;
  }

  .cancel-button {
    background: #374151;
    color: #d1d5db;
  }

  .cancel-button:hover {
    background: #4b5563;
  }
}

:global(.editor-action-button) {
  position: absolute;
  z-index: 1000;
  width: 28px;
  height: 28px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 8px;
  background: #1a1a1a;
  color: #f8fafc;
  font-size: 18px;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  border: 2px solid #3b82f6;
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
  backdrop-filter: blur(8px);
}

:global(.add-button) {
  left: 50%;
  transform: translateX(-50%) scale(0.9);
}

:global(.add-button-top) {
  top: -14px;
}

:global(.add-button-bottom) {
  bottom: -14px;
}

:global(.delete-button) {
  top: 8px;
  right: 8px;
  background: #1a1a1a;
  border-color: #ef4444;
  transform: scale(0.9);
}

:global(.component-container) {
  position: relative;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  border-radius: 4px;
}

:global(.edit-mode .component-container:hover) {
  background: rgba(59, 130, 246, 0.04);
}

:global(.edit-mode .component-container:hover .editor-action-button) {
  opacity: 1;
  transform: translateX(-50%) scale(1);
}

:global(.edit-mode .component-container:hover .delete-button) {
  transform: scale(1);
}

:global(.preview-mode .component-container:hover),
:global(.preview-mode .component-container),
:global(.preview-mode .component-container *) {
  background: none !important;
  outline: none !important;
  border: none !important;
}

:global(.editor-action-button:hover) {
  transform: translateX(-50%) scale(1.1) !important;
  box-shadow: 0 8px 12px rgba(0, 0, 0, 0.15);
}

:global(.delete-button:hover) {
  transform: scale(1.1) !important;
  background: #dc2626;
  border-color: #dc2626;
}

:global(.element-content) {
  position: relative;
  padding: 4px;
}

:global(.selected-element) {
  outline: none !important;
  position: relative;
}

:global(.edit-mode .selected-element::before) {
  content: '';
  position: absolute;
  inset: -4px;
  border: 2px solid #3b82f6;
  border-radius: 6px;
  pointer-events: none;
  background: rgba(59, 130, 246, 0.04);
}

:global(.edit-mode .selected-element::after) {
  content: '';
  position: absolute;
  inset: -4px;
  border-radius: 6px;
  pointer-events: none;
  background: linear-gradient(45deg, 
    rgba(59, 130, 246, 0.1) 0%,
    rgba(59, 130, 246, 0) 100%
  );
  animation: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

:global(.preview-mode .selected-element::before),
:global(.preview-mode .selected-element::after) {
  display: none !important;
}
</style>


