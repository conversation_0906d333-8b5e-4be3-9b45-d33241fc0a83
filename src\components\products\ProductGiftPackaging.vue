<template>
  <div class="gift-packaging-container" :class="{ 'gift-packaging-compact': props.shippingFree === 'YES' && !giftPackaging }">
    <div class="flex flex-col gap-4">
      <div class="flex items-center justify-between">
        <IluriaLabel class="block text-sm font-medium">{{ t('product.giftPackagingQuestion') }}</IluriaLabel>
        <IluriaToggleSwitch v-model="giftPackaging" />
      </div>

      <div class="flex flex-col gap-4" v-if="giftPackaging">
        <IluriaInputText
          id="giftPackagingPrice"
          name="giftPackagingPrice"
          type="money"
          v-model="giftPackagingPrice"
          :label="t('product.giftPackagingPrice')"
          placeholder="0,00"
          prefix="R$"
        />

        <IluriaRadioGroup
          id="giftPackagingType"
          name="giftPackagingType"
          :label="t('product.giftPackagingType')"
          v-model="giftPackagingType"
          default-value="SUM_PRICE"
          :options="[
            { label: t('product.giftPackagingTypeSum'), value: 'SUM_PRICE' },
            { label: t('product.giftPackagingTypeMax'), value: 'MAX_PRICE' },
          ]"
        />
      </div>
    </div>
  </div>
</template>

<script setup>
import { watch } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';    


const { t } = useI18n();

const props = defineProps({
  shippingFree: {
    type: String,
    default: 'NO'
  }
});

const giftPackaging = defineModel('giftPackaging', { default: false });
const giftPackagingPrice = defineModel('giftPackagingPrice', { default: null });
const giftPackagingType = defineModel('giftPackagingType', { default: 'SUM_PRICE' });

watch(() => giftPackaging.value, (newValue) => {
  if (!newValue) {
    giftPackagingPrice.value = null;
    giftPackagingType.value = null;
  } else if (giftPackagingType.value === null) {
    giftPackagingType.value = 'SUM_PRICE';
  }
});
</script>

<style scoped>
.gift-packaging-container {
  justify-content: top;
  display: flex;
  flex-direction: column;
  gap: 1rem;
  min-height: auto;
  height: auto;
  padding: 1.25rem 0;
}

/* Reduzir padding quando o frete grátis estiver ativo e embalagem colapsada */
.gift-packaging-container.gift-packaging-compact {
  padding: 0;
}
</style>
