<template>
  <div class="mb-6">
    <IluriaSelect
      id="min-requirement-type"
      v-model="modelValue.minRequirementType"
      :options="[
        { value: 'NONE', label: t('promotions.editor.noMinimumRequired') },
        { value: 'ORDER_VALUE', label: t('promotions.editor.minimumOrderValue') },
        { value: 'ITEM_QUANTITY', label: t('promotions.editor.minimumItemQuantity') }
      ]"
      :placeholder="t('promotions.editor.selectMinimumRequirementType')"
      @update:modelValue="emitUpdate"
    />
    
    <div v-if="modelValue.minRequirementType === 'ORDER_VALUE'" class="mt-3">
      <IluriaInputText
        id="minimumOrderValue"
        v-model.number="modelValue.minValue"
        :placeholder="t('promotions.editor.minimumOrderPlaceholder')"
        type="money"
        prefix="R$"
        :min="0"
        :step="0.01"
        class="w-full"
        @update:modelValue="emitUpdate"
      />
    </div>
    
    <div v-if="modelValue.minRequirementType === 'ITEM_QUANTITY'" class="mt-3">
      <IluriaInputText 
        id="minimumItemQuantity" 
        v-model.number="modelValue.minQuantity" 
        :placeholder="t('promotions.editor.minimumItemsPlaceholder')"
        type="number"
        :min="1"
        :step="1"
        suffix="un"
        class="w-full"
        @update:modelValue="emitUpdate"
      />
    </div>
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import { watch } from 'vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

if (!props.modelValue.minRequirementType) {
  props.modelValue.minRequirementType = 'NONE';
}

watch(() => props.modelValue.minRequirementType, (newType) => {
  if (newType === 'ORDER_VALUE') {
    if (!props.modelValue.minValue) {
      props.modelValue.minValue = 0;
    }
    props.modelValue.minQuantity = 0;
  } else if (newType === 'ITEM_QUANTITY') {
    if (!props.modelValue.minQuantity) {
      props.modelValue.minQuantity = 0;
    }
    props.modelValue.minValue = 0;
  } else {
    props.modelValue.minValue = 0;
    props.modelValue.minQuantity = 0;
  }
  
  props.modelValue.minRequirementType = newType;
  emitUpdate();
});

const emitUpdate = () => {
  emit('update:modelValue', props.modelValue);
};
</script>
