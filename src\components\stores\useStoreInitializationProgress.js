import { ref, onUnmounted } from 'vue'
import { Client } from '@stomp/stompjs'
import SockJ<PERSON> from 'sockjs-client'
import { useAuthStore } from '@/stores/auth.store'

// Polyfill for global if not defined
if (typeof global === 'undefined') {
  window.global = window
}

export function useStoreInitializationProgress() {
  const authStore = useAuthStore()
  
  const isConnected = ref(false)
  const progress = ref({
    step: '',
    message: '',
    progress: 0,
    timestamp: 0
  })
  const isInitializing = ref(false)
  const error = ref(null)
  
  let stompClient = null
  let subscription = null

  const connect = () => {
    if (stompClient && stompClient.connected) {
      return Promise.resolve()
    }

    return new Promise((resolve, reject) => {
      try {
        // Use modern STOMP client with SockJS
        stompClient = new Client({
          webSocketFactory: () => new SockJS('http://localhost:8081/ws/store-initialization'),
          connectHeaders: {},
          debug: (str) => {
            // Logs habilitados para debug
           
          },
          reconnectDelay: 5000,
          heartbeatIncoming: 4000,
          heartbeatOutgoing: 4000,
          onConnect: (frame) => {
           
            isConnected.value = true
            resolve()
          },
          onStompError: (frame) => {
            console.error('❌ STOMP Error:', frame)
            isConnected.value = false
            const errorMessage = frame.headers['message'] || 'STOMP connection error'
            error.value = errorMessage
            reject(new Error(errorMessage))
          },
          onWebSocketError: (wsError) => {
            console.error('❌ WebSocket Error:', wsError)
            isConnected.value = false
            const errorMessage = `WebSocket error: ${wsError.message || wsError}`
            error.value = errorMessage
            reject(new Error(errorMessage))
          },
          onWebSocketClose: (event) => {
            console.warn('⚠️ WebSocket Closed:', event)
            isConnected.value = false
          }
        })

        stompClient.activate()
      } catch (err) {
        reject(err)
      }
    })
  }

  const subscribe = (userEmail) => {
    if (!stompClient || !stompClient.connected) {
      return
    }

    const destination = `/topic/store-initialization/${userEmail}`
    
    subscription = stompClient.subscribe(destination, (message) => {
      try {
        const data = JSON.parse(message.body)

        // Validar se os dados necessários existem
        if (!data || typeof data !== 'object') {
          console.warn('Invalid progress data received:', data)
          return
        }

        progress.value = {
          step: data.step || 'STARTED',
          message: data.message || 'Processando...',
          progress: data.progress || 0,
          timestamp: data.timestamp || Date.now()
        }

        if (data.step === 'STARTED') {
          isInitializing.value = true
          error.value = null
        } else if (data.step === 'COMPLETED') {
          // Keep initializing true to show completion state
          // Will be set to false by the component after showing success
        } else if (data.step === 'ERROR') {
          isInitializing.value = false
          error.value = data.message
        }
      } catch (err) {
        console.error('Error processing progress message:', err)
        const errorMessage = typeof err === 'string' ? err : (err.message || 'Erro ao processar mensagem de progresso')
        error.value = errorMessage
        isInitializing.value = false
      }
    })
  }

  const startListening = async (userEmail) => {
    try {
      // Set initial connecting state
      progress.value = {
        step: 'CONNECTING',
        message: 'Conectando ao servidor...',
        progress: 5,
        timestamp: Date.now()
      }

      await connect()
      subscribe(userEmail)
    } catch (err) {
      console.error('❌ Erro ao conectar WebSocket:', err)
      const errorMessage = typeof err === 'string' ? err : (err.message || 'Erro desconhecido')
      error.value = `Erro ao conectar com o servidor: ${errorMessage}`

      // Tentar novamente após 3 segundos
      setTimeout(() => {
      
        startListening(userEmail)
      }, 3000)
    }
  }

  const disconnect = () => {
    if (subscription) {
      subscription.unsubscribe()
      subscription = null
    }

    if (stompClient && stompClient.connected) {
      stompClient.deactivate()
    }

    isConnected.value = false
    isInitializing.value = false
  }

  const reset = () => {
    progress.value = {
      step: '',
      message: '',
      progress: 0,
      timestamp: 0
    }
    isInitializing.value = false
    error.value = null
  }

  // Cleanup on unmount
  onUnmounted(() => {
    disconnect()
  })

  return {
    isConnected,
    progress,
    isInitializing,
    error,
    startListening,
    disconnect,
    reset
  }
}