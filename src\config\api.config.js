// API configuration with environment variable support

// Default values for development
const DEFAULT_STOREFRONT_URL = 'http://localhost:8080';
const DEFAULT_AUTH_USER = 'http://localhost:8081/user';
const DEFAULT_AUTH_URL = 'http://localhost:8081/auth';
const DEFAULT_STORE_URL = 'http://localhost:8081/store';
const DEFAULT_CATEGORY_URL = 'http://localhost:8081/categories';
const DEFAULT_BLOG_CATEGORY_URL = 'http://localhost:8081/blog-categories';
const DEFAULT_BLOG_POST_URL = 'http://localhost:8081/blog-posts';
const DEFAULT_CUSTOMER_URL = 'http://localhost:8081/customer';
const DEFAULT_ADDRESS_URL = 'http://localhost:8081/addresses';
const DEFAULT_COUPON_URL = 'http://localhost:8081/coupons';
const DEFAULT_PRODUCT_URL = 'http://localhost:8081/api/products';
const DEFAULT_PAGE_URL = 'http://localhost:8081/pages';
const DEFAULT_ORDER_URL = 'http://localhost:8081/orders';
const DEFAULT_SETTINGS_URL = 'http://localhost:8081';
const DEFAULT_DOMAINS_URL = 'http://localhost:8081/domain-manager';
const DEFAULT_PROMOTION_URL = 'http://localhost:8081/promotions';
const DEFAULT_FILE_MANAGER_URL = 'http://localhost:8081/file-manager';
const DEFAULT_CEP_URL = 'http://localhost:8081/origin-cep';
const DEFAULT_COLLECTION_URL = 'http://localhost:8081/api/collection-product';
const DEFAULT_ATTRIBUTE_URL = 'http://localhost:8081/api/attributes';
const DEFAULT_MEASUREMENT_TABLE_URL = 'http://localhost:8081/api/measurement-tables';
const DEFAULT_COMBINED_PRODUCT_URL = 'http://localhost:8081/api/combined-products';
const DEFAULT_QUESTION_URL = 'http://localhost:8081/api/questions';
const DEFAULT_STORE_STATUS_URL = 'http://localhost:8081/store-status';
const DEFAULT_SOCIAL_LINKS_URL = 'http://localhost:8081/store/social-links';
const DEFAULT_LAYOUT_URL = 'http://localhost:8081/api/layouts';
const DEFAULT_THEME_CATEGORY_URL = 'http://localhost:8081/api/theme-categories';
const DEFAULT_THEME_URL = 'http://localhost:8081/store/themes';
const DEFAULT_GOOGLE_MAPS_API_KEY = 'AIzaSyCDhfeGIVpj_9bil4Hdc9NHFT7QuKtDnP0';
const DEFAULT_PAYMENT_INTEGRATION_URL = 'http://localhost:8081/api/payment-integration';
const DEFAULT_STORE_PHYSICAL_DATA_URL = 'http://localhost:8081/api/store/physical-data';
const DEFAULT_COMMUNITY_URL = 'http://localhost:8081/api/v1/community';
const DEFAULT_CEP_ADDRESS_URL = 'http://localhost:8081/cep-address-api';
const DEFAULT_USER_PROFILE_URL = 'http://localhost:8081/user/profile';
const DEFAULT_USER_SESSIONS_URL = 'http://localhost:8081/api/user/sessions';
const DEFAULT_TEAM_BASE_URL = 'http://localhost:8081/store/team';
const DEFAULT_COLLABORATION_BASE_URL = 'http://localhost:8081/user/collaboration';
const DEFAULT_ROLE_BASE_URL = 'http://localhost:8081/';

// Get environment variables from import.meta.env (Vite) or process.env (Node.js)
const getEnvVariable = (key, defaultValue) => {
  // For Vite in browser
  if (typeof import.meta !== 'undefined' && import.meta.env) {
    return import.meta.env[key] || defaultValue;
  }
  // For Node.js environment
  if (typeof process !== 'undefined' && process.env) {
    return process.env[key] || defaultValue;
  }
  return defaultValue;
};

// Export API URLs
export const API_URLS = {
  STOREFRONT_BASE_URL: getEnvVariable('VITE_STOREFRONT_API_URL', DEFAULT_STOREFRONT_URL),
  AUTH_USER_BASE_URL: getEnvVariable('VITE_AUTH_USER_API_URL', DEFAULT_AUTH_USER),
  AUTH_BASE_URL: getEnvVariable('VITE_AUTH_API_URL', DEFAULT_AUTH_URL),
  SETTINGS_BASE_URL: getEnvVariable('VITE_SETTINGS_API_URL', DEFAULT_SETTINGS_URL),
  STORE_BASE_URL: getEnvVariable('VITE_STORE_API_URL', DEFAULT_STORE_URL),
  CATEGORY_BASE_URL: getEnvVariable('VITE_CATEGORY_API_URL', DEFAULT_CATEGORY_URL),
  BLOG_CATEGORY_BASE_URL: getEnvVariable('VITE_BLOG_CATEGORY_API_URL', DEFAULT_BLOG_CATEGORY_URL),
  BLOG_POST_BASE_URL: getEnvVariable('VITE_BLOG_POST_API_URL', DEFAULT_BLOG_POST_URL),
  CUSTOMER_BASE_URL: getEnvVariable('VITE_CUSTOMER_API_URL', DEFAULT_CUSTOMER_URL),
  ADDRESS_BASE_URL: getEnvVariable('VITE_ADDRESS_API_URL', DEFAULT_ADDRESS_URL),
  COUPON_BASE_URL: getEnvVariable('VITE_COUPON_API_URL', DEFAULT_COUPON_URL),
  PRODUCT_BASE_URL: getEnvVariable('VITE_PRODUCT_API_URL', DEFAULT_PRODUCT_URL),
  PAGE_BASE_URL: getEnvVariable('VITE_PAGE_API_URL', DEFAULT_PAGE_URL),
  ORDER_BASE_URL: getEnvVariable('VITE_ORDER_API_URL', DEFAULT_ORDER_URL),
  PROMOTION_BASE_URL: getEnvVariable('VITE_PROMOTION_API_URL', DEFAULT_PROMOTION_URL),
  DOMAINS_BASE_URL: getEnvVariable('VITE_DOMAINS_API_URL', DEFAULT_DOMAINS_URL),
  FILE_MANAGER_BASE_URL: getEnvVariable('VITE_FILE_MANAGER_API_URL', DEFAULT_FILE_MANAGER_URL),
  CEP_BASE_URL: getEnvVariable('VITE_CEP_API_URL', DEFAULT_CEP_URL),
  COLLECTION_BASE_URL: getEnvVariable('VITE_COLLECTION_API_URL', DEFAULT_COLLECTION_URL),
  COMBINED_PRODUCT_BASE_URL: getEnvVariable('VITE_COMBINED_PRODUCT_API_URL', DEFAULT_COMBINED_PRODUCT_URL),
  ATTRIBUTE_BASE_URL: getEnvVariable('VITE_ATTRIBUTE_API_URL', DEFAULT_ATTRIBUTE_URL),
  MEASUREMENT_TABLE_BASE_URL: getEnvVariable('VITE_MEASUREMENT_TABLE_API_URL', DEFAULT_MEASUREMENT_TABLE_URL),
  STORE_STATUS_BASE_URL: getEnvVariable('VITE_STORE_STATUS_API_URL', DEFAULT_STORE_STATUS_URL),
  SOCIAL_LINKS_BASE_URL: getEnvVariable('VITE_SOCIAL_LINKS_API_URL', DEFAULT_SOCIAL_LINKS_URL),
  PRODUCT_QUESTIONS_BASE_URL: getEnvVariable('VITE_PRODUCT_QUESTIONS_API_URL', DEFAULT_QUESTION_URL),
  LAYOUT_BASE_URL: getEnvVariable('VITE_LAYOUT_API_URL', DEFAULT_LAYOUT_URL),
  THEME_CATEGORY_BASE_URL: getEnvVariable('VITE_THEME_CATEGORY_API_URL', DEFAULT_THEME_CATEGORY_URL),
  THEME_BASE_URL: getEnvVariable('VITE_THEME_API_URL', DEFAULT_THEME_URL),
  PAYMENT_INTEGRATION_BASE_URL: getEnvVariable('VITE_PAYMENT_INTEGRATION_API_URL', DEFAULT_PAYMENT_INTEGRATION_URL),
  STORE_PHYSICAL_DATA_BASE_URL: getEnvVariable('VITE_STORE_PHYSICAL_DATA_API_URL', DEFAULT_STORE_PHYSICAL_DATA_URL),
  COMMUNITY_BASE_URL: getEnvVariable('VITE_COMMUNITY_API_URL', DEFAULT_COMMUNITY_URL),
  CEP_ADDRESS_BASE_URL: getEnvVariable('VITE_CEP_ADDRESS_API_URL', DEFAULT_CEP_ADDRESS_URL),
  USER_PROFILE_BASE_URL: getEnvVariable('VITE_USER_PROFILE_API_URL', DEFAULT_USER_PROFILE_URL),
  USER_SESSIONS_BASE_URL: getEnvVariable('VITE_USER_SESSIONS_API_URL', DEFAULT_USER_SESSIONS_URL),
  TEAM_BASE_URL: getEnvVariable('VITE_TEAM_API_URL', DEFAULT_TEAM_BASE_URL),
  COLLABORATION_BASE_URL: getEnvVariable('VITE_COLLABORATION_API_URL', DEFAULT_COLLABORATION_BASE_URL),
  ROLE_BASE_URL: getEnvVariable('VITE_ROLE_API_URL', DEFAULT_ROLE_BASE_URL)

};

// Export Google Maps API Key
export const GOOGLE_MAPS_API_KEY = getEnvVariable('VITE_GOOGLE_MAPS_API_KEY', DEFAULT_GOOGLE_MAPS_API_KEY);

export default API_URLS;
