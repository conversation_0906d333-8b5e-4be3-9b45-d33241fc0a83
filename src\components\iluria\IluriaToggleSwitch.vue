<template>
  <div :class="wrapperClass">
    <div :class="{ 'flex items-center gap-2': props.labelPosition === 'horizontal', 'flex flex-col': props.labelPosition === 'vertical' }">
      <IluriaLabel v-if="label" :for="toggleId">{{ label }}</IluriaLabel>
      <div 
        :id="toggleId"
        class="custom-toggle-switch"
        :class="{ 'toggle-checked': model }"
        @click="toggleValue"
        @keydown.space.prevent="toggleValue"
        @keydown.enter.prevent="toggleValue"
        :tabindex="0"
        role="switch"
        :aria-checked="model"
        :aria-labelledby="label ? `${toggleId}-label` : undefined"
      >
        <div class="toggle-handle"></div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import IluriaLabel from './IluriaLabel.vue'

const model = defineModel()

const props = defineProps({
  id: {
    type: String,
    required: false
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  wrapperClass: {
    type: String,
    default: ''
  },
  labelPosition: {
    type: String,
    default: 'vertical'
  }
})

const toggleId = computed(() => props.id || `toggle-${Math.random().toString(36).substr(2, 9)}`)

const toggleValue = () => {
  model.value = !model.value
}
</script>

<style scoped>
.custom-toggle-switch {
  width: 44px;
  height: 24px;
  background: var(--iluria-color-switch-bg);
  border: 1px solid var(--iluria-color-switch-border);
  border-radius: 12px;
  position: relative;
  cursor: pointer;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  outline: none;
  display: inline-block;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.custom-toggle-switch:hover {
  background: var(--iluria-color-hover);
}

.custom-toggle-switch:focus-visible {
  outline: 2px solid var(--iluria-color-focus-ring);
  outline-offset: 2px;
}

.custom-toggle-switch.toggle-checked {
  background: var(--iluria-color-switch-bg-active);
  border-color: var(--iluria-color-switch-border-active);
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.custom-toggle-switch.toggle-checked:hover {
  background: var(--iluria-color-primary-hover);
  border-color: var(--iluria-color-primary-hover);
}

.toggle-handle {
  position: absolute;
  top: 2px;
  left: 2px;
  width: 18px;
  height: 18px;
  background: var(--iluria-color-switch-handle);
  border-radius: 50%;
  transition: all 0.3s cubic-bezier(0.68, -0.55, 0.265, 1.55);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
  z-index: 1;
}

.custom-toggle-switch.toggle-checked .toggle-handle {
  transform: translateX(20px);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.custom-toggle-switch:active .toggle-handle {
  transform: none;
}

.custom-toggle-switch.toggle-checked:active .toggle-handle {
  transform: translateX(20px);
}

.mt-1 {
  margin-top: 0.25rem;
}
</style>
