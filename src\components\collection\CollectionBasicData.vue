<template>
    <div class="space-y-4">
      <div class="form-group w-full">
        <IluriaInputText 
          id="name" 
          name="name" 
          :label="t('collectionProduct.form.name')" 
          v-model="form.name" 
          :formContext="props.formContext?.name" 
        />
      </div>
  
      <div class="form-group w-full">
        <IluriaEditor
          id="description" 
          :label="t('collectionProduct.form.description')"
          height="150px"
          v-model="form.description"
          :placeholder="t('collectionProduct.form.description')"
          hideToggle
        />
        <Message v-if="props.formContext?.description?.invalid"> 
          {{ props.formContext.description.error?.message }} 
        </Message>
      </div>

      <div class="mb-1 form-group">
        <IluriaRadioGroup
          id="type" 
          name="type" 
          :label="t('collectionProduct.form.type')" 
          v-model="form.type"
          :options="typeOptions"
          :formContext="props.formContext?.type"
        />
      </div>
    </div>
  </template>
  
  <script setup>
  import { inject, ref } from 'vue';
  import { useI18n } from 'vue-i18n';
  import Message from 'primevue/message'
  import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
  import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
  import IluriaEditor from '@/components/editor/IluriaEditor.vue';
  
  const { t } = useI18n();
  
  const form = inject('productForm');

  const typeOptions = ref([
    { label: t('collectionProduct.form.manual'), value: 'MANUAL' },
    { label: t('collectionProduct.form.smart'), value: 'SMART' }
  ]);
  
  const props = defineProps({
    formContext: {
      type: Object,
      default: () => ({})
    }
  });
  
  const validationRules = {
    name: {},
    description: {},
    type: {},
  };
  
  defineExpose({ validationRules });
  </script>
