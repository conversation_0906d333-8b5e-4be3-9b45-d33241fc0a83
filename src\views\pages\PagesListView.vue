<template>
  <div class="pages-list-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="t('pages.pages')"
      subtitle="<PERSON><PERSON><PERSON><PERSON> as páginas do seu site"
      :showSearch="true"
      :showAdd="true"
      :addText="t('pages.add')"
      @search="handleSearch"
      @add-click="goToAddPage"
    />

    <!-- Main Content -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="sortedPages"
        :loading="loading"
        :columns="tableColumns"
        class="pages-table"
      >
        <!-- Header Slots -->
        <template #header-title>
          <span class="column-header" data-sortable="true" @click="toggleSort('title')">
            {{ t('pages.title') }}
            <span v-if="sortField === 'title'">
              {{ sortOrder === 1 ? '▲' : sortOrder === -1 ? '▼' : '' }}
            </span>
          </span>
        </template>

        <template #header-activeProd>
          <span class="column-header" data-sortable="true" @click="toggleSort('activeProd')">
            {{ t('pages.publishedProduction') }}
            <span v-if="sortField === 'activeProd'">
              {{ sortOrder === 1 ? '▲' : sortOrder === -1 ? '▼' : '' }}
            </span>
          </span>
        </template>

        <template #header-activeDev>
          <span class="column-header" data-sortable="true" @click="toggleSort('activeDev')">
            {{ t('pages.publishedDevelopment') }}
            <span v-if="sortField === 'activeDev'">
              {{ sortOrder === 1 ? '▲' : sortOrder === -1 ? '▼' : '' }}
            </span>
          </span>
        </template>

        <template #header-updated>
          <span class="column-header" data-sortable="true" @click="toggleSort('updated')">
            {{ t('pages.updated') }}
            <span v-if="sortField === 'updated'">
              {{ sortOrder === 1 ? '▲' : sortOrder === -1 ? '▼' : '' }}
            </span>
          </span>
        </template>

        <template #header-actions>
          <span class="column-header">{{ t('pages.actions') }}</span>
        </template>

        <!-- Production Status Column -->
        <template #column-activeProd="{ data }">
          <span class="status-badge" :class="getStatusClass(data.activeProd)">
            {{ data.activeProd ? 'Yes' : 'No' }}
          </span>
        </template>

        <!-- Development Status Column -->
        <template #column-activeDev="{ data }">
          <span class="status-badge" :class="getStatusClass(data.activeDev)">
            {{ data.activeDev ? 'Yes' : 'No' }}
          </span>
        </template>

        <!-- Updated Column -->
        <template #column-updated="{ data }">
          <span class="date-text">{{ formatDate(data.updatedAt) }}</span>
        </template>

        <!-- Actions Column -->
        <template #column-actions="{ data }">
          <div class="action-buttons">
            <IluriaButton 
              color="text-primary" 
              size="small" 
              :hugeIcon="PencilEdit01Icon" 
              @click.prevent="editPage(data.id)"
              :title="t('edit')"
            >
              {{ t('edit') }}
            </IluriaButton>
            <IluriaButton 
              color="text-danger" 
              size="small" 
              :hugeIcon="Delete01Icon" 
              @click.prevent="confirmDelete(data)"
              :title="t('delete')"
            >
              {{ t('delete') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <DocumentValidationIcon />
            </div>
            <h3 class="empty-title">{{ t('pages.noPages') }}</h3>
            <p class="empty-description">Crie sua primeira página para começar</p>
            <IluriaButton @click="goToAddPage" :hugeIcon="PlusSignSquareIcon" class="mt-4">
              {{ t('pages.add') }}
            </IluriaButton>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>Carregando páginas...</span>
          </div>
        </template>
      </IluriaDataTable>

      <!-- Pagination -->
      <div v-if="totalPages > 0" class="pagination-wrapper">
        <IluriaPagination 
          :current-page="currentPage"
          :total-pages="totalPages"
          @go-to-page="changePage"
        />
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import PagesService from '@/services/pages.service'  
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import { DocumentValidationIcon } from '@hugeicons-pro/core-stroke-rounded'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import { Delete01Icon, PencilEdit01Icon, PlusSignSquareIcon } from '@hugeicons-pro/core-stroke-rounded'

const router = useRouter()
const { t } = useI18n()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const pages = ref([]) 
const loading = ref(true) 
const currentPage = ref(0) 
const totalPages = ref(0) 
const filters = ref({ filter: '' }) 

const sortField = ref(null)
const sortOrder = ref(null)

const toggleSort = (field) => {
  if (sortField.value !== field) {
    sortField.value = field
    sortOrder.value = 1
  } else if (sortOrder.value === 1) {
    sortOrder.value = -1
  } else if (sortOrder.value === -1) {
    sortField.value = null
    sortOrder.value = null
  } else {
    sortOrder.value = 1
  }
}

const sortedPages = computed(() => {
  if (!sortField.value || !sortOrder.value) return pages.value
  const sorted = [...pages.value]
  if (sortField.value === 'title') {
    sorted.sort((a, b) => {
      if (!a.title || !b.title) return 0
      return sortOrder.value * a.title.localeCompare(b.title)
    })
  } else if (sortField.value === 'updated') {
    sorted.sort((a, b) => {
      return sortOrder.value * (new Date(a.updatedAt).getTime() - new Date(b.updatedAt).getTime())
    })
  } else if (sortField.value === 'activeProd' || sortField.value === 'activeDev') {
    sorted.sort((a, b) => {
      return sortOrder.value * ((a[sortField.value] === b[sortField.value]) ? 0 : (a[sortField.value] ? -1 : 1))
    })
  }
  return sorted
})

// Configuração das colunas da tabela
const tableColumns = [
  { field: 'title', headerClass: 'col-large', class: 'col-large' },
  { field: 'activeProd', headerClass: 'col-medium', class: 'col-medium' },
  { field: 'activeDev', headerClass: 'col-medium', class: 'col-medium' },
  { field: 'updated', headerClass: 'col-date', class: 'col-date' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
]

const loadPages = async () => {
    loading.value = true
    try {
        const response = await PagesService.getPages(filters.value.filter, currentPage.value, 10)
        pages.value = response.content
        totalPages.value = response.totalPages
    } catch (error) {
        console.error('Error loading pages:', error)
    } finally {
        loading.value = false
    }
}

const formatDate = (dateString) => {
    if (!dateString) return '';
    const date = new Date(dateString);
    return new Intl.DateTimeFormat('default', {
        year: 'numeric',
        month: 'short',
        day: 'numeric',
        hour: '2-digit',
        minute: '2-digit'
    }).format(date);
}

const changePage = (page) => {
    currentPage.value = page
    loadPages()
}

const editPage = (id) => {
    router.push(`/pages/${id}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
    confirmModalMessage.value = message
    confirmModalTitle.value = title
    confirmModalConfirmText.value = t('delete')
    confirmModalCancelText.value = t('cancel')
    confirmModalType.value = 'error'
    confirmCallback.value = onConfirm
    showConfirmModal.value = true
}

const handleConfirm = () => {
    if (confirmCallback.value) {
        confirmCallback.value()
    }
    showConfirmModal.value = false
}

const handleCancel = () => {
    showConfirmModal.value = false
}

const confirmDelete = (page) => {
    showConfirmDanger(
        t('pages.confirmDelete'),
        t('confirmDialog.defaultTitle'),
        () => deletePage(page.id)
    );
}

const deletePage = async (id) => {
    try {
        await PagesService.deletePage(id)
        loadPages()
    } catch (error) {
        console.error('Error deleting page:', error)
    }
}

const goToAddPage = () => {
    router.push('/pages/new') 
}

const getStatusClass = (status) => {
    return {
        'status-active': status,
        'status-inactive': !status
    }
}

let searchTimeout = null
const debouncedSearch = () => {
    clearTimeout(searchTimeout)
    searchTimeout = setTimeout(() => {
        currentPage.value = 0
        loadPages()
    }, 400)
}

const handleSearch = (searchValue) => {
    filters.value.filter = searchValue
    debouncedSearch()
}

onMounted(() => {
    loadPages()
})
</script>

<style scoped>
.pages-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}



.search-input {
  min-width: 250px;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* Status Badge */
.status-badge {
  display: inline-flex;
  align-items: center;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 6px;
}

.status-active {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
}

.status-inactive {
  background: var(--iluria-color-error-bg);
  color: var(--iluria-color-error);
}

/* Date Text */
.date-text {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  display: block;
  text-align: center;
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: center;
}

/* Pagination */
.pagination-wrapper {
  margin-top: 24px;
  display: flex;
  justify-content: center;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
}

.empty-icon {
  font-size: 48px;
  color: var(--iluria-color-text-disabled);
  margin-bottom: 16px;
}

.empty-icon svg {
  width: 48px;
  height: 48px;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Utility classes */
.mt-4 {
  margin-top: 1rem;
}

/* Responsive */
@media (max-width: 768px) {
  .pages-list-container {
    padding: 16px;
  }
  

  
  .search-input {
    min-width: auto;
    width: 100%;
  }
  
  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .pages-list-container {
    padding: 12px;
  }
  

}

/* Column width definitions */
:deep(.col-large) {
  width: 220px;
  text-align: center;
}

:deep(.col-medium) {
  width: 180px;
  text-align: center;
}

:deep(.col-date) {
  width: 130px;
  text-align: center;
}

:deep(.col-actions) {
  width: 120px;
  text-align: center;
}

/* Column header style */
:deep(.pages-table th .column-header) {
  display: block;
  width: 100%;
  text-align: center;
  cursor: default;
  user-select: none;
}

:deep(.pages-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* General table styling */
:deep(.pages-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.pages-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.pages-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
}

:deep(.pages-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.pages-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.pages-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
}
</style>
