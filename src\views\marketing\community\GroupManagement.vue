<template>
  <ViewContainer
    :title="$t('community.groupManagement.managementTitle')"
    :icon="Folder01Icon"
  >




      <form @submit.prevent="handleSubmit" class="space-y-6">
        <!-- Group Name -->
        <div>
          <label for="name" class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-2">{{ $t('community.groupManagement.groupName') }}</label>
          <input
            type="text"
            id="name"
            v-model="formData.name"
            maxlength="60"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-gray-200 focus:outline-none"
            :class="{ 'border-red-500': errors.name }"
          >
          <p v-if="errors.name" class="mt-1 text-red-500 text-sm">{{ errors.name }}</p>
        </div>

        <!-- Category -->
        <div>
          <label for="categoryId" class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-2">{{ $t('community.groupManagement.categoryLabel') }}</label>
          <select
            id="categoryId"
            v-model="formData.categoryId"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-gray-200 focus:outline-none"
            :class="{ 'border-red-500': errors.categoryId }"
          >
            <option value="">{{ $t('community.groupManagement.selectCategory') }}</option>
            <option
              v-for="category in categories"
              :key="category.id"
              :value="category.id"
            >
              {{ category.name }}
            </option>
          </select>
          <p v-if="errors.categoryId" class="mt-1 text-red-500 text-sm">{{ errors.categoryId }}</p>
        </div>

        <!-- Description -->
        <div>
          <label for="description" class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-2">{{ $t('community.groupManagement.description') }}</label>
          <textarea
            id="description"
            v-model="formData.description"
            rows="3"
            maxlength="160"
            class="w-full px-4 py-2 border rounded-lg focus:ring-2 focus:ring-gray-200 focus:outline-none resize-none"
            :class="{ 'border-red-500': errors.description }"
          ></textarea>
          <p v-if="errors.description" class="mt-1 text-red-500 text-sm">{{ errors.description }}</p>
        </div>

        <!-- Image Upload -->
        <IluriaSimpleImageUpload
          v-model="formData.imageUrl"
          :label="$t('community.groupManagement.imageLabel')"
          :error-message="errors.imageUrl"
          :add-button-text="$t('community.groupManagement.uploadImageText')"
          :change-button-text="$t('community.groupManagement.changeImageText')"
          :remove-button-text="$t('community.groupManagement.removeImageText')"
          :format-hint="$t('community.groupManagement.imageSizeHint')"
        />


      </form>
  </ViewContainer>
</template>

<script setup>
import { ref, reactive, watch, onMounted, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { z } from 'zod';
import { useToastStore } from '@/stores/toast.store';
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { Folder01Icon } from '@hugeicons-pro/core-bulk-rounded';
import communityService from '@/services/community.service';

const props = defineProps({
  groupToEdit: {
    type: Object,
    default: null
  }
});

const emit = defineEmits(['close', 'success']);
const toast = useToastStore();
const { t } = useI18n();

const isSubmitting = ref(false);
const loading = ref(false);
const error = ref(null);
const categories = ref([]);

const formData = reactive({
  name: '',
  description: '',
  imageUrl: null,
  categoryId: ''
});

const errors = reactive({
  name: '',
  description: '',
  imageUrl: '',
  categoryId: ''
});

const groupSchema = computed(() => z.object({
  name: z.string({
      required_error: t('community.groupManagement.validation.nameRequired'),
      invalid_type_error: t('community.groupManagement.validation.nameRequired')
    })
    .trim()
    .min(1, t('community.groupManagement.validation.nameRequired'))
    .max(60, t('validation.maxLength', { field: t('community.groupManagement.groupName'), length: 60 })),
  description: z.string({
      required_error: t('community.groupManagement.validation.descriptionRequired'),
      invalid_type_error: t('community.groupManagement.validation.descriptionRequired')
    })
    .trim()
    .min(1, t('community.groupManagement.validation.descriptionRequired'))
    .max(160, t('validation.maxLength', { field: t('community.groupManagement.description'), length: 160 })),
  categoryId: z.string({
      required_error: t('community.groupManagement.validation.categoryRequired'),
      invalid_type_error: t('community.groupManagement.validation.categoryRequired')
    })
    .min(1, t('community.groupManagement.validation.categoryRequired'))
}));

// Load categories
const loadCategories = async () => {
  loading.value = true;
  error.value = null;
  try {
    const response = await communityService.listCategories();
    categories.value = response;
  } catch (err) {
    console.error('Error loading categories:', err);
    error.value = t('community.groupManagement.error.loadCategories');
    toast.addToast({
      type: 'error',
      message: t('community.groupManagement.error.loadCategories')
    });
  } finally {
    loading.value = false;
  }
};

// Watch for changes in groupToEdit prop
watch(() => props.groupToEdit, (newValue) => {
  if (newValue) {
    formData.name = newValue.name;
    formData.description = newValue.description;
    formData.imageUrl = newValue.imageUrl;
    formData.categoryId = newValue.categoryId;
  } else {
    // Reset form when creating new group
    formData.name = '';
    formData.description = '';
    formData.imageUrl = null;
    formData.categoryId = '';
  }
}, { immediate: true });

const validateForm = () => {
  const parsed = groupSchema.value.safeParse(formData);
  errors.name = errors.description = errors.categoryId = '';
  if (!parsed.success) {
    const fe = parsed.error.formErrors.fieldErrors;
    if (fe.name) errors.name = fe.name[0];
    if (fe.description) errors.description = fe.description[0];
    if (fe.categoryId) errors.categoryId = fe.categoryId[0];
    return false;
  }
  return true;
};

const handleSubmit = async () => {
  if (!validateForm()) return;

  isSubmitting.value = true;
  try {
    const basePayload = {
      name: formData.name,
      description: formData.description,
      categoryId: formData.categoryId
    };
    if (typeof formData.imageUrl === 'string' && formData.imageUrl.trim() !== '') {
      basePayload.imageUrl = formData.imageUrl.trim();
    }

    if (props.groupToEdit) {
      const groupUpdated = await communityService.updateGroup(props.groupToEdit.id, basePayload);
      if (formData.imageUrl instanceof File) {
        await communityService.uploadGroupImage(groupUpdated.id, formData.imageUrl);
      }
      toast.addToast({ type: 'success', message: t('community.groupManagement.success.updated') });
      emit('saved');
    } else {
      const created = await communityService.createGroup(basePayload);
      if (formData.imageUrl instanceof File) {
        await communityService.uploadGroupImage(created.id, formData.imageUrl);
      }
      toast.addToast({ type: 'success', message: t('community.groupManagement.success.created') });
      emit('saved');
    }

    emit('success'); // Notifica que a operação foi bem-sucedida
    emit('close');
  } catch (error) {
    console.error('Error saving group:', error);
    toast.addToast({
      type: 'error',
      message: t('community.groupManagement.error.save')
    });
  } finally {
    isSubmitting.value = false;
  }
};


onMounted(() => {
  loadCategories();
});

// Expose methods for parent component
defineExpose({
  handleSubmit
});
</script>

<style scoped>
</style> 