<template>
  <div class="iluria-textarea-wrapper">
    <IluriaLabel v-if="label" :for="textareaId" class="iluria-textarea-label">
      {{ label }}
    </IluriaLabel>
    
    <div class="textarea-container">
      <textarea
        :id="textareaId"
        :name="name || textareaId"
        v-model="model"
        :disabled="disabled"
        :placeholder="placeholder"
        :required="required"
        :rows="rows"
        :maxlength="maxlength"
        :aria-describedby="ariaDescribedBy"
        class="iluria-textarea"
        @input="handleInput"
      />
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import IluriaLabel from '../IluriaLabel.vue'

const model = defineModel()

const props = defineProps({
  id: {
    type: String,
    default: null
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  rows: {
    type: Number,
    default: 4
  },
  maxlength: {
    type: Number,
    default: null
  },
  ariaDescribedBy: {
    type: String,
    default: ''
  }
})

const emit = defineEmits(['update:modelValue', 'input'])

const textareaId = computed(() => props.id || `iluria-textarea-${Math.random().toString(36).substring(2, 11)}`)

const handleInput = (event) => {
  const value = event.target.value
  model.value = value
  emit('input', value)
}
</script>

<style scoped>
.iluria-textarea-wrapper {
  position: relative;
  width: 100%;
}

.iluria-textarea-label {
  display: block;
  margin-bottom: 0.5rem;
}

.textarea-container {
  position: relative;
  width: 100%;
}

.iluria-textarea {
  width: 100%;
  padding: 0.75rem;
  background-color: var(--iluria-color-input-bg, #ffffff);
  border: 2px solid var(--iluria-color-input-border, #e2e8f0);
  border-radius: 8px;
  color: var(--iluria-color-input-text, #374151);
  font-size: 0.875rem;
  line-height: 1.5;
  resize: none;
  transition: all 0.2s ease;
  min-height: 80px;
}

.iluria-textarea::placeholder {
  color: var(--iluria-color-input-placeholder, #9ca3af);
}

.iluria-textarea:hover:not(:disabled) {
  border-color: var(--iluria-color-input-border-hover, #d1d5db);
}

.iluria-textarea:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus, #3b82f6);
  box-shadow: 0 0 0 3px var(--iluria-color-input-shadow-focus, rgba(59, 130, 246, 0.1));
}

.iluria-textarea:disabled {
  background-color: var(--iluria-color-input-bg-disabled, #f9fafb);
  color: var(--iluria-color-text-muted, #9ca3af);
  cursor: not-allowed;
  opacity: 0.6;
  resize: none;
}

/* Dark mode support */
.theme-dark .iluria-textarea {
  background-color: var(--iluria-color-input-bg);
  border-color: var(--iluria-color-input-border);
  color: var(--iluria-color-input-text);
}

.theme-dark .iluria-textarea::placeholder {
  color: var(--iluria-color-input-placeholder);
}

/* Error state */
.iluria-textarea.error {
  border-color: var(--iluria-color-error, #ef4444);
  background-color: rgba(254, 226, 226, 0.5);
}

.iluria-textarea.error:focus {
  border-color: var(--iluria-color-error, #ef4444);
  box-shadow: 0 0 0 3px rgba(239, 68, 68, 0.1);
}

/* Success state */
.iluria-textarea.success {
  border-color: var(--iluria-color-success, #10b981);
}

.iluria-textarea.success:focus {
  border-color: var(--iluria-color-success, #10b981);
  box-shadow: 0 0 0 3px rgba(16, 185, 129, 0.1);
}

@media (max-width: 768px) {
  .iluria-textarea {
    font-size: 16px; /* Prevents zoom on iOS */
  }
}

/* Accessibility improvements */
@media (prefers-reduced-motion: reduce) {
  .iluria-textarea {
    transition: none;
  }
}

@media (prefers-contrast: high) {
  .iluria-textarea {
    border-width: 3px;
  }
}
</style> 