<template>
  <div>
    <!-- Escopo da Promoção -->
    <div v-if="modelValue.type !== 'CROSS_SELLING'" class="mb-8">
      <div class="mb-4">
        <IluriaLabel>Aplicar a <span class="text-gray-500 text-sm">(opcional)</span></IluriaLabel>
        <div class="mb-4">
          <IluriaSelect
            v-model="modelValue.scope"
            :options="[
              { label: 'Toda a loja', value: 'ALL_STORE' },
              { label: 'Categorias específicas', value: 'CATEGORIES' },
              { label: 'Produtos específicos', value: 'PRODUCTS' }
            ]"
            option-label="label"
            option-value="value"
            name="promotion-scope"
            id="promotion-scope"
            class="w-full flex gap-4"
            @update:modelValue="setScope($event)"
          />
        </div>
      </div>

      <!-- Descrição do escopo -->
      <p v-if="modelValue.scope === 'ALL_STORE'" class="text-gray-600 mt-2">
        A promoção poderá ser usada em todos os produtos de todas as categorias da loja.
      </p>

      <!-- Seletor de categorias -->
      <div v-if="modelValue.scope === 'CATEGORIES'" class="mt-4">
        <ProductCategories
          v-model="selectedCategoryId"
          @update:categoryName="handleCategoryNameUpdate"
        />
      </div>

      <!-- Seletor de produtos -->
      <div v-if="modelValue.scope === 'PRODUCTS'" class="mt-4">
        <div class="flex flex-wrap gap-3 mb-4">
          <div v-for="product in modelValue.products" :key="product.id" 
               class="relative inline-flex items-center px-3 py-2 rounded-md bg-gray-100 border border-gray-200 shadow-sm w-full md:w-auto">
            <!-- Imagem do produto se disponível -->
            <div v-if="product.imageUrl" class="w-10 h-10 mr-3 bg-white rounded-md overflow-hidden flex-shrink-0 border border-gray-200">
              <img :src="product.imageUrl" :alt="product.name || product.productName" class="w-full h-full object-cover" />
            </div>
            <div v-else class="w-10 h-10 mr-3 bg-gray-200 rounded-md flex items-center justify-center flex-shrink-0">
              <svg class="w-5 h-5 text-gray-400" fill="currentColor" viewBox="0 0 20 20">
                <path fill-rule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4V5h12v10z" clip-rule="evenodd" />
              </svg>
            </div>
            <!-- Informações do produto -->
            <div class="flex flex-col flex-grow min-w-0">
              <span class="text-sm font-medium truncate">{{ product.name || product.productName }}</span>
              <span v-if="product.sku" class="text-xs text-gray-500">SKU: {{ product.sku }}</span>
              <span v-if="product.price" class="text-xs text-gray-500">{{ formatPrice(product.price) }}</span>
            </div>
            <!-- Botão de remover -->
            <IluriaButton
              size="small"
              color="ghost"
              :hugeIcon="Cancel01Icon"
              @click="unselectProduct(product.id)"
              class="ml-2"
              :title="'Remover produto'"
            />
          </div>
        </div>
        
        <IluriaButton
          color="primary"
          variant="outline"
          :hugeIcon="PlusSignIcon"
          @click="openProductSelector"
        >
          Selecionar produtos
        </IluriaButton>
        
        <ProductSelectorModal
          :visible="showProductSelector"
          @update:visible="closeProductSelector"
          @select="selectProduct"
        />
      </div>
    </div>

    <!-- Combinar com outras promoções -->
    <div class="mb-8">
      <IluriaLabel size="lg" class="mb-4">Combinar com outras promoções</IluriaLabel>
      
      <div class="flex items-center mb-4">
        <div class="font-medium">Descontos definidos em<br/>outras promoções</div>
        <div class="ml-auto">
          <IluriaRadioGroup
            v-model="localCombinePromos"
            :options="[
              { label: 'São considerados no cálculo do desconto', value: true },
              { label: 'Não são considerados no cálculo do desconto', value: false }
            ]"
            option-label="label"
            option-value="value"
            name="combine-promos"
            id="combine-promos"
            :grid="true"
            :cols="2"
            class="w-full"
          />
        </div>
      </div>
    </div>

    <div class="mb-8">
      <IluriaLabel size="lg" class="mb-4">Período de Vigência da promoção</IluriaLabel>
      <div class="grid grid-cols-2 gap-6">
        <div>
          <IluriaLabel>Data de início</IluriaLabel>
          <input 
            type="date" 
            v-model="localStartDate" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
        </div>
        <div>
          <IluriaLabel>Hora de início (-03)</IluriaLabel>
          <input 
            type="time" 
            v-model="localStartTime" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
        </div>
      </div>

      <div class="mt-4">
        <IluriaToggleSwitch
          id="define-end-date"
          v-model="localDefineEndDate"
          label="Definir data de término"
          wrapperClass="flex items-center gap-2"
        />
      </div>

      <div v-if="localDefineEndDate" class="grid grid-cols-2 gap-6 mt-4">
        <div>
          <IluriaLabel>Data de término</IluriaLabel>
          <input 
            type="date" 
            v-model="localEndDate" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
        </div>
        <div>
          <IluriaLabel>Hora de término (-03)</IluriaLabel>
          <input 
            type="time" 
            v-model="localEndTime" 
            class="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-primary/50"
          />
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import ProductCategories from '@/components/products/ProductCategories.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import ProductSelectorModal from '@/components/products/ProductSelectorModal.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { categoryService } from '@/services/category.service';
import { Cancel01Icon, PlusSignIcon } from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const showProductSelector = ref(false);
const allCategories = ref([]);
const selectedCategoryIds = ref([]);
const selectedCategoryId = ref(null);

onMounted(async () => {
  try {
    allCategories.value = await categoryService.fetchCategories() || [];
  } catch (error) {
    console.error('Failed to fetch categories:', error);
  }
});

onMounted(() => {
  if (!props.modelValue.scope) {
    props.modelValue.scope = 'ALL_STORE';
  }
  
  if (!Array.isArray(props.modelValue.categories)) {
    props.modelValue.categories = [];
  }
  
  if (props.modelValue.categories.length > 0) {
    const firstCategory = props.modelValue.categories[0];
    selectedCategoryId.value = firstCategory.categoryId || firstCategory.id;
  }
  
  selectedCategoryIds.value = props.modelValue.categories
    .filter(cat => cat && (cat.id || cat.categoryId))
    .map(cat => ({
      id: (cat.id || cat.categoryId).toString(),
      categoryId: parseInt(cat.id || cat.categoryId, 10) || 0,
      name: cat.name || cat.categoryName || `Categoria ${cat.id || cat.categoryId}`,
      categoryName: cat.categoryName || cat.name || `Categoria ${cat.id || cat.categoryId}`
    }));

  if (!Array.isArray(props.modelValue.products)) {
    props.modelValue.products = [];
  }
});

function setScope(type) {
  props.modelValue.scope = type;
  
  if (type === 'ALL_STORE') {
    props.modelValue.categories = [];
    props.modelValue.triggerProducts = [];
    selectedCategoryIds.value = [];
    selectedCategoryId.value = null;
  }

  else if (type === 'CATEGORIES') {
    props.modelValue.triggerProducts = [];
  }

  else if (type === 'PRODUCTS') {
    props.modelValue.categories = [];
    selectedCategoryIds.value = [];
    selectedCategoryId.value = null;
  }
  
  emit('update:modelValue', { ...props.modelValue });
}

function findCategoryNameById(categoryId) {
  if (!categoryId) return '';
  
  function searchInCategories(categories, id) {
    if (!categories || !Array.isArray(categories)) return null;
    
    for (const category of categories) {
      if (category.id && category.id.toString() === id.toString()) {
        return category.title || '';
      }
      
      if (category.children && Array.isArray(category.children)) {
        const found = searchInCategories(category.children, id);
        if (found) return found;
      }
    }
    
    return null;
  }
  
  return searchInCategories(allCategories.value, categoryId) || '';
}

function formatCategoriesForSave(categoryIds) {
  if (!Array.isArray(categoryIds) || categoryIds.length === 0) {
    return [];
  }
  
  return categoryIds.map(id => {
    let categoryId;
    
    if (typeof id === 'object' && id !== null) {
      categoryId = id.id || id.categoryId || '';
    } else {
      categoryId = id.toString();
    }
    
    const categoryName = findCategoryNameById(categoryId);
    
    return {
      categoryId: categoryId,
      categoryName: categoryName || ''
    };
  });
}

function openProductSelector() {
  showProductSelector.value = true;
}

function closeProductSelector(value) {
  showProductSelector.value = value;
}

function getProductsByType(type) {
  if (!props.modelValue.products) return [];
  return props.modelValue.products.filter(p => p.type === type);
}

function getTypeLabel(type) {
  const types = {
    'TRIGGER': 'Gatilho',
    'GIFT': 'Brinde',
    'SUGGEST': 'Sugerido'
  };
  return types[type] || type;
}

function selectProduct(products, type = 'TRIGGER') {
  if (!Array.isArray(products)) {
    products = [products];
  }


  if (!Array.isArray(props.modelValue.products)) {
    props.modelValue.products = [];
  }

  products.forEach(product => {
    if (!isProductSelected(product.id, type)) {
      const existingProduct = props.modelValue.products.find(p => 
        (p.id === product.id || p.productId === product.id || p.id === product.productId) &&
        p.type === type
      );

      if (!existingProduct) {
        const productToAdd = {
          ...product,
          id: product.id || product.productId,
          productId: product.productId || product.id,
          name: product.name || product.productName,
          productName: product.productName || product.name,
          sku: product.sku || product.ean || '',
          price: Number(product.price) || 0,
          imageUrl: product.imageUrl || product.image || '',
          position: product.position || 0,
          variation: product.variation || null,
          type: type 
        };
        
        props.modelValue.products.push(productToAdd);
      }
    }
  });
  

  if (props.modelValue.scope !== 'PRODUCTS') {
    props.modelValue.scope = 'PRODUCTS';
  }
  
  emit('update:modelValue', { ...props.modelValue });
}

function isProductSelected(productId, type = 'TRIGGER') {
  if (!Array.isArray(props.modelValue.products)) return false;
  return props.modelValue.products.some(product => 
    (product.id === productId || 
     product.productId === productId || 
     (product.id && product.id.toString() === productId.toString()) ||
     (product.productId && product.productId.toString() === productId.toString())) && 
    product.type === type
  );
}

function unselectProduct(productId, type = 'TRIGGER') {
  if (!Array.isArray(props.modelValue.products)) return;
  
  const index = props.modelValue.products.findIndex(p => 
    (p.id === productId || 
     p.productId === productId ||
     (p.id && p.id.toString() === productId.toString()) ||
     (p.productId && p.productId.toString() === productId.toString())) && 
    p.type === type
  );
  
  if (index !== -1) {
    props.modelValue.products.splice(index, 1);
    emit('update:modelValue', { ...props.modelValue });
  }
}

function unselectCategory(categoryId) {
  if (!categoryId && categoryId !== 0) return;
  
  const idStr = categoryId.toString();
  
  const index = props.modelValue.categories.findIndex(cat => {
    if (!cat) return false;
    
    const catId = cat.id || cat.categoryId;
    return catId && catId.toString() === idStr;
  });
  
  if (index !== -1) {
    props.modelValue.categories.splice(index, 1);
    
    selectedCategoryIds.value = selectedCategoryIds.value.filter(id => {
      const idToCompare = typeof id === 'object' ? (id.id || id.categoryId) : id;
      return idToCompare && idToCompare.toString() !== idStr;
    });
    
    emit('update:modelValue', { ...props.modelValue });
  }
}

function getCategoryDisplayName(category) {
  if (!category) return '';
  
  const name = category.name || category.categoryName || '';
  const id = category.id || category.categoryId || '';
  
  if (name && name.trim() !== '') {
    return name;
  }
  
  if (id) {
    return `Categoria ${id}`;
  }
  
  return 'Categoria sem nome';
}

function formatPrice(price) {
  if (price === null || price === undefined) return 'R$ 0,00';
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(price);
}

watch(() => props.modelValue.categories, (newCategories) => {
  if (!Array.isArray(newCategories)) {
    if (selectedCategoryIds.value.length > 0) {
      selectedCategoryIds.value = [];
    }
    return;
  }
  
  const formattedCategories = newCategories
    .filter(cat => cat && (cat.id || cat.categoryId))
    .map(cat => {
      const categoryId = cat.id || cat.categoryId;
      const realCategoryName = findCategoryNameById(categoryId);
      
      return {
        id: categoryId,
        categoryId: categoryId,
        name: realCategoryName || cat.name || cat.categoryName || '',
        categoryName: realCategoryName || cat.categoryName || cat.name || ''
      };
    });
  
  const currentIds = selectedCategoryIds.value.map(cat => {
    if (typeof cat === 'object') return cat.id || cat.categoryId;
    return cat.toString();
  });
  
  const newIds = formattedCategories.map(cat => cat.id);
  
  if (JSON.stringify(currentIds.sort()) !== JSON.stringify([...new Set(newIds)].sort())) {
    selectedCategoryIds.value = formattedCategories;
  }
}, { immediate: true, deep: true });

watch(selectedCategoryIds, (newIds) => {
  if (!Array.isArray(newIds)) return;
  
  const formattedCategories = formatCategoriesForSave(newIds);
  
  // Verificar se houve mudanças para evitar loops infinitos
  const currentCategories = Array.isArray(props.modelValue.categories) 
    ? props.modelValue.categories 
    : [];
    
  // Normalizar para comparação
  const normalize = (categories) => 
    categories
      .filter(cat => cat && (cat.id || cat.categoryId))
      .map(cat => ({
        id: String(cat.id || cat.categoryId),
        categoryId: String(cat.id || cat.categoryId),
        categoryName: String(cat.categoryName || cat.name || `Categoria ${cat.id || cat.categoryId}`)
      }))
      .sort((a, b) => a.id.localeCompare(b.id));
  
  const currentNormalized = normalize(currentCategories);
  const newNormalized = normalize(formattedCategories);
  
  const hasChanges = JSON.stringify(currentNormalized) !== JSON.stringify(newNormalized);
  
  if (hasChanges) {
    // Atualizar o modelo com as categorias formatadas
    props.modelValue.categories = formattedCategories;
    emit('update:modelValue', { ...props.modelValue });
  }
}, { deep: true });

watch(selectedCategoryId, (newCategoryId) => {
  if (newCategoryId) {
    const categoryName = findCategoryNameById(newCategoryId);
    const categoryData = {
      categoryId: newCategoryId,
      categoryName: categoryName || `Categoria ${newCategoryId}`
    };
    
    props.modelValue.categories = [categoryData];
    emit('update:modelValue', { ...props.modelValue });
  } else {
    props.modelValue.categories = [];
    emit('update:modelValue', { ...props.modelValue });
  }
});

const localCombinePromos = ref(props.modelValue.combinePromos);
const localStartDate = ref(props.modelValue.startDate ? new Date(props.modelValue.startDate).toISOString().slice(0, 10) : new Date().toISOString().slice(0, 10));
const localStartTime = ref(props.modelValue.startTime || '00:00');
const localEndDate = ref(props.modelValue.endDate ? new Date(props.modelValue.endDate).toISOString().slice(0, 10) : new Date().toISOString().slice(0, 10));
const localEndTime = ref(props.modelValue.endTime || '23:59');
const localDefineEndDate = ref(props.modelValue.defineEndDate);

watch(localCombinePromos, (newValue) => {
  props.modelValue.combinePromos = newValue;
  emit('update:modelValue', props.modelValue);
});

watch(localDefineEndDate, (newValue) => {
  props.modelValue.defineEndDate = newValue;
  emit('update:modelValue', props.modelValue);
});

watch([localStartDate, localStartTime, localEndDate, localEndTime], () => {
  const startDate = new Date(`${localStartDate.value}T${localStartTime.value}:00`);
  props.modelValue.startDate = startDate.toISOString();
  props.modelValue.startTime = localStartTime.value;
  
  if (localDefineEndDate.value) {
    const endDate = new Date(`${localEndDate.value}T${localEndTime.value}:00`);
    props.modelValue.endDate = endDate.toISOString();
    props.modelValue.endTime = localEndTime.value;
  } else {
    props.modelValue.endDate = null;
    props.modelValue.endTime = null;
  }
  
  emit('update:modelValue', props.modelValue);
});

function handleCategoryNameUpdate(categoryName) {
  if (selectedCategoryId.value && categoryName) {
    const categoryData = {
      categoryId: selectedCategoryId.value,
      categoryName: categoryName
    };
    
    props.modelValue.categories = [categoryData];
    emit('update:modelValue', { ...props.modelValue });
  }
}
</script>
