import axios from 'axios';
import { API_URLS } from '../config/api.config';

const endpoint = `${API_URLS.CATEGORY_BASE_URL}`;

export const categoryService = {
  async fetchCategories() {
    try {
      const response = await axios.get(`${API_URLS.CATEGORY_BASE_URL}`);

      return response.data;
    } catch (error) {
      throw error;
    }
  },

  async saveCategories({categories, deletedCategories }) {
    const cleanedCategories = this.cleanCategoriesForSave(categories);

    const response = await axios.post(`${API_URLS.CATEGORY_BASE_URL}`, {
      categories: cleanedCategories,
      deletedCategories
    });
    return response.data;
  },

  cleanCategoriesForSave(categories) {
    return categories.map(category => {
      const cleanCategory = {
        id: category.id?.startsWith('menu-') ? null : category.id,
        parentId: category.parentId,
        title: category.title,
        children: category.children ? this.cleanCategoriesForSave(category.children) : []
      };

      return cleanCategory;
    });
  },

  async getSeoData(categoryId) {
    const response = await axios.get(`${API_URLS.CATEGORY_BASE_URL}/info/${categoryId}`);
    return response.data;
  },

  async saveSeoData(categoryId, seoData) {
    const response = await axios.put(`${API_URLS.CATEGORY_BASE_URL}/info/${categoryId}`, seoData);
    return response.data;
  },

uploadImage(categoryId, file, imageType) {
  const formData = new FormData();
  formData.append('file', file);
  
  if (imageType) {
    formData.append('imageType', imageType);
  }

  return axios.post(`${endpoint}/${categoryId}/images`, formData, {
    headers: {
      'Content-Type': 'multipart/form-data'
    }
  })
  .then(response => {
    return response.data;
  })
  .catch(error => {
    throw error;
  });
},

deleteImage(categoryId, imageType) {
  return axios.delete(`${endpoint}/${categoryId}/images/${imageType}`)
    .then(response => {
      return response.data;
    })
    .catch(error => {
      throw error;
    });
},


};
