import { ref, onUnmounted } from 'vue'
import { Client } from '@stomp/stompjs'
import SockJ<PERSON> from 'sockjs-client'
import { useAuthStore } from '@/stores/auth.store'

export function useSitePublishingProgress() {
    const authStore = useAuthStore()
    const isConnected = ref(false)
    const progress = ref({ step: '', message: '', progress: 0, timestamp: 0 })
    const error = ref(null)
    const isPublishing = ref(false)

    let stompClient = null
    let subscription = null

    const connect = () => {
        if (stompClient?.connected) {
            return Promise.resolve()
        }

        return new Promise((resolve, reject) => {
            stompClient = new Client({
                webSocketFactory: () => {
                    return new SockJS('http://localhost:8081/ws/site-publishing')
                },
                reconnectDelay: 5000,
                onConnect: () => {
                    isConnected.value = true
                    resolve()
                },
                onStompError: frame => {
                    reject(new Error(frame.headers.message))
                },
                onWebSocketError: err => {
                    reject(err)
                },
                onWebSocketClose: () => {
                    isConnected.value = false
                }
            })

            stompClient.activate()
        })
    }

    const subscribe = (userEmail) => {
        if (!stompClient?.connected) {
            console.warn('⚠️ Tentativa de inscrição sem conexão WebSocket')
            return
        }

        const destination = `/topic/site-publishing/${userEmail}`

        subscription = stompClient.subscribe(destination, (message) => {
            const data = JSON.parse(message.body)
            progress.value = {
                step: data.step || 'STARTING',
                message: data.message || 'Publicando...',
                progress: data.progress || 0,
                timestamp: data.timestamp || Date.now()
            }

            if (data.step === 'STARTED') {
                isPublishing.value = true
                error.value = null
            } else if (data.step === 'ERROR') {
                isPublishing.value = false
                error.value = data.message
            } else if (data.step === 'COMPLETED') {
                setTimeout(() => {
                    isPublishing.value = false
                }, 3000)
            }
        })

    }

    const startListening = async (userEmail) => {

        // Verificar se o email foi fornecido
        if (!userEmail) {
            error.value = 'Email do usuário não encontrado'
            isPublishing.value = false
            return
        }

        isPublishing.value = true;
        progress.value = {
            step: 'CONNECTING',
            message: 'Conectando ao servidor...',
            progress: 5,
            timestamp: Date.now()
        }

        try {
            await connect()
            subscribe(userEmail)
        } catch (err) {
            console.error('❌ Erro ao iniciar escuta:', err)
            error.value = `Erro: ${err.message || 'desconhecido'}`
            isPublishing.value = false
            setTimeout(() => startListening(userEmail), 3000)
        }
    }

    const disconnect = () => {
        subscription?.unsubscribe()
        stompClient?.deactivate()
        isConnected.value = false
        isPublishing.value = false
    }

    const reset = () => {
        progress.value = { step: '', message: '', progress: 0, timestamp: 0 }
        isPublishing.value = false
        error.value = null
    }

    onUnmounted(() => disconnect())

    return { isConnected, progress, isPublishing, error, startListening, disconnect, reset }
}