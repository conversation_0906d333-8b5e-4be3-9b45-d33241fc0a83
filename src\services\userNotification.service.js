import axios from 'axios'
import { API_URLS } from '@/config/api.config'
import { useAuthStore } from '@/stores/auth.store'

class UserNotificationService {
  constructor() {
    this.baseURL = `${API_URLS.AUTH_USER_BASE_URL}/notification-settings`
  }

  async getUserNotificationSettings() {
    try {
      const response = await axios.get(this.baseURL)
      return response.data
    } catch (error) {
      // Se for erro de autenticação, propaga
      if (error.response?.status === 401 || error.response?.status === 403) {
        throw error
      }

      // Se for 404 (não encontrado), retorna configurações padrão
      if (error.response?.status === 404) {
        return this.getDefaultSettings()
      }

      // Para outros erros, propaga a exceção
      throw error
    }
  }

  async saveUserNotificationSettings(settings) {
    try {
      // Garantir que o email do usuário está incluído
      const authStore = useAuthStore()
      const userEmail = authStore.userEmail || settings.userEmail

      if (!userEmail) {
        throw new Error('Email do usuário não encontrado. Faça login novamente.')
      }

      // Validar e limpar os dados antes de enviar
      const cleanSettings = this.validateAndCleanSettings(settings)

      const settingsWithEmail = {
        ...cleanSettings,
        userEmail: userEmail
      }

      const response = await axios.post(this.baseURL, settingsWithEmail, {
        headers: {
          'Content-Type': 'application/json'
        },
        timeout: 10000
      })

      return response.data
    } catch (error) {
      throw error
    }
  }

  async updateNotificationSetting(settingKey, value) {
    try {
      const currentSettings = await this.getUserNotificationSettings()
      const authStore = useAuthStore()

      const updatedSettings = {
        ...currentSettings,
        [settingKey]: value,
        userEmail: authStore.userEmail
      }

      const result = await this.saveUserNotificationSettings(updatedSettings)
      return result
    } catch (error) {
      throw error
    }
  }

  validateAndCleanSettings(settings) {
    const validFields = [
      // Campos de compatibilidade
      'stores', 'accountAndSafety', 'systemAndUpdates',
      'collabInvites', 'collabUpdates', 'roleChanges', 'doNotDisturb',
      'startTime', 'endTime', 'newsletter', 'specialOffers', 'feedbackSurveys',
      // Novos campos do sistema
      'securityAlerts', 'accountChanges', 'loginNotifications',
      'systemUpdates', 'maintenanceNotifications', 'featureAnnouncements',
      'promotionalEmails', 'productRecommendations',
      'mentions', 'comments', 'followNotifications',
      'weeklySummary', 'usageReports', 'emailFrequency',
      'quietHoursEnabled', 'quietHoursStart', 'quietHoursEnd', 'quietHoursTimezone'
    ]

    const cleanSettings = {}

    for (const [key, value] of Object.entries(settings)) {
      if (validFields.includes(key)) {
        if (typeof value === 'boolean' || typeof value === 'string') {
          cleanSettings[key] = value
        }
      }
    }

    return cleanSettings
  }

  getDefaultSettings() {
    return {
      // Security & Account (Critical - sempre habilitadas)
      securityAlerts: true,
      accountChanges: true,
      loginNotifications: true,
      
      // System & Platform
      systemUpdates: true,
      maintenanceNotifications: true,
      featureAnnouncements: false,
      
      // Marketing & Promotional
      promotionalEmails: false,
      newsletter: false,
      productRecommendations: false,
      
      // Collaboration & Social
      mentions: true,
      comments: true,
      followNotifications: true,
      
      // Activity & Usage
      weeklySummary: false,
      usageReports: false,
      
      // Email Frequency
      emailFrequency: 'IMMEDIATE',
      
      // Do Not Disturb
      quietHoursEnabled: false,
      quietHoursStart: '22:00',
      quietHoursEnd: '08:00',
      quietHoursTimezone: 'America/Sao_Paulo',
      
      // Campos de compatibilidade com frontend existente
      stores: true,
      accountAndSafety: true,
      systemAndUpdates: true,
      collabInvites: true,
      collabUpdates: true,
      roleChanges: true,
      doNotDisturb: false,
      startTime: '22:00',
      endTime: '08:00',
      specialOffers: false,
      feedbackSurveys: false
    }
  }

  /**
   * Busca notificações não lidas do usuário.
   */
  async getUnreadNotifications(page = 1, pageSize = 10) {
    try {
      const response = await axios.get(`${API_URLS.STORE_BASE_URL}/user/notifications`, {
        params: {
          page,
          size: pageSize,
          onlyUnread: true
        }
      })
      return {
        notifications: response.data.content || [],
        unreadCount: response.data.totalUnread || 0,
        hasMore: response.data.hasNext || false,
        totalPages: response.data.totalPages || 1
      }
    } catch (error) {
      console.error('Error fetching user notifications:', error)
      throw error
    }
  }

  /**
   * Marca todas as notificações do usuário como lidas.
   */
  async markAllAsRead() {
    try {
      const response = await axios.post(`${API_URLS.STORE_BASE_URL}/user/notifications/mark-all-read`)
      return response.data
    } catch (error) {
      console.error('Error marking all user notifications as read:', error)
      throw error
    }
  }

  /**
   * Marca uma notificação específica como lida.
   */
  async markAsRead(notificationId) {
    try {
      const response = await axios.post(`${API_URLS.STORE_BASE_URL}/user/notifications/${notificationId}/read`)
      return response.data
    } catch (error) {
      console.error('Error marking user notification as read:', error)
      throw error
    }
  }
}

export default new UserNotificationService()