<template>
  <div>
    <IluriaLabel v-if="label" :for="name || id || ''" labelClass="mb-2 block">
      {{ label }}
    </IluriaLabel>
    
    <div class="w-full">
      <!-- Layout customizado sem InputGroup para evitar linha divisória -->
      <div ref="containerElement" class="relative flex items-center w-full h-[38px] rounded-lg overflow-hidden border-2 transition-colors custom-input-group"
           :class="[
             'border-[var(--iluria-color-input-border)]',
             'focus-within:border-[var(--iluria-color-input-border-focus)] focus-within:shadow-[0_0_0_2px_var(--iluria-color-focus-ring)]'
           ]">
        
        <!-- Prefix -->
        <div v-if="prefix" ref="prefixElement" class="px-4 py-2 flex items-center justify-center h-full min-w-max flex-shrink-0 custom-prefix">
          <span ref="prefixSpan" class="font-medium whitespace-nowrap text-[var(--iluria-color-input-text)]">{{ prefix }}</span>
        </div>

        <!-- Input -->
        <component :is="inputType"
          ref="inputElement"
          :id="id"
          :name="name || id"
          v-model="displayedValue"
          :disabled="disabled"
          :placeholder="placeholder"
          :required="required"
          :unstyled="true"
          class="flex-1 min-w-0 block py-2 text-base sm:text-sm/6 px-3 h-full border-0 bg-transparent text-[var(--iluria-color-input-text)] focus:outline-none focus:ring-0 input-themed-custom"
          inputClass="w-full focus-visible:border-0 focus-visible:outline-0 border-0"
          :class="[inputClass]"
          v-bind="conditionalProps()"
          @keypress="handleKeypress"
        />

        <!-- Suffix -->
        <div v-if="suffix" ref="suffixElement" class="px-3 py-1 flex items-center justify-center h-full min-w-max flex-shrink-0 custom-suffix">
          <span ref="suffixSpan" class="font-medium whitespace-nowrap text-[var(--iluria-color-input-text)]">{{ suffix }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { InputText, InputMask } from 'primevue';
import { computed, ref } from 'vue';
import IluriaLabel from '../IluriaLabel.vue';

const model = defineModel();

// Refs para todos os elementos
const inputElement = ref(null);
const containerElement = ref(null);
const prefixElement = ref(null);
const suffixElement = ref(null);
const prefixSpan = ref(null);
const suffixSpan = ref(null);

const displayedValue = computed({
  get() {
    if (props.type === 'money') {
      return formatMoney(String(model.value || ''));
    }
    return model.value;
  },
  set(newValue) {
    if (props.type !== 'money') {
      model.value = newValue;
      return;
    }

    const digits = (String(newValue || '')).replace(/\D/g, '');

    if (!digits) {
      model.value = '';
      return;
    }

    const integerPartRaw = digits.slice(0, -2) || '0';
    const decimalPart = digits.slice(-2).padStart(2, '0');

    const sanitized = `${integerPartRaw}.${decimalPart}`;
    model.value = sanitized;
  }
});

const props = defineProps({
  id: {
    type: String,
    required: false
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    required: false
  },
  inputClass: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  type: {
    type: String,
    default: 'text'
  },
  prefix: {
    type: String,
    default: ''
  },
  suffix: {
    type: String,
    default: ''
  },
  mask: {
    type: String,
    default: undefined
  }
});

const inputType = computed(() => {
  if (props.type === 'mask' || props.mask) {
    return InputMask;
  } else {
    return InputText;
  }
})

const conditionalProps = () => {
  if (props.type === 'mask' || props.mask) {
    return {
      mask: getMask(),
      unmask: true
    };
  } else {
    return {
      type: props.type
    };
  }
}

const getMask = () => {
  // Se tem máscara customizada definida
  if (props.mask) {
    switch (props.mask) {
      case 'cpf':
        return '999.999.999-99';
      case 'cnpj':
        return '99.999.999/9999-99';
      case 'phone':
        return '(99) 99999-9999';
      case 'cep':
        return '99999-999';
      default:
        return props.mask;
    }
  }
  
  return undefined;
};

// Função para controlar entrada de teclas
const handleKeypress = (event) => {
  if (props.type === 'number' || props.type === 'money') {
    const key = event.key;

    // Permite teclas de controle como Backspace, Delete, setas, etc.
    if (key.length > 1 || event.ctrlKey || event.metaKey) {
      return;
    }
    
    const currentValue = event.target.value || '';

    // Para 'money', permite apenas um separador decimal (vírgula)
    if (props.type === 'money' && key === ',') {
      if (currentValue.includes(',')) {
        event.preventDefault();
      }
      return;
    }

    // Permite apenas dígitos
    if (!/\d/.test(key)) {
      event.preventDefault();
    }
  }
};

const handleInput = (event) => {
  if (props.type !== 'money') {
    model.value = event.target.value;
    return;
  }
  const input = event.target;
  const rawValue = input.value;

  if (props.type !== 'money') {
    model.value = rawValue;
    return;
  }

  // Mantém apenas números e uma vírgula
  let cleaned = rawValue.replace(/[^\d,]/g, '');

  // Garante no máximo uma vírgula
  const commaIndex = cleaned.indexOf(',');
  if (commaIndex !== -1) {
    cleaned =
      cleaned.slice(0, commaIndex + 1) + cleaned.slice(commaIndex + 1).replace(/,/g, '');
  }

  const cursorStart = input.selectionStart;

  // Formata
  const formatted = formatMoney(cleaned);

  // Define no model
  model.value = formatted;

  // Define no input e ajusta cursor
  setTimeout(() => {
    input.value = formatted;

    // Recalcula posição do cursor com base no número de pontos adicionados
    const diff = formatted.length - rawValue.length;
    const newCursor = Math.max(0, Math.min(cursorStart + diff, formatted.length));
    input.setSelectionRange(newCursor, newCursor);
  }, 0);
};

const formatMoney = (value) => {
  // Converte para número, tratando casos de string com vírgula
  const stringValue = String(value || '').replace(',', '.');
  const num = parseFloat(stringValue);

  // Se não for um número válido, retorna string vazia
  if (isNaN(num)) {
    return '';
  }

  // Garante duas casas decimais
  const fixed = num.toFixed(2);
  const parts = fixed.split('.');
  const integerPart = parts[0];
  const decimalPart = parts[1];

  // Adiciona separadores de milhar (.)
  const integerFormatted = integerPart.replace(/\B(?=(\d{3})+(?!\d))/g, '.');

  // Retorna no formato "1.234,56"
  return `${integerFormatted},${decimalPart}`;
};


</script>

<style>
/* Input theming - usando style não-scoped para maior especificidade */
.input-themed,
.input-themed input,
.input-themed .p-inputtext {
  background-color: var(--iluria-color-input-bg) !important;
  border-color: var(--iluria-color-input-border) !important;
  color: var(--iluria-color-input-text) !important;
  transition: background-color 0.3s ease, border-color 0.3s ease, color 0.3s ease !important;
}

.input-themed:focus,
.input-themed input:focus,
.input-themed .p-inputtext:focus {
  border-color: var(--iluria-color-input-border-focus) !important;
  background-color: var(--iluria-color-input-bg) !important;
  outline: none !important;
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
  border-width: 2px !important;
}

.input-themed::placeholder,
.input-themed input::placeholder,
.input-themed .p-inputtext::placeholder {
  color: var(--iluria-color-input-placeholder) !important;
}

/* Prefix/Suffix theming - compatibilidade com componentes antigos */
.input-prefix,
.p-inputgroup .input-prefix {
  background-color: var(--iluria-color-input-bg) !important;
  border-color: var(--iluria-color-input-border) !important;
  transition: background-color 0.3s ease, border-color 0.3s ease !important;
}

.input-prefix-text {
  color: var(--iluria-color-input-text) !important;
  transition: color 0.3s ease !important;
}

/* Estados de erro - compatibilidade com inputs antigos */
.input-error,
.input-error input,
.input-error .p-inputtext {
  border-color: var(--iluria-color-error) !important;
  background-color: rgba(254, 226, 226, 0.5) !important;
}

.input-error .input-prefix {
  border-color: var(--iluria-color-error) !important;
  background-color: rgba(254, 226, 226, 0.5) !important;
}

/* Estados de erro para custom input group - usar fundo do tema */
.custom-input-group.input-error {
  border-color: var(--iluria-color-error) !important;
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
}

/* Prefix e suffix em estado de erro mantêm fundo transparente */
.custom-input-group.input-error .custom-prefix,
.custom-input-group.input-error .custom-suffix {
  background-color: transparent !important;
}

/* ===== ESTILOS PARA INPUTMASK ===== */

/* Garantir que o InputMask se comporte consistentemente */
.input-themed.p-inputmask {
  background-color: var(--iluria-color-input-bg) !important;
  border-color: var(--iluria-color-input-border) !important;
  color: var(--iluria-color-input-text) !important;
}

.input-themed.p-inputmask:focus {
  border-color: var(--iluria-color-input-border-focus) !important;
  background-color: var(--iluria-color-input-bg) !important;
  outline: none !important;
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
  border-width: 2px !important;
}

.input-themed.p-inputmask::placeholder {
  color: var(--iluria-color-input-placeholder) !important;
}

/* Força refresh para elementos que possam ter cache */
.force-refresh * {
  background-color: var(--iluria-color-input-bg) !important;
}

/* ===== ESTILOS PARA CUSTOM INPUT GROUP ===== */

/* Layout customizado com flexbox - usar variáveis do tema */
.custom-input-group {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
  border-color: var(--iluria-color-input-border) !important;
  transition: border-color 0.3s ease, box-shadow 0.3s ease !important;
}

/* Mantém fundo do tema em todos os estados */
.custom-input-group:focus-within,
.custom-input-group:hover,
.custom-input-group.focus,
.custom-input-group:has(input:focus),
.custom-input-group:has(input:not(:placeholder-shown)) {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
}

/* Input customizado integrado - TODOS OS ESTADOS */
.input-themed-custom,
.input-themed-custom:focus,
.input-themed-custom:active,
.input-themed-custom:hover,
.input-themed-custom:not(:placeholder-shown),
.input-themed-custom:placeholder-shown,
.input-themed-custom:valid,
.input-themed-custom:invalid,
.input-themed-custom:disabled,
.input-themed-custom:enabled {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  color: var(--iluria-color-input-text) !important;
  transition: all 0.3s ease !important;
}

.input-themed-custom::placeholder {
  color: var(--iluria-color-input-placeholder) !important;
}

/* Força transparência específica para InputText e InputMask do PrimeVue */
.custom-input-group .input-themed-custom.p-inputtext,
.custom-input-group .input-themed-custom.p-inputtext:focus,
.custom-input-group .input-themed-custom.p-inputtext:active,
.custom-input-group .input-themed-custom.p-inputtext:hover,
.custom-input-group .input-themed-custom.p-inputmask,
.custom-input-group .input-themed-custom.p-inputmask:focus,
.custom-input-group .input-themed-custom.p-inputmask:active,
.custom-input-group .input-themed-custom.p-inputmask:hover {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
}

/* Prefix e Suffix customizados - fundo completamente transparente */
.custom-input-group .custom-prefix,
.custom-input-group .custom-suffix {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
  color: var(--iluria-color-input-text) !important;
}

/* Força transparência nos elementos filhos */
.custom-input-group .custom-prefix *,
.custom-input-group .custom-suffix * {
  background: none !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* ===== ELIMINAÇÃO TOTAL DE QUALQUER COR FRACA ===== */

/* Remove qualquer herança de cor, gradientes ou sombras sutis */
.custom-input-group .custom-prefix,
.custom-input-group .custom-suffix,
.custom-input-group .custom-prefix::before,
.custom-input-group .custom-prefix::after,
.custom-input-group .custom-suffix::before,
.custom-input-group .custom-suffix::after {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  background-attachment: inherit !important;
  background-blend-mode: normal !important;
  background-clip: border-box !important;
  background-origin: padding-box !important;
  background-position: 0% 0% !important;
  background-repeat: repeat !important;
  background-size: auto auto !important;
  box-shadow: none !important;
  text-shadow: none !important;
  filter: none !important;
  opacity: 1 !important;
  mix-blend-mode: normal !important;
}

/* Remove qualquer cor herdada de elementos pai */
.custom-input-group .custom-prefix span,
.custom-input-group .custom-suffix span {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  inherit: none !important;
}

/* ===== FORÇA TRANSPARÊNCIA EM TODOS OS ESTADOS POSSÍVEIS ===== */

/* Estados: focus, hover, active, com valor, sem valor */
.custom-input-group:focus-within .custom-prefix,
.custom-input-group:focus-within .custom-suffix,
.custom-input-group:hover .custom-prefix,
.custom-input-group:hover .custom-suffix,
.custom-input-group.focus .custom-prefix,
.custom-input-group.focus .custom-suffix,
.custom-input-group.active .custom-prefix,
.custom-input-group.active .custom-suffix,
.custom-input-group .custom-prefix:focus,
.custom-input-group .custom-suffix:focus,
.custom-input-group .custom-prefix:hover,
.custom-input-group .custom-suffix:hover,
.custom-input-group .custom-prefix:active,
.custom-input-group .custom-suffix:active {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Estados específicos do input que podem afetar prefix/suffix */
.custom-input-group .input-themed-custom:focus ~ .custom-prefix,
.custom-input-group .input-themed-custom:focus ~ .custom-suffix,
.custom-input-group .input-themed-custom:active ~ .custom-prefix,
.custom-input-group .input-themed-custom:active ~ .custom-suffix,
.custom-input-group .input-themed-custom:hover ~ .custom-prefix,
.custom-input-group .input-themed-custom:hover ~ .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* Força transparência quando input tem valor */
.custom-input-group .input-themed-custom:not(:placeholder-shown) ~ .custom-prefix,
.custom-input-group .input-themed-custom:not(:placeholder-shown) ~ .custom-suffix,
.custom-input-group:has(.input-themed-custom:not(:placeholder-shown)) .custom-prefix,
.custom-input-group:has(.input-themed-custom:not(:placeholder-shown)) .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* Força transparência em todos os elementos filhos em qualquer estado */
.custom-input-group:focus-within .custom-prefix *,
.custom-input-group:focus-within .custom-suffix *,
.custom-input-group:hover .custom-prefix *,
.custom-input-group:hover .custom-suffix *,
.custom-input-group .custom-prefix:focus *,
.custom-input-group .custom-suffix:focus *,
.custom-input-group .custom-prefix:hover *,
.custom-input-group .custom-suffix:hover *,
.custom-input-group .custom-prefix:active *,
.custom-input-group .custom-suffix:active * {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
}

/* ===== PREVINE DIVISÃO DURANTE A DIGITAÇÃO ===== */

/* Garante transparência durante eventos de input */
.custom-input-group .custom-prefix,
.custom-input-group .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Força transparência durante qualquer evento ou estado */
.custom-input-group .custom-prefix,
.custom-input-group .custom-suffix,
.custom-input-group .custom-prefix *,
.custom-input-group .custom-suffix * {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border-left: none !important;
  border-right: none !important;
  border-top: none !important;
  border-bottom: none !important;
  border: none !important;
  outline: none !important;
  box-shadow: none !important;
  text-shadow: none !important;
}

/* Previne qualquer mudança visual durante input */
.custom-input-group:has(input:focus) .custom-prefix,
.custom-input-group:has(input:focus) .custom-suffix,
.custom-input-group:has(input:not(:placeholder-shown)) .custom-prefix,
.custom-input-group:has(input:not(:placeholder-shown)) .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
}

/* ===== FORÇA ESPECÍFICA PARA CAMPOS NUMÉRICOS COM VALORES ===== */

/* Previne divisão em campos type="number" com valores */
.custom-input-group .input-themed-custom[type="number"] ~ .custom-prefix,
.custom-input-group .input-themed-custom[type="number"] ~ .custom-suffix,
.custom-input-group:has(.input-themed-custom[type="number"]) .custom-prefix,
.custom-input-group:has(.input-themed-custom[type="number"]) .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Estados específicos para InputText number do PrimeVue */
.custom-input-group .p-inputtext[type="number"] ~ .custom-prefix,
.custom-input-group .p-inputtext[type="number"] ~ .custom-suffix,
.custom-input-group:has(.p-inputtext[type="number"]) .custom-prefix,
.custom-input-group:has(.p-inputtext[type="number"]) .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Força transparência quando campos numéricos têm valores válidos */
.custom-input-group .input-themed-custom[type="number"]:valid ~ .custom-prefix,
.custom-input-group .input-themed-custom[type="number"]:valid ~ .custom-suffix,
.custom-input-group .p-inputtext[type="number"]:valid ~ .custom-prefix,
.custom-input-group .p-inputtext[type="number"]:valid ~ .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
}

/* ===== REGRA FINAL ULTRA-AGRESSIVA ===== */

/* Força transparência SEMPRE, independente de qualquer estado */
.custom-input-group .custom-prefix,
.custom-input-group .custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
  position: relative !important;
}

/* Sobrescreve qualquer regra do PrimeVue ou externa */
.custom-input-group .custom-prefix::before,
.custom-input-group .custom-prefix::after,
.custom-input-group .custom-suffix::before,
.custom-input-group .custom-suffix::after {
  display: none !important;
  content: none !important;
  background: none !important;
  border: none !important;
}

/* Força transparência com especificidade máxima */
div.custom-input-group div.custom-prefix,
div.custom-input-group div.custom-suffix {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* Força transparência nos spans internos */
.custom-input-group .custom-prefix span,
.custom-input-group .custom-suffix span {
  background: transparent !important;
  background-color: transparent !important;
  background-image: none !important;
  border: none !important;
  box-shadow: none !important;
}

/* ===== ESPECIFICIDADE ABSOLUTA MÁXIMA ===== */

/* Força fundo do tema com especificidade máxima */
div.custom-input-group[style*="background"] {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
}

/* Força fundo do tema em qualquer estado possível */
div.custom-input-group,
div.custom-input-group:focus,
div.custom-input-group:hover,
div.custom-input-group:active,
div.custom-input-group:focus-within,
div.custom-input-group.input-error,
div.custom-input-group.focus,
div.custom-input-group[class*="focus"],
div.custom-input-group[class*="error"] {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
  background-image: none !important;
}

/* ===== PREVINE MUDANÇAS DE COR NO AUTOFILL ===== */

/* Força o input a manter a cor do tema mesmo com autofill */
.input-themed-custom:-webkit-autofill,
.input-themed-custom:-webkit-autofill:focus,
.input-themed-custom:-webkit-autofill:hover {
  -webkit-box-shadow: 0 0 0 1000px var(--iluria-color-input-bg) inset !important;
  -webkit-text-fill-color: var(--iluria-color-input-text) !important;
  transition: background-color 5000s ease-in-out 0s !important;
}

/* Para outros navegadores */
.input-themed-custom:autofill {
  background-color: var(--iluria-color-input-bg) !important;
  color: var(--iluria-color-input-text) !important;
}

/* Força o container a manter a cor do tema */
.custom-input-group:has(.input-themed-custom:-webkit-autofill) {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
}

/* Garante uso das variáveis do tema */
.custom-input-group {
  background-color: var(--iluria-color-input-bg) !important;
  background: var(--iluria-color-input-bg) !important;
  background-image: none !important;
}

/* Remove qualquer herança de cor de tema */
</style>