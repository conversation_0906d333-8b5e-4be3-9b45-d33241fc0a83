import { useGlobalPermissions } from '@/composables/usePermissions';

/**
 * Diretiva personalizada para controlar visibilidade/estado de elementos baseado em permissões
 * 
 * Exemplos de uso:
 * 
 * <!-- Oculta elemento se não tem permissão -->
 * <button v-permission="'product.create'"><PERSON><PERSON><PERSON>du<PERSON></button>
 * 
 * <!-- <PERSON><PERSON><PERSON> de permiss<PERSON> (AND por padrão) -->
 * <div v-permission="['product.edit', 'product.view']">Editar Produto</div>
 * 
 * <!-- Operação OR -->
 * <div v-permission:any="['order.view', 'customer.view']">Dashboard</div>
 * 
 * <!-- Desabilita ao invés de ocultar -->
 * <button v-permission:disable="'product.delete'">Excluir</button>
 * 
 * <!-- Combina modificadores -->
 * <button v-permission:any.disable="['order.edit', 'order.cancel']">Ações do Pedido</button>
 */

/**
 * Aplica o comportamento da diretiva no elemento
 */
function applyPermissionBehavior(el, binding, permissions) {
  const { hasPermission, hasAllPermissions, hasAnyPermission, permissionsLoaded } = permissions;
  
  // Se ainda não carregou permissões, mantém elemento oculto/desabilitado por segurança
  if (!permissionsLoaded.value) {
    if (binding.arg === 'disable') {
      el.disabled = true;
    } else {
      el.style.display = 'none';
    }
    return;
  }
  
  // Normaliza permissões para array
  const requiredPermissions = Array.isArray(binding.value) ? binding.value : [binding.value];
  
  // Se não há permissões especificadas, permite acesso
  if (!binding.value || requiredPermissions.length === 0) {
    if (binding.arg === 'disable') {
      el.disabled = false;
    } else {
      el.style.display = '';
    }
    return;
  }
  
  // Verifica permissões
  let hasAccess = false;
  
  if (binding.arg === 'any') {
    hasAccess = hasAnyPermission(requiredPermissions);
  } else {
    hasAccess = hasAllPermissions(requiredPermissions);
  }
  
  // Aplica comportamento baseado no resultado
  if (binding.arg === 'disable' || binding.modifiers.disable) {
    // Desabilita elemento ao invés de ocultar
    el.disabled = !hasAccess;
    
    // Opcionalmente adiciona classe visual para elementos desabilitados
    if (!hasAccess) {
      el.classList.add('opacity-50', 'cursor-not-allowed');
    } else {
      el.classList.remove('opacity-50', 'cursor-not-allowed');
    }
  } else {
    // Comportamento padrão: oculta elemento
    if (hasAccess) {
      el.style.display = '';
    } else {
      el.style.display = 'none';
    }
  }
}

/**
 * Definição da diretiva v-permission
 */
export const permissionDirective = {
  name: 'permission',
  
  /**
   * Chamado quando o elemento é inserido no DOM
   */
  mounted(el, binding) {
    const permissions = useGlobalPermissions();
    
    // Armazena referência das permissões no elemento para uso posterior
    el._permissionsInstance = permissions;
    
    // Aplica comportamento inicial
    applyPermissionBehavior(el, binding, permissions);
    
    // Observa mudanças nas permissões para reativar
    const unwatch = permissions.permissionsLoaded.value
      ? null
      : permissions.$watch?.(() => permissions.permissionsLoaded.value, () => {
          applyPermissionBehavior(el, binding, permissions);
        });
    
    el._permissionUnwatch = unwatch;
  },
  
  /**
   * Chamado quando a diretiva é atualizada
   */
  updated(el, binding) {
    const permissions = el._permissionsInstance || useGlobalPermissions();
    applyPermissionBehavior(el, binding, permissions);
  },
  
  /**
   * Chamado quando o elemento é removido do DOM
   */
  unmounted(el) {
    // Limpa observadores
    if (el._permissionUnwatch) {
      el._permissionUnwatch();
    }
    delete el._permissionsInstance;
    delete el._permissionUnwatch;
  }
};

/**
 * Plugin para registrar a diretiva globalmente
 */
export default {
  install(app) {
    app.directive('permission', permissionDirective);
  }
};