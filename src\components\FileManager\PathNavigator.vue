<template>
  <div class="flex items-center flex-wrap bg-gray-100 rounded-lg px-3 py-2 w-full overflow-x-auto">
    <button 
      @click="navigateTo(-1)"
      class="flex items-center justify-center rounded-md hover:bg-gray-200 w-8 h-8 mr-1 transition-colors focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
      title="Home"
    >
      <HugeiconsIcon :icon= "Home05Icon" size="22" />
    </button>
    
    <div class="flex items-center text-sm">
      <template v-if="!folderSegments || folderSegments.length === 0">
        <span class="px-2 py-1 rounded-md bg-gray-200 text-gray-700">
          /
        </span>
      </template>
      
      <template v-else>
        <template v-for="(segment, index) in folderSegments" :key="index">
          <span class="text-gray-400 mx-1">/</span>
          <button
            @click="navigateTo(index)"
            class="px-2 py-1 text-sm font-medium text-gray-700 hover:bg-gray-200 rounded-md transition-colors"
            :class="{
              'text-blue-600': index === folderSegments.length - 1,
              'hover:bg-gray-100': index !== folderSegments.length - 1
            }"
          >
            {{ segment.name || segment }}
          </button>
        </template>
      </template>
    </div>
  </div>
</template>
<script setup>
import {computed} from 'vue';
import { Home05Icon } from '@hugeicons-pro/core-bulk-rounded';
import {HugeiconsIcon} from '@hugeicons/vue';

const props = defineProps({
  path: String,
  segments: Array,
  environmentColor: String
});

const emit = defineEmits(['navigate']);

const folderSegments = computed(() => {
  if (!props.segments || !Array.isArray(props.segments)) return [];
  
  const filtered = [];
  let lastFolderIndex = -1;
  
  props.segments.forEach((segment, index) => {
    if (!segment.type || segment.type === 'FOLDER') {
      filtered.push({
        ...segment,
        originalIndex: index
      });
      lastFolderIndex = index;
    } else {
      filtered.push({
        ...segment,
        originalIndex: lastFolderIndex >= 0 ? lastFolderIndex : index,
        isFile: true
      });
    }
  });
  
  return filtered;
});

const navigateTo = (index) => {
  if (index === -1) {
    emit('navigate', '/');
    return;
  }

  const segment = folderSegments.value[index];
  if (!segment) return;
  
  const targetIndex = segment.isFile ? segment.originalIndex : index;
  
  let lastFolderIndex = targetIndex;
  for (let i = targetIndex; i >= 0; i--) {
    const s = folderSegments.value[i];
    if (s && (!s.type || s.type === 'FOLDER')) {
      lastFolderIndex = i;
      break;
    }
  }
  
  const pathSegments = [];
  for (let i = 0; i <= lastFolderIndex; i++) {
    const s = props.segments[i];
    if (s && (!s.type || s.type === 'FOLDER')) {
      pathSegments.push(s.name || s);
    }
  }
  
  const path = '/' + pathSegments.join('/');
  emit('navigate', path);
};
</script>

<style scoped>
::-webkit-scrollbar {
  height: 6px;
}

::-webkit-scrollbar-track {
  background: #f1f1f1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb {
  background: #c1c1c1;
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: #a8a8a8;
}
</style>
