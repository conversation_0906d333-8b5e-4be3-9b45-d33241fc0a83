<template>
  <div class="customer-import-container">
    <!-- Header com ações -->
    <IluriaHeader
      :title="t('customer.import.title')"
      :subtitle="t('customer.import.subtitle')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="step === 2"
      :saveText="t('customer.import.startImport')"
      :saveIcon="Upload01Icon"
      @cancel-click="goBack"
      @save-click="openConfirm"
    />

    <!-- Tabs Navigation -->
    <div class="tabs-container">
      <div class="tabs-nav">
        <IluriaButton
          v-for="tab in tabs"
          :key="tab.id"
          :class="['tab-button', { active: activeTab === tab.id }]"
          :color="activeTab === tab.id ? 'primary' : 'ghost'"
          :variant="activeTab === tab.id ? 'solid' : 'ghost'"
          :hugeIcon="tab.icon"
          size="medium"
          @click="activeTab = tab.id"
        >
          {{ t(tab.label) }}
        </IluriaButton>
      </div>
    </div>

    <!-- Tab Content: Import Process -->
    <div v-if="activeTab === 'import'" class="tab-content">
      <!-- Step 1: File Selection -->
      <div v-if="step === 1" class="import-form-container">
      <ViewContainer
        :title="t('customer.import.fileSelection')"
        :subtitle="t('customer.import.fileSelectionDescription')"
        :icon="File01Icon"
        icon-color="blue"
      >
        <div class="space-y-6">
          <!-- File Upload Area -->
          <div class="file-upload-area" :class="{ 'has-file': selectedFile }">
            <input
              ref="fileInput"
              type="file"
              accept=".csv"
              @change="handleFileSelect"
              class="hidden"
            />

            <div v-if="!selectedFile" class="upload-prompt">
              <HugeiconsIcon :icon="CloudUploadIcon" :size="48" class="upload-icon" />
              <h3 class="upload-title">{{ t('customer.import.selectFile') }}</h3>
              <p class="upload-description">{{ t('customer.import.selectFileDescription') }}</p>
              <IluriaButton color="primary" @click="$refs.fileInput.click()">
                {{ t('customer.import.chooseFile') }}
              </IluriaButton>
            </div>

            <div v-else class="file-selected">
              <div class="file-info">
                <HugeiconsIcon :icon="File01Icon" :size="32" class="file-icon" />
                <div class="file-details">
                  <h4 class="file-name">{{ selectedFile.name }}</h4>
                  <p class="file-size">{{ formatFileSize(selectedFile.size) }}</p>
                </div>
              </div>
              <IluriaButton color="outline" @click="removeFile">
                {{ t('customer.import.removeFile') }}
              </IluriaButton>
            </div>
          </div>

          <!-- File Validation Errors -->
          <div v-if="fileErrors.length > 0" class="error-list">
            <div v-for="error in fileErrors" :key="error" class="error-item">
              <HugeiconsIcon :icon="AlertCircleIcon" :size="16" />
              <span>{{ error }}</span>
            </div>
          </div>

          <!-- Next Button -->
          <div class="step-actions">
            <IluriaButton
              color="primary"
              :disabled="!selectedFile || fileErrors.length > 0"
              :loading="parsingHeaders"
              @click="parseFileHeaders"
            >
              {{ t('customer.import.nextStep') }}
            </IluriaButton>
          </div>
        </div>
      </ViewContainer>
    </div>

    <!-- Step 2: Field Mapping -->
    <div v-if="step === 2" class="import-form-container">
      <ViewContainer
        :title="t('customer.import.fieldMapping')"
        :subtitle="t('customer.import.fieldMappingDescription')"
        :icon="CheckmarkSquare03Icon"
        icon-color="purple"
      >
        <!-- Loading State -->
        <div v-if="!availableFields.basic.length && !availableFields.addresses.length" class="loading-fields">
          <div class="loading-spinner"></div>
          <span>Carregando campos disponíveis...</span>
        </div>
        <div class="space-y-6">
          <!-- Status Summary -->
          <div v-if="mappedFieldsCount > 0 || csvHeaders.length > validCsvHeaders.length" class="mapping-summary">
            <div v-if="mappedFieldsCount > 0" class="summary-item success">
              <HugeiconsIcon :icon="Tick01Icon" :size="16" />
              <span>{{ mappedFieldsCount }}/{{ validCsvHeaders.length }} campos mapeados</span>
            </div>
            <div v-if="csvHeaders.length > validCsvHeaders.length" class="summary-item info">
              <HugeiconsIcon :icon="InformationCircleIcon" :size="16" />
              <span>{{ csvHeaders.length - validCsvHeaders.length }} campos sem dados válidos foram ocultados</span>
            </div>
          </div>

          <!-- CSV Preview -->
          <div class="csv-preview">
            <div class="preview-header">
              <h4 class="preview-title">{{ t('customer.import.csvPreview') }}</h4>
              <div class="preview-info">
                <HugeiconsIcon :icon="AttachmentIcon" :size="16" />
                <span>{{ csvAnalysis.columnCount }} {{ t('customer.import.columns') }} • {{ csvPreviewRows.length }} linhas</span>
              </div>
            </div>
            <div class="preview-table-wrapper">
              <IluriaDataTable
                :value="paginatedPreviewData"
                :columns="previewColumns"
                :loading="parsingHeaders"
                :show-header="true"
                class="preview-datatable"
              >
                <!-- Template para células com texto longo -->
                <template v-for="header in csvHeaders" :key="header" #[`column-${header}`]="{ data }">
                  <div
                    class="cell-content"
                    :title="data[header] && String(data[header]).length > 30 ? data[header] : ''"
                  >
                    {{ data[header] || '-' }}
                  </div>
                </template>
              </IluriaDataTable>
            </div>
            <div v-if="csvPreviewRows.length > pageSize" class="preview-pagination-wrapper">
              <IluriaPagination
                :current-page="currentPage"
                :total-pages="totalPages"
                @go-to-page="handlePageChange"
                class="preview-pagination"
              />
            </div>
          </div>

          <!-- Field Mapping -->
          <div class="field-mapping">
            <div class="mapping-header">
              <h4 class="mapping-title">{{ t('customer.import.mapFields') }}</h4>
              <div class="mapping-status-badge">
                <span class="status-text">{{ mappedFieldsCount }}/{{ validCsvHeaders.length }} {{ t('customer.import.fieldsMapped') }}</span>
              </div>
            </div>

            <div class="mapping-list">
              <div v-for="header in validCsvHeaders" :key="header" class="mapping-item">
                <div class="csv-field">
                  <div class="field-icon-wrapper">
                    <HugeiconsIcon :icon="getFieldIcon('csv')" :size="20" class="field-icon" />
                  </div>
                  <div class="field-info">
                    <span class="field-name">{{ header }}</span>
                    <span v-if="isDetectedAsCompleteAddress(header)" class="address-badge">
                      <HugeiconsIcon :icon="Home04Icon" :size="12" />
                      {{ t('customer.import.completeAddresses') }}
                    </span>
                  </div>
                </div>

                <div class="mapping-arrow">
                  <HugeiconsIcon :icon="ArrowRight01Icon" :size="18" />
                </div>

                <div class="system-field">
                  <div class="select-wrapper">
                    <div class="select-icon-wrapper">
                      <HugeiconsIcon
                        :icon="getFieldIcon(fieldMapping[header])"
                        :size="20"
                        class="select-icon"
                      />
                    </div>
                    <IluriaSelect
                      v-model="fieldMapping[header]"
                      :options="getFieldOptions(header)"
                      :loading="!availableFields.basic.length && !availableFields.addresses.length"
                      :placeholder="isIgnoredField(header) ? t('customer.import.fieldIgnored') : t('customer.import.selectField')"
                      class="field-select"
                      :disabled="!hasValidData(header) || isIgnoredField(header)"
                      appendTo="body"
                    />
                  </div>
                  <div class="mapped-check-wrapper" :class="{
                    'has-error': !hasValidData(header) && !isIgnoredField(header),
                    'is-ignored': isIgnoredField(header)
                  }">
                    <HugeiconsIcon
                      v-if="hasValidData(header) && fieldMapping[header] && !isIgnoredField(header)"
                      :icon="Tick01Icon"
                      :size="20"
                      class="mapped-check success"
                    />
                    <HugeiconsIcon
                      v-else-if="isIgnoredField(header)"
                      :icon="InformationCircleIcon"
                      :size="20"
                      class="mapped-check ignored"
                    />
                    <HugeiconsIcon
                      v-else-if="!hasValidData(header)"
                      :icon="Cancel01Icon"
                      :size="20"
                      class="mapped-check error"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>




          <!-- Informações sobre Mapeamento Inteligente -->
          <div class="info-section">
            <div class="info-header">
              <HugeiconsIcon :icon="InformationCircleIcon" :size="20" />
              <h3 class="info-title">{{ t('customer.import.intelligentAddressMapping') }}</h3>
            </div>
            <div class="info-grid">
              <div class="info-card complete-address">
                <div class="info-card-header">
                  <HugeiconsIcon :icon="Home04Icon" :size="18" />
                  <h4 class="info-card-title">{{ t('customer.import.completeAddresses') }}</h4>
                </div>
                <p class="info-card-text">
                  {{ t('customer.import.completeAddressesDescription') }}
                  <br><strong>{{ t('customer.import.completeAddressesExample') }}</strong>
                </p>
              </div>

              <div class="info-card separate-fields">
                <div class="info-card-header">
                  <HugeiconsIcon :icon="Location01Icon" :size="18" />
                  <h4 class="info-card-title">{{ t('customer.import.separateFields') }}</h4>
                </div>
                <p class="info-card-text">
                  {{ t('customer.import.separateFieldsDescription') }}
                </p>
              </div>
            </div>
          </div>

          <!-- Step Actions -->
          <div class="step-actions">
            <IluriaButton color="outline" @click="goBackToFileSelection">
              {{ t('customer.import.backToFile') }}
            </IluriaButton>
          </div>
        </div>
      </ViewContainer>

      <!-- Preview dos Dados Mapeados -->
      <ViewContainer
        v-if="mappedFieldsCount > 0"
        :title="t('customer.import.mappingPreview')"
        :subtitle="t('customer.import.howWillBeSaved')"
        :icon="Database01Icon"
        icon-color="green"
      >
        <template #header-actions>
          <div class="mapping-stats">
            <span class="stat-badge success">
              <HugeiconsIcon :icon="CheckmarkCircle01Icon" :size="14" />
              {{ mappedFieldsCount }} campos mapeados
            </span>
            <span class="stat-badge info">
              <HugeiconsIcon :icon="ViewIcon" :size="14" />
              {{ Math.min(paginatedPreviewData.length, 3) }} {{ t('customer.import.exampleRecords') }}
            </span>
          </div>
        </template>

        <!-- Preview Unificado -->
        <div class="unified-preview-container">
          <div class="preview-cards">
            <div v-for="(row, index) in paginatedPreviewData.slice(0, 3)" :key="index" class="customer-preview-card">
              <div class="card-header">
                <HugeiconsIcon :icon="UserIcon" :size="18" />
                <span class="card-title">{{ t('customer.import.customer') }} {{ index + 1 }}</span>
              </div>

              <div class="card-content">
                <!-- Dados Básicos -->
                <div v-if="hasBasicMappings" class="data-section">
                  <div class="section-header">
                    <HugeiconsIcon :icon="UserIcon" :size="16" />
                    <h6 class="section-title">{{ t('customer.import.customerData') }}</h6>
                  </div>
                  <div class="field-grid">
                    <div v-for="field in basicFields" :key="field.value" class="field-item">
                      <div v-if="getPreviewValue(row, field.value)" class="field-row">
                        <div class="field-label">
                          <HugeiconsIcon :icon="getFieldIcon(field.value)" :size="14" />
                          {{ field.label }}:
                        </div>
                        <div class="field-value">{{ getPreviewValue(row, field.value) }}</div>
                      </div>
                    </div>
                  </div>
                </div>

                <!-- Dados de Endereço -->
                <div v-if="hasAddressMappings" class="data-section">
                  <div class="section-header">
                    <HugeiconsIcon :icon="Home04Icon" :size="16" />
                    <h6 class="section-title">{{ t('customer.import.customerAddresses') }}</h6>
                  </div>
                  <div class="field-grid">
                    <div v-for="field in addressFields" :key="field.value" class="field-item">
                      <div class="field-row">
                        <div class="field-label">
                          <HugeiconsIcon :icon="getFieldIcon(field.value)" :size="14" />
                          {{ field.label }}:
                        </div>
                        <div class="field-value">
                          {{ getPreviewValue(row, field.value) || '-' }}
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </ViewContainer>
    </div>
    </div>

    <!-- Tab Content: Guide -->
    <div v-if="activeTab === 'guide'" class="tab-content">
      <div class="guide-container">
        <!-- Preparação do CSV -->
        <ViewContainer
          :title="t('customer.import.guide.csvPreparation')"
          :subtitle="t('customer.import.guide.csvPreparationDescription')"
          :icon="FileEditIcon"
          icon-color="blue"
        >
          <div class="guide-section">
            <div class="guide-cards">
              <div class="guide-card">
                <div class="guide-card-header">
                  <HugeiconsIcon :icon="Table01Icon" :size="20" />
                  <h4>{{ t('customer.import.guide.csvStructure') }}</h4>
                </div>
                <div class="guide-card-content">
                  <p>{{ t('customer.import.guide.csvStructureDescription') }}</p>
                  <div class="code-example">
                    <pre>name,email,phone,document
João Silva,<EMAIL>,11999999999,12345678900
Maria Santos,<EMAIL>,11888888888,98765432100</pre>
                  </div>
                </div>
              </div>

              <div class="guide-card">
                <div class="guide-card-header">
                  <HugeiconsIcon :icon="CheckmarkCircle01Icon" :size="20" />
                  <h4>{{ t('customer.import.guide.bestPractices') }}</h4>
                </div>
                <div class="guide-card-content">
                  <ul class="guide-list">
                    <li>{{ t('customer.import.guide.practice1') }}</li>
                    <li>{{ t('customer.import.guide.practice2') }}</li>
                    <li>{{ t('customer.import.guide.practice3') }}</li>
                    <li>{{ t('customer.import.guide.practice4') }}</li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </ViewContainer>

        <!-- Mapeamento de Campos -->
        <ViewContainer
          :title="t('customer.import.guide.fieldMapping')"
          :subtitle="t('customer.import.guide.fieldMappingDescription')"
          :icon="ConnectIcon"
          icon-color="purple"
        >
          <div class="guide-section">
            <div class="mapping-examples">
              <div class="mapping-example">
                <div class="example-header">
                  <HugeiconsIcon :icon="UserIcon" :size="18" />
                  <h5>{{ t('customer.import.guide.customerFields') }}</h5>
                </div>
                <div class="field-examples">
                  <div class="field-example">
                    <span class="csv-example">nome</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Nome Completo</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">email</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">E-mail</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">telefone</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Telefone</span>
                  </div>
                </div>
              </div>

              <div class="mapping-example">
                <div class="example-header">
                  <HugeiconsIcon :icon="Home04Icon" :size="18" />
                  <h5>{{ t('customer.import.guide.addressFields') }}</h5>
                </div>
                <div class="field-examples">
                  <div class="field-example">
                    <span class="csv-example">endereco_completo</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Endereço Completo (com parsing automático)</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">rua</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Rua</span>
                  </div>
                  <div class="field-example">
                    <span class="csv-example">cidade</span>
                    <HugeiconsIcon :icon="ArrowRight01Icon" :size="14" />
                    <span class="system-example">Cidade</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </ViewContainer>

        <!-- Dicas Avançadas -->
        <ViewContainer
          :title="t('customer.import.guide.advancedTips')"
          :subtitle="t('customer.import.guide.advancedTipsDescription')"
          :icon="Idea01Icon"
          icon-color="green"
        >
          <div class="guide-section">
            <div class="tips-grid">
              <div class="tip-card">
                <div class="tip-icon">
                  <HugeiconsIcon :icon="Home04Icon" :size="24" />
                </div>
                <div class="tip-content">
                  <h6>{{ t('customer.import.guide.addressTip') }}</h6>
                  <p>{{ t('customer.import.guide.addressTipDescription') }}</p>
                  <div class="tip-example">
                    "Rua das Flores, 123 - Centro, São Paulo - SP, 01001-000"
                  </div>
                </div>
              </div>

              <div class="tip-card">
                <div class="tip-icon">
                  <HugeiconsIcon :icon="Database01Icon" :size="24" />
                </div>
                <div class="tip-content">
                  <h6>{{ t('customer.import.guide.dataTip') }}</h6>
                  <p>{{ t('customer.import.guide.dataTipDescription') }}</p>
                  <div class="tip-example">
                    "1990-01-01" ou "01/01/1990"
                  </div>
                </div>
              </div>

              <div class="tip-card">
                <div class="tip-icon">
                  <HugeiconsIcon :icon="SecurityCheckIcon" :size="24" />
                </div>
                <div class="tip-content">
                  <h6>{{ t('customer.import.guide.validationTip') }}</h6>
                  <p>{{ t('customer.import.guide.validationTipDescription') }}</p>
                </div>
              </div>
            </div>
          </div>
        </ViewContainer>
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import CustomerImportService from '@/services/customerImport.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  Cancel01Icon,
  Upload01Icon,
  File01Icon,
  CloudUploadIcon,
  AlertCircleIcon,
  CheckmarkSquare03Icon,
  AttachmentIcon,
  Tick01Icon,
  InformationCircleIcon,
  Database01Icon,
  UserIcon,
  Home04Icon,
  ArrowRight01Icon,
  CheckmarkCircle01Icon,
  ViewIcon,
  Location01Icon,
  Mail01Icon,
  TelephoneIcon,
  Calendar01Icon,
  LanguageCircleIcon,
  MarketingIcon,
  ToggleOnIcon,
  CsvIcon,
  FileEditIcon,
  Table01Icon,
  ConnectIcon,
  Idea01Icon,
  SecurityCheckIcon,
  BookOpenIcon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const router = useRouter()
const toast = useToast()

const activeTab = ref('import')

const tabs = [
  {
    id: 'import',
    label: 'customer.import.tabs.import',
    icon: Upload01Icon
  },
  {
    id: 'guide',
    label: 'customer.import.tabs.guide',
    icon: BookOpenIcon
  }
]

const step = ref(1)
const selectedFile = ref(null)
const fileErrors = ref([])
const parsingHeaders = ref(false)
const importing = ref(false)

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)

// CSV data
const csvHeaders = ref([])
const csvPreviewRows = ref([])
const fieldMapping = ref({})
const availableFields = ref({ basic: [], addresses: [], addressComponents: [] })
const mappedCustomersPreview = ref([])
const mappedAddressesPreview = ref([])

// Pagination
const currentPage = ref(1)
const pageSize = ref(10)
const csvAnalysis = ref({
  hasExtraColumns: false,
  hasMissingColumns: false,
  columnCount: 0,
  recommendedColumns: []
})

// Computed
const canImport = computed(() => {
  return Object.values(fieldMapping.value).some(value => value !== '')
})

const mappedFieldsCount = computed(() => {
  return Object.values(fieldMapping.value).filter(value => value && value.trim() !== '').length
})

// Headers filtrados - apenas campos com dados válidos
const validCsvHeaders = computed(() => {
  return csvHeaders.value.filter(header => hasValidData(header))
})

// Preview table columns
const previewColumns = computed(() => {
  return csvHeaders.value.map(header => {
    // Detectar se é um campo de endereço completo ou longo
    const isLongField = isDetectedAsCompleteAddress(header) ||
                       header.toLowerCase().includes('endereco') ||
                       header.toLowerCase().includes('address') ||
                       header.toLowerCase().includes('rua') ||
                       header.toLowerCase().includes('street')

    return {
      field: header,
      class: isLongField ? 'text-left column-long-text' : 'text-left column-normal-text',
      style: isLongField ? 'width: 200px; max-width: 200px; min-width: 180px;' : 'width: 100px; max-width: 100px; min-width: 80px;'
    }
  })
})

// Pagination computed properties
const totalPages = computed(() => {
  return Math.ceil(csvPreviewRows.value.length / pageSize.value)
})

const paginatedPreviewData = computed(() => {
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value

  return csvPreviewRows.value.slice(start, end).map(row => {
    const rowData = {}
    csvHeaders.value.forEach((header, index) => {
      rowData[header] = row[index] || '-'
    })
    return rowData
  })
})

// Preview de dados mapeados
const hasAddressMappings = computed(() => {
  return Object.values(fieldMapping.value).some(field =>
    field && (
      ['street', 'number', 'complement', 'district', 'city', 'state', 'country', 'zipCode', 'type', 'label'].includes(field) ||
      field === 'completeAddress'
    )
  )
})



const hasBasicMappings = computed(() => {
  return Object.values(fieldMapping.value).some(field =>
    field && ['name', 'email', 'nickname', 'phone', 'document', 'documentType', 'dayOfBirth', 'defaultLanguage', 'allowsEmailMarketing', 'allowsPhoneMarketing', 'active'].includes(field)
  )
})

// Campos básicos para preview
const basicFields = computed(() => {
  return [
    { value: 'name', label: t('customer.import.fields.name') },
    { value: 'email', label: t('customer.import.fields.email') },
    { value: 'nickname', label: t('customer.import.fields.nickname') },
    { value: 'phone', label: t('customer.import.fields.phone') },
    { value: 'document', label: t('customer.import.fields.document') },
    { value: 'documentType', label: t('customer.import.fields.documentType') },
    { value: 'dayOfBirth', label: t('customer.import.fields.dayOfBirth') },
    { value: 'defaultLanguage', label: t('customer.import.fields.defaultLanguage') },
    { value: 'allowsEmailMarketing', label: t('customer.import.fields.allowsEmailMarketing') },
    { value: 'allowsPhoneMarketing', label: t('customer.import.fields.allowsPhoneMarketing') },
    { value: 'active', label: t('customer.import.fields.active') }
  ]
})

// Campos de endereço para preview
const addressFields = computed(() => {
  return [
    { value: 'street', label: t('customer.import.fields.street') },
    { value: 'number', label: t('customer.import.fields.number') },
    { value: 'complement', label: t('customer.import.fields.complement') },
    { value: 'district', label: t('customer.import.fields.district') },
    { value: 'city', label: t('customer.import.fields.city') },
    { value: 'state', label: t('customer.import.fields.state') },
    { value: 'country', label: t('customer.import.fields.country') },
    { value: 'zipCode', label: t('customer.import.fields.zipCode') },
    { value: 'type', label: t('customer.import.fields.type') },
    { value: 'label', label: t('customer.import.fields.label') }
  ]
})

// Colunas para tabela customers






// Método para obter ícone do campo
const getFieldIcon = (fieldType) => {
  const iconMap = {
    // Campos CSV
    'csv': CsvIcon,

    // Campos básicos
    'email': Mail01Icon,
    'name': UserIcon,
    'nickname': UserIcon,
    'phone': TelephoneIcon,
    'document': File01Icon,
    'documentType': File01Icon,
    'dayOfBirth': Calendar01Icon,
    'defaultLanguage': LanguageCircleIcon,
    'allowsEmailMarketing': MarketingIcon,
    'allowsPhoneMarketing': TelephoneIcon,
    'active': ToggleOnIcon,

    // Campos de endereço
    'street': Location01Icon,
    'number': Location01Icon,
    'complement': Location01Icon,
    'district': Location01Icon,
    'city': Location01Icon,
    'state': Location01Icon,
    'country': Location01Icon,
    'zipCode': Location01Icon,
    'type': Home04Icon,
    'label': Home04Icon,
    'completeAddress': Home04Icon
  }

  return iconMap[fieldType] || File01Icon
}

// Função para obter valor do preview baseado no mapeamento
const getPreviewValue = (row, fieldType) => {
  // Primeiro, verificar se há um campo CSV mapeado diretamente para este fieldType
  const directCsvField = Object.keys(fieldMapping.value).find(key => fieldMapping.value[key] === fieldType)

  if (directCsvField && row[directCsvField]) {
    const value = row[directCsvField]

    // Formatação especial para alguns campos
    if (fieldType === 'active') {
      return value === 'true' || value === '1' || value === 'sim' ? 'Sim' : 'Não'
    }

    if (fieldType === 'allowsEmailMarketing' || fieldType === 'allowsPhoneMarketing') {
      return value === 'true' || value === '1' || value === 'sim' ? 'Sim' : 'Não'
    }

    return value
  }

  // Se não há mapeamento direto, verificar se há campos de endereço completo que podem fornecer este campo
  const completeAddressFields = Object.keys(fieldMapping.value).filter(key => fieldMapping.value[key] === 'completeAddress')

  for (const csvField of completeAddressFields) {
    if (row[csvField]) {
      const parsed = parseCompleteAddress(row[csvField])

      // Retornar o campo específico do endereço parseado se existir
      if (fieldType === 'street' && parsed.street) return parsed.street
      if (fieldType === 'number' && parsed.number) return parsed.number
      if (fieldType === 'complement' && parsed.complement) return parsed.complement
      if (fieldType === 'district' && parsed.district) return parsed.district
      if (fieldType === 'city' && parsed.city) return parsed.city
      if (fieldType === 'state' && parsed.state) return parsed.state
      if (fieldType === 'zipCode' && parsed.zipCode) return parsed.zipCode
      if (fieldType === 'country') return parsed.country || 'Brasil'
      if (fieldType === 'type') return parsed.type || 'HOME'
      if (fieldType === 'label') return parsed.label || 'Principal'
    }
  }

  return null
}



// Methods
const handleFileSelect = (event) => {
  const file = event.target.files[0]
  if (file) {
    processFile(file)
  }
}

const processFile = (file) => {
  selectedFile.value = file
  fileErrors.value = CustomerImportService.validateCSVFile(file)
}

const removeFile = () => {
  selectedFile.value = null
  fileErrors.value = []
  csvHeaders.value = []
  csvPreviewRows.value = []
  fieldMapping.value = {}
  currentPage.value = 1
  step.value = 1
}

const handlePageChange = (page) => {
  currentPage.value = page
  // Scroll para o topo da tabela quando mudar de página
  nextTick(() => {
    const tableElement = document.querySelector('.preview-datatable')
    if (tableElement) {
      tableElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  })
}

// Função para fazer parsing de endereço completo brasileiro
const parseCompleteAddress = (completeAddress) => {
  if (!completeAddress || typeof completeAddress !== 'string') {
    return {}
  }

  const address = completeAddress.trim()
  const parsed = {}

  try {

    const cepMatch = address.match(/(\d{5}-?\d{3})\s*$/)
    if (cepMatch) {
      parsed.zipCode = cepMatch[1].replace('-', '')
    }

    // Remover CEP para processar o resto
    let remaining = address.replace(/,?\s*CEP\s*\d{5}-?\d{3}\s*$/, '').replace(/,?\s*\d{5}-?\d{3}\s*$/, '')

    // Extrair estado (formato " - SP," ou " - SP")
    const stateMatch = remaining.match(/\s*-\s*([A-Z]{2})\s*,?\s*$/)
    if (stateMatch) {
      parsed.state = stateMatch[1]
      remaining = remaining.replace(/\s*-\s*[A-Z]{2}\s*,?\s*$/, '')
    }

    // Dividir por vírgulas
    const parts = remaining.split(',').map(part => part.trim()).filter(part => part)

    if (parts.length >= 1) {
      // Primeira parte: logradouro e número
      const firstPart = parts[0]

      // Padrões mais flexíveis para número
      // Suporta: "Rua A 123", "Av. Paulista, 1000", "Rua das Flores, 250"
      const numberMatch = firstPart.match(/^(.+?)[,\s]+(\d+[\w\-]*)\s*(.*)$/)
      if (numberMatch) {
        parsed.street = numberMatch[1].trim().replace(/,$/, '') // Remove vírgula final se houver
        parsed.number = numberMatch[2].trim()
        if (numberMatch[3].trim()) {
          // Se há algo após o número na primeira parte, é complemento
          const complement = numberMatch[3].trim().replace(/^-\s*/, '') // Remove hífen inicial
          if (complement) {
            parsed.complement = complement
          }
        }
      } else {
        // Se não conseguiu extrair número, toda a primeira parte é a rua
        parsed.street = firstPart
      }
    }

    if (parts.length >= 2) {
      // Segunda parte pode ser complemento ou bairro
      const secondPart = parts[1]

      // Se começa com hífen, é complemento
      if (secondPart.startsWith('-') || secondPart.match(/^(apto|apt|casa|bloco|sala|andar|loja|conj|conjunto)/i)) {
        const complement = secondPart.replace(/^-\s*/, '') // Remove hífen inicial
        parsed.complement = parsed.complement ? `${parsed.complement}, ${complement}` : complement
      } else {
        // Senão, é bairro
        parsed.district = secondPart
      }
    }

    if (parts.length >= 3) {
      // Terceira parte: geralmente bairro (se não foi definido) ou cidade
      if (!parsed.district) {
        parsed.district = parts[2]
      } else {
        parsed.city = parts[2]
      }
    }

    if (parts.length >= 4) {
      // Quarta parte: cidade
      parsed.city = parts[3]
    }

    // Se não encontrou cidade, tentar extrair da última parte antes do estado
    if (!parsed.city && parts.length >= 2) {
      parsed.city = parts[parts.length - 1]
    }

    // Limpeza final dos campos
    Object.keys(parsed).forEach(key => {
      if (typeof parsed[key] === 'string') {
        parsed[key] = parsed[key].trim()
      }
    })

    // Defaults
    parsed.country = parsed.country || 'Brasil'
    parsed.type = parsed.type || 'HOME'
    parsed.label = parsed.label || 'Principal'

  } catch (error) {
    console.error('Erro ao fazer parsing do endereço:', error)
  }

  return parsed
}



const formatFileSize = (bytes) => {
  return CustomerImportService.formatFileSize(bytes)
}

const getFieldDisplayName = (fieldName) => {
  return CustomerImportService.getFieldDisplayName(fieldName)
}

// Função para verificar se um campo deve ser ignorado (não mapeado)
const isIgnoredField = (header) => {
  const ignoredFields = ['creationdate', 'created_at', 'data_criacao', 'datacriacao']
  return ignoredFields.includes(header.toLowerCase().trim())
}

// Função para verificar se um campo tem dados válidos no CSV
const hasValidData = (header) => {
  if (!csvPreviewRows.value || csvPreviewRows.value.length === 0) return false

  const headerIndex = csvHeaders.value.indexOf(header)
  if (headerIndex === -1) return false

  // Verifica se pelo menos 1 linha tem dados não nulos/vazios para este campo
  return csvPreviewRows.value.some(row => {
    const value = row[headerIndex]
    return value !== null && value !== undefined && value !== '' && String(value).trim() !== ''
  })
}

// Função para verificar se um campo foi detectado como endereço completo
const isDetectedAsCompleteAddress = (header) => {
  if (!hasValidData(header)) return false
  
  const headerIndex = csvHeaders.value.indexOf(header)
  const sampleData = csvPreviewRows.value.map(row => row[headerIndex]).filter(val => val)
  
  return isCompleteAddressField(header, sampleData)
}

// Função para gerar preview dos dados mapeados
const generateMappedPreview = () => {
  if (!csvPreviewRows.value || csvPreviewRows.value.length === 0) {
    mappedCustomersPreview.value = []
    mappedAddressesPreview.value = []
    return
  }

  const sampleRows = csvPreviewRows.value.slice(0, 3)

  // Preview para customers
  mappedCustomersPreview.value = sampleRows.map((row, index) => {
    const customerData = { id: `${index + 1}` }

    Object.entries(fieldMapping.value).forEach(([csvField, systemField]) => {
      if (systemField && availableFields.value.basic.includes(systemField)) {
        const headerIndex = csvHeaders.value.indexOf(csvField)
        if (headerIndex !== -1) {
          customerData[systemField] = row[headerIndex] || '-'
        }
      }
    })

    customerData.locale = 'pt-BR'
    customerData.created_at = new Date().toISOString().split('T')[0]

    return customerData
  })

  // Preview para addresses (se houver mapeamentos de endereço)
  if (hasAddressMappings.value) {
    mappedAddressesPreview.value = sampleRows.map((row, index) => {
      const addressData = { id: `${index + 1}`, customer_id: `${index + 1}` }

      Object.entries(fieldMapping.value).forEach(([csvField, systemField]) => {
        if (systemField && availableFields.value.addresses.includes(systemField)) {
          const headerIndex = csvHeaders.value.indexOf(csvField)
          if (headerIndex !== -1) {
            addressData[systemField] = row[headerIndex] || '-'
          }
        }
      })

      return addressData
    })
  } else {
    mappedAddressesPreview.value = []
  }
}

// Watcher para atualizar preview quando mapeamento mudar
watch([fieldMapping, csvPreviewRows], () => {
  generateMappedPreview()
}, { deep: true })

// Função para detectar se um campo contém endereço completo
const isCompleteAddressField = (csvHeader, sampleData) => {
  const headerLower = csvHeader.toLowerCase()

  // Verifica se o nome do campo sugere endereço completo
  const addressPatterns = [
    /^(address|endereco|addr)\d*$/i,
    /^endereco_(principal|secundario|trabalho|completo)$/i,
    /^(address|endereco)_?(primary|secondary|work|complete|full)$/i
  ]

  if (addressPatterns.some(pattern => pattern.test(headerLower))) {
    return true
  }

  // Verifica o conteúdo de algumas amostras
  const samples = sampleData.slice(0, 5).filter(sample => sample && sample.trim())
  if (samples.length === 0) return false

  // Padrões que indicam endereço completo brasileiro
  const completeAddressPatterns = [
    /.*,.*-.*,.*CEP/i, // Formato: Rua, Num - Bairro, Cidade - Estado, CEP
    /.*,.*\d{5}-?\d{3}/i, // Contém CEP
    /.*-\s*(SP|RJ|MG|RS|BA|PR|PE|SC|CE|AM|GO|DF|ES|MA|MS|MT|PA|PB|PI|RN|RO|RR|SE|TO)\s*[,\s]/i, // Contém estado brasileiro
    /.*(rua|av|avenida|alameda|travessa|estrada).*,.*\d+/i, // Logradouro + número
    /.*,.*,.*,.*-\s*[A-Z]{2}/i, // Formato com múltiplas vírgulas e estado
    /.*(rua|av|avenida|alameda|travessa|estrada).*\d+.*-.*,.*,.*-\s*[A-Z]{2}/i // Formato completo
  ]

  // Pelo menos 60% das amostras devem corresponder aos padrões
  const matchingCount = samples.filter(sample =>
    completeAddressPatterns.some(pattern => pattern.test(sample))
  ).length

  return matchingCount >= Math.ceil(samples.length * 0.6)
}

// Função para obter opções de campos filtradas
const getFieldOptions = (csvHeader) => {
  // Se o campo CSV não tem dados válidos, não mostrar opções
  if (!hasValidData(csvHeader)) {
    return []
  }

  const options = []

  // Adicionar opção vazia
  options.push({
    label: 'Selecione um campo...',
    value: ''
  })

  // Campos básicos - garantir que existem e são válidos
  const basicFields = availableFields.value.basic || []
  if (basicFields.length > 0) {
    const basicOptions = basicFields
      .filter(field => field && field.trim() !== '') // Remove campos nulos/vazios
      .map(field => ({
        label: getFieldDisplayName(field),
        value: field
      }))
    
    if (basicOptions.length > 0) {
      // Adicionar campos básicos individuais (não agrupados)
      basicOptions.forEach(option => {
        options.push(option)
      })
    }
  }

  // Verificar se é um campo de endereço completo
  const headerIndex = csvHeaders.value.indexOf(csvHeader)
  const sampleData = csvPreviewRows.value.map(row => row[headerIndex]).filter(val => val)
  const isCompleteAddress = isCompleteAddressField(csvHeader, sampleData)

  // Campos de endereço - oferece opções diferentes baseado no tipo
  const addressFields = availableFields.value.addresses || []
  if (addressFields.length > 0) {
    if (isCompleteAddress) {
      // Para endereços completos, oferecer opção de parsing automático
      options.push({
        label: '🏠 📍 Endereço Completo (com parsing automático)',
        value: 'completeAddress',
        description: 'O sistema irá extrair rua, número, bairro, cidade, etc. automaticamente'
      })
      
      // Também oferecer campos individuais como fallback
      const addressOptions = addressFields
        .filter(field => field && field.trim() !== '')
        .map(field => ({
          label: `🏠 ${getFieldDisplayName(field)} (manual)`,
          value: field
        }))
      
      addressOptions.forEach(option => {
        options.push(option)
      })
    } else {
      // Para campos separados, opções normais
      const addressOptions = addressFields
        .filter(field => field && field.trim() !== '')
        .map(field => ({
          label: `📍 ${getFieldDisplayName(field)}`,
          value: field
        }))
      
      addressOptions.forEach(option => {
        options.push(option)
      })
    }
  }

  return options
}

const parseFileHeaders = async () => {
  if (!selectedFile.value) return
  
  try {
    parsingHeaders.value = true
    
    const result = await CustomerImportService.parseCSVHeaders(selectedFile.value)
    csvHeaders.value = result.headers
    csvPreviewRows.value = result.previewRows
    
    // Initialize field mapping first
    fieldMapping.value = {}
    csvHeaders.value.forEach(header => {
      fieldMapping.value[header] = ''
    })
    
    // Analyze CSV structure and auto-map fields
    analyzeCsvStructure()

    // Generate initial preview
    generateMappedPreview()

    step.value = 2
    
  } catch (error) {
    console.error('Error parsing CSV headers:', error)
    toast.showError(t('customer.import.parseError'))
  } finally {
    parsingHeaders.value = false
  }
}

const analyzeCsvStructure = () => {
  // Análise inteligente com suporte a endereços
  
  const expectedColumns = ['name', 'email', 'phone', 'document']
  
  csvAnalysis.value.columnCount = csvHeaders.value.length
  csvAnalysis.value.hasExtraColumns = csvHeaders.value.length > expectedColumns.length
  csvAnalysis.value.hasMissingColumns = csvHeaders.value.length < expectedColumns.length
  
  // Auto-mapear campos inteligentemente
  csvHeaders.value.forEach(header => {
    const lowerHeader = header.toLowerCase().trim()
    const headerIndex = csvHeaders.value.indexOf(header)
    const sampleData = csvPreviewRows.value.map(row => row[headerIndex]).filter(val => val)

    // Mapear campos básicos com correspondência exata e variações
    if (lowerHeader === 'name' || lowerHeader === 'cliente_nome') {
      fieldMapping.value[header] = 'name'
    } else if (lowerHeader === 'email' || lowerHeader === 'contato_email') {
      fieldMapping.value[header] = 'email'
    } else if (lowerHeader === 'nickname' || lowerHeader === 'apelido_cliente') {
      fieldMapping.value[header] = 'nickname'
    } else if (lowerHeader === 'phone' || lowerHeader === 'telefone_contato') {
      fieldMapping.value[header] = 'phone'
    } else if (lowerHeader === 'document' || lowerHeader === 'numero_documento') {
      fieldMapping.value[header] = 'document'
    } else if (lowerHeader === 'documenttype' || lowerHeader === 'tipo_doc') {
      fieldMapping.value[header] = 'documentType'
    } else if (lowerHeader === 'dayofbirth' || lowerHeader === 'data_nascimento') {
      fieldMapping.value[header] = 'dayOfBirth'
    } else if (lowerHeader === 'defaultlanguage' || lowerHeader === 'idioma_padrao') {
      fieldMapping.value[header] = 'defaultLanguage'
    } else if (lowerHeader === 'allowsemailmarketing' || lowerHeader === 'aceita_email_promo') {
      fieldMapping.value[header] = 'allowsEmailMarketing'
    } else if (lowerHeader === 'allowsphonemarketing' || lowerHeader === 'aceita_sms_promo') {
      fieldMapping.value[header] = 'allowsPhoneMarketing'
    } else if (lowerHeader === 'active' || lowerHeader === 'status_ativo') {
      fieldMapping.value[header] = 'active'
    } else if (lowerHeader === 'creationdate') {
      // Skip creation date - will be set automatically
      fieldMapping.value[header] = ''
    }
    // Mapear endereços inteligentemente - primeiro verificar se é endereço completo
    else if (isCompleteAddressField(header, sampleData)) {
      fieldMapping.value[header] = 'completeAddress'
    }
    // Campos de endereço individuais
    else if (lowerHeader.includes('rua') || lowerHeader.includes('street') || lowerHeader.includes('logradouro')) {
      fieldMapping.value[header] = 'street'
    } else if (lowerHeader.includes('numero') || lowerHeader.includes('number')) {
      fieldMapping.value[header] = 'number'
    } else if (lowerHeader.includes('complemento') || lowerHeader.includes('complement') || lowerHeader.includes('apto')) {
      fieldMapping.value[header] = 'complement'
    } else if (lowerHeader.includes('bairro') || lowerHeader.includes('district')) {
      fieldMapping.value[header] = 'district'
    } else if (lowerHeader.includes('cidade') || lowerHeader.includes('city')) {
      fieldMapping.value[header] = 'city'
    } else if (lowerHeader.includes('estado') || lowerHeader.includes('state') || lowerHeader.includes('uf')) {
      fieldMapping.value[header] = 'state'
    } else if (lowerHeader.includes('cep') || lowerHeader.includes('zip')) {
      fieldMapping.value[header] = 'zipCode'
    }
  })
}

const goBackToFileSelection = () => {
  step.value = 1
}

const startImport = async () => {
  if (!selectedFile.value) return
  
  try {
    importing.value = true



    await CustomerImportService.createImport(selectedFile.value, fieldMapping.value)

    toast.showSuccess(
      t('customer.import.importStartedMessage'),
      {
        title: t('customer.import.importStarted'),
        duration: 5000
      }
    )

    // Navegar para lista de clientes
    router.push('/customer-list')
    
  } catch (error) {
    console.error('Error starting import:', error)

    // Better error handling
    let errorMessage = t('customer.import.importError')

    if (error.response?.data?.errors?.length > 0) {
      errorMessage = error.response.data.errors[0].message
    } else if (error.response?.data?.message) {
      errorMessage = error.response.data.message
    } else if (error.message) {
      errorMessage = error.message
    }

    toast.showError(errorMessage, {
      title: t('customer.import.importError'),
      duration: 8000
    })
  } finally {
    importing.value = false
  }
}

const goBack = () => {
  router.push('/customer-list')
}



// Modal control functions
const showConfirm = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('confirm')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'info'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const openConfirm = () => {
  showConfirm(
    t('customer.import.confirmMessage'),
    t('customer.import.confirmTitle'),
    startImport
  )
}

const loadAvailableFields = async () => {
  try {
    const fields = await CustomerImportService.getAvailableFields()

    
    // Garantir estrutura válida
    availableFields.value = {
      basic: fields?.basic || [
        'name', 'email', 'nickname', 'phone', 'document', 'documentType',
        'dayOfBirth', 'defaultLanguage', 'allowsEmailMarketing', 'allowsPhoneMarketing', 'active'
      ],
      addresses: fields?.addresses || [
        'street', 'number', 'complement', 'district', 'city', 'state', 'country', 'zipCode', 'type', 'label'
      ]
    }
    

  } catch (error) {
    console.error('Error loading available fields:', error)
    // Fallback robusto para garantir funcionamento
    availableFields.value = {
      basic: [
        'name', 'email', 'nickname', 'phone', 'document', 'documentType',
        'dayOfBirth', 'defaultLanguage', 'allowsEmailMarketing', 'allowsPhoneMarketing', 'active'
      ],
      addresses: [
        'street', 'number', 'complement', 'district', 'city', 'state', 'country', 'zipCode', 'type', 'label'
      ]
    }
  }
}

// Lifecycle
onMounted(() => {
  loadAvailableFields()
})
</script>

<style scoped>
.customer-import-container {
  max-width: 1200px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
}

/* Import Form Container */
.import-form-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* File Upload Area */
.file-upload-area {
  border: 2px dashed var(--iluria-color-border);
  border-radius: 12px;
  padding: 40px 20px;
  text-align: center;
  transition: all 0.3s ease;
  background: var(--iluria-color-surface-secondary);
}



.file-upload-area.has-file {
  border-style: solid;
  border-color: var(--iluria-color-success);
  background: rgba(34, 197, 94, 0.05);
}

.upload-prompt {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 16px;
}

.upload-icon {
  color: var(--iluria-color-text-secondary);
}

.upload-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.upload-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.file-selected {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 16px;
  background: var(--iluria-color-surface-primary);
  border-radius: 8px;
}

.file-info {
  display: flex;
  align-items: center;
  gap: 12px;
}

.file-icon {
  color: var(--iluria-color-primary);
}

.file-name {
  font-size: 16px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.file-size {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Error List */
.error-list {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.3);
  border-radius: 8px;
  padding: 16px;
}

.error-item {
  display: flex;
  align-items: center;
  gap: 8px;
  color: #dc2626;
  font-size: 14px;
  margin-bottom: 8px;
}

.error-item:last-child {
  margin-bottom: 0;
}

/* CSV Analysis Alerts */
.csv-alerts {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.alert {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.1);
  border: 1px solid rgba(245, 158, 11, 0.3);
  color: #d97706;
}

.alert-info {
  background: rgba(59, 130, 246, 0.1);
  border: 1px solid rgba(59, 130, 246, 0.3);
  color: #2563eb;
}

/* CSV Preview */
.csv-preview {
  margin-bottom: 24px;
}

.preview-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 12px;
}

.preview-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.preview-info {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  background: var(--iluria-color-surface-secondary);
  padding: 6px 12px;
  border-radius: 6px;
  font-weight: 500;
}

.preview-table-wrapper {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
  background: var(--iluria-color-surface-primary);
  margin-bottom: 16px;
  overflow-x: auto;
}

.preview-datatable {
  border: none;
  border-radius: 0;
  min-width: 100%;
  table-layout: fixed;
}

/* Estilos para colunas com texto longo */
.preview-datatable .column-long-text {
  width: 200px !important;
  max-width: 200px !important;
  min-width: 180px !important;
}

.preview-datatable .column-normal-text {
  width: 100px !important;
  max-width: 100px !important;
  min-width: 80px !important;
}

/* Configuração geral das células */
.preview-datatable td {
  position: relative;
  overflow: hidden;
  vertical-align: top;
  padding: 8px 12px !important;
}

/* Melhorar headers da tabela */
.preview-datatable th {
  position: sticky;
  top: 0;
  background: var(--iluria-color-surface-secondary);
  z-index: 10;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  border-bottom: 2px solid var(--iluria-color-border);
}

/* Conteúdo das células */
.preview-datatable .cell-content {
  display: block;
  width: 100%;
  font-size: 13px;
  line-height: 1.3;
}

/* Células com texto longo (endereços) */
.preview-datatable .column-long-text .cell-content {
  white-space: normal;
  word-wrap: break-word;
  overflow-wrap: break-word;
  hyphens: auto;
  max-height: 4em; /* ~3 linhas */
  overflow: hidden;
  display: -webkit-box;
  -webkit-line-clamp: 3;
  line-clamp: 3;
  -webkit-box-orient: vertical;
  cursor: help;
}

/* Células com texto normal */
.preview-datatable .column-normal-text .cell-content {
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  cursor: help;
}

.preview-pagination-wrapper {
  display: flex;
  justify-content: center;
  align-items: center;
  padding: 16px;
  background: var(--iluria-color-surface-secondary);
  border-top: 1px solid var(--iluria-color-border);
  border-radius: 0 0 12px 12px;
}

.preview-pagination {
  margin: 0;
}

/* Field Mapping */
.mapping-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 16px 0;
}

.mapping-grid {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.mapping-row {
  display: grid;
  grid-template-columns: 1fr auto 1fr;
  gap: 16px;
  align-items: center;
  padding: 16px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
}

.csv-field,
.iluria-field {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.field-label {
  font-size: 12px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.field-value {
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  padding: 8px 12px;
  background: var(--iluria-color-surface-primary);
  border-radius: 6px;
  border: 1px solid var(--iluria-color-border);
}

.field-select-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}

.field-select {
  flex: 1;
}

.field-select:focus {
  outline: none;
}

.field-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.mapped-icon {
  color: var(--iluria-color-success);
}

.unmapped-icon {
  color: var(--iluria-color-warning);
}

.arrow {
  color: var(--iluria-color-text-secondary);
}

/* Step Actions */
.step-actions {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
  padding-top: 24px;
  border-top: 1px solid var(--iluria-color-border);
}

/* Responsive */
@media (max-width: 768px) {
  .mapping-row {
    grid-template-columns: 1fr;
    gap: 12px;
  }

  .arrow {
    transform: rotate(90deg);
    justify-self: center;
  }

  .step-actions {
    flex-direction: column;
  }
}

/* New Field Mapping Styles */
.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.mapping-header .header-content {
  flex: 1;
}

.mapping-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
}

.mapping-subtitle {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.mapping-stats {
  display: flex;
  align-items: center;
  gap: 8px;
}

.mapped-count {
  font-size: 12px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 12px;
  display: inline-block;
}

.mapped-count.success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}



.csv-field-section,
.iluria-field-section {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.field-header {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.csv-field-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  padding: 12px 16px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
  font-family: 'Courier New', monospace;
  min-height: 44px;
  display: flex;
  align-items: center;
}

.field-select-container {
  position: relative;
  display: flex;
  align-items: center;
  gap: 8px;
}



.field-status {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
}

.mapped-icon {
  color: var(--iluria-color-success);
}

.mapping-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-tertiary);
  margin-top: 20px;
}

/* Mapped Data Preview Styles */

.mapping-stats {
  display: flex;
  gap: 8px;
  align-items: center;
}

.stat-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 12px;
  border-radius: 12px;
  display: inline-block;
}

.stat-badge.success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.stat-badge.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

.mapped-tables-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.mapped-table-section {
  background: var(--iluria-color-surface-secondary);
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
  overflow: hidden;
}

.table-section-header {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 16px 20px;
  background: var(--iluria-color-surface-tertiary);
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  background: var(--iluria-color-surface-primary);
  border-radius: 6px;
  color: var(--iluria-color-primary);
}

.table-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
  font-family: 'Courier New', monospace;
}

.table-description {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.mapped-table-section .table-wrapper {
  padding: 0;
  background: var(--iluria-color-surface-primary);
}

.mapped-datatable {
  border: none;
  border-radius: 0;
}

.mapped-datatable :deep(.p-datatable-thead > tr > th) {
  background: var(--iluria-color-surface-tertiary);
  font-size: 11px;
  padding: 10px 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.mapped-datatable :deep(.p-datatable-tbody > tr > td) {
  padding: 10px 12px;
  font-size: 12px;
  font-family: 'Courier New', monospace;
}

/* Loading and Error States */
.loading-fields {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 40px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.csv-field-value.invalid {
  background: rgba(239, 68, 68, 0.05);
  border-color: rgba(239, 68, 68, 0.2);
  color: #dc2626;
  opacity: 0.8;
}

.csv-field-value.complete-address {
  background: rgba(59, 130, 246, 0.05);
  border-color: rgba(59, 130, 246, 0.2);
  color: #1d4ed8;
}

.invalid-badge {
  font-size: 10px;
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
  padding: 2px 6px;
  border-radius: 4px;
  margin-left: 8px;
  font-weight: 500;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}



.field-select:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Improved Alert Styles */
.alert {
  display: flex;
  align-items: center;
  gap: 10px;
  padding: 14px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid transparent;
}

.alert-warning {
  background: rgba(245, 158, 11, 0.08);
  border-color: rgba(245, 158, 11, 0.2);
  color: #d97706;
}

.alert-info {
  background: rgba(59, 130, 246, 0.08);
  border-color: rgba(59, 130, 246, 0.2);
  color: #2563eb;
}

.alert-success {
  background: rgba(34, 197, 94, 0.08);
  border-color: rgba(34, 197, 94, 0.2);
  color: #16a34a;
}

/* Simplified Mapping Styles */
.mapping-summary {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-bottom: 24px;
}

.summary-item {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 12px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
}

.summary-item.success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.summary-item.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Simplified Field Mapping */
.csv-field {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 200px;
}

.field-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  flex-shrink: 0;
}

.field-icon {
  color: var(--iluria-color-text-secondary);
}

.field-info {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex: 1;
  min-width: 0;
  overflow: visible;
  justify-content: center;
}

.field-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  word-break: break-word;
  white-space: normal;
  overflow-wrap: break-word;
  margin: 0;
  line-height: 1.4;
}

.address-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 11px;
  padding: 4px 8px;
  border-radius: 6px;
  background: rgba(168, 85, 247, 0.1);
  color: #7c3aed;
  font-weight: 500;
  margin-top: 4px;
  white-space: nowrap;
  flex-shrink: 0;
}

.mapping-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-secondary);
  flex-shrink: 0;
  margin: 0 8px;
}

.system-field {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  min-width: 200px;
}

.select-wrapper {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
  position: relative;
  min-height: 40px;
}

.select-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
}

.select-icon {
  color: var(--iluria-color-text-secondary);
  flex-shrink: 0;
}

.field-select {
  flex: 1;
}

.mapped-check-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 32px;
  height: 32px;
  border-radius: 6px;
  background: rgba(34, 197, 94, 0.1);
  border: 1px solid rgba(34, 197, 94, 0.2);
  flex-shrink: 0;
}

.mapped-check-wrapper.has-error {
  background: rgba(239, 68, 68, 0.1);
  border: 1px solid rgba(239, 68, 68, 0.2);
}

.mapped-check-wrapper.is-ignored {
  background: rgba(107, 114, 128, 0.1);
  border: 1px solid rgba(107, 114, 128, 0.2);
}

.mapped-check {
  flex-shrink: 0;
}

.mapped-check.success {
  color: #16a34a;
}

.mapped-check.error {
  color: #dc2626;
}

.mapped-check.ignored {
  color: #6b7280;
}

/* Melhorar layout dos itens de mapeamento */
.mapping-item {
  display: flex;
  align-items: center;
  gap: 20px;
  padding: 20px;
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
  margin-bottom: 12px;
}

.mapping-item:hover {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
  transform: translateY(-1px);
}

.mapping-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.mapping-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.mapping-status-badge {
  display: flex;
  align-items: center;
  gap: 8px;
}

.status-text {
  font-size: 13px;
  font-weight: 500;
  padding: 6px 12px;
  border-radius: 6px;
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.mapping-list {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Melhorar resolução das tabelas */
.preview-table-wrapper .iluria-data-table,
.mapped-tables-container .iluria-data-table {
  font-size: 13px !important;
  width: 100%;
}

.preview-table-wrapper .iluria-data-table th,
.mapped-tables-container .iluria-data-table th {
  font-size: 12px !important;
  font-weight: 600;
  padding: 8px 12px !important;
  vertical-align: top;
  word-wrap: break-word;
}

.preview-table-wrapper .iluria-data-table td,
.mapped-tables-container .iluria-data-table td {
  font-size: 13px !important;
  padding: 8px 12px !important;
  vertical-align: top;
  word-wrap: break-word;
}

/* Responsividade da tabela de preview */
@media (max-width: 1200px) {
  .preview-datatable .column-long-text {
    width: 160px !important;
    max-width: 160px !important;
    min-width: 140px !important;
  }

  .preview-datatable .column-normal-text {
    width: 90px !important;
    max-width: 90px !important;
    min-width: 70px !important;
  }
}

@media (max-width: 768px) {
  .preview-table-wrapper {
    overflow-x: auto;
  }

  .preview-datatable .column-long-text {
    width: 140px !important;
    max-width: 140px !important;
    min-width: 120px !important;
  }

  .preview-datatable .column-normal-text {
    width: 80px !important;
    max-width: 80px !important;
    min-width: 60px !important;
  }

  .preview-datatable .cell-content {
    font-size: 12px;
  }
}

/* Melhorar info cards */
.info-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
}

.info-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.info-card {
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 16px;
}

.info-card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 8px;
}

.info-card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.info-card-text {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.5;
  margin: 0;
}

/* Melhorar badges de estatísticas */
.stat-badge {
  display: flex;
  align-items: center;
  gap: 4px;
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 4px;
}

.stat-badge.success {
  background: rgba(34, 197, 94, 0.1);
  color: #16a34a;
  border: 1px solid rgba(34, 197, 94, 0.2);
}

.stat-badge.info {
  background: rgba(59, 130, 246, 0.1);
  color: #2563eb;
  border: 1px solid rgba(59, 130, 246, 0.2);
}

/* Garantir que dropdowns funcionem corretamente */
.field-select .p-dropdown-panel {
  z-index: 9999 !important;
}

.field-select .p-dropdown {
  width: 100%;
}

/* Preview Unificado */
.unified-preview-container {
  margin-top: 24px;
}

.preview-cards {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.customer-preview-card {
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  overflow: hidden;
}

.customer-preview-card .card-header {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 16px 20px;
  background: var(--iluria-color-surface-secondary);
  border-bottom: 1px solid var(--iluria-color-border);
}

.customer-preview-card .card-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.customer-preview-card .card-content {
  padding: 20px;
}

.data-section {
  margin-bottom: 20px;
}

.data-section:last-child {
  margin-bottom: 0;
}

.section-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 12px;
  padding-bottom: 8px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.section-title {
  font-size: 13px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.field-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 12px;
}

.field-item {
  display: flex;
  flex-direction: column;
}

.field-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.field-label {
  display: flex;
  align-items: center;
  gap: 6px;
  font-size: 12px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
}

.field-value {
  font-size: 13px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  padding: 6px 8px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 6px;
  border: 1px solid var(--iluria-color-border);
}

/* Tabs */
.tabs-container {
  margin-bottom: 24px;
}

.tabs-nav {
  display: flex;
  gap: 4px;
  background: var(--iluria-color-surface-secondary);
  padding: 4px;
  border-radius: 12px;
  border: 1px solid var(--iluria-color-border);
}

.tab-button {
  flex: 1;
  justify-content: center;
}

/* Remove box-shadow dos botões de tabs para evitar conflito com o design */
.tabs-nav .tab-button.btn {
  box-shadow: none !important;
}

.tabs-nav .tab-button.btn:active {
  transform: none !important;
}



/* Dark theme adjustments for guide cards */
.theme-dark .guide-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .guide-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--iluria-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-dark .tip-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .tip-card:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--iluria-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-dark .code-example {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .tip-example {
  background: rgba(0, 0, 0, 0.3);
  border-color: rgba(255, 255, 255, 0.1);
  color: #e5e7eb;
}

/* Dark theme adjustments for mapping and preview cards */
.theme-dark .mapping-item {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .mapping-item:hover {
  background: rgba(255, 255, 255, 0.08);
  border-color: var(--iluria-color-primary);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

.theme-dark .customer-preview-card {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .customer-preview-card .card-header {
  background: rgba(255, 255, 255, 0.03);
  border-bottom-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .csv-preview .preview-table-wrapper {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.theme-dark .preview-info {
  background: rgba(255, 255, 255, 0.05);
  color: var(--iluria-color-text-secondary);
}

.theme-dark .mapping-summary .summary-item {
  background: rgba(255, 255, 255, 0.05);
  border-color: rgba(255, 255, 255, 0.1);
}

.tab-content {
  animation: fadeIn 0.3s ease;
}

@keyframes fadeIn {
  from { opacity: 0; transform: translateY(10px); }
  to { opacity: 1; transform: translateY(0); }
}

/* Guide Styles */
.guide-container {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

.guide-section {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.guide-cards {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
  gap: 20px;
}

.guide-card {
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.guide-card:hover {
  border-color: var(--iluria-color-primary-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.guide-card-header {
  display: flex;
  align-items: center;
  gap: 12px;
  margin-bottom: 16px;
}

.guide-card-header h4 {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.guide-card-content {
  color: var(--iluria-color-text-secondary);
  line-height: 1.6;
}

.code-example {
  background: var(--iluria-color-surface-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 16px;
  margin-top: 12px;
  font-family: 'Courier New', monospace;
  font-size: 13px;
  overflow-x: auto;
}

.code-example pre {
  margin: 0;
  white-space: pre;
  color: var(--iluria-color-text-primary);
}

.guide-list {
  list-style: none;
  padding: 0;
  margin: 0;
}

.guide-list li {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  margin-bottom: 8px;
  padding-left: 20px;
  position: relative;
}

.guide-list li::before {
  content: "•";
  color: var(--iluria-color-primary);
  font-weight: bold;
  position: absolute;
  left: 0;
}

/* Mapping Examples */
.mapping-examples {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(350px, 1fr));
  gap: 24px;
}

.mapping-example {
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
}

.example-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 16px;
  padding-bottom: 12px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.example-header h5 {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.field-examples {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.field-example {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  background: var(--iluria-color-surface-secondary);
  border-radius: 8px;
}

.csv-example {
  font-family: 'Courier New', monospace;
  font-size: 13px;
  background: rgba(59, 130, 246, 0.1);
  color: #1d4ed8;
  padding: 4px 8px;
  border-radius: 4px;
  font-weight: 500;
  min-width: 120px;
}

.system-example {
  font-size: 13px;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

/* Tips Grid */
.tips-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 20px;
}

.tip-card {
  display: flex;
  gap: 16px;
  background: var(--iluria-color-surface-primary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 20px;
  transition: all 0.2s ease;
}

.tip-card:hover {
  border-color: var(--iluria-color-primary-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.08);
}

.tip-icon {
  flex-shrink: 0;
  width: 48px;
  height: 48px;
  background: var(--iluria-color-primary-light);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-primary);
}

.tip-content h6 {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.tip-content p {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.5;
  margin: 0 0 12px 0;
}

.tip-example {
  font-family: 'Courier New', monospace;
  font-size: 12px;
  background: var(--iluria-color-surface-secondary);
  color: var(--iluria-color-text-primary);
  padding: 8px 12px;
  border-radius: 6px;
  border-left: 3px solid var(--iluria-color-primary);
}
</style>
