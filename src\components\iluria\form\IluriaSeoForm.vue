<template>
    <!-- <PERSON><PERSON> -->
    <div class="form-group w-full mb-2">
    <IluriaInputText 
      id="meta_title" 
      name="meta_title" 
      :label="t('seo.pageTitle')" 
      v-model="form.seo.metaTitle" 
      maxlength="70"
      :formContext="formContext?.seo?.metaTitle"
      @input="limitMetaTitle"
    />
    <div class="text-xs flex justify-end mt-1" :class="{
      'text-gray-500': (form.seo?.metaTitle?.length || 0) < 60,
      'text-yellow-500': (form.seo?.metaTitle?.length || 0) >= 60 && (form.seo?.metaTitle?.length || 0) < 70,
      'text-red-500': (form.seo?.metaTitle?.length || 0) >= 70
    }">
      {{ form.seo?.metaTitle?.length || 0 }} {{ t('seo.recommendedLengths.title') }}
    </div>
  </div>
  
  <!-- Meta <PERSON> -->
  <div>
    <IluriaInputText
      id="meta_description"
      :label="t('seo.metaDescription')"
      v-model="form.seo.metaDescription"
      :formContext="formContext?.seo?.metaDescription"
      :placeholder="t('seo.metaDescriptionPlaceholder')"
      maxlength="160"
      @input="limitMetaDescription"
    />
    <div class="text-xs flex justify-end mt-1" :class="{
      'text-gray-500': form.seo?.metaDescription?.length < 136,
      'text-yellow-500': form.seo?.metaDescription?.length >= 136 && form.seo?.metaDescription?.length < 160,
      'text-red-500': form.seo?.metaDescription?.length >= 160
    }">
      {{ form.seo?.metaDescription?.length || 0 }} {{ t('seo.recommendedLengths.description') }}
    </div>
  </div>
</template>

<script setup>
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaEditor from '@/components/editor/IluriaEditor.vue';
import { useI18n } from 'vue-i18n';
import { computed } from 'vue';

const { t } = useI18n()

const props = defineProps({
  form: {
    type: Object,
    required: true
  },
  formContext: {
    type: Object,
    default: () => ({})
  }
})

function stripHtml(html) {
  if (!html) return "";
  const temporalDivElement = document.createElement("div");
  temporalDivElement.innerHTML = html;
  let text = temporalDivElement.textContent || temporalDivElement.innerText || "";
  return text.replace(/\s+/g, ' ').trim();
}

const cleanDescriptionLength = computed(() => {
  return stripHtml(props.form.seo?.metaDescription).length;
});

function limitMetaDescription() {
  if (props.form.seo.metaDescription && props.form.seo.metaDescription.length > 160) {
    props.form.seo.metaDescription = props.form.seo.metaDescription.substring(0, 160)
  }
}

function limitMetaTitle() {
  if (props.form.seo.metaTitle && props.form.seo.metaTitle.length > 70) {
    props.form.seo.metaTitle = props.form.seo.metaTitle.substring(0, 70)
  }
}
</script>
