<template>
  <div class="footer-sidebar-editor">
    <!-- Tabs -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- <PERSON><PERSON> -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.footerContent') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.companyName') }}</IluriaLabel>
            <IluriaInputText
              v-model="footerConfig.companyName"
              :placeholder="$t('layoutEditor.enterCompanyName')"
              @update:modelValue="updateFooter"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.copyright') }}</IluriaLabel>
            <IluriaInputText
              v-model="footerConfig.copyright"
              :placeholder="$t('layoutEditor.enterCopyright')"
              @update:modelValue="updateFooter"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.address') }}</IluriaLabel>
            <IluriaInputText
              v-model="footerConfig.address"
              :placeholder="$t('layoutEditor.enterAddress')"
              @update:modelValue="updateFooter"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.phone') }}</IluriaLabel>
            <IluriaInputText
              v-model="footerConfig.phone"
              :placeholder="$t('layoutEditor.enterPhone')"
              @update:modelValue="updateFooter"
            />
          </div>
        </div>
      </div>

      <!-- Aba Cores -->
      <div v-if="activeTab === 'colors'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.colorConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.backgroundColor') }}</IluriaLabel>
            <IluriaColorPicker
                v-model="footerConfig.backgroundColor"
                @update:modelValue="updateFooter"
              />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.textColor') }}</IluriaLabel>
            <IluriaColorPicker
                v-model="footerConfig.textColor"
                @update:modelValue="updateFooter"
              />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed'])

// Tabs
const tabs = [
  { value: 'content', label: t('layoutEditor.content') },
  { value: 'colors', label: t('layoutEditor.colors') }
]

// Estado das abas
const activeTab = ref('content')

// Configuração do footer
const footerConfig = ref({
  companyName: 'Minha Empresa',
  copyright: '© 2024 Todos os direitos reservados.',
  address: 'Rua Exemplo, 123 - Centro',
  phone: '(11) 1234-5678',
  backgroundColor: '#2c3e50',
  textColor: '#ffffff'
})

// Carrega configuração do elemento
const loadFooterConfig = () => {
  if (!props.element) return

  try {
    footerConfig.value = {
      companyName: props.element.getAttribute('data-company-name') || 'Minha Empresa',
      copyright: props.element.getAttribute('data-copyright') || '© 2024 Todos os direitos reservados.',
      address: props.element.getAttribute('data-address') || 'Rua Exemplo, 123 - Centro',
      phone: props.element.getAttribute('data-phone') || '(11) 1234-5678',
      backgroundColor: props.element.getAttribute('data-bg-color') || '#2c3e50',
      textColor: props.element.getAttribute('data-text-color') || '#ffffff'
    }
  } catch (error) {
    console.error('Erro ao carregar configuração do footer:', error)
  }
}

// Atualiza o footer
const updateFooter = () => {
  if (!props.element) return

  try {
    // Atualiza atributos do elemento
    Object.entries(footerConfig.value).forEach(([key, value]) => {
      const attributeName = `data-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
      props.element.setAttribute(attributeName, value.toString())
    })

    // Re-gera o HTML
    regenerateFooterHTML()
    
    // Aplica estilos
    applyFooterStyles()
    
    // Notifica mudanças
    emit('data-changed', footerConfig.value)
  } catch (error) {
    console.error('Erro ao atualizar footer:', error)
  }
}

// Re-gera o HTML do footer
const regenerateFooterHTML = () => {
  if (!props.element) return
  
  try {
    const data = footerConfig.value

    const footerHTML = `
      <footer class="footer-main">
        <div class="footer-container">
          <div class="footer-content">
            <div class="footer-info">
              <h3 class="company-name">${data.companyName}</h3>
              <p class="address">${data.address}</p>
              <p class="phone">${data.phone}</p>
            </div>
            <div class="footer-bottom">
              <p class="copyright">${data.copyright}</p>
            </div>
          </div>
        </div>
      </footer>
    `
    
    const existingStyles = props.element.querySelector('.footer-dynamic-styles')
    props.element.innerHTML = footerHTML
    if (existingStyles) {
      props.element.appendChild(existingStyles)
    }
    
  } catch (error) {
    console.error('Erro ao regenerar HTML:', error)
  }
}

// Aplica estilos
const applyFooterStyles = () => {
  if (!props.element) return
  
  try {
    const existingStyle = props.element.querySelector('.footer-dynamic-styles')
    if (existingStyle) existingStyle.remove()

    const styles = `
      <style class="footer-dynamic-styles">
        .footer-main {
          background-color: ${footerConfig.value.backgroundColor} !important;
          color: ${footerConfig.value.textColor} !important;
          padding: 40px 0;
        }
        .footer-container { max-width: 1200px; margin: 0 auto; padding: 0 20px; }
        .footer-content { text-align: center; }
        .company-name { font-size: 24px; font-weight: bold; margin-bottom: 15px; color: ${footerConfig.value.textColor} !important; }
        .address, .phone { margin: 5px 0; color: ${footerConfig.value.textColor} !important; }
        .footer-bottom { margin-top: 30px; padding-top: 20px; border-top: 1px solid rgba(255,255,255,0.2); }
        .copyright { margin: 0; font-size: 14px; color: ${footerConfig.value.textColor} !important; }
      </style>
    `
    
    props.element.insertAdjacentHTML('beforeend', styles)
    
  } catch (error) {
    console.error('Erro ao aplicar estilos:', error)
  }
}

// Lifecycle
onMounted(() => {
  loadFooterConfig()
})
</script>

<style scoped>
.footer-sidebar-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  border-bottom: 2px solid transparent;
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-bg);
}

.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.form-group {
  margin-bottom: 1rem;
}
</style> 
