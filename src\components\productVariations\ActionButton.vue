<template>
  <IluriaButton 
    :color="getButtonColor"
    :size="getButtonSize"
    @click="$emit('click')"
  >
    <slot></slot>
  </IluriaButton>
</template>

<script setup>
import { computed } from 'vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';

const props = defineProps({
  variant: {
    type: String,
    default: 'primary',
    validator: (value) => ['primary', 'secondary', 'danger', 'text'].includes(value)
  },
  size: {
    type: String,
    default: 'md',
    validator: (value) => ['sm', 'md', 'lg'].includes(value)
  }
});

const emit = defineEmits(['click']);

const getButtonColor = computed(() => {
  const colorMap = {
    primary: 'primary',
    secondary: 'outline',
    danger: 'danger',
    text: 'text-primary'
  };
  return colorMap[props.variant] || 'primary';
});

const getButtonSize = computed(() => {
  const sizeMap = {
    sm: 'small',
    md: 'medium',
    lg: 'large'
  };
  return sizeMap[props.size] || 'medium';
});
</script>
