<template>
  <div class="location-sidebar-editor">
    <!-- Tabs -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- Aba Geral -->
      <div v-if="activeTab === 'general'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.general') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.title') }}</IluriaLabel>
            <IluriaInputText
              v-model="title"
              placeholder="Título da seção"
              @update:modelValue="updateLocation"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.subtitle') }}</IluriaLabel>
            <IluriaTextarea
              v-model="subtitle"
              placeholder="Descrição da seção de localizações"
              @update:modelValue="updateLocation"
              rows="3"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.layout') }}</IluriaLabel>
            <IluriaSelect 
              v-model="layout" 
              :options="[
                { value: 'grid', label: $t('layoutEditor.grid') },
                { value: 'list', label: $t('layoutEditor.list') },
                { value: 'cards', label: $t('layoutEditor.cards') }
              ]"
              @update:modelValue="updateLocation"
            />
          </div>
        </div>
      </div>

      <!-- Aba Localizações -->
      <div v-if="activeTab === 'locations'" class="tab-panel">
        <div class="locations-header">
          <h3 class="section-title">{{ $t('layoutEditor.manageLocations') }}</h3>
          <IluriaButton @click="addLocation" variant="primary" size="small">
            {{ $t('layoutEditor.addLocation') }}
          </IluriaButton>
        </div>

        <div class="locations-list">
          <div 
            v-for="(location, index) in locations" 
            :key="location.id"
            class="location-editor-item"
          >
            <div class="location-item-header">
              <h4>{{ location.name || $t('layoutEditor.newLocation') }}</h4>
              <IluriaButton 
                @click="removeLocation(index)" 
                color="danger" 
                variant="ghost"
                size="small"
              >
                {{ $t('layoutEditor.remove') }}
              </IluriaButton>
            </div>

            <div class="location-form">
              <div class="form-group">
                <IluriaLabel>{{ $t('layoutEditor.locationName') }}</IluriaLabel>
                <IluriaInputText
                  v-model="location.name"
                  placeholder="Nome da localização"
                  @update:modelValue="updateLocation"
                />
              </div>

              <div class="form-group">
                <IluriaLabel>{{ $t('layoutEditor.description') }}</IluriaLabel>
                <IluriaTextarea
                  v-model="location.description"
                  placeholder="Descrição da localização"
                  @update:modelValue="updateLocation"
                  rows="2"
                />
              </div>

              <div class="form-group">
                <IluriaLabel>{{ $t('layoutEditor.address') }}</IluriaLabel>
                <IluriaTextarea
                  v-model="location.address"
                  placeholder="Endereço completo"
                  @update:modelValue="onAddressChange"
                  rows="2"
                />
              </div>

              <div class="form-row">
                <div class="form-group">
                  <IluriaLabel>{{ $t('layoutEditor.phone') }}</IluriaLabel>
                  <IluriaInputText
                    v-model="location.phone"
                    placeholder="+55 (11) 9999-9999"
                    @update:modelValue="updateLocation"
                  />
                </div>
                <div class="form-group">
                  <IluriaLabel>{{ $t('layoutEditor.email') }}</IluriaLabel>
                  <IluriaInputText
                    v-model="location.email"
                    placeholder="<EMAIL>"
                    @update:modelValue="updateLocation"
                  />
                </div>
              </div>

              <div class="form-group">
                <IluriaLabel>{{ $t('layoutEditor.hours') }}</IluriaLabel>
                <IluriaInputText
                  v-model="location.hours"
                  placeholder="Horário de funcionamento"
                  @update:modelValue="updateLocation"
                />
              </div>

              <div class="form-group">
                <IluriaLabel>{{ $t('layoutEditor.image') }}</IluriaLabel>
                <div class="image-input-group">
                  <IluriaInputText
                    v-model="location.image"
                    placeholder="URL da imagem"
                    @update:modelValue="updateLocation"
                    readonly
                    class="image-url-input"
                  />
                  <IluriaButton 
                    @click="openImageSelector(index)" 
                    variant="secondary" 
                    size="small"
                    class="select-image-btn"
                  >
                    📷 {{ $t('layoutEditor.selectImage') }}
                  </IluriaButton>
                </div>
                <div v-if="location.image" class="image-preview">
                  <img :src="location.image" :alt="location.name" />
                  <IluriaButton 
                    @click="removeImage(index)" 
                    variant="ghost"
                    color="danger"
                    size="small"
                    class="remove-image-btn-custom"
                  >
                    ✕
                  </IluriaButton>
                </div>
              </div>

              <div class="form-group">
                <IluriaCheckBox
                  v-model="location.hasMap"
                  :label="$t('layoutEditor.showMap')"
                  @update:modelValue="onMapToggle(index)"
                />
                <div v-if="location.hasMap && location.address" class="map-update-section">
                  <IluriaButton 
                    @click="updateLocationMap(index)" 
                    variant="secondary" 
                    size="small"
                    class="update-map-btn"
                  >
                    🗺️ Atualizar Mapa
                  </IluriaButton>
                  <small class="map-hint">{{ $t('layoutEditor.mapUpdateHint') }}</small>
                </div>
              </div>

              <div class="social-section">
                <h5>{{ $t('layoutEditor.socialLinks') }}</h5>
                <div class="form-row">
                  <div class="form-group">
                    <IluriaLabel>YouTube</IluriaLabel>
                    <IluriaInputText
                      v-model="location.social.youtube"
                      placeholder="https://youtube.com/..."
                      @update:modelValue="updateLocation"
                    />
                  </div>
                  <div class="form-group">
                    <IluriaLabel>Instagram</IluriaLabel>
                    <IluriaInputText
                      v-model="location.social.instagram"
                      placeholder="https://instagram.com/..."
                      @update:modelValue="updateLocation"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <IluriaLabel>Facebook</IluriaLabel>
                    <IluriaInputText
                      v-model="location.social.facebook"
                      placeholder="https://facebook.com/..."
                      @update:modelValue="updateLocation"
                    />
                  </div>
                  <div class="form-group">
                    <IluriaLabel>Twitter</IluriaLabel>
                    <IluriaInputText
                      v-model="location.social.twitter"
                      placeholder="https://twitter.com/..."
                      @update:modelValue="updateLocation"
                    />
                  </div>
                </div>
                <div class="form-row">
                  <div class="form-group">
                    <IluriaLabel>TikTok</IluriaLabel>
                    <IluriaInputText
                      v-model="location.social.tiktok"
                      placeholder="https://tiktok.com/..."
                      @update:modelValue="updateLocation"
                    />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Aba Design -->
      <div v-if="activeTab === 'design'" class="tab-panel">
        
        <!-- Seção Cores -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.backgroundColors') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sectionBackground') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="sectionBackground"
                @update:modelValue="onStyleChange('sectionBackground', $event)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.cardBackground') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="cardBackground"
                @update:modelValue="onStyleChange('cardBackground', $event)"
              />
            </div>
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.textColors') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.titleColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="titleColor"
                @update:modelValue="onStyleChange('titleColor', $event)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.textColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="textColor"
                @update:modelValue="onStyleChange('textColor', $event)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.accentColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                v-model="accentColor"
                @update:modelValue="onStyleChange('accentColor', $event)"
              />
            </div>
          </div>
        </div>

        <!-- Seção Layout -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.spacing') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.sectionPadding') }}</IluriaLabel>
            <IluriaRange
              v-model="sectionPadding"
              :min="0"
              :max="200"
              :step="5"
              @update:modelValue="onStyleChange('sectionPadding', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.cardPadding') }}</IluriaLabel>
            <IluriaRange
              v-model="cardPadding"
              :min="0"
              :max="100"
              :step="5"
              @update:modelValue="onStyleChange('cardPadding', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.itemGap') }}</IluriaLabel>
            <IluriaRange
              v-model="itemGap"
              :min="0"
              :max="100"
              :step="5"
              @update:modelValue="onStyleChange('itemGap', $event + 'px')"
            />
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.borders') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.borderRadius') }}</IluriaLabel>
            <IluriaRange
              v-model="borderRadius"
              :min="0"
              :max="50"
              :step="1"
              @update:modelValue="onStyleChange('borderRadius', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.shadowIntensity') }}</IluriaLabel>
            <IluriaSelect
              v-model="shadowIntensity"
              @update:modelValue="onShadowChange"
              :options="shadowOptions"
            />
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.maxWidth') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.containerMaxWidth') }}</IluriaLabel>
            <IluriaRange
              v-model="maxWidth"
              :min="800"
              :max="2000"
              :step="50"
              @update:modelValue="onStyleChange('maxWidth', $event + 'px')"
            />
          </div>
        </div>

        <!-- Seção Typography -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.titletypography') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.titleFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="titleFontSize"
              :min="20"
              :max="80"
              :step="2"
              @update:modelValue="onStyleChange('titleFontSize', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.titleFontWeight') }}</IluriaLabel>
            <IluriaSelect
              v-model="titleFontWeight"
              @update:modelValue="onStyleChange('titleFontWeight', titleFontWeight)"
              :options="fontWeightOptions"
            />
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.locationNameTypography') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.locationNameFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="locationNameFontSize"
              :min="16"
              :max="48"
              :step="2"
              @update:modelValue="onStyleChange('locationNameFontSize', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.locationNameFontWeight') }}</IluriaLabel>
            <IluriaSelect
              v-model="locationNameFontWeight"
              @update:modelValue="onStyleChange('locationNameFontWeight', locationNameFontWeight)"
              :options="fontWeightOptions"
            />
          </div>
        </div>

        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.bodyText') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.bodyFontSize') }}</IluriaLabel>
            <IluriaRange
              v-model="bodyFontSize"
              :min="12"
              :max="24"
              :step="1"
              @update:modelValue="onStyleChange('bodyFontSize', $event + 'px')"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.lineHeight') }}</IluriaLabel>
            <IluriaRange
              v-model="lineHeight"
              :min="1"
              :max="3"
              :step="0.1"
              @update:modelValue="onStyleChange('lineHeight', $event)"
            />
          </div>
        </div>

        <!-- Botão Reset Estilos -->
        <div class="section">
          <div class="form-group">
            <IluriaButton @click="resetStyles" variant="secondary" color="warning" size="small">
              {{ $t('layoutEditor.resetStyles') }}
            </IluriaButton>
          </div>
        </div>

      </div>

    </div>
  </div>

  <!-- Modal de Seleção de Imagem -->
  <ImageSelectionModal
    v-model:visible="showImageSelector"
    :environment="environment"
    :cropDimensions="{ width: 300, height: 200 }"
    :cropAspectRatio="3/2"
    :enableCropEditor="true"
    @image-selected="onImageSelected"
  />
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import ImageSelectionModal from '@/components/layoutEditor/Common/ImageSelectionModal.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaTextarea from '@/components/Textarea.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed', 'element-updated'])

// Tabs
const tabs = [
  { value: 'general', label: t('layoutEditor.general') },
  { value: 'locations', label: t('layoutEditor.locations') },
  { value: 'design', label: t('layoutEditor.design') }
]

// Estado das abas
const activeTab = ref('general')

// Estados reativos - Conteúdo
const title = ref('Nossas Lojas')
const subtitle = ref('Visite uma de nossas lojas físicas para conhecer nossos produtos de perto e receber atendimento personalizado.')
const layout = ref('grid')
const locations = ref([])

// Estados reativos - Design
// Cores
const sectionBackground = ref('#f8f9fa')
const cardBackground = ref('#ffffff')
const titleColor = ref('#1a1a1a')
const textColor = ref('#666666')
const accentColor = ref('#007bff')

// Layout
const sectionPadding = ref(64)
const cardPadding = ref(32)
const itemGap = ref(48)
const borderRadius = ref(12)
const shadowIntensity = ref('medium')
const maxWidth = ref(1200)

// Typography
const titleFontSize = ref(40)
const titleFontWeight = ref('700')
const locationNameFontSize = ref(28)
const locationNameFontWeight = ref('600')
const bodyFontSize = ref(16)
const lineHeight = ref(1.4)

// Opções para selects
const shadowOptions = [
  { value: 'none', label: 'Nenhuma' },
  { value: 'light', label: 'Leve' },
  { value: 'medium', label: 'Média' },
  { value: 'heavy', label: 'Forte' }
]

const fontWeightOptions = [
  { value: '400', label: 'Normal' },
  { value: '500', label: 'Médio' },
  { value: '600', label: 'Semi-negrito' },
  { value: '700', label: 'Negrito' }
]

// Estados para seleção de imagem
const showImageSelector = ref(false)
const selectedLocationIndex = ref(-1)
const environment = ref('DEVELOP')

// Estados para debounce de endereço
let addressDebounceTimer = null

// Carrega dados do elemento DOM
const loadLocationData = () => {
  if (!props.element) return
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'location') {
      targetElement = props.element.closest('[data-component="location"]')
    }
    
    if (!targetElement) return
    
    // Carregar dados básicos
    title.value = targetElement.getAttribute('data-title') || 'Nossas Lojas'
    subtitle.value = targetElement.getAttribute('data-subtitle') || 'Visite uma de nossas lojas físicas para conhecer nossos produtos de perto e receber atendimento personalizado.'
    layout.value = targetElement.getAttribute('data-layout') || 'grid'
    
    // Carregar localizações dos atributos data
    const locationsData = targetElement.getAttribute('data-locations')
    if (locationsData) {
      try {
        locations.value = JSON.parse(locationsData)
      } catch (error) {
        locations.value = getDefaultLocations()
      }
    } else {
      locations.value = getDefaultLocations()
    }

    // Carregar estilos de design dos atributos data
    sectionBackground.value = targetElement.getAttribute('data-section-background') || '#f8f9fa'
    cardBackground.value = targetElement.getAttribute('data-card-background') || '#ffffff'
    titleColor.value = targetElement.getAttribute('data-title-color') || '#1a1a1a'
    textColor.value = targetElement.getAttribute('data-text-color') || '#666666'
    accentColor.value = targetElement.getAttribute('data-accent-color') || '#007bff'
    
    sectionPadding.value = parseInt(targetElement.getAttribute('data-section-padding')) || 64
    cardPadding.value = parseInt(targetElement.getAttribute('data-card-padding')) || 32
    itemGap.value = parseInt(targetElement.getAttribute('data-item-gap')) || 48
    borderRadius.value = parseInt(targetElement.getAttribute('data-border-radius')) || 12
    shadowIntensity.value = targetElement.getAttribute('data-shadow-intensity') || 'medium'
    maxWidth.value = parseInt(targetElement.getAttribute('data-max-width')) || 1200
    
    titleFontSize.value = parseInt(targetElement.getAttribute('data-title-font-size')) || 40
    titleFontWeight.value = targetElement.getAttribute('data-title-font-weight') || '700'
    locationNameFontSize.value = parseInt(targetElement.getAttribute('data-location-name-font-size')) || 28
    locationNameFontWeight.value = targetElement.getAttribute('data-location-name-font-weight') || '600'
    bodyFontSize.value = parseInt(targetElement.getAttribute('data-body-font-size')) || 16
    lineHeight.value = parseFloat(targetElement.getAttribute('data-line-height')) || 1.4
  } catch (error) {
    resetData()
  }
}

// Dados padrão
const getDefaultLocations = () => [
  {
    id: 1,
    name: 'Loja Principal - Centro',
    description: 'Nossa loja principal no centro da cidade, com todo o catálogo de produtos disponível.',
    address: 'Rua Augusta, 123 - Centro, São Paulo - SP, 01305-000',
    phone: '+55 (11) 3333-4444',
    email: '<EMAIL>',
    hours: 'Segunda à Sexta: 9h às 18h | Sábado: 9h às 17h',
    image: 'https://mercadoeconsumo.com.br/wp-content/smush-webp/2021/04/SHOPPING-CENTER-NORTE-1140x570.jpg.webp',
    hasMap: false,
    social: { 
      youtube: 'https://youtube.com/@minhaloja', 
      instagram: 'https://instagram.com/minhaloja',
      facebook: 'https://facebook.com/minhaloja',
      twitter: 'https://twitter.com/minhaloja', 
      tiktok: 'https://tiktok.com/@minhaloja' 
    }
  }
]

const resetData = () => {
  title.value = 'Nossas Lojas'
  subtitle.value = 'Visite uma de nossas lojas físicas para conhecer nossos produtos de perto e receber atendimento personalizado.'
  layout.value = 'grid'
  locations.value = getDefaultLocations()
}

// Função para limpar mapas renderizados antes de salvar
const clearRenderedMaps = (targetElement) => {
  const mapElements = targetElement.querySelectorAll('.location-map')
  mapElements.forEach(mapEl => {
    mapEl.innerHTML = ''
    mapEl.removeAttribute('data-map-enabled')
    mapEl.removeAttribute('data-map-address')
    mapEl.removeAttribute('data-map-location-name')
  })
}

// Salva dados no elemento DOM
const saveLocationData = () => {
  if (!props.element) return false
  
  try {
    let targetElement = props.element
    if (!targetElement.getAttribute('data-component') || 
        targetElement.getAttribute('data-component') !== 'location') {
      targetElement = props.element.closest('[data-component="location"]')
    }
    
    if (!targetElement) return false
    
    // Limpar mapas renderizados antes de salvar
    clearRenderedMaps(targetElement)
    
    // Salvar dados básicos
    targetElement.setAttribute('data-title', title.value)
    targetElement.setAttribute('data-subtitle', subtitle.value)
    targetElement.setAttribute('data-layout', layout.value)
    targetElement.setAttribute('data-locations', JSON.stringify(locations.value))
    
    // Salvar estilos de design nos atributos data
    targetElement.setAttribute('data-section-background', sectionBackground.value)
    targetElement.setAttribute('data-card-background', cardBackground.value)
    targetElement.setAttribute('data-title-color', titleColor.value)
    targetElement.setAttribute('data-text-color', textColor.value)
    targetElement.setAttribute('data-accent-color', accentColor.value)
    
    targetElement.setAttribute('data-section-padding', sectionPadding.value.toString())
    targetElement.setAttribute('data-card-padding', cardPadding.value.toString())
    targetElement.setAttribute('data-item-gap', itemGap.value.toString())
    targetElement.setAttribute('data-border-radius', borderRadius.value.toString())
    targetElement.setAttribute('data-shadow-intensity', shadowIntensity.value)
    targetElement.setAttribute('data-max-width', maxWidth.value.toString())
    
    targetElement.setAttribute('data-title-font-size', titleFontSize.value.toString())
    targetElement.setAttribute('data-title-font-weight', titleFontWeight.value)
    targetElement.setAttribute('data-location-name-font-size', locationNameFontSize.value.toString())
    targetElement.setAttribute('data-location-name-font-weight', locationNameFontWeight.value)
    targetElement.setAttribute('data-body-font-size', bodyFontSize.value.toString())
    targetElement.setAttribute('data-line-height', lineHeight.value.toString())
    
    // Atualizar conteúdo visual
    updateElementContent(targetElement)
    
    // Aplicar estilos visuais
    applyAllStyles(targetElement)
    
    return true
  } catch (error) {
    return false
  }
}

// Atualização do conteúdo do elemento
const updateElementContent = (targetElement) => {
  if (!targetElement) return
  
  // Atualizar título e subtítulo
  const titleEl = targetElement.querySelector('.location-title')
  const subtitleEl = targetElement.querySelector('.location-subtitle')
  
  if (titleEl) titleEl.textContent = title.value
  if (subtitleEl) subtitleEl.textContent = subtitle.value
  
  // Atualizar classe do layout
  const gridEl = targetElement.querySelector('.locations-grid')
  if (gridEl) {
    // Remover classes antigas de layout
    gridEl.classList.remove('layout-grid', 'layout-list', 'layout-cards')
    // Adicionar nova classe de layout
    gridEl.classList.add(`layout-${layout.value}`)
    
    // Regenerar HTML das localizações (versão simplificada para evitar problemas)
    gridEl.innerHTML = locations.value.map(location => `
      <div class="location-item ${location.hasMap ? 'map-location' : ''}" data-location-id="${location.id}">
        ${!location.hasMap && location.image ? `
          <div class="location-image">
            <img src="${location.image}" alt="${location.name}" class="location-photo">
          </div>
        ` : ''}
        <div class="location-content">
          <h2 class="location-name">${location.name}</h2>
          ${location.description ? `<p class="location-description">${location.description}</p>` : ''}
          
          <div class="location-details">
            ${location.address ? `
              <div class="detail-item">
                <strong>Endereço</strong>
                <p class="location-address">${location.address}</p>
                <a href="https://www.google.com/maps/search/?api=1&query=${encodeURIComponent(location.address)}" target="_blank" class="get-directions">Como chegar →</a>
              </div>
            ` : ''}
            
            ${location.hours ? `
              <div class="detail-item">
                <strong>Horário de Funcionamento</strong>
                <p class="location-hours">${location.hours}</p>
              </div>
            ` : ''}
            
            ${location.phone || location.email ? `
              <div class="detail-item">
                <strong>Entre em contato</strong>
                ${location.phone ? `<p class="location-phone">${location.phone}</p>` : ''}
                ${location.email ? `<p class="location-email">${location.email}</p>` : ''}
              </div>
            ` : ''}
          </div>
        </div>
        ${location.hasMap ? `<div class="location-map"></div>` : ''}
      </div>
    `).join('')
  }
}

// Aplicação de estilos visuais
const applyAllStyles = (targetElement) => {
  if (!targetElement) return
  
  const container = targetElement.querySelector('.location-container')
  const locationItems = targetElement.querySelectorAll('.location-item')
  const titleEl = targetElement.querySelector('.location-title')
  const subtitleEl = targetElement.querySelector('.location-subtitle')
  const locationNames = targetElement.querySelectorAll('.location-name')
  const textElements = targetElement.querySelectorAll('.location-description, .detail-item p')
  const grid = targetElement.querySelector('.locations-grid')
  
  // Aplicar estilos de seção
  targetElement.style.background = sectionBackground.value
  targetElement.style.padding = sectionPadding.value + 'px'
  
  // Aplicar estilos de cards
  locationItems.forEach(item => {
    item.style.background = cardBackground.value
    item.style.borderRadius = borderRadius.value + 'px'
    
    const shadows = {
      none: 'none',
      light: '0 2px 4px rgba(0, 0, 0, 0.1)',
      medium: '0 4px 6px rgba(0, 0, 0, 0.1)',
      heavy: '0 10px 25px rgba(0, 0, 0, 0.15)'
    }
    item.style.boxShadow = shadows[shadowIntensity.value]
  })
  
  // Aplicar estilos de texto
  if (titleEl) {
    titleEl.style.color = titleColor.value
    titleEl.style.fontSize = titleFontSize.value + 'px'
    titleEl.style.fontWeight = titleFontWeight.value
  }
  
  if (subtitleEl) {
    subtitleEl.style.color = textColor.value
    subtitleEl.style.fontSize = bodyFontSize.value + 'px'
    subtitleEl.style.lineHeight = lineHeight.value
  }
  
  locationNames.forEach(el => {
    el.style.fontSize = locationNameFontSize.value + 'px'
    el.style.fontWeight = locationNameFontWeight.value
  })
  
  textElements.forEach(el => {
    el.style.color = textColor.value
    el.style.fontSize = bodyFontSize.value + 'px'
    el.style.lineHeight = lineHeight.value
  })
  
  // Aplicar estilos de layout
  if (container) container.style.maxWidth = maxWidth.value + 'px'
  if (grid) grid.style.gap = itemGap.value + 'px'
  
  const contentElements = targetElement.querySelectorAll('.location-content')
  contentElements.forEach(el => {
    el.style.padding = cardPadding.value + 'px'
  })
  
  // Aplicar cor de destaque
  const accentElements = targetElement.querySelectorAll('.get-directions')
  accentElements.forEach(el => {
    el.style.color = accentColor.value
  })
}

// Manipuladores de eventos de design
const onStyleChange = (property, value) => {
  emit('data-changed', { [property]: value })
  
  // NOVO: Emitir evento para o sistema de undo/redo
  emit('element-updated', {
    element: props.element,
    data: { [property]: value },
    description: null, // Será detectado automaticamente baseado na propriedade
    type: 'style-change'
  })
  
  // Auto-save com debounce (mantido para compatibilidade)
  setTimeout(() => {
    saveLocationData()
  }, 300)
}

const onShadowChange = () => {
  onStyleChange('shadowIntensity', shadowIntensity.value)
}

// Reset de estilos
const resetStyles = () => {
  sectionBackground.value = '#f8f9fa'
  cardBackground.value = '#ffffff'
  titleColor.value = '#1a1a1a'
  textColor.value = '#666666'
  accentColor.value = '#007bff'
  sectionPadding.value = 64
  cardPadding.value = 32
  itemGap.value = 48
  borderRadius.value = 12
  shadowIntensity.value = 'medium'
  maxWidth.value = 1200
  titleFontSize.value = 40
  titleFontWeight.value = '700'
  locationNameFontSize.value = 28
  locationNameFontWeight.value = '600'
  bodyFontSize.value = 16
  lineHeight.value = 1.4
  
  saveLocationData()
}

// Gerenciamento de localizações
const addLocation = () => {
  const newId = Math.max(...locations.value.map(l => l.id), 0) + 1
  locations.value.push({
    id: newId,
    name: 'Nova Localização',
    description: '',
    address: '',
    phone: '',
    email: '',
    hours: '',
    image: '',
    hasMap: false,
    social: { 
      youtube: '', 
      instagram: '',
      facebook: '',
      twitter: '', 
      tiktok: '' 
    }
  })
  updateLocation()
}

const removeLocation = (index) => {
  locations.value.splice(index, 1)
  updateLocation()
}

// Abrir seletor de imagem
const openImageSelector = (index) => {
  selectedLocationIndex.value = index
  showImageSelector.value = true
}

// Callback quando uma imagem é selecionada
const onImageSelected = (imageData) => {
  if (selectedLocationIndex.value >= 0 && selectedLocationIndex.value < locations.value.length) {
    locations.value[selectedLocationIndex.value].image = imageData.url
    updateLocation()
  }
  showImageSelector.value = false
  selectedLocationIndex.value = -1
}

// Remover imagem
const removeImage = (index) => {
  if (index >= 0 && index < locations.value.length) {
    locations.value[index].image = ''
    updateLocation()
  }
}

// Manipulador específico para mudança de endereço
const onAddressChange = () => {
  // Limpar timer anterior
  if (addressDebounceTimer) {
    clearTimeout(addressDebounceTimer)
  }
  
  // Criar novo timer com debounce maior para endereços
  addressDebounceTimer = setTimeout(() => {
    updateLocation()
  }, 2000) // 2 segundos de debounce para endereços
}

// Manipulador para toggle do mapa
const onMapToggle = (index) => {
  updateLocation()
}

// Função para atualizar mapa manualmente
const updateLocationMap = async (index) => {
  const location = locations.value[index]
  if (!location || !location.hasMap || !location.address) {
    return
  }
  
  // Forçar atualização do mapa
  setTimeout(async () => {
    try {
      const targetElement = props.element?.closest('[data-component="location"]') || props.element
      if (targetElement) {
        const LocationConfigModule = await import('@/components/layoutEditor/configs/location.config.js')
        const LocationConfig = LocationConfigModule.default
        
        const mapElement = targetElement.querySelector(`[data-location-id="${location.id}"] .location-map`)
        if (mapElement && LocationConfig.renderGoogleMapsDirectly) {
          // Limpar mapa existente
          mapElement.innerHTML = ''
          // Renderizar novo mapa
          LocationConfig.renderGoogleMapsDirectly(mapElement, location)
        }
      }
    } catch (error) {
      // Silently handle error
    }
  }, 100)
}

// Atualiza o location
const updateLocation = () => {
  if (!props.element) return

  try {
    // Salvar dados
    const success = saveLocationData()
    
    if (success) {
      const changeData = {
        title: title.value,
        subtitle: subtitle.value,
        layout: layout.value,
        locations: locations.value
      }
      
      // Notifica mudanças (mantido para compatibilidade)
      emit('data-changed', changeData)
      
      // NOVO: Emitir evento para o sistema de undo/redo
      emit('element-updated', {
        element: props.element,
        data: changeData,
        description: null, // Será detectado automaticamente
        type: 'content-change'
      })
    }
  } catch (error) {
    // Silently handle error
  }
}

// Lifecycle
onMounted(() => {
  loadLocationData()
})
</script>

<style scoped>
.location-sidebar-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Tabs */
.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  overflow-x: auto;
  flex-shrink: 0;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-bg);
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
}

.section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.form-group {
  margin-bottom: 1rem;
}

.form-row {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 1rem;
}

/* Locations Header */
.locations-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
}

.locations-header h3 {
  margin: 0;
  color: var(--iluria-color-text-primary);
  font-size: 1.1rem;
}

/* Locations List */
.locations-list {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.location-editor-item {
  background: var(--iluria-color-card-bg);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid var(--iluria-color-border);
}

.location-item-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1.5rem;
  padding-bottom: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.location-item-header h4 {
  margin: 0;
  color: var(--iluria-color-text-primary);
  font-size: 1rem;
}

.location-form {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

/* Image Input */
.image-input-group {
  display: flex;
  gap: 0.75rem;
  align-items: flex-end;
}

.image-url-input {
  flex: 1;
}

.select-image-btn {
  white-space: nowrap;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.image-preview {
  margin-top: 0.75rem;
  position: relative;
  display: inline-block;
  border-radius: 8px;
  overflow: hidden;
  max-width: 200px;
}

.image-preview img {
  width: 100%;
  height: auto;
  max-height: 120px;
  object-fit: cover;
  display: block;
}

.remove-image-btn-custom {
  position: absolute !important;
  top: 0.5rem !important;
  right: 0.5rem !important;
  width: 24px !important;
  height: 24px !important;
  min-width: 24px !important;
  min-height: 24px !important;
  padding: 0 !important;
  border-radius: 50% !important;
}

/* Social Section */
.social-section {
  margin-top: 1rem;
  padding-top: 1rem;
  border-top: 1px solid var(--iluria-color-border);
}

.social-section h5 {
  margin: 0 0 1rem 0;
  color: var(--iluria-color-text-primary);
  font-size: 0.9rem;
}

/* Map Update Section */
.map-update-section {
  margin-top: 0.75rem;
  padding: 0.75rem;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 6px;
  border: 1px solid var(--iluria-color-border);
}

.update-map-btn {
  width: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  margin-bottom: 0.5rem;
}

.map-hint {
  color: var(--iluria-color-text-secondary);
  font-size: 0.8rem;
  text-align: center;
  display: block;
}

/* Design Tab Styles */
.color-input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-panel {
    padding: 0.75rem;
  }
  
  .form-row {
    grid-template-columns: 1fr;
  }
  
  .locations-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .location-item-header {
    flex-direction: column;
    gap: 1rem;
    align-items: stretch;
  }
  
  .image-input-group {
    flex-direction: column;
    align-items: stretch;
  }
}
</style> 
