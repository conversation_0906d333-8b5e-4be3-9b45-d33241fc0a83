<template>
  <div class="page-container">
    <!-- Page Header -->
    <IluriaHeader
      :title="isEditing ? t('measurementTable.editTable') : t('measurementTable.createNew')"
      :subtitle="isEditing ? t('measurementTable.editSubtitle') : t('measurementTable.createSubtitle')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="isEditing ? t('save') : t('create')"
      @cancel-click="router.back()"
      @save-click="saveTable"
    />

    <!-- Loading State -->
    <div v-if="loading" class="loading-state">
      <Spinner />
      <span>{{ t('product.loading') }}</span>
    </div>

    <!-- Main Content -->
    <ViewContainer 
      v-else
      :title="isEditing ? t('measurementTable.editForm') : t('measurementTable.createForm')" 
      :icon="RulerIcon"
      iconColor="green"
      :subtitle="t('measurementTable.formSubtitle', 'Configure os dados da tabela de medidas')"
    >
      <MeasurementTableFormInline 
        ref="formRef"
        v-model="form" 
        :measurement-table-id="isEditing ? tableId : null"
      />
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, computed, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { FloppyDiskIcon, RulerIcon } from '@hugeicons-pro/core-bulk-rounded'

import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import Spinner from '@/components/Spinner.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import MeasurementTableFormInline from '@/components/measurementTables/MeasurementTableFormInline.vue'
import { measurementTableApi } from '@/services/measurementTable.service'

const { t } = useI18n()
const router = useRouter()
const route = useRoute()
const toast = useToast()

const loading = ref(false)
const saving = ref(false)
const formRef = ref(null)

const form = ref({
  name: '',
  type: 'STRUCTURED',
  imageUrl: '',
  columns: [],
  rows: [],
  newImageFile: null
})

const isEditing = computed(() => !!route.params.id)
const tableId = computed(() => route.params.id)

const loadTable = async () => {
  if (!isEditing.value) {
    resetForm()
    return
  }
  
  try {
    loading.value = true
    const table = await measurementTableApi.getById(tableId.value)
    
    form.value = {
      name: table.name || '',
      type: table.type || 'STRUCTURED',
      imageUrl: table.imageUrl || '',
      columns: table.columns || [],
      rows: table.rows || [],
      newImageFile: null
    }
  } catch (error) {
    toast.showError(t('measurementTable.errorLoading'))
    router.back()
  } finally {
    loading.value = false
  }
}

const saveTable = async () => {
  try {
    saving.value = true

    if (!form.value.name || form.value.name.trim() === '') {
      toast.showError(t('measurementTable.nameRequired') || 'Nome é obrigatório')
      return
    }
    const dataToSave = {
      name: form.value.name.trim(),
      type: form.value.type,
      columns: form.value.columns?.map(col => ({
        ...(isEditing.value && col.id && !col.id.startsWith('col_') ? { id: col.id } : {}),
        label: col.label,
        unit: col.unit,
        position: col.position,
        imageUrl: col.imageUrl && !col.imageUrl.startsWith('data:') ? col.imageUrl : ''
      })) || [],
      rows: form.value.rows?.map(row => ({
        ...(isEditing.value && row.id && !row.id.startsWith('row_') ? { id: row.id } : {}),
        sizeLabel: row.sizeLabel,
        position: row.position,
        values: row.values?.map(val => ({
          ...(isEditing.value && val.id && !val.id.startsWith('val_') ? { id: val.id } : {}),
          value: val.value
        })) || []
      })) || [],
      // Só incluir imageUrl se não for um preview local (data:)
      imageUrl: form.value.imageUrl && !form.value.imageUrl.startsWith('data:') ? form.value.imageUrl : ''
    }
    
    if (isEditing.value) {
      // Atualizar tabela existente
      await measurementTableApi.update(tableId.value, dataToSave)
      toast.showSuccess(t('measurementTable.updatedSuccess'))
      
      // Se tem imagem pendente, fazer upload
      if (form.value.newImageFile) {
        try {
          await formRef.value?.uploadPendingImage()
        } catch (error) {
          // Upload da imagem falhou, mas dados foram salvos
        }
      }
    } else {
      // Criar nova tabela
      const response = await measurementTableApi.create(dataToSave)
      const newTableId = response.data.id
      
      toast.showSuccess(t('measurementTable.createdSuccess'))
      
      // Upload da imagem principal se houver
      if (form.value.newImageFile) {
        try {
          await measurementTableApi.uploadImage(newTableId, form.value.newImageFile)
          toast.showSuccess(t('measurementTable.imageUploadSuccess'))
        } catch (error) {
          toast.showError(t('measurementTable.imageUploadError'))
        }
      }
      
      // Upload das imagens das colunas se houver (usar upload em lote)
      if (form.value.pendingColumnImages && Object.keys(form.value.pendingColumnImages).length > 0) {
        try {
          await measurementTableApi.batchUploadColumnImages(newTableId, form.value.pendingColumnImages)
          toast.showSuccess(t('measurementTable.columnImageSaved'))
        } catch (error) {
          toast.showError(t('measurementTable.imageUploadError'))
        }
      }
    }
    
    // Sempre redirecionar para a lista
    router.push('/measurement-tables')
    
  } catch (error) {
    const errorMessage = isEditing.value 
      ? t('measurementTable.errorUpdating')
      : t('measurementTable.errorCreating')
    toast.showError(errorMessage)
  } finally {
    saving.value = false
  }
}

// Função para resetar o form
const resetForm = () => {
  form.value = {
    name: '',
    type: 'STRUCTURED',
    imageUrl: '',
    columns: [],
    rows: [],
    newImageFile: null
  }
}

onMounted(() => {
  loadTable()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
}



/* Loading State */
.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  gap: 16px;
  padding: 64px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  

}


</style>

<div class="flex items-center justify-center bg-gray-50 rounded-lg border-2 border-dashed border-gray-300 aspect-[4/3] max-h-64">
</div>

 
