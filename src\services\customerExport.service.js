import axios from 'axios';
import { API_URLS } from '../config/api.config';

class CustomerExportService {
    #endpoint = `${API_URLS.CUSTOMER_EXPORT_BASE_URL || 'http://localhost:8081/customers/exports'}`;

    // Create a new export job
    async createExport(fileType, selectedFields) {
        try {
            const response = await axios.post(this.#endpoint, {
                fileType,
                selectedFields
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.data) {
                throw new Error('No response data received');
            }

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Get list of export jobs
    async getExports(page = 0, size = 10) {
        try {
            const response = await axios.get(this.#endpoint, {
                params: { page, size }
            });
            return {
                content: response.data.content || [],
                totalPages: response.data.totalPages || 0,
                totalElements: response.data.totalElements || 0
            };
        } catch (error) {
            throw error;
        }
    }

    // Get a specific export job
    async getExport(id) {
        try {
            const response = await axios.get(`${this.#endpoint}/${id}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Generate secure download token
    async generateDownloadToken(id) {
        try {
            const response = await axios.get(`${this.#endpoint}/${id}/download-token`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Download file using secure token
    async downloadExportFile(id, fileName) {
        try {
            // First, generate a secure download token
            const tokenResponse = await this.generateDownloadToken(id);
            const downloadToken = tokenResponse.downloadToken;

            // Then download using the token
            const response = await axios.get(`${this.#endpoint}/download/${downloadToken}`, {
                responseType: 'blob',
                timeout: 60000 // 60 seconds timeout for large files
            });

            // Create blob link to download
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            throw error;
        }
    }

    // Delete an export job
    async deleteExport(id) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${id}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Get available fields for export
    async getAvailableFields() {
        try {
            const response = await axios.get(`${this.#endpoint}/fields`);
            return response.data;
        } catch (error) {
            // Return default fields structure in case of error
            return {
                basic: [
                    'name',
                    'email',
                    'phone',
                    'document',
                    'dayOfBirth',
                    'active',
                    'creationDate'
                ],
                addresses: ['address']
            };
        }
    }

    // Get total customer count for export preview
    async getCustomerCount() {
        try {
            const response = await axios.get(`${this.#endpoint}/count`);
            return response.data.count || 0;
        } catch (error) {
            return 0;
        }
    }



    // Format file size for display
    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Get status display text
    getStatusText(status) {
        const statusMap = {
            'PENDING': 'Pendente',
            'IN_PROGRESS': 'Em andamento',
            'COMPLETED': 'Concluído',
            'FAILED': 'Falhou'
        };
        return statusMap[status] || status;
    }

    // Get status color for UI
    getStatusColor(status) {
        const colorMap = {
            'PENDING': 'warning',
            'IN_PROGRESS': 'info',
            'COMPLETED': 'success',
            'FAILED': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    // Check if export can be downloaded
    canDownload(exportJob) {
        return exportJob.status === 'COMPLETED' && exportJob.fileSize > 0;
    }

    // Check if export can be deleted
    canDelete(exportJob) {
        return ['COMPLETED', 'FAILED'].includes(exportJob.status);
    }

    // Get field display name
    getFieldDisplayName(fieldName) {
        const fieldMap = {
            'name': 'Nome',
            'email': 'E-mail',
            'phone': 'Telefone',
            'document': 'Documento',
            'documentType': 'Tipo de Documento',
            'dayOfBirth': 'Data de Nascimento',
            'defaultLanguage': 'Idioma Padrão',
            'allowsEmailMarketing': 'Permite Marketing por E-mail',
            'active': 'Ativo',
            'creationDate': 'Data de Criação',
            'address': 'Endereço',
            'nickname': 'Apelido'
        };
        return fieldMap[fieldName] || fieldName;
    }
}

export default new CustomerExportService(); 