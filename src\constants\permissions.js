/**
 * Enum de permissões do sistema - espelha Permission.java do backend
 * Cada permissão pode ser atribuída a diferentes cargos da equipe.
 */
export const PERMISSIONS = {
  // Gerenciamento da Loja
  STORE_ADMIN: { code: 'store.admin', description: 'Administração completa da loja' },
  STORE_SETTINGS: { code: 'store.settings', description: 'Gerenciar configurações da loja' },
  STORE_VIEW: { code: 'store.view', description: 'Visualizar informações da loja' },
  
  // Gerenciamento de Produtos
  PRODUCT_CREATE: { code: 'product.create', description: 'Criar produtos' },
  PRODUCT_EDIT: { code: 'product.edit', description: 'Editar produtos' },
  PRODUCT_DELETE: { code: 'product.delete', description: 'Excluir produtos' },
  PRODUCT_VIEW: { code: 'product.view', description: 'Visualizar produtos' },
  PRODUCT_CATEGORY_MANAGE: { code: 'product.category.manage', description: 'Gerenciar categorias' },
  
  // Gerenciamento de Pedidos
  ORDER_VIEW: { code: 'order.view', description: 'Visualizar pedidos' },
  ORDER_EDIT: { code: 'order.edit', description: 'Editar pedidos' },
  ORDER_CANCEL: { code: 'order.cancel', description: 'Cancelar pedidos' },
  ORDER_EXPORT: { code: 'order.export', description: 'Exportar pedidos' },
  ORDER_STATUS_CHANGE: { code: 'order.status.change', description: 'Alterar status de pedidos' },
  
  // Gerenciamento de Clientes
  CUSTOMER_VIEW: { code: 'customer.view', description: 'Visualizar clientes' },
  CUSTOMER_EDIT: { code: 'customer.edit', description: 'Editar clientes' },
  CUSTOMER_DELETE: { code: 'customer.delete', description: 'Excluir clientes' },
  CUSTOMER_EXPORT: { code: 'customer.export', description: 'Exportar dados de clientes' },
  
  // Gerenciamento Financeiro
  FINANCIAL_VIEW: { code: 'financial.view', description: 'Visualizar dados financeiros' },
  FINANCIAL_REPORTS: { code: 'financial.reports', description: 'Acessar relatórios financeiros' },
  FINANCIAL_EXPORT: { code: 'financial.export', description: 'Exportar dados financeiros' },
  
  // Marketing e Promoções
  PROMOTION_CREATE: { code: 'promotion.create', description: 'Criar promoções' },
  PROMOTION_EDIT: { code: 'promotion.edit', description: 'Editar promoções' },
  PROMOTION_DELETE: { code: 'promotion.delete', description: 'Excluir promoções' },
  PROMOTION_VIEW: { code: 'promotion.view', description: 'Visualizar promoções' },
  
  // Layout e Design
  LAYOUT_EDIT: { code: 'layout.edit', description: 'Editar layout da loja' },
  LAYOUT_PUBLISH: { code: 'layout.publish', description: 'Publicar alterações de layout' },
  LAYOUT_TEMPLATE_MANAGE: { code: 'layout.template.manage', description: 'Gerenciar templates' },
  
  // Gerenciamento de Arquivos
  FILE_UPLOAD: { code: 'file.upload', description: 'Upload de arquivos' },
  FILE_DELETE: { code: 'file.delete', description: 'Excluir arquivos' },
  FILE_MANAGE: { code: 'file.manage', description: 'Gerenciar arquivos da loja' },
  
  // Analytics e Relatórios
  ANALYTICS_VIEW: { code: 'analytics.view', description: 'Visualizar analytics' },
  REPORTS_GENERATE: { code: 'reports.generate', description: 'Gerar relatórios' },
  REPORTS_EXPORT: { code: 'reports.export', description: 'Exportar relatórios' },
  
  // Gerenciamento de Equipe
  TEAM_VIEW: { code: 'team.view', description: 'Visualizar membros da equipe' },
  TEAM_INVITE: { code: 'team.invite', description: 'Convidar membros' },
  TEAM_EDIT: { code: 'team.edit', description: 'Editar membros da equipe' },
  TEAM_REMOVE: { code: 'team.remove', description: 'Remover membros da equipe' },
  
  // Configurações de Pagamento
  PAYMENT_SETTINGS: { code: 'payment.settings', description: 'Configurar métodos de pagamento' },
  PAYMENT_VIEW: { code: 'payment.view', description: 'Visualizar configurações de pagamento' },
  
  // Configurações de Frete
  SHIPPING_SETTINGS: { code: 'shipping.settings', description: 'Configurar opções de frete' },
  SHIPPING_VIEW: { code: 'shipping.view', description: 'Visualizar configurações de frete' },

  // Notificações
  NOTIFICATION_NEW_SALES: { code: 'notification.newSales', description: 'Receber notificações de novas vendas' },
  NOTIFICATION_PRODUCT_REVIEWS: { code: 'notification.productReviews', description: 'Receber notificações de avaliações de produtos' },
  NOTIFICATION_PRODUCT_QUESTIONS: { code: 'notification.productQuestions', description: 'Receber notificações de perguntas sobre produtos' },
  NOTIFICATION_NEWSLETTER_SUBSCRIPTIONS: { code: 'notification.newsletterSubscriptions', description: 'Receber notificações de novas inscrições na newsletter' },
  NOTIFICATION_NEW_CUSTOMER_REGISTRATIONS: { code: 'notification.newCustomerRegistrations', description: 'Receber notificações de novos cadastros de clientes' },
};

/**
 * Array com todos os códigos de permissões para facilitar validações
 */
export const PERMISSION_CODES = Object.values(PERMISSIONS).map(p => p.code);

/**
 * Função helper para buscar permissão por código
 * @param {string} code - Código da permissão
 * @returns {object|null} Objeto da permissão ou null se não encontrada
 */
export function getPermissionByCode(code) {
  return Object.values(PERMISSIONS).find(p => p.code === code) || null;
}

/**
 * Função helper para validar se um código de permissão é válido
 * @param {string} code - Código da permissão
 * @returns {boolean} True se o código é válido
 */
export function isValidPermissionCode(code) {
  return PERMISSION_CODES.includes(code);
}

/**
 * Grupos de permissões para facilitar categorização na UI
 */
export const PERMISSION_GROUPS = {
  STORE: {
    name: 'Loja',
    permissions: [PERMISSIONS.STORE_ADMIN, PERMISSIONS.STORE_SETTINGS, PERMISSIONS.STORE_VIEW]
  },
  PRODUCTS: {
    name: 'Produtos',
    permissions: [
      PERMISSIONS.PRODUCT_CREATE, PERMISSIONS.PRODUCT_EDIT, PERMISSIONS.PRODUCT_DELETE,
      PERMISSIONS.PRODUCT_VIEW, PERMISSIONS.PRODUCT_CATEGORY_MANAGE
    ]
  },
  ORDERS: {
    name: 'Pedidos',
    permissions: [
      PERMISSIONS.ORDER_VIEW, PERMISSIONS.ORDER_EDIT, PERMISSIONS.ORDER_CANCEL,
      PERMISSIONS.ORDER_EXPORT, PERMISSIONS.ORDER_STATUS_CHANGE
    ]
  },
  CUSTOMERS: {
    name: 'Clientes',
    permissions: [
      PERMISSIONS.CUSTOMER_VIEW, PERMISSIONS.CUSTOMER_EDIT, PERMISSIONS.CUSTOMER_DELETE,
      PERMISSIONS.CUSTOMER_EXPORT
    ]
  },
  FINANCIAL: {
    name: 'Financeiro',
    permissions: [
      PERMISSIONS.FINANCIAL_VIEW, PERMISSIONS.FINANCIAL_REPORTS, PERMISSIONS.FINANCIAL_EXPORT
    ]
  },
  MARKETING: {
    name: 'Marketing',
    permissions: [
      PERMISSIONS.PROMOTION_CREATE, PERMISSIONS.PROMOTION_EDIT, PERMISSIONS.PROMOTION_DELETE,
      PERMISSIONS.PROMOTION_VIEW
    ]
  },
  LAYOUT: {
    name: 'Layout',
    permissions: [
      PERMISSIONS.LAYOUT_EDIT, PERMISSIONS.LAYOUT_PUBLISH, PERMISSIONS.LAYOUT_TEMPLATE_MANAGE
    ]
  },
  FILES: {
    name: 'Arquivos',
    permissions: [
      PERMISSIONS.FILE_UPLOAD, PERMISSIONS.FILE_DELETE, PERMISSIONS.FILE_MANAGE
    ]
  },
  ANALYTICS: {
    name: 'Analytics',
    permissions: [
      PERMISSIONS.ANALYTICS_VIEW, PERMISSIONS.REPORTS_GENERATE, PERMISSIONS.REPORTS_EXPORT
    ]
  },
  TEAM: {
    name: 'Equipe',
    permissions: [
      PERMISSIONS.TEAM_VIEW, PERMISSIONS.TEAM_INVITE, PERMISSIONS.TEAM_EDIT, PERMISSIONS.TEAM_REMOVE
    ]
  },
  PAYMENT: {
    name: 'Pagamento',
    permissions: [
      PERMISSIONS.PAYMENT_SETTINGS, PERMISSIONS.PAYMENT_VIEW
    ]
  },
  SHIPPING: {
    name: 'Frete',
    permissions: [
      PERMISSIONS.SHIPPING_SETTINGS, PERMISSIONS.SHIPPING_VIEW
    ]
  }
};