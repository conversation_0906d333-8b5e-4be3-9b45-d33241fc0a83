{"customersTitle": "Customers", "customersSubtitle": "Manage your customer base and their information", "customersListTitle": "Customer List", "customersListSubtitle": "View and manage all registered customers", "new": "New Customer", "newCustomer": "New Customer", "searchPlaceholder": "Search customers...", "loadingCustomers": "Loading customers...", "noCustomers": "No customers found", "noCustomersDescription": "You don't have any registered customers yet. Add the first customer to get started.", "createFirstCustomer": "Create First Customer", "imageHeader": "Image", "nameHeader": "Name", "emailHeader": "Email", "phoneHeader": "Mobile", "createdAt": "Created At", "expiresAt": "Expires At", "actionsHeader": "Actions", "noPhone": "No mobile", "editCustomer": "Edit customer", "deleteCustomer": "Delete customer", "viewCustomer": "View customer", "confirmDeleteTitle": "Confirm deletion", "confirmDeleteMessage": "Are you sure you want to delete customer {name}? This action cannot be undone.", "deleteSuccess": "Customer deleted successfully", "deleteError": "Error deleting customer", "loadError": "Error loading customers", "createTitle": "Register New Customer", "createSubtitle": "Add a new customer to the system", "editTitle": "Edit Customer", "editSubtitle": "Update customer information", "basicDataTitle": "Customer Data", "basicDataSubtitle": "Main customer information", "addressTitle": "Addresses", "addressSubtitle": "Manage customer addresses", "typeLabel": "Customer Type", "cpfLabel": "Individual (CPF)", "cnpjLabel": "Company (CNPJ)", "documentLabel": "Document", "statusLabel": "Blocked Customer", "activeStatus": "Active Customer", "inactiveStatus": "Inactive Customer", "nameLabel": "Full Name", "emailLabel": "Email", "phoneLabel": "Mobile", "birthDateLabel": "Date of Birth", "marketingLabel": "Marketing", "marketingDescription": "Accepts promotional messages by email", "phoneMarketingDescription": "Accepts promotional messages via phone (SMS/Whatsapp)", "photoLabel": "Customer Photo", "selectPhoto": "Select Photo", "changePhoto": "Change Photo", "removePhoto": "Remove Photo", "photoFormatHint": "Accepted formats: JPG, PNG, GIF (max. 2MB)", "photoUploadError": "Error uploading photo. Customer saved without photo.", "save": "Save", "update": "Update", "cancel": "Cancel", "createSuccess": "Customer created successfully", "updateSuccess": "Customer updated successfully", "saveError": "Error saving customer", "status": {"active": "Active", "inactive": "Inactive", "banned": "Banned", "pending": "Pending"}, "fields": {"name": "Name", "email": "Email", "phone": "Mobile", "status": "Status", "registrationDate": "Registration Date", "lastLogin": "Last Login", "orders": "Orders", "totalSpent": "Total Spent", "actions": "Actions", "billingAddress": "Billing Address", "shippingAddress": "Shipping Address", "address1": "Address Line 1", "address2": "Address Line 2", "city": "City", "state": "State/Province", "postalCode": "Postal Code", "country": "Country", "company": "Company", "taxNumber": "Tax Number", "notes": "Notes"}, "tabs": {"overview": "Overview", "orders": "Orders", "addresses": "Addresses", "activity": "Activity", "notes": "Notes"}, "messages": {"loadError": "Error loading customers", "deleteConfirm": "Are you sure you want to delete this customer?", "deleteSuccess": "Customer deleted successfully", "deleteError": "Error deleting customer", "saveSuccess": "Customer saved successfully", "saveError": "Error saving customer"}, "actions": {"addNew": "Add New Customer", "export": "Export", "import": "Import", "sendEmail": "Send Email", "resetPassword": "Reset Password", "viewOrders": "View Orders", "viewAddresses": "View Addresses", "viewActivity": "View Activity", "addNote": "Add Note", "editNote": "Edit Note", "deleteNote": "Delete Note"}, "filters": {"all": "All Customers", "new": "New Customers", "returning": "Returning Customers", "highValue": "High-Value Customers", "inactive": "Inactive Customers"}, "groups": {"title": "Customer Groups", "addGroup": "Add Group", "editGroup": "Edit Group", "deleteGroup": "Delete Group", "groupName": "Group Name", "groupDescription": "Description", "groupDiscount": "Discount (%)", "groupMinimumOrder": "Minimum Order", "groupTaxExempt": "Tax Exempt", "groupFreeShipping": "Free Shipping", "groupCustomers": "Customers in Group"}, "importExport": {"title": "Import/Export Customers", "import": {"title": "Import Customers", "description": "Upload a CSV file containing customer data.", "downloadTemplate": "Download Template", "uploadFile": "Upload File", "mapping": "CSV Mapping", "preview": "Preview", "import": "Import", "success": "{count} customers imported successfully", "error": "Error importing customers"}, "export": {"title": "Export Customer List", "subtitle": "Select the fields you want to include in the export", "fileFormat": "File Format", "csvDescription": "CSV file (comma-separated values)", "xlsxDescription": "Excel spreadsheet (XLSX)", "fieldSelection": "Field Selection", "selectAllFields": "Select all fields", "selectAllDescription": "Check this option to select all available fields at once", "basicData": "Basic Data", "basicDataDescription": "Select the file format for export", "basicFields": "Basic Fields", "addressFields": "Address", "totalCustomers": "Total customers", "customers": "customers", "fieldSelectionDescription": "Choose which fields to include in the export", "summaryDescription": "Review settings before starting", "fields": "fields", "summary": "Export Summary", "format": "Format", "selectedFieldsCount": "Selected fields", "exportData": "Export Data", "confirmTitle": "Confirm Export", "confirmMessage": "Do you want to start exporting the selected data?", "confirmExport": "Start Export", "startExport": "Start Export", "viewExports": "View Exports", "exportStarted": "Export Started", "exportStartedMessage": "The export was started successfully.", "exportError": "Error starting export", "noFieldsSelected": "Select at least one field to export"}, "exportList": {"title": "Customer Exports", "subtitle": "Manage your customer data exports", "newExport": "New Export", "fileName": "File Name", "createdAt": "Created At", "fileSize": "Size", "status": "Status", "actions": "Actions", "download": "Download file", "delete": "Delete export", "noExports": "No exports found", "noExportsDescription": "You haven't created any customer exports yet", "createFirstExport": "Create First Export", "loadError": "Error loading exports", "downloadStarted": "Download started", "downloadError": "Error downloading file", "deleteTitle": "Confirm Deletion", "deleteMessage": "Are you sure you want to delete this export?", "deleteConfirm": "Delete", "deleteSuccess": "Export deleted successfully", "deleteError": "Error deleting export", "infoTitle": "Important Information", "retentionTitle": "File Retention", "retentionDescription": "Files are automatically kept for 30 days and then removed", "processingTitle": "Processing Time", "processingDescription": "Large exports may take a few minutes to complete", "formatsTitle": "Available Formats", "formatsDescription": "Support for CSV and Excel (.xlsx) with complete customer data", "loading": "Loading exports...", "backToList": "Back to Customer List"}}, "list": {"importButton": "Import Customers", "exportButton": "Export Customers"}, "import": {"title": "Import Customers", "subtitle": "Import customer data from a CSV file", "viewImports": "View Imports", "startImport": "Start Import", "fileSelection": "File Selection", "fileSelectionDescription": "Select a CSV file to import", "selectFile": "Select CSV File", "selectFileDescription": "Drag and drop a CSV file here or click to select", "chooseFile": "Choose <PERSON>", "removeFile": "Remove File", "nextStep": "Next Step", "fieldMapping": "Field Mapping", "fieldMappingDescription": "Configure how CSV fields will be mapped", "csvPreview": "CSV Preview", "mapFields": "Map Fields", "csvField": "CSV Field", "iluriaField": "<PERSON><PERSON>", "selectField": "Select field...", "basicFields": "Basic Fields", "addressFields": "Address Fields", "completeAddressFields": "Complete Addresses", "addressComponentFields": "Address Components", "backToFile": "Back to File", "confirmMessage": "Are you sure you want to start the import?", "confirmTitle": "Confirm Import", "importStarted": "Import Started", "importStartedMessage": "The import has been started and will be processed in the background", "importError": "Error starting import", "parseError": "Error parsing CSV file", "fieldsMapped": "fields mapped", "fieldsAutomaticallyMapped": "fields automatically mapped", "fieldsWithoutValidData": "fields without valid data were hidden", "exampleRecords": "example records", "howWillBeSaved": "How it will be saved in the database", "mainCustomerData": "Main customer data", "customerAddresses": "Customer addresses", "intelligentAddressMapping": "Intelligent Address Mapping", "completeAddresses": "Complete Addresses", "completeAddressesDescription": "Fields with complete addresses are automatically detected and parsed into separate fields.", "completeAddressesExample": "Example: \"Street A, 123 - District, City - SP, ZIP 01001-000\"", "separateFields": "Separate Fields", "separateFieldsDescription": "Individual fields like \"street\", \"city\", \"state\" are mapped directly to the corresponding system fields.", "customerData": "Customer Data", "customerDataDescription": "Main information that will be saved in the customers table", "customer": "Customer", "fields": {"name": "Full Name", "email": "Email", "nickname": "Nickname", "phone": "Phone", "document": "CPF/CNPJ", "documentType": "Document Type", "dayOfBirth": "Date of Birth", "defaultLanguage": "Default Language", "allowsEmailMarketing": "Allows Email Marketing", "allowsPhoneMarketing": "Allows Phone Marketing", "active": "Active", "street": "Street/Address", "number": "Number", "complement": "Complement", "district": "District", "city": "City", "state": "State", "country": "Country", "zipCode": "ZIP Code", "type": "Type", "label": "Label"}, "tabs": {"import": "Import", "guide": "Guide"}, "guide": {"csvPreparation": "CSV File Preparation", "csvPreparationDescription": "How to structure your CSV file for best results", "csvStructure": "CSV Structure", "csvStructureDescription": "Your CSV file should have headers in the first row and data in subsequent rows:", "bestPractices": "Best Practices", "practice1": "Use UTF-8 encoding to avoid character issues", "practice2": "Include headers in the first row", "practice3": "Use consistent date formats (YYYY-MM-DD or DD/MM/YYYY)", "practice4": "Avoid empty rows between data", "fieldMapping": "Field Mapping", "fieldMappingDescription": "How to map your CSV fields to system fields", "customerFields": "Customer <PERSON>", "addressFields": "Address Fields", "advancedTips": "Advanced Tips", "advancedTipsDescription": "Special features to maximize your import", "addressTip": "Complete Addresses", "addressTipDescription": "Use a field with complete address and the system will automatically parse it:", "dataTip": "Date Format", "dataTipDescription": "Use standard formats for birth dates:", "validationTip": "Automatic Validation", "validationTipDescription": "The system automatically validates emails, phones and documents during import"}}, "importList": {"title": "Customer Imports", "subtitle": "Manage your customer data imports", "newImport": "New Import", "fileName": "File Name", "status": "Status", "records": "Records", "processed": "processed", "createdAt": "Created At", "completedAt": "Completed At", "actions": "Actions", "noImports": "No imports found", "noImportsDescription": "You haven't created any customer imports yet", "createFirstImport": "Create First Import", "loadError": "Error loading imports", "deleteConfirmMessage": "Are you sure you want to delete the import '{fileName}'?", "deleteConfirmTitle": "Confirm Deletion", "deleteSuccess": "Import deleted successfully", "deleteError": "Error deleting import"}, "bulk": {"selectedOne": "selected", "selectedPlural": "selected", "selectedAny": "selected", "selectedPluralAny": "selected", "actions": "Bulk actions", "deleteSelected": "Delete selected", "deleteSelectedShort": "Delete", "selectCustomer": "Select customer", "confirmDeleteTitle": "Confirm bulk deletion", "confirmDeleteMessage": "Are you sure you want to delete {count} {entity}? This action cannot be undone.", "confirmDeleteMessageSingle": "Are you sure you want to delete 1 {entity}? This action cannot be undone.", "deleteSuccess": "{count} {entity} deleted successfully", "deleteSuccessSingle": "1 {entity} deleted successfully", "deleteError": "Error deleting selected {entity}", "deletePartialError": "Some {entity} could not be deleted", "processing": "Processing...", "clearSelection": "Clear selection", "selectAll": "Select all", "deselectAll": "Deselect all", "customer": "customer", "customers": "customers"}}