import axios from 'axios';

/**
 * Serviço para gerenciar permissões do usuário
 * Integra com o backend para buscar permissões baseadas no token JWT
 */
class PermissionsService {
  
  constructor() {
    this.cache = new Map();
    this.cacheTimeout = 5 * 60 * 1000; // 5 minutos
  }

  /**
   * Busca as permissões do usuário atual
   * Usa cache para otimizar performance
   * 
   * @returns {Promise<{permissions: Set<string>, storeId: string, roleId: string}>}
   */
  async getCurrentUserPermissions() {
    try {
      // Verificar cache primeiro
      const cached = this.getCachedPermissions();
      if (cached) {
        return cached;
      }

      const response = await axios.get('/api/v1/permissions/current');
      
      const result = {
        permissions: new Set(response.data.permissions || []),
        storeId: response.data.storeId,
        roleId: response.data.roleId
      };

      // Armazenar no cache
      this.setCachedPermissions(result);
      
      return result;
      
    } catch (error) {
      console.error('Erro ao buscar permissões do usuário:', error);
      
      // Em caso de erro, retorna sem permissões (fail-safe)
      return {
        permissions: new Set(),
        storeId: null,
        roleId: null
      };
    }
  }

  /**
   * Verifica se o usuário possui uma permissão específica
   * 
   * @param {string} permissionCode - Código da permissão
   * @returns {Promise<boolean>}
   */
  async hasPermission(permissionCode) {
    const { permissions } = await this.getCurrentUserPermissions();
    return permissions.has(permissionCode);
  }

  /**
   * Verifica se o usuário possui todas as permissões especificadas (AND)
   * 
   * @param {string[]} permissionCodes - Array de códigos de permissão
   * @returns {Promise<boolean>}
   */
  async hasAllPermissions(permissionCodes) {
    if (!Array.isArray(permissionCodes) || permissionCodes.length === 0) return true;
    
    const { permissions } = await this.getCurrentUserPermissions();
    return permissionCodes.every(code => permissions.has(code));
  }

  /**
   * Verifica se o usuário possui qualquer uma das permissões especificadas (OR)
   * 
   * @param {string[]} permissionCodes - Array de códigos de permissão
   * @returns {Promise<boolean>}
   */
  async hasAnyPermission(permissionCodes) {
    if (!Array.isArray(permissionCodes) || permissionCodes.length === 0) return true;
    
    const { permissions } = await this.getCurrentUserPermissions();
    return permissionCodes.some(code => permissions.has(code));
  }

  /**
   * Obtém permissões do cache se ainda válidas
   * 
   * @returns {object|null}
   */
  getCachedPermissions() {
    const cacheKey = this.getCacheKey();
    const cached = this.cache.get(cacheKey);
    
    if (cached && (Date.now() - cached.timestamp) < this.cacheTimeout) {
      return cached.data;
    }
    
    return null;
  }

  /**
   * Armazena permissões no cache
   * 
   * @param {object} data - Dados das permissões
   */
  setCachedPermissions(data) {
    const cacheKey = this.getCacheKey();
    this.cache.set(cacheKey, {
      data,
      timestamp: Date.now()
    });
  }

  /**
   * Gera chave de cache baseada no token atual + storeId
   * 
   * @returns {string}
   */
  getCacheKey() {
    // Usar os headers de autorização para gerar chave única
    const auth = axios.defaults.headers.common['Authorization'];
    if (!auth) return 'no-auth';
    
    const token = auth.replace('Bearer ', '');
    
    // Extrair storeId do token para criar chave única por loja
    try {
      const payload = JSON.parse(atob(token.split('.')[1]));
      const storeId = payload.storeId || 'no-store';
      const userId = payload.sub || 'no-user';
      
      // Combinar userId + storeId + fragmentos do token para chave única
      return `permissions_${userId}_${storeId}_${token.substring(0, 8)}_${token.substring(token.length - 8)}`;
    } catch (error) {
      // Fallback para o método antigo se não conseguir decodificar
      console.warn('Erro ao extrair storeId do token para cache:', error);
      return `permissions_${token.substring(0, 10)}_${token.substring(token.length - 10)}`;
    }
  }

  /**
   * Limpa o cache de permissões
   * Útil quando o usuário faz logout ou troca de loja
   */
  clearCache() {
    this.cache.clear();
  }

  /**
   * Invalida cache para o token atual
   * Útil quando o token é renovado
   */
  invalidateCurrentCache() {
    const cacheKey = this.getCacheKey();
    this.cache.delete(cacheKey);
  }

  /**
   * Invalida todo o cache de um usuário específico
   * Útil quando o usuário troca de loja
   */
  invalidateUserCache(userId) {
    if (!userId) return;
    
    // Remove todas as entradas de cache que começam com o userId
    const keysToDelete = [];
    for (const key of this.cache.keys()) {
      if (key.startsWith(`permissions_${userId}_`)) {
        keysToDelete.push(key);
      }
    }
    
    keysToDelete.forEach(key => this.cache.delete(key));
  }

  /**
   * Força atualização das permissões
   * Remove do cache e busca novamente
   * 
   * @returns {Promise<object>}
   */
  async refreshPermissions() {
    this.invalidateCurrentCache();
    return await this.getCurrentUserPermissions();
  }
}

export default new PermissionsService();