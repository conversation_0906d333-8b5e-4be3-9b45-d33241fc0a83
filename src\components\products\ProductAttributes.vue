<template>
  <div class="attributes-section overflow-visible pb-24">
    <!-- Header da seção -->
    <div class="flex justify-between items-center mb-6">
      <div>
        
      </div>
      <IluriaButton
        v-if="attributes.length > 0"
        color="dark"
        variant="outline"
        :hugeIcon="PlusSignIcon"
        @click="addAttribute"
      >
        {{ t('product.attributes.addAttribute') }}
      </IluriaButton>
    </div>

    <!-- Estado vazio -->
    <div v-if="attributes.length === 0" class="text-center py-12 rounded-lg border-2 border-dashed border-[var(--iluria-color-border)] bg-[var(--iluria-color-surface)]">
      <div class="w-16 h-16 mx-auto mb-4 text-[var(--iluria-color-text-tertiary)]">
        <svg fill="none" stroke="currentColor" viewBox="0 0 24 24" class="w-full h-full">
          <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
        </svg>
      </div>
      <h4 class="text-lg font-medium text-[var(--iluria-color-text-primary)] mb-2">{{ t('product.attributes.emptyTitle') }}</h4>
      <p class="text-[var(--iluria-color-text-secondary)] mb-6 max-w-md mx-auto">{{ t('product.attributes.emptyDescription') }}</p>
      <IluriaButton
        color="dark"
        variant="outline"
        :hugeIcon="PlusSignIcon"
        @click="addAttribute"
        class="mx-auto"
      >
        {{ t('product.attributes.addAttribute') }}
      </IluriaButton>
    </div>

    <!-- Lista de atributos com drag and drop -->
    <div v-if="attributes.length > 0" class="space-y-4 overflow-visible">
      <draggable 
        v-model="attributes" 
        @end="onSortAttributes"
        item-key="id"
        handle=".drag-handle"
        class="space-y-4 overflow-visible"
        :animation="150"
        ghost-class="drag-ghost"
        chosen-class="drag-chosen"
      >
        <template #item="{ element: attribute, index }">
          <div class="bg-[var(--iluria-color-surface)] border border-[var(--iluria-color-border)] rounded-lg shadow-sm overflow-visible" :key="attribute.id" :data-attribute-index="index">
            <!-- Header do atributo -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-[var(--iluria-color-border)]">
              <div class="flex items-center space-x-3">
                <!-- Drag handle -->
                <div class="drag-handle cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-text-secondary)] p-1">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM3 12a1 1 0 000 2h.01a1 1 0 100-2H3zM8 4a1 1 0 000 2h.01a1 1 0 100-2H8zM8 8a1 1 0 000 2h.01a1 1 0 100-2H8zM8 12a1 1 0 000 2h.01a1 1 0 100-2H8z"/>
                  </svg>
                </div>
                
                <!-- Ícone do atributo -->
                <div class="flex items-center justify-center w-10 h-10 bg-[var(--iluria-color-surface-hover)] rounded-full">
                  <svg class="w-5 h-5 text-[var(--iluria-color-text-primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 7h.01M7 3h5c.512 0 1.024.195 1.414.586l7 7a2 2 0 010 2.828l-7 7a2 2 0 01-2.828 0l-7-7A1.994 1.994 0 013 12V7a4 4 0 014-4z"/>
                  </svg>
                </div>
                
                <div>
                  <h4 class="text-lg font-medium text-[var(--iluria-color-text-primary)]">
                    {{ attribute.name || t('product.attributes.attribute') }}
                  </h4>
                  <p class="text-sm text-[var(--iluria-color-text-secondary)]" v-if="attribute.values && attribute.values.length > 0">
                    {{ attribute.values.length }} {{ attribute.values.length === 1 ? 'valor' : 'valores' }}
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <!-- Badge de filtro -->
                <span 
                  v-if="attribute.useAsFilter" 
                  class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800"
                >
                  <svg class="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                    <path fill-rule="evenodd" d="M3 3a1 1 0 011-1h12a1 1 0 011 1v3a1 1 0 01-.293.707L12 11.414V15a1 1 0 01-.293.707l-2 2A1 1 0 018 17v-5.586L3.293 6.707A1 1 0 013 6V3z" clip-rule="evenodd"/>
                  </svg>
                  Filtro ativo
                </span>
                
                <!-- Botão de collapse -->
                <button
                  type="button"
                  @click="toggleAttributeCollapse(index)"
                  class="p-2 text-gray-400 hover:text-gray-600 rounded-full hover:bg-gray-50"
                >
                  <svg 
                    class="w-5 h-5 transition-transform duration-200"
                    :class="{ 'transform rotate-180': !isAttributeCollapsed(index) }"
                    fill="none" 
                    stroke="currentColor" 
                    viewBox="0 0 24 24"
                  >
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                  </svg>
                </button>
                
                <button
                  type="button"
                  class="p-2 text-red-400 hover:text-red-600 rounded-full hover:bg-red-50"
                  @click="removeAttribute(index)"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Conteúdo do atributo (Colapsável) -->
            <div v-show="!isAttributeCollapsed(index)" class="p-6 space-y-6">
              <!-- Primeira linha: Nome do atributo e checkbox de filtro -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <!-- Dropdown de atributos com busca -->
                <div class="relative">
                  <label class="block text-sm font-medium text-[var(--iluria-color-text-primary)] mb-2">
                    {{ t('product.attributes.attribute') }}
                  </label>
                  <div class="relative">
                    <button
                      type="button"
                      @click="toggleAttributeDropdown(index)"
                      class="w-full bg-[var(--iluria-color-input-bg)] border border-[var(--iluria-color-input-border)] rounded-lg px-4 py-3 text-left text-[var(--iluria-color-input-text)] shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--iluria-color-focus-ring)] focus:border-[var(--iluria-color-input-border-focus)] flex items-center justify-between"
                    >
                      <span class="block truncate">
                        {{ attribute.name || t('product.attributes.attributeNamePlaceholder') }}
                      </span>
                      <svg class="w-5 h-5 text-[var(--iluria-color-text-tertiary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                      </svg>
                    </button>
                    
                    <!-- Dropdown de atributos -->
                    <div 
                      v-if="showAttributeDropdown === index"
                      class="absolute z-50 mt-2 w-full bg-[var(--iluria-color-surface)] border border-[var(--iluria-color-border)] rounded-lg shadow-lg max-h-60 overflow-auto"
                      :style="getDropdownStyle(index)"
                    >
                      <!-- Campo de busca -->
                      <div class="p-3 border-b border-[var(--iluria-color-border)]">
                        <div class="relative">
                          <input
                            type="text"
                            v-model="attributeSearchQuery"
                            @input="searchAttributes"
                            placeholder="Buscar atributo..."
                            class="w-full px-4 py-2 pl-10 border border-[var(--iluria-color-input-border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--iluria-color-focus-ring)] focus:border-[var(--iluria-color-input-border-focus)] bg-[var(--iluria-color-input-bg)] text-[var(--iluria-color-input-text)]"
                          />
                          <svg class="w-5 h-5 text-[var(--iluria-color-text-tertiary)] absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                          </svg>
                        </div>
                      </div>
                      
                      <!-- Lista de atributos -->
                      <div class="py-2">
                        <div v-if="filteredAttributes.length > 0" class="space-y-1">
                          <button
                            v-for="attribute in filteredAttributes"
                            :key="attribute.id"
                            type="button"
                            @click="selectAttribute(index, attribute)"
                            class="w-full px-4 py-2 text-left hover:bg-[var(--iluria-color-hover)] text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-text-primary)] flex items-center justify-between"
                          >
                            <span>{{ attribute.name }}</span>
                            <span class="text-xs text-[var(--iluria-color-text-secondary)]">{{ attribute.usageCount || 0 }} uso(s)</span>
                          </button>
                        </div>
                        
                        <!-- Separador e opções de ação -->
                        <div class="border-t border-[var(--iluria-color-border)] pt-2 mt-2">
                          <!-- Botão único que muda conforme a pesquisa -->
                          <button
                            type="button"
                            @click="openModalForAttribute(attributeSearchQuery.trim(), index)"
                            class="w-full px-4 py-2 text-left text-[var(--iluria-color-text-primary)] hover:bg-[var(--iluria-color-hover)] flex items-center"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            <span v-if="attributeSearchQuery && attributeSearchQuery.trim()">
                              Adicionar "{{ attributeSearchQuery.trim() }}"
                            </span>
                            <span v-else>
                              Adicionar atributo
                            </span>
                          </button>
                        </div>
                        
                        <!-- Estado vazio -->

                      </div>
                    </div>
                  </div>
                </div>

                <!-- Switch usar como filtro -->
                <div class="flex items-center h-full pl-6">
                  <div class="flex items-center space-x-3 pt-7">
                    <IluriaToggleSwitch
                      :id="`filter-${attribute.id}`"
                      v-model="attribute.useAsFilter"
                      @update:modelValue="updateModelValue"
                      :label="t('product.attributes.useAsFilter')"
                      labelPosition="horizontal"
                    />
                  </div>
                </div>
              </div>

              <!-- Valores do atributo -->
              <div v-if="attribute.name" class="space-y-4">
                <label class="block text-sm font-medium text-[var(--iluria-color-text-primary)]">
                  {{ t('product.attributes.values') }}
                </label>
                
                <!-- Conteúdo dos valores -->
                <div class="space-y-4">
                  <!-- Dropdown para adicionar valores -->
                  <div class="relative">
                    <button
                      type="button"
                      @click="toggleValueDropdown(index)"
                      class="value-dropdown-button w-full bg-[var(--iluria-color-input-bg)] border border-[var(--iluria-color-input-border)] rounded-lg px-4 py-3 text-left text-[var(--iluria-color-input-text)] shadow-sm focus:outline-none focus:ring-2 focus:ring-[var(--iluria-color-focus-ring)] focus:border-[var(--iluria-color-input-border-focus)] flex items-center justify-between"
                    >
                      <span class="block truncate">
                        {{ getSelectedValuesText(attribute) }}
                      </span>
                      <svg class="w-5 h-5 text-[var(--iluria-color-text-tertiary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                      </svg>
                    </button>
                    
                    <!-- Dropdown de valores -->
                    <div 
                      v-if="showValueDropdown === index"
                      class="absolute z-50 mt-2 w-full bg-[var(--iluria-color-surface)] border border-[var(--iluria-color-border)] rounded-lg shadow-lg max-h-60 overflow-auto"
                      :style="getDropdownStyle(index)"
                    >
                      <!-- Campo de busca -->
                      <div class="p-3 border-b border-[var(--iluria-color-border)]">
                        <div class="relative">
                          <input
                            type="text"
                            v-model="valueSearchInputs[index]"
                            @input="loadValueSuggestions(index)"
                            :placeholder="'Digite um valor...'"
                            class="value-search-input w-full px-4 py-2 pl-10 border border-[var(--iluria-color-input-border)] rounded-md focus:outline-none focus:ring-2 focus:ring-[var(--iluria-color-focus-ring)] focus:border-[var(--iluria-color-input-border-focus)] bg-[var(--iluria-color-input-bg)] text-[var(--iluria-color-input-text)]"
                          />
                          <svg class="w-5 h-5 text-[var(--iluria-color-text-tertiary)] absolute left-3 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
                          </svg>
                        </div>
                      </div>
                      
                      <!-- Lista de valores sugeridos -->
                      <div class="py-2">
                        <div v-if="filteredValueSuggestions[index] && filteredValueSuggestions[index].length > 0" class="space-y-1">
                          <button
                            v-for="suggestion in filteredValueSuggestions[index]"
                            :key="suggestion.id || suggestion.value"
                            type="button"
                            @click="toggleValueSelection(index, suggestion)"
                            class="w-full px-4 py-2 text-left hover:bg-[var(--iluria-color-hover)] text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-text-primary)] flex items-center justify-between transition-colors duration-150"
                            :class="{ 'bg-[var(--iluria-color-surface-hover)] text-[var(--iluria-color-text-primary)] font-medium': isValueSelected(index, suggestion.value || suggestion) }"
                          >
                            <span>{{ suggestion.value || suggestion }}</span>
                            <div class="flex items-center space-x-3">
                                <span v-if="suggestion.usageCount" class="text-xs text-[var(--iluria-color-text-secondary)]">{{ suggestion.usageCount || 0 }} uso(s)</span>
                                <div class="w-5 h-5">
                                  <svg v-if="isValueSelected(index, suggestion.value || suggestion)" class="w-5 h-5 text-[var(--iluria-color-primary)]" fill="currentColor" viewBox="0 0 20 20">
                                    <path fill-rule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clip-rule="evenodd" />
                                  </svg>
                                </div>
                            </div>
                          </button>
                        </div>
                        
                        <!-- Separador e opções de ação -->
                        <div class="border-t border-[var(--iluria-color-border)] pt-2 mt-2">
                          <!-- Botão único que muda conforme a pesquisa -->
                          <button
                            type="button"
                            @click="openModalForValues(index, valueSearchInputs[index] || '')"
                            class="w-full px-4 py-2 text-left text-[var(--iluria-color-text-primary)] hover:bg-[var(--iluria-color-hover)] flex items-center"
                          >
                            <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                              <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                            </svg>
                            <span v-if="valueSearchInputs[index] && valueSearchInputs[index].trim()">
                              Gerenciar valores de "{{ attribute.name }}"
                            </span>
                            <span v-else>
                              Gerenciar valores
                            </span>
                          </button>
                        </div>
                        
                        <!-- Estado vazio -->
                        <div v-if="(!filteredValueSuggestions[index] || filteredValueSuggestions[index].length === 0) && !valueSearchInputs[index]" class="px-4 py-3 text-center text-[var(--iluria-color-text-secondary)] text-sm">
                          Digite para buscar ou criar valores
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>

      <!-- Botão adicionar atributo no final -->
      <div class="flex justify-center pt-4">
        <IluriaButton
          color="dark"
          variant="outline"
          :hugeIcon="PlusSignIcon"
          @click="addAttribute"
        >
          {{ t('product.attributes.addAttribute') }}
        </IluriaButton>
      </div>
    </div>

    <!-- MODAL UNIFICADO PARA CRIAR/EDITAR ATRIBUTOS E VALORES -->
    <AttributeModal
      :visible="showAttributeModal"
      @update:visible="showAttributeModal = $event"
      :is-editing="modalConfig.isEditing"
      :mode="modalConfig.mode"
      :attribute="modalConfig.attribute"
      :categories="rawCategories"
      :saving="savingAttribute"
      :fixed-category-id="modalConfig.fixedCategoryId"
      :fixed-category-name="modalConfig.fixedCategoryName"
      :attribute-name-for-values-mode="modalConfig.attributeNameForValuesMode"
      @save="handleSaveFromModal"
      @cancel="showAttributeModal = false"
    />
  </div>
</template>

<script setup>
import { ref, watch, inject, computed, nextTick, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToast } from '@/services/toast.service';
import draggable from 'vuedraggable';
import { attributesApi } from '@/services/attributes.service';
import { categoryService } from '@/services/category.service';
import IluriaModal from '@/components/iluria/IluriaModal.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import AttributeModal from '@/components/products/AttributeModal.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { PlusSignIcon } from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();
const toast = useToast();

// Inject do formulário de produto
const form = inject('productForm');

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Estado local
const attributes = ref([]);
const availableAttributes = ref([]);
const showAttributeDropdown = ref(null);
const showValueDropdown = ref(null);
const currentAttributeIndex = ref(null);
const currentValueAttributeIndex = ref(null);
const attributeSearchQuery = ref('');
const valueSearchInputs = ref({});
const valueSuggestions = ref({});
const collapsedAttributes = ref({});
const rawCategories = ref([]);

// --- NOVO ESTADO PARA O MODAL UNIFICADO ---
const showAttributeModal = ref(false);
const savingAttribute = ref(false);
const modalConfig = ref({
  isEditing: false,
  mode: 'attribute', // 'attribute' ou 'value'
  attribute: {},
  attributeIndex: null,
  fixedCategoryId: null,
  fixedCategoryName: '',
  attributeNameForValuesMode: ''
});
// --- FIM DO NOVO ESTADO ---

// Computed
const selectedCategoryId = computed(() => form?.value?.categoryId);
const selectedCategoryName = computed(() => form?.value?.categoryName);
const currentAttributeName = computed(() => attributes.value[currentAttributeIndex.value]?.name);

const filteredAttributes = computed(() => {
  if (!attributeSearchQuery.value) {
    return availableAttributes.value.slice(0, 10);
  }
  
  return availableAttributes.value.filter(attr =>
    attr.name.toLowerCase().includes(attributeSearchQuery.value.toLowerCase())
  ).slice(0, 10);
});

const filteredValueSuggestions = computed(() => {
  const result = {};
  
  Object.keys(valueSuggestions.value).forEach(index => {
    const suggestions = valueSuggestions.value[index] || [];
    const searchQuery = valueSearchInputs.value[index] || '';
    
    if (searchQuery.trim()) {
      result[index] = suggestions.filter(suggestion => {
        const value = suggestion.value || suggestion;
        return value.toLowerCase().includes(searchQuery.toLowerCase());
      });
    } else {
      result[index] = suggestions;
    }
  });
  
  return result;
});

// Função para criar um novo atributo
const createNewAttributeItem = () => ({
  id: `attr_${Date.now()}_${Math.random()}`,
  name: '',
  values: [],
  useAsFilter: false,
  position: 0
});

// Função para adicionar atributo
const addAttribute = () => {
  const newAttr = createNewAttributeItem();
  newAttr.position = attributes.value.length;
  attributes.value.push(newAttr);
  const newIndex = attributes.value.length - 1;
  valueSearchInputs.value[newIndex] = '';
  collapsedAttributes.value[newIndex] = false; // Começar expandido
  updateModelValue();
};

// Função para remover atributo
const removeAttribute = (index) => {
  attributes.value.splice(index, 1);
  delete valueSearchInputs.value[index];
  delete valueSuggestions.value[index];
  delete collapsedAttributes.value[index];
  // Fechar dropdowns se estavam abertos neste índice
  if (showAttributeDropdown.value === index) showAttributeDropdown.value = null;
  if (showValueDropdown.value === index) showValueDropdown.value = null;
  updateModelValue();
};

// Função para reordenar atributos
const onSortAttributes = () => {
  attributes.value.forEach((attr, index) => {
    attr.position = index;
  });
  updateModelValue();
};

// Função para toggle do dropdown de atributos
const toggleAttributeDropdown = (index) => {
  showAttributeDropdown.value = showAttributeDropdown.value === index ? null : index;
  showValueDropdown.value = null; // Fechar dropdown de valores
};

// Função para toggle do dropdown de valores
const toggleValueDropdown = (index) => {
  showValueDropdown.value = showValueDropdown.value === index ? null : index;
  showAttributeDropdown.value = null; // Fechar dropdown de atributos
  
  // Inicializar campo de busca se não existir
  if (!(index in valueSearchInputs.value)) {
    valueSearchInputs.value[index] = '';
  }
  
  // Carregar sugestões ao abrir
  if (showValueDropdown.value === index) {
    loadValueSuggestions(index);
  }
};

// Função para toggle do collapse de atributos
const toggleAttributeCollapse = (index) => {
  collapsedAttributes.value[index] = !collapsedAttributes.value[index];
};

// Função para verificar se atributo está colapsado
const isAttributeCollapsed = (index) => {
  return collapsedAttributes.value[index] || false;
};

// Util: encontrar ancestral scrollável (overflow auto/scroll)
const getScrollableAncestor = (el) => {
  while (el && el !== document.body) {
    const style = window.getComputedStyle(el);
    const overflowY = style.overflowY;
    if (overflowY === 'auto' || overflowY === 'scroll') {
      return el;
    }
    el = el.parentElement;
  }
  return document.body;
};

const getDropdownStyle = (index) => {
  const triggerEl = document.querySelector(`[data-attribute-index="${index}"] .value-dropdown-button, [data-attribute-index="${index}"] button.value-dropdown-button`);
  if (!triggerEl) {
    return { top: '100%', left: '0', right: '0' };
  }

  const rect = triggerEl.getBoundingClientRect();
  const dropdownEl = triggerEl.parentElement.querySelector('.absolute.z-50');
  const dropdownHeight = dropdownEl ? dropdownEl.scrollHeight : 280;

  // Encontrar ancestral com scroll
  const scrollAncestor = getScrollableAncestor(triggerEl);
  const ancestorRect = scrollAncestor.getBoundingClientRect();

  const spaceBelow = ancestorRect.bottom - rect.bottom;
  const spaceAbove = rect.top - ancestorRect.top;

  if (spaceBelow < dropdownHeight && spaceAbove > spaceBelow) {
    // Abre para cima e ajusta altura se necessário
    const maxH = Math.min(dropdownHeight, spaceAbove - 16);
    return { top: 'auto', bottom: 'calc(100% + 8px)', left: '0', right: '0', maxHeight: `${maxH}px` };
  }
  // Abre para baixo
  const maxH = Math.min(dropdownHeight, spaceBelow - 16);
  return { top: 'calc(100% + 8px)', left: '0', right: '0', maxHeight: `${maxH}px` };
};

// Função para selecionar atributo
const selectAttribute = (index, attribute) => {
  // Limpar valores selecionados quando trocar de atributo
  if (attributes.value[index].attributeId !== attribute.id) {
    attributes.value[index].values = [];
  }
  
  attributes.value[index].name = attribute.name;
  attributes.value[index].attributeId = attribute.id;
  showAttributeDropdown.value = null;
  attributeSearchQuery.value = '';
  
  // Carregar sugestões de valores para o novo atributo
  loadValueSuggestions(index);
  
  updateModelValue();
};

// Função para carregar categorias
const loadCategories = async () => {
  try {
    const data = await categoryService.fetchCategories();
    rawCategories.value = data || [];
  } catch (error) {
    console.error('Erro ao carregar categorias para hierarquia:', error);
    rawCategories.value = [];
  }
};

// Função para obter IDs de todas as categorias pai (hierarquia para cima)
const getAllParentCategoryIds = (categoryId, allCategories = rawCategories.value) => {
  const categoryIds = [categoryId]; // Incluir a própria categoria
  
  const findParentIds = (targetId, categories) => {
    for (const category of categories) {
      if (category.id === targetId) {
        return [];
      }
      
      if (category.children && category.children.length > 0) {
        for (const child of category.children) {
          if (child.id === targetId) {
            // Encontrou o filho, adicionar o pai
            categoryIds.push(category.id);
            // Buscar recursivamente os pais do pai
            findParentIds(category.id, allCategories);
            return;
          } else {
            // Continuar buscando nos filhos
            findParentIds(targetId, [child]);
          }
        }
      }
    }
  };
  
  findParentIds(categoryId, allCategories);
  return [...new Set(categoryIds)]; // Remover duplicatas
};

// Função para buscar atributos (com hierarquia + ALL)
const searchAttributes = async () => {
  if (!selectedCategoryId.value) {
    availableAttributes.value = [];
    return;
  }
  
  try {
    // 1. Buscar atributos da categoria atual + todas as categorias pai
    const categoryIds = getAllParentCategoryIds(selectedCategoryId.value);
    
    // 2. Sempre incluir atributos "ALL" (globais)
    categoryIds.push('ALL');
    
    let response;
    if (categoryIds.length === 1) {
      // Apenas uma categoria, usar método simples
      response = await attributesApi.getByCategoryId(selectedCategoryId.value);
      availableAttributes.value = Array.isArray(response) ? response : (response.data || []);
    } else {
      // Múltiplas categorias, usar método consolidado
      response = await attributesApi.getAllByMultipleCategories({
        categoryIds: categoryIds,
        size: 1000 // Buscar todos os atributos
      });
      availableAttributes.value = response.data?.content || [];
    }
    


    
  } catch (error) {
    console.error('Erro ao buscar atributos:', error);
    // Fallback: tentar apenas a categoria atual + ALL
    try {
      const categoryResponse = await attributesApi.getByCategoryId(selectedCategoryId.value);
      const allResponse = await attributesApi.getByCategoryId('ALL');
      
      const categoryAttributes = Array.isArray(categoryResponse) ? categoryResponse : (categoryResponse.data || []);
      const allAttributes = Array.isArray(allResponse) ? allResponse : (allResponse.data || []);
      
      // Consolidar sem duplicatas
      const allAttributesMap = new Map();
      [...categoryAttributes, ...allAttributes].forEach(attr => {
        allAttributesMap.set(attr.id, attr);
      });
      
      availableAttributes.value = Array.from(allAttributesMap.values());
      
    } catch (fallbackError) {
      console.error('Erro no fallback:', fallbackError);
      availableAttributes.value = [];
    }
  }
};

// Função para adicionar valor
const addValue = (index, value) => {
  if (!value) return;
  
  if (!attributes.value[index]) return;
  
  if (!attributes.value[index].values) {
    attributes.value[index].values = [];
  }
  
  if (!attributes.value[index].values.includes(value)) {
    attributes.value[index].values.push(value);
    updateModelValue();
  }
};

// Função para verificar se valor já está selecionado
const isValueAlreadySelected = (index, value) => {
  return attributes.value[index]?.values?.includes(value) || false;
};

// Função para remover valor
const removeValue = (attributeIndex, valueIndex) => {
  attributes.value[attributeIndex].values.splice(valueIndex, 1);
  updateModelValue();
};

// Função para carregar sugestões de valores
const loadValueSuggestions = async (index) => {
  const attribute = attributes.value[index];
  const attributeId = attribute?.attributeId;
  const attributeName = attribute?.name;
  
  if (!attributeId && !attributeName) {
    valueSuggestions.value[index] = [];
    return;
  }
  
  try {
    if (attributeId) {
      const attributeData = await attributesApi.getAttributeWithValues(attributeId);
      valueSuggestions.value[index] = attributeData.values || [];
    } else {
      const suggestions = await attributesApi.getSuggestedValues(selectedCategoryId.value, '');
      valueSuggestions.value[index] = suggestions || [];
    }
  } catch (error) {
    valueSuggestions.value[index] = [];
  }
};

// Função para selecionar sugestão de valor
const selectValueSuggestion = (index, suggestion) => {
  const value = suggestion.value || suggestion;
  addValue(index, value);
  valueSearchInputs.value[index] = '';
  showValueDropdown.value = null;
};

// --- NOVAS FUNÇÕES PARA O MODAL ---
const openModalForAttribute = (initialName = '', attributeIndex) => {
  showAttributeDropdown.value = null; // Fechar dropdown

  // Busca o nome da categoria para garantir que está atualizado
  const categoryId = selectedCategoryId.value;
  const findCategory = (categories, id) => {
    for (const cat of categories) {
      if (cat.id === id) return cat;
      if (cat.children) {
        const found = findCategory(cat.children, id);
        if (found) return found;
      }
    }
    return null;
  };
  const category = findCategory(rawCategories.value, categoryId);
  const categoryName = category ? (category.title || category.name) : '';

  modalConfig.value = {
    isEditing: false,
    mode: 'attribute',
    attribute: {
      name: initialName,
      values: [],
      active: true,
      categoryId: categoryId
    },
    attributeIndex: attributeIndex,
    fixedCategoryId: categoryId,
    fixedCategoryName: categoryName,
    attributeNameForValuesMode: ''
  };
  showAttributeModal.value = true;
};

const openModalForValues = (attributeIndex, initialValue = '') => {
  showValueDropdown.value = null; // Fechar dropdown
  const attribute = attributes.value[attributeIndex];

  if (!attribute || !attribute.name) {
    toast.showError('Selecione um atributo antes de adicionar valores.');
    return;
  }

  // Adiciona o valor inicial (da busca) à lista se ele não existir
  const existingValues = [...(attribute.values || [])];
  if (initialValue && !existingValues.includes(initialValue)) {
    // Não adiciona mais automaticamente, apenas abre o modal
  }

  modalConfig.value = {
    isEditing: true, // Sempre editando ao gerenciar valores
    mode: 'value',
    attribute: {
      ...attribute,
      values: existingValues
    },
    attributeIndex: attributeIndex,
    fixedCategoryId: selectedCategoryId.value,
    fixedCategoryName: selectedCategoryName.value,
    attributeNameForValuesMode: attribute.name
  };
  showAttributeModal.value = true;
};

const handleSaveFromModal = async (savedData) => {
  savingAttribute.value = true;
  
  try {
    // --- LÓGICA PARA SALVAR ATRIBUTO ---
    if (modalConfig.value.mode === 'attribute') {
      const attributeData = {
        name: savedData.name,
        categoryId: savedData.categoryId,
        active: true, // Sempre ativo no contexto do produto
        initialValues: savedData.initialValues || []
      };

      const response = await attributesApi.create(attributeData);
      const createdAttribute = response.data || response;

      // Adicionar à lista de atributos disponíveis
      availableAttributes.value.unshift(createdAttribute);
      
      const attributeIndex = modalConfig.value.attributeIndex;

      // Se o modal foi aberto a partir de um cartão de atributo, atualize-o.
      if (attributeIndex !== null && attributes.value[attributeIndex]) {
        const attributeToUpdate = attributes.value[attributeIndex];
        attributeToUpdate.name = createdAttribute.name;
        attributeToUpdate.attributeId = createdAttribute.id;
        attributeToUpdate.values = [...(attributeData.initialValues || [])];
        
        toast.showSuccess('Atributo criado e selecionado!');
        
        // Fechar dropdown de atributos que estava aberto para este item.
        if (showAttributeDropdown.value === attributeIndex) {
            showAttributeDropdown.value = null;
        }
        attributeSearchQuery.value = '';

      } else {
        // Este caso não deve mais ocorrer, mas por segurança, logamos um erro.
        console.error("handleSaveFromModal foi chamado sem um attributeIndex válido, criando um novo atributo no final da lista.");
        // Fallback para o comportamento antigo para evitar perda de dados
        const newAttrItem = createNewAttributeItem();
        const newAttr = {
          ...newAttrItem,
          name: createdAttribute.name,
          attributeId: createdAttribute.id,
          position: attributes.value.length,
          values: [...(attributeData.initialValues || [])]
        };
        attributes.value.push(newAttr);
        toast.showSuccess('Atributo criado e adicionado ao produto!');
      }

    // --- LÓGICA PARA SALVAR VALORES ---
    } else if (modalConfig.value.mode === 'value') {
      const attributeIndex = modalConfig.value.attributeIndex;
      const attribute = attributes.value[attributeIndex];
      const attributeId = attribute.attributeId;

      if (!attributeId) {
        // Se o atributo ainda não foi salvo (é um novo sem ID), apenas atualiza localmente
        attribute.values = savedData.values;
        toast.showSuccess('Valores atualizados localmente.');
      } else {
        // Se o atributo já existe, sincroniza com a API
        const existingValuesData = await attributesApi.getAttributeWithValues(attributeId);
        const existingValues = (existingValuesData.values || []).map(v => v.value);
        const newValues = savedData.values;
        
        // Valores a serem adicionados
        const valuesToAdd = newValues.filter(v => !existingValues.includes(v));

        for (const value of valuesToAdd) {
          await attributesApi.createValue({ value, attributeId });
        }
        
        // Atualiza a lista local
        attribute.values = newValues;
        
        toast.showSuccess('Valores salvos e atualizados!');
      }
    }
    
    updateModelValue();
    showAttributeModal.value = false;

  } catch (error) {
    console.error('Erro ao salvar:', error);
    toast.showError('Ocorreu um erro ao salvar. Tente novamente.');
  } finally {
    savingAttribute.value = false;
  }
};
// --- FIM DAS NOVAS FUNÇÕES ---

// Função para atualizar o modelo
const updateModelValue = () => {
  emit('update:modelValue', attributes.value);
  
  if (form?.value) {
    form.value.attributes = attributes.value;
  }
};

// Função para fechar dropdowns ao clicar fora
const handleClickOutside = (event) => {
  if (!event.target.closest('.relative')) {
    showAttributeDropdown.value = null;
    showValueDropdown.value = null;
  }
};

// Watch para mudanças no modelValue
watch(() => props.modelValue, (newValue) => {
  if (Array.isArray(newValue)) {
    attributes.value = newValue.map(attr => ({
      ...attr,
      id: attr.id || `attr_${Date.now()}_${Math.random()}`
    }));

    // garantir que atributos existentes estejam na lista disponível
    attributes.value.forEach(attr => {
      if (attr.attributeId && attr.name) {
        const exists = availableAttributes.value.some(a => a.id === attr.attributeId);
        if (!exists) {
          availableAttributes.value.push({ id: attr.attributeId, name: attr.name });
        }
      }
    });

    // Inicializar inputs de novos valores
    attributes.value.forEach((_, index) => {
      if (!(index in valueSearchInputs.value)) {
        valueSearchInputs.value[index] = '';
      }
      // Inicializar estado de collapse (começar expandido)
      if (!(index in collapsedAttributes.value)) {
        collapsedAttributes.value[index] = false;
      }
    });
  }
}, { immediate: true, deep: true });

// Watch para mudança de categoria - LIMPAR ATRIBUTOS E VALORES
watch(selectedCategoryId, (newCategoryId, oldCategoryId) => {
  // Se mudou a categoria, limpar atributos e valores
  if (oldCategoryId && oldCategoryId !== newCategoryId) {
    attributes.value = [];
    availableAttributes.value = [];
    valueSearchInputs.value = {};
    valueSuggestions.value = {};
    collapsedAttributes.value = {};
    updateModelValue();
  }
  
  if (newCategoryId) {
    searchAttributes();
  }
}, { immediate: true });

// Lifecycle
onMounted(async () => {
  document.addEventListener('click', handleClickOutside);
  
  // Carregar categorias primeiro para permitir hierarquia
  await loadCategories();
  
  if (selectedCategoryId.value) {
    searchAttributes();
  }
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
});

const getSelectedValuesText = (attribute) => {
  const values = attribute.values || [];
  const count = values.length;

  if (count === 0) {
    return t('product.attributes.addValue');
  }

  const MAX_VISIBLE = 3;
  const visibleValues = values.slice(0, MAX_VISIBLE);
  let text = visibleValues.join(', ');

  if (count > MAX_VISIBLE) {
    text += `, ... (+${count - MAX_VISIBLE})`;
  }

  return text;
};

const isValueSelected = (attributeIndex, value) => {
  return attributes.value[attributeIndex]?.values?.includes(value) || false;
};

const toggleValueSelection = (attributeIndex, suggestion) => {
  const value = suggestion.value || suggestion;
  const attribute = attributes.value[attributeIndex];
  
  if (!attribute.values) {
    attribute.values = [];
  }
  
  const valueIndex = attribute.values.indexOf(value);
  
  if (valueIndex > -1) {
    attribute.values.splice(valueIndex, 1);
  } else {
    attribute.values.push(value);
  }
  
  updateModelValue();
};
</script>

<style scoped>
.drag-handle {
  transition: all 0.2s ease;
}

.drag-handle:hover {
  transform: scale(1.1);
}

/* Animações customizadas */
.attributes-section {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Estilos para drag and drop com temas */
:deep(.drag-ghost) {
  opacity: 0.6;
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
  color: var(--iluria-color-text-primary) !important;
}

:deep(.drag-chosen) {
  background: var(--iluria-color-surface-hover) !important;
  border: 1px solid var(--iluria-color-primary) !important;
}

/* Garantir que todos os elementos dentro do ghost usem as cores do tema */
:deep(.drag-ghost *) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
}

:deep(.drag-ghost svg) {
  color: var(--iluria-color-text-tertiary) !important;
}
</style> 
