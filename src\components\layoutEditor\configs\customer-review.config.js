export default {
  name: '<PERSON><PERSON><PERSON><PERSON><PERSON>',
  type: 'customer-review',
  dataComponent: 'customer-review',
  elementType: 'customer-review',
  className: 'iluria-customer-review',
  selectors: ['[data-component="customer-review"]', '.iluria-customer-review'],
  category: 'testimonials',
  description: 'Seção de avaliações e depoimentos de clientes',
  priority: 75,
  icon: 'star',
  
  html: `<div data-component="customer-review" data-element-type="customer-review" data-layout="grid" data-show-rating="true" class="iluria-customer-review" style="width: 100%; padding: 4rem 0; background: #f9fafb;">
    <div class="container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
      <div class="reviews-header" style="text-align: center; margin-bottom: 3rem;">
        <h2 style="font-size: 2.5rem; font-weight: bold; margin: 0 0 1rem; color: #1f2937;">O que nossos clientes dizem</h2>
        <div class="overall-rating" style="display: flex; align-items: center; justify-content: center; gap: 1rem; margin-bottom: 1rem;">
          <div class="stars" style="display: flex; gap: 4px;">
            ${Array.from({length: 5}).map(() => '<svg width="20" height="20" viewBox="0 0 24 24" fill="#fbbf24" stroke="#fbbf24" stroke-width="1"><polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/></svg>').join('')}
          </div>
          <span style="font-size: 1.1rem; font-weight: 600; color: #374151;">4.8 de 5.0 (2,847 avaliações)</span>
        </div>
      </div>
      <div class="reviews-grid" style="display: grid; grid-template-columns: repeat(3, 1fr); gap: 2rem;">
        ${Array.from({length: 6}).map((_, i) => `
          <div class="review-card" style="background: white; padding: 2rem; border-radius: 16px; box-shadow: 0 4px 6px rgba(0, 0, 0, 0.05); position: relative;">
            <div class="review-header" style="display: flex; align-items: center; gap: 1rem; margin-bottom: 1rem;">
              <div class="avatar" style="width: 48px; height: 48px; border-radius: 50%; background: linear-gradient(135deg, #667eea, #764ba2); display: flex; align-items: center; justify-content: center; color: white; font-weight: bold; font-size: 1.2rem;">
                ${String.fromCharCode(65 + i)}
              </div>
              <div>
                <h4 style="margin: 0; font-size: 1.1rem; font-weight: 600; color: #1f2937;">Cliente ${i + 1}</h4>
                <div class="stars" style="display: flex; gap: 2px; margin-top: 4px;">
                  ${Array.from({length: 5}).map((_, j) => `<svg width="14" height="14" viewBox="0 0 24 24" fill="${j < (4 + Math.random()) ? '#fbbf24' : '#e5e7eb'}" stroke="none"><polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/></svg>`).join('')}
                </div>
              </div>
            </div>
            <p class="review-text" style="margin: 0; line-height: 1.6; color: #4b5563; font-size: 0.95rem;">
              "${['Produto incrível, superou minhas expectativas!', 'Excelente qualidade e entrega rápida.', 'Recomendo para todos, muito satisfeito!', 'Atendimento excepcional e produto top.', 'Melhor compra que fiz este ano.', 'Qualidade impecável, voltarei a comprar.'][i]}"
            </p>
            <div class="review-date" style="margin-top: 1rem; font-size: 0.85rem; color: #9ca3af;">
              ${new Date(Date.now() - Math.random() * 30 * 24 * 60 * 60 * 1000).toLocaleDateString('pt-BR')}
            </div>
          </div>
        `).join('')}
      </div>
      <div class="reviews-footer" style="text-align: center; margin-top: 3rem;">
        <button style="background: #3b82f6; color: white; border: none; padding: 12px 30px; border-radius: 8px; font-size: 1rem; font-weight: 600; cursor: pointer; transition: background-color 0.3s ease;">Ver todas as avaliações</button>
      </div>
    </div>
    <style>
      @media (max-width: 768px) {
        .iluria-customer-review .reviews-grid { grid-template-columns: 1fr !important; gap: 1.5rem !important; }
        .iluria-customer-review .container { padding: 0 1rem !important; }
        .iluria-customer-review { padding: 2rem 0 !important; }
      }
      .review-card:hover { transform: translateY(-2px); box-shadow: 0 8px 16px rgba(0, 0, 0, 0.1) !important; }
    </style>
    
    <!-- CSS para neutralizar margens do container -->
    <style>
      .iluria-customer-review {
        margin: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
      }
      
      /* Neutralizar padding do element-content apenas para customer-review */
      .component-container:has(.iluria-customer-review) .element-content {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      /* Fallback para navegadores sem :has() */
      .element-content:has(.iluria-customer-review) {
        padding: 0 !important;
        margin: 0 !important;
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'customer-review-config',
      title: 'Configurar Avaliações',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"><polygon points="12,2 15.09,8.26 22,9.27 17,14.14 18.18,21.02 12,17.77 5.82,21.02 7,14.14 2,9.27 8.91,8.26"/></svg>`,
      condition: (element) => element?.getAttribute('data-component') === 'customer-review'
    }
  ],
  
  initialization: { autoInit: true, scriptLoader: 'initializeCustomerReviewComponent' },
  cleanup: { preserveStyles: ['display', 'grid-template-columns', 'gap', 'padding', 'margin', 'border-radius', 'box-shadow', 'background', 'color'] },
  detection: { priority: 55 }
} 