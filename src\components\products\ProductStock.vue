<template>
  <div class="space-y-4">
    <IluriaInputText
      id="stockQuantityTotal"
      name="stockQuantityTotal"
      :label="t('product.stockQuantity')"
      :suffix="getUnitText(form.stockQuantityTotal)"
      type="number"
      v-model.number="form.stockQuantityTotal"
      :formContext="props.formContext?.stockQuantityTotal"
    />
  </div>
</template>

<script setup>
import { inject, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

// Inject the product form data from parent component
const form = inject('productForm');

// Define props
const props = defineProps({
  formContext: {
    type: Object,
    default: () => ({})
  }
});

// Função para retornar o texto da unidade baseado na quantidade
const getUnitText = (quantity) => {
  return quantity > 1 ? 'unidades' : 'unidade';
};

// Define validation rules
const validationRules = {
  stockQuantityTotal: {}
};

// Expose validation rules
defineExpose({ validationRules });
</script> 
