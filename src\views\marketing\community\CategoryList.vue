<template>
  <div class="space-y-4">
    <div v-if="categories.length === 0" class="text-center py-8 text-[var(--iluria-color-text-muted)]">
      {{ $t('community.categoryManagement.noCategories') }}
    </div>
    
    <div v-else class="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
      <div 
        v-for="category in categories" 
        :key="category.id"
        class="bg-[var(--iluria-color-container-bg)] rounded-lg border border-[color:var(--iluria-color-border)] p-4 flex items-center justify-between group hover:border-[color:var(--iluria-color-border-hover)] transition-colors"
      >
        <span class="text-[var(--iluria-color-text-primary)]">{{ category.name }}</span>
        <IluriaButton
          color="text-danger"
          size="small"
          :hugeIcon="Delete01Icon"
          class="opacity-0 group-hover:opacity-100 transition-opacity"
          @click="showDeleteConfirmation(category)"
        />
      </div>
    </div>

    <!-- Confirmation Modal -->
    <IluriaConfirmationModal
      :isVisible="showDeleteModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      confirmText="Excluir"
      cancelText="Cancelar"
      type="error"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup>
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { useToastStore } from '@/stores/toast.store';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import { Delete01Icon } from '@hugeicons-pro/core-bulk-rounded';
const { t } = useI18n();

const toast = useToastStore();
const emit = defineEmits(['delete', 'edit']);
const props = defineProps({
  categories: {
    type: Array,
    required: true
  }
});

const showDeleteModal = ref(false);
const categoryToDelete = ref(null);

// Computed properties for modal
const confirmModalTitle = computed(() => t('common.confirmDeleteTitle'));
const confirmModalMessage = computed(() => 
  categoryToDelete.value 
    ? t('community.categoryManagement.confirmDeleteMessage',{name:categoryToDelete.value.name})
    : ''
);

const showDeleteConfirmation = (category) => {
  if (!category?.id) {
    toast.addToast({
      type: 'error',
      message: t('community.categoryManagement.error.invalidCategory')
    });
    return;
  }
  categoryToDelete.value = category;
  showDeleteModal.value = true;
};

const confirmDelete = async () => {
  try {
    if (!categoryToDelete.value?.id) {
      toast.addToast({
        type: 'error',
        message: t('common.idNotFound')
      });
      return;
    }
    emit('delete', categoryToDelete.value);
    showDeleteModal.value = false;
    categoryToDelete.value = null;
  } catch (error) {
    console.error('Error in confirmDelete:', error);
    toast.addToast({
      type: 'error',
      message: 'Erro ao excluir categoria'
    });
  }
};

const cancelDelete = () => {
  showDeleteModal.value = false;
  categoryToDelete.value = null;
};

const handleEdit = (category) => {
  emit('edit', category);
};
</script> 