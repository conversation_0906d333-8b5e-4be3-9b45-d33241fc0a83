import axios from 'axios';
import { API_URLS } from '@/config/api.config';

const endpoint = API_URLS.MEASUREMENT_TABLE_BASE_URL;

export const measurementTableApi = {
  getAll: (params = {}) => {
    const { page = 0, size = 10 } = params;
    
    return axios.get(endpoint, {
      params: {
        page,
        size
      }
    });
  },
  
  getAllList: () => axios.get(`${endpoint}/all`).then(response => {
    // Check for various possible data wrappers, including Spring Pageable's "content"
    const data = response.data?.content || response.data?.data || response.data;
    // Ensure we always return an array
    return Array.isArray(data) ? data : [];
  }),
  
  getById: (id) => axios.get(`${endpoint}/${id}`).then(response => {
    return response.data?.data || response.data;
  }),
  
  create: (measurementTable) => {
    return axios.post(endpoint, measurementTable);
  },
  
  update: (id, measurementTable) => {
    return axios.put(`${endpoint}/${id}`, measurementTable);
  },
  
  delete: (id) => axios.delete(`${endpoint}/${id}`),
  
  // Funções de upload de imagens
  uploadImage: async (id, file) => {
    try {
      if (!file?.type?.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem válida');
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await axios.post(`${endpoint}/${id}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data; // Retorna a URL da imagem
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      throw error;
    }
  },
  
  getImageUrl: (id) => {
    return `${endpoint}/${id}/images`;
  },
  
  deleteImage: (id) => {
    return axios.delete(`${endpoint}/${id}/images`);
  },

  // Funções de upload de imagens das colunas
  uploadColumnImage: async (measurementTableId, columnId, file) => {
    try {
      if (!file?.type?.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem válida');
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      const response = await axios.post(`${endpoint}/${measurementTableId}/columns/${columnId}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data; // Retorna a URL da imagem
    } catch (error) {
      console.error('Erro ao fazer upload da imagem da coluna:', error);
      throw error;
    }
  },
  
  deleteColumnImage: (measurementTableId, columnId) => {
    return axios.delete(`${endpoint}/${measurementTableId}/columns/${columnId}/images`);
  },

  // Upload em lote de imagens de colunas após criação da tabela
  batchUploadColumnImages: async (measurementTableId, columnImagesMap) => {
    try {
      const formData = new FormData();
      
      // Adicionar cada arquivo com o nome column_index
      Object.entries(columnImagesMap).forEach(([columnIndex, file]) => {
        if (file && file instanceof File) {
          formData.append(`column_${columnIndex}`, file);
        }
      });
      
      const response = await axios.post(`${endpoint}/${measurementTableId}/columns/images/batch`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      return response.data; // Retorna map com URLs das imagens por índice
    } catch (error) {
      console.error('Erro ao fazer upload em lote das imagens das colunas:', error);
      throw error;
    }
  }
};

export default measurementTableApi; 