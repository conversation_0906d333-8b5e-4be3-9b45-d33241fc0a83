# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build Commands

- `npm run dev` - Start development server with Vite (runs on `--host` flag)
- `npm run build` - Build the application for production
- `npm run preview` - Preview the built application

## Project Architecture

### Tech Stack
- **Framework**: Vue 3 with Composition API
- **Build Tool**: Vite with TailwindCSS 4.0
- **State Management**: Pinia with persisted state
- **UI Components**: PrimeVue 
- **Routing**: Vue Router with authentication guards
- **Internationalization**: Vue i18n (pt-br and en)
- **HTTP Client**: Axios with custom configuration
- **Icons**: HugeIcons Pro, FontAwesome, Heroicons

### Key Directories

#### `/src/components/`
- **`iluria/`** - Core design system components (buttons, forms, modals, etc.)
- **`layoutEditor/`** - Visual page builder system with component registry
- **`dashboard/`** - Analytics and dashboard components
- **`editor/`** - Content editing tools (Monaco, Editor.js)
- **`FileManager/`** - File upload/management system

#### `/src/views/`
- **`product/`** - Product management (CRUD, variations, categories)
- **`customer/`** - Customer management and exports
- **`order/`** - Order processing and management
- **`settings/`** - Store configuration (shipping, SEO, payments)
- **`layoutEditor/`** - Visual page builder interface
- **`blog/`** - Blog management system

#### `/src/services/`
API service layer with consistent naming: `[feature].service.js`
- Axios-based services for all backend communication
- Centralized error handling and authentication

#### `/src/stores/`
Pinia stores for global state management:
- `auth.store.js` - Authentication state and user management
- `toast.store.js` - Toast notifications

#### `/src/composables/`
Vue composables for shared functionality:
- **Layout Editor System**: `useComponentRegistry.js`, `useEditorSystem.js`, `useLayoutManager.js`
- **UI Utilities**: `useTheme.js`, `useFont.js`, `useUndoRedo.js`

### Layout Editor System

The project includes a sophisticated visual page builder with:

#### Component Configuration System
- **Centralized configs** in `/src/components/layoutEditor/configs/`
- Each component has a `*.config.js` file with HTML template, toolbar actions, and property editors
- Auto-loading system that discovers components automatically

#### Component Registry
- **Auto-detection** of components based on CSS selectors and data attributes
- **Dynamic script injection** for components requiring JavaScript
- **Property editors** for visual component configuration

#### Key Files
- `useComponentRegistry.js` - Core component registration system
- `useComponentScriptInjection.js` - Dynamic script loading
- `configs/index.js` - Auto-loads all component configurations

### Authentication & Routing

- **Route Guards**: All routes except login/signup require authentication
- **Store Integration**: Auth state managed via Pinia with persistence
- **Token Management**: Automatic token refresh and validation

### Development Patterns

#### Component Structure
- Use Vue 3 Composition API consistently
- Components in `components/iluria/` follow design system patterns
- Property validation with Vue props and Vee-Validate/Yup

#### API Integration
- Services follow consistent patterns: `[feature].service.js`
- Centralized axios configuration with interceptors
- Error handling through toast notifications

#### Internationalization
- Keys organized by feature: `[feature]_[lang].json`
- Support for Portuguese (pt-br) and English (en)
- Use `$t()` function for translations

### File Upload & Management

- **FileManager component** for file operations
- **Image processing** with Cropper.js integration
- **Multi-environment support** (development/production)

### Content Management

- **Editor.js integration** for rich content editing
- **Monaco Editor** for code editing
- **SEO management** across products, categories, and pages

### Key Configuration Files

- `vite.config.js` - Build configuration with custom root (`src/`)
- `tailwind.config.js` - TailwindCSS configuration (not present, likely uses defaults)
- `src/config/api.config.js` - API endpoint configuration
- `src/config/auth.js` - Authentication configuration
- `src/includes/i18n.js` - Internationalization setup

### Entry Point

The application bootstraps through:
1. `src/views/index/index.js` - Main application setup
2. `src/views/index/index.vue` - Root component
3. `src/views/index/router.js` - Route configuration

### Development Notes

- The project uses ES modules (`"type": "module"` in package.json)
- Vite serves from `src/` directory (custom root configuration)
- Build output goes to `dist/` directory
- No test framework configured - consider adding if needed
- Uses Docker for containerization (`Dockerfile` and `compose.yaml` present)

### Logging Guidelines

- Never use logs, console logs or any logs (except error or warn logs)
- Only use logs when explicitly requested by the user
- If 90% necessary, suggest log usage to the user before implementing

### Icon Usage Guidelines

- Sempre que for usar icones deve usar icones do Hugeicons

## Iluria Design System
- Ever use the Theme system useTheme.js file to apply color themes

### Core Components Philosophy
The project uses a comprehensive design system located in `/src/components/iluria/` with consistent patterns:

- **IluriaButton.vue** - Standardized button component with variants (solid, outline, ghost), colors (primary, secondary, danger, dark), and icon support
- **IluriaInputText.vue** - Form input with built-in validation display and mask support
- **IluriaToggleSwitch.vue** - Toggle switches for boolean settings
- **IluriaModal.vue** - Modal dialogs with consistent styling

### Form Component Pattern
All form components follow a consistent pattern:
- Use `defineModel()` for two-way binding with parent components
- Export `validationRules` as computed properties for dynamic validation
- Support `formContext` prop for validation state display

### Component Integration Rules
From `.cursor/rules/nocoments.mdc`:
- Use internationalized messages from `/src/locales/` instead of hardcoded text
- Use Iluria components (e.g., `IluriaInputText`) instead of native HTML elements
- Use `IluriaButton` instead of native button elements
- Use HugeIcons instead of custom SVG icons
- Use `@` imports for internal modules
- Minimize console.log usage at MAXIMUM (Never Use)
- Avoid unnecessary code comments

## Form Validation Architecture

### Validation Service Pattern
The project uses a centralized validation service (`/src/services/validation.service.js`) with Zod schemas:

```javascript
// Example validation rules in component
const validationRules = computed(() => {
  if (enabledCondition.value) {
    return {
      fieldName: requiredText(t('validation.fieldLabel'))
    };
  }
  return {};
});

defineExpose({ validationRules });
```

### Form Container Pattern
Views with forms follow this pattern:
1. Use PrimeVue Forms with Zod resolver
2. Collect validation rules from child components via refs
3. Combine rules in computed `formResolver`
4. Handle submission with centralized error handling

### Component Communication
- Parent-child: Use `defineModel()` for two-way binding
- Form validation: Child components expose `validationRules` as computed
- Services: Use composables (e.g., `useToast()`) for cross-cutting concerns

## Internationalization Patterns

### File Organization
- Language files organized by feature: `[feature]_[lang].json`
- Nested objects for related translations (e.g., `shippingConfig.zipCodeRestriction.*`)
- Consistent key naming: `[action][Entity][Property]` (e.g., `pickupDescriptionLabel`)

### Usage Patterns
- Always use `t()` function with keys, never hardcoded strings
- Prefer descriptive keys over generic ones
- Include context in translation keys for better maintainability

## State Management Patterns

### Pinia Store Structure
- Feature-based stores (e.g., `auth.store.js`, `toast.store.js`)
- Use `persistedState` plugin for data that should survive page reloads
- Composable pattern for store interaction (e.g., `useToast()`)

### Service Layer Architecture
- One service per domain: `[feature].service.js`
- Axios-based with centralized configuration
- Consistent error handling through toast notifications
- Services handle API communication, stores manage state

## Component Layout Patterns

### ViewContainer Pattern
Views use `ViewContainer` components with:
- Consistent title/subtitle structure using i18n
- Icon integration with color themes
- Nested form components with validation

### Grid Layouts
- Use CSS Grid for complex layouts: `grid grid-cols-1 lg:grid-cols-2 gap-6`
- Responsive design with Tailwind breakpoints
- Consistent spacing with gap utilities

## Error Handling Strategy

### Toast Notification System
- Centralized through `useToast()` composable
- Consistent message types: success, error, warning, info
- Support for persistent notifications and action buttons
- Automatic i18n integration for titles



## Development Workflow Considerations

### Component Development
1. Check existing Iluria components before creating new ones
2. Follow the design system patterns for consistency
3. Implement proper validation using the validation service
4. Use TypeScript-like prop validation with Vue 3
5. Ensure responsive design with Tailwind classes

### Testing Considerations
- No test framework currently configured
- Verify i18n message display in both languages