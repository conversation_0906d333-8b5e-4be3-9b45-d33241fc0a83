<template>
    <div class="store-mode-container">
        <IluriaHeader
            :title="t('storeMode.title')"
            :subtitle="t('storeMode.subtitle')"
            :showSave="true"
            :saveText="t('storeMode.saveButton')"
            @save-click="saveAllModes"
        />
        <ViewContainer :title="t('storeMode.maintenanceMode.title')" :icon="Settings02Icon" iconColor="orange">
            <div class="flex flex-row items-center gap-4">
                <p>{{ t('storeMode.maintenanceMode.activateLabel') }}</p>
                <IluriaToggleSwitch v-model="activated" />
            </div>

            <div class="flex flex-col gap-2 mt-5" v-if="activated == 1 || activated == true">
                <h1 class="text-lg font-bold">{{ t('storeMode.maintenanceMode.activatedTitle') }}</h1>
                <p>{{ t('storeMode.maintenanceMode.activatedDescription') }}</p>
                <div class="flex flex-col gap-4">
                    <IluriaRadioGroup
                        id="maintenance-type"
                        v-model="type"
                        :options="maintenanceTypeOptions"
                        :label="t('storeMode.maintenanceMode.typeLabel')"
                        direction="horizontal"
                    />
                </div>
            </div>
            <div class="flex flex-col mt-5">
                <div v-if="type == 'txt' && (activated == 1 || activated == true)">
                    <Textarea v-model="maintenanceMessage" />
                </div>
                <div v-if="type == 'img' && (activated == 1 || activated == true)">
                    <IluriaFileUpload @select="onFileSelect" :multiple="false" :showPreviewBox="true" />
                </div>
            </div>
        </ViewContainer>

        <ViewContainer :title="t('storeMode.vacationMode.title')" :icon="BeachIcon" iconColor="blue" class="mt-6">
            <div class="flex flex-row items-center gap-4">
                <p>{{ t('storeMode.vacationMode.activateLabel') }}</p>
                <IluriaToggleSwitch v-model="vacationActivated" />
            </div>

            <div class="flex flex-col gap-2 mt-5" v-if="vacationActivated == 1 || vacationActivated == true">
                <h1 class="text-lg font-bold">{{ t('storeMode.vacationMode.activatedTitle') }}</h1>
                <p>{{ t('storeMode.vacationMode.activatedDescription') }}</p>
            </div>
            <div class="flex flex-col mt-5">
                <div v-if="vacationActivated == 1 || vacationActivated == true">
                    <Textarea v-model="vacationMessage" :placeholder="t('storeMode.vacationMode.messagePlaceholder')" />
                </div>
            </div>
        </ViewContainer>
    </div>
</template>

<script setup>
import Textarea from '@/components/Textarea.vue';
import IluriaFileUpload from '@/components/iluria/form/IluriaFileUpload.vue';
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import storeService from '@/services/store.service';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { ref, watch, computed } from 'vue';
import { useToast } from '@/services/toast.service';
import { useI18n } from 'vue-i18n';
import { Settings02Icon, BeachIcon } from '@hugeicons-pro/core-stroke-rounded';

const { showSuccess, showError } = useToast();
const { t } = useI18n();

const type = ref('img');
const activated = ref(0);
const maintenanceMessage = ref('');
const selectedFile = ref(null);

// Variáveis para modo férias
const vacationActivated = ref(0);
const vacationMessage = ref('');

// Opções traduzidas para o radio group
const maintenanceTypeOptions = computed(() => [
    { value: 'img', label: t('storeMode.maintenanceMode.typeOptions.image') },
    { value: 'txt', label: t('storeMode.maintenanceMode.typeOptions.text') }
]);

// Watchers para garantir que apenas um modo esteja ativo por vez
watch(activated, (newValue) => {
    if (newValue === 1 || newValue === true) {
        vacationActivated.value = 0;
    }
});

watch(vacationActivated, (newValue) => {
    if (newValue === 1 || newValue === true) {
        activated.value = 0;
    }
});

const onFileSelect = (event) => {
    selectedFile.value = event.files[0] || null;

    if (event.files?.length) {
        for (const file of event.files) {
            if (!file.objectURL) {
                file.objectURL = URL.createObjectURL(file);
            }
        }
    }
};

const saveAllModes = async () => {
    try {
        // Validação para modo manutenção
        if (activated.value == 1 || activated.value == true) {
            if (type.value === 'img' && !selectedFile.value) {
                showError(t('storeMode.maintenanceMode.validation.imageRequired'));
                return;
            }
            if (type.value === 'txt' && (!maintenanceMessage.value || maintenanceMessage.value.trim() === '')) {
                showError(t('storeMode.maintenanceMode.validation.messageRequired'));
                return;
            }
        }

        // Validação para modo férias
        if (vacationActivated.value == 1 || vacationActivated.value == true) {
            if (!vacationMessage.value || vacationMessage.value.trim() === '') {
                showError(t('storeMode.vacationMode.validation.messageRequired'));
                return;
            }
        }

        // Salvar modo manutenção
        let finalImageUrl = null;
        let finalMaintenanceMessage = maintenanceMessage.value;

        if ((activated.value == 1 || activated.value == true) && type.value === 'img' && selectedFile.value) {
            const formData = new FormData();
            formData.append('file', selectedFile.value);

            const response = await storeService.uploadMaintenanceImage(formData);

            finalImageUrl = response.data;
            finalMaintenanceMessage = null;
        } else if ((activated.value == 1 || activated.value == true) && type.value === 'txt') {
            finalMaintenanceMessage = maintenanceMessage.value;
            finalImageUrl = null;
        }

        const maintenanceData = {
            maintenanceModeEnabled: activated.value,
            maintenanceMessage: finalMaintenanceMessage,
            imageUrl: finalImageUrl
        };

        await storeService.saveStoreStatus(maintenanceData);

        // Salvar modo férias
        const vacationData = {
            vacationModeEnabled: vacationActivated.value,
            vacationMessage: vacationMessage.value,
            imageUrl: null
        };

        await storeService.saveVacationStatus(vacationData);

        showSuccess(t('storeMode.messages.saveSuccess'));
    } catch (error) {
        console.error(error);
        showError(t('storeMode.messages.saveError'));
    }
};
</script>

<style scoped>


.store-mode-container {
    max-width: 1400px;
    margin: 0 auto;
    padding: 24px;
    background: var(--iluria-color-background);
    min-height: 100vh 647px;
}
</style>
