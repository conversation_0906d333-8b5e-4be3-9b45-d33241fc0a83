/**
 * Utilitário para detecção de dispositivos e geração de fingerprints
 */
class DeviceDetection {
    /**
     * Gera um fingerprint único para o dispositivo
     * @returns {string} Device fingerprint
     */
    generateDeviceFingerprint() {
        try {
            const canvas = document.createElement('canvas');
            const ctx = canvas.getContext('2d');
            ctx.textBaseline = 'top';
            ctx.font = '14px Arial';
            ctx.fillText('Device fingerprint', 2, 2);
            
            const fingerprint = [
                navigator.userAgent,
                navigator.language,
                screen.width + 'x' + screen.height,
                screen.colorDepth,
                new Date().getTimezoneOffset(),
                this.getCanvasFingerprint(canvas),
                navigator.platform,
                navigator.hardwareConcurrency || 'unknown',
                navigator.deviceMemory || 'unknown'
            ].join('|');
            
            // Gera hash simples do fingerprint
            return this.simpleHash(fingerprint);
        } catch (error) {
            console.warn('Erro ao gerar device fingerprint:', error);
            // Fallback para valores básicos
            return this.simpleHash([
                navigator.userAgent || 'unknown',
                navigator.platform || 'unknown',
                screen.width + 'x' + screen.height || 'unknown',
                new Date().getTimezoneOffset() || 0
            ].join('|'));
        }
    }

    /**
     * Gera fingerprint do canvas
     * @private
     * @param {HTMLCanvasElement} canvas
     * @returns {string}
     */
    getCanvasFingerprint(canvas) {
        try {
            return canvas.toDataURL();
        } catch (error) {
            return 'canvas_blocked';
        }
    }

    /**
     * Gera hash simples de uma string
     * @private
     * @param {string} str
     * @returns {string}
     */
    simpleHash(str) {
        let hash = 0;
        if (str.length === 0) return hash.toString();
        
        for (let i = 0; i < str.length; i++) {
            const char = str.charCodeAt(i);
            hash = ((hash << 5) - hash) + char;
            hash = hash & hash; // Convert to 32bit integer
        }
        
        return Math.abs(hash).toString(16);
    }

    /**
     * Obtém informações do dispositivo
     * @returns {Object} Informações do dispositivo
     */
    getDeviceInfo() {
        const userAgent = navigator.userAgent || '';
        const platform = navigator.platform || '';
        
        let deviceType = 'desktop';
        let deviceName = 'Dispositivo desconhecido';
        let osInfo = 'Sistema desconhecido';

        // Detecta tipo de dispositivo
        if (/Mobile|Android|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(userAgent)) {
            if (/iPad|Tablet/i.test(userAgent)) {
                deviceType = 'tablet';
            } else {
                deviceType = 'mobile';
            }
        }

        // Detecta sistema operacional
        if (platform.includes('Win')) {
            osInfo = 'Windows';
            deviceName = 'PC Windows';
        } else if (platform.includes('Mac')) {
            osInfo = 'macOS';
            deviceName = 'Mac';
        } else if (platform.includes('Linux')) {
            osInfo = 'Linux';
            deviceName = 'Linux PC';
        } else if (/Android/i.test(userAgent)) {
            osInfo = 'Android';
            deviceName = 'Android';
        } else if (/iPhone|iPad|iPod/i.test(userAgent)) {
            osInfo = 'iOS';
            deviceName = userAgent.includes('iPad') ? 'iPad' : 'iPhone';
        }

        return {
            deviceType,
            deviceName,
            osInfo,
            platform,
            userAgent,
            screenResolution: `${screen.width}x${screen.height}`,
            colorDepth: screen.colorDepth,
            pixelDepth: screen.pixelDepth,
            hardwareConcurrency: navigator.hardwareConcurrency || 'unknown',
            deviceMemory: navigator.deviceMemory || 'unknown'
        };
    }

    /**
     * Obtém informações do navegador
     * @returns {Object} Informações do navegador
     */
    getBrowserInfo() {
        const userAgent = navigator.userAgent || '';
        let browserName = 'Desconhecido';
        let browserVersion = 'Desconhecido';

        // Detecta navegador
        if (userAgent.includes('Chrome') && !userAgent.includes('Edg')) {
            browserName = 'Chrome';
            const match = userAgent.match(/Chrome\/([0-9.]+)/);
            if (match) browserVersion = match[1];
        } else if (userAgent.includes('Firefox')) {
            browserName = 'Firefox';
            const match = userAgent.match(/Firefox\/([0-9.]+)/);
            if (match) browserVersion = match[1];
        } else if (userAgent.includes('Safari') && !userAgent.includes('Chrome')) {
            browserName = 'Safari';
            const match = userAgent.match(/Version\/([0-9.]+)/);
            if (match) browserVersion = match[1];
        } else if (userAgent.includes('Edg')) {
            browserName = 'Edge';
            const match = userAgent.match(/Edg\/([0-9.]+)/);
            if (match) browserVersion = match[1];
        }

        return {
            name: browserName,
            version: browserVersion,
            userAgent,
            language: navigator.language || 'unknown',
            languages: navigator.languages || ['unknown'],
            cookieEnabled: navigator.cookieEnabled,
            doNotTrack: navigator.doNotTrack || 'unknown'
        };
    }

    /**
     * Obtém informações de localização baseada no timezone
     * @returns {Object} Informações de localização
     */
    getLocationInfo() {
        try {
            const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone;
            const locale = navigator.language || 'pt-BR';
            
            // Mapeia timezone para localização aproximada
            const timezoneMap = {
                'America/Sao_Paulo': { city: 'São Paulo', country: 'Brasil', region: 'América do Sul' },
                'America/New_York': { city: 'Nova York', country: 'Estados Unidos', region: 'América do Norte' },
                'Europe/London': { city: 'Londres', country: 'Reino Unido', region: 'Europa' },
                'Asia/Tokyo': { city: 'Tóquio', country: 'Japão', region: 'Ásia' },
                'Australia/Sydney': { city: 'Sydney', country: 'Austrália', region: 'Oceania' }
            };

            const location = timezoneMap[timezone] || {
                city: 'Desconhecido',
                country: 'Desconhecido',
                region: 'Desconhecido'
            };

            return {
                timezone,
                locale,
                timezoneOffset: new Date().getTimezoneOffset(),
                ...location
            };
        } catch (error) {
            console.warn('Erro ao obter informações de localização:', error);
            return {
                timezone: 'unknown',
                locale: 'unknown',
                timezoneOffset: 0,
                city: 'Desconhecido',
                country: 'Desconhecido',
                region: 'Desconhecido'
            };
        }
    }

    /**
     * Obtém informações da tela
     * @returns {Object} Informações completas da tela
     */
    getScreenInfo() {
        return {
            width: screen.width,
            height: screen.height,
            availWidth: screen.availWidth,
            availHeight: screen.availHeight,
            pixelRatio: window.devicePixelRatio || 1,
            colorDepth: screen.colorDepth || 'unknown',
            orientation: screen.orientation?.type || 'unknown'
        };
    }

    /**
     * Verifica se o dispositivo suporta touch
     * @returns {boolean}
     */
    isTouchDevice() {
        return 'ontouchstart' in window || 
               navigator.maxTouchPoints > 0 || 
               navigator.msMaxTouchPoints > 0;
    }

    /**
     * Obtém informações completas para identificação de sessão
     * @returns {Object} Informações completas
     */
    getSessionIdentificationInfo() {
        const deviceInfo = this.getDeviceInfo();
        const browserInfo = this.getBrowserInfo();
        const locationInfo = this.getLocationInfo();

        return {
            deviceInfo: JSON.stringify(deviceInfo),
            browserInfo: JSON.stringify(browserInfo),
            locationInfo: JSON.stringify(locationInfo),
            userAgent: navigator.userAgent || 'unknown',
            screenResolution: `${screen.width}x${screen.height}`,
            timezone: locationInfo.timezone,
            timestamp: new Date().toISOString()
        };
    }
}

export default new DeviceDetection();
