<template>
  <div class="page-container">
    <!-- Page Header -->
    <IluriaHeader
      :title="isEditing ? t('measurementTable.editTable') : t('measurementTable.createNew')"
      :subtitle="isEditing ? t('measurementTable.editSubtitle') : t('measurementTable.createSubtitle')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="t('save')"
      @cancel-click="handleCancel"
      @save-click="handleSave"
    />

    <!-- Main Content -->
    <ViewContainer
      :title="t('measurementTable.formTitle', 'Configuração da Tabela')"
      :subtitle="t('measurementTable.formSubtitle', 'Configure os dados da tabela de medidas')"
      :icon="RulerIcon"
      iconColor="green"
    >

    <!-- BEGIN: Form -->
    <Form v-slot="$form" :resolver="resolver" :validate-on-submit="true" :validate-on-blur="false" :validate-on-change="false" :validate-on-value-update="false" :initial-values="form" class="w-full max-w-full">
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6 mb-6">
        <IluriaInputText
          id="measurementTableName"
          name="name"
          v-model="form.name"
          :label="t('measurementTable.name')"
          :placeholder="t('measurementTable.namePlaceholder')"
          :formContext="$form.name"
          @update:model-value="(value) => updateFormField('name', value)"
        />
        
      </div>

      <div class="mb-6">
        <IluriaRadioGroup
          id="measurementTableType"
          name="type"
          v-model="form.type"
          :label="t('measurementTable.type')"
          :options="typeOptions"
          :formContext="$form.type"
          @update:model-value="onTypeChange"
        />
      </div>

      <!-- Tipo Imagem -->
      <div v-if="form.type === 'IMAGE'" class="mb-6">
        <IluriaSimpleImageUpload
          v-model="form.imageUrl"
          :label="t('measurementTable.image')"
          :description="t('measurementTable.imageDescription')"
          accept="image/*"
          :add-button-text="t('measurementTable.addImage')"
          :change-button-text="t('measurementTable.changeImage')"
          :remove-button-text="t('measurementTable.removeImage')"
          :format-hint="t('measurementTable.imageFormats')"
          :prevent-cache="true"
          @change="onImageChange"
        />
      </div>

      <!-- Tipo Estruturado -->
      <div v-if="form.type === 'STRUCTURED'" class="space-y-6">
        <!-- Configuração com Tags -->
        <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
          <!-- Colunas com Tags -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">{{ t('measurementTable.columns') }}</h3>
            
            <IluriaInputTags
              id="columnTags"
              v-model="columnTags"
              :label="t('measurementTable.columnTagsLabel')"
              :placeholder="t('measurementTable.columnTagsPlaceholder')"
              :draggable="true"
            />
            
            <p class="text-sm text-gray-600">{{ t('measurementTable.columnTagsHint') }}</p>
          </div>
          
          <!-- Tamanhos com Tags -->
          <div class="space-y-4">
            <h3 class="text-lg font-medium">{{ t('measurementTable.sizes') }}</h3>
            
            <IluriaInputTags
              id="sizeTags"
              v-model="sizeTags"
              :label="t('measurementTable.sizeTagsLabel')"
              :placeholder="t('measurementTable.sizeTagsPlaceholder')"
              :draggable="true"
            />
            
            <p class="text-sm text-gray-600">{{ t('measurementTable.sizeTagsHint') }}</p>
            
            <!-- Range de Tamanhos Pré-definidos -->
            <div class="mt-4">
              <label class="block text-sm font-medium text-gray-700 mb-2">
                {{ t('measurementTable.quickSizeRanges') }}
              </label>
              <div class="flex flex-wrap gap-2 mb-4">
                <IluriaButton 
                  v-for="range in sizeRanges.filter(r => !r.isCustom)"
                  :key="range.key"
                  @click="applySizeRange(range.sizes)"
                  color="secondary"
                  size="small"
                >
                  {{ t(`measurementTable.${range.key}`) }}
                </IluriaButton>
              </div>
              
              
            </div>
          </div>
        </div>

        <!-- Tabela de Medidas Gerada -->
        <div v-if="columnTags.length > 0 && sizeTags.length > 0" class="mt-6">
          <h3 class="text-lg font-medium mb-4">{{ t('measurementTable.measurementGrid') }}</h3>
          
          <div class="overflow-x-auto border rounded-lg">
            <table class="min-w-full border-collapse bg-white">
              <thead class="bg-gray-50">
                <tr>
                  <th class="border border-gray-300 px-4 py-3 text-left font-medium text-gray-900">
                    {{ t('measurementTable.size') }}
                  </th>
                    <th 
                    v-for="(column, index) in columnTags" 
                    :key="index"
                    class="border border-gray-300 px-4 py-3 text-center font-medium text-gray-900"
                    >
                    <div class="space-y-2">
                      <div class="flex items-center justify-center gap-2">
                        <span class="block">{{ column }}</span>
                <IluriaButton 
                          @click="openImageUploadModal(index, column)"
                          color="secondary"
                  size="small"
                          :hugeIcon="Image01Icon"
                          class="!p-1"
                   />
              </div>
                    <div class="flex justify-center">
                      <select
                        v-model="columnUnits[index]"
                        class="px-2 py-1 text-xs border border-gray-300 rounded-md bg-white hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 min-w-[60px] cursor-pointer transition-colors"
                        @change="updateColumnUnit(index, $event.target.value)"
                      >
                        <option v-for="unit in unitOptions" :key="unit.value" :value="unit.value">
                          {{ unit.label }}
                        </option>
                      </select>
                    </div>
            </div>
                </th>
              </tr>
            </thead>
            <tbody>
              <tr v-for="(size, sizeIndex) in sizeTags" :key="sizeIndex" class="hover:bg-gray-50">
                <td class="border border-gray-300 px-4 py-3 font-medium text-gray-900">
                  {{ size }}
                </td>
                <td 
                  v-for="(column, colIndex) in columnTags" 
                  :key="colIndex"
                  class="border border-gray-300 px-2 py-2"
                >
                  <IluriaInputText
                    :key="`measurement-${sizeIndex}-${colIndex}`"
                    :model-value="getMeasurementValue(sizeIndex, colIndex)"
                    @update:model-value="(value) => setMeasurementValue(sizeIndex, colIndex, value)"
                    :placeholder="t('measurementTable.measurementPlaceholder')"
                    class="w-full text-center measurement-input"
                    @keydown.enter.prevent
                    @keydown.tab.stop="moveToNextCell(sizeIndex, colIndex, $event)"
                  />
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  </Form>

  <!-- Preview -->
  <div v-if="showPreview" class="mt-8">
    <IluriaTitle class="text-lg font-medium mb-4">{{ t('measurementTable.preview') }}</IluriaTitle>
    <MeasurementTablePreview 
      :measurement-table="form" 
      :product-images="productImages"
    />
  </div>

  <!-- Modal de Upload de Imagem para Coluna -->
  <IluriaModal 
    v-model="showImageUploadModal" 
    :title="t('measurementTable.uploadColumnImage')"
    size="md"
  >
    <div v-if="selectedColumnForImage" class="space-y-4">
      <div class="text-center">
        <h4 class="text-lg font-medium text-gray-900 mb-2">
          {{ selectedColumnForImage.name }}
        </h4>
        <p class="text-sm text-gray-600">
          {{ t('measurementTable.columnImageDescription') }}
        </p>
      </div>

      <IluriaSimpleImageUpload
        v-model="columnImagePreview"
        :label="t('measurementTable.columnImage')"
        :description="t('measurementTable.columnImageHelp')"
        accept="image/*"
        :add-button-text="t('measurementTable.addColumnImage')"
        :change-button-text="t('measurementTable.changeColumnImage')"
        :remove-button-text="t('measurementTable.removeColumnImage')"
        :format-hint="t('measurementTable.imageFormats')"
        :prevent-cache="true"
        @change="onColumnImageChange"
      />
    </div>

    <template #footer>
      <div class="flex justify-end gap-3">
        <IluriaButton 
          @click="closeImageUploadModal" 
          color="secondary"
        >
          {{ t('cancel') }}
        </IluriaButton>
        <IluriaButton 
          @click="saveColumnImage" 
          color="primary"
          :disabled="!columnImagePreview"
        >
          {{ t('save') }}
        </IluriaButton>
  </div>
    </template>
  </IluriaModal>
</ViewContainer>
</div>
</template>

<script setup>
import { ref, computed, watch, nextTick, onMounted } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { Image01Icon } from '@hugeicons-pro/core-bulk-rounded'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaRadioGroup from '@/components/iluria/form/IluriaRadioGroup.vue'
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import MeasurementTablePreview from './MeasurementTablePreview.vue'
import { measurementTableApi } from '@/services/measurementTable.service'
import { Form } from '@primevue/forms'
import { zodResolver } from '@primevue/forms/resolvers/zod'
import { z } from 'zod'
import IluriaInputTags from '@/components/iluria/form/IluriaInputTags.vue'
import IluriaTitle from '@/components/iluria/IluriaTitle.vue'
import { RulerIcon } from '@hugeicons-pro/core-bulk-rounded'

const { t } = useI18n()
const toast = useToast()
const router = useRouter()
const route = useRoute()

const props = defineProps({
  modelValue: {
    type: Object,
    default: () => ({})
  },
  showPreview: {
    type: Boolean,
    default: true
  },
  measurementTableId: {
    type: String,
    default: null
  },
  productImages: {
    type: Array,
    default: () => []
  }
})

const emit = defineEmits(['update:modelValue', 'save', 'cancel'])

const form = ref({
  name: '',
  type: 'STRUCTURED',
  imageUrl: '',
  columns: [],
  rows: [],
  newImageFile: null
})

// Watch for props changes to update form
watch(() => props.measurementTableId, async () => {
  if (props.measurementTableId) {
    await loadTable()
  }
}, { immediate: true })

const typeOptions = [
  { label: t('measurementTable.typeImage'), value: 'IMAGE' },
  { label: t('measurementTable.typeStructured'), value: 'STRUCTURED' }
]

const unitOptions = [
  { label: 'cm', value: 'cm' },
  { label: 'mm', value: 'mm' },
  { label: 'pol', value: 'in' },
  { label: 'm', value: 'm' },
]

const sizeRanges = [
  { key: 'sizeRangePPtoGG', sizes: ['PP', 'P', 'M', 'G', 'GG'] },
  { key: 'sizeRangeXStoXL', sizes: ['XS', 'S', 'M', 'L', 'XL'] },
  { key: 'sizeRangeXStoXXL', sizes: ['XS', 'S', 'M', 'L', 'XL', 'XXL'] },
  { key: 'sizeRangeNumeric', sizes: ['36', '38', '40', '42', '44', '46', '48'] },
  { key: 'sizeRangeInfantil', sizes: ['2', '4', '6', '8', '10', '12', '14', '16'] },
  { key: 'sizeRangeBaby', sizes: ['RN', '3M', '6M', '9M', '12M', '18M', '24M'] },
  { key: 'sizeRangeCustom', sizes: [], isCustom: true }
]

// Tags para colunas e tamanhos
const columnTags = ref([])
const sizeTags = ref([])
const columnUnits = ref([])
const measurementGrid = ref({})

const showImageUploadModal = ref(false)
const selectedColumnForImage = ref(null)
const columnImagePreview = ref(null)

// Flag para prevenir recursão infinita
const isUpdatingFromProps = ref(false)

// Função para gerar ID único temporário
const generateTempId = (prefix = 'temp') => {
  return `${prefix}_${Date.now()}_${Math.random().toString(36).substring(2, 9)}`
}

const updateFormField = (field, value) => {
  if (!isUpdatingFromProps.value) {
    const updatedForm = { ...form.value }
    updatedForm[field] = value
    form.value = updatedForm
  }
}

watch(columnTags, (newColumns, oldColumns) => {
  if (!isUpdatingFromProps.value && JSON.stringify(newColumns) !== JSON.stringify(oldColumns)) {
    syncColumnsFromTags(newColumns)
  }
}, { deep: true })

watch(sizeTags, (newSizes, oldSizes) => {
  if (!isUpdatingFromProps.value && JSON.stringify(newSizes) !== JSON.stringify(oldSizes)) {
    syncRowsFromTags(newSizes)
  }
}, { deep: true })

const getMeasurementValue = (sizeIndex, colIndex) => {
  const key = `${sizeIndex}-${colIndex}`
  return measurementGrid.value[key] || null
}

const setMeasurementValue = (sizeIndex, colIndex, value) => {
  const key = `${sizeIndex}-${colIndex}`
    if (measurementGrid.value[key] !== value) {
    measurementGrid.value[key] = value
    clearTimeout(updateFormFromGrid.timeoutId)
    updateFormFromGrid.timeoutId = setTimeout(() => {
      updateFormFromGrid()
    }, 50)
  }
}

const moveToNextCell = (sizeIndex, colIndex, event) => {
  event.preventDefault()
  
  let nextColIndex = colIndex + 1
  let nextSizeIndex = sizeIndex
  
  if (nextColIndex >= columnTags.value.length) {
    nextColIndex = 0
    nextSizeIndex = sizeIndex + 1
  }
  
  if (nextSizeIndex >= sizeTags.value.length) {
    nextColIndex = 0
    nextSizeIndex = 0
  }
  
  nextTick(() => {
    const inputs = document.querySelectorAll('.measurement-input')
    const nextInputIndex = nextSizeIndex * columnTags.value.length + nextColIndex
    if (inputs[nextInputIndex]) {
      inputs[nextInputIndex].focus()
    }
  })
}

const applySizeRange = (sizes) => {
  sizeTags.value = [...sizes]
}

const updateColumnUnit = (index, unit) => {
  columnUnits.value[index] = unit
  syncColumnsFromTags(columnTags.value)
}

const openImageUploadModal = (columnIndex, columnName) => {
  const column = form.value.columns?.[columnIndex]
  
  selectedColumnForImage.value = {
    index: columnIndex,
    name: columnName,
    id: column?.id
  }
  
  if (column?.imageUrl) {
    columnImagePreview.value = column.imageUrl
  } else {
    columnImagePreview.value = null
  }
  
  showImageUploadModal.value = true
}

const closeImageUploadModal = () => {
  showImageUploadModal.value = false
  selectedColumnForImage.value = null
  columnImagePreview.value = null
}

const onColumnImageChange = (file) => {
  columnImagePreview.value = file
}

const saveColumnImage = async () => {
  if (!selectedColumnForImage.value || !columnImagePreview.value) return
  
  const columnIndex = selectedColumnForImage.value.index
  const columnId = selectedColumnForImage.value.id
  
  // Atualizar o formulário para incluir a imagem na coluna
  const updatedForm = { ...form.value }
  
  // Garantir que as colunas existem
  if (!updatedForm.columns) {
    updatedForm.columns = []
  }
  
  // Garantir que a coluna existe
  while (updatedForm.columns.length <= columnIndex) {
    updatedForm.columns.push({
      id: generateTempId(),
      label: columnTags.value[updatedForm.columns.length] || '',
      unit: columnUnits.value[updatedForm.columns.length] || 'cm',
      imageUrl: '',
      position: updatedForm.columns.length
    })
  }
  
  // Verificar se pode fazer upload individual (tabela existe, arquivo válido, ID real não temporário)
  const isULID = columnId && columnId.length === 26 && !columnId.includes('_')
  const canUploadIndividually = props.measurementTableId && 
      columnImagePreview.value instanceof File && 
      isULID
  
  if (canUploadIndividually) {
    try {
      const imageUrl = await measurementTableApi.uploadColumnImage(
        props.measurementTableId, 
        columnId, 
        columnImagePreview.value
      )
      updatedForm.columns[columnIndex].imageUrl = imageUrl
      form.value = updatedForm
      closeImageUploadModal()
      toast.showSuccess(t('measurementTable.columnImageSaved'))
    } catch (error) {
      console.error('Erro no upload individual da imagem da coluna:', error)
      toast.showError(t('measurementTable.imageUploadError'))
    }
  } else {
    // Caso seja novo (sem ID real) ou ID temporário, armazenar para upload posterior
    if (columnImagePreview.value instanceof File) {
      // Armazenar arquivo por ID da coluna, não por índice
      if (!updatedForm.pendingColumnImagesByColumnId) {
        updatedForm.pendingColumnImagesByColumnId = {}
      }
      updatedForm.pendingColumnImagesByColumnId[columnId] = {
        file: columnImagePreview.value,
        columnIndex: columnIndex // Guardar índice para mapear na hora do upload
      }
      
      // Manter compatibilidade com sistema antigo para transição
      if (!updatedForm.pendingColumnImages) {
        updatedForm.pendingColumnImages = {}
      }
      updatedForm.pendingColumnImages[columnIndex] = columnImagePreview.value
      
      // Criar preview local
      const reader = new FileReader()
      reader.onload = (e) => {
        updatedForm.columns[columnIndex].imageUrl = e.target.result
        form.value = updatedForm
        closeImageUploadModal()
        toast.showSuccess(t('measurementTable.columnImageSaved'))
      }
      reader.readAsDataURL(columnImagePreview.value)
    } else {
      // Se é uma URL, usar diretamente
      updatedForm.columns[columnIndex].imageUrl = columnImagePreview.value
      form.value = updatedForm
      closeImageUploadModal()
      toast.showSuccess(t('measurementTable.columnImageSaved'))
    }
  }
}

const syncColumnsFromTags = (columns) => {
  const updatedForm = { ...form.value }
  const existingColumns = updatedForm.columns || []
  
  // Criar mapa de colunas existentes por label para preservar IDs e imagens durante reordenação
  const existingColumnsByLabel = {}
  existingColumns.forEach(col => {
    if (col.label) {
      existingColumnsByLabel[col.label] = col
    }
  })
  
  updatedForm.columns = columns.map((label, index) => {
    // Primeiro, tentar encontrar coluna existente pelo label
    const existingColumnByLabel = existingColumnsByLabel[label]
    // Fallback para coluna na mesma posição
    const existingColumnByIndex = existingColumns[index]
    
    const columnToUse = existingColumnByLabel || existingColumnByIndex
    
    return {
      id: columnToUse?.id || generateTempId(),
      label,
      unit: columnUnits.value[index] || columnToUse?.unit || 'cm',
      imageUrl: columnToUse?.imageUrl || '',
      position: index
    }
  })
  form.value = updatedForm
  
  // Ajustar tamanho do array de unidades
  while (columnUnits.value.length < columns.length) {
    columnUnits.value.push('cm')
  }
  if (columnUnits.value.length > columns.length) {
    columnUnits.value = columnUnits.value.slice(0, columns.length)
  }
}

const syncRowsFromTags = (sizes) => {
  const updatedForm = { ...form.value }
  const existingRows = updatedForm.rows || []
  
  updatedForm.rows = sizes.map((sizeLabel, index) => {
    const existingRow = existingRows[index]
    return {
      id: existingRow?.id || generateTempId(),
      sizeLabel,
      position: index,
      values: existingRow?.values || []
    }
  })
  form.value = updatedForm
}

const updateFormFromGrid = () => {
  if (isUpdatingFromProps.value) return
  
  const updatedForm = { ...form.value }
  
  if (!updatedForm.columns) {
    updatedForm.columns = []
  }
  
  // Criar mapa de colunas existentes por label para preservar durante grid updates
  const existingColumnsByLabel = {}
  updatedForm.columns.forEach(col => {
    if (col.label) {
      existingColumnsByLabel[col.label] = col
    }
  })
  
  updatedForm.columns = columnTags.value.map((label, index) => {
    const existingColumn = existingColumnsByLabel[label] || updatedForm.columns[index]
    return {
      id: existingColumn?.id || generateTempId(),
      label,
      unit: columnUnits.value[index] || existingColumn?.unit || 'cm',
      imageUrl: existingColumn?.imageUrl || '',
      position: index
    }
  })
  
  updatedForm.rows = sizeTags.value.map((sizeLabel, sizeIndex) => ({
    id: updatedForm.rows?.[sizeIndex]?.id || generateTempId(),
    sizeLabel,
    position: sizeIndex,
    values: columnTags.value.map((columnLabel, colIndex) => ({
      id: updatedForm.rows?.[sizeIndex]?.values?.[colIndex]?.id || generateTempId(),
      value: getMeasurementValue(sizeIndex, colIndex) || null
    }))
  }))
  
  form.value = updatedForm
}

const resolver = zodResolver(
  z.object({
    name: z.string()
      .trim()
      .min(1, t('measurementTable.nameRequired'))
      .max(255, t('validation.maxLength', { field: t('measurementTable.name'), length: 255 })),
    type: z.enum(['IMAGE', 'STRUCTURED'], {
      required_error: t('measurementTable.typeRequired'),
    }),
    columns: z.array(z.object({
      label: z.string().min(1, t('validation.fieldRequired', { field: t('measurementTable.columnLabel') })),
      unit: z.string().optional(),
      imageUrl: z.string().optional(),
    })).optional(),
    rows: z.array(z.object({
      sizeLabel: z.string().min(1, t('validation.fieldRequired', { field: t('measurementTable.sizeLabel') })),
      values: z.array(z.object({
        value: z.string().nullable().optional(),
      })).optional(),
    })).optional(),
  })
)

const onImageChange = async (file) => {
  const updatedForm = { ...form.value }
  
  if (!file) {
    if (props.measurementTableId && updatedForm.imageUrl && updatedForm.imageUrl.startsWith('http')) {
      try {
        await measurementTableApi.deleteImage(props.measurementTableId)
        toast.showSuccess(t('measurementTable.imageDeleteSuccess'))
      } catch (error) {
        console.error('Erro ao remover a imagem:', error)
        toast.showError(t('measurementTable.imageDeleteError'))
      }
    }
    
    updatedForm.imageUrl = ''
    updatedForm.newImageFile = null
    form.value = updatedForm
    return
  }
  
  if (file.size > 15 * 1024 * 1024) {
    toast.showError(t('measurementTable.imageTooLarge') || 'A imagem não pode ser maior que 15MB')
    return
  }
  
  updatedForm.newImageFile = file
  
  if (props.measurementTableId) {
    try {
      const imageUrl = await measurementTableApi.uploadImage(props.measurementTableId, file)
      updatedForm.imageUrl = imageUrl
      updatedForm.newImageFile = null
      toast.showSuccess(t('measurementTable.imageUploadSuccess'))
    } catch (error) {
      console.error('Erro no upload:', error)
      toast.showError(t('measurementTable.imageUploadError'))
      return
    }
  } else {
    const reader = new FileReader()
    reader.onload = (e) => {
      updatedForm.imageUrl = e.target.result
      form.value = updatedForm
    }
    reader.readAsDataURL(file)
    return
  }
  
  form.value = updatedForm
}

const onTypeChange = (newType) => {
  const updatedForm = { ...form.value }
  updatedForm.type = newType
  if (newType === 'IMAGE') {
    updatedForm.columns = []
    updatedForm.rows = []
    columnTags.value = []
    sizeTags.value = []
    measurementGrid.value = {}
  } else {
    updatedForm.imageUrl = ''
    updatedForm.newImageFile = null
  }
  form.value = updatedForm
}

// Inicializar tags a partir do formulário existente
watch(() => props.modelValue, (newValue, oldValue) => {
  // Evitar processar se o valor é o mesmo ou se não há referências válidas
  if (newValue === oldValue || !columnTags.value || !sizeTags.value) return
  
  try {
    isUpdatingFromProps.value = true
    
    // Limpar estado anterior apenas se necessário
    if (!newValue || (!newValue.columns && !newValue.rows)) {
      columnTags.value = []
      sizeTags.value = []
      columnUnits.value = []
      measurementGrid.value = {}
      return
    }
    
    // Só atualizar se realmente mudou
    const newColumns = Array.isArray(newValue.columns) ? newValue.columns : []
    const newRows = Array.isArray(newValue.rows) ? newValue.rows : []
    
    const currentColumnLabels = columnTags.value ? columnTags.value.join(',') : ''
    const newColumnLabels = newColumns.map(col => col?.label || '').join(',')
    
    if (currentColumnLabels !== newColumnLabels) {
      columnTags.value = newColumns.map(col => col?.label || '')
      columnUnits.value = newColumns.map(col => col?.unit || 'cm')
    }
    
    const currentRowLabels = sizeTags.value ? sizeTags.value.join(',') : ''
    const newRowLabels = newRows.map(row => row?.sizeLabel || '').join(',')
    
    if (currentRowLabels !== newRowLabels) {
      sizeTags.value = newRows.map(row => row?.sizeLabel || '')
      
      // Limpar grade existente
      measurementGrid.value = {}
      
      // Reconstituir a grade de medidas
      newRows.forEach((row, sizeIndex) => {
        if (row?.values && Array.isArray(row.values) && row.values.length > 0) {
          row.values.forEach((value, colIndex) => {
            if (value && typeof value.value !== 'undefined') {
              const key = `${sizeIndex}-${colIndex}`
              measurementGrid.value[key] = value.value
            }
          })
        }
      })
    }
  } catch (error) {
    console.error('Error in MeasurementTableForm watch:', error)
  } finally {
    // Aguardar próximo tick para garantir que todas as atualizações foram processadas
    nextTick(() => {
      try {
        isUpdatingFromProps.value = false
      } catch (error) {
        console.error('Error resetting isUpdatingFromProps:', error)
      }
    })
  }
}, { immediate: true })

defineExpose({
  uploadPendingImage: async () => {
    if (form.value.newImageFile && props.measurementTableId) {
      try {
        const imageUrl = await measurementTableApi.uploadImage(props.measurementTableId, form.value.newImageFile)
        const updatedForm = { ...form.value }
        updatedForm.imageUrl = imageUrl
        updatedForm.newImageFile = null
        form.value = updatedForm
        toast.showSuccess(t('measurementTable.imageUploadSuccess'))
        return imageUrl
      } catch (error) {
        toast.showError(t('measurementTable.imageUploadError'))
        throw error
      }
    }
    return null
  },
  
  uploadPendingColumnImages: async () => {
    if ((form.value.pendingColumnImagesByColumnId || form.value.pendingColumnImages) && props.measurementTableId) {
      try {
        let uploadedUrls = {}
        const updatedForm = { ...form.value }
        
        // Usar novo sistema baseado em IDs de colunas se disponível
        if (form.value.pendingColumnImagesByColumnId) {
          // Mapear IDs de colunas para índices atuais para upload em lote
          const indexedImages = {}
          Object.entries(form.value.pendingColumnImagesByColumnId).forEach(([columnId, data]) => {
            // Encontrar índice atual da coluna pelo ID
            const currentIndex = updatedForm.columns.findIndex(col => col.id === columnId)
            if (currentIndex >= 0) {
              indexedImages[currentIndex] = data.file
            }
          })
          
          if (Object.keys(indexedImages).length > 0) {
            uploadedUrls = await measurementTableApi.batchUploadColumnImages(
              props.measurementTableId, 
              indexedImages
            )
          }
        } 
        // Fallback para sistema antigo
        else if (form.value.pendingColumnImages) {
          uploadedUrls = await measurementTableApi.batchUploadColumnImages(
            props.measurementTableId, 
            form.value.pendingColumnImages
          )
        }
        
        // Atualizar URLs das colunas com as URLs reais do S3
        Object.entries(uploadedUrls).forEach(([columnIndex, imageUrl]) => {
          const index = parseInt(columnIndex)
          if (updatedForm.columns[index]) {
            updatedForm.columns[index].imageUrl = imageUrl
          }
        })
        
        // Limpar imagens pendentes
        updatedForm.pendingColumnImages = {}
        updatedForm.pendingColumnImagesByColumnId = {}
        form.value = updatedForm
        
        toast.showSuccess(t('measurementTable.columnImageSaved'))
        return uploadedUrls
      } catch (error) {
        toast.showError(t('measurementTable.imageUploadError'))
        throw error
      }
    }
    return {}
  }
})

// Editing helpers
const isEditing = computed(() => !!route.params.id)
const tableId = computed(() => route.params.id)

const loading = ref(false)
const saving = ref(false)
const showPreview = ref(true) // Added showPreview ref with default value true

const loadTable = async () => {
  if (!isEditing.value) return
  try {
    loading.value = true
    const table = await measurementTableApi.getById(tableId.value)
    
    // Populate form
    form.value = {
      name: table.name || '',
      type: table.type || 'STRUCTURED',
      imageUrl: table.imageUrl || '',
      columns: table.columns || [],
      rows: table.rows || [],
      newImageFile: null
    }

    // Populate tags and grid
    if (table.type === 'STRUCTURED') {
      columnTags.value = table.columns?.map(col => col.label) || []
      columnUnits.value = table.columns?.map(col => col.unit || 'cm') || []
      sizeTags.value = table.rows?.map(row => row.sizeLabel) || []
      
      // Populate measurement grid
      measurementGrid.value = {}
      table.rows?.forEach((row, sizeIndex) => {
        row.values?.forEach((value, colIndex) => {
          if (value && typeof value.value !== 'undefined') {
            const key = `${sizeIndex}-${colIndex}`
            measurementGrid.value[key] = value.value
          }
        })
      })
    }
  } catch (error) {
    console.error('Error loading measurement table:', error)
    toast.showError(t('measurementTable.errorLoading'))
    router.back()
  } finally {
    loading.value = false
  }
}

// Preview readiness
const isPreviewReady = computed(() => {
  if (form.value.type === 'IMAGE') {
    return !!form.value.imageUrl
  }
  return columnTags.value.length > 0 && sizeTags.value.length > 0
})

const handleSave = async () => {
  try {
    saving.value = true

    if (!form.value.name || form.value.name.trim() === '') {
      toast.showError(t('measurementTable.nameRequired'))
      return
    }

    const dataToSave = {
      name: form.value.name.trim(),
      type: form.value.type,
      columns: form.value.columns?.map(col => ({
        // Ao editar, só incluir ID se não for temporário
        ...(isEditing.value && col.id && !col.id.includes('_') ? { id: col.id } : {}),
        label: col.label,
        unit: col.unit,
        position: col.position,
        imageUrl: col.imageUrl && !col.imageUrl.startsWith('data:') ? col.imageUrl : ''
      })) || [],
      rows: form.value.rows?.map(row => ({
        // Ao editar, só incluir ID se não for temporário
        ...(isEditing.value && row.id && !row.id.includes('_') ? { id: row.id } : {}),
        sizeLabel: row.sizeLabel,
        position: row.position,
        values: row.values?.map(val => ({
          // Ao editar, só incluir ID se não for temporário
          ...(isEditing.value && val.id && !val.id.includes('_') ? { id: val.id } : {}),
          value: val.value
        })) || []
      })) || [],
      imageUrl: form.value.imageUrl && !form.value.imageUrl.startsWith('data:') ? form.value.imageUrl : ''
    }

    let savedTableId

    if (isEditing.value) {
      // update
      await measurementTableApi.update(tableId.value, dataToSave)
      savedTableId = tableId.value
      toast.showSuccess(t('measurementTable.updatedSuccess'))
    } else {
      // create
      const response = await measurementTableApi.create(dataToSave)
      savedTableId = response.data.id
      toast.showSuccess(t('measurementTable.createdSuccess'))
    }

    // Upload pending images if any
    try {
      if (form.value.newImageFile) {
        await measurementTableApi.uploadImage(savedTableId, form.value.newImageFile)
      }

      // Upload column images if any
      if (form.value.pendingColumnImagesByColumnId) {
        const indexedImages = {}
        Object.entries(form.value.pendingColumnImagesByColumnId).forEach(([columnId, data]) => {
          // Só fazer upload se o ID da coluna não for temporário
          if (!columnId.includes('_')) {
            const currentIndex = form.value.columns.findIndex(col => col.id === columnId)
            if (currentIndex >= 0) {
              indexedImages[currentIndex] = data.file
            }
          }
        })
        
        if (Object.keys(indexedImages).length > 0) {
          await measurementTableApi.batchUploadColumnImages(savedTableId, indexedImages)
        }
      }
    } catch (uploadError) {
      console.error('Error uploading images:', uploadError)
      toast.showError(t('measurementTable.imageUploadError'))
    }

    router.push('/measurement-tables')
  } catch (error) {
    console.error('Error saving measurement table:', error)
    const errorMessage = error.response?.data?.message || 
      (isEditing.value ? t('measurementTable.errorUpdating') : t('measurementTable.errorCreating'))
    toast.showError(errorMessage)
  } finally {
    saving.value = false
  }
}

const handleCancel = () => {
  router.push('/measurement-tables')
}

onMounted(() => {
  loadTable()
})
</script>

<style scoped>
.page-container {
  padding: 24px;
}

/* Page Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  gap: 24px;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content {
  flex: 1;
}

.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.2;
}

.page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.4;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    margin-bottom: 24px;
    padding-bottom: 16px;
  }
  
  .page-title {
    font-size: 24px;
  }
  
  .page-subtitle {
    font-size: 15px;
    margin: 6px 0 0 0;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
}

@media (max-width: 640px) {
  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }
}

@media (max-width: 480px) {
  .page-container {
    padding: 12px;
  }
  
  .page-title {
    font-size: 22px;
  }
  
  .page-subtitle {
    font-size: 14px;
    margin: 8px 0 0 0;
  }
}
</style>

 