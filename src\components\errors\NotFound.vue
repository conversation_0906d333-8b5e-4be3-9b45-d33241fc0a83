<template>
  <div class="not-found-container">
    <div class="error-content">
      <!-- Ícone principal -->
      <div class="error-icon">
        <HugeiconsIcon :icon="Alert01Icon" size="80" class="error-icon-color" />
      </div>
      
      <!-- <PERSON><PERSON><PERSON> de erro -->
      <div class="error-code">
        <h1 class="error-code-text">404</h1>
      </div>
      
      <!-- Título -->
      <div class="error-title">
        <h2 class="error-title-text">
          {{ t('errors.notFound.title') }}
        </h2>
      </div>
      
      <!-- Descrição -->
      <div class="error-description">
        <p class="error-description-text">
          {{ customMessage || t('errors.notFound.description') }}
        </p>
      </div>
      
      <!-- Informações adicionais (se fornecidas) -->
      <div v-if="showRequestedPath && requestedPath" class="path-info">
        <div class="path-box">
          <h3 class="path-title">
            Caminho solicitado:
          </h3>
          <p class="path-text">
            <HugeiconsIcon :icon="LinkIcon" size="14" class="path-icon" />
            {{ requestedPath }}
          </p>
        </div>
      </div>
      
      <!-- Ações -->
      <div class="error-actions">
        <IluriaButton 
          @click="goBack" 
          :hugeIcon="ArrowLeft02Icon"
          color="primary"
        >
          {{ t('errors.notFound.goBack') }}
        </IluriaButton>
        
        <IluriaButton 
          @click="goHome"
          :hugeIcon="Home01Icon"
          variant="outline"
          color="primary"
        >
          {{ t('errors.notFound.goHome') }}
        </IluriaButton>
      </div>
      
      <!-- Sugestões de navegação (opcional) -->
      <div v-if="showSuggestions" class="suggestions-info">
        <p class="suggestions-text">
          Talvez você esteja procurando por uma dessas páginas:
        </p>
        <div class="suggestions-links">
          <IluriaButton 
            @click="goToProducts"
            :hugeIcon="ShoppingBasket01Icon"
            variant="outline"
            size="small"
          >
            Produtos
          </IluriaButton>
          <IluriaButton 
            @click="goToCustomers"
            :hugeIcon="UserMultipleIcon"
            variant="outline"
            size="small"
          >
            Clientes
          </IluriaButton>
          <IluriaButton 
            @click="goToOrders"
            :hugeIcon="ShoppingCart01Icon"
            variant="outline"
            size="small"
          >
            Pedidos
          </IluriaButton>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import { 
  ArrowLeft02Icon, 
  Home01Icon,
  LinkIcon,
  ShoppingBasket01Icon,
  UserMultipleIcon,
  ShoppingCart01Icon
} from '@hugeicons-pro/core-stroke-standard';

import { 
  Alert01Icon
} from '@hugeicons-pro/core-solid-rounded';
import IluriaButton from '@/components/iluria/IluriaButton.vue';

const props = defineProps({
  /**
   * Mensagem customizada para substituir a padrão  
   */
  customMessage: {
    type: String,
    default: null
  },
  
  /**
   * Se deve mostrar o caminho solicitado que não foi encontrado
   */
  showRequestedPath: {
    type: Boolean,
    default: true
  },
  
  /**
   * Se deve mostrar sugestões de navegação
   */
  showSuggestions: {
    type: Boolean,
    default: false
  }
});

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

// Get requested path from current route
const requestedPath = computed(() => {
  return route.path;
});

/**
 * Volta para a página anterior
 */
const goBack = () => {
  if (window.history.length > 1) {
    router.go(-1);
  } else {
    goHome();
  }
};

/**
 * Vai para a página inicial
 */
const goHome = () => {
  router.push('/');
};

/**
 * Navegação para páginas comuns
 */
const goToProducts = () => {
  router.push('/products');
};

const goToCustomers = () => {
  router.push('/customers');
};

const goToOrders = () => {
  router.push('/orders');
};
</script>

<style scoped>
.not-found-container {
  min-height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  background: linear-gradient(135deg, var(--iluria-color-body-bg) 0%, var(--iluria-color-container-bg) 100%);
  padding: 3rem 1rem;
  font-family: var(--iluria-font-family);
}

.error-content {
  text-align: center;
  max-width: 32rem;
  margin: 0 auto;
  background: var(--iluria-color-container-bg);
  border-radius: 1rem;
  padding: 3rem 2rem;
  box-shadow: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
  border: 1px solid var(--iluria-color-border);
}

.error-icon {
  margin-bottom: 1.5rem;
  display: flex;
  justify-content: center;
  animation: fadeInUp 0.6s ease-out;
}

.error-icon-color {
  color: #f59e0b;
}

.error-code {
  margin-bottom: 1rem;
  animation: fadeInUp 0.6s ease-out 0.1s both;
}

.error-code-text {
  font-size: 4rem;
  font-weight: 800;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1;
}

.error-title {
  margin-bottom: 1rem;
  animation: fadeInUp 0.6s ease-out 0.2s both;
}

.error-title-text {
  font-size: 1.5rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.error-description {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out 0.3s both;
}

.error-description-text {
  color: var(--iluria-color-text-secondary);
  line-height: 1.6;
  max-width: 28rem;
  margin: 0 auto;
}

.path-info {
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out 0.4s both;
}

.path-box {
  max-width: 28rem;
  margin: 0 auto;
}

.path-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
}

.path-text {
  display: flex;
  align-items: center;
  justify-content: center;
  margin: 0;
  font-size: 0.875rem;
  color: var(--iluria-color-text-tertiary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
}

.path-icon {
  margin-right: 0.5rem;
  color: var(--iluria-color-text-tertiary);
  flex-shrink: 0;
}

.error-actions {
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  gap: 1rem;
  margin-bottom: 2rem;
  animation: fadeInUp 0.6s ease-out 0.5s both;
}

@media (min-width: 640px) {
  .error-actions {
    flex-direction: row;
    gap: 1rem;
  }
}

.suggestions-info {
  margin-top: 2rem;
  padding-top: 1.5rem;
  border-top: 1px solid var(--iluria-color-border);
  animation: fadeInUp 0.6s ease-out 0.6s both;
}

.suggestions-text {
  font-size: 0.875rem;
  color: var(--iluria-color-text-tertiary);
  margin: 0 0 1rem 0;
}

.suggestions-links {
  display: flex;
  flex-wrap: wrap;
  justify-content: center;
  gap: 0.5rem;
}

/* Responsividade aprimorada */
@media (max-width: 640px) {
  .error-content {
    padding: 2rem 1.5rem;
    margin: 1rem;
  }
  
  .error-code-text {
    font-size: 3rem;
  }
  
  .error-title-text {
    font-size: 1.25rem;
  }
  
  .suggestions-links {
    flex-direction: column;
    align-items: center;
  }
}

/* Animações melhoradas */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* Hover effects para interatividade */
.error-content:hover {
  transform: translateY(-2px);
  transition: transform 0.3s ease;
}


</style>