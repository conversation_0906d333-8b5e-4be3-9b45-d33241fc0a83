// Utilitário para gerar CSS e JavaScript de animações para HTML final
export class AnimationUtils {
  
  // Gera keyframes CSS baseado no tipo de animação
  static generateKeyframes(type, keyframeName, customKeyframes = '') {
    switch (type) {
      case 'fade':
        return `@keyframes ${keyframeName} {
          from { opacity: 0; }
          to { opacity: 1; }
        }`
      
      case 'slideInLeft':
        return `@keyframes ${keyframeName} {
          from { transform: translateX(-100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }`
      
      case 'slideInRight':
        return `@keyframes ${keyframeName} {
          from { transform: translateX(100%); opacity: 0; }
          to { transform: translateX(0); opacity: 1; }
        }`
      
      case 'slideInUp':
        return `@keyframes ${keyframeName} {
          from { transform: translateY(-100%); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }`
      
      case 'slideInDown':
        return `@keyframes ${keyframeName} {
          from { transform: translateY(100%); opacity: 0; }
          to { transform: translateY(0); opacity: 1; }
        }`
      
      case 'zoomIn':
        return `@keyframes ${keyframeName} {
          from { transform: scale(0); opacity: 0; }
          to { transform: scale(1); opacity: 1; }
        }`
      
      case 'zoomOut':
        return `@keyframes ${keyframeName} {
          from { transform: scale(1); opacity: 1; }
          to { transform: scale(0); opacity: 0; }
        }`
      
      case 'rotateIn':
        return `@keyframes ${keyframeName} {
          from { transform: rotate(-360deg); opacity: 0; }
          to { transform: rotate(0deg); opacity: 1; }
        }`
      
      case 'bounceIn':
        return `@keyframes ${keyframeName} {
          0% { transform: scale(0); opacity: 0; }
          50% { transform: scale(1.5); opacity: 1; }
          75% { transform: scale(0.75); }
          100% { transform: scale(1); }
        }`
      
      case 'flipInX':
        return `@keyframes ${keyframeName} {
          from { transform: rotateX(90deg); opacity: 0; }
          to { transform: rotateX(0deg); opacity: 1; }
        }`
      
      case 'flipInY':
        return `@keyframes ${keyframeName} {
          from { transform: rotateY(90deg); opacity: 0; }
          to { transform: rotateY(0deg); opacity: 1; }
        }`
      
      case 'pulse':
        return `@keyframes ${keyframeName} {
          0% { transform: scale(1); }
          50% { transform: scale(1.2); }
          100% { transform: scale(1); }
        }`
      
      case 'shake':
        return `@keyframes ${keyframeName} {
          0% { transform: translateX(0); }
          10% { transform: translateX(-10px); }
          20% { transform: translateX(10px); }
          30% { transform: translateX(-10px); }
          40% { transform: translateX(10px); }
          50% { transform: translateX(0); }
        }`
      
      case 'custom':
        return `@keyframes ${keyframeName} {
          ${customKeyframes}
        }`
      
      default:
        return ''
    }
  }
  
  // Gera CSS de animação baseado no gatilho
  static generateAnimationCSS(animationId, keyframeName, trigger, duration, timingFunction, delay, iterations) {
    const animationProps = `${duration}s ${timingFunction} ${delay}s`
    const iterationCount = trigger === 'continuous' ? iterations : '1'
    const fillMode = 'both'
    
    switch (trigger) {
      case 'onLoad':
        return `[data-animation-id="${animationId}"] {
          animation: ${keyframeName} ${animationProps} ${iterationCount} ${fillMode};
        }`
      
      case 'onHover':
        return `[data-animation-id="${animationId}"] {
          animation-name: ${keyframeName};
          animation-duration: ${duration}s;
          animation-timing-function: ${timingFunction};
          animation-delay: ${delay}s;
          animation-iteration-count: 1;
          animation-fill-mode: ${fillMode};
          animation-play-state: paused;
        }
        
        [data-animation-id="${animationId}"]:hover {
          animation-play-state: running;
        }`
      
      case 'onScroll':
        return `[data-animation-id="${animationId}"] {
          animation-name: ${keyframeName};
          animation-duration: ${duration}s;
          animation-timing-function: ${timingFunction};
          animation-delay: ${delay}s;
          animation-iteration-count: 1;
          animation-fill-mode: ${fillMode};
          animation-play-state: paused;
          opacity: 0;
        }
        
        [data-animation-id="${animationId}"].in-view {
          animation-play-state: running;
          opacity: 1;
        }`
      
      case 'onClick':
        return `[data-animation-id="${animationId}"] {
          animation-name: ${keyframeName};
          animation-duration: ${duration}s;
          animation-timing-function: ${timingFunction};
          animation-delay: ${delay}s;
          animation-iteration-count: 1;
          animation-fill-mode: ${fillMode};
          animation-play-state: paused;
        }
        
        [data-animation-id="${animationId}"].clicked {
          animation-play-state: running;
        }`
      
      case 'continuous':
        return `[data-animation-id="${animationId}"] {
          animation: ${keyframeName} ${animationProps} ${iterationCount} ${fillMode};
        }`
      
      default:
        return ''
    }
  }
  
  // Gera JavaScript de controle de animações
  static generateAnimationJS() {
    return `
      (function() {
        // Aguarda o DOM estar pronto
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initAnimations);
        } else {
          initAnimations();
        }
        
        function initAnimations() {
          // Intersection Observer para animações on scroll
          const observer = new IntersectionObserver((entries) => {
            entries.forEach(entry => {
              if (entry.isIntersecting) {
                entry.target.classList.add('in-view');
                // Remove observer após primeira ativação para elementos únicos
                if (entry.target.getAttribute('data-animation-trigger') === 'onScroll') {
                  observer.unobserve(entry.target);
                }
              }
            });
          }, {
            threshold: 0.1,
            rootMargin: '50px 0px'
          });
          
          // Observa todos os elementos com trigger onScroll
          document.querySelectorAll('[data-animation-trigger="onScroll"]').forEach(el => {
            observer.observe(el);
          });
          
          // Controle para animações onClick
          document.querySelectorAll('[data-animation-trigger="onClick"]').forEach(el => {
            el.style.cursor = 'pointer';
            el.addEventListener('click', function() {
              // Remove classe se existir para reiniciar animação
              this.classList.remove('clicked');
              // Force reflow
              this.offsetHeight;
              // Adiciona classe para ativar animação
              this.classList.add('clicked');
            });
          });
          
          // Reobserva quando novos elementos são adicionados via JavaScript
          const mutationObserver = new MutationObserver((mutations) => {
            mutations.forEach((mutation) => {
              mutation.addedNodes.forEach((node) => {
                if (node.nodeType === 1) {
                  // Verifica o próprio nó
                  if (node.getAttribute && node.getAttribute('data-animation-trigger') === 'onScroll') {
                    observer.observe(node);
                  }
                  
                  // Verifica nós filhos
                  if (node.querySelectorAll) {
                    const scrollElements = node.querySelectorAll('[data-animation-trigger="onScroll"]');
                    scrollElements.forEach(el => observer.observe(el));
                    
                    const clickElements = node.querySelectorAll('[data-animation-trigger="onClick"]');
                    clickElements.forEach(el => {
                      el.style.cursor = 'pointer';
                      el.addEventListener('click', function() {
                        this.classList.remove('clicked');
                        this.offsetHeight;
                        this.classList.add('clicked');
                      });
                    });
                  }
                }
              });
            });
          });
          
          // Observa mudanças no DOM
          mutationObserver.observe(document.body, {
            childList: true,
            subtree: true
          });
          
        }
      })();
    `
  }
  
  // Analisa HTML e extrai informações de animações
  static extractAnimationsFromHTML(htmlString) {
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlString, 'text/html')
    const animatedElements = doc.querySelectorAll('[data-animation-type][data-animation-trigger]')
    
    const animations = []
    
    animatedElements.forEach(element => {
      const type = element.getAttribute('data-animation-type')
      const trigger = element.getAttribute('data-animation-trigger')
      const id = element.getAttribute('data-animation-id')
      
      if (type && trigger && id) {
        // Extrai valores dos atributos do elemento ou usa padrões
        const duration = parseFloat(element.getAttribute('data-animation-duration')) || 0.6
        const timingFunction = element.getAttribute('data-animation-timing') || 'ease'
        const delay = parseFloat(element.getAttribute('data-animation-delay')) || 0
        const iterations = element.getAttribute('data-animation-iterations') || '1'
        const customKeyframes = element.getAttribute('data-animation-keyframes') || ''
        
        animations.push({
          id,
          type,
          trigger,
          duration,
          timingFunction,
          delay,
          iterations,
          customKeyframes,
          element
        })
      }
    })
    
    return animations
  }
  
  // Gera CSS completo para todas as animações encontradas
  static generateCompleteAnimationCSS(animations) {
    let css = '/* Estilos de Animação Gerados Automaticamente */\n'
    
    animations.forEach(animation => {
      const keyframeName = `${animation.id}-keyframes`
      
      // Gera keyframes
      const keyframes = this.generateKeyframes(animation.type, keyframeName, animation.customKeyframes)
      if (keyframes) {
        css += keyframes + '\n\n'
      }
      
      // Gera CSS de animação
      const animationCSS = this.generateAnimationCSS(
        animation.id,
        keyframeName,
        animation.trigger,
        animation.duration,
        animation.timingFunction,
        animation.delay,
        animation.iterations
      )
      if (animationCSS) {
        css += animationCSS + '\n\n'
      }
    })
    
    return css
  }
  
  // Injeta animações completas no HTML
  static injectAnimationsIntoHTML(htmlString) {
    try {
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlString, 'text/html')
      
      // Extrai todas as animações do HTML
      const animations = this.extractAnimationsFromHTML(htmlString)
      
      if (animations.length === 0) {
        return htmlString
      }
      
      // Gera CSS completo para todas as animações
      const completeCSS = this.generateCompleteAnimationCSS(animations)
      
      // Gera JavaScript completo para controle das animações
      const completeJS = this.generateAnimationJS()
      
      // Remove estilos de animação antigos
      const oldStyles = doc.querySelectorAll('style[id*="animation"]')
      oldStyles.forEach(style => style.remove())
      
      const oldScripts = doc.querySelectorAll('script[id*="animation"]')
      oldScripts.forEach(script => script.remove())
      
      // Adiciona o CSS das animações no head
      const style = doc.createElement('style')
      style.id = 'animation-styles'
      style.textContent = completeCSS
      doc.head.appendChild(style)
      
      // Adiciona o JavaScript de controle no final do body
      const script = doc.createElement('script')
      script.id = 'animation-controls'
      script.textContent = completeJS
      doc.body.appendChild(script)
      
      return doc.documentElement.outerHTML
    } catch (error) {
      console.error('Erro ao injetar animações no HTML:', error)
      return htmlString
    }
  }
  
  // Função utilitária para limpar animações antigas do HTML
  static cleanOldAnimations(htmlString) {
    const parser = new DOMParser()
    const doc = parser.parseFromString(htmlString, 'text/html')
    
    // Remove estilos de animação antigos
    const oldStyles = doc.querySelectorAll('#iluria-animations, [id^="animation-style-"]')
    oldStyles.forEach(style => style.remove())
    
    // Remove scripts de animação antigos
    const oldScripts = doc.querySelectorAll('#iluria-animation-controller, #scroll-trigger-script')
    oldScripts.forEach(script => script.remove())
    
    return doc.documentElement.outerHTML
  }
}

export default AnimationUtils 