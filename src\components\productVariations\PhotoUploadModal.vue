<template>
  <IluriaModal
    v-model="modalVisible"
    :title="t('productVariations.selectPhotos')"
    class="w-full max-w-5xl photo-upload-modal"
    :contentStyle="{ zIndex: 10001, maxHeight: '85vh', overflow: 'auto' }"
  >
    <!-- Modal Body -->
    <div class="modal-content">
            <!-- Upload Area -->
      <div 
        class="upload-area"
        @dragover.prevent
        @drop.prevent="handleDrop"
        @click="triggerFileInput"
      >
        <div class="upload-content">
          <HugeiconsIcon :icon="Image02Icon" class="upload-icon" />
          <p class="upload-button-text">{{ t('productVariations.selectFiles') }}</p>
        </div>
        <input 
          type="file" 
          ref="fileInput" 
          class="hidden" 
          accept="image/*" 
          multiple
          @change="handleFileSelect"
        >
        <p class="upload-text">{{ t('productVariations.dragAndDropPhotos') }}</p>
      </div>

      <!-- Select All Checkbox - positioned between upload area and images -->
      <div v-if="selectedFiles.length > 0" class="select-all-section">
        <IluriaCheckBox
          :modelValue="isAllSelected"
          @update:modelValue="toggleSelectAll"
          :label="isAllSelected ? t('productVariations.unselectAll') : t('productVariations.selectAll')"
          class="select-all-checkbox"
        />
      </div>

      <!-- Image Grid -->
      <draggable
        v-if="selectedFiles.length > 0"
        v-model="selectedFiles"
        class="image-grid"
        :animation="200"
        item-key="name"
        @start="dragStart"
        @end="dragEnd"
      >
        <template #item="{ element: file, index }">
          <div class="image-item">
            <div 
              class="image-container"
              :class="{
                'selected': selectedIndexes.includes(index),
                'not-selected': !selectedIndexes.includes(index)
              }"
              @click="handlePhotoClick(index)"
            >
              <img 
                :src="getPreviewUrl(file)" 
                class="w-full h-full object-cover"
                alt="Preview"
                @load="handleImageLoad(index)"
                @error="handleImageError(index)"
                :class="{ 'opacity-50': imageErrors[index] }"
                v-show="isFileObject(file) || imageLoaded[index]"
                :style="{ 'background-color': 'transparent' }"
              />
              <!-- Loading indicator - only for existing photos from backend -->
              <div v-if="!isFileObject(file) && !imageLoaded[index] && !imageErrors[index]" class="loading-indicator">
                <div class="loading-spinner"></div>
              </div>
              <!-- Error indicator -->
              <div v-if="imageErrors[index]" class="error-indicator">
                <HugeiconsIcon :icon="CloudUploadIcon" class="error-icon" />
                <span class="error-text">Erro</span>
              </div>
              <!-- Hover overlay -->
              <div class="hover-overlay"></div>
              
              <!-- Selection checkbox -->
              <IluriaCheckBox
                :modelValue="selectedIndexes.includes(index)"
                @update:modelValue="(val) => toggleSelection(index)"
                class="selection-checkbox"
                @click.stop
              />
              
              <!-- Position indicator -->
              <div class="position-indicator">
                {{ index + 1 }}
              </div>
            </div>
            <div class="file-name">
              {{ getFileName(file) }}
            </div>
            <div class="file-info">
              {{ getFileInfo(file) }}
            </div>
          </div>
        </template>
      </draggable>
    </div>

    <template #footer>
      <div class="flex justify-between">
        <div class="flex items-center gap-4">
          <IluriaButton
            v-if="selectedIndexes.length > 0"
            @click="deleteSelected"
            color="text-danger"
            :huge-icon="Delete03Icon"
          >
            {{ t('productVariations.deleteSelected') }}
          </IluriaButton>
        </div>
        <IluriaButton 
          @click="save" 
          :huge-icon="CheckmarkCircle02Icon"
        >
          {{ t('productVariations.save') }}
        </IluriaButton>
      </div>
    </template>
  </IluriaModal>
</template>

<script setup>
import { ref, onMounted, onUnmounted, watch, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { HugeiconsIcon } from '@hugeicons/vue';
import {
  CloudUploadIcon,
  CheckmarkSquare03Icon,
  Delete03Icon,
  CheckmarkCircle02Icon,
  RefreshIcon,
  Image02Icon
} from '@hugeicons-pro/core-bulk-rounded';
import draggable from 'vuedraggable';
import IluriaModal from '@/components/iluria/IluriaModal.vue';
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { productsApi } from '@/services/product.service';
import { useToast } from '@/services/toast.service';

const { t } = useI18n();
const toast = useToast();

const props = defineProps({
  isOpen: Boolean,
  initialFiles: Array,
  productId: {
    type: String,
    default: null
  },
  variationId: {
    type: String,
    default: null
  },
  isEditing: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['close', 'save']);

const modalVisible = computed({
  get: () => props.isOpen,
  set: (value) => {
    if (!value) {
      close();
    }
  }
});

const fileInput = ref(null);
const selectedFiles = ref([]);
const selectedIndexes = ref([]);
const isDragging = ref(false);
const imageLoaded = ref([]);
const imageErrors = ref([]);
const objectUrls = ref(new Map());

// Cache to store files per variation to prevent data loss
const variationFilesCache = ref(new Map());

// Gestão otimizada de Object URLs
const objectUrlsCleanupQueue = new Set();

const cleanupObjectUrls = (force = false) => {
  if (force) {
    // Limpeza forçada - limpar todas as URLs
    objectUrls.value.forEach(url => {
      URL.revokeObjectURL(url);
    });
    objectUrls.value.clear();
  } else {
    // Limpeza seletiva - apenas URLs não utilizadas
    const currentFiles = selectedFiles.value;
    const urlsToKeep = new Map();

    currentFiles.forEach(file => {
      if (file instanceof File && objectUrls.value.has(file)) {
        urlsToKeep.set(file, objectUrls.value.get(file));
      }
    });

    // Limpar URLs não utilizadas
    objectUrls.value.forEach((url, file) => {
      if (!urlsToKeep.has(file)) {
        URL.revokeObjectURL(url);
      }
    });

    objectUrls.value = urlsToKeep;
  }
};

// Track current variation to detect changes
const currentVariationId = ref(null);

watch(() => props.isOpen, (newValue) => {
  if (newValue) {
    // Reset selection indexes
    selectedIndexes.value = [];

    // Check if we're opening for a different variation
    const isNewVariation = currentVariationId.value !== props.variationId;

    if (isNewVariation || currentVariationId.value === null) {
      // Save current files to cache before switching
      if (currentVariationId.value !== null) {
        variationFilesCache.value.set(currentVariationId.value, [...selectedFiles.value]);
      }

      // Switch to new variation
      currentVariationId.value = props.variationId;

      // Check if we have cached files for this variation
      const cachedFiles = variationFilesCache.value.get(props.variationId);

      if (cachedFiles && cachedFiles.length > 0) {
        // Use cached files
        selectedFiles.value = [...cachedFiles];
      } else {
        // Load files from props for this variation
        selectedFiles.value = [];

        if (props.initialFiles && props.initialFiles.length > 0) {
          const existingPhotos = props.initialFiles.filter(photo => {
            // Filter only valid objects: File objects or objects with URL
            const isValidFile = photo instanceof File && photo.size > 0 && photo.name;
            const isValidExisting = photo && photo.url && Object.keys(photo).length > 1;
            const isValid = isValidFile || isValidExisting;

            return isValid;
          }).map(photo => {
            if (photo.url) {
              return {
                ...photo,
                isExisting: true
              };
            }
            return photo;
          });

          selectedFiles.value = [...existingPhotos];
          // Cache the initial files
          variationFilesCache.value.set(props.variationId, [...existingPhotos]);
        }
      }
    }

    // Ajustar arrays de controle para o tamanho atual
    const currentLength = selectedFiles.value.length;
    imageLoaded.value = Array(currentLength).fill(false);
    imageErrors.value = Array(currentLength).fill(false);

    // Marcar imagens existentes como carregadas
    selectedFiles.value.forEach((file, index) => {
      if (file.isExisting) {
        imageLoaded.value[index] = true;
      }
    });
  } else if (!newValue) {
    // Não limpar Object URLs ao fechar, apenas quando necessário
    // cleanupObjectUrls();
  }
}, { immediate: true });

// Watch for variation ID changes to ensure isolation
watch(() => props.variationId, (newVariationId, oldVariationId) => {
  // If variation ID changes while modal is open, load new variation's files
  if (props.isOpen && newVariationId !== oldVariationId && oldVariationId !== undefined) {
    currentVariationId.value = newVariationId;
    selectedIndexes.value = [];

    // Load files for the new variation
    if (props.initialFiles && props.initialFiles.length > 0) {
      const existingPhotos = props.initialFiles.filter(photo => {
        const isValidFile = photo instanceof File && photo.size > 0 && photo.name;
        const isValidExisting = photo && photo.url && Object.keys(photo).length > 1;
        return isValidFile || isValidExisting;
      }).map(photo => {
        if (photo.url) {
          return {
            ...photo,
            isExisting: true
          };
        }
        return photo;
      });

      selectedFiles.value = [...existingPhotos];
    } else {
      selectedFiles.value = [];
    }

    // Clean up old Object URLs
    cleanupObjectUrls(false);
  }
});

const handleEscKey = (event) => {
  if (event.key === 'Escape' && props.isOpen) {
    close();
  }
};

onMounted(() => {
  document.addEventListener('keydown', handleEscKey);
});

onUnmounted(() => {
  document.removeEventListener('keydown', handleEscKey);
  cleanupObjectUrls();
});

const dragStart = () => {
  isDragging.value = true;
};

const dragEnd = () => {
  setTimeout(() => {
    isDragging.value = false;
  }, 0);
};

const handlePhotoClick = (index) => {
  if (!isDragging.value) {
    toggleSelection(index);
  }
};

const triggerFileInput = () => {
  fileInput.value?.click();
};

const handleFileSelect = (event) => {
  const input = event.target;
  if (input.files) {
    addFiles(Array.from(input.files));
  }
};

const handleDrop = (event) => {
  const files = event.dataTransfer?.files;
  if (files) {
    addFiles(Array.from(files));
  }
};

const addFiles = async (files) => {
  const imageFiles = files.filter(file => {
    return file.type.startsWith('image/') &&
           ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'].includes(file.type);
  });

  // Filtrar arquivos duplicados baseado no nome e tamanho
  const existingFileSignatures = selectedFiles.value.map(file => {
    if (file instanceof File) {
      return `${file.name}-${file.size}`;
    }
    return `${file.name || file.url}-${file.size || 0}`;
  });

  const newFiles = imageFiles.filter(file => {
    const signature = `${file.name}-${file.size}`;
    return !existingFileSignatures.includes(signature);
  });

  if (newFiles.length === 0) {
    toast.showWarning(t('product.duplicateFilesSkipped'));
    return;
  }

  // Verificar se a variação é existente (ID real do backend) ou nova (ID temporário)
  const isExistingVariation = props.variationId &&
    typeof props.variationId === 'string' &&
    props.variationId.match(/^[0-9A-Z]{26}$/); // Formato ULID do backend

  if (props.isEditing && props.productId && props.variationId && isExistingVariation && newFiles.length > 0) {
    try {
      // Desabilitar cleanup durante o upload
      const shouldPreventCleanup = true;

      // Upload com retry logic
      let uploadAttempts = 0;
      const maxAttempts = 3;
      let uploadSuccess = false;

      while (uploadAttempts < maxAttempts && !uploadSuccess) {
        try {
          uploadAttempts++;
          await productsApi.uploadVariationImages(props.productId, props.variationId, newFiles);
          uploadSuccess = true;
        } catch (uploadError) {
          console.warn(`Tentativa ${uploadAttempts} de upload falhou:`, uploadError);
        if (uploadAttempts === maxAttempts) {
            throw uploadError;
          }
          // Aguardar antes de tentar novamente
          await new Promise(resolve => setTimeout(resolve, 1000 * uploadAttempts));
        }
      }

      // Aguardar um pouco para garantir que o backend processou
      await new Promise(resolve => setTimeout(resolve, 500));

      const updatedProduct = await productsApi.getById(props.productId);
      const updatedVariation = updatedProduct.variations?.find(v => v.id === props.variationId);

      if (updatedVariation && updatedVariation.photos) {
        const sortedPhotos = updatedVariation.photos.sort((a, b) => (a.position || 0) - (b.position || 0));

        // Preservar Object URLs existentes antes de atualizar
        const existingUrls = new Map();
        selectedFiles.value.forEach(file => {
          if (file instanceof File && objectUrls.value.has(file)) {
            existingUrls.set(file.name, objectUrls.value.get(file));
          }
        });

        selectedFiles.value = sortedPhotos.map(photo => ({
          ...photo,
          isExisting: true
        }));

        imageLoaded.value = Array(selectedFiles.value.length).fill(true);
        imageErrors.value = Array(selectedFiles.value.length).fill(false);

        // Limpar URLs não utilizadas
        existingUrls.forEach((url, fileName) => {
          if (!selectedFiles.value.some(file => file.name === fileName)) {
            URL.revokeObjectURL(url);
            objectUrls.value.delete(fileName);
          }
        });
    }

    toast.showSuccess(t('product.imagesUploadedSuccessfully'));

  } catch (error) {
    console.error('Erro no upload das imagens:', error);
    toast.showError(t('product.errorUploadingImages'));

    // Em caso de erro, ainda adicionar as imagens localmente
    const startIndex = selectedFiles.value.length;
    selectedFiles.value.push(...newFiles);

    for (let i = 0; i < newFiles.length; i++) {
      const index = startIndex + i;
      imageLoaded.value[index] = true;
      imageErrors.value[index] = false;
    }
  }
} else {
  // Para variações novas ou modo criação, apenas adicionar aos arquivos selecionados
  const startIndex = selectedFiles.value.length;

  // Adicionar arquivos com posição correta
  const filesWithPosition = newFiles.map((file, index) => {
    // Adicionar propriedade position ao File object
    Object.defineProperty(file, 'position', {
      value: startIndex + index,
      writable: true,
      enumerable: false,
      configurable: true
    });
    return file;
  });

  selectedFiles.value.push(...filesWithPosition);
  selectedIndexes.value = [];

  // Expandir arrays de controle
  const newLength = selectedFiles.value.length;
  imageLoaded.value = Array(newLength).fill(false);
  imageErrors.value = Array(newLength).fill(false);

  // Marcar novas imagens como carregadas
  for (let i = startIndex; i < newLength; i++) {
    imageLoaded.value[i] = true;
    imageErrors.value[i] = false;
  }

  if (!isExistingVariation && props.isEditing) {
    toast.showInfo(t('product.imagesWillBeUploadedOnSave'));
  } else {
    toast.showSuccess(t('product.imagesAddedSuccessfully', { count: newFiles.length }));
  }
}
};

const getPreviewUrl = (file) => {
  try {
    if (file.url && file.isExisting) {
      return file.url;
    }
    if (file instanceof File && file.type && file.type.startsWith('image/')) {
      if (objectUrls.value.has(file)) {
        return objectUrls.value.get(file);
      }
      const url = URL.createObjectURL(file);
      objectUrls.value.set(file, url);

      // Não agendar limpeza automática - apenas limpar quando necessário
      // A limpeza será feita apenas quando o modal for fechado definitivamente
      // ou quando o arquivo for removido

      return url;
    }
    return '';
  } catch (error) {
    console.warn('Erro ao gerar preview URL:', error);
    return '';
  }
};

const getFileExtension = (filename) => {
  return filename.split('.').pop() || '';
};

const toggleSelection = (index) => {
  const currentIndex = selectedIndexes.value.indexOf(index);
  if (currentIndex === -1) {
    selectedIndexes.value.push(index);
  } else {
    selectedIndexes.value.splice(currentIndex, 1);
  }
};

const selectAll = () => {
  selectedIndexes.value = Array.from({ length: selectedFiles.value.length }, (_, i) => i);
};

const unselectAll = () => {
  selectedIndexes.value = [];
};

// Computed property para verificar se todos os itens estão selecionados
const isAllSelected = computed(() => {
  return selectedFiles.value.length > 0 && selectedIndexes.value.length === selectedFiles.value.length;
});

// Função para alternar entre selecionar tudo e desselecionar tudo
const toggleSelectAll = (value) => {
  if (value) {
    selectAll();
  } else {
    unselectAll();
  }
};

const deleteSelected = async () => {
  if (selectedIndexes.value.length === 0) {
    return;
  }

  // Verificar se a variação é existente (ID real do backend)
  const isExistingVariation = props.variationId &&
    typeof props.variationId === 'string' &&
    props.variationId.match(/^[0-9A-Z]{26}$/);

  // Ordenar índices em ordem decrescente para evitar problemas de índice ao remover
  const sortedIndexes = [...selectedIndexes.value].sort((a, b) => b - a);

  // Processar exclusões uma por uma para manter consistência
  for (const index of sortedIndexes) {
    const file = selectedFiles.value[index];

    // Se for uma imagem existente no backend, excluir via API
    if (file.isExisting && file.url && props.isEditing && props.productId && props.variationId && isExistingVariation) {
      try {
        await productsApi.deleteVariationImage(props.productId, props.variationId, file.url);
      } catch (error) {
        console.error('Erro ao excluir imagem do backend:', error);
        toast.showError(t('product.errorDeletingImage'));
        continue; // Pular esta imagem se houver erro
      }
    }

    // Limpar Object URLs
    if (objectUrls.value.has(file)) {
      URL.revokeObjectURL(objectUrls.value.get(file));
      objectUrls.value.delete(file);
    }

    // Remover da lista
    selectedFiles.value.splice(index, 1);
    imageLoaded.value.splice(index, 1);
    imageErrors.value.splice(index, 1);

    // Ajustar índices selecionados
    selectedIndexes.value = selectedIndexes.value
      .filter(i => i !== index)
      .map(i => i > index ? i - 1 : i);
  }

  // Limpar seleções restantes
  selectedIndexes.value = [];
};

const close = () => {
  // Only clear temporary selections, keep files for the current variation
  selectedIndexes.value = [];

  // Clear file input
  if (fileInput.value) {
    fileInput.value.value = '';
  }

  // Clean up unused Object URLs but keep the ones for current files
  cleanupObjectUrls(false);

  emit('close');
};

const save = () => {
  const photosToSave = selectedFiles.value.filter(item => {
    // Só incluir itens válidos: File objects ou objetos existentes com URL
    const isValidFile = item instanceof File && item.size > 0 && item.name;
    const isValidExisting = item.isExisting && item.url;
    const isValid = isValidFile || isValidExisting;

    return isValid;
  }).map((item, index) => {
    if (item.isExisting) {
      return {
        ...item,
        position: index
      };
    }
    // Para File objects, criar um objeto com as propriedades necessárias
    if (item instanceof File) {
      return {
        file: item,
        name: item.name,
        size: item.size,
        type: item.type,
        position: index,
        isExisting: false
      };
    }
    return item;
  });


  // Update cache with current files
  if (currentVariationId.value) {
    variationFilesCache.value.set(currentVariationId.value, [...selectedFiles.value]);
  }

  emit('save', photosToSave);
};

const handleImageLoad = (index) => {
  imageLoaded.value[index] = true;
};

const handleImageError = (index) => {
  imageErrors.value[index] = true;
};

const isFileObject = (file) => {
  return file instanceof File;
};

const formatFileSize = (size) => {
  if (size < 1024) {
    return size + ' B';
  } else if (size < 1024 * 1024) {
    return (size / 1024).toFixed(2) + ' KB';
  } else {
    return (size / (1024 * 1024)).toFixed(2) + ' MB';
  }
};

const getFileName = (file) => {
  if (file.isExisting) {
    return file.name || `Photo ${file.position + 1}`;
  } else if (file instanceof File) {
    return file.name;
  }
  return '';
};

const getFileInfo = (file) => {
  if (file.isExisting) {
    const extension = file.name ? getFileExtension(file.name).toUpperCase() : 'IMG';
    const size = file.size ? formatFileSize(file.size) : 'Unknown size';
    return `${extension} • ${size}`;
  } else if (file instanceof File) {
    return getFileExtension(file.name).toUpperCase() + ' • ' + formatFileSize(file.size);
  }
  return '';
};

const removePhoto = async (index) => {
  const file = selectedFiles.value[index];
  
  // Verificar se a variação é existente (ID real do backend)
  const isExistingVariation = props.variationId && 
    typeof props.variationId === 'string' && 
    props.variationId.match(/^[0-9A-Z]{26}$/);
  
  if (file.isExisting && file.url && props.isEditing && props.productId && props.variationId && isExistingVariation) {
    try {
      await productsApi.deleteVariationImage(props.productId, props.variationId, file.url);
    } catch (error) {
      toast.showError(t('product.errorDeletingImage'));
      return;
    }
  }
  
  if (objectUrls.value.has(file)) {
    URL.revokeObjectURL(objectUrls.value.get(file));
    objectUrls.value.delete(file);
  }
  
  selectedFiles.value.splice(index, 1);
  imageLoaded.value.splice(index, 1);
  imageErrors.value.splice(index, 1);
  
  selectedIndexes.value = selectedIndexes.value
    .filter(i => i !== index)
    .map(i => i > index ? i - 1 : i);
};

// Limpeza periódica de Object URLs não utilizadas
const cleanupUnusedUrls = () => {
  objectUrlsCleanupQueue.forEach(file => {
    // Mais conservador: só limpar se o arquivo realmente não está sendo usado
    const isFileInUse = selectedFiles.value.some(selectedFile => {
      return selectedFile === file || 
             (selectedFile instanceof File && selectedFile.name === file.name);
    });
    
    if (objectUrls.value.has(file) && !isFileInUse) {
      // Aguardar mais tempo antes de limpar durante uploads ativos
      const isUploadActive = selectedFiles.value.some(f => f instanceof File);
      const cleanupDelay = isUploadActive ? 60000 : 30000; // 1 min durante upload, 30s normal
      
      setTimeout(() => {
        if (objectUrls.value.has(file) && !selectedFiles.value.includes(file)) {
          URL.revokeObjectURL(objectUrls.value.get(file));
          objectUrls.value.delete(file);
        }
      }, cleanupDelay);
      
      objectUrlsCleanupQueue.delete(file);
    }
  });
};

// Executar limpeza a cada 30 segundos (menos agressivo)
const cleanupInterval = setInterval(cleanupUnusedUrls, 30000);

onUnmounted(() => {
  clearInterval(cleanupInterval);
  cleanupObjectUrls();
});
</script>

<style scoped>
/* Modal Content */
.modal-content {
  padding: 24px;
  background: var(--iluria-color-background);
}

/* Upload Area */
.upload-area {
  border: 3px dashed var(--iluria-color-border);
  border-radius: 16px;
  padding: 40px 32px;
  text-align: center;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  background: linear-gradient(135deg, var(--iluria-color-surface) 0%, var(--iluria-color-surface-hover) 100%);
  cursor: pointer;
  user-select: none;
  position: relative;
  overflow: hidden;
}

.upload-area::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
  transform: translateX(-100%);
  transition: transform 0.6s ease;
}

.upload-area:hover::before {
  transform: translateX(100%);
}

.upload-area:hover {
  border-color: var(--iluria-color-primary);
  background: linear-gradient(135deg, var(--iluria-color-surface-hover) 0%, var(--iluria-color-primary-light) 100%);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.upload-content {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: var(--iluria-color-text-primary);
  justify-content: center;
  margin-bottom: 16px;
}

.upload-icon {
  font-size: 56px;
  color: var(--iluria-color-primary);
  margin-bottom: 16px;
  transition: all 0.3s ease;
  filter: drop-shadow(0 2px 4px rgba(0, 0, 0, 0.1));
}

.upload-area:hover .upload-icon {
  color: var(--iluria-color-primary-hover);
  transform: scale(1.1);
}

.upload-button-text {
  margin: 0;
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-primary);
  transition: all 0.3s ease;
  text-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

.upload-area:hover .upload-button-text {
  color: var(--iluria-color-primary-hover);
  transform: translateY(-1px);
}

.upload-text {
  margin: 0;
  font-size: 15px;
  color: var(--iluria-color-text-secondary);
  font-weight: 400;
  opacity: 0.8;
  transition: opacity 0.3s ease;
}

.upload-area:hover .upload-text {
  opacity: 1;
}

/* Select All Section */
.select-all-section {
  margin-top: 20px;
  margin-bottom: 8px;
  padding: 12px 16px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}

.select-all-section:hover {
  background: var(--iluria-color-surface-hover);
  border-color: var(--iluria-color-primary-light);
}

.select-all-checkbox {
  font-weight: 500;
}

/* Image Grid */
.image-grid {
  margin-top: 24px;
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(140px, 1fr));
  gap: 16px;
  max-height: 50vh;
  overflow-y: auto;
  padding: 4px;
}

@media (min-width: 640px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(160px, 1fr));
    gap: 18px;
  }
}

@media (min-width: 768px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(180px, 1fr));
    gap: 20px;
  }
}

@media (min-width: 1024px) {
  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 24px;
  }
}

/* Image Item */
.image-item {
  position: relative;
}

.image-container {
  aspect-ratio: 1;
  background: var(--iluria-color-surface);
  border-radius: 12px;
  overflow: hidden;
  cursor: pointer;
  border: 3px solid transparent;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  min-height: 140px;
  max-height: 200px;
  position: relative;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
}

.image-container.selected {
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 4px var(--iluria-color-focus-ring), 0 4px 16px rgba(0, 0, 0, 0.15);
  transform: scale(1.02);
}

.image-container.not-selected:hover {
  border-color: var(--iluria-color-primary-light);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
  transform: translateY(-2px);
}

.image-container img {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  object-fit: cover;
  object-position: center;
  z-index: 1;
  display: block;
  transition: transform 0.3s ease;
}

.image-container:hover img {
  transform: scale(1.05);
}

/* Loading Indicator */
.loading-indicator {
  position: absolute;
  inset: 0;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-surface);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 2px solid var(--iluria-color-border);
  border-bottom-color: var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  to {
    transform: rotate(360deg);
  }
}

/* Error Indicator */
.error-indicator {
  position: absolute;
  inset: 0;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-error-bg);
}

.error-icon {
  font-size: 32px;
  color: var(--iluria-color-error);
}

.error-text {
  font-size: 12px;
  color: var(--iluria-color-error);
  margin-top: 4px;
}

/* Selection Overlay */
.selection-overlay {
  position: absolute;
  top: 8px;
  right: 8px;
  z-index: 10;
}

.selection-badge {
  background: var(--iluria-color-primary);
  border-radius: 50%;
  padding: 4px;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
}

.selection-icon {
  font-size: 14px;
  color: var(--iluria-color-primary-contrast);
}

/* Hover Overlay */
.hover-overlay {
  position: absolute;
  inset: 0;
  background: var(--iluria-color-overlay);
  opacity: 0;
  transition: opacity 0.2s ease;
}

.image-item:hover .hover-overlay {
  opacity: 0.1;
}

/* Selection Checkbox */
.selection-checkbox {
  position: absolute;
  top: 8px;
  left: 8px;
  z-index: 10;
}

/* Delete Button */
.delete-button-container {
  position: absolute;
  bottom: 8px;
  right: 8px;
  z-index: 10;
  opacity: 1;
  transition: opacity 0.2s ease;
}

.delete-button {
  padding: 4px !important;
  min-width: 0 !important;
  width: 24px !important;
  height: 24px !important;
  border-radius: 50% !important;
  background: var(--iluria-color-error) !important;
  color: var(--iluria-color-error-contrast) !important;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2) !important;
}

.delete-button:hover {
  background: var(--iluria-color-error-hover) !important;
}

.delete-icon {
  width: 12px;
  height: 12px;
}

/* Position Indicator */
.position-indicator {
  position: absolute;
  bottom: 4px;
  left: 4px;
  background: rgba(0, 0, 0, 0.7);
  color: white;
  font-size: 12px;
  padding: 2px 8px;
  border-radius: 4px;
}

/* File Info */
.file-name {
  margin-top: 4px;
  text-align: center;
  font-size: 14px;
  color: var(--iluria-color-text);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
}

.file-info {
  text-align: center;
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

/* Custom Scrollbar */
.image-grid::-webkit-scrollbar {
  width: 8px;
}

.image-grid::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
  border-radius: 4px;
}

.image-grid::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 4px;
  transition: background 0.3s ease;
}

.image-grid::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-primary-light);
}

/* Enhanced Responsive Design */
@media (max-width: 480px) {
  .modal-content {
    padding: 16px;
  }

  .upload-area {
    padding: 24px 16px;
  }

  .upload-icon {
    font-size: 40px;
  }

  .upload-button-text {
    font-size: 16px;
  }

  .image-grid {
    grid-template-columns: repeat(auto-fill, minmax(120px, 1fr));
    gap: 12px;
  }
}

/* Modal Z-index */
.photo-upload-modal :deep(.p-dialog) {
  z-index: 10001 !important;
}

.photo-upload-modal :deep(.p-dialog-mask) {
  z-index: 10000 !important;
  background-color: rgba(0, 0, 0, 0.8) !important;
}

/* Modal Content */
.photo-upload-modal :deep(.p-dialog-content) {
  background: var(--iluria-color-background) !important;
  color: var(--iluria-color-text) !important;
}

.photo-upload-modal :deep(.p-dialog-header) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text) !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
}

.photo-upload-modal :deep(.p-dialog-footer) {
  background: var(--iluria-color-surface) !important;
  border-top: 1px solid var(--iluria-color-border) !important;
}

/* Responsive */
@media (max-width: 640px) {
  .image-grid {
    grid-template-columns: repeat(2, 1fr);
    gap: 12px;
  }
  
  .upload-area {
    padding: 24px;
  }
  
  .upload-icon {
    font-size: 40px;
    margin-bottom: 12px;
  }
}
</style>
