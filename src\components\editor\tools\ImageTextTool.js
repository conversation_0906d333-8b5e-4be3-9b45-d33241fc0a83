/**
 * ImageText Tool for Editor.js
 * Creates an image and text layout with customizable positioning and styling.
 */
export default class ImageTextTool {
  /**
   * @param {object} tool - tool properties got from editor.js
   * @param {object} tool.data - previously saved data
   * @param {object} tool.config - user config for Tool
   * @param {object} tool.api - Editor.js API
   */
  constructor({ data, api }) {
    this.api = api;
    this.data = data || {};

    // Default values
    this.data.imagePosition = this.data.imagePosition || 'left';
    this.data.title = this.data.title || 'Premium Footbeds';
    this.data.text = this.data.text || 'We build our footwear with premium materials and construction techniques that deliver comfort and durability. Our footbeds are designed to provide all-day support and cushioning.';
    this.data.imageUrl = this.data.imageUrl || 'https://www.lojadelayouts.com/wp-content/uploads/2024/04/Iluria.jpg';
    this.data.textColor = this.data.textColor || '#ffffff';
    this.data.backgroundColor = this.data.backgroundColor || '#000000';

    // Inicializar array de botões (até 3 botões)
    if (!this.data.buttons || this.data.buttons.length === 0) {
      this.data.buttons = [
        {
          enabled: true,
          text: 'Shop Now',
          url: '#'
        },
        {
          enabled: false,
          text: 'Botão 2',
          url: '#'
        },
        {
          enabled: false,
          text: 'Botão 3',
          url: '#'
        }
      ];
    }

    // Manter compatibilidade com versão anterior
    if (this.data.hasButton !== undefined && this.data.buttonText && this.data.buttonUrl) {
      this.data.buttons[0] = {
        enabled: this.data.hasButton,
        text: this.data.buttonText,
        url: this.data.buttonUrl
      };
      // Limpar propriedades antigas
      delete this.data.hasButton;
      delete this.data.buttonText;
      delete this.data.buttonUrl;
    }

    this.wrapper = undefined;
  }

  /**
   * Return Tool's view
   */
  render() {
    this.wrapper = document.createElement('div');
    this.wrapper.classList.add('image-text-tool');
    this.wrapper.innerHTML = this.getToolHTML();

    this.addEventListeners();
    return this.wrapper;
  }

  /**
   * Generate HTML for the tool
   */
  getToolHTML() {
    const isImageLeft = this.data.imagePosition === 'left';

    return `
      <div class="image-text-container" style="
        background-color: ${this.data.backgroundColor};
        color: ${this.data.textColor};
        border-radius: 12px;
        display: flex;
        align-items: stretch;
        min-height: 400px;
        overflow: hidden;
        flex-direction: ${isImageLeft ? 'row' : 'row-reverse'};
      ">
        <div class="image-section" style="
          flex: 1;
          position: relative;
          min-height: 400px;
        ">
          <div class="image-upload-area" style="
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            border: ${!this.data.imageUrl ? '2px dashed rgba(255,255,255,0.3)' : 'none'};
            border-radius: ${!this.data.imageUrl ? '8px' : '0'};
            margin: ${!this.data.imageUrl ? '20px' : '0'};
            text-align: center;
            transition: all 0.3s ease;
            background-image: ${this.data.imageUrl ? `url(${this.data.imageUrl})` : 'none'};
            background-size: cover;
            background-position: center;
            background-repeat: no-repeat;
            display: flex;
            align-items: center;
            justify-content: center;
          ">
            ${!this.data.imageUrl ? `
              <div style="color: rgba(255,255,255,0.7);">
                <div style="font-size: 24px; margin-bottom: 8px;">📷</div>
                <div>Use o menu "Imagem" para carregar</div>
              </div>
            ` : ''}
          </div>
        </div>

        <div class="text-section" style="
          flex: 1;
          padding: 40px;
          display: flex;
          flex-direction: column;
          justify-content: center;
        ">
          <h2 style="
            font-size: 28px;
            font-weight: bold;
            margin: 0 0 16px 0;
            color: ${this.data.textColor};
          ">${this.data.title}</h2>

          <p style="
            font-size: 16px;
            line-height: 1.6;
            margin: 0 0 24px 0;
            color: ${this.data.textColor};
          ">${this.data.text}</p>

          <div class="buttons-container" style="
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
          ">
            ${this.data.buttons.filter(btn => btn.enabled === true).map(button => `
              <button style="
                background: transparent;
                color: ${this.data.textColor};
                border: 1px solid ${this.data.textColor};
                padding: 12px 24px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 500;
                transition: all 0.3s ease;
                text-decoration: none;
                display: inline-block;
                white-space: nowrap;
              ">${button.text}</button>
            `).join('')}
          </div>
        </div>
      </div>
    `;
  }

  /**
   * Renders the settings panel using Editor.js native menu system
   * @returns {Array} Menu configuration with submenus
   */
  renderSettings() {
    return [
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <path d="M9 3v18"/>
        </svg>`,
        title: 'Layout',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <path d="M9 3v18"/>
              </svg>`,
              title: 'Posição da Imagem',
              type: 'html',
              element: this._createPositionSelector()
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
          <circle cx="9" cy="9" r="2"/>
          <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
        </svg>`,
        title: 'Imagem',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
                <circle cx="9" cy="9" r="2"/>
                <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
              </svg>`,
              title: 'Carregar Imagem',
              type: 'html',
              element: this._createImageUploader()
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="3"/>
          <path d="M12 1v6m0 6v6m11-7h-6m-6 0H1"/>
        </svg>`,
        title: 'Cores',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 6h16M4 12h16M4 18h16"/>
              </svg>`,
              title: 'Cor do Texto',
              type: 'html',
              element: this._createColorPicker('textColor', this.data.textColor, 'Cor do Texto')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              </svg>`,
              title: 'Cor de Fundo',
              type: 'html',
              element: this._createColorPicker('backgroundColor', this.data.backgroundColor, 'Cor de Fundo')
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
        </svg>`,
        title: 'Texto',
        children: {
          items: [
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
              </svg>`,
              title: 'Título',
              type: 'html',
              element: this._createTextInput('title', this.data.title, 'Título')
            },
            {
              icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
                <polyline points="14,2 14,8 20,8"/>
                <line x1="16" y1="13" x2="8" y2="13"/>
                <line x1="16" y1="17" x2="8" y2="17"/>
              </svg>`,
              title: 'Descrição',
              type: 'html',
              element: this._createTextArea('text', this.data.text, 'Descrição')
            }
          ]
        }
      },
      {
        icon: `<svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
          <circle cx="12" cy="5" r="2"/>
        </svg>`,
        title: 'Botões',
        children: {
          items: this._createButtonMenuItems()
        }
      }
    ];
  }

  /**
   * Creates a toggle switch for a specific button
   * @param {number} buttonIndex - Index of the button
   * @param {boolean} currentValue - Current value
   * @returns {HTMLElement}
   */
  _createButtonToggle(buttonIndex, currentValue) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = 'Ativar';
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      flex: 1;
    `;

    const toggle = document.createElement('input');
    toggle.type = 'checkbox';
    toggle.checked = currentValue;
    toggle.style.cssText = `
      width: 16px;
      height: 16px;
      cursor: pointer;
      appearance: none;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background-color: ${currentValue ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)'};
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: ${currentValue ? '#ffffff' : 'transparent'};
    `;
    toggle.textContent = currentValue ? '✔' : '';

    toggle.addEventListener('change', (event) => {
      this.data.buttons[buttonIndex].enabled = event.target.checked;
      toggle.style.backgroundColor = event.target.checked ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)';
      toggle.style.color = event.target.checked ? '#ffffff' : 'transparent';
      toggle.textContent = event.target.checked ? '✔' : '';
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(toggle);
    return container;
  }

  /**
   * Creates a text input for a specific button text
   * @param {number} buttonIndex - Index of the button
   * @param {string} currentValue - Current value
   * @returns {HTMLElement}
   */
  _createButtonTextInput(buttonIndex, currentValue) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = 'Texto';
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      font-weight: 500;
    `;

    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentValue;
    input.placeholder = 'Texto do botão';
    input.style.cssText = `
      padding: 6px 8px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background: var(--iluria-color-surface, #ffffff);
      color: var(--iluria-color-text-primary, #374151);
      font-size: 13px;
      width: 100%;
      box-sizing: border-box;
    `;

    input.addEventListener('input', (event) => {
      this.data.buttons[buttonIndex].text = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(input);
    return container;
  }

  /**
   * Creates a URL input for a specific button
   * @param {number} buttonIndex - Index of the button
   * @param {string} currentValue - Current value
   * @returns {HTMLElement}
   */
  _createButtonUrlInput(buttonIndex, currentValue) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = 'Link';
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      font-weight: 500;
    `;

    const input = document.createElement('input');
    input.type = 'url';
    input.value = currentValue;
    input.placeholder = 'https://...';
    input.style.cssText = `
      padding: 6px 8px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background: var(--iluria-color-surface, #ffffff);
      color: var(--iluria-color-text-primary, #374151);
      font-size: 13px;
      width: 100%;
      box-sizing: border-box;
    `;

    input.addEventListener('input', (event) => {
      this.data.buttons[buttonIndex].url = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(input);
    return container;
  }

  /**
   * Creates menu items for button configuration
   * @returns {Array} Array of menu items for buttons
   */
  _createButtonMenuItems() {
    const items = [];

    // Garantir que temos pelo menos 1 botão e no máximo 3
    while (this.data.buttons.length < 3) {
      this.data.buttons.push({
        enabled: false,
        text: `Botão ${this.data.buttons.length + 1}`,
        url: '#'
      });
    }

    // Criar submenu para cada botão
    for (let i = 0; i < 3; i++) {
      const button = this.data.buttons[i];
      const buttonNumber = i + 1;

      items.push({
        icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
          <circle cx="12" cy="5" r="2"/>
        </svg>`,
        title: `Botão ${buttonNumber}`,
        children: {
          items: [
            {
              icon: `<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <rect x="3" y="11" width="18" height="10" rx="2" ry="2"/>
                <circle cx="12" cy="5" r="2"/>
              </svg>`,
              title: 'Mostrar Botão',
              type: 'html',
              element: this._createButtonToggle(i, button.enabled)
            },
            {
              icon: `<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M4 7V4h16v3M9 20h6M12 4v16"/>
              </svg>`,
              title: 'Texto do Botão',
              type: 'html',
              element: this._createButtonTextInput(i, button.text)
            },
            {
              icon: `<svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M10 13a5 5 0 0 0 7.54.54l3-3a5 5 0 0 0-7.07-7.07l-1.72 1.71"/>
                <path d="M14 11a5 5 0 0 0-7.54-.54l-3 3a5 5 0 0 0 7.07 7.07l1.71-1.71"/>
              </svg>`,
              title: 'Link do Botão',
              type: 'html',
              element: this._createButtonUrlInput(i, button.url)
            }
          ]
        }
      });
    }

    return items;
  }

  /**
   * Add event listeners
   */
  addEventListeners() {
    // Removido: Image upload via clique na área da imagem
    // Agora o upload é feito apenas pelo menu lateral
  }

  /**
   * Creates a color picker element for the menu
   * @param {string} property - The property name to update
   * @param {string} currentValue - Current color value
   * @param {string} label - Label for the color picker
   * @returns {HTMLElement}
   */
  _createColorPicker(property, currentValue, label) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = label;
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      flex: 1;
      white-space: nowrap;
      overflow: hidden;
      text-overflow: ellipsis;
    `;

    const input = document.createElement('input');
    input.type = 'color';
    input.value = currentValue;
    input.style.cssText = `
      width: 32px;
      height: 24px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      cursor: pointer;
      background: var(--iluria-color-surface, #ffffff);
      flex-shrink: 0;
    `;

    input.addEventListener('input', (event) => {
      this.data[property] = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(input);
    return container;
  }

  /**
   * Creates a position selector for the menu
   * @returns {HTMLElement}
   */
  _createPositionSelector() {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = 'Posição';
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      flex: 1;
    `;

    const select = document.createElement('select');
    select.innerHTML = `
      <option value="left" ${this.data.imagePosition === 'left' ? 'selected' : ''}>Esquerda</option>
      <option value="right" ${this.data.imagePosition === 'right' ? 'selected' : ''}>Direita</option>
    `;
    select.style.cssText = `
      padding: 4px 8px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background: var(--iluria-color-surface, #ffffff);
      color: var(--iluria-color-text-primary, #374151);
      font-size: 13px;
    `;

    select.addEventListener('change', (event) => {
      this.data.imagePosition = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(select);
    return container;
  }

  /**
   * Creates a text input element for the menu
   * @param {string} property - The property name to update
   * @param {string} currentValue - Current value
   * @param {string} label - Label for the input
   * @returns {HTMLElement}
   */
  _createTextInput(property, currentValue, label) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = label;
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      font-weight: 500;
    `;

    const input = document.createElement('input');
    input.type = 'text';
    input.value = currentValue;
    input.placeholder = label;
    input.style.cssText = `
      padding: 6px 8px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background: var(--iluria-color-surface, #ffffff);
      color: var(--iluria-color-text-primary, #374151);
      font-size: 13px;
      width: 100%;
      box-sizing: border-box;
    `;

    input.addEventListener('input', (event) => {
      this.data[property] = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(input);
    return container;
  }

  /**
   * Creates a textarea element for the menu
   * @param {string} property - The property name to update
   * @param {string} currentValue - Current value
   * @param {string} label - Label for the textarea
   * @returns {HTMLElement}
   */
  _createTextArea(property, currentValue, label) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 4px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = label;
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      font-weight: 500;
    `;

    const textarea = document.createElement('textarea');
    textarea.value = currentValue;
    textarea.placeholder = label;
    textarea.rows = 3;
    textarea.style.cssText = `
      padding: 6px 8px;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background: var(--iluria-color-surface, #ffffff);
      color: var(--iluria-color-text-primary, #374151);
      font-size: 13px;
      width: 100%;
      box-sizing: border-box;
      resize: vertical;
      min-height: 60px;
    `;

    textarea.addEventListener('input', (event) => {
      this.data[property] = event.target.value;
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(textarea);
    return container;
  }

  /**
   * Creates a toggle switch element for the menu
   * @param {string} property - The property name to update
   * @param {boolean} currentValue - Current value
   * @param {string} label - Label for the toggle
   * @returns {HTMLElement}
   */
  _createToggle(property, currentValue, label) {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      align-items: center;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      box-sizing: border-box;
      background-color: var(--iluria-color-surface, #ffffff);
      border-radius: 4px;
    `;

    const labelEl = document.createElement('span');
    labelEl.textContent = label;
    labelEl.style.cssText = `
      font-size: 13px;
      color: var(--iluria-color-text-primary, #374151);
      flex: 1;
    `;

    const toggle = document.createElement('input');
    toggle.type = 'checkbox';
    toggle.checked = currentValue;
    toggle.style.cssText = `
      width: 16px;
      height: 16px;
      cursor: pointer;
      appearance: none;
      border: 1px solid var(--iluria-color-border, #d1d5db);
      border-radius: 3px;
      background-color: ${currentValue ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)'};
      display: flex;
      justify-content: center;
      align-items: center;
      font-size: 12px;
      color: ${currentValue ? '#ffffff' : 'transparent'};
    `;
    toggle.textContent = currentValue ? '✔' : '';

    toggle.addEventListener('change', (event) => {
      this.data[property] = event.target.checked;
      toggle.style.backgroundColor = event.target.checked ? 'var(--iluria-color-primary, #3b82f6)' : 'var(--iluria-color-surface, #ffffff)';
      toggle.style.color = event.target.checked ? '#ffffff' : 'transparent';
      toggle.textContent = event.target.checked ? '✔' : '';
      this._updateView();
    });

    container.appendChild(labelEl);
    container.appendChild(toggle);
    return container;
  }






  /**
   * Creates an image uploader for the menu
   * @returns {HTMLElement}
   */
  _createImageUploader() {
    const container = document.createElement('div');
    container.style.cssText = `
      padding: 8px 12px;
      display: flex;
      flex-direction: column;
      gap: 8px;
      min-width: 160px;
      max-width: 180px;
      background-color: var(--iluria-color-surface, #ffffff);
      border: 1px solid var(--iluria-color-border, #e5e7eb);
      border-radius: 4px;
      transition: all 0.2s ease;
      box-sizing: border-box;
    `;

    const label = document.createElement('div');
    label.style.cssText = `
      font-size: 12px;
      color: var(--iluria-color-text-secondary, #6b7280);
      margin-bottom: 4px;
    `;
    label.textContent = 'Imagem';

    // Container para preview da imagem atual
    const previewContainer = document.createElement('div');
    previewContainer.style.cssText = `
      width: 100%;
      min-height: 80px;
      border: 1px dashed var(--iluria-color-border, #d1d5db);
      border-radius: 4px;
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 8px;
      background-color: var(--iluria-color-background, #f9fafb);
      position: relative;
      overflow: hidden;
    `;

    // Função para atualizar preview
    const updatePreview = () => {
      if (this.data.imageUrl) {
        previewContainer.innerHTML = `
          <img src="${this.data.imageUrl}" 
               style="width: 100%; height: 80px; object-fit: cover; border-radius: 2px;" 
               alt="Preview" />
          <div style="
            position: absolute;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            background: rgba(0, 0, 0, 0.7);
            border-radius: 50%;
            width: 32px;
            height: 32px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 1;
            transition: background 0.2s ease;
          " class="edit-overlay">
            <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
              <path d="M11 4H4a2 2 0 0 0-2 2v14a2 2 0 0 0 2 2h14a2 2 0 0 0 2-2v-7"/>
              <path d="M18.5 2.5a2.121 2.121 0 0 1 3 3L12 15l-4 1 1-4 9.5-9.5z"/>
            </svg>
          </div>
          <div style="
            position: absolute;
            top: 4px;
            right: 4px;
            background: rgba(239, 68, 68, 0.9);
            border-radius: 50%;
            width: 24px;
            height: 24px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            opacity: 1;
            transition: background 0.2s ease;
          " class="delete-overlay">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="white" stroke-width="2">
              <path d="M3 6h18"/>
              <path d="M19 6v14c0 1-1 2-2 2H7c-1 0-2-1-2-2V6"/>
              <path d="M8 6V4c0-1 1-2 2-2h4c1 0 2 1 2 2v2"/>
              <line x1="10" y1="11" x2="10" y2="17"/>
              <line x1="14" y1="11" x2="14" y2="17"/>
            </svg>
          </div>
        `;
        
        // Adicionar efeito hover para feedback visual
        previewContainer.addEventListener('mouseenter', () => {
          const editOverlay = previewContainer.querySelector('.edit-overlay');
          const deleteOverlay = previewContainer.querySelector('.delete-overlay');
          if (editOverlay) editOverlay.style.background = 'rgba(0, 0, 0, 0.9)';
          if (deleteOverlay) deleteOverlay.style.background = 'rgba(239, 68, 68, 1)';
        });
        
        previewContainer.addEventListener('mouseleave', () => {
          const editOverlay = previewContainer.querySelector('.edit-overlay');
          const deleteOverlay = previewContainer.querySelector('.delete-overlay');
          if (editOverlay) editOverlay.style.background = 'rgba(0, 0, 0, 0.7)';
          if (deleteOverlay) deleteOverlay.style.background = 'rgba(239, 68, 68, 0.9)';
        });
        
        // Evento para editar imagem
        const editOverlay = previewContainer.querySelector('.edit-overlay');
        if (editOverlay) {
          editOverlay.addEventListener('click', () => {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.style.display = 'none';
            
            fileInput.addEventListener('change', (event) => {
              const file = event.target.files[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                  this.data.imageUrl = e.target.result;
                  updatePreview();
                  this._updateView();
                };
                reader.readAsDataURL(file);
              }
            });
            
            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
          });
        }
        
        // Evento para remover imagem
        const deleteOverlay = previewContainer.querySelector('.delete-overlay');
        if (deleteOverlay) {
          deleteOverlay.addEventListener('click', () => {
            this.data.imageUrl = '';
            updatePreview();
            this._updateView();
          });
        }
      } else {
        previewContainer.innerHTML = `
          <div style="
            text-align: center; 
            color: var(--iluria-color-text-muted, #9ca3af); 
            font-size: 11px;
            cursor: pointer;
            width: 100%;
            height: 100%;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            gap: 4px;
          " class="upload-area">
            <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <circle cx="9" cy="9" r="2"/>
              <path d="m21 15-3.086-3.086a2 2 0 0 0-2.828 0L6 21"/>
            </svg>
            <span>Clique para selecionar</span>
          </div>
        `;
        
        // Evento para upload quando não há imagem
        const uploadArea = previewContainer.querySelector('.upload-area');
        if (uploadArea) {
          uploadArea.addEventListener('click', () => {
            const fileInput = document.createElement('input');
            fileInput.type = 'file';
            fileInput.accept = 'image/*';
            fileInput.style.display = 'none';
            
            fileInput.addEventListener('change', (event) => {
              const file = event.target.files[0];
              if (file) {
                const reader = new FileReader();
                reader.onload = (e) => {
                  this.data.imageUrl = e.target.result;
                  updatePreview();
                  this._updateView();
                };
                reader.readAsDataURL(file);
              }
            });
            
            document.body.appendChild(fileInput);
            fileInput.click();
            document.body.removeChild(fileInput);
          });
        }
      }
    };

    // Montar o componente
    container.appendChild(label);
    container.appendChild(previewContainer);

    // Atualizar preview inicial
    updatePreview();

    return container;
  }

  /**
   * Helper to update the view when settings change.
   */
  _updateView() {
    // Regenerate the entire HTML to reflect changes
    this.wrapper.innerHTML = this.getToolHTML();
    this.addEventListeners();
  }

  /**
   * Open image upload dialog (deprecated - use menu instead)
   */
  openImageUpload() {
    // Funcionalidade movida para o menu "Imagem"
    // Este método foi mantido para compatibilidade mas não faz nada
    console.warn('openImageUpload() foi deprecado. Use o menu "Imagem" para carregar imagens.');
  }



  /**
   * Return Tool's data
   */
  save() {
    return this.data;
  }

  /**
   * Sanitizer config for data cleaning
   */
  static get sanitize() {
    return {
      title: {},
      text: {},
      imageUrl: false,
      textColor: false,
      backgroundColor: false,
      imagePosition: false,
      buttons: {
        text: {},
        url: false,
        color: false,
        backgroundColor: false
      }
    };
  }

  /**
   * Tool's Toolbox settings
   */
  static get toolbox() {
    return {
      title: 'Card de Imagem',
      icon: `<svg width="20" height="20" viewBox="0 0 20 20" xmlns="http://www.w3.org/2000/svg">
        <path d="M2 3h16v2H2V3zm0 4h6v10H2V7zm8 0h8v4h-8V7zm0 6h8v4h-8v-4z"/>
      </svg>`
    };
  }
}
