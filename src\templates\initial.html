<!DOCTYPE html>
<html lang="pt-BR">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>{{storeName}} - Página Inicial</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #fff;
        }
        
        .content-wrapper {
            min-height: 100vh;
            display: flex;
            flex-direction: column;
        }
        
        .component-container {
            position: relative;
        }
        
        .element-content {
            width: 100%;
        }
        
        .container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 0 20px;
        }
        
        /* Header Styles */
        .iluria-header {
            background: #2c3e50;
            color: white;
            padding: 1rem 0;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
        }
        
        .iluria-header nav {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .iluria-header .logo {
            font-size: 1.5rem;
            font-weight: bold;
        }
        
        .iluria-header .nav-links {
            display: flex;
            list-style: none;
            gap: 2rem;
        }
        
        .iluria-header .nav-links a {
            color: white;
            text-decoration: none;
            transition: color 0.3s;
        }
        
        .iluria-header .nav-links a:hover {
            color: #3498db;
        }
        
        /* Hero Styles */
        .iluria-hero {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 4rem 0;
            text-align: center;
        }
        
        .iluria-hero h1 {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .iluria-hero p {
            font-size: 1.25rem;
            margin-bottom: 2rem;
        }
        
        .iluria-hero .btn {
            display: inline-block;
            padding: 12px 30px;
            background: #3498db;
            color: white;
            text-decoration: none;
            border-radius: 5px;
            transition: background 0.3s;
        }
        
        .iluria-hero .btn:hover {
            background: #2980b9;
        }
        
        /* Features Styles */
        .iluria-features {
            padding: 4rem 0;
            background: #f8f9fa;
        }
        
        .iluria-features h2 {
            text-align: center;
            margin-bottom: 3rem;
            color: #2c3e50;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 2rem;
        }
        
        .feature-card {
            background: white;
            padding: 2rem;
            border-radius: 10px;
            text-align: center;
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            transition: transform 0.3s;
        }
        
        .feature-card:hover {
            transform: translateY(-5px);
        }
        
        .feature-icon {
            font-size: 3rem;
            margin-bottom: 1rem;
        }
        
        .feature-card h3 {
            margin-bottom: 1rem;
            color: #2c3e50;
        }
        
        /* Footer Styles */
        .iluria-footer {
            background: #2c3e50;
            color: white;
            padding: 2rem 0;
            text-align: center;
            margin-top: auto;
        }
        
        .iluria-footer p {
            margin-bottom: 0.5rem;
        }
        
        /* Responsive */
        @media (max-width: 768px) {
            .iluria-header nav {
                flex-direction: column;
                gap: 1rem;
            }
            
            .iluria-header .nav-links {
                gap: 1rem;
            }
            
            .iluria-hero h1 {
                font-size: 2rem;
            }
            
            .features-grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body class="" data-page-background="#ffffff" data-page-content-width="100" data-background-type="solid">
    <div class="content-wrapper">
        <div id="app">
            <!-- Header Component -->
            <div class="component-container" data-element-id="el-header-initial" style="transition: outline 0.3s;">
                <div class="element-content">
                    <div data-component="header" data-element-type="header" class="iluria-header">
                        <nav class="container">
                            <div class="logo" data-element-type="text">{{storeName}}</div>
                            <ul class="nav-links">
                                <li><a href="#" data-element-type="text">Início</a></li>
                                <li><a href="#" data-element-type="text">Produtos</a></li>
                                <li><a href="#" data-element-type="text">Sobre</a></li>
                                <li><a href="#" data-element-type="text">Contato</a></li>
                            </ul>
                        </nav>
                    </div>
                </div>
            </div>

            <!-- Hero Component -->
            <div class="component-container" data-element-id="el-hero-initial" style="transition: outline 0.3s;">
                <div class="element-content">
                    <div data-component="hero" data-element-type="hero" class="iluria-hero">
                        <section class="hero">
                            <div class="container">
                                <h1 data-element-type="text">Bem-vindo à {{storeName}}</h1>
                                <p data-element-type="text">Descubra produtos incríveis com qualidade excepcional</p>
                                <a href="#" class="btn" data-element-type="text">Ver Produtos</a>
                            </div>
                        </section>
                    </div>
                </div>
            </div>

            <!-- Features Component -->
            <div class="component-container" data-element-id="el-features-initial" style="transition: outline 0.3s;">
                <div class="element-content">
                    <div data-component="features" data-element-type="features" class="iluria-features">
                        <section class="features">
                            <div class="container">
                                <h2 data-element-type="text">Por que escolher {{storeName}}?</h2>
                                <div class="features-grid">
                                    <div class="feature-card" data-element-type="spacing">
                                        <div class="feature-icon" data-element-type="text">🚚</div>
                                        <h3 data-element-type="text">Entrega Rápida</h3>
                                        <p data-element-type="text">Entregamos seus produtos com agilidade e segurança</p>
                                    </div>
                                    <div class="feature-card" data-element-type="spacing">
                                        <div class="feature-icon" data-element-type="text">💎</div>
                                        <h3 data-element-type="text">Qualidade Premium</h3>
                                        <p data-element-type="text">Produtos selecionados com os mais altos padrões</p>
                                    </div>
                                    <div class="feature-card" data-element-type="spacing">
                                        <div class="feature-icon" data-element-type="text">🔒</div>
                                        <h3 data-element-type="text">Compra Segura</h3>
                                        <p data-element-type="text">Sua segurança é nossa prioridade</p>
                                    </div>
                                </div>
                            </div>
                        </section>
                    </div>
                </div>
            </div>

            <!-- Footer Component -->
            <div class="component-container" data-element-id="el-footer-initial" style="transition: outline 0.3s;">
                <div class="element-content">
                    <div data-component="footer" data-element-type="footer" class="iluria-footer">
                        <footer>
                            <div class="container">
                                <p data-element-type="text">&copy; 2024 {{storeName}}. Todos os direitos reservados.</p>
                                <p data-element-type="text">Feito com ❤️ pela equipe Iluria</p>
                            </div>
                        </footer>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Script de integração com API de produtos -->
    <script id="iluria-product-api">
    (function() {
        // Configuração da API baseada no ambiente
        const isEditor = window.location.pathname.includes('/dev-env/');
        const apiBaseUrl = isEditor ? 'http://localhost:8081/api/products' : '/api/storefront/products';
        
        // Função para carregar produtos
        window.loadProducts = async function(limit = 10) {
            try {
                const response = await fetch(apiBaseUrl);
                const products = await response.json();
                return limit ? products.slice(0, limit) : products;
            } catch (error) {
                console.error('Erro ao carregar produtos:', error);
                return [];
            }
        };
        
        // Auto-carregar produtos se houver elementos com data-product-list
        document.addEventListener('DOMContentLoaded', function() {
          
        });
    })();
    </script>
</body>
</html>
