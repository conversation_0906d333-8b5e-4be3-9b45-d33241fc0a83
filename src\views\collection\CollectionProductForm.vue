<template>
  <div v-if="isLoading" class="page-container">
    <div class="loading-state">
      <div class="loading-spinner"></div>
      <span>{{ t('loading') }}</span>
    </div>
  </div>

  <div v-else class="page-container">
    <!-- Page Header -->
    <IluriaHeader
      :title="isEditing ? t('collectionProduct.form.editTitle') : t('collectionProduct.form.title')"
      :subtitle="t('collectionProduct.form.subtitle', 'Configure os dados da sua coleção')"
      :showCancel="true"
      :cancelText="t('cancel')"
      :showSave="true"
      :saveText="t('save')"
      @cancel-click="router.back()"
      @save-click="saveCollection"
    />

    <Form v-slot="$form" :resolver="resolver" @submit.prevent="saveCollection"
      :validate-on-blur="true"
      :validate-on-value-update="false"
      :validate-on-submit="false">

      <!-- Collection Basic Data -->
      <ViewContainer 
        :title="t('collectionProduct.form.basicData')" 
        :icon="Folder01Icon"
        iconColor="blue"
        :subtitle="t('collectionProduct.form.basicDataSubtitle', 'Informações básicas da coleção')"
      >
        <CollectionBasicData
          ref="collectionBasicDataRef"
          :formContext="$form"
        />
      </ViewContainer>

      <!-- SEO Configuration -->
      <ViewContainer 
        :title="t('seo.title')" 
        :icon="Search02Icon"
        iconColor="green"
        :subtitle="t('seo.subtitle', 'Configurações de SEO para otimizar sua coleção nos motores de busca')"
      >
        <!-- SEO Preview -->
        <div class="seo-preview">
          <IluriaLabel class="preview-label">{{ t('collectionProduct.form.seo.previewTitle') }}:</IluriaLabel>
          
          <div class="preview-card">
            <div v-if="form.seo.slug" class="preview-url">
              www.iluria.com/{{ form.seo.slug }}
            </div>
            
            <div class="preview-title" :class="{'preview-placeholder': !form.seo.metaTitle}">
              {{ form.seo.metaTitle || t('collectionProduct.form.seo.previewTitle') }}
            </div>
            
            <div class="preview-description" :class="{'preview-placeholder': !form.seo.metaDescription}">
              {{ form.seo.metaDescription || t('seo.metaDescriptionPlaceholder') }}
            </div>
          </div>
        </div>
        
        <IluriaSeoForm :form="form" />
      </ViewContainer>
    </Form>
  </div>
</template>

<script setup>
import ViewContainer from '@/components/layout/ViewContainer.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import CollectionBasicData from '@/components/collection/CollectionBasicData.vue'
import collectionService from '@/services/collection.service.js';
import IluriaSeoForm from '@/components/iluria/form/IluriaSeoForm.vue';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import { ref, provide, onMounted } from 'vue';
import { useI18n } from 'vue-i18n';
import { useRouter, useRoute } from 'vue-router';
import { Form } from '@primevue/forms';
import { zodResolver } from '@primevue/forms/resolvers/zod';
import { z } from 'zod';
import { requiredText } from '@/services/validation.service';
import { FloppyDiskIcon, Folder01Icon } from '@hugeicons-pro/core-bulk-rounded';
import { Search02Icon } from '@hugeicons-pro/core-stroke-standard';
import { useToast } from '@/services/toast.service'

const router = useRouter();
const route = useRoute();
const { t } = useI18n();

const isEditing = ref(false);
const collectionBasicDataRef = ref(null);
const collectionId = ref(null);
const toast = useToast();
const isLoading = ref(false);

const form = ref({
  name: '',
  description: '',
  type: 'MANUAL',
  seo: {
    metaTitle: '',
    metaDescription: '',
    slug: ''
  }
});

provide('productForm', form);

const resolver = zodResolver(
  z.object({
    name: requiredText(t('collectionProduct.form.name')),
    description: z.string().optional(),
  })
);

onMounted(async () => {
  if (route.params.id) {
    isEditing.value = true;
    collectionId.value = route.params.id;
    await loadCollection();
  }
});

async function loadCollection() {
  try {
    isLoading.value = true;
    const collectionData = await collectionService.getCollection(collectionId.value);
    
    form.value = {
      name: collectionData.name || '',
      description: collectionData.description || '',
      type: collectionData.type || 'MANUAL',
      seo: {
        metaTitle: collectionData.metaTitle || '',
        metaDescription: collectionData.metaDescription || '',
        slug: collectionData.slug || ''
      }
    };
  } catch (error) {
    toast.showError(t('collectionProduct.form.loadError'));
  } finally {
    isLoading.value = false;
  }
}

function resetForm() {
  form.value = {
    name: '',
    description: '',
    type: 'MANUAL',
    seo: {
      metaTitle: '',
      metaDescription: '',
      slug: '',
    },
  };
}

async function saveCollection() {
  try {
    const collectionData = {
      ...form.value,
      metaTitle: form.value.seo?.metaTitle || '',
      metaDescription: form.value.seo?.metaDescription || '',
      slug: form.value.seo?.slug || ''
    };
    
    delete collectionData.seo;
    
    if (isEditing.value) {
      await collectionService.updateCollection(collectionId.value, collectionData);
    } else {
      await collectionService.createCollection(collectionData);
    }
    toast.showSuccess(t('collectionProduct.form.saveSuccess'));
    router.back();
  } catch (error) {
    toast.showError(t('collectionProduct.form.saveError'));
    resetForm();
  }
}
</script>

<style scoped>
.page-container {
  padding: 24px;
}



/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 64px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* SEO Preview */
.seo-preview {
  margin-bottom: 24px;
  padding: 20px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
}

.preview-label {
  display: block;
  margin-bottom: 12px;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

.preview-card {
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 16px;
}

.preview-url {
  font-size: 12px;
  color: var(--iluria-color-success);
  margin-bottom: 8px;
  font-weight: 500;
}

.preview-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 8px;
  line-height: 1.3;
  word-break: break-word; /* NEW: break long strings */
  overflow-wrap: anywhere; /* NEW: allow wrapping anywhere */
}

.preview-title.preview-placeholder {
  color: var(--iluria-color-text-muted);
  font-style: italic;
}

.preview-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
  word-break: break-word; /* NEW: break long strings */
  overflow-wrap: anywhere; /* NEW: allow wrapping anywhere */
}

.preview-description.preview-placeholder {
  color: var(--iluria-color-text-muted);
  font-style: italic;
}

/* Spacing between ViewContainers */
.page-container > form > :deep(.view-container:not(:last-child)) {
  margin-bottom: 24px;
}

/* Responsive */
@media (max-width: 768px) {
  .page-container {
    padding: 16px;
  }
  
  .seo-preview {
    padding: 16px;
  }
  
  .preview-card {
    padding: 12px;
  }
}


</style>
