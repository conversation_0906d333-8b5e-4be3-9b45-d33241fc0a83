<template>
  <Transition name="slide-up" appear>
    <div v-if="selectedCount > 0" class="bulk-action-bar" :class="{ 'loading': loading }">
      <div class="bulk-action-content">
        <!-- Left side: Counter -->
        <div class="bulk-counter">
          <span class="counter-number">{{ selectedCount }}</span>
          <span class="counter-text">{{ counterText }}</span>
        </div>

        <!-- Right side: Actions -->
        <div class="bulk-actions">
          <slot name="actions">
            <IluriaButton
              v-for="(action, index) in actions"
              :key="index"
              :color="action.color || 'secondary'"
              :variant="action.variant || 'solid'"
              :size="action.size || 'medium'"
              :huge-icon="action.icon"
              :loading="loading && action.loading"
              :disabled="disabled || (loading && !action.loading)"
              @click="handleActionClick(action, index)"
            >
              {{ action.text }}
            </IluriaButton>
          </slot>
        </div>
      </div>
    </div>
  </Transition>
</template>

<script setup>
import { computed } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaButton from './IluriaButton.vue'

const { t } = useI18n()

const props = defineProps({
  selectedCount: {
    type: Number,
    required: true,
    validator: (value) => value >= 0
  },
  actions: {
    type: Array,
    default: () => [],
    validator: (actions) => actions.every(action => 
      action && 
      typeof action.text === 'string' && 
      typeof action.callback === 'function'
    )
  },
  loading: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  entityName: {
    type: String,
    default: 'item',
    validator: (value) => typeof value === 'string' && value.length > 0
  },
  entityNamePlural: {
    type: String,
    default: 'itens',
    validator: (value) => typeof value === 'string' && value.length > 0
  }
})

const emit = defineEmits(['action-click'])

const counterText = computed(() => {
  const count = props.selectedCount
  if (count === 1) {
    return `${props.entityName} ${t('customer.bulk.selectedOne')}`
  }
  return `${props.entityNamePlural} ${t('customer.bulk.selectedAny')}`
})

const handleActionClick = (action, index) => {
  if (props.disabled || props.loading) return
  
  emit('action-click', { action, index })
  
  if (typeof action.callback === 'function') {
    action.callback()
  }
}

defineOptions({
  name: 'IluriaBulkActionBar'
})
</script>

<style scoped>
.bulk-action-bar {
  position: sticky;
  bottom: 0;
  left: 0;
  right: 0;
  z-index: 40;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  box-shadow: var(--iluria-shadow-sm);
  transition: all 0.3s ease;
  margin-bottom: 16px;
}

.bulk-action-bar.loading {
  pointer-events: none;
  opacity: 0.8;
}

.bulk-action-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 12px 16px;
  gap: 16px;
}

/* Counter Section */
.bulk-counter {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--iluria-color-text-primary);
}

.counter-number {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  min-width: 24px;
  height: 24px;
  padding: 0 8px;
  background: var(--iluria-color-primary);
  color: var(--iluria-color-button-primary-fg);
  border-radius: 12px;
  font-size: 12px;
  font-weight: 600;
  line-height: 1;
}

.counter-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
}

/* Actions Section */
.bulk-actions {
  display: flex;
  align-items: center;
  gap: 12px;
}

/* Transitions */
.slide-up-enter-active,
.slide-up-leave-active {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.slide-up-enter-from,
.slide-up-leave-to {
  transform: translateY(100%);
  opacity: 0;
}

.slide-up-enter-to,
.slide-up-leave-from {
  transform: translateY(0);
  opacity: 1;
}

/* Responsive Design */
@media (max-width: 768px) {
  .bulk-action-content {
    flex-direction: column;
    gap: 12px;
    padding: 12px 16px;
  }
  
  .bulk-counter {
    order: 1;
  }
  
  .bulk-actions {
    order: 2;
    flex-wrap: wrap;
    justify-content: center;
    gap: 8px;
  }
}

@media (max-width: 480px) {
  .bulk-actions {
    width: 100%;
  }
  
  .bulk-actions :deep(.btn) {
    flex: 1;
    min-width: 0;
  }
}

/* Desktop optimizations */
@media (min-width: 1024px) {
  .bulk-action-content {
    max-width: 1400px;
    margin: 0 auto;
  }
}

/* Accessibility */
@media (prefers-reduced-motion: reduce) {
  .bulk-action-bar,
  .slide-up-enter-active,
  .slide-up-leave-active {
    transition: none;
  }
}

/* High contrast mode */
@media (prefers-contrast: high) {
  .bulk-action-bar {
    border-top-width: 2px;
  }
  
  .counter-number {
    border: 1px solid var(--iluria-color-text-primary);
  }
}

/* Focus management */
.bulk-action-bar:focus-within {
  box-shadow: 0 -4px 6px -1px rgba(0, 0, 0, 0.1), 
              0 -2px 4px -2px rgba(0, 0, 0, 0.1),
              inset 0 0 0 2px var(--iluria-color-primary);
}
</style>