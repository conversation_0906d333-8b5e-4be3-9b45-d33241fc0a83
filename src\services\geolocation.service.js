/**
 * Serviço de Geolocalização por IP
 * Utiliza ipinfo.io como provedor principal (50k requests/mês gr<PERSON>)
 * Com fallback inteligente baseado no navegador
 */

class GeolocationService {
    #cache = new Map()
    #cacheExpiry = 24 * 60 * 60 * 1000 // 24 horas em ms
    
    /**
     * Obtém localização por IP com cache
     * @param {string} ip - Endereço IP
     * @returns {Promise<Object>} Informações de localização
     */
    async getLocationByIP(ip) {
        if (!ip || ip === '127.0.0.1' || ip === 'localhost') {
            return this.#getLocalLocation()
        }

        // Verifica cache
        const cached = this.#getFromCache(ip)
        if (cached) {
            return cached
        }

        try {
            // Usa ipinfo.io como provedor principal (50k requests/mês grátis)
            const location = await this.#fetchFromIpinfo(ip)
            this.#saveToCache(ip, location)
            return location
        } catch (error) {
            console.warn('Erro na geolocalização:', error.message)

            // Fallback: gera localização baseada em heurísticas
            const fallbackLocation = this.#generateFallbackLocation(ip)
            this.#saveToCache(ip, fallbackLocation)
            return fallbackLocation
        }
    }

    /**
     * Obtém localização do IP atual do usuário
     * @returns {Promise<Object>} Informações de localização
     */
    async getCurrentLocation() {
        try {
            // Usa ipinfo.io para detectar localização atual
            const response = await this.#fetchWithTimeout('https://ipinfo.io/json', 3000)
            const data = await response.json()

            if (data.error) {
                throw new Error(data.error || 'Erro na API')
            }

            return this.#formatLocation(data, 'ipinfo')
        } catch (error) {
            console.warn('Erro ao obter localização atual:', error.message)

            // Fallback: usa localização inteligente baseada no navegador
            return this.#getSmartLocalLocation()
        }
    }

    /**
     * Fetch com timeout personalizado
     * @private
     * @param {string} url
     * @param {number} timeout
     * @returns {Promise<Response>}
     */
    async #fetchWithTimeout(url, timeout = 5000) {
        const controller = new AbortController()
        const timeoutId = setTimeout(() => controller.abort(), timeout)

        try {
            const response = await fetch(url, {
                signal: controller.signal,
                headers: {
                    'Accept': 'application/json',
                    'Content-Type': 'application/json'
                }
            })

            if (!response.ok) {
                throw new Error(`HTTP ${response.status}: ${response.statusText}`)
            }

            return response
        } finally {
            clearTimeout(timeoutId)
        }
    }

    /**
     * Busca localização via ipwhois.app
     * @private
     * @param {string} ip
     * @returns {Promise<Object>}
     */
    async #fetchFromIpWhoIs(ip) {
        const response = await this.#fetchWithTimeout(`https://ipwhois.app/json/${ip}`, 3000)
        const data = await response.json()

        if (!data.success) {
            throw new Error(`ipwhois: ${data.message || 'Erro desconhecido'}`)
        }

        return this.#formatLocation(data, 'ipwhois')
    }

    /**
     * Busca localização via ipinfo.io
     * @private
     * @param {string} ip
     * @returns {Promise<Object>}
     */
    async #fetchFromIpinfo(ip) {
        const response = await this.#fetchWithTimeout(`https://ipinfo.io/${ip}/json`, 3000)
        const data = await response.json()

        if (data.error) {
            throw new Error(`ipinfo.io: ${data.error || 'Erro desconhecido'}`)
        }

        return this.#formatLocation(data, 'ipinfo')
    }



    /**
     * Formata dados de localização para formato padrão
     * @private
     * @param {Object} data 
     * @param {string} provider 
     * @returns {Object}
     */
    #formatLocation(data, provider) {
        let formatted = {
            ip: data.ip || data.query || 'Desconhecido',
            country: 'Desconhecido',
            countryCode: 'XX',
            region: 'Desconhecido',
            city: 'Desconhecido',
            latitude: null,
            longitude: null,
            timezone: null,
            isp: 'Desconhecido',
            display: 'Localização desconhecida',
            provider
        }

        if (provider === 'ipinfo') {
            // ipinfo.io format
            formatted.country = data.country || 'Desconhecido'
            formatted.countryCode = data.country || 'XX'
            formatted.region = data.region || 'Desconhecido'
            formatted.city = data.city || 'Desconhecido'

            // ipinfo.io retorna loc como "lat,lng"
            if (data.loc) {
                const [lat, lng] = data.loc.split(',')
                formatted.latitude = parseFloat(lat)
                formatted.longitude = parseFloat(lng)
            }

            formatted.timezone = data.timezone
            formatted.isp = data.org || 'Desconhecido'
        }

        // Gera display amigável
        formatted.display = this.#generateDisplayName(formatted)

        return formatted
    }

    /**
     * Gera nome de exibição amigável
     * @private
     * @param {Object} location 
     * @returns {string}
     */
    #generateDisplayName(location) {
        const parts = []
        
        if (location.city && location.city !== 'Desconhecido') {
            parts.push(location.city)
        }
        
        if (location.region && location.region !== 'Desconhecido' && location.region !== location.city) {
            parts.push(location.region)
        }
        
        if (location.country && location.country !== 'Desconhecido') {
            parts.push(location.country)
        }
        
        return parts.length > 0 ? parts.join(', ') : 'Localização desconhecida'
    }

    /**
     * Obtém dados do cache
     * @private
     * @param {string} ip 
     * @returns {Object|null}
     */
    #getFromCache(ip) {
        const cached = this.#cache.get(ip)
        if (!cached) return null
        
        const now = Date.now()
        if (now - cached.timestamp > this.#cacheExpiry) {
            this.#cache.delete(ip)
            return null
        }
        
        return cached.data
    }

    /**
     * Salva dados no cache
     * @private
     * @param {string} ip 
     * @param {Object} data 
     */
    #saveToCache(ip, data) {
        this.#cache.set(ip, {
            data,
            timestamp: Date.now()
        })
        
        // Limpa cache antigo periodicamente
        if (this.#cache.size > 100) {
            this.#cleanCache()
        }
    }

    /**
     * Limpa entradas antigas do cache
     * @private
     */
    #cleanCache() {
        const now = Date.now()
        for (const [ip, cached] of this.#cache.entries()) {
            if (now - cached.timestamp > this.#cacheExpiry) {
                this.#cache.delete(ip)
            }
        }
    }

    /**
     * Retorna localização local padrão
     * @private
     * @returns {Object}
     */
    #getLocalLocation() {
        // Tenta detectar país pelo timezone do navegador
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
        let country = 'Brasil'
        let countryCode = 'BR'
        let city = 'Localhost'

        // Mapeia alguns timezones comuns
        if (timezone.includes('America/Sao_Paulo') || timezone.includes('America/Fortaleza')) {
            country = 'Brasil'
            countryCode = 'BR'
            city = 'Brasil'
        } else if (timezone.includes('America/New_York') || timezone.includes('America/Chicago')) {
            country = 'Estados Unidos'
            countryCode = 'US'
            city = 'Estados Unidos'
        } else if (timezone.includes('Europe/')) {
            country = 'Europa'
            countryCode = 'EU'
            city = 'Europa'
        }

        return {
            ip: '127.0.0.1',
            country,
            countryCode,
            region: 'Local',
            city,
            latitude: null,
            longitude: null,
            timezone,
            isp: 'Local',
            display: `${city}, ${country}`,
            provider: 'local'
        }
    }

    /**
     * Gera localização de fallback baseada em heurísticas
     * @private
     * @param {string} ip
     * @returns {Object}
     */
    #generateFallbackLocation(ip) {
        // Usa timezone do navegador como base
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
        const language = navigator.language || 'pt-BR'

        let country = 'Brasil'
        let countryCode = 'BR'
        let city = 'Brasil'

        // Detecta país pelo timezone
        if (timezone.includes('America/Sao_Paulo') || timezone.includes('America/Fortaleza')) {
            country = 'Brasil'
            countryCode = 'BR'
            city = 'Brasil'
        } else if (timezone.includes('America/New_York') || timezone.includes('America/Chicago')) {
            country = 'Estados Unidos'
            countryCode = 'US'
            city = 'Estados Unidos'
        } else if (timezone.includes('Europe/')) {
            country = 'Europa'
            countryCode = 'EU'
            city = 'Europa'
        } else if (language.startsWith('pt')) {
            country = 'Brasil'
            countryCode = 'BR'
            city = 'Brasil'
        }

        return {
            ip,
            country,
            countryCode,
            region: 'Estimado',
            city,
            latitude: null,
            longitude: null,
            timezone,
            isp: 'Estimado',
            display: `${city}, ${country}`,
            provider: 'fallback'
        }
    }

    /**
     * Retorna localização inteligente baseada no navegador
     * @private
     * @returns {Object}
     */
    #getSmartLocalLocation() {
        const timezone = Intl.DateTimeFormat().resolvedOptions().timeZone
        const language = navigator.language || 'pt-BR'

        let country = 'Brasil'
        let countryCode = 'BR'
        let city = 'Brasil'

        // Detecta país pelo timezone e idioma
        if (timezone.includes('America/Sao_Paulo') || language.startsWith('pt-BR')) {
            country = 'Brasil'
            countryCode = 'BR'
            city = 'Brasil'
        } else if (timezone.includes('America/New_York') || language.startsWith('en-US')) {
            country = 'Estados Unidos'
            countryCode = 'US'
            city = 'Estados Unidos'
        } else if (timezone.includes('Europe/')) {
            country = 'Europa'
            countryCode = 'EU'
            city = 'Europa'
        }

        return {
            ip: 'Auto-detectado',
            country,
            countryCode,
            region: 'Auto-detectado',
            city,
            latitude: null,
            longitude: null,
            timezone,
            isp: 'Auto-detectado',
            display: `${city}, ${country}`,
            provider: 'browser'
        }
    }

    /**
     * Retorna localização desconhecida padrão
     * @private
     * @returns {Object}
     */
    #getUnknownLocation() {
        return {
            ip: 'Desconhecido',
            country: 'Desconhecido',
            countryCode: 'XX',
            region: 'Desconhecido',
            city: 'Desconhecido',
            latitude: null,
            longitude: null,
            timezone: null,
            isp: 'Desconhecido',
            display: 'Localização desconhecida',
            provider: 'unknown'
        }
    }

    /**
     * Limpa todo o cache
     */
    clearCache() {
        this.#cache.clear()
    }

    /**
     * Obtém estatísticas do cache
     * @returns {Object}
     */
    getCacheStats() {
        return {
            size: this.#cache.size,
            entries: Array.from(this.#cache.keys())
        }
    }
}

// Exporta instância singleton
export default new GeolocationService()
