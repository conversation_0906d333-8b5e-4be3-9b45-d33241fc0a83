<script setup>
import { ref, onMounted, provide, watch, computed } from 'vue'
import EditorHeader from '@/components/layoutEditor/BaseEditorLayout/EditorHeader.vue'
import EditorCanvas from '@/components/layoutEditor/BaseEditorLayout/EditorCanvas.vue'
import EditorLayout from '@/components/layoutEditor/sidebar/EditorLayout.vue'
import Toast from 'primevue/toast'
import { useElementSelection } from '@/composables/useElementSelection.js'
import { useComponentProcessing } from '@/composables/useComponentProcessing.js'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import { useComponentRegistry } from '@/composables/useComponentRegistry.js'
import { useTheme } from '@/composables/useTheme'
import { useUndoRedoSingleton } from '@/composables/useUndoRedo.js'
import { useLayoutManager } from '@/composables/useLayoutManager'
import '@/components/layoutEditor/registry/BasicEditorsSetup.js'

const { t } = useI18n()
const toast = useToast()

// Sistema de Undo/Redo
const {
  canUndo,
  canRedo,
  saveState,
  undo,
  redo,
  setupKeyboardShortcuts,
  initialize: initializeUndoRedo,
  markAsSaved,
  flushPendingStates
} = useUndoRedoSingleton()

// ✅ NOVO: Sistema de gerenciamento de layouts
const {
  currentLayout,
} = useLayoutManager()

// ✅ NOVO: Computed para determinar o tipo de layout atual
const currentLayoutType = computed(() => {
  if (!currentLayout.value) return 'static'
  
  // 🔧 CORREÇÃO: Usar o tipo do layout em vez de mapear layoutId específico
  // Isso permite que layouts customizados (cópias) sejam detectados corretamente
  if (currentLayout.value.type) {
    return currentLayout.value.type
  }
  
  // Fallback: mapear layoutId para tipo de layout (para layouts antigos)
  const layoutTypeMap = {
    'index': 'static',
    'product': 'product',
    'collection': 'collection',
    'cart': 'system',
    '404': 'system'
  }
  
  const layoutType = layoutTypeMap[currentLayout.value.layoutId] || 'static'
  return layoutType
})
const { shouldIgnoreElementForWrapping } = useElementSelection()
const { processComponentsAndInjectScripts } = useComponentProcessing()
const { 
  autoInitializeComponents 
} = useComponentRegistry()

// Inicializar sistema de temas
const { initTheme } = useTheme()
initTheme()

const mode = ref('view')
const viewportSize = ref('desktop')
const initialHtml = ref('')
const isLoading = ref(true)
const hasError = ref(false)
const editorCanvasRef = ref(null)
const editorHeaderRef = ref(null)
const editorLayoutRef = ref(null)
import { getAuthToken, AUTH_CONFIG } from '@/config/auth'
import { createScriptInjectionService } from '@/services/scriptInjectionService.js'
import { useAuthStore } from '@/stores/auth.store'
import tokenManager from '@/services/tokenManager.service'

const authStore = useAuthStore()
// Usar storeToken em vez de userToken para operações que precisam de storeId
const authToken = authStore.storeToken || authStore.jwtToken || getAuthToken()

// Verificar se o token tem storeId
const tokenInfo = tokenManager.extractUserInfo(authToken)
if (!tokenInfo?.storeId) {
  console.warn('Token não contém storeId. Operações de upload podem falhar.')
}

// Referência para cleanup dos atalhos de teclado
let keyboardShortcutsCleanup = null

// Estado das configurações da página
const pageSettings = ref({
  backgroundColor: '#ffffff',
      contentWidth: 75
})

provide('initialHtml', initialHtml)
provide('authToken', authToken)
provide('pageSettings', pageSettings)

onMounted(async () => {
  try {
    // ✅ CORREÇÃO: Garantir que inicia sem layout específico
    currentLayout.value = null
    await fetchInitialHtml()
  } catch (error) {
    hasError.value = true
  } finally {
    isLoading.value = false
  }
})

// ✅ NOVO: Função para buscar HTML específico de um layout
async function fetchLayoutHtml(layout) {
  try {
    const filename = layout.filename || 'index.html'
    
    // Para layouts padrão (index.html), sempre usar fetchInitialHtml
    if (filename === 'index.html' || layout.layoutId === 'index') {
      // ✅ CORREÇÃO: Resetar layout para index quando carrega página inicial
      currentLayout.value = null
      await fetchInitialHtml()
      return
    }
    
    // Para outros layouts, tentar buscar arquivo específico
    // Obter storeId do token atual
    const authStore = useAuthStore()
    const currentToken = authStore.getCurrentToken()
    const userInfo = tokenManager.extractUserInfo(currentToken)

    if (!userInfo?.storeId) {
      console.error('StoreId not found in token for layout fetch')
      return
    }

    const layoutEndpoint = `${AUTH_CONFIG.ENDPOINTS.DEV_ENV}/${userInfo.storeId}/${filename}`
    
    const response = await fetch(layoutEndpoint, {
      method: 'GET',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'Cache-Control': 'no-cache'
      }
    })
    
    if (!response.ok) {
      // Se o layout específico não existir, usar o padrão
      currentLayout.value = null
      await fetchInitialHtml()
      return
    }
    
    const htmlContent = await response.text()
    
    if (htmlContent && htmlContent.trim()) {
      // Processar o HTML do layout
      await processLayoutHtml(htmlContent)
      hasError.value = false
    } else {
      currentLayout.value = null
      await fetchInitialHtml()
    }
    
  } catch (error) {
    // Fallback para HTML inicial
    currentLayout.value = null
    await fetchInitialHtml()
  }
}

async function fetchInitialHtml() {
  try {
    // ✅ CORREÇÃO: Resetar layout atual quando carrega HTML inicial
    currentLayout.value = null

    // Obter storeId do token atual
    const authStore = useAuthStore()
    const currentToken = authStore.getCurrentToken()
    const userInfo = tokenManager.extractUserInfo(currentToken)

    if (!userInfo?.storeId) {
      console.error('StoreId not found in token for initial HTML fetch')
      hasError.value = true
      return
    }

    // Lista de endpoints para tentar em ordem de prioridade (com storeId)
    const endpoints = [
      `${AUTH_CONFIG.ENDPOINTS.DEV_ENV}/${userInfo.storeId}`,
      `${AUTH_CONFIG.ENDPOINTS.DEV_ENV}/${userInfo.storeId}/index.html`,
      `${AUTH_CONFIG.ENDPOINTS.DEV_ENV_PAGES}/${userInfo.storeId}`
    ]
    
    let response = null
    let lastError = null
    
    // Tenta cada endpoint até encontrar um que funcione
    for (const endpoint of endpoints) {
      try {
        
        response = await fetch(endpoint, {
          method: 'GET',
          headers: {
            'Authorization': `Bearer ${authToken}`,
            'Cache-Control': 'no-cache'
          }
        })
        
        if (response.ok) {
          break
        } else {
          console.warn(`⚠️ [EditorView] Falha no endpoint ${endpoint}: ${response.status}`)
          lastError = { endpoint, status: response.status }
        }
      } catch (error) {
        console.warn(`⚠️ [EditorView] Erro ao tentar endpoint ${endpoint}:`, error)
        lastError = { endpoint, error }
      }
    }

    if (!response || !response.ok) {
      console.error('❌ [EditorView] Todos os endpoints falharam:', lastError)
      
      // Se tudo falhar, mostrar erro
      hasError.value = true
      initialHtml.value = ''
      return
    }

    const htmlContent = await response.text()
    


    if (htmlContent && htmlContent.trim()) {
      // Extrair configurações do HTML antes de processar
      const parser = new DOMParser()
      const doc = parser.parseFromString(htmlContent, 'text/html')
      const body = doc.body
      
      if (body) {
        // Atualiza as configurações com os valores salvos
        const savedBgColor = body.getAttribute('data-page-background')
        const savedWidth = body.getAttribute('data-page-content-width')
        
        // ✅ NOVO: Carrega configurações específicas da página
        const savedPageTitle = body.getAttribute('data-page-title')
        const savedPageLogo = body.getAttribute('data-page-logo')
        const savedPageFavicon = body.getAttribute('data-page-favicon')
        
        // Recupera configurações avançadas de background
        const backgroundType = body.getAttribute('data-background-type')
        
        // Configurações básicas
        if (savedBgColor) {
          pageSettings.value.backgroundColor = savedBgColor
        }
        if (savedWidth) {
          let width = parseInt(savedWidth) || 75
          
          // Converter valores antigos para o novo sistema de porcentagem
          if (width >= 1000) {
            if (width >= 9999) width = 100
            else if (width >= 8500) width = 75
            else if (width >= 8000) width = 50
            else if (width >= 2000) width = 75
            else width = 50
          }
          
          pageSettings.value.contentWidth = width
        }
        
        // ✅ NOVO: Aplica configurações da página
        if (savedPageTitle) {
          pageSettings.value.pageTitle = savedPageTitle
        }
        if (savedPageLogo) {
          pageSettings.value.pageLogo = savedPageLogo
        }
        if (savedPageFavicon) {
          pageSettings.value.pageFavicon = savedPageFavicon
        }
        
        // Configurações de tipo de background
        if (backgroundType) {
          pageSettings.value.backgroundType = backgroundType
          
          // Configurações específicas para cada tipo de background
          if (backgroundType === 'preset') {
            const selectedPreset = body.getAttribute('data-selected-preset')
            const presetCss = body.getAttribute('data-preset-css')
            
            if (selectedPreset) {
              pageSettings.value.selectedPreset = selectedPreset
            }
            if (presetCss) {
              pageSettings.value.presetCss = presetCss
            }
          } else if (backgroundType === 'gradient') {
            const gradientType = body.getAttribute('data-gradient-type')
            const gradientAngle = body.getAttribute('data-gradient-angle')
            const gradientStops = body.getAttribute('data-gradient-stops')
            
            if (gradientType) {
              pageSettings.value.gradientType = gradientType
            }
            if (gradientAngle) {
              pageSettings.value.gradientAngle = parseInt(gradientAngle) || 90
            }
            if (gradientStops) {
              try {
                pageSettings.value.gradientStops = JSON.parse(gradientStops)
              } catch (e) {
                console.error('Erro ao parsear gradient stops:', e)
              }
            }
          } else if (backgroundType === 'image') {
            const backgroundImage = body.getAttribute('data-background-image')
            const imageSize = body.getAttribute('data-image-size')
            const imagePosition = body.getAttribute('data-image-position')
            
            if (backgroundImage) {
              pageSettings.value.backgroundImage = backgroundImage
            }
            if (imageSize) {
              pageSettings.value.imageSize = imageSize
            }
            if (imagePosition) {
              pageSettings.value.imagePosition = imagePosition
            }
          }
        }
        
      }

      initialHtml.value = processHtmlForEditing(htmlContent)
      hasError.value = false
    } else {
      hasError.value = true
      initialHtml.value = ''
    }
  } catch (error) {
    console.error('❌ [EditorView] Erro ao buscar HTML inicial:', error)
    hasError.value = true
    initialHtml.value = ''
  }
  
  // ✅ Após carregar o HTML, inicializa automaticamente todos os componentes
  setTimeout(() => {
    try {
      autoInitializeComponents(document)
      
      // Inicializa o sistema de undo/redo após carregar o HTML
      initializeUndoRedoSystem()
    } catch (error) {
      console.error('❌ [EditorView] Erro na auto-inicialização:', error)
    }
  }, 500)
}

// ✅ NOVO: Função para processar HTML de layout específico
async function processLayoutHtml(htmlContent) {
  // Extrair configurações do HTML antes de processar (mesmo que fetchInitialHtml)
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlContent, 'text/html')
  const body = doc.body
  
  if (body) {
    // Manter configurações atuais como base, só sobrescrever se existir no HTML
    const currentSettings = { ...pageSettings.value }
    
    // Carregar configurações salvas no HTML
    const savedBgColor = body.getAttribute('data-page-background')
    const savedWidth = body.getAttribute('data-page-content-width')
    const savedPageTitle = body.getAttribute('data-page-title')
    const savedPageLogo = body.getAttribute('data-page-logo')
    const savedPageFavicon = body.getAttribute('data-page-favicon')
    const backgroundType = body.getAttribute('data-background-type')
    
    // Aplicar configurações apenas se existirem no HTML
    if (savedBgColor) {
      pageSettings.value.backgroundColor = savedBgColor
    } else {
      // Manter cor atual se não especificada
      pageSettings.value.backgroundColor = currentSettings.backgroundColor || '#ffffff'
    }
    
    if (savedWidth) {
      let width = parseInt(savedWidth) || 75
      if (width >= 1000) {
        if (width >= 9999) width = 100
        else if (width >= 8500) width = 75
        else if (width >= 8000) width = 50
        else if (width >= 2000) width = 75
        else width = 50
      }
      pageSettings.value.contentWidth = width
    } else {
      // Manter largura atual se não especificada
      pageSettings.value.contentWidth = currentSettings.contentWidth || 75
    }
    
    // Aplicar configurações da página apenas se existirem
    pageSettings.value.pageTitle = savedPageTitle || currentSettings.pageTitle
    pageSettings.value.pageLogo = savedPageLogo || currentSettings.pageLogo
    pageSettings.value.pageFavicon = savedPageFavicon || currentSettings.pageFavicon
    
    // Configurações de background avançadas
    if (backgroundType) {
      pageSettings.value.backgroundType = backgroundType
      
      if (backgroundType === 'preset') {
        const selectedPreset = body.getAttribute('data-selected-preset')
        const presetCss = body.getAttribute('data-preset-css')
        if (selectedPreset) pageSettings.value.selectedPreset = selectedPreset
        if (presetCss) pageSettings.value.presetCss = presetCss
      } else if (backgroundType === 'gradient') {
        const gradientType = body.getAttribute('data-gradient-type')
        const gradientAngle = body.getAttribute('data-gradient-angle')
        const gradientStops = body.getAttribute('data-gradient-stops')
        if (gradientType) pageSettings.value.gradientType = gradientType
        if (gradientAngle) pageSettings.value.gradientAngle = parseInt(gradientAngle) || 90
        if (gradientStops) {
          try {
            pageSettings.value.gradientStops = JSON.parse(gradientStops)
          } catch (e) {
            console.error('Erro ao parsear gradient stops:', e)
          }
        }
      } else if (backgroundType === 'image') {
        const backgroundImage = body.getAttribute('data-background-image')
        const imageSize = body.getAttribute('data-image-size')
        const imagePosition = body.getAttribute('data-image-position')
        if (backgroundImage) pageSettings.value.backgroundImage = backgroundImage
        if (imageSize) pageSettings.value.imageSize = imageSize
        if (imagePosition) pageSettings.value.imagePosition = imagePosition
      }
    }
  }

  // Processar HTML para edição
  initialHtml.value = processHtmlForEditing(htmlContent)
}

function processHtmlForEditing(htmlContent) {
  let bodyContent = htmlContent
  
  // Extrair o conteúdo do body
  const bodyMatch = bodyContent.match(/<body[^>]*>([\s\S]*?)<\/body>/i)
  if (bodyMatch && bodyMatch[1]) {
    bodyContent = bodyMatch[1].trim()
  }
  
  const parser = new DOMParser()
  const doc = parser.parseFromString(`<div>${bodyContent}</div>`, 'text/html')
  const container = doc.body.firstChild
  
  // Primeiro, remover qualquer container aninhado que possa existir
  const nestedContainers = container.querySelectorAll('.component-container .component-container')
  nestedContainers.forEach(nestedContainer => {
    const parentContainer = nestedContainer.closest('.component-container')
    const parent = parentContainer.parentNode
    
    // Mover todos os elementos do container aninhado para o pai
    Array.from(nestedContainer.childNodes).forEach(child => {
      parent.insertBefore(child, parentContainer.nextSibling)
    })
    
    // Remover o container aninhado vazio
    if (nestedContainer.parentNode) {
      nestedContainer.parentNode.removeChild(nestedContainer)
    }
  })
  
  // Processar grids de produtos
  const productGrids = container.querySelectorAll('[data-component="dynamic-grid-produtos"]')
  productGrids.forEach(gridElement => {
    // Verificar se já está em um container
    if (gridElement.closest('.component-container')) {
      // Se já está em um container, verificar se a estrutura está correta
      const existingContainer = gridElement.closest('.component-container')
      const existingContentWrapper = existingContainer.querySelector('.element-content')
      
      // Se não tem o wrapper correto, reorganizar
      if (!existingContentWrapper || !existingContentWrapper.contains(gridElement)) {
        // Limpar o container
        while (existingContainer.firstChild) {
          existingContainer.removeChild(existingContainer.firstChild)
        }
        
        // Criar a estrutura correta
        const contentWrapper = doc.createElement('div')
        contentWrapper.className = 'element-content'
        contentWrapper.appendChild(gridElement)
        existingContainer.appendChild(contentWrapper)
      }
      
      return
    }
    
    // Se não está em um container, criar um novo
    const componentContainer = doc.createElement('div')
    componentContainer.className = 'component-container'
    componentContainer.setAttribute('data-editable-element', 'true')
    
    const parent = gridElement.parentNode
    parent.insertBefore(componentContainer, gridElement)
    
    const contentWrapper = doc.createElement('div')
    contentWrapper.className = 'element-content'
    contentWrapper.appendChild(gridElement)
    
    componentContainer.appendChild(contentWrapper)
  })
  

  
  
  
  const elementsToWrap = container.querySelectorAll('h1, h2, h3, h4, h5, h6, p, img, div:not(.component-container)')
  
  elementsToWrap.forEach(element => {
    // 🛡️ SISTEMA CENTRALIZADO: usa a mesma lógica de proteção do EditorCanvas
    if (shouldIgnoreElementForWrapping(element)) {
      return
    }
    
    const componentContainer = doc.createElement('div')
    componentContainer.className = 'component-container'
    componentContainer.setAttribute('data-editable-element', 'true')
    
    if (['H1', 'H2', 'H3', 'H4', 'H5', 'H6', 'P'].includes(element.tagName)) {
      element.setAttribute('data-element-type', 'text')
    }
    
    element.setAttribute('data-element-id', 'el-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
    
    const parent = element.parentNode
    parent.insertBefore(componentContainer, element)
    
    const contentWrapper = doc.createElement('div')
    contentWrapper.className = 'element-content'
    contentWrapper.appendChild(element)
    
    componentContainer.appendChild(contentWrapper)
  })
  
  
  // 🚀 SISTEMA CENTRALIZADO: processamento automático de todos os componentes
  try {
    processComponentsAndInjectScripts(doc, authToken)

  } catch (error) {
    console.error('❌ Erro no processamento centralizado:', error)
    // Fallback para processamento básico se houver erro
  }
  
  // 📝 NOTA: Locations e Statements são processados automaticamente pelo sistema centralizado
  
  return container.innerHTML
}

const handleModeChange = (newMode) => {
  if (newMode === 'edit' || newMode === 'view') {
    mode.value = newMode
  } else {
    console.warn('Invalid mode value:', newMode)
  }
}

const handleViewportChange = (newSize) => {
  viewportSize.value = newSize
}

// ✅ CORREÇÃO: Handler para mudança de layout
const handleLayoutChange = async (layout) => {
  try {
    isLoading.value = true
    hasError.value = false
    
    // ✅ CORREÇÃO: Definir layout atual ANTES de carregar HTML
    currentLayout.value = layout
    
    // Limpar HTML atual para evitar problemas de cache
    initialHtml.value = ''
    
    // Atualizar o HTML baseado no layout selecionado
    await fetchLayoutHtml(layout)
    
    // Verificar se o HTML foi carregado corretamente
    if (!initialHtml.value || initialHtml.value.trim() === '') {
      await fetchInitialHtml()
    }
    
    // Reinicializar componentes no novo HTML
    setTimeout(() => {
      try {
        autoInitializeComponents(document)
        initializeUndoRedoSystem()
      } catch (error) {
        // Silencioso
      }
    }, 500)
    
  } catch (error) {
    hasError.value = true
    // Em caso de erro, tentar carregar o HTML inicial como fallback
    try {
      await fetchInitialHtml()
    } catch (fallbackError) {
      console.error('❌ [EditorView] Erro no fallback:', fallbackError)
      toast.error(t('layoutEditor.error.message'))
    }
  } finally {
    isLoading.value = false
  }
}

const getCurrentHtml = () => {
  const iframe = document.querySelector('#editor-frame')
  if (!iframe) return initialHtml.value
  
  const doc = iframe.contentDocument
  if (!doc) return initialHtml.value
  
  const html = doc.documentElement.outerHTML
  
  if (!html || html.length < 50) {
    console.warn('HTML vazio ou muito curto detectado, usando o HTML inicial')
    return initialHtml.value
  }
  
  return html
}

const processHtmlForSaving = async (htmlContent) => {
  const parser = new DOMParser()
  const doc = parser.parseFromString(htmlContent, 'text/html')
  
  // Importa sistema avançado
  const { 
    processElementForSaving, 
    getAllComponentSelectors,
    autoInitializeComponents 
  } = useComponentRegistry()

  // 🚀 SISTEMA AUTOMÁTICO: Processa TODOS os componentes automaticamente

  
  if (typeof getAllComponentSelectors === 'function') {
    const componentSelector = getAllComponentSelectors()

    
    if (componentSelector && componentSelector.length > 0) {
      const allComponents = doc.querySelectorAll(componentSelector)

      
      allComponents.forEach(element => {
        // Processa cada elemento automaticamente
        processElementForSaving(element, null)
      })
    } else {
      console.warn('⚠️ [processHtmlForSaving] Nenhum seletor de componente encontrado')
    }
  } else {

  }

  // 🚀 USAR SERVIÇO CENTRALIZADO: Sistema de injeção de scripts standalone
  try {
    const scriptService = createScriptInjectionService(authToken)
    await scriptService.processForSaving(doc)

  } catch (error) {
    console.error('❌ [processHtmlForSaving] Erro no serviço centralizado:', error)
  }

  // ✅ NOVO: Salva configurações da página no body
  const body = doc.body
  if (body && pageSettings.value) {

    
    // Configurações básicas
    if (pageSettings.value.backgroundColor) {
      body.setAttribute('data-page-background', pageSettings.value.backgroundColor)
    }
    if (pageSettings.value.contentWidth) {
      body.setAttribute('data-page-content-width', pageSettings.value.contentWidth.toString())
    }
    
    // Configurações da página (título, logo, favicon)
    if (pageSettings.value.pageTitle) {
      body.setAttribute('data-page-title', pageSettings.value.pageTitle)
    }
    if (pageSettings.value.pageLogo) {
      body.setAttribute('data-page-logo', pageSettings.value.pageLogo)
    }
    if (pageSettings.value.pageFavicon) {
      body.setAttribute('data-page-favicon', pageSettings.value.pageFavicon)
    }
    
    // Configurações de background
    if (pageSettings.value.backgroundType) {
      body.setAttribute('data-background-type', pageSettings.value.backgroundType)
      
      if (pageSettings.value.backgroundType === 'preset') {
        if (pageSettings.value.selectedPreset) {
          body.setAttribute('data-selected-preset', pageSettings.value.selectedPreset)
        }
        if (pageSettings.value.presetCss) {
          body.setAttribute('data-preset-css', pageSettings.value.presetCss)
        }
      } else if (pageSettings.value.backgroundType === 'gradient') {
        if (pageSettings.value.gradientType) {
          body.setAttribute('data-gradient-type', pageSettings.value.gradientType)
        }
        if (pageSettings.value.gradientAngle) {
          body.setAttribute('data-gradient-angle', pageSettings.value.gradientAngle.toString())
        }
        if (pageSettings.value.gradientStops) {
          body.setAttribute('data-gradient-stops', JSON.stringify(pageSettings.value.gradientStops))
        }
      } else if (pageSettings.value.backgroundType === 'image') {
        if (pageSettings.value.backgroundImage) {
          body.setAttribute('data-background-image', pageSettings.value.backgroundImage)
        }
        if (pageSettings.value.imageSize) {
          body.setAttribute('data-image-size', pageSettings.value.imageSize)
        }
        if (pageSettings.value.imagePosition) {
          body.setAttribute('data-image-position', pageSettings.value.imagePosition)
        }
      }
    }
    

  }

  // ✅ Inicializa automaticamente todos os componentes
  autoInitializeComponents(doc)

  return doc.documentElement.outerHTML
}

// Manipulador de atualização das configurações
const handleSettingsUpdate = (settings) => {
  pageSettings.value = { ...pageSettings.value, ...settings }
  
  // Salva estado após mudança de configurações (imediato)
  saveEditorStateImmediate('Configurações alteradas')
}

// === HANDLERS DO NOVO SISTEMA DE SIDEBAR ===

/**
 * Handler para seleção de elementos na sidebar
 */
const handleElementSelect = (element) => {


}

/**
 * Handler para ações da toolbar (agora integrada com sidebar)
 */
const handleToolbarAction = (action) => {

  
  // Processar ações específicas se necessário
  if (action.type === 'delete') {
    // Salvar estado após deleção
    saveEditorStateImmediate('Elemento removido')
  } else if (action.type === 'add-above' || action.type === 'add-below') {
    // Ações de adicionar serão tratadas pelo handleComponentSelect
  } else {
    // Outras ações são mudanças de propriedades
    // Estado será salvo pelos editores específicos
  }
}

/**
 * Handler para atualizações de elementos via sidebar
 */
const handleElementUpdated = (data) => {
  const { element, data: changeData, description, type } = data || {}
  
  // Determina o tipo de mudança e descrição apropriada
  const changeInfo = determineChangeType(changeData, description, type)
  
  // Salva o estado com configurações inteligentes baseadas no tipo
  saveEditorState(changeInfo.description, changeInfo.options)
}

/**
 * Determina o tipo de mudança e opções de salvamento baseado nos dados
 */
const determineChangeType = (changeData, description, type) => {
  if (!changeData) {
    return {
      description: description || 'Elemento atualizado',
      options: { immediate: false, debounceMs: 400 }
    }
  }

  // Se já tem uma descrição específica, usar ela
  if (description) {
    return {
      description,
      options: getDefaultSaveOptions(description)
    }
  }

  // Detecta tipo de mudança baseado nas propriedades alteradas
  const properties = Object.keys(changeData)
  
  // Mudanças de cor (devem usar debounce longo)
  const colorProperties = ['color', 'backgroundColor', 'titleColor', 'textColor', 'accentColor', 'iconColor', 'cardBackground', 'sectionBackground']
  if (properties.some(prop => colorProperties.some(colorProp => 
    prop.toLowerCase().includes(colorProp.toLowerCase()) || 
    prop.toLowerCase().includes('cor') ||
    prop.toLowerCase().includes('color')
  ))) {
    return {
      description: 'Cor alterada',
      options: { 
        immediate: false, 
        debounceMs: 800,
        debounceKey: 'color-changes'
      }
    }
  }

  // Mudanças de tamanho/espaçamento (debounce médio)
  const sizeProperties = ['fontSize', 'padding', 'margin', 'spacing', 'gap', 'width', 'height', 'size']
  if (properties.some(prop => sizeProperties.some(sizeProp => 
    prop.toLowerCase().includes(sizeProp.toLowerCase()) ||
    prop.toLowerCase().includes('tamanho') ||
    prop.toLowerCase().includes('espaco')
  ))) {
    return {
      description: 'Espaçamento alterado',
      options: { 
        immediate: false, 
        debounceMs: 600,
        debounceKey: 'spacing-changes'
      }
    }
  }

  // Mudanças de texto (debounce longo para typing contínuo)
  const textProperties = ['title', 'subtitle', 'text', 'content', 'description', 'label']
  if (properties.some(prop => textProperties.some(textProp => 
    prop.toLowerCase().includes(textProp.toLowerCase()) ||
    prop.toLowerCase().includes('titulo') ||
    prop.toLowerCase().includes('texto')
  ))) {
    return {
      description: 'Texto alterado',
      options: { 
        immediate: false, 
        debounceMs: 1000,
        debounceKey: 'text-changes'
      }
    }
  }

  // Mudanças de border/bordas (debounce médio)
  const borderProperties = ['border', 'borderRadius', 'borderColor', 'borderWidth']
  if (properties.some(prop => borderProperties.some(borderProp => 
    prop.toLowerCase().includes(borderProp.toLowerCase()) ||
    prop.toLowerCase().includes('borda')
  ))) {
    return {
      description: 'Bordas alteradas',
      options: { 
        immediate: false, 
        debounceMs: 600,
        debounceKey: 'border-changes'
      }
    }
  }

  // Mudanças estruturais/layout (imediato)
  const structuralProperties = ['layout', 'columns', 'grid', 'position', 'display', 'enabled', 'visible']
  if (properties.some(prop => structuralProperties.some(structProp => 
    prop.toLowerCase().includes(structProp.toLowerCase())
  ))) {
    return {
      description: 'Configurações alteradas',
      options: { immediate: true }
    }
  }

  // Adição/remoção de itens (imediato)
  if (Array.isArray(changeData) || 
      (typeof changeData === 'object' && 
       (changeData.hasOwnProperty('length') || 
        Object.keys(changeData).some(key => key.includes('item') || key.includes('list'))))) {
    return {
      description: 'Itens atualizados',
      options: { immediate: true }
    }
  }

  // Padrão: debounce curto
  return {
    description: 'Propriedades alteradas',
    options: { 
      immediate: false, 
      debounceMs: 400,
      debounceKey: 'general-changes'
    }
  }
}

/**
 * Handler para seleção de seções na sidebar
 */
const handleSectionSelect = (sectionData) => {
  try {
    // Simular seleção do elemento no canvas primeiro
    if (sectionData.element && editorCanvasRef.value && editorCanvasRef.value.handleElementSelect) {
      editorCanvasRef.value.handleElementSelect(sectionData.element)
    }
    
    // Abrir sidebar no modo de propriedades
    if (editorLayoutRef.value && editorLayoutRef.value.setActiveTab) {
      editorLayoutRef.value.setActiveTab('properties')
    }
    
  } catch (error) {
    console.error('❌ Erro ao selecionar seção:', error)
  }
}

/**
 * Handler para seleção de componentes na sidebar (biblioteca)
 */
const handleComponentSelect = async (componentData) => {
  // Verificar se temos acesso ao EditorCanvas
  if (!editorCanvasRef.value) {
    console.error('❌ EditorCanvas não disponível')
    return
  }
  
  // Adicionar componente sempre na segunda posição (após header)
  const iframe = document.querySelector('#editor-frame')
  if (!iframe || !iframe.contentDocument) {
    console.error('❌ Iframe não disponível')
    return
  }
  
  const newElement = await addComponentAfterHeader(iframe.contentDocument, componentData.type)
  
  // Salvar estado após adicionar componente
  saveEditorStateImmediate('Componente adicionado')
  
  // 🎯 IMPLEMENTAR REDIRECIONAMENTO AUTOMÁTICO
  if (newElement) {
    // 1. Scroll para o novo elemento no iframe
    scrollToElementInIframe(newElement)
    
    // 2. Selecionar automaticamente o elemento
    const elementToSelect = newElement.querySelector('.element-content > *')
    if (elementToSelect && editorCanvasRef.value && editorCanvasRef.value.handleElementSelect) {
      editorCanvasRef.value.handleElementSelect(elementToSelect)
    }
    
    // 3. Redirecionar para a tela de seções (não para o editor específico)
    setTimeout(() => {
      redirectToSectionsList(componentData.type)
    }, 300)
    
    // 4. Refresh da sidebar após um delay
    setTimeout(() => {
      if (editorLayoutRef.value && editorLayoutRef.value.refreshSidebar) {
        editorLayoutRef.value.refreshSidebar()
      }
    }, 500)
  }
}

/**
 * Adiciona componente sempre na segunda posição (após header)
 */
const addComponentAfterHeader = async (doc, componentType) => {
  try {
    // Buscar header
    const header = doc.querySelector('[data-component="header"]')
    if (!header) {
      console.error('❌ Header não encontrado')
      return null
    }
    
    // Buscar container do header
    const headerContainer = header.closest('.component-container')
    if (!headerContainer) {
      console.error('❌ Container do header não encontrado')
      return null
    }
    
    // Criar novo componente
    const newComponent = await createComponentInPosition(componentType, headerContainer, 'after')
    
    if (newComponent) {
      return newComponent
    }
    
    return null
    
  } catch (error) {
    console.error(`❌ Erro ao adicionar componente ${componentType}:`, error)
    return null
  }
}

/**
 * Faz scroll para um elemento específico no iframe
 */
const scrollToElementInIframe = (element) => {
  try {
    const iframe = document.querySelector('#editor-frame')
    if (!iframe || !element) return
    
    // Scroll suave para o elemento
    element.scrollIntoView({ 
      behavior: 'smooth', 
      block: 'center', 
      inline: 'nearest' 
    })
    
    // Destacar temporariamente o elemento
    element.style.outline = '3px solid #3b82f6'
    element.style.outlineOffset = '4px'
    element.style.transition = 'outline 0.3s ease'
    
      setTimeout(() => {
      element.style.outline = ''
      element.style.outlineOffset = ''
    }, 2000)
    
  } catch (error) {
    console.error('❌ Erro ao fazer scroll para elemento:', error)
  }
}

/**
 * Redireciona para a tela de seções da sidebar
 */
const redirectToSectionsList = (componentType) => {
  try {
    if (!editorLayoutRef.value) return
    
    // Abrir sidebar na tela de seções (home)
    if (editorLayoutRef.value.setActiveTab) {
      editorLayoutRef.value.setActiveTab('home')
    }
    
    
  } catch (error) {
    console.error('❌ Erro ao redirecionar para lista de seções:', error)
  }
  }

  /**
 * Cria componente em posição específica
 */
const createComponentInPosition = async (type, referenceContainer, position) => {
  try {
    const doc = referenceContainer.ownerDocument
    
    // Importar configurações do componente
    const { getConfigByType } = await import('@/components/layoutEditor/configs/index.js')
    const componentConfig = getConfigByType(type)
    
    if (!componentConfig) {
      console.error(`❌ Configuração não encontrada para: ${type}`)
      return null
    }
    
    // Criar container
    const container = doc.createElement('div')
    container.className = 'component-container'
    container.setAttribute('data-editable-element', 'true')
    container.setAttribute('data-element-id', 'el-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
    container.setAttribute('draggable', 'true')
    
    // Criar wrapper do conteúdo
    const contentWrapper = doc.createElement('div')
    contentWrapper.className = 'element-content'
    
    // Criar elemento do componente
    const tempDiv = doc.createElement('div')
    tempDiv.innerHTML = componentConfig.html
    const element = tempDiv.firstChild
    
    if (!element) {
      console.error(`❌ Falha ao criar elemento para: ${type}`)
      return null
    }
    
    contentWrapper.appendChild(element)
    container.appendChild(contentWrapper)
    
    // Inserir na posição correta
    const parent = referenceContainer.parentNode
    if (position === 'after') {
      if (referenceContainer.nextSibling) {
        parent.insertBefore(container, referenceContainer.nextSibling)
      } else {
        parent.appendChild(container)
      }
    } else {
      parent.insertBefore(container, referenceContainer)
    }
    
    // Configurar atributos do elemento
    if (element && typeof element.hasAttribute === 'function' && !element.hasAttribute('data-element-type')) {
      element.setAttribute('data-element-type', type)
    }
    
    // Configurar drag and drop se necessário
    if (editorCanvasRef.value && editorCanvasRef.value.setupDragAndDrop) {
      editorCanvasRef.value.setupDragAndDrop(container, doc, editorCanvasRef.value.handleElementSelect)
    }
    
    // Inicializar componente
    setTimeout(async () => {
      try {
       
      
        // Auto-inicialização geral
        const { autoInitializeComponents } = await import('@/composables/useComponentScriptInjection.js')
        if (autoInitializeComponents) {
          autoInitializeComponents(doc)
        }
      } catch (error) {
        console.error(`❌ Erro ao inicializar componente ${type}:`, error)
      }
    }, 100)
    
    return container
    
  } catch (error) {
    console.error(`❌ Erro ao criar componente ${type}:`, error)
    return null
  }
}

/**
 * Handler para atualizações das configurações da página via sidebar
 */
const handlePageSettingsUpdated = (settings) => {
    
  
  // Reutilizar o handler existente
  handleSettingsUpdate(settings)
  
  // Salvar estado para undo/redo (imediato pois são configurações estruturais)
  saveEditorStateImmediate('Configurações da página alteradas')
}

/**
 * Handler para exclusão de seções via sidebar (ANTES da exclusão)
 */
const handleSectionWillDelete = (section) => {
  // 🔄 CRÍTICO: Salvar estado ANTES da exclusão para permitir undo
  saveEditorStateImmediate(`Seção "${section.name}" removida`)
  

}



const saveEditorState = (description = '', options = {}) => {
  const iframe = document.querySelector('#editor-frame')
  if (iframe && editorCanvasRef.value) {
    const selectedElement = editorCanvasRef.value.selectedElement || null
    
    // Determina se deve usar debounce baseado no tipo de mudança
    const defaultOptions = getDefaultSaveOptions(description)
    const finalOptions = { ...defaultOptions, ...options }
    
    saveState(iframe, pageSettings.value, selectedElement, description, finalOptions)
  }
}

/**
 * Salva estado imediatamente (para ações pontuais)
 */
const saveEditorStateImmediate = (description = '') => {
  saveEditorState(description, { immediate: true })
}

/**
 * Determina opções padrão baseadas no tipo de mudança
 */
const getDefaultSaveOptions = (description) => {
  // Mudanças que devem usar debounce (contínuas)
  const debouncedChanges = [
    'Texto alterado',
    'Cor alterada',
    'Tamanho da fonte alterado',
    'Espaçamento alterado',
    'Bordas alteradas',
    'Header configurado',
    'Footer configurado'
  ]
  
  // Mudanças que devem ser imediatas (pontuais)
  const immediateChanges = [
    'Elemento removido',
    'Componente adicionado',
    'Imagem alterada',
    'Configurações alteradas'
  ]
  
  // Verifica se é mudança contínua (usa debounce mais longo)
  if (debouncedChanges.some(change => description.includes(change.split(' ')[0]))) {
    return { 
      immediate: false, 
      debounceMs: 800, // 800ms para mudanças contínuas
      debounceKey: description.split(' ')[0] // Agrupa por tipo de mudança
    }
  }
  
  // Verifica se é mudança imediata
  if (immediateChanges.some(change => description.includes(change.split(' ')[0]))) {
    return { immediate: true }
  }
  
  // Padrão: debounce curto
  return { 
    immediate: false, 
    debounceMs: 400 // 400ms para outras mudanças
  }
}

/**
 * Aplica as configurações da página (usado pelo undo/redo)
 */
const applyPageSettingsFromSnapshot = (settings) => {
  pageSettings.value = { ...settings }
}

/**
 * Função para reselecionar elemento (usado pelo undo/redo)
 */
const handleElementSelectFromSnapshot = (element) => {
  if (editorCanvasRef.value && editorCanvasRef.value.handleElementSelect) {
    editorCanvasRef.value.handleElementSelect(element)
  }
}

/**
 * Função para reinicializar o editor após undo/redo
 * CRÍTICO: Esta função reconecta todos os event listeners perdidos E atualiza a sidebar
 */
const reinitializeEditorAfterUndoRedo = () => {
  if (editorCanvasRef.value && editorCanvasRef.value.setupEditorEvents) {
    try {
      editorCanvasRef.value.setupEditorEvents()
    } catch (error) {
      console.error('[EditorView] Erro ao reinicializar editor:', error)
    }
  }
  
  // 🔄 CRITICAL: Atualizar sidebar de seções após undo/redo
  // Aguarda um pouco para o DOM do iframe se estabilizar
  setTimeout(() => {
    if (editorLayoutRef.value && editorLayoutRef.value.refreshSidebar) {
      try {
        editorLayoutRef.value.refreshSidebar()
      } catch (error) {
        console.error('[EditorView] Erro ao atualizar sidebar após undo/redo:', error)
      }
    }
  }, 100)
}

/**
 * Inicializa o sistema de undo/redo
 */
const initializeUndoRedoSystem = () => {
  const iframe = document.querySelector('#editor-frame')
  if (iframe && iframe.contentDocument) {
    // Inicializa com o estado atual
    initializeUndoRedo(iframe, pageSettings.value, 'Estado inicial')
    
    // Configura atalhos de teclado COM função de reinicialização
    keyboardShortcutsCleanup = setupKeyboardShortcuts(
      iframe, 
      applyPageSettingsFromSnapshot, 
      handleElementSelectFromSnapshot,
      reinitializeEditorAfterUndoRedo // NOVA FUNÇÃO DE REINICIALIZAÇÃO
    )
  }
}

/**
 * Executa undo
 */
const executeUndo = async () => {
  const iframe = document.querySelector('#editor-frame')
  if (iframe) {
    const snapshot = await undo(
      iframe, 
      applyPageSettingsFromSnapshot, 
      handleElementSelectFromSnapshot,
      reinitializeEditorAfterUndoRedo // ADICIONA REINICIALIZAÇÃO
    )
   
  }
}

/**
 * Executa redo
 */
const executeRedo = async () => {
  const iframe = document.querySelector('#editor-frame')
  if (iframe) {
    const snapshot = await redo(
      iframe, 
      applyPageSettingsFromSnapshot, 
      handleElementSelectFromSnapshot,
      reinitializeEditorAfterUndoRedo // ADICIONA REINICIALIZAÇÃO
    )
    
  }
}

const handleSaveHtml = async () => {
  // Força o salvamento de todos os estados pendentes antes de salvar
  flushPendingStates()

  // Usar getCleanHtml do EditorCanvas para obter HTML com estilos de produção corretos
  let currentHtml = ''
  
  if (editorCanvasRef.value && editorCanvasRef.value.getCleanHtml) {
    currentHtml = await editorCanvasRef.value.getCleanHtml()
  } else {
    // Fallback para o método antigo se getCleanHtml não estiver disponível
    currentHtml = getCurrentHtml()
  }
  
  if (!currentHtml || currentHtml.length < 50) {
    alert('Não é possível salvar um HTML vazio ou inválido. A operação foi cancelada.')
    
    // Notifica o header sobre o erro
    if (editorHeaderRef.value?.onSaveError) {
      editorHeaderRef.value.onSaveError()
    }
    
    return
  }
  
  currentHtml = await processHtmlForSaving(currentHtml)
  
  try {
    // ✅ CORREÇÃO: Lógica mais inteligente para determinar filename
    let filename = 'index.html'

    // Se tem layout específico definido, usar seu filename
    if (currentLayout.value?.filename && currentLayout.value.filename !== 'index.html') {
      filename = currentLayout.value.filename
    }
    // Se não tem layout definido, sempre usar index.html (página principal)

    // Convertendo o HTML para um arquivo para usar a nova API de upload
    const htmlBlob = new Blob([currentHtml], { type: 'text/html' })
    const formData = new FormData()
    formData.append('file', htmlBlob, filename)
    formData.append('environment', 'DEVELOP')

    // Importar deviceDetection para gerar headers de sessão
    const deviceDetection = await import('@/utils/deviceDetection')
    const deviceFingerprint = deviceDetection.default.generateDeviceFingerprint()

    const response = await fetch('http://localhost:8081/file-manager/upload', {
      method: 'POST',
      headers: {
        'Authorization': `Bearer ${authToken}`,
        'X-Device-Fingerprint': deviceFingerprint,
        'X-Request-ID': `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`,
        'X-Client-Version': '1.0.0',
        'X-Timestamp': new Date().toISOString()
      },
      body: formData
    })

    if (response.ok) {
    
      
      // Marca como salvo no sistema de undo/redo
      markAsSaved()
      
      // Notifica o header sobre o sucesso
      if (editorHeaderRef.value?.onSaveSuccess) {
        editorHeaderRef.value.onSaveSuccess()
      }

    } else {
      const errorText = await response.text()
      throw new Error(errorText)
    }
  } catch (error) {
    console.error('Erro ao salvar HTML:', error)

    // Verificar se o erro é relacionado à falta de storeId
    const errorMessage = error.message || error.toString()
    if (errorMessage.includes('UNAUTHORIZED') || errorMessage.includes('storeId')) {
      alert('Erro de autenticação: Token não contém informações da loja. Por favor, selecione uma loja novamente.')
    } else {
      alert('Falha ao salvar alterações. Por favor, tente novamente.')
    }

    // Notifica o header sobre o erro
    if (editorHeaderRef.value?.onSaveError) {
      editorHeaderRef.value.onSaveError()
    }
  }
}

const handleRetry = async () => {
  hasError.value = false
  isLoading.value = true
  try {
    await fetchInitialHtml()
  } catch (error) {
    console.error('Error retrying to fetch HTML:', error)
    hasError.value = true
  } finally {
    isLoading.value = false
  }
}

// Cleanup quando o componente for desmontado
onMounted(() => {
  return () => {
    // Força salvamento de estados pendentes
    flushPendingStates()
    
    // Limpa os atalhos de teclado
    if (keyboardShortcutsCleanup) {
      keyboardShortcutsCleanup()
      keyboardShortcutsCleanup = null
    }
  }
})

// Também força salvamento quando sai da página
window.addEventListener('beforeunload', () => {
  flushPendingStates()
})
</script>

<template>
  <Toast />
  <EditorHeader 
    ref="editorHeaderRef"
    :mode="mode" 
    :viewportSize="viewportSize"
    :canUndo="canUndo"
    :canRedo="canRedo"
    @mode-change="handleModeChange"
    @viewport-change="handleViewportChange"
    @save-html="handleSaveHtml"
    @settings-update="handleSettingsUpdate"
    @undo="executeUndo"
    @redo="executeRedo"
    @layout-change="handleLayoutChange"
  />
  
  <!-- Novo layout com sidebar -->
  <EditorLayout 
    ref="editorLayoutRef"
    :mode="mode"
    :layoutType="currentLayoutType"
    @element-select="handleElementSelect"
    @toolbar-action="handleToolbarAction"
    @element-updated="handleElementUpdated"
    @component-select="handleComponentSelect"
    @page-settings-updated="handlePageSettingsUpdated"
    @section-will-delete="handleSectionWillDelete"
  >
    <template #canvas="{ onElementSelect, onToolbarAction }">
      <EditorCanvas 
        ref="editorCanvasRef"
        :mode="mode" 
        :viewportSize="viewportSize"
        :settings="pageSettings"
        :is-loading="isLoading"
        :has-error="hasError"
        :saveEditorState="saveEditorState"
        :onElementSelect="onElementSelect"
        :onToolbarAction="onToolbarAction"
        @retry="handleRetry"
      />
    </template>
  </EditorLayout>
</template>

<style scoped>
.editor-action-button {
  position: absolute;
  z-index: 10;
  width: 24px;
  height: 24px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 50%;
  background: #3b82f6;
  color: white;
  font-size: 16px;
  cursor: pointer;
  opacity: 0;
  transition: all 0.2s ease;
  border: 2px solid white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.add-button {
  left: 50%;
  transform: translateX(-50%);
}

.add-button-top {
  top: -12px;
}

.add-button-bottom {
  bottom: -12px;
}

.delete-button {
  top: 8px;
  right: 8px;
  background: #ef4444;
}

.component-container {
  position: relative;
  transition: all 0.2s ease;
}

.component-container:hover .editor-action-button {
  opacity: 1;
}

.component-container:hover {
  outline: 1px dashed #3b82f6;
  outline-offset: 2px;
}

.editor-action-button:hover {
  transform: translateX(-50%) scale(1.1);
  box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

.delete-button:hover {
  transform: scale(1.1);
  background: #dc2626;
}

.element-content {
  position: relative;
}

:root {
  font-family: Inter, system-ui, Avenir, Helvetica, Arial, sans-serif;
  line-height: 1.5;
  font-weight: 400;

  color-scheme: light dark;
  color: rgba(0, 0, 0, 0.87);
  font-synthesis: none;
  text-rendering: optimizeLegibility;
  -webkit-font-smoothing: antialiased;
  -moz-osx-font-smoothing: grayscale;
}
</style>
