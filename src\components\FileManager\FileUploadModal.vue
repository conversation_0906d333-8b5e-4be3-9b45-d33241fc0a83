<template>
  <IluriaModal
    v-model:visible="localVisible"
    :header="t('fileManager.uploadFiles')"
    :modal="true"
    :dismissableMask="true"
    class="w-full max-w-4xl"
    :contentStyle="{ padding: '0' }"
  >
    <div class="p-4">
      <div class="flex flex-col h-full">
        <div class="max-w-3xl mx-auto w-full bg-white rounded-lg">
          <div 
            @dragover.prevent="isDragging = true"
            @dragleave.prevent="isDragging = false"
            @drop="handleDrop"
            :class="[
              'border-2 border-dashed rounded-lg p-8 text-center transition-all',
              isDragging 
                ? `bg-${environmentColor}-100 border-${environmentColor}-400` 
                : 'bg-gray-50 border-gray-300'
            ]"
          >
            <div class="flex flex-col items-center">
              <HugeiconsIcon :icon="CloudUploadIcon" class="text-5xl mb-4 text-gray-400" />
              
              <h3 class="text-lg font-medium text-gray-800 mb-2">
                {{ t('fileManager.chooseFile') }}
              </h3>
              
              <p class="text-sm text-gray-500 mb-4">
                {{ t('fileManager.anyFileType') }}
              </p>
              
              <div class="flex items-center mb-4">
                <input 
                  type="checkbox" 
                  id="directory-upload"
                  v-model="directoryUpload"
                  class="mr-2 h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                >
                <label for="directory-upload" class="text-sm text-gray-600">
                  {{ t('fileManager.preservesDirectoryStructure') }}
                </label>
              </div>
              
              <p v-if="currentFolderId" class="text-xs text-gray-500 mb-2">
                {{ t('fileManager.filesUploadedToCurrentFolder') }}
              </p>
              
              <label 
                :class="[
                  'inline-flex items-center justify-center px-4 py-2 rounded-md text-sm font-medium transition-colors',
                  `bg-${environmentColor}-600 hover:bg-${environmentColor}-700 text-white`
                ]"
              >
                <input 
                  type="file" 
                  :multiple="!directoryUpload"
                  :webkitdirectory="directoryUpload"
                  :directory="directoryUpload"
                  class="hidden" 
                  @change="handleFileInput"
                  :accept="directoryUpload ? '' : acceptedFileTypes.join(',')"
                >
                  <span class="text-xs text-gray-500">{{ t('fileManager.clickToBrowse') }}</span>
                </label>
            </div>
          </div>
          
          <div v-if="files.length > 0" class="mt-6">
            <h3 class="text-md font-medium text-gray-800 mb-3">{{ t('fileManager.selectedFiles') }}</h3>
            
            <div class="space-y-3 max-h-64 overflow-y-auto">
              <div 
                v-for="file in files" 
                :key="file.name"
                class="bg-gray-50 rounded-lg p-3 flex items-center"
              >
                <div class="flex items-center space-x-3">
                  <HugeiconsIcon :icon="getFileIcon(file)" class="text-xl text-gray-500" />
                </div>
                
                <div class="flex-1 min-w-0 ml-3">
                  <div>
                    <p class="text-sm font-medium text-gray-800 truncate" :title="file.name">{{ file.name }}</p>
                    <p class="text-xs text-gray-500">{{ formatFileSize(file.size) }}</p>
                  </div>
                </div>
                
                <div class="flex items-center">
                  <span 
                    v-if="uploadStatus[file.name]" 
                    :class="[
                      'flex items-center mx-3',
                      uploadStatus[file.name] === 'completed' ? 'text-green-500' :
                      uploadStatus[file.name] === 'error' ? 'text-red-500' : `text-${environmentColor}-600`
                    ]"
                  >
                    <HugeiconsIcon 
                      v-if="uploadStatus[file.name] === 'completed'"
                      :icon="CheckmarkCircle02Icon" 
                      class="mr-1" 
                    />
                    <HugeiconsIcon 
                      v-else-if="uploadStatus[file.name] === 'error'"
                      :icon="CancelCircleIcon" 
                      class="mr-1" 
                    />
                    <HugeiconsIcon 
                      v-else
                      :icon="CircleIcon" 
                      class="mr-1 animate-spin" 
                    />
                    <span>
                      {{ 
                        uploadStatus[file.name] === 'completed' ? t('fileManager.completed') :
                        uploadStatus[file.name] === 'error' ? t('fileManager.error') : t('fileManager.uploading')
                      }}
                    </span>
                  </span>
                  
                  <div 
                    v-if="uploadStatus[file.name] === 'uploading'"
                    class="w-24 bg-gray-200 rounded-full h-1.5 mx-2"
                  >
                    <div 
                      class="h-1.5 rounded-full transition-all duration-300" 
                      :class="`bg-${environmentColor}-600`"
                      :style="{ width: (uploadProgress[file.name] || 0) + '%' }"
                    ></div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    
    <template #footer>
      <div class="flex justify-end gap-3 p-4 border-t border-gray-200">
        <IluriaButton 
          @click="completeUpload" 
          :disabled="!hasCompletedUploads"
        >
          {{ t('fileManager.done') }}
        </IluriaButton>
      </div>
    </template>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { HugeiconsIcon } from '@hugeicons/vue';
import { CloudUploadIcon, Delete01Icon, CheckmarkCircle02Icon, CancelCircleIcon,
  CircleIcon, Image01Icon, Pdf01Icon, Video01Icon, Doc01Icon, File01Icon } from '@hugeicons-pro/core-bulk-rounded';
import fileManagerService from '@/services/fileManager.service.js';
import { useI18n } from 'vue-i18n';
import IluriaModal from '@/components/Iluria/IluriaModal.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';

const { t } = useI18n();

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  environment: {
    type: String,
    required: true
  },
  currentFolder: {
    type: Object,
    default: () => null
  }
});

const emit = defineEmits(['update:visible', 'upload-complete']);

const localVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
});

const directoryUpload = ref(false);

const currentFolderId = computed(() => {
  return props.currentFolder?.id || null;
});

const environmentColor = computed(() => {
  return props.environment === 'DEVELOP' ? 'develop' : 'production';
});

const isDragging = ref(false);
const files = ref([]);
const uploadProgress = ref({});
const uploadStatus = ref({});

const hasCompletedUploads = computed(() => {
  return Object.values(uploadStatus.value).some(status => status === 'completed');
});

const acceptedFileTypes = [
  'image/*',
  'application/pdf',
  'application/msword',
  'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
  'application/vnd.ms-excel',
  'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
  'text/plain'
];

const handleDrop = (e) => {
  e.preventDefault();
  isDragging.value = false;
  
  if (e.dataTransfer?.files) {
    handleFiles(e.dataTransfer.files);
  }
};

const handleFileInput = async (e) => {
  const input = e.target;
  if (!input.files || input.files.length === 0) return;
  
  try {
    let targetFolderId = currentFolderId.value;
    if (directoryUpload.value) {
      await processDirectoryFiles(input.files, targetFolderId);
    } else {
      await handleFiles(input.files);
    }
  } catch (error) {
    console.error('Error handling file input:', error);
  } finally {
    input.value = '';
  }
};

const processDirectoryFiles = async (fileList, targetFolderId = null) => {
  const files = Array.from(fileList);
  
  if (files[0]?.webkitRelativePath) {
    const directoryMap = new Map();
    const folderIdCache = new Map();
    
    for (const file of files) {
      const path = file.webkitRelativePath || '';
      const pathParts = path.split('/');
      const fileName = pathParts.pop() || file.name;
      const directoryPath = pathParts.join('/');
      
      if (!directoryMap.has(directoryPath)) {
        directoryMap.set(directoryPath, []);
      }
      
      const fileWithPath = new File([file], file.name, { type: file.type });
      fileWithPath.relativePath = path;
      directoryMap.get(directoryPath)?.push(fileWithPath);
    }
    
    for (const dirPath of directoryMap.keys()) {
      if (dirPath) {
        try {
          const folderId = await createDirectoryStructure(dirPath, targetFolderId);
          if (folderId) {
            folderIdCache.set(dirPath, folderId);
          }
        } catch (error) {
          console.error(`Error creating directory structure for ${dirPath}:`, error);
        }
      }
    }
    
    for (const [dirPath, dirFiles] of directoryMap.entries()) {
      try {
        const parentFolderId = dirPath ? folderIdCache.get(dirPath) : targetFolderId;
        
        for (const file of dirFiles) {
          try {
            await uploadFile(file, '', parentFolderId);
          } catch (error) {
            console.error(`Error uploading file ${file.name}:`, error);
          }
        }
      } catch (error) {
        console.error(`Error processing directory ${dirPath}:`, error);
      }
    }
  } else {
    for (const file of files) {
      try {
        await uploadFile(file, '', targetFolderId);
      } catch (error) {
        console.error(`Error uploading file ${file.name}:`, error);
      }
    }
  }
};

const createDirectoryStructure = async (path, targetFolderId = null) => {
  if (!path) return targetFolderId;
  
  const parts = path.split('/').filter(Boolean);
  let currentPath = '';
  let lastFolderId = targetFolderId || null;
  
  for (const part of parts) {
    currentPath = currentPath ? `${currentPath}/${part}` : part;
    
    try {
      const exists = await checkDirectoryExists(part, lastFolderId);
      
      if (!exists) {
        const newFolder = await fileManagerService.createFolder({
          name: part,
          parentFolderId: lastFolderId,
          environment: props.environment
        });
        
        if (newFolder && newFolder.id) {
          lastFolderId = newFolder.id;
        }
      } else {
        try {
          const contents = await fileManagerService.getById(props.environment, lastFolderId);
          const folder = Array.isArray(contents) 
            ? contents.find(item => item.name === part && item.type === 'FOLDER')
            : null;
            
          if (folder && folder.id) {
            lastFolderId = folder.id;
          } else {
            const newFolder = await fileManagerService.createFolder({
              name: part,
              parentFolderId: lastFolderId,
              environment: props.environment
            });
            
            if (newFolder && newFolder.id) {
              lastFolderId = newFolder.id;
            }
          }
        } catch (error) {
          console.error(`Error getting folder ${part}:`, error);
          try {
            const newFolder = await fileManagerService.createFolder({
              name: part,
              parentFolderId: lastFolderId,
              environment: props.environment
            });
            
            if (newFolder && newFolder.id) {
              lastFolderId = newFolder.id;
            }
          } catch (createError) {
            console.error(`Error creating folder ${part} after failed check:`, createError);
            throw createError;
          }
        }
      }
    } catch (error) {
      console.error(`Error creating directory ${currentPath}:`, error);
      throw error;
    }
  }
  
  return lastFolderId;
};

const checkDirectoryExists = async (folderName, parentId) => {
  try {
    const contents = await fileManagerService.getById(props.environment, parentId);
    
    return Array.isArray(contents) && contents.some((item) => 
      item.name === folderName && item.type === 'FOLDER'
    );
  } catch (error) {
    console.error(`Error checking if directory exists:`, error);
    return false;
  }
};

const handleFiles = async (fileList) => {
  const fileArray = Array.from(fileList);
  
  for (const file of fileArray) {
    if (files.value.some(f => f.name === file.name && f.size === file.size)) {
      continue;
    }
    
    files.value.push(file);
    
    uploadFile(file, '', currentFolderId.value).catch(err => {
      console.error(`Erro ao fazer upload do arquivo ${file.name}:`, err);
    });
  }
};

const uploadFile = async (file, relativePath = '', targetFolderId = null) => {
  const displayName = file.name;
  
  try {
    let parentFolderId = targetFolderId || currentFolderId.value;
    
    if (!targetFolderId && relativePath) {
      const pathParts = relativePath.split('/').filter(Boolean);
      if (pathParts[pathParts.length - 1] === file.name) {
        pathParts.pop();
      }
      
      if (pathParts.length > 0) {
        const folderPath = pathParts.join('/');
        const folderId = await createDirectoryStructure(folderPath, null);
        if (folderId) {
          parentFolderId = folderId;
        }
      }
    }
    
    uploadProgress.value[displayName] = 0;
    uploadStatus.value[displayName] = 'uploading';
    
    if (!files.value.some(f => f === file)) {
      files.value.push(file);
    }
    
    const config = {
      onUploadProgress: (progressEvent) => {
        if (progressEvent.lengthComputable) {
          const percentCompleted = Math.round((progressEvent.loaded * 100) / progressEvent.total);
          uploadProgress.value[displayName] = percentCompleted;
        }
      }
    };
    
    const response = await fileManagerService.uploadFile(
      file, 
      parentFolderId, 
      props.environment,
      '', 
      config
    );
    
    uploadProgress.value[displayName] = 100;
    uploadStatus.value[displayName] = 'completed';
    
    return response;
  } catch (error) {
    console.error('Upload error:', error);
    uploadProgress.value[displayName] = 100;
    uploadStatus.value[displayName] = 'error';
    throw error;
  }
};

const removeFile = (fileToRemove) => {
  files.value = files.value.filter(f => f !== fileToRemove);
  delete uploadProgress.value[fileToRemove.name];
  delete uploadStatus.value[fileToRemove.name];
};

const getFileIcon = (file) => {
  const type = file.type;
  
  if (type.startsWith('image/')) {
    return Image01Icon;
  } else if (type === 'application/pdf') {
    return Pdf01Icon;
  } else if (type.startsWith('video/')) {
    return Video01Icon;
  } else if (type.startsWith('text/')) {
    return Doc01Icon;
  } else {
    return File01Icon;
  }
};

const formatFileSize = (bytes) => {
  if (bytes < 1024) {
    return `${bytes} B`;
  } else if (bytes < 1024 * 1024) {
    return `${(bytes / 1024).toFixed(0)} KB`;
  } else {
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  }
};

const closeModal = () => {
  if (Object.values(uploadStatus.value).some(status => status === 'uploading')) {
    if (confirm(t('fileManager.confirmCancelUploads'))) {
      localVisible.value = false;
      resetUploadState();
    }
  } else {
    localVisible.value = false;
    resetUploadState();
  }
};

const resetUploadState = () => {
  files.value = [];
  uploadProgress.value = {};
  uploadStatus.value = {};
  directoryUpload.value = false;
};

const completeUpload = () => {
  emit('upload-complete');
  localVisible.value = false;
  resetUploadState();
};

watch(() => localVisible.value, (newValue) => {
  if (newValue) {
    resetUploadState();
  }
});
</script>

<style scoped>
.upload-area {
  transition: all 0.2s ease;
}

.upload-area:hover {
  background-color: rgba(0, 0, 0, 0.02);
}
</style>
