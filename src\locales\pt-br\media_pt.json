{"title": "Biblioteca de Mídia", "library": "Biblioteca", "addNew": "Adicionar <PERSON>", "uploadFiles": "Enviar Arqui<PERSON>", "mediaLibrary": "Biblioteca de Mídia", "searchMedia": "Buscar mídia...", "noItems": "Nenhum item de mídia encontrado", "allMediaItems": "Todos os Itens de Mídia", "allDates": "<PERSON><PERSON> as Datas", "filter": "Filtrar", "listView": "Visualização em Lista", "gridView": "Visualização em Grade", "sortBy": "Ordenar Por", "newest": "<PERSON><PERSON>", "oldest": "<PERSON><PERSON>", "name": "Nome", "date": "Data", "fileSize": "Tamanho do Arquivo", "dimensions": "Dimensões", "type": "Tipo", "allTypes": "Todos os Tipos", "images": "Imagens", "videos": "Vídeos", "documents": "Documentos", "audio": "<PERSON><PERSON><PERSON>", "archives": "<PERSON>r<PERSON><PERSON>", "other": "Outros", "mediaItems": "{{count}} itens", "mediaItemSelected": "{{count}} se<PERSON><PERSON><PERSON>(s)", "deleteSelected": "Excluir Selecionados", "deleteConfirm": "Tem certeza que deseja excluir permanentemente os itens selecionados?", "deleteSuccess": "Itens selecionados foram excluídos com sucesso", "deleteError": "Erro ao excluir itens", "uploadProgress": "Enviando {{fileName}} ({{progress}}%)", "uploadComplete": "Upload concluído", "uploadError": "Erro ao enviar arquivo", "fileTooLarge": "Arquivo muito grande. Tamanho máximo: {{maxSize}}MB.", "invalidFileType": "Tipo de arquivo não permitido.", "maxFilesExceeded": "Você só pode enviar {{maxFiles}} arquivos por vez.", "dragAndDrop": "<PERSON><PERSON>ste arquivos para cá ou clique para enviar", "or": "ou", "browseFiles": "<PERSON><PERSON><PERSON>", "fileInfo": {"name": "Nome", "fileUrl": "URL do Arquivo", "fileSize": "Tamanho do Arquivo", "fileType": "Tipo de Arquivo", "dimensions": "Dimensões", "uploadedOn": "Enviado em", "modifiedOn": "Modificado em", "altText": "Texto Alternativo", "title": "<PERSON><PERSON><PERSON><PERSON>", "caption": "<PERSON>a", "description": "Descrição", "copyUrl": "Copiar URL", "editImage": "<PERSON><PERSON>", "download": "Baixar", "delete": "Excluir", "close": "<PERSON><PERSON><PERSON>", "saveChanges": "<PERSON><PERSON>", "saving": "Salvando...", "saved": "Salvo", "error": "Erro ao salvar alterações"}, "imageEditor": {"title": "<PERSON><PERSON>", "crop": "Cortar", "rotate": "<PERSON><PERSON><PERSON>", "flip": "<PERSON><PERSON><PERSON>", "brightness": "Bril<PERSON>", "contrast": "Contraste", "saturation": "Saturação", "blur": "Desfoque", "sharpen": "<PERSON><PERSON><PERSON>", "grayscale": "E<PERSON>la de Cinza", "sepia": "Sépia", "invert": "Inverter Cores", "reset": "Redefinir", "apply": "Aplicar", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON>", "saving": "Salvando...", "saved": "Imagem salva com sucesso", "error": "Erro ao salvar imagem"}, "fileTypes": {"image": "Imagens", "video": "Vídeos", "audio": "<PERSON><PERSON><PERSON>", "document": "Documentos", "spreadsheet": "<PERSON>il<PERSON>", "presentation": "Apresentações", "archive": "<PERSON>r<PERSON><PERSON>", "code": "Có<PERSON><PERSON>", "other": "Outros"}, "fileIcons": {"image": "Imagem", "video": "Vídeo", "audio": "<PERSON><PERSON><PERSON>", "document": "Documento", "pdf": "PDF", "spreadsheet": "<PERSON><PERSON><PERSON>", "presentation": "Apresentação", "archive": "Arquivo", "code": "Código", "other": "Arquivo"}, "fileActions": {"view": "Visualizar", "edit": "<PERSON><PERSON>", "download": "Baixar", "copyLink": "Copiar Link", "delete": "Excluir", "select": "Selecionar", "deselect": "<PERSON><PERSON><PERSON>"}, "bulkActions": {"download": "Baixar Selecionados", "delete": "Excluir Selecionados", "deleteConfirm": "Tem certeza que deseja excluir os itens selecionados?", "deleteSuccess": "Itens selecionados foram excluídos", "deleteError": "Erro ao excluir itens"}, "uploader": {"title": "Enviar Arqui<PERSON>", "subtitle": "Arraste e solte arquivos aqui ou clique para procurar", "browse": "<PERSON><PERSON><PERSON>", "or": "ou", "maxFileSize": "<PERSON><PERSON><PERSON> máximo: {{size}}MB", "allowedFileTypes": "Tipos permitidos: {{types}}", "uploading": "Enviando...", "uploaded": "Enviado", "failed": "<PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "retry": "Tentar Novamente", "done": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "error": {"tooManyFiles": "Muitos arquivos. O máximo permitido é {{maxFiles}}.", "fileTooLarge": "O arquivo '{{name}}' é muito grande. Tam<PERSON><PERSON> máximo: {{maxSize}}MB.", "invalidType": "O arquivo '{{name}}' tem um tipo inválido.", "unknown": "Ocorreu um erro ao enviar '{{name}}'."}}, "fileDetails": {"title": "Detalhes do Arquivo", "name": "Nome", "url": "URL", "size": "<PERSON><PERSON><PERSON>", "type": "Tipo", "dimensions": "Dimensões", "uploaded": "Enviado", "modified": "Modificado", "altText": "Texto Alternativo", "titleField": "<PERSON><PERSON><PERSON><PERSON>", "caption": "<PERSON>a", "description": "Descrição", "copyUrl": "Copiar URL", "copySuccess": "URL copiada para a área de transferência", "copyError": "Erro ao copiar URL", "save": "<PERSON><PERSON>", "saving": "Salvando...", "saved": "Salvo", "error": "Erro ao salvar alterações"}, "filePreview": {"unsupported": "Visualização não disponível para este tipo de arquivo.", "download": "Baixar"}, "fileList": {"name": "Nome", "size": "<PERSON><PERSON><PERSON>", "type": "Tipo", "date": "Data", "actions": "Ações"}, "folderTree": {"title": "Pastas", "createFolder": "Nova Pasta", "renameFolder": "Renomear Pasta", "deleteFolder": "Excluir <PERSON>", "folderName": "<PERSON>me da Pasta", "create": "<PERSON><PERSON><PERSON>", "rename": "Renomear", "delete": "Excluir", "cancel": "<PERSON><PERSON><PERSON>", "deleteConfirm": "Tem certeza que deseja excluir esta pasta?", "deleteSuccess": "Pasta excluída com sucesso", "deleteError": "Erro ao excluir pasta", "createSuccess": "Pasta criada com sucesso", "createError": "Erro ao criar pasta", "renameSuccess": "Pasta renomeada com sucesso", "renameError": "Erro ao renomear pasta"}}