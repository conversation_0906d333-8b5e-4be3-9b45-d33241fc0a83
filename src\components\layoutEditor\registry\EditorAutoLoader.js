/**
 * Sistema de inicialização de editores
 * Agora os editores funcionam diretamente como componentes Vue
 */

let isInitialized = false

/**
 * Inicializa o sistema de editores
 */
export async function initializeEditorSystem() {
  if (isInitialized) {
    return Promise.resolve()
  }
  
  try {
    isInitialized = true
    return Promise.resolve()
  } catch (error) {
    console.error('Erro ao inicializar sistema de editores:', error)
    isInitialized = false
    return Promise.reject(error)
  }
}

/**
 * Verifica se o sistema está inicializado
 */
export function isEditorSystemInitialized() {
  return isInitialized
}

/**
 * Força reinicialização do sistema
 */
export function resetEditorSystem() {
  isInitialized = false
} 