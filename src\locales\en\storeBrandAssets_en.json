{"title": "Brand Assets", "subtitle": "Configure your store's logo and favicon", "saveButton": "Save Changes", "cancelButton": "Cancel", "logoSection": {"title": "Store Logo", "subtitle": "Upload your store's main logo", "uploadButton": "Click to upload", "changeButton": "Change Logo", "removeButton": "Remove", "preview": "Logo preview will appear here", "formats": "PNG, JPG, SVG up to 2MB", "recommendations": "Recommendations:", "dimensionsText": "Dimensions: 200×80px or similar", "backgroundText": "Transparent background (PNG/SVG)", "retinaText": "High resolution for retina displays"}, "faviconSection": {"title": "Favicon", "subtitle": "Icon that appears in the browser tab", "uploadButton": "Click to upload", "changeButton": "Change Favicon", "removeButton": "Remove", "preview": "My Store - Browser", "formats": "ICO, PNG up to 1MB", "recommendations": "Recommendations:", "dimensionsText": "Dimensions: 32×32px or 16×16px", "formatText": "ICO format preferred", "designText": "Simple and recognizable design"}, "tips": {"title": "Tips for better results", "transparentLogo": "Use logos with transparent background for better integration", "testFavicon": "Test favicon in different browsers", "consistentDesign": "Maintain consistent design between favicon and logo"}, "messages": {"loadError": "Error loading brand settings", "saveSuccess": "Setting<PERSON> saved successfully!", "saveError": "Error saving settings", "logoUploadSuccess": "Logo uploaded successfully!", "logoUploadError": "Error uploading logo", "logoDeleteSuccess": "Logo removed successfully!", "logoDeleteError": "Error removing logo", "faviconUploadSuccess": "Favicon uploaded successfully!", "faviconUploadError": "Error uploading favicon", "faviconDeleteSuccess": "Favicon removed successfully!", "faviconDeleteError": "Error removing favicon", "logoTooLarge": "Logo too large. Maximum 2MB allowed", "faviconTooLarge": "Favicon too large. Maximum 1MB allowed", "invalidLogoFormat": "Invalid format. Use PNG, JPG or SVG for logo", "invalidFaviconFormat": "Invalid format. Use ICO, PNG or JPG for favicon", "networkError": "Connection error. Check your internet", "serverError": "Server error. Try again later"}}