<template>
  <ViewContainer
    :title="t('userSettings.sections.billing.paymentMethods')"
    :icon="CreditCardIcon"
    iconColor="blue"
  >
    <!-- Conteúdo será implementado aqui -->
  </ViewContainer>
</template>

<script setup>
import { useI18n } from 'vue-i18n'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { CreditCardIcon } from '@hugeicons-pro/core-stroke-standard'

// Composables
const { t } = useI18n()
</script>

<style scoped>
/* Estilos serão implementados aqui */
</style>
