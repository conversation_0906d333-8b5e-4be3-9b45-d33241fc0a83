<template>
    <span class="tooltip-icon" v-tooltip="props.text">
        <HugeiconsIcon v-if="icon" :icon="icon" :size="18" class="mr-1 inline mb-0.5 icon" />
    </span>
</template>

<script setup>
import { HelpCircleIcon } from '@hugeicons-pro/core-bulk-rounded';
import { HugeiconsIcon } from '@hugeicons/vue';


const props = defineProps({
    text: {
        type: String,
        required: true
    },
    icon: {
        type: Object,
        default: HelpCircleIcon
    },
    position: {
        type: String,
        default: 'right'
    }
})

</script>
