<template>
  <LoginPage>
    <form @submit.prevent="requestPasswordRecovery">
      <div class="mb-4">
        <IluriaInputText
          id="userEmail"
          v-model="email"
          type="email"
          :label="$t('login.forgetPassword')"
          :placeholder="$t('login.enterEmail')"
          required
        />
      </div>

      <div id="login-button-container">
        <IluriaButton type="submit" :disabled="loading">
          {{ loading ? $t("login.sending") : $t("login.send") }}
        </IluriaButton>
      </div>
    </form>

    <!-- Mensagens de Status -->
    <div v-if="passwordRecoveryStatus === 'success'" class="mt-4 p-3 bg-green-100 text-green-800 rounded-md">
      {{ $t("login.recoveryEmailSent") }}
    </div>
    <div v-if="passwordRecoveryStatus === 'error'" class="mt-4 p-3 bg-red-100 text-red-800 rounded-md">
      {{ $t("login.errorSendingRecoveryEmail") }}
    </div>
  </LoginPage>
</template>


<script setup>
import { ref } from 'vue';
import { useToast } from '@/services/toast.service';
import { useAuthStore } from '@/stores/auth.store'; 
import { useI18n } from 'vue-i18n';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import LoginPage from '@/components/login/LoginPage.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { addToast } = useToast();
const authStore = useAuthStore();
const { t } = useI18n();

const email = ref('');
const loading = ref(false);
const passwordRecoveryStatus = ref(null);

const requestPasswordRecovery = async () => {
  if (!email.value) return;

  loading.value = true;

  try {
    const response = await authStore.forgotPassword(email.value);

    if (response.success) {
      passwordRecoveryStatus.value = 'success';
      addToast(t("passwordRecovery.EmailSent"), 'success'); 
    } else {
      passwordRecoveryStatus.value = 'error';
      addToast(t("passwordRecovery.UnknownError"), 'error'); 
    }
  } catch (error) {
    passwordRecoveryStatus.value = 'error';
    addToast(t("passwordRecovery.UnknownError"), 'error'); 
  } finally {
    loading.value = false;
  }
};
</script>


<style scoped>
.left-column {
  background-color: white;
}

.right-column {
  background-color: #d6c3ae;
  background-image: url("@/assets/img/login/login-splash-4.jpg");
  background-size: cover;
  background-repeat: no-repeat;
  background-position: 75%;
}

.left-column-inner-container {
  margin: 25px;
  max-width: 400px;
}

.logo-iluria {
  display: block;
  margin: 0 auto;
  max-width: 250px;
  height: auto;
  margin-bottom: 2rem;
}

#login-button-container {
  height: 46px;
  overflow: hidden;
  margin-top: 30px;
  margin-bottom: 40px;
}

button {
  border: 1px solid transparent;
  border-radius: 8px;
  padding: 10px 16px;
  outline: none;
  width: 100%;
}
</style>
