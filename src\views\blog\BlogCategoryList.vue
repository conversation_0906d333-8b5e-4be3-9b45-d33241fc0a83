<template>
  <div class="blog-category-list-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('blogCategory.title')"
      :subtitle="t('blogCategory.subtitle')"
      :showAdd="!isReorderMode"
      :addText="t('blogCategory.newCategory')"
      :customButtons="getHeaderButtons()"
      @add-click="goToAddCategory"
      @custom-click="handleCustomButtonClick"
    />

    <!-- Content -->
    <div class="list-content">
      <!-- Categories Table Container with integrated search -->

      <!-- Categories Table Container -->
      <ViewContainer 
        :title="t('blogCategory.titleView')"
        :icon="BookmarkAdd01Icon"
        iconColor="blue"
      >
        <!-- Integrated Search Filter -->
        <div v-if="hasCategories" class="search-section">
          <div class="search-container">
            <div class="search-input-wrapper">
              <div class="search-icon">
                <HugeiconsIcon :icon="Search01Icon" size="16" />
              </div>
              <IluriaInputText
                id="search-category"
                type="text"
                v-model="filters.filter"
                :placeholder="t('blogCategory.searchPlaceholder')"
                @input="debouncedSearch"
                class="search-input"
              />
            </div>
            <div class="categories-count">
              {{ totalCategories }} {{ totalCategories === 1 ? t('blogCategory.oneCategory') : t('blogCategory.categories') }}
            </div>
          </div>
        </div>

        <IluriaDataTable
          v-if="!isReorderMode"
          :value="sortedCategories"
          :columns="hasCategories ? mainTableColumns : []"
          :loading="loading"
          dataKey="id"
          class="categories-table"
        >

            <!-- Header Templates for Sorting -->
            <template #header-name="{ column }">
                <div @click="toggleSort('name')" class="sortable-header">
                    <span>{{ column.header }}</span>
                    <span v-if="sortField === 'name'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                </div>
            </template>

            <template #header-slug="{ column }">
                <div @click="toggleSort('slug')" class="sortable-header">
                    <span>{{ column.header }}</span>
                    <span v-if="sortField === 'slug'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                </div>
            </template>

            <template #header-postsCount="{ column }">
                <div @click="toggleSort('postsCount')" class="sortable-header">
                    <span>{{ column.header }}</span>
                    <span v-if="sortField === 'postsCount'" class="sort-icon">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
                </div>
            </template>

            <!-- Header Actions -->
            <template #header-actions="{ column }">
                <div class="flex items-center justify-center">
                    <span>{{ column.header }}</span>
                </div>
            </template>


          <!-- Name Column -->
          <template #column-name="{ data }">
            <div class="category-name-cell">
              <div v-if="data.imageUrl" class="category-image">
                <img :src="data.imageUrl" :alt="data.name" />
              </div>
              <div class="category-info">
                <span class="category-name">{{ data.name }}</span>
                <span v-if="data.description" class="category-description">{{ data.description }}</span>
              </div>
            </div>
          </template>

          <!-- Slug Column -->
          <template #column-slug="{ data }">
            <span class="category-slug">{{ data.slug }}</span>
          </template>

          <!-- Posts Count Column -->
          <template #column-postsCount="{ data }">
            <span class="posts-count">{{ data.postsCount || 0 }} {{ t('blogCategory.postsSuffix') }}</span>
          </template>

          <!-- Actions Column -->
          <template #column-actions="{ data }">
            <div class="action-buttons">
              <IluriaButton 
                color="primary" 
                size="small" 
                :hugeIcon="PencilEdit01Icon" 
                @click="editCategory(data.id)"
                :title="t('blogCategory.edit')"
                variant="ghost"
              />
              <IluriaButton 
                color="danger" 
                size="small" 
                :hugeIcon="Delete01Icon" 
                @click.prevent="confirmDeleteCategory(data)"
                :title="t('blogCategory.delete')"
                variant="ghost"
              />
            </div>
          </template>

          <!-- Empty State -->
          <template #empty>
            <div class="empty-state">
              <div class="empty-icon">
                <HugeiconsIcon :icon="BookmarkAdd01Icon" :size="48" class="text-gray-400" />
              </div>
              <h3 class="empty-title">{{ t('blogCategory.empty') }}</h3>
              <p class="empty-description">{{ t('blogCategory.emptyDescription') }}</p>
              <IluriaButton 
                color="primary"
                :hugeIcon="Add01Icon"
                @click="goToAddCategory"
                class="mt-4"
              >
                {{ t('blogCategory.createFirst') }}
              </IluriaButton>
            </div>
          </template>

          <!-- Loading State -->
          <template #loading>
            <div class="loading-state">
              <div class="loading-spinner"></div>
              <span>{{ t('blogCategory.loading') }}</span>
            </div>
          </template>
        </IluriaDataTable>
        
        <!-- Draggable List for Reordering -->
        <VueDraggable
          v-if="isReorderMode"
          v-model="categories"
          class="reorder-list"
          :disabled="loading"
          item-key="id"
          handle=".drag-handle"
          ghost-class="ghost-item"
        >
          <template #item="{ element: category }">
            <div class="reorder-item">
              <div class="drag-handle">
                <HugeiconsIcon :icon="DragDropVerticalIcon" :size="24" class="cursor-grab text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-primary)]" />
              </div>
              <div class="category-info-reorder">
                <div v-if="category.imageUrl" class="category-image-small">
                  <img :src="category.imageUrl" :alt="category.name" />
                </div>
                <div class="category-details">
                  <span class="category-name-reorder">{{ category.name }}</span>
                  <span v-if="category.description" class="category-desc-reorder">{{ category.description }}</span>
                </div>
              </div>
            </div>
          </template>
        </VueDraggable>
        
        <!-- Pagination -->
        <div class="pagination-container" v-if="totalPages > 1 && !isReorderMode">
          <IluriaPagination 
            :current-page="currentPage"
            :total-pages="totalPages"
            @page-change="changePage"
          />
        </div>
      </ViewContainer>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import {
  PencilEdit01Icon,
  Delete01Icon,
  BookmarkAdd01Icon,
  Add01Icon,
  FilterIcon,
  ArrowLeft01Icon,
  Search01Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { DragDropVerticalIcon } from '@hugeicons-pro/core-stroke-rounded'
import { HugeiconsIcon } from '@hugeicons/vue'
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import BlogCategoryService from '@/services/blogCategory.service'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import { useI18n } from 'vue-i18n'
import { useTheme } from '@/composables/useTheme'
import { debounce } from '@/utils/debounce'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import VueDraggable from 'vuedraggable'

const router = useRouter()
const toast = useToast()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const { t } = useI18n()

const { initTheme } = useTheme()
initTheme()

const categories = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)
const totalCategories = ref(0)
const filters = ref({ filter: '' })
const hasCategories = computed(() => categories.value.length > 0)
const isReorderMode = ref(false)

const sortField = ref('name')
const sortOrder = ref(1) // 1 for ascending, -1 for descending

// Header button functions
const getHeaderButtons = () => {
  const buttons = []

  // Voltar ao Dashboard
  buttons.push({
    text: t('blogDashboard.title'),
    color: 'secondary',
    variant: 'outline',
    icon: ArrowLeft01Icon,
    onClick: () => router.push('/blog')
  })

  if (hasCategories.value && !isReorderMode.value) {
    buttons.push({
      text: t('blogCategory.reorder'),
      color: 'secondary',
      icon: FilterIcon,
      onClick: toggleReorderMode
    })
  }

  if (isReorderMode.value) {
    buttons.push({
      text: t('blogCategory.saveOrder'),
      color: 'success',
      icon: Add01Icon,
      onClick: saveOrder
    })
    buttons.push({
      text: t('blogCategory.cancel'),
      color: 'secondary',
      variant: 'outline',
      onClick: cancelReorder
    })
  }

  return buttons
}

const handleCustomButtonClick = (index, button) => {
  if (button.onClick) {
    button.onClick()
  }
}

// Configuração das colunas da tabela principal
const mainTableColumns = [
  { field: 'name', header: t('blogCategory.name'), sortable: true, class: 'col-flex' },
  { field: 'slug', header: t('blogCategory.slug'), sortable: true, class: 'col-medium' },
  { field: 'postsCount', header: t('blogCategory.posts'), sortable: true, class: 'col-small' },
  { field: 'actions', header: t('blogCategory.actions'), class: 'col-actions' }
]

const toggleSort = (field) => {
    if (sortField.value === field) {
        sortOrder.value *= -1
    } else {
        sortField.value = field
        sortOrder.value = 1
    }
}

const sortedCategories = computed(() => {
    if (!categories.value) return []

    return [...categories.value].sort((a, b) => {
        const field = sortField.value
        let aValue = a[field]
        let bValue = b[field]

        if (typeof aValue === 'string') {
            return aValue.localeCompare(bValue, undefined, { numeric: true }) * sortOrder.value
        }

        if (aValue < bValue) return -1 * sortOrder.value
        if (aValue > bValue) return 1 * sortOrder.value
        return 0
    })
})


// Carregar categorias com paginação
const loadCategories = async () => {
  loading.value = true
  try {
    const response = await BlogCategoryService.listBlogCategories(filters.value.filter, currentPage.value, 10)
    categories.value = response.content  
    totalPages.value = response.totalPages
    totalCategories.value = response.totalElements || response.content.length
  } catch (error) {
    console.error('Erro ao carregar categorias:', error)
    toast.add({ severity: 'error', summary: t('blogCategory.errorTitle'), detail: t('blogCategory.loadError'), life: 3000 })
  } finally {
    loading.value = false
  }
}

const debouncedSearch = debounce(() => {
  currentPage.value = 0
  loadCategories()
}, 300)

const changePage = (page) => {
  currentPage.value = page
  loadCategories()
}

// Navegar para página de adição de categoria
const goToAddCategory = () => {
  router.push('/blog/categories/new')
}

// Editar categoria
const editCategory = (categoryId) => {
  router.push(`/blog/categories/${categoryId}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

// Confirmar exclusão de categoria
const confirmDeleteCategory = (category) => {
  showConfirmDanger(
    t('blogCategory.deleteConfirm', { name: category.name }),
    t('blogCategory.deleteConfirmTitle'),
    () => deleteCategory(category.id)
  )
}

// Excluir categoria
const deleteCategory = async (categoryId) => {
  try {
    await BlogCategoryService.deleteBlogCategory(categoryId)
    toast.add({ severity: 'success', summary: t('blogCategory.successTitle'), detail: t('blogCategory.deleteSuccess'), life: 3000 })
    loadCategories()
  } catch (error) {
    console.error('Erro ao excluir categoria:', error)
    toast.add({ severity: 'error', summary: t('blogCategory.errorTitle'), detail: t('blogCategory.deleteError'), life: 3000 })
  }
}

// Funções de reordenação
const originalCategoriesOrder = ref([])

const toggleReorderMode = () => {
  isReorderMode.value = true
  originalCategoriesOrder.value = [...categories.value]
}

const cancelReorder = () => {
  isReorderMode.value = false
  categories.value = [...originalCategoriesOrder.value]
}

const saveOrder = async () => {
  try {
    const categoryIds = categories.value.map(cat => cat.id)
    await BlogCategoryService.reorderCategories(categoryIds)
    isReorderMode.value = false
    toast.add({ severity: 'success', summary: 'Sucesso', detail: 'Ordem das categorias salva com sucesso!', life: 3000 })
    loadCategories()
  } catch (error) {
    console.error('Erro ao reordenar categorias:', error)
    toast.add({ severity: 'error', summary: 'Erro', detail: 'Erro ao salvar a ordem das categorias', life: 3000 })
  }
}

onMounted(() => {
  loadCategories()
})
</script>

<style scoped>
.blog-category-list-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
}

.icon {
  color: var(--iluria-color-primary);
}



.action-button {
  display: inline-flex;
  align-items: center;
  padding: 10px 16px;
  border-radius: 8px;
  font-size: 14px;
  font-weight: 500;
  text-decoration: none;
  transition: all 0.2s ease;
  white-space: nowrap;
}

.action-button.secondary {
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text);
  border: 1px solid var(--iluria-color-border);
}

.action-button.secondary:hover {
  background: var(--iluria-color-sidebar-bg);
  border-color: var(--iluria-color-border-hover);
}

/* Main Content */
.list-content {
  display: flex;
  flex-direction: column;
  gap: 20px;
  background: var(--iluria-color-background);
}

/* Integrated Search */
.search-section {
  padding-bottom: 20px;
  margin-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.search-container {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  gap: 16px;
}

.search-input-wrapper {
  position: relative;
  flex: 1;
  max-width: 400px;
}

.search-icon {
  position: absolute;
  left: 12px;
  top: 50%;
  transform: translateY(-50%);
  z-index: 10;
  color: var(--iluria-color-input-placeholder);
  pointer-events: none;
}

.search-input {
  width: 100%;
}

.search-input :deep(.custom-input-group) {
  padding-left: 40px;
}

.search-input :deep(input) {
  padding-left: 40px;
}

.categories-count {
  font-size: 14px;
  color: var(--iluria-color-text-muted);
  white-space: nowrap;
  font-weight: 500;
}

.categories-table {
  width: 100%;
}

.categories-table :deep(th) {
    background: var(--iluria-color-background) !important;
    color: var(--iluria-color-text-muted) !important;
    font-size: 12px !important;
    font-weight: 500 !important;
    text-transform: uppercase;
    padding: 8px 16px !important;
    border: none !important;
    text-align: left;
    vertical-align: middle;
}

.categories-table :deep(td) {
  vertical-align: middle !important;
  padding: 12px 16px !important;
  min-height: 60px;
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text);
  border: none !important;
}



.categories-table :deep(.p-datatable-table) {
    border-collapse: collapse;
}

.categories-table :deep(tr) {
  min-height: 60px;
}

.sortable-header {
    display: flex;
    align-items: center;
    gap: 8px;
    cursor: pointer;
    user-select: none;
}

.sort-icon {
    font-size: 12px;
}

/* Column Widths */
:deep(.col-small) { width: 140px; text-align: left; }
:deep(.col-medium) { width: 25%; }
:deep(.col-large) { width: 220px; }
:deep(.col-actions) { width: 120px; text-align: center; }
:deep(.col-flex) { flex: 1; min-width: 200px; }

:deep(.p-datatable-thead > tr > th.col-small) {
    justify-content: flex-start;
}

/* Category Table Styles */
.category-name-cell {
  display: flex;
  align-items: center;
  gap: 12px;
  max-width: 100%;
  width: 100%;
}

.category-image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.category-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-info {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
  overflow: hidden;
}

.category-name {
  font-weight: 500;
  color: var(--iluria-color-text);
  margin-bottom: 2px;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.category-description {
  font-size: 12px;
  color: var(--iluria-color-text-muted);
  line-height: 1.2;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  width: 100%;
  max-width: 300px;
  display: block;
}

.category-slug {
  font-family: monospace;
  font-size: 13px;
  color: var(--iluria-color-text-muted);
  background: var(--iluria-color-container-bg);
  padding: 2px 6px;
  border-radius: 4px;
  word-break: break-all;
  display: inline-block;
}

.posts-count {
  font-size: 14px;
  color: var(--iluria-color-text);
}

.action-buttons {
  display: flex;
  gap: 8px;
  justify-content: flex-end;
}

/* Empty and Loading States */
.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  text-align: center;
}

.empty-icon {
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text);
  margin-bottom: 8px;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-muted);
  margin-bottom: 16px;
}

.loading-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 60px 20px;
  color: var(--iluria-color-text-muted);
}

.loading-spinner {
  width: 32px;
  height: 32px;
  border: 3px solid var(--iluria-color-border);
  border-top: 3px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

/* Reorder List Styles */
.reorder-list {
  display: flex;
  flex-direction: column;
  gap: 8px;
  margin-top: 16px;
}

.reorder-item {
  display: flex;
  align-items: center;
  padding: 16px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: move;
  transition: all 0.2s ease;
}

.reorder-item:hover {
  background: var(--iluria-color-sidebar-bg);
  border-color: var(--iluria-color-border-hover);
}

.drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 24px;
  height: 24px;
  margin-right: 12px;
  color: var(--iluria-color-primary);
  cursor: grab;
}

.drag-handle:active {
  cursor: grabbing;
}

.category-info-reorder {
  display: flex;
  align-items: center;
  gap: 12px;
  flex: 1;
}

.category-image-small {
  width: 32px;
  height: 32px;
  border-radius: 6px;
  overflow: hidden;
  flex-shrink: 0;
}

.category-image-small img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.category-details {
  display: flex;
  flex-direction: column;
  flex: 1;
  min-width: 0;
}

.category-name-reorder {
  font-weight: 600;
  color: var(--iluria-color-text);
  margin-bottom: 2px;
}

.category-desc-reorder {
  font-size: 14px;
  color: var(--iluria-color-text-muted);
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.ghost-item {
  opacity: 0.3;
  background: var(--iluria-color-primary-light);
  border-color: var(--iluria-color-primary);
}

/* Responsive */
@media (max-width: 768px) {
  .blog-category-list-container {
    padding: 16px;
  }
  


  .action-button {
    font-size: 13px;
    padding: 8px 12px;
  }

  .category-name-cell {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  .search-container {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .search-input-wrapper {
    max-width: none;
  }

  .categories-count {
    text-align: center;
    font-size: 13px;
  }
}
</style> 