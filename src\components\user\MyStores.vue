<template>
  <div class="my-stores">
    <!-- Stores Table -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="stores"
        :columns="storesTableColumns"
        :loading="loading"
        dataKey="id"
        class="stores-table iluria-data-table"
      >
        <!-- Column Headers -->
        <template #header-logo>
          <span class="column-header logo-header">Logo</span>
        </template>
        <template #header-store>
          <span class="column-header">Nome da Loja</span>
        </template>
        <template #header-role>
          <span class="column-header role-header">Meu Cargo</span>
        </template>
        <template #header-joinedAt>
          <span class="column-header joined-header">Membro desde</span>
        </template>
        <template #header-actions>
          <span class="column-header actions-header">Ações</span>
        </template>

        <!-- Column Templates -->
        <template #column-logo="{ data }">
          <div class="store-logo">
            <div v-if="data.store?.logo" class="logo-image">
              <img :src="data.store.logo" :alt="`Logo ${getStoreName(data)}`" />
            </div>
            <div v-else class="logo-avatar" :class="{ 'owner': isOwner(data.userRoleName || data.userRole) }">
              {{ getStoreInitials(data) }}
            </div>
          </div>
        </template>

        <template #column-store="{ data }">
          <div class="store-name-info">
            <h4 class="store-name">{{ getStoreName(data) }}</h4>
          </div>
        </template>

        <template #column-role="{ data }">
          <span class="role-badge" :class="getRoleClass(data.userRoleName || data.userRole)">
            {{ getRoleLabel(data.userRoleName || data.userRole) }}
          </span>
        </template>

        <template #column-joinedAt="{ data }">
          <div class="join-date">
            <span class="date-text">{{ formatDate(data.joinedAt) }}</span>
            <span class="duration-text">{{ getDuration(data.joinedAt) }}</span>
          </div>
        </template>

        <template #column-actions="{ data }">
          <div class="action-buttons">
            <IluriaButton 
              v-if="isOwner(data.userRoleName || data.userRole)"
              color="primary" 
              size="small" 
              :hugeIcon="Settings02Icon" 
              @click.prevent="manageStore(data)"
              title="Gerenciar loja"
            >
              Gerenciar
            </IluriaButton>
            <IluriaButton 
              v-else
              color="danger" 
              size="small" 
              variant="outline"
              :hugeIcon="LogoutIcon" 
              @click.prevent="confirmLeaveStore(data)"
              title="Sair da loja"
              :loading="data.leaving"
            >
              Sair
            </IluriaButton>
          </div>
        </template>
        
        <!-- Empty State -->
        <template #empty>
          <div class="empty-state">
            <div class="empty-icon">
              <HugeiconsIcon :icon="Store01Icon" size="48" :strokeWidth="1.5" />
            </div>
            <h3 class="empty-title">Nenhuma loja encontrada</h3>
            <p class="empty-description">Você ainda não faz parte de nenhuma loja como colaborador.</p>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>Carregando lojas...</span>
          </div>
        </template>
      </IluriaDataTable>

      <!-- Pagination -->
      <div class="pagination-container" v-if="totalPages > 0">
        <IluriaPagination 
          :current-page="currentPage"
          :total-pages="totalPages"
          @go-to-page="changePage"
        />
      </div>
    </div>

    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal 
      :isVisible="showConfirmDialog"
      :title="confirmData.title" 
      :message="confirmData.message"
      :type="confirmData.type || 'error'"
      @confirm="handleConfirm"
      @cancel="showConfirmDialog = false"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
// Removido import desnecessário do IluriaConfirmationModal
import { HugeiconsIcon } from '@hugeicons/vue'
import { 
  Store01Icon,
  LogoutIcon,
  Settings02Icon
} from '@hugeicons-pro/core-stroke-rounded'
import { collaborationApi } from '@/services/collaboration.service'
import { useToast } from '@/services/toast.service'
import storeService from '@/services/store.service'

const router = useRouter()
const toast = useToast()

// Confirmation dialog state
const showConfirmDialog = ref(false)
const confirmData = ref({ title: '', message: '', type: 'error', onConfirm: null })

// Reactive data
const stores = ref([])
const loading = ref(true)
const currentPage = ref(0)
const totalPages = ref(0)

// Table columns configuration
const storesTableColumns = computed(() => [
  { field: 'logo', headerClass: 'col-logo', class: 'col-logo' },
  { field: 'store', headerClass: 'col-store', class: 'col-store' },
  { field: 'role', headerClass: 'col-role', class: 'col-role' },
  { field: 'joinedAt', headerClass: 'col-joined', class: 'col-joined' },
  { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
])

// Methods
const loadMyStores = async () => {
  loading.value = true
  try {
    const params = {
      page: currentPage.value,
      size: 10
    }
    
    const response = await collaborationApi.getMyCollaborations(params)
    
    // Se response.data é um array, use diretamente; se é um objeto com content, use content
    if (Array.isArray(response.data)) {
      stores.value = response.data
      totalPages.value = response.data.length > 10 ? Math.ceil(response.data.length / 10) : 0
    } else {
      stores.value = response.data.content || response.data
      totalPages.value = response.data.page?.totalPages || response.data.totalPages || 0
    }
    
  } catch (error) {
    console.error('Error loading my stores:', error)
    toast.showError('Erro ao carregar suas lojas')
  } finally {
    loading.value = false
  }
}

const changePage = (page) => {
  currentPage.value = page
  loadMyStores()
}

const manageStore = (storeData) => {
  // Navigate to store management
  router.push(`/stores/${storeData.storeId}/settings`)
}

const confirmLeaveStore = (storeData) => {
  const storeName = storeData.store?.name || 'esta loja'
  
  confirmData.value = {
    title: 'Confirmar Saída',
    message: `Tem certeza que deseja sair da loja "${storeName}"? Você perderá acesso a todos os dados e funcionalidades da loja.`,
    type: 'error',
    onConfirm: () => leaveStore(storeData)
  }
  showConfirmDialog.value = true
}

const leaveStore = async (storeData) => {

  
  const storeIndex = stores.value.findIndex(s => s.id === storeData.id)
  if (storeIndex !== -1) {
    stores.value[storeIndex].leaving = true
  }
  
  try {
    await collaborationApi.leaveStore(storeData.storeId)
    toast.showSuccess('Você saiu da loja com sucesso')
    
    // Remove the store from the list
    stores.value = stores.value.filter(s => s.id !== storeData.id)
    
  } catch (error) {
    console.error('❌ Error leaving store:', error)
    console.error('❌ Error response:', error.response?.data)
    console.error('❌ Error status:', error.response?.status)
    toast.showError('Erro ao sair da loja')
    
    if (storeIndex !== -1) {
      stores.value[storeIndex].leaving = false
    }
  }
}

// Utility functions
const getRoleClass = (role) => {
  if (!role) return 'role-default'
  
  // Check for owner/proprietário roles
  const roleLower = role.toLowerCase()
  if (roleLower.includes('owner') || roleLower.includes('proprietário')) {
    return 'role-owner'
  }
  
  // Legacy role mappings for backward compatibility
  const classes = {
    'OWNER': 'role-owner',
    'ADMINISTRATOR': 'role-admin',
    'DESIGNER': 'role-designer', 
    'MARKETER': 'role-marketer'
  }
  return classes[role] || 'role-default'
}

const getRoleLabel = (role) => {
  if (!role) return 'Cargo não encontrado'
  
  // Legacy role mappings for backward compatibility
  const labels = {
    'OWNER': 'Proprietário',
    'ADMINISTRATOR': 'Administrador',
    'DESIGNER': 'Designer',
    'MARKETER': 'Marketeiro'
  }
  
  // Return the mapped label or the role name itself (for dynamic roles)
  return labels[role] || role
}


const formatDate = (dateString) => {
  if (!dateString) return ''
  return new Date(dateString).toLocaleDateString('pt-BR')
}

const getDuration = (dateString) => {
  if (!dateString) return ''
  
  const now = new Date()
  const date = new Date(dateString)
  const diffInMonths = Math.floor((now - date) / (1000 * 60 * 60 * 24 * 30))
  
  if (diffInMonths < 1) return 'Menos de 1 mês'
  if (diffInMonths === 1) return '1 mês'
  if (diffInMonths < 12) return `${diffInMonths} meses`
  
  const years = Math.floor(diffInMonths / 12)
  const remainingMonths = diffInMonths % 12
  
  if (years === 1) {
    return remainingMonths === 0 ? '1 ano' : `1 ano e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`
  }
  
  return remainingMonths === 0 ? `${years} anos` : `${years} anos e ${remainingMonths} ${remainingMonths === 1 ? 'mês' : 'meses'}`
}

// Store utility functions
const isOwner = (role) => {
  if (!role) return false
  
  const roleLower = role.toLowerCase()
  return roleLower === 'owner' || 
         roleLower === 'proprietário' || 
         roleLower.includes('owner') || 
         roleLower.includes('proprietário')
}

const getStoreName = (data) => {
  return data.store?.name || data.storeName || 'Loja'
}

const getStoreInitials = (data) => {
  const storeName = getStoreName(data)
  const words = storeName.split(' ')
  if (words.length === 1) {
    return words[0].substring(0, 2).toUpperCase()
  }
  return (words[0][0] + words[words.length - 1][0]).toUpperCase()
}

// Confirmation dialog handler
const handleConfirm = () => {
  if (confirmData.value.onConfirm) {
    confirmData.value.onConfirm()
  }
  showConfirmDialog.value = false
}

// Lifecycle
onMounted(() => {
  loadMyStores()
})
</script>

<style scoped>
.my-stores {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-logo) { 
  width: 80px; 
  text-align: center;
}
:deep(.col-store) { 
  width: auto; 
}
:deep(.col-role) { 
  width: 140px; 
  text-align: left;
}
:deep(.col-joined) { 
  width: 160px; 
  text-align: center;
}
:deep(.col-actions) { 
  width: 100px; 
  text-align: right;
}

/* Table Styling */
:deep(.stores-table) {
  border-radius: 8px;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.stores-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: left;
}

/* Header alignments */
.logo-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

.role-header {
  display: flex;
  justify-content: flex-start;
  width: 100%;
}

.joined-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

.actions-header {
  display: flex;
  justify-content: center;
  width: 100%;
}

:deep(.stores-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  font-size: 14px;
}

/* Store Logo */
.store-logo {
  display: flex;
  justify-content: center;
  width: 100%;
}

.logo-image {
  width: 48px;
  height: 48px;
  border-radius: 8px;
  overflow: hidden;
  border: 2px solid var(--iluria-color-border);
}

.logo-image img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.logo-avatar {
  width: 48px;
  height: 48px;
  background: var(--iluria-color-primary);
  color: white;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 600;
  font-size: 16px;
  letter-spacing: 0.5px;
  border: 2px solid var(--iluria-color-border);
  transition: all 0.3s ease;
}

.logo-avatar.owner {
  background: linear-gradient(135deg, #f59e0b 0%, #d97706 100%);
  border-color: #f59e0b;
}

/* Store Name Info */
.store-name-info {
  flex: 1;
  min-width: 0;
}

.store-name {
  font-size: 15px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 2px 0;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.store-badges {
  display: flex;
  gap: 6px;
  margin-top: 4px;
}

.owner-badge {
  background: #fef3c7;
  color: #d97706;
  font-size: 10px;
  font-weight: 600;
  padding: 2px 6px;
  border-radius: 8px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
}

/* Role Badge */
.role-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 8px;
  border-radius: 12px;
  text-align: center;
  white-space: nowrap;
  display: inline-block;
}

.role-owner {
  background: linear-gradient(135deg, #fef3c7 0%, #fde68a 100%);
  color: #d97706;
  font-weight: 600;
}

.role-admin {
  background: #fef3c7;
  color: #d97706;
}

.role-designer {
  background: #e0e7ff;
  color: #3730a3;
}

.role-marketer {
  background: #f3e8ff;
  color: #7c3aed;
}


/* Join Date */
.join-date {
  width: 100%;
}

.date-text {
  display: block;
  font-size: 13px;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  margin-bottom: 2px;
}

.duration-text {
  display: block;
  font-size: 11px;
  color: var(--iluria-color-text-secondary);
}

/* Action Buttons */
.action-buttons {
  display: flex;
  gap: 8px;
  align-items: center;
  justify-content: center;
  width: 100%;
}

.pending-text {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  font-style: italic;
}

/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  color: var(--iluria-color-text-muted);
  margin-bottom: 16px;
}

.empty-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  max-width: 400px;
  line-height: 1.5;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid #e5e7eb;
  border-top: 2px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 16px;
  padding: 16px;
  background: var(--iluria-color-sidebar-bg);
}

/* Responsive */
@media (max-width: 768px) {
  .my-stores {
    padding: 16px;
  }

  .action-buttons {
    flex-direction: column;
    gap: 4px;
  }

  :deep(.stores-table .p-datatable-tbody > tr > td) {
    padding: 12px 8px;
  }

  .store-details {
    max-width: 200px;
  }

  .store-name {
    font-size: 14px;
  }
}
</style>