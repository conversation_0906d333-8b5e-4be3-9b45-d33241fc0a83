{"title": "Configurações do Usuário", "subtitle": "Gerencie suas preferências e configurações de conta", "sections": {"profile": {"title": "Perfil", "myProfile": "<PERSON><PERSON>", "subtitle": "Gerencie suas informações pessoais e preferências da conta", "profilePhoto": "Foto do Perfil", "profilePhotoDescription": "Adicione uma foto para personalizar seu perfil", "changePhoto": "<PERSON><PERSON><PERSON>", "removePhoto": "Remover", "personalInformation": "Informaçõ<PERSON>", "fullName": "Nome <PERSON>to", "fullNamePlaceholder": "Digite seu nome completo", "email": "Email", "emailPlaceholder": "<EMAIL>", "phone": "Telefone", "phonePlaceholder": "(11) 99999-9999", "cpf": "CPF", "cpfPlaceholder": "000.000.000-00", "cpfHint": "CPF não pode ser alterado", "preferences": "Preferências", "language": "Idioma", "languageDescription": "Escolha o idioma da interface", "timezone": "<PERSON><PERSON>", "timezoneDescription": "Defina seu fuso horário local", "saving": "Salvando...", "saveChanges": "<PERSON><PERSON>", "profileUpdated": "Perfil atualizado com sucesso!", "profileUpdateError": "Erro ao salvar perfil", "profileLoadError": "Erro ao carregar perfil"}, "security": {"title": "Segurança", "settings": "Configurações de Segurança", "subtitle": "Gerencie a segurança da sua conta e monitore atividades", "changePassword": "<PERSON><PERSON><PERSON>", "twoFactorAuth": "Autenticação de Dois Fatores", "sessionsAndLogs": "Sessões e Logs", "authenticatorApp": "Aplicativo Autenticador", "authenticatorDescription": "Use um aplicativo autenticador para adicionar uma camada extra de segurança", "enabled": "<PERSON><PERSON>do", "disabled": "Desativado", "activate": "Ativar", "configure": "Configurar", "activeSessions": "Sessões <PERSON>", "sessionManagement": "Gerenciamento de Sessões", "sessionDescription": "Monitore e gerencie suas sessões ativas", "viewAllSessions": "<PERSON><PERSON> as <PERSON><PERSON><PERSON><PERSON>", "activityMonitoring": "Monitoramento de Atividades", "recentActivity": "Atividade Recente", "activityDescription": "Acompanhe as atividades recentes da sua conta", "viewActivityLog": "Ver Registro de Atividades", "twoFactorEnabled": "Autenticação de dois fatores ativada", "twoFactorDisabled": "Autenticação de dois fatores desativada", "twoFactorError": "Erro ao alterar autenticação de dois fatores"}, "twoFactor": {"title": "Autenticação de Dois Fatores", "description": "Use um aplicativo autenticador para adicionar uma camada extra de segurança", "statusEnabled": "<PERSON><PERSON>do", "statusDisabled": "Desativado", "enable": "Ativar", "disable": "Desativar", "configure": "Configurar", "setupTitle": "Configurar Autenticação TOTP", "setupDescription": "Configure seu aplicativo autenticador para obter códigos de verificação", "qrInstructions": "Escaneie o código QR abaixo com seu aplicativo autenticador:", "manualInstructions": "Ou digite manualmente esta chave no seu aplicativo:", "appSuggestions": "Aplicativos recomendados: Google Authenticator, <PERSON><PERSON>, Microsoft Authenticator", "recommended": "Recomendado", "enterCode": "Código do aplicativo:", "codePlaceholder": "", "verifySetup": "Verificar e Ativar", "verifying": "Verificando...", "disableTitle": "Desativar TOTP", "disableDescription": "Digite sua senha atual para desativar a autenticação por aplicativo", "currentPassword": "<PERSON><PERSON> atual", "currentPasswordPlaceholder": "Digite sua senha atual", "disabling": "Desativando...", "totpEnabled": "TOTP ativado com sucesso!", "totpDisabled": "TOTP desativado com sucesso!", "invalidCode": "Código inválido. Tente novamente.", "invalidPassword": "Senha incorreta", "setupError": "Erro ao configurar TOTP", "disableError": "Erro ao desativar TOTP", "generatingQR": "Gerando código QR..."}, "billing": {"title": "Cobranças", "paymentMethods": "Métodos de Pagamento", "subscriptions": "Assinaturas"}, "stores": {"title": "<PERSON><PERSON>", "myStores": "<PERSON><PERSON>", "invites": "<PERSON><PERSON><PERSON>"}, "notifications": {"title": "Notificações", "email": "Notificações por Email"}, "account": {"title": "Conta", "myProfile": "<PERSON><PERSON>", "preferences": "Preferências", "settings": "Configurações da Conta", "contacts": "Contatos"}}, "changePassword": "<PERSON><PERSON><PERSON>", "changePasswordDescription": "<PERSON><PERSON><PERSON>", "changePasswordInstructions": "Para sua segurança, escolha uma senha forte e única que você não use em outros lugares.", "currentPassword": "<PERSON><PERSON>", "currentPasswordPlaceholder": "Digite sua senha atual", "newPassword": "Nova Senha", "newPasswordPlaceholder": "Digite sua nova senha", "confirmNewPassword": "Confirmar <PERSON>", "confirmNewPasswordPlaceholder": "Digite novamente sua nova senha", "securityNoticeTitle": "Segurança da Conta", "securityNoticeText": "Sua senha será criptografada e armazenada com segurança. Recomendamos usar uma senha única que contenha letras maiúsculas, minús<PERSON>s, números e símbolos.", "passwordRequirements": "Requis<PERSON>s da Senha", "requirementUpperCase": "<PERSON>elo menos uma letra ma<PERSON> (A-Z)", "requirementLowerCase": "<PERSON>elo menos uma letra minúscula (a-z)", "requirementNumber": "<PERSON><PERSON> menos um número (0-9)", "requirementSpecialChar": "Pelo menos um caractere especial (!@#$%)", "requirementMinLength": "<PERSON><PERSON><PERSON> de 8 caracteres", "strengthWeak": "Fraca", "strengthMedium": "Média", "strengthGood": "Bo<PERSON>", "strengthStrong": "Forte", "cancel": "<PERSON><PERSON><PERSON>", "updatePassword": "<PERSON><PERSON><PERSON>", "updating": "Alterando...", "save": "<PERSON><PERSON>", "confirmPassword": "Senha atual é obrigatória", "newPasswordRequired": "Nova senha é obrigatória", "confirmNewPasswordRequired": "Confirmação da nova senha é obrigatória", "invalidCurrentPassword": "Senha atual incorreta", "password": {"mfaTitle": "Confirmar altera<PERSON> de senha", "mfaDescription": "Para sua segurança, confirme sua identidade antes de alterar a senha"}, "mfa": {"title": "Verificação de Segurança", "description": "Para sua segurança, confirme sua identidade", "emailSent": "Código enviado para", "checkInbox": "Verifique sua caixa de entrada e pasta de spam", "totpInstruction": "Digite o código do seu aplicativo autenticador", "emailCode": "Código do email", "totpCode": "Código TOTP", "verify": "Verificar", "verifying": "Verificando...", "resend": "Reenviar", "resendIn": "Reenviar em {seconds}s", "invalidCode": "Código inválido. Tente novamente.", "codeExpired": "<PERSON>ó<PERSON> expirado. Um novo código foi enviado.", "verificationSuccess": "Verificação realizada com sucesso!", "verificationError": "Erro na verificação. Tente novamente."}, "sessions": {"invalidatedTitle": "Outras sessões desconectadas", "invalidatedMessage": "<PERSON><PERSON> as outras sessões foram desconectadas por segurança após a alteração da senha."}, "common": {"cancel": "<PERSON><PERSON><PERSON>", "confirm": "Confirmar", "close": "<PERSON><PERSON><PERSON>"}, "billing": {"savedCards": "<PERSON><PERSON><PERSON><PERSON>", "addCard": "<PERSON><PERSON><PERSON><PERSON>", "addFirstCard": "<PERSON><PERSON><PERSON><PERSON>", "addNewCard": "Adicionar <PERSON>", "addNewCardOption": "Adicionar novo cartão de crédito", "noCardsTitle": "Nenhum cartão salvo", "noCardsDescription": "Adicione um cartão para facilitar seus pagamentos futuros", "noSavedCards": "Você não possui cartões salvos", "defaultCard": "Cartão Padrão", "setDefault": "Definir como Padrão", "updateCard": "<PERSON><PERSON><PERSON><PERSON>", "editCard": "<PERSON><PERSON>", "cardNumber": "Número do Cartão", "cardNumberPlaceholder": "0000 0000 0000 0000", "cardHolder": "Nome no Cartão", "cardHolderPlaceholder": "Nome como está no cartão", "expiryDate": "Data de Validade", "cvv": "CVV", "setAsDefault": "Definir como cartão padrão", "saveCard": "<PERSON><PERSON> cartão para uso futuro", "securityMessage": "Suas informações de pagamento são protegidas com criptografia de nível bancário", "paymentHistory": "Históric<PERSON> de Pagamentos", "downloadInvoices": "Baixar <PERSON>", "date": "Data", "description": "Descrição", "amount": "Valor", "viewInvoice": "<PERSON><PERSON>", "download": "Baixar", "deleteCardTitle": "Excluir <PERSON>", "deleteCardMessage": "Tem certeza que deseja excluir este cartão? Esta ação não pode ser desfeita.", "zipCode": "CEP", "zipCodePlaceholder": "00000-000", "street": "<PERSON><PERSON>", "streetPlaceholder": "Nome da rua", "number": "Número", "numberPlaceholder": "123", "complement": "Complemento", "complementPlaceholder": "Apto, bloco, etc.", "neighborhood": "Bairro", "neighborhoodPlaceholder": "Nome do bairro", "city": "Cidade", "cityPlaceholder": "Nome da cidade", "state": "Estado", "statePlaceholder": "Selecione o estado", "country": "<PERSON><PERSON>", "countryPlaceholder": "Brasil", "saveAddress": "<PERSON><PERSON>", "validation": {"invalidCardNumber": "Número do cartão inválido", "cardHolderMinLength": "Nome deve ter pelo menos 3 caracteres", "invalidExpiryDate": "Data de validade inválida (MM/AA)", "invalidCvv": "CVV deve ter 3 ou 4 dígitos", "invalidZipCode": "CEP inválido"}, "status": {"paid": "Pago", "pending": "Pendente", "failed": "Fal<PERSON>"}, "success": {"cardAdded": "Cartão adicionado com sucesso", "cardUpdated": "Cartão atualizado com sucesso", "cardDeleted": "Cartão excluído com sucesso", "defaultCardSet": "Cartão padrão definido com sucesso"}, "errors": {"loadCards": "Erro ao carregar cartões", "loadHistory": "Erro ao carregar histórico", "setDefault": "<PERSON>rro ao definir cart<PERSON> padrão", "deleteCard": "Erro ao excluir cartão"}}, "subscriptions": {"currentPlan": "Plano Atual", "availablePlans": "Planos Disponíveis", "availableUpgrades": "Upgrades Disponíveis", "noActiveSubscription": "Nenhuma assinatura ativa", "noActiveDescription": "Escolha um plano para começar a usar todos os recursos", "choosePlan": "Escolher Plano", "recommended": "Recomendado", "monthly": "Mensal", "yearly": "<PERSON><PERSON>", "savePercent": "Economize {percent}%", "subscribe": "<PERSON><PERSON><PERSON>", "upgrade": "Fazer Upgrade", "downgrade": "Fazer Downgrade", "upgradeTo": "Upgrade para {plan}", "manage": "Gerenciar", "cancel": "<PERSON><PERSON><PERSON>", "pause": "Pausar", "status": "Status", "nextBilling": "Próxima <PERSON>", "paymentMethod": "<PERSON><PERSON><PERSON><PERSON>", "amount": "Valor", "history": "Hist<PERSON><PERSON><PERSON>", "date": "Data", "action": "Ação", "plan": "Plano", "periods": {"monthly": "mês", "yearly": "ano"}, "statuses": {"active": "Ativo", "cancelled": "Cancelado", "pending": "Pendente", "paused": "<PERSON><PERSON><PERSON>"}, "manageSubscription": "Gerenciar Assinatura", "changePaymentMethod": "<PERSON><PERSON><PERSON>", "currentPaymentMethod": "Método de Pagamento Atual", "selectNewMethod": "Selecionar Novo Método", "changeMethod": "<PERSON><PERSON><PERSON>", "billingAddress": "Endereço de Cobrança", "noBillingAddress": "Nenhum endereço de cobrança cadastrado", "notifications": "Notificações", "billingReminders": "Lembretes de Cobrança", "billingRemindersDesc": "Re<PERSON>ber lembretes antes da data de cobrança", "renewalNotices": "Avisos de Renovação", "renewalNoticesDesc": "Ser notificado sobre renovações automáticas", "planUpdates": "Atualizações de Plano", "planUpdatesDesc": "Receber informações sobre novos recursos e planos", "dangerZone": "Zona de Perigo", "pauseSubscription": "<PERSON><PERSON><PERSON>", "pauseSubscriptionDesc": "Pausar temporariamente sua assinatura", "cancelSubscription": "<PERSON><PERSON><PERSON>", "cancelSubscriptionDesc": "Cancelar permanentemente sua assinatura", "cancelTitle": "<PERSON><PERSON><PERSON>", "cancelMessage": "Tem certeza que deseja cancelar sua assinatura? Você perderá acesso a todos os recursos premium.", "upgradePlan": "Upgrade de Plano", "currentPrice": "Preço Atual", "newPrice": "Novo Preço", "difference": "Diferença", "upgradeInfo": "A cobrança será proporcional ao tempo restante do período atual", "confirmUpgradeTitle": "Confirmar Upgrade", "confirmUpgradeMessage": "Você está fazendo upgrade de {currentPlan} para {newPlan}. O valor será alterado de {currentPrice} para {newPrice} (diferença de {difference}).", "success": {"subscribed": "Assinatura realizada com sucesso", "upgraded": "Upgrade realizado com sucesso", "planChanged": "Plano alterado com sucesso", "cancelled": "Assinatura cancelada com sucesso", "paused": "Assinatura pausada com sucesso", "updated": "Assinatura atualizada com sucesso", "paymentMethodChanged": "Método de pagamento alterado com sucesso", "addressSaved": "Endereço salvo com sucesso", "notificationsUpdated": "Configurações de notificação atualizadas"}, "errors": {"loadCurrent": "Erro ao carregar assinatura atual", "loadPlans": "Erro ao carregar planos disponíveis", "loadHistory": "Erro ao carregar histórico", "subscribe": "Erro ao realizar assinatura", "changePlan": "Erro ao alterar plano", "upgrade": "Erro ao fazer upgrade", "cancel": "Erro ao cancelar assinatura", "pause": "Erro ao pausar assinatura", "changePaymentMethod": "Erro ao alterar método de pagamento", "updateNotifications": "Erro ao atualizar configurações de notificação"}}}