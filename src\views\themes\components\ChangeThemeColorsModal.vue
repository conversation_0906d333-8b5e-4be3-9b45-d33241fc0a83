<template>

  <IluriaModal
    :model-value="isVisible"
    @update:model-value="isVisible = $event"
    :title="$t('themes.changeColors')"
    :subtitle="$t('themes.changeColorsDescription')"
    :icon="PaintBoardIcon"
    icon-color="purple"
    @save="handleSave"
    @cancel="handleCancel"
    :save-label="isSaving ? $t('themes.savingColors') : $t('themes.saveColors')"
    :cancel-label="$t('cancel')"
    :saving="isSaving"
    :dialog-style="{
      width: '800px',
      maxWidth: '95vw'
    }"
  >
    <div class="colors-form">
      <!-- Preview Section -->
      <div class="preview-section">
        <h3 class="section-title">
          <HugeiconsIcon :icon="ViewIcon" class="section-icon" />
          {{ $t('themes.preview.title') }}
        </h3>
        <div class="color-preview" :style="previewStyles">
          <div class="preview-header">
            <h4>{{ theme?.name || 'Tema' }}</h4>
            <p>{{ $t('themes.stylePreviewText') }}</p>
          </div>
          <div class="preview-content">
            <div class="preview-card">
              <div class="preview-card-header">
                <h5>Componente de Exemplo</h5>
              </div>
              <div class="preview-card-body">
                <p class="preview-text">{{ $t('themes.sampleText') }}</p>
                <div class="preview-buttons">
                  <div class="preview-button primary">{{ $t('themes.primaryButton') }}</div>
                  <div class="preview-button secondary">{{ $t('themes.secondaryButton') }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Colors Configuration -->
      <div class="colors-section">
        <h3 class="section-title">
          <HugeiconsIcon :icon="PaintBoardIcon" class="section-icon" />
          {{ $t('themes.colorConfiguration') }}
        </h3>

        <div class="colors-grid">
          <!-- Background Geral -->
          <div class="color-group" :class="{ active: activeColorType === 'backgroundGeneral' }">
            <label class="color-label" for="background-general-color" @click="setActiveColorType('backgroundGeneral')">
              <HugeiconsIcon :icon="LayoutIcon" class="color-icon" />
              {{ $t('themes.backgroundGeneral') }}
            </label>
            <div class="color-input-group">
              <input
                id="background-general-color"
                type="color"
                v-model="colors.backgroundGeneral"
                class="color-picker"
                @input="updatePreview"
                @focus="setActiveColorType('backgroundGeneral')"
              />
              <IluriaInputText
                v-model="colors.backgroundGeneral"
                placeholder="#ffffff"
                @input="updatePreview"
                @focus="setActiveColorType('backgroundGeneral')"
                class="color-text-input"
              />
            </div>
          </div>

          <!-- Background dos Componentes -->
          <div class="color-group" :class="{ active: activeColorType === 'backgroundComponents' }">
            <label class="color-label" for="background-components-color" @click="setActiveColorType('backgroundComponents')">
              <HugeiconsIcon :icon="SquareIcon" class="color-icon" />
              {{ $t('themes.backgroundComponents') }}
            </label>
            <div class="color-input-group">
              <input
                type="color"
                v-model="colors.backgroundComponents"
                class="color-picker"
                @input="updatePreview"
                @focus="setActiveColorType('backgroundComponents')"
              />
              <IluriaInputText
                v-model="colors.backgroundComponents"
                placeholder="#f8f9fa"
                @input="updatePreview"
                @focus="setActiveColorType('backgroundComponents')"
                class="color-text-input"
              />
            </div>
          </div>

          <!-- Cor dos Títulos -->
          <div class="color-group" :class="{ active: activeColorType === 'titleColor' }">
            <label class="color-label" for="title-color" @click="setActiveColorType('titleColor')">
              <HugeiconsIcon :icon="Edit01Icon" class="color-icon" />
              {{ $t('themes.titleColor') }}
            </label>
            <div class="color-input-group">
              <input
                type="color"
                v-model="colors.titleColor"
                class="color-picker"
                @input="updatePreview"
                @focus="setActiveColorType('titleColor')"
              />
              <IluriaInputText
                v-model="colors.titleColor"
                placeholder="#1a1a1a"
                @input="updatePreview"
                @focus="setActiveColorType('titleColor')"
                class="color-text-input"
              />
            </div>
          </div>

          <!-- Cor dos Textos -->
          <div class="color-group" :class="{ active: activeColorType === 'textColor' }">
            <label class="color-label" for="text-color" @click="setActiveColorType('textColor')">
              <HugeiconsIcon :icon="TextIcon" class="color-icon" />
              {{ $t('themes.textColor') }}
            </label>
            <div class="color-input-group">
              <input
                type="color"
                v-model="colors.textColor"
                class="color-picker"
                @input="updatePreview"
                @focus="setActiveColorType('textColor')"
              />
              <IluriaInputText
                v-model="colors.textColor"
                placeholder="#666666"
                @input="updatePreview"
                @focus="setActiveColorType('textColor')"
                class="color-text-input"
              />
            </div>
          </div>
        </div>

        <!-- Quick Actions -->
        <div class="quick-actions">
          <IluriaButton
            variant="outline"
            size="small"
            @click="resetColors"
            :hugeIcon="RefreshIcon"
          >
            {{ $t('themes.resetColors') }}
          </IluriaButton>

          <IluriaButton
            variant="outline"
            size="small"
            @click="generateHarmoniousColors"
            :hugeIcon="PaintBoardIcon"
          >
            {{ $t('themes.harmoniousColors') }}
          </IluriaButton>

          <IluriaButton
            variant="outline"
            size="small"
            @click="randomColors"
            :hugeIcon="ShuffleIcon"
          >
            {{ $t('themes.randomColors') }}
          </IluriaButton>
        </div>
      </div>

      <!-- Info Message -->
      <div class="info-message">
        <HugeiconsIcon :icon="InformationCircleIcon" class="info-icon" />
        <span>{{ $t('themes.changeColorsInfo') }}</span>
      </div>
    </div>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  PaintBoardIcon,
  InformationCircleIcon,
  RefreshIcon,
  ViewIcon,
  LayoutIcon,
  SquareIcon,
  Edit01Icon,
  TextIcon,
  ShuffleIcon
} from '@hugeicons-pro/core-stroke-rounded'

import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'

// Props
const props = defineProps({
  theme: {
    type: Object,
    default: null
  },
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['update:visible', 'save', 'cancel'])

// State
const isVisible = ref(props.visible)
const isSaving = ref(false)
const colors = ref({
  backgroundGeneral: '#ffffff',
  backgroundComponents: '#f8f9fa',
  titleColor: '#1a1a1a',
  textColor: '#666666'
})

// Armazenar cores originais do tema para reset
const originalColors = ref({
  backgroundGeneral: '#ffffff',
  backgroundComponents: '#f8f9fa',
  titleColor: '#1a1a1a',
  textColor: '#666666'
})

// Active color type for selection
const activeColorType = ref(null)

// Computed
const previewStyles = computed(() => ({
  '--background-general': colors.value.backgroundGeneral,
  '--background-components': colors.value.backgroundComponents,
  '--title-color': colors.value.titleColor,
  '--text-color': colors.value.textColor,
  backgroundColor: colors.value.backgroundGeneral,
  color: colors.value.textColor
}))



// Methods
const updatePreview = () => {
  // Validar cores hex
  Object.keys(colors.value).forEach(key => {
    if (!colors.value[key].startsWith('#')) {
      colors.value[key] = '#' + colors.value[key].replace('#', '')
    }
    if (colors.value[key].length !== 7) {
      // Manter cor anterior se inválida
      return
    }
  })
}

const setActiveColorType = (type) => {
  activeColorType.value = type
}



const resetColors = () => {
  // Restaurar para as cores originais do tema (não as cores atuais)
  colors.value = {
    backgroundGeneral: originalColors.value.backgroundGeneral,
    backgroundComponents: originalColors.value.backgroundComponents,
    titleColor: originalColors.value.titleColor,
    textColor: originalColors.value.textColor
  }
  updatePreview()
}

const generateHarmoniousColors = () => {
  // Gerar uma paleta harmoniosa baseada em uma cor base
  const baseHue = Math.floor(Math.random() * 360)

  // Função para converter HSL para HEX
  const hslToHex = (h, s, l) => {
    l /= 100
    const a = s * Math.min(l, 1 - l) / 100
    const f = n => {
      const k = (n + h / 30) % 12
      const color = l - a * Math.max(Math.min(k - 3, 9 - k, 1), -1)
      return Math.round(255 * color).toString(16).padStart(2, '0')
    }
    return `#${f(0)}${f(8)}${f(4)}`
  }

  colors.value = {
    backgroundGeneral: '#ffffff',
    backgroundComponents: hslToHex(baseHue, 10, 97),
    titleColor: hslToHex(baseHue, 20, 20),
    textColor: hslToHex(baseHue, 15, 40)
  }
  updatePreview()
}

const randomColors = () => {
  const generateRandomColor = () => {
    return '#' + Math.floor(Math.random()*16777215).toString(16).padStart(6, '0')
  }

  colors.value = {
    backgroundGeneral: '#ffffff', // Manter fundo branco para legibilidade
    backgroundComponents: generateRandomColor(),
    titleColor: generateRandomColor(),
    textColor: generateRandomColor()
  }
  updatePreview()
}

const handleSave = async () => {
  isSaving.value = true

  try {
    // Validar se o tema existe e tem ID
    if (!props.theme || !props.theme.id) {
      throw new Error('Tema inválido: ID não encontrado')
    }

    emit('save', {
      theme: props.theme,
      colors: { ...colors.value }
    })

    handleCancel()
  } catch (error) {
    console.error('Erro ao salvar cores do tema:', error)
    isSaving.value = false
  }
}

const handleCancel = () => {
  isSaving.value = false
  isVisible.value = false
  activeColorType.value = null
  emit('update:visible', false)
  emit('cancel')
}

const getOriginalThemeColors = () => {
  // Definir cores padrão baseadas no sistema de design da Iluria
  const defaultColors = {
    backgroundGeneral: '#ffffff',
    backgroundComponents: '#f8f9fa',
    titleColor: '#1a1a1a',
    textColor: '#666666'
  }

  // Se o tema tem um baseThemeId, tentar encontrar as cores originais do tema base
  if (props.theme?.baseThemeId) {
    // Mapear cores específicas baseadas no ID do tema base
    const baseThemeColors = getBaseThemeColors(props.theme.baseThemeId)
    if (baseThemeColors) {
      return baseThemeColors
    }
  }

  // Se o tema é padrão, usar cores específicas do design system
  if (props.theme?.isDefault) {
    // Para temas padrão, usar cores originais do design system
    return defaultColors
  }

  // Para temas personalizados sem baseThemeId, usar cores padrão como fallback
  return defaultColors
}

const getBaseThemeColors = (baseThemeId) => {
  // Mapear cores baseadas nos temas predefinidos do sistema
  const themeColorMappings = {
    'light': {
      backgroundGeneral: '#f9fafb',
      backgroundComponents: '#ffffff',
      titleColor: '#1f2937',
      textColor: '#374151'
    },
    'dark': {
      backgroundGeneral: '#0a0a0a',
      backgroundComponents: '#1a1a1a',
      titleColor: '#ffffff',
      textColor: '#d1d5db'
    },
    'corporate': {
      backgroundGeneral: '#f8fafc',
      backgroundComponents: '#ffffff',
      titleColor: '#1e293b',
      textColor: '#475569'
    },
    'bumblebee': {
      backgroundGeneral: '#fffbeb',
      backgroundComponents: '#ffffff',
      titleColor: '#92400e',
      textColor: '#451a03'
    },
    'winter': {
      backgroundGeneral: '#f8fafc',
      backgroundComponents: '#ffffff',
      titleColor: '#0f172a',
      textColor: '#334155'
    },
    'nord': {
      backgroundGeneral: '#eceff4',
      backgroundComponents: '#ffffff',
      titleColor: '#2e3440',
      textColor: '#3b4252'
    },
    'fantasy': {
      backgroundGeneral: '#faf5ff',
      backgroundComponents: '#ffffff',
      titleColor: '#3c1361',
      textColor: '#581c87'
    }
  }

  return themeColorMappings[baseThemeId] || null
}

const initializeColors = () => {
  // Armazenar cores originais primeiro (para reset)
  originalColors.value = getOriginalThemeColors()

  if (props.theme?.cssVariables) {
    const cssVars = props.theme.cssVariables

    // Armazenar cores atuais como cores de trabalho
    colors.value = {
      backgroundGeneral: cssVars['color-background-general'] || originalColors.value.backgroundGeneral,
      backgroundComponents: cssVars['color-background-components'] || originalColors.value.backgroundComponents,
      titleColor: cssVars['color-title'] || originalColors.value.titleColor,
      textColor: cssVars['color-text'] || originalColors.value.textColor
    }
  } else {
    // Se não há cssVariables, usar cores originais
    colors.value = { ...originalColors.value }
  }
}

// Watchers
watch(() => props.visible, (visible) => {
  isVisible.value = visible
  if (visible) {
    initializeColors()
  }
}, { immediate: true })

watch(isVisible, (newValue) => {
  emit('update:visible', newValue)
})


watch(() => props.theme, () => {
  if (props.visible) {
    initializeColors()
  }
})




</script>

<style scoped>
.colors-form {
  display: flex;
  flex-direction: column;
  gap: 2rem;
}

.section-title {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 1.5rem;
  display: flex;
  align-items: center;
  gap: 0.5rem;
}

.section-icon {
  width: 1.25rem;
  height: 1.25rem;
  color: var(--iluria-color-primary);
}

/* Preview Section */
.preview-section {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 1.5rem;
  background: var(--iluria-color-surface);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.color-preview {
  border-radius: 8px;
  padding: 2rem;
  min-height: 160px;
  transition: all 0.3s ease;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.preview-header h4 {
  font-size: 1.5rem;
  font-weight: 700;
  margin-bottom: 0.5rem;
  color: var(--title-color);
}

.preview-header p {
  font-size: 1rem;
  color: var(--text-color);
  margin-bottom: 1.5rem;
  opacity: 0.8;
}

.preview-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.preview-card {
  background: var(--background-components);
  border-radius: 8px;
  padding: 1.5rem;
  border: 1px solid rgba(0, 0, 0, 0.05);
}

.preview-card-header h5 {
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--title-color);
  margin-bottom: 0.5rem;
}

.preview-card-body {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.preview-text {
  font-size: 0.95rem;
  color: var(--text-color);
  line-height: 1.5;
}

.preview-buttons {
  display: flex;
  gap: 1rem;
  flex-wrap: wrap;
}

.preview-button {
  padding: 0.75rem 1.5rem;
  border-radius: 6px;
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s;
  border: none;
}

.preview-button.primary {
  background: var(--title-color);
  color: var(--background-general);
}

.preview-button.secondary {
  background: var(--text-color);
  color: var(--background-general);
}



/* Colors Section */
.colors-section {
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  padding: 1.5rem;
  background: var(--iluria-color-surface);
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
}

.colors-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 1.5rem;
  margin-bottom: 2rem;
}

.color-group {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem;
  border: 2px solid transparent;
  border-radius: 8px;
  transition: all 0.2s ease;
  cursor: pointer;
}

.color-group:hover {
  background: var(--iluria-color-hover);
}

.color-group.active {
  border-color: var(--iluria-color-primary);
  background: rgba(var(--iluria-color-primary-rgb), 0.05);
}

.color-label {
  font-size: 0.9rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  display: flex;
  align-items: center;
  gap: 0.5rem;
  cursor: pointer;
}

.color-icon {
  width: 1rem;
  height: 1rem;
  color: var(--iluria-color-primary);
}

.color-input-group {
  display: flex;
  gap: 0.75rem;
  align-items: center;
}

.color-picker {
  width: 50px;
  height: 40px;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  cursor: pointer;
  background: none;
  transition: all 0.2s ease;
}

.color-picker:hover {
  border-color: var(--iluria-color-primary);
}

.color-text-input {
  flex: 1;
}

/* Quick Actions */
.quick-actions {
  display: flex;
  gap: 1rem;
  justify-content: center;
  flex-wrap: wrap;
}

/* Info Message */
.info-message {
  display: flex;
  align-items: flex-start;
  gap: 0.75rem;
  padding: 1rem;
  background: var(--iluria-color-info-bg, #eff6ff);
  border: 1px solid var(--iluria-color-info-border, #bfdbfe);
  border-radius: 8px;
  font-size: 0.875rem;
  color: var(--iluria-color-info-text, #1e40af);
}

.info-icon {
  width: 1.25rem;
  height: 1.25rem;
  flex-shrink: 0;
  margin-top: 0.125rem;
}

/* Responsive */
@media (max-width: 768px) {
  .colors-grid {
    grid-template-columns: 1fr;
  }

  .preview-content {
    align-items: flex-start;
  }

  .preview-buttons {
    flex-direction: column;
    align-items: flex-start;
  }

  .quick-actions {
    flex-direction: column;
  }
}
</style>
