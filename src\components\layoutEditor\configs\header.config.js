export default {
  name: 'Header',
  type: 'header',
  dataComponent: 'header',
  elementType: 'header',
  className: 'iluria-header',
  selectors: ['[data-component="header"]', '.iluria-header'],
  category: 'layout',
  description: 'Cabeçalho da página com logotipo, menu de navegação e elementos funcionais',
  priority: 95,
  layoutTypes: ['static', 'product', 'collection', 'system'], // ✅ Disponível em todos os layouts
  icon: 'title',
  
  html: `<header
    data-component="header"
    data-element-type="header"
    data-header-type="default"
    data-show-logo="true"
    data-show-menu="true"
    data-show-search="true"
    data-show-cart="true"
    data-show-user="true"
    data-fixed="false"
    data-transparent="false"
    data-bg-color="#ffffff"
    data-text-color="#000000"
    data-border-bottom="true"
    class="iluria-header"
    style="background-color: #ffffff; color: #000000; border-bottom: 1px solid #e5e7eb; width: 100%; z-index: 1000; position: relative;">
    <div class="header-container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
      <div class="header-content" style="display: flex; align-items: center; justify-content: space-between; height: 70px;">
        <div class="header-left" style="display: flex; align-items: center; gap: 2rem;">
          <div class="header-logo" style="display: flex; align-items: center;">
            <svg width="32" height="32" viewBox="0 0 32 32" fill="none" style="margin-right: 0.75rem;">
              <rect width="32" height="32" rx="8" fill="currentColor"/>
              <path d="M8 12h16M8 16h16M8 20h12" stroke="white" stroke-width="2" stroke-linecap="round"/>
            </svg>
            <span class="logo-text" style="font-size: 1.5rem; font-weight: bold; color: #000000;">Iluria</span>
          </div>
          <nav class="header-nav" style="display: flex; align-items: center;">
            <ul class="nav-menu" style="display: flex; list-style: none; margin: 0; padding: 0; gap: 2rem;">
              <li class="nav-item">
                <a href="#" class="nav-link" style="color: #000000; text-decoration: none; font-weight: 500; transition: color 0.3s ease; position: relative;">Início</a>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link" style="color: #000000; text-decoration: none; font-weight: 500; transition: color 0.3s ease; position: relative;">Produtos</a>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link" style="color: #000000; text-decoration: none; font-weight: 500; transition: color 0.3s ease; position: relative;">Sobre</a>
              </li>
              <li class="nav-item">
                <a href="#" class="nav-link" style="color: #000000; text-decoration: none; font-weight: 500; transition: color 0.3s ease; position: relative;">Contato</a>
              </li>
            </ul>
          </nav>
        </div>
        <div class="header-right" style="display: flex; align-items: center; gap: 1rem;">
          <div class="header-search" style="position: relative;">
            <input type="text" placeholder="Buscar..." class="search-input" style="padding: 8px 40px 8px 16px; border: 1px solid #d1d5db; border-radius: 20px; background: #f9fafb; font-size: 14px; width: 200px; transition: all 0.3s ease;">
            <svg class="search-icon" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" style="position: absolute; right: 12px; top: 50%; transform: translateY(-50%); color: #6b7280;">
              <circle cx="11" cy="11" r="8"/>
              <path d="m21 21-4.35-4.35"/>
            </svg>
          </div>
          <div class="header-actions" style="display: flex; align-items: center; gap: 1rem;">
            <button class="header-btn user-btn" style="background: none; border: none; color: #000000; cursor: pointer; padding: 8px; border-radius: 50%; transition: background-color 0.3s ease; display: flex; align-items: center; justify-content: center;">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
                <circle cx="12" cy="7" r="4"/>
              </svg>
            </button>
            <button class="header-btn cart-btn" style="background: none; border: none; color: #000000; cursor: pointer; padding: 8px; border-radius: 50%; transition: background-color 0.3s ease; display: flex; align-items: center; justify-content: center; position: relative;">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <circle cx="8" cy="21" r="1"/>
                <circle cx="19" cy="21" r="1"/>
                <path d="M2.05 2.05h2l2.66 12.42a2 2 0 0 0 2 1.58h9.78a2 2 0 0 0 1.95-1.57L23 6H6"/>
              </svg>
              <span class="cart-count" style="position: absolute; top: 2px; right: 2px; background: #ef4444; color: white; border-radius: 50%; width: 16px; height: 16px; font-size: 10px; display: flex; align-items: center; justify-content: center; font-weight: bold;">3</span>
            </button>
            <button class="header-btn mobile-menu-btn" style="background: none; border: none; color: #000000; cursor: pointer; padding: 8px; display: none;">
              <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                <line x1="3" y1="6" x2="21" y2="6"/>
                <line x1="3" y1="12" x2="21" y2="12"/>
                <line x1="3" y1="18" x2="21" y2="18"/>
              </svg>
            </button>
          </div>
        </div>
      </div>
    </div>
    <style>
      @media (max-width: 768px) {
        .iluria-header .header-nav { display: none; }
        .iluria-header .header-search { display: none; }
        .iluria-header .mobile-menu-btn { display: flex !important; }
        .iluria-header .header-content { height: 60px; }
        .iluria-header .header-container { padding: 0 1rem; }
      }
      .nav-link:hover { color: #6366f1 !important; }
      .header-btn:hover { background-color: #f3f4f6 !important; }
      .search-input:focus { outline: none; border-color: #6366f1; background: white; }
    </style>
  </header>`,
  
  toolbarActions: [
    {
      type: 'header-config',
      title: 'Configurar Header',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
        <line x1="9" y1="9" x2="15" y2="9"/>
        <line x1="9" y1="15" x2="15" y2="15"/>
      </svg>`,
      condition: (element) => {
        return element?.getAttribute('data-component') === 'header'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'header-config',
      component: 'HeaderConfigEditor',
      fallback: 'DefaultConfigEditor'
    }
  ],
  
  initialization: {
    autoInit: true,
    reinitOnChange: true,
    scriptLoader: 'initializeHeaderComponent'
  },
  
  cleanup: {
    removeEditingStyles: false,
    preserveStyles: [
      'background-color', 'color', 'border-bottom', 'width', 'z-index', 'position',
      'max-width', 'margin', 'padding', 'display', 'align-items', 'justify-content',
      'height', 'gap', 'font-size', 'font-weight', 'text-decoration', 'transition',
      'border', 'border-radius', 'cursor', 'list-style', 'outline'
    ],
    childSelectors: [
      '.header-container', '.header-content', '.header-left', '.header-right',
      '.header-logo', '.logo-text', '.header-nav', '.nav-menu', '.nav-item', '.nav-link',
      '.header-search', '.search-input', '.search-icon', '.header-actions', '.header-btn',
      '.user-btn', '.cart-btn', '.cart-count', '.mobile-menu-btn'
    ]
  },
  
  detection: {
    priority: 90
  }
} 