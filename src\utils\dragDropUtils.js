export function setupDragAndDrop(container, doc, selectCallback, mode = 'edit') {
  // Only set up drag and drop if container exists
  if (!container) return
  
  // Verificar se é um componente protegido (header/footer)
  const isProtectedComponent = (element) => {
    if (!element) return false
    
    const component = element.querySelector('[data-component]')
    if (component) {
      const dataComponent = component.getAttribute('data-component')
      return dataComponent === 'header' || dataComponent === 'footer'
    }
    
    // Verificar no próprio elemento
    const dataComponent = element.getAttribute('data-component')
    return dataComponent === 'header' || dataComponent === 'footer'
  }
  
  const isProtected = isProtectedComponent(container)
  
  // Configurar drag apenas se não for componente protegido
  if (isProtected) {
    container.setAttribute('draggable', 'false')
    container.style.cursor = 'pointer' // Manter cursor de clique
    
    // Adicionar indicador visual de componente fixo APENAS no modo de edição
    if (mode === 'edit') {
      const existingIndicator = container.querySelector('.protected-component-indicator')
      if (!existingIndicator) {
        const indicator = doc.createElement('div')
        indicator.className = 'protected-component-indicator'
        indicator.style.cssText = `
          position: absolute;
          top: 8px;
          right: 8px;
          background: rgba(0, 0, 0, 0.7);
          color: white;
          padding: 4px 8px;
          border-radius: 4px;
          font-size: 12px;
          font-weight: 500;
          z-index: 1000;
          pointer-events: none;
          display: flex;
          align-items: center;
          gap: 4px;
        `
        indicator.innerHTML = `
          <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/>
            <path d="m3.3 7 8.7 5 8.7-5"/>
            <path d="M12 22V12"/>
          </svg>
          Fixo
        `
        container.style.position = 'relative'
        container.appendChild(indicator)
      }
    } else {
      // Remover indicador no modo de visualização
      const existingIndicator = container.querySelector('.protected-component-indicator')
      if (existingIndicator) {
        existingIndicator.remove()
      }
    }
  } else {
  container.setAttribute('draggable', 'true')
  }
  
  const dragStartHandler = (e) => {
    // Verificação adicional no momento do drag
    if (isProtected) {
      e.preventDefault()
      return false
    }
    
    doc.draggedElement = container
    container.classList.add('dragging')
    e.dataTransfer.setData('text/plain', container.id || 'component')
    e.dataTransfer.effectAllowed = 'move'
  }
  
  const dragEndHandler = () => {
    container.classList.remove('dragging')
    doc.draggedElement = null
    const dropZones = doc.querySelectorAll('.drop-zone')
    dropZones.forEach(zone => zone.classList.remove('drop-zone'))
  }
  
  const dragOverHandler = (e) => {
    e.preventDefault()
    if (doc.draggedElement === container) return
    
    // Não permitir drop em componentes protegidos
    if (isProtected) return
    
    const containers = doc.querySelectorAll('.component-container')
    containers.forEach(c => c.classList.remove('drop-zone'))
    container.classList.add('drop-zone')
  }
  
  const dragLeaveHandler = () => {
    container.classList.remove('drop-zone')
  }
  
  const dropHandler = (e) => {
    e.preventDefault()
    container.classList.remove('drop-zone')
    
    if (!doc.draggedElement) return
    
    // Não permitir drop em componentes protegidos
    if (isProtected) return
    
    const rect = container.getBoundingClientRect()
    const midY = rect.top + rect.height / 2
    const isAbove = e.clientY < midY
    
    if (isAbove) {
      container.parentNode.insertBefore(doc.draggedElement, container)
    } else {
      container.parentNode.insertBefore(doc.draggedElement, container.nextSibling)
    }
  }
  
  const clickHandler = (e) => {
    if (e.target.classList.contains('add-button')) return
    
    const element = container.querySelector('h1, h2, h3, p, img, button')
    if (element) {
      selectCallback(element)
    }
  }
  
  // Adicionar event listeners sempre (para clickHandler funcionar)
  container.addEventListener('click', clickHandler)
  
  // Adicionar drag handlers apenas se não for protegido
  if (!isProtected) {
  container.addEventListener('dragstart', dragStartHandler)
  container.addEventListener('dragend', dragEndHandler)
  }
  
  // Adicionar drop handlers sempre (para receber drops se não for protegido)
  container.addEventListener('dragover', dragOverHandler)
  container.addEventListener('dragleave', dragLeaveHandler)
  container.addEventListener('drop', dropHandler)
  
  // Return cleanup function
  return () => {
    container.removeEventListener('click', clickHandler)
    
    if (!isProtected) {
    container.removeEventListener('dragstart', dragStartHandler)
    container.removeEventListener('dragend', dragEndHandler)
    }
    
    container.removeEventListener('dragover', dragOverHandler)
    container.removeEventListener('dragleave', dragLeaveHandler)
    container.removeEventListener('drop', dropHandler)
    
    // Cleanup do indicador para componentes protegidos
    if (isProtected) {
      const indicator = container.querySelector('.protected-component-indicator')
      if (indicator) {
        indicator.remove()
      }
    }
  }
}

// Nova função específica para drag & drop de seções na sidebar
export function setupSectionDragAndDrop(sidebarContainer, sections, iframeDocument, onSectionReorder) {
  if (!sidebarContainer || !sections || !iframeDocument) return () => {}
  
  let draggedSectionIndex = null
  let draggedSection = null
  
  const findSectionItemElement = (target) => {
    return target.closest('.section-item')
  }
  
  const getSectionIndex = (sectionElement) => {
    const allSectionItems = Array.from(sidebarContainer.querySelectorAll('.section-item'))
    return allSectionItems.indexOf(sectionElement)
  }
  
  const isProtectedSection = (section) => {
    return section.dataComponent === 'header' || 
           section.dataComponent === 'footer' || 
           section.type === 'Header' || 
           section.type === 'Footer'
  }
  
  const dragStartHandler = (e) => {
    const sectionItem = findSectionItemElement(e.target)
    if (!sectionItem) return
    
    const sectionIndex = getSectionIndex(sectionItem)
    const section = sections[sectionIndex]
    
    // Não permitir arrastar seções protegidas
    if (isProtectedSection(section)) {
      e.preventDefault()
      return
    }
    
    draggedSectionIndex = sectionIndex
    draggedSection = section
    
    sectionItem.classList.add('dragging')
    e.dataTransfer.setData('text/plain', 'section')
    e.dataTransfer.effectAllowed = 'move'
    
    // Adicionar classe visual para outros itens
    const allItems = sidebarContainer.querySelectorAll('.section-item')
    allItems.forEach(item => {
      if (item !== sectionItem && !isProtectedSection(sections[getSectionIndex(item)])) {
        item.classList.add('drop-target')
      }
    })
  }
  
  const dragEndHandler = (e) => {
    const sectionItem = findSectionItemElement(e.target)
    if (sectionItem) {
      sectionItem.classList.remove('dragging')
    }
    
    // Remover classes visuais
    const allItems = sidebarContainer.querySelectorAll('.section-item')
    allItems.forEach(item => {
      item.classList.remove('drop-target', 'drop-above', 'drop-below')
    })
    
    draggedSectionIndex = null
    draggedSection = null
  }
  
  const dragOverHandler = (e) => {
    e.preventDefault()
    
    const sectionItem = findSectionItemElement(e.target)
    if (!sectionItem || draggedSectionIndex === null) return
    
    const targetIndex = getSectionIndex(sectionItem)
    const targetSection = sections[targetIndex]
    
    // Não permitir drop em seções protegidas
    if (isProtectedSection(targetSection)) return
    
    // Não fazer nada se for o mesmo elemento
    if (targetIndex === draggedSectionIndex) return
    
    // Remover classes anteriores
    const allItems = sidebarContainer.querySelectorAll('.section-item')
    allItems.forEach(item => item.classList.remove('drop-above', 'drop-below'))
    
    // Determinar se é antes ou depois
    const rect = sectionItem.getBoundingClientRect()
    const midY = rect.top + rect.height / 2
    const isAbove = e.clientY < midY
    
    if (isAbove) {
      sectionItem.classList.add('drop-above')
    } else {
      sectionItem.classList.add('drop-below')
    }
  }
  
  const dragLeaveHandler = (e) => {
    // Só remover se realmente saiu do container
    if (!sidebarContainer.contains(e.relatedTarget)) {
      const allItems = sidebarContainer.querySelectorAll('.section-item')
      allItems.forEach(item => item.classList.remove('drop-above', 'drop-below'))
    }
  }
  
  const dropHandler = (e) => {
    e.preventDefault()
    
    const sectionItem = findSectionItemElement(e.target)
    if (!sectionItem || draggedSectionIndex === null || !draggedSection) return
    
    const targetIndex = getSectionIndex(sectionItem)
    const targetSection = sections[targetIndex]
    
    // Não permitir drop em seções protegidas
    if (isProtectedSection(targetSection)) return
    
    // Não fazer nada se for o mesmo elemento
    if (targetIndex === draggedSectionIndex) return
    
    // Determinar posição do drop
    const rect = sectionItem.getBoundingClientRect()
    const midY = rect.top + rect.height / 2
    const isAbove = e.clientY < midY
    
    // Mover elemento no iframe
    const draggedElement = draggedSection.element
    const targetElement = targetSection.element
    
    if (!draggedElement || !targetElement || !draggedElement.parentNode) return
    
    try {
      if (isAbove) {
        // Inserir antes do elemento target
        targetElement.parentNode.insertBefore(draggedElement, targetElement)
      } else {
        // Inserir depois do elemento target
        if (targetElement.nextSibling) {
          targetElement.parentNode.insertBefore(draggedElement, targetElement.nextSibling)
        } else {
          targetElement.parentNode.appendChild(draggedElement)
        }
      }
      
      // Notificar que houve reordenação
      if (onSectionReorder) {
        onSectionReorder()
      }
      
    } catch (error) {
      console.error('Erro ao reordenar seções:', error)
    }
    
    // Limpar classes visuais
    const allItems = sidebarContainer.querySelectorAll('.section-item')
    allItems.forEach(item => item.classList.remove('drop-above', 'drop-below'))
  }
  
  // Adicionar event listeners ao container
  sidebarContainer.addEventListener('dragstart', dragStartHandler)
  sidebarContainer.addEventListener('dragend', dragEndHandler)
  sidebarContainer.addEventListener('dragover', dragOverHandler)
  sidebarContainer.addEventListener('dragleave', dragLeaveHandler)
  sidebarContainer.addEventListener('drop', dropHandler)
  
  // Configurar elementos como draggable
  const updateDraggableElements = () => {
    const sectionItems = sidebarContainer.querySelectorAll('.section-item')
    sectionItems.forEach((item, index) => {
      const section = sections[index]
      if (section && !isProtectedSection(section)) {
        item.setAttribute('draggable', 'true')
        item.style.cursor = 'grab'
        
        // Configurar handle específico
        const dragHandle = item.querySelector('.section-drag-handle')
        if (dragHandle) {
          dragHandle.style.cursor = 'grab'
        }
      } else {
        item.setAttribute('draggable', 'false')
        item.style.cursor = 'default'
      }
    })
  }
  
  // Configurar inicialmente
  updateDraggableElements()
  
  // Return cleanup function
  return () => {
    sidebarContainer.removeEventListener('dragstart', dragStartHandler)
    sidebarContainer.removeEventListener('dragend', dragEndHandler)
    sidebarContainer.removeEventListener('dragover', dragOverHandler)
    sidebarContainer.removeEventListener('dragleave', dragLeaveHandler)
    sidebarContainer.removeEventListener('drop', dropHandler)
    
    // Limpar draggable dos elementos
    const sectionItems = sidebarContainer.querySelectorAll('.section-item')
    sectionItems.forEach(item => {
      item.removeAttribute('draggable')
      item.style.cursor = ''
    })
  }
}