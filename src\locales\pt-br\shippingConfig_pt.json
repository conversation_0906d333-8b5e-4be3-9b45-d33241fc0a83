{"title": "Configurações de Frete", "description": "Aqui você pode definir valores que serão acrescentados ao preço do frete. Isto é útil para cobrir custos de embalagens, impostos etc.", "increaseShippingTitle": "Configurações de Frete", "increaseShippingSubtitle": "Adicione valores ou percentuais ao frete calculado", "pickupTitle": "Retirar na Loja", "pickupSubtitle": "Configure a opção de retirar a compra na loja", "correiosTitle": "Integração com correios", "correiosSubtitle": "Configure a integração com os Correios para oferecer SEDEX e PAC", "localShippingTitle": "Entrega personalizada", "localShippingSubtitle": "Configure entregas em regiões específicas", "addFixedValueLabel": "Deseja acrescentar um valor fixo ao preço do frete?", "fixedValueLabel": "Valor fixo a ser acrescentado ao frete:", "addPercentageLabel": "Deseja acrescentar um percentual ao preço do frete?", "percentageLabel": "Percentual de acréscimo:", "fixedValuePlaceholder": "Ex: 10,00", "percentagePlaceholder": "0", "saveButton": "<PERSON><PERSON>", "savedSuccess": "Configurações de frete salvas com sucesso", "savedError": "Erro ao salvar configurações de frete", "validationError": "Por favor, corrija os erros de validação antes de salvar.", "loadError": "Erro ao carregar configurações de frete.", "atLeastOneOption": "Selecione pelo menos uma opção de acréscimo.", "savedLocallyMessage": "Configurações de frete salvas localmente. Serão sincronizadas quando o servidor estiver disponível.", "offerPickupLabel": "Oferecer 'Retirar na Loja' como forma de envio na sua loja?", "pickupOption": "Oferecer 'Retirar na Loja' como forma de envio em sua loja?", "pickupDescription": "Esta opção de envio pode ser usada caso o cliente deseje retirar o pedido na loja, ateliê, escritório etc.", "pickupDetailedDescription": "Esta opção de envio pode ser usada caso o cliente deseje retirar o pedido na loja, ateliê, escritório etc. Todos os pedidos que tiverem esta opção de envio escolhida terão seus fretes definidos automaticamente com sendo zero, e o pagamento poderá ser feito online na loja no momento da compra.", "pickupDescriptionLabel": "Informe a descrição para a opção de retirada na loja. Exemplo: \"Retirar na loja\":", "pickupDescriptionPlaceholder": "Retirar na Loja em São Paulo - SP", "yesLabel": "<PERSON>m", "noLabel": "Não", "fixedValueRequired": "O valor fixo é obrigatório quando a opção está ativada.", "percentageRequired": "O percentual é obrigatório quando a opção está ativada.", "pickupDescriptionRequired": "A descrição da retirada é obrigatória quando a opção está ativada.", "cepRangeRequired": "É necessário adicionar pelo menos uma faixa de CEP.", "cepRangeFieldsRequired": "<PERSON><PERSON> as faixas de CEP devem estar completamente preenchidas.", "cepRangeInvalid": "Na região '{name}', o CEP inicial deve ser menor que o CEP final.", "shippingConfig": {"title": "Configurações de Frete", "subtitle": "Adicione valores ou percentuais ao frete calculado"}, "pickupConfig": {"subtitle": "Configure a opção de retirada do local"}, "zipCodeRestrictionConfig": {"title": "Configurar Restrições de Frete por Faixas de CEP", "subtitle": "Defina regiões que não recebem entrega ou têm restrições especiais"}, "correiosConfig": {"title": "Integração com correios", "subtitle": "Configure a integração com os Correios para oferecer SEDEX e PAC"}, "localShipping": {"title": "Entrega personalizada", "description": "Configure entregas em regiões específicas", "enableLabel": "Oferecer \"Entrega Local\" como método de envio na sua loja?", "descriptionLabel": "Digite a descrição para a opção de entrega local (exemplo: Motoboy):", "descriptionPlaceholder": "Motoboy", "uploadTitle": "Upload de arquivo", "uploadDescription": "Faça o upload de um arquivo CSV com as faixas de CEP para entrega local.", "uploadLabel": "Selecione um arquivo CSV", "uploadHint": "O arquivo deve conter as colunas: start_zipcode, end_zipcode, start_weight, end_weight, price, delivery_time", "uploadSuccess": "Arquivo enviado com sucesso!", "uploadError": "Erro ao enviar o arquivo. Verifique o formato e tente novamente.", "rangesTitle": "Faixas de CEP cadastradas", "startZipcode": "CEP inicial", "endZipcode": "CEP final", "weight": "Peso (kg)", "price": "Preço", "deliveryTime": "Prazo", "day": "dia", "days": "dias", "discountRules": "Regras de desconto", "discountDescription": "Configure as regras de desconto para entrega local.", "noDiscount": "<PERSON><PERSON> desconto", "freeShipping": "Entre<PERSON> g<PERSON>", "freeShippingMinValue": "Entrega grátis para pedidos acima de um valor", "discountAboveValue": "Desconto na entrega para pedidos acima de um valor", "minimumOrderValue": "Valor mínimo do pedido", "percentageDiscount": "Percentual de desconto", "noDiscountDescription": "Pedidos serão entregues normalmente sem desconto.", "freeShippingDescription": "Pedidos acima de um valor terão frete grátis.", "freeShippingMinValueDescription": "Pedidos acima de um valor terão frete grátis.", "minimumOrderValueDescription": "Pedidos acima de um valor terão desconto na entrega.", "percentageDiscountDescription": "Pedidos acima de um valor terão desconto na entrega.", "minOrderValue": "Valor mínimo do pedido", "discountPercentage": "Desconto", "dragAndDrop": "Arraste e solte o arquivo CSV com as faixas de CEP para entrega local aqui ou clique para carregar.", "chooseFile": "Escolher arquivo"}, "zipCodeRestriction": {"title": "Restringir Entregas por Região", "subtitle": "Configure quais regiões serão atendidas", "description": "Aqui você pode definir quais faixas de CEP serão atendidas pela loja. Caso não deseje restringir a área de entrega, deixe esta opção como 'Não'. Caso deseje restringir a entrega a apenas algumas faixas de CEPs específicos, você pode habilitar esta opção e especificar as faixas de CEPs abaixo.", "toggleTitle": "Restringir as entregas por faixas de CEP", "enableLabel": "<PERSON><PERSON><PERSON> habil<PERSON> as faixas de CEPs?", "rangesDescription": "Dê um nome para a região atendida e especifique um CEP inicial e final. Pedidos dentro das faixas de CEP serão atendidos normalmente.", "regionNameLabel": "Nome da região:", "startZipLabel": "CEP inicial:", "endZipLabel": "CEP final:", "regionPlaceholder": "Ex: Zona Norte", "zipPlaceholder": "00000-000", "removeRange": "Remover faixa", "addRange": "Adicionar faixa de CEP", "completeAllFields": "Por favor, preencha todos os campos obrigatórios antes de adicionar uma nova faixa de CEP.", "atLeastOneRange": "É necessário ter pelo menos uma faixa de CEP cadastrada.", "fieldsRequired": "Todos os campos são obrigatórios. Preencha ou remova as linhas em branco."}, "correios": {"description": "Configure a integração com os Correios para oferecer SEDEX e PAC como opções de envio em sua loja.", "offerSedex": "Oferecer SEDEX como forma de envio em sua loja", "offerPac": "Oferecer PAC como forma de envio na sua loja", "includeInsurance": "Deseja incluir o preço do seguro no frete", "showDeliveryTime": "Deseja exibir o prazo estimado da entrega", "addFixedValue": "Deseja acrescentar um valor fixo ao preço do frete", "fixedValueAmount": "Valor a acrescentar:", "addPercentageValue": "Deseja acrescentar um percentual ao preço do frete", "percentageAmount": "Percentual de acréscimo:", "hasContract": "Tem contrato com os Correios?", "credentials": "Credenciais dos Correios", "user": "Usuário:", "apiKey": "API KEY:"}}