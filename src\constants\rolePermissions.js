/**
 * Mapeamento de roles para permissões
 * 
 * Este arquivo espelha a lógica de permissões do backend sem precisar fazer API calls.
 * As permissões são extraídas diretamente do roleId presente no token JWT.
 * 
 * IMPORTANTE: Este mapeamento deve ser mantido sincronizado com o backend!
 */

import { PERMISSION_CODES } from './permissions';

/**
 * Mapeamento de roles do sistema para suas respectivas permissões
 * Baseado no sistema dinâmico do backend RolePermissions
 */
export const ROLE_PERMISSIONS = {
  // Admin da loja - acesso total
  'admin': [
    // Store
    PERMISSION_CODES.STORE_ADMIN,
    PERMISSION_CODES.STORE_SETTINGS,
    PERMISSION_CODES.STORE_VIEW,
    
    // Products  
    PERMISSION_CODES.PRODUCT_CREATE,
    PERMISSION_CODES.PRODUCT_EDIT,
    PERMISSION_CODES.PRODUCT_DELETE,
    PERMISSION_CODES.PRODUCT_VIEW,
    PERMISSION_CODES.PRODUCT_CATEGORY_MANAGE,
    
    // Orders
    PERMISSION_CODES.ORDER_VIEW,
    PERMISSION_CODES.ORDER_EDIT,
    PERMISSION_CODES.ORDER_CANCEL,
    PERMISSION_CODES.ORDER_EXPORT,
    PERMISSION_CODES.ORDER_STATUS_CHANGE,
    
    // Customers
    PERMISSION_CODES.CUSTOMER_VIEW,
    PERMISSION_CODES.CUSTOMER_EDIT,
    PERMISSION_CODES.CUSTOMER_DELETE,
    PERMISSION_CODES.CUSTOMER_EXPORT,
    
    // Team
    PERMISSION_CODES.TEAM_VIEW,
    PERMISSION_CODES.TEAM_EDIT,
    PERMISSION_CODES.TEAM_INVITE,
    PERMISSION_CODES.TEAM_REMOVE,
    
    // Analytics
    PERMISSION_CODES.ANALYTICS_VIEW,
    PERMISSION_CODES.REPORTS_GENERATE,
    PERMISSION_CODES.REPORTS_EXPORT,

    // Marketing
    PERMISSION_CODES.PROMOTION_CREATE,
    PERMISSION_CODES.PROMOTION_EDIT,
    PERMISSION_CODES.PROMOTION_DELETE,
    PERMISSION_CODES.PROMOTION_VIEW,

    // Shipping
    PERMISSION_CODES.SHIPPING_SETTINGS,
    PERMISSION_CODES.SHIPPING_VIEW,

    // Financial
    PERMISSION_CODES.FINANCIAL_VIEW,
    PERMISSION_CODES.FINANCIAL_REPORTS,
    PERMISSION_CODES.FINANCIAL_EXPORT
  ],

  // Gerente - acesso de gestão sem administração crítica
  'manager': [
    PERMISSION_CODES.STORE_VIEW,
    PERMISSION_CODES.PRODUCT_CREATE,
    PERMISSION_CODES.PRODUCT_EDIT,
    PERMISSION_CODES.PRODUCT_VIEW,
    PERMISSION_CODES.PRODUCT_CATEGORY_MANAGE,
    PERMISSION_CODES.ORDER_VIEW,
    PERMISSION_CODES.ORDER_EDIT,
    PERMISSION_CODES.ORDER_STATUS_CHANGE,
    PERMISSION_CODES.CUSTOMER_VIEW,
    PERMISSION_CODES.CUSTOMER_EDIT,
    PERMISSION_CODES.TEAM_VIEW,
    PERMISSION_CODES.ANALYTICS_VIEW,
    PERMISSION_CODES.PROMOTION_CREATE,
    PERMISSION_CODES.PROMOTION_EDIT,
    PERMISSION_CODES.PROMOTION_VIEW
  ],

  // Operador - operações do dia a dia
  'operator': [
    PERMISSION_CODES.STORE_VIEW,
    PERMISSION_CODES.PRODUCT_VIEW,
    PERMISSION_CODES.PRODUCT_EDIT,
    PERMISSION_CODES.ORDER_VIEW,
    PERMISSION_CODES.ORDER_EDIT,
    PERMISSION_CODES.ORDER_STATUS_CHANGE,
    PERMISSION_CODES.CUSTOMER_VIEW,
    PERMISSION_CODES.CUSTOMER_EDIT
  ],

  // Vendedor - foco em vendas e clientes
  'seller': [
    PERMISSION_CODES.STORE_VIEW,
    PERMISSION_CODES.PRODUCT_VIEW,
    PERMISSION_CODES.ORDER_VIEW,
    PERMISSION_CODES.CUSTOMER_VIEW,
    PERMISSION_CODES.CUSTOMER_EDIT,
    PERMISSION_CODES.ANALYTICS_VIEW
  ],

  // Visualizador - apenas leitura
  'viewer': [
    PERMISSION_CODES.STORE_VIEW,
    PERMISSION_CODES.PRODUCT_VIEW,
    PERMISSION_CODES.ORDER_VIEW,
    PERMISSION_CODES.CUSTOMER_VIEW,
    PERMISSION_CODES.ANALYTICS_VIEW
  ],

  // Suporte - acesso limitado para ajudar clientes
  'support': [
    PERMISSION_CODES.STORE_VIEW,
    PERMISSION_CODES.ORDER_VIEW,
    PERMISSION_CODES.CUSTOMER_VIEW,
    PERMISSION_CODES.CUSTOMER_EDIT
  ]
};

/**
 * Obtém as permissões para um role específico
 * @param {string} roleId - ID do role (ex: 'admin', 'manager', 'operator')
 * @returns {string[]} Array de códigos de permissão
 */
export function getPermissionsForRole(roleId) {
  if (!roleId || typeof roleId !== 'string') {
    console.warn('Role ID inválido:', roleId);
    return [];
  }

  // Normaliza o roleId (remove espaços, converte para lowercase)
  const normalizedRoleId = roleId.trim().toLowerCase();
  
  const permissions = ROLE_PERMISSIONS[normalizedRoleId];
  
  if (!permissions) {
    console.warn(`Role '${roleId}' não encontrado no mapeamento de permissões`);
    return [];
  }

  return [...permissions]; // Retorna cópia para evitar mutação
}

/**
 * Verifica se um role possui uma permissão específica
 * @param {string} roleId - ID do role
 * @param {string} permissionCode - Código da permissão
 * @returns {boolean}
 */
export function roleHasPermission(roleId, permissionCode) {
  const permissions = getPermissionsForRole(roleId);
  return permissions.includes(permissionCode);
}

/**
 * Obtém todos os roles disponíveis
 * @returns {string[]} Array de IDs de roles
 */
export function getAvailableRoles() {
  return Object.keys(ROLE_PERMISSIONS);
}

/**
 * Obtém informações detalhadas sobre um role
 * @param {string} roleId - ID do role
 * @returns {Object} Informações do role
 */
export function getRoleInfo(roleId) {
  const roleDescriptions = {
    'admin': 'Administrador da Loja',
    'manager': 'Gerente',
    'operator': 'Operador',
    'seller': 'Vendedor',
    'viewer': 'Visualizador',
    'support': 'Suporte'
  };

  const permissions = getPermissionsForRole(roleId);
  
  return {
    id: roleId,
    name: roleDescriptions[roleId] || roleId,
    permissions,
    permissionCount: permissions.length
  };
}