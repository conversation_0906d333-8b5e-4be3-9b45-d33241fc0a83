import { ref, computed, onMounted, onUnmounted } from 'vue'
import { useAuthStore } from '@/stores/auth.store'
import userSessionService from '@/services/userSession.service'
import { useToast } from '@/services/toast.service'
import { useGeolocation } from '@/composables/useGeolocation'

/**
 * Composable para gerenciamento de sessões de usuário
 */
export function useUserSessions() {
    const authStore = useAuthStore()
    const toast = useToast()
    const { areLocationsSuspicious, isValidLocation } = useGeolocation()

    // Estado reativo
    const sessions = ref([])
    const loading = ref(false)
    const error = ref(null)
    
    // Estados de operações
    const terminatingSession = ref(null)
    const terminatingAll = ref(false)
    
    
    // Computed
    const activeSessions = computed(() =>
        sessions.value
            .map(session => userSessionService.formatSessionForDisplay(session))
            .sort((a, b) => {
                // Sessão atual sempre primeiro
                if (a.isCurrent && !b.isCurrent) return -1
                if (!a.isCurrent && b.isCurrent) return 1

                // Para as outras sessões, ordena por última atividade (mais recente primeiro)
                return new Date(b.lastActivity) - new Date(a.lastActivity)
            })
    )
    
    const currentSession = computed(() => 
        sessions.value.find(session => session.isCurrent)
    )
    
    const otherSessions = computed(() => 
        sessions.value.filter(session => !session.isCurrent)
    )
    
    const hasMultipleSessions = computed(() => 
        activeSessions.value.length > 1
    )
    
    /**
     * Carrega sessões ativas do usuário
     */
    async function loadSessions() {
        if (!authStore.userLoggedIn) {
            sessions.value = []
            return
        }
        
        loading.value = true
        error.value = null
        
        try {
            const data = await userSessionService.getActiveSessions()
            sessions.value = data
        } catch (err) {
            error.value = err.message || 'Erro ao carregar sessões'
            console.error('Erro ao carregar sessões:', err)
            
            // Mostra toast de erro apenas se não for erro de autenticação
            if (!err.message?.includes('Sessão expirada')) {
                toast.addToast(error.value, 'error')
            }
        } finally {
            loading.value = false
        }
    }
    
    /**
     * Termina uma sessão específica
     */
    async function terminateSession(sessionId) {
        if (!sessionId || terminatingSession.value === sessionId) return
        
        // Verifica se pode terminar a sessão
        const session = sessions.value.find(s => s.sessionId === sessionId)
        if (session && !userSessionService.canTerminateSession(sessionId, session)) {
            toast.addToast('Não é possível encerrar a sessão atual', 'warn')
            return
        }
        
        terminatingSession.value = sessionId
        
        try {
            await authStore.invalidateSession(sessionId)
            
            // Remove da lista local
            sessions.value = sessions.value.filter(s => s.sessionId !== sessionId)
            
            toast.addToast('Sessão encerrada com sucesso', 'success')
            
            // Força atualização da lista
            await loadSessions()
        } catch (error) {
            console.error('Erro ao encerrar sessão:', error)
            toast.addToast(error.message || 'Erro ao encerrar sessão', 'error')
        } finally {
            terminatingSession.value = null
        }
    }
    
    /**
     * Termina todas as outras sessões (mantém apenas a atual)
     */
    async function terminateAllOtherSessions() {
        if (terminatingAll.value || !hasMultipleSessions.value) return
        
        terminatingAll.value = true
        
        try {
            await userSessionService.terminateAllOtherSessions()
            
            // Mantém apenas a sessão atual
            sessions.value = sessions.value.filter(s => s.isCurrent)
            
            toast.addToast('Todas as outras sessões foram encerradas', 'success')
            
            // Força atualização da lista
            await loadSessions()
        } catch (error) {
            console.error('Erro ao encerrar sessões:', error)
            toast.addToast(error.message || 'Erro ao encerrar sessões', 'error')
        } finally {
            terminatingAll.value = false
        }
    }
    
    /**
     * Atualiza atividade da sessão atual
     */
    function updateCurrentSessionActivity() {
        authStore.updateSessionActivity()
    }
    
    /**
     * Obtém informações do dispositivo atual
     */
    function getCurrentDeviceInfo() {
        return userSessionService.getDeviceInfo()
    }
    
    /**
     * Formata tempo relativo para exibição
     */
    function formatSessionTime(dateString) {
        return userSessionService.formatRelativeTime(dateString)
    }
    
    /**
     * Formata informações da sessão para exibição
     */
    function formatSessionInfo(session) {
        if (session.deviceInfo && session.browserInfo) {
            return `${session.deviceInfo} • ${session.browserInfo}`
        }
        
        // Fallback para sessões antigas
        return session.device && session.browser 
            ? `${session.device} • ${session.browser}`
            : 'Dispositivo desconhecido'
    }
    
    /**
     * Verifica se uma sessão está expirada
     */
    function isSessionExpired(session) {
        if (!session.expiresAt) return false
        return new Date() > new Date(session.expiresAt)
    }
    


    /**
     * Obtém informações da sessão atual
     */
    function getCurrentSessionInfo() {
        return authStore.getCurrentSessionInfo()
    }

    /**
     * Verifica se há sessões suspeitas (múltiplas sessões de locais diferentes)
     */
    const suspiciousSessions = computed(() => {
        if (activeSessions.value.length < 2) return []

        const currentSession = activeSessions.value.find(s => s.isCurrent)
        if (!currentSession) return []

        return activeSessions.value.filter(session => {
            if (session.isCurrent) return false

            // Se tem dados de geolocalização estruturados, usa análise avançada
            if (session.locationInfo && currentSession.locationInfo &&
                isValidLocation(session.locationInfo) && isValidLocation(currentSession.locationInfo)) {
                return areLocationsSuspicious(currentSession.locationInfo, session.locationInfo)
            }

            // Fallback para comparação simples de strings
            const currentLocation = currentSession.locationDisplay || ''
            const sessionLocation = session.locationDisplay || ''

            // Se as localizações são muito diferentes, marca como suspeita
            return currentLocation !== sessionLocation &&
                   sessionLocation !== 'Localização desconhecida' &&
                   currentLocation !== 'Localização desconhecida' &&
                   !sessionLocation.includes('Localhost')
        })
    })

    /**
     * Obtém estatísticas das sessões
     */
    const sessionStats = computed(() => {
        const total = activeSessions.value.length
        const current = activeSessions.value.filter(s => s.isCurrent).length
        const others = total - current
        const mobile = activeSessions.value.filter(s => s.isMobile === true).length
        const desktop = activeSessions.value.filter(s => s.isDesktop === true).length
        const tablet = activeSessions.value.filter(s => s.isTablet === true).length

        return {
            total,
            current,
            others,
            devices: { mobile, desktop, tablet },
            suspicious: suspiciousSessions.value.length,
            locations: [...new Set(activeSessions.value.map(s => s.locationDisplay).filter(Boolean))]
        }
    })
    
    // Lifecycle
    onMounted(() => {
        if (authStore.userLoggedIn) {
            loadSessions()
        }
    })
    
    // Retorna API do composable
    return {
        // Estado básico
        sessions,
        loading,
        error,
        terminatingSession,
        terminatingAll,
        
        
        // Computed básicos
        activeSessions,
        currentSession,
        otherSessions,
        hasMultipleSessions,
        
        // Computed avançados
        suspiciousSessions,
        sessionStats,
        
        // Métodos básicos
        loadSessions,
        terminateSession,
        terminateAllOtherSessions,
        updateCurrentSessionActivity,
        getCurrentDeviceInfo,
        formatSessionTime,
        formatSessionInfo,
        isSessionExpired,
        
        getCurrentSessionInfo
    }
}