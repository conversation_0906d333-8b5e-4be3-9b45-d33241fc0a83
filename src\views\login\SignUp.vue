<template>
  <LoginPage>
    <div v-if="signupId && email">
      <!-- Email display as text -->
      <div class="mb-6 text-center">
        <h2 class="text-xl font-semibold text-gray-900 mb-2">
          {{ $t('signup.completeRegistrationFor') }}
        </h2>
        <p class="text-lg text-gray-700 font-medium">{{ email }}</p>
      </div>

      <form @submit.prevent="signUp">

        <div class="mb-4">
          <IluriaInputText
            name="firstName"
            v-model="firstName"
            :label="$t('signup.firstName')"
            class="signup-input"
          />
        </div>

        <div class="mb-4">
          <IluriaInputText
            name="lastName"
            v-model="lastName"
            :label="$t('signup.lastName')"
            class="signup-input"
          />
        </div>

        <div class="mb-4 relative">
          <IluriaInputText
            name="password"
            :type="passwordType"
            v-model="password"
            :label="$t('signup.password')"
            inputClass="bg-white pr-10"
            class="signup-input"
          >
            <template #append>
              <ShowHidePasswordButton
                :passwordInputType="passwordType"
                @passwordTypeChange="togglePasswordVisibility('password')"
              />
            </template>
          </IluriaInputText>
        </div>

        <div class="mb-4 relative">
          <IluriaInputText
            name="confirmPassword"
            :type="confirmPasswordType"
            v-model="confirmPassword"
            :label="$t('signup.confirmPassword')"
            inputClass="bg-white pr-10"
            class="signup-input"
          >
            <template #append>
              <ShowHidePasswordButton
                :passwordInputType="confirmPasswordType"
                @passwordTypeChange="togglePasswordVisibility('confirmPassword')"
              />
            </template>
          </IluriaInputText>
        </div>

        <IluriaButton
          type="submit"
          :disabled="loading"
          class="w-full"
        >
          {{ loading ? $t('signup.loading') : $t('signup.submit') }}
        </IluriaButton>
      </form>

      <!-- Status Messages -->
      <div v-if="loading" class="text-center mt-4 text-red-500">{{ $t("login.sending") }}</div>
      <div v-if="signupStatus === 'success'" class="mt-4 p-2 bg-green-100 text-green-800 rounded-md">
        {{ $t('signup.success') }}
      </div>
      <div v-if="signupStatus === 'error'" class="mt-4 p-2 bg-red-100 text-red-800 rounded-md">
        {{ error || $t('signup.invalidLink') }}
      </div>
    </div>

    <div v-else>
      <p class="p-2 bg-red-100 text-red-800 rounded-md">{{ $t('signup.invalidLink') }}</p>
    </div>
  </LoginPage>
</template>


<script setup>
import { ref, onMounted, computed } from 'vue';
import { useToast } from '@/services/toast.service';
import { useAuthStore } from '@/stores/auth.store';
import { useRoute } from 'vue-router';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import ShowHidePasswordButton from '@/components/login/ShowHidePasswordButton.vue';
import { useI18n } from 'vue-i18n';
import { useRouter } from 'vue-router';
import LoginPage from '@/components/login/LoginPage.vue';

import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const router = useRouter();
const { t } = useI18n();
const { addToast } = useToast();
const authStore = useAuthStore();
const route = useRoute();

const firstName = ref('');
const lastName = ref('');
const password = ref('');
const confirmPassword = ref('');
const loading = ref(false);
const error = ref('');
const signupStatus = ref('idle');
const signupId = ref(null);
const email = ref(null);
const passwordType = ref('password');
const confirmPasswordType = ref('password');


onMounted(() => {
  const { signUpCode: signUpCode, email: emailParam } = route.query;

  if (signUpCode && emailParam) {
    signupId.value = signUpCode;
    email.value = emailParam;
  } else {
    error.value = t('signup.invalidLink');
  }
});

const togglePasswordVisibility = (field) => {
  if (field === 'password') {
    passwordType.value = passwordType.value === 'password' ? 'text' : 'password';
  } else if (field === 'confirmPassword') {
    confirmPasswordType.value = confirmPasswordType.value === 'password' ? 'text' : 'password';
  }
};

const signUp = async () => {


  loading.value = true;
  error.value = '';
  signupStatus.value = 'idle';

  try {
    const response = await authStore.verifySignup(
      signupId.value,
      email.value,
      password.value,
      firstName.value,
      lastName.value
    );



    if (response.success) {
      signupStatus.value = 'success';
      addToast(t('signup.success'), 'success');
      router.push('/login');
    } else {
      signupStatus.value = 'error';
      error.value = response.message || t('UnknownError');
      addToast(response.message || t('UnknownError'), 'error');
    }
  } catch (err) {
    console.error('Signup error:', err);
    signupStatus.value = 'error';
    error.value = err.message || t('UnknownError');
    addToast(err.message || t('UnknownError'), 'error');
  } finally {
    loading.value = false;
  }
};
</script>

<style scoped>
.error {
  border-color: red !important;
}

.error-message {
  color: red;
  font-size: 0.9em;
}

/* Force labels to be black in signup form */
:deep(.signup-input label) {
  color: #000000 !important;
}

:deep(.signup-input .iluria-label) {
  color: #000000 !important;
}

/* Ensure text color is black for all label elements */
:deep(.signup-input) label,
:deep(.signup-input) .label,
:deep(.signup-input) .form-label {
  color: #000000 !important;
}

/* Additional specificity for various label selectors */
:deep(.signup-input) .p-float-label label,
:deep(.signup-input) .field label,
:deep(.signup-input) .input-label,
:deep(.signup-input) span.label {
  color: #000000 !important;
}

/* Override any inherited text colors */
.signup-input :deep(*) {
  --label-color: #000000;
}

.signup-input :deep(label),
.signup-input :deep(.label) {
  color: #000000 !important;
  --text-color: #000000 !important;
}
</style>
