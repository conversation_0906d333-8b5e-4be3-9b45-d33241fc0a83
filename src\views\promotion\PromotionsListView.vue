<template>
  <div class="promotions-list-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="t('promotions.promotionsTitle')"
      :subtitle="t('promotions.promotionsSubtitle')"
      :showSearch="hasPromotions"
      :showAdd="true"
      :addText="t('promotions.add')"
      :searchPlaceholder="t('promotions.searchPlaceholder')"
      @search="handleSearch"
      @add-click="goToAddPromotion"
    />

    <!-- Table Wrapper -->
    <div class="table-wrapper">
      <IluriaDataTable
        :value="sortedPromotions"
        :columns="mainTableColumns"
        :loading="loading"
        dataKey="id"
        class="promotions-table"
      >
        <template #header-name>
            <span class="column-header" data-sortable="true" @click="toggleSort('name')">
                {{ t('promotions.nameHeader') }}
                <span v-if="sortField === 'name'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
            </span>
        </template>
        <template #header-type>
            <span class="column-header" data-sortable="true" @click="toggleSort('type')">
                {{ t('promotions.typeHeader') }}
                <span v-if="sortField === 'type'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
            </span>
        </template>
        <template #header-discountValue>
            <span class="column-header" data-sortable="true" @click="toggleSort('discountValue')">
                {{ t('promotions.discountValueHeader') }}
                <span v-if="sortField === 'discountValue'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
            </span>
        </template>
        <template #header-active>
            <span class="column-header" data-sortable="true" @click="toggleSort('active')">
                {{ t('promotions.activeHeader') }}
                <span v-if="sortField === 'active'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
            </span>
        </template>
        <template #header-combinePromos>
            <span class="column-header" data-sortable="true" @click="toggleSort('combinePromos')">
                {{ t('promotions.combinePromosHeader') }}
                <span v-if="sortField === 'combinePromos'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
            </span>
        </template>
        <template #header-createdAt>
            <span class="column-header" data-sortable="true" @click="toggleSort('createdAt')">
                {{ t('promotions.createdHeader') }}
                <span v-if="sortField === 'createdAt'">{{ sortOrder === 1 ? '▲' : '▼' }}</span>
            </span>
        </template>
        <template #header-actions>
            <span class="column-header">{{ t('promotions.actionsHeader') }}</span>
        </template>

        <!-- Column Templates -->
        <template #column-name="{ data }">
            <div class="promotion-name">
              <span class="name-text">{{ data.name }}</span>
              <span v-if="data.description" class="description-text">{{ data.description }}</span>
            </div>
        </template>
        <template #column-type="{ data }">
            <div class="type-badge-container">
              <span class="type-badge" :class="`type-${data.type?.toLowerCase()}`">
                {{ formatPromotionType(data.type) }}
              </span>
            </div>
        </template>
        <template #column-discountValue="{ data }">
            <div class="discount-value">
              <span class="value-text">
                {{ formatDiscountValue(data) }}
              </span>
            </div>
        </template>
        <template #column-active="{ data }">
            <span class="status-badge" :class="data.active ? 'status-active' : 'status-inactive'">
              {{ data.active ? t('promotions.yes') : t('promotions.no') }}
            </span>
        </template>
        <template #column-combinePromos="{ data }">
            <span class="combine-badge" :class="data.combinePromos ? 'combine-yes' : 'combine-no'">
              {{ data.combinePromos ? t('promotions.yes') : t('promotions.no') }}
            </span>
        </template>
        <template #column-createdAt="{ data }">
            <span class="date-text">{{ formatDate(data.createdAt) }}</span>
        </template>
        <template #column-actions="{ data }">
            <div class="action-buttons">
              <IluriaButton 
                color="text-primary" 
                size="small" 
                :hugeIcon="PencilEdit01Icon" 
                @click="editPromotion(data.id)"
                :title="t('promotions.editPromotion')"
              />
              <IluriaButton 
                color="text-danger" 
                size="small" 
                :hugeIcon="Delete01Icon" 
                @click="confirmDelete(data)"
                :title="t('promotions.deletePromotion')"
              />
            </div>
        </template>
      
        <!-- Empty State -->
      <template #empty>
        <div class="empty-state">
          <div class="empty-icon">
            <DiscountIcon class="w-12 h-12" />
          </div>
          <h3 class="empty-title">{{ t('promotions.noPromotions') }}</h3>
          <p class="empty-description">{{ t('promotions.noPromotionsDescription') }}</p>
          <IluriaButton 
            color="dark"
            :hugeIcon="DiscountIcon"
            @click="goToAddPromotion"
            class="mt-4"
          >
            {{ t('promotions.createFirstPromotion') }}
          </IluriaButton>
          </div>
        </template>

        <!-- Loading State -->
        <template #loading>
          <div class="loading-state">
            <div class="loading-spinner"></div>
            <span>{{ t('promotions.loadingPromotions') }}</span>
          </div>
        </template>
      </IluriaDataTable>
    </div>
    
    <!-- Pagination -->
    <div class="pagination-container" v-if="totalPages > 0">
      <IluriaPagination 
        :current-page="currentPage"
        :total-pages="totalPages"
        @go-to-page="changePage"
      />
    </div>

    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import { useToast } from 'primevue/usetoast'
import PromotionsService from '@/services/promotions.service'  
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaDataTable from '@/components/iluria/IluriaDataTable.vue'
import { 
  DiscountIcon,
  Delete01Icon, 
  PencilEdit01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const router = useRouter()
const { t } = useI18n()
const toast = useToast()

// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const promotions = ref([]) 
const loading = ref(true) 
const currentPage = ref(0) 
const totalPages = ref(0) 
const filters = ref({ filter: '' }) 
const hasPromotions = ref(false)

const mainTableColumns = [
    { field: 'name', headerClass: 'col-flex', class: 'col-flex' },
    { field: 'type', headerClass: 'col-medium', class: 'col-medium' },
    { field: 'discountValue', headerClass: 'col-medium', class: 'col-medium' },
    { field: 'active', headerClass: 'col-small', class: 'col-small' },
    { field: 'combinePromos', headerClass: 'col-medium', class: 'col-medium' },
    { field: 'createdAt', headerClass: 'col-date', class: 'col-date' },
    { field: 'actions', headerClass: 'col-actions', class: 'col-actions' }
];

const sortField = ref(null);
const sortOrder = ref(null);

const toggleSort = (field) => {
    if (sortField.value === field) {
        sortOrder.value *= -1;
    } else {
        sortField.value = field;
        sortOrder.value = 1;
    }
};

const sortedPromotions = computed(() => {
    if (!sortField.value) {
        return promotions.value;
    }
    return [...promotions.value].sort((a, b) => {
        const valA = a[sortField.value];
        const valB = b[sortField.value];

        if (typeof valA === 'boolean') {
            return (valA === valB ? 0 : valA ? -1 : 1) * sortOrder.value;
        }

        if (sortField.value === 'createdAt') {
            return (new Date(valA).getTime() - new Date(valB).getTime()) * sortOrder.value;
        }

        if (valA < valB) return -1 * sortOrder.value;
        if (valA > valB) return 1 * sortOrder.value;
        return 0;
    });
});

const loadPromotions = async () => {
  loading.value = true
  try {
    const response = await PromotionsService.getPromotions(filters.value.filter, currentPage.value, 10)
    promotions.value = response.content
    totalPages.value = response.totalPages
    
    hasPromotions.value = response.content.length > 0
  } catch (error) {
    console.error('Error loading promotions:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('promotions.error'), 
      detail: t('promotions.loadError'), 
      life: 3000 
    })
  } finally {
    loading.value = false
  }
}

const formatDate = (dateString) => {
  if (!dateString) return ''
  const date = new Date(dateString)
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

const formatPromotionType = (type) => {
  if (!type) return ''
  
  const typeMap = {
    'SIMPLE_DISCOUNT': t('promotions.types.simpleDiscount'),
    'PROGRESSIVE_DISCOUNT': t('promotions.types.progressiveDiscount'),
    'FREE_SHIPPING': t('promotions.types.freeShipping'),
    'GIFT': t('promotions.types.gift'),
    'CROSS_SELLING': t('promotions.types.crossSelling'),
    'FIRST_PURCHASE': t('promotions.types.firstPurchase')
  }
  
  return typeMap[type] || type
}

const formatDiscountValue = (promotion) => {
  if (!promotion.discountValue) return '-'
  
  if (promotion.type === 'FREE_SHIPPING') {
    return t('promotions.freeShippingValue')
  }
  
  if (promotion.discountType === 'PERCENTAGE_TOTAL') {
    return `${promotion.discountValue}%`
  } else {
    return new Intl.NumberFormat('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    }).format(promotion.discountValue)
  }
}

const changePage = (page) => {
  currentPage.value = page
  loadPromotions()
}

const editPromotion = (id) => {
  router.push(`/promotions/${id}`)
}

// Modal control functions
const showConfirmDanger = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('delete')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const confirmDelete = (promotion) => {
  showConfirmDanger(
    t('promotions.confirmDeleteMessage', { name: promotion.name }),
    t('promotions.confirmDeleteTitle'),
    () => deletePromotion(promotion.id)
  )
}

const deletePromotion = async (id) => {
  try {
    await PromotionsService.deletePromotion(id)
    toast.add({ 
      severity: 'success', 
      summary: t('promotions.success'), 
      detail: t('promotions.deleteSuccess'), 
      life: 3000 
    })
    loadPromotions()
  } catch (error) {
    console.error('Error deleting promotion:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('promotions.error'), 
      detail: t('promotions.deleteError'), 
      life: 3000 
    })
  }
}

const goToAddPromotion = () => {
  router.push('/promotions/new') 
}

let searchTimeout = null
const debouncedSearch = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    currentPage.value = 0
    loadPromotions()
  }, 400)
}

const handleSearch = (searchValue) => {
  filters.value.filter = searchValue
  debouncedSearch()
}

onMounted(() => {
  loadPromotions()
})
</script>

<style scoped>
.promotions-list-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
}

/* Header */
.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 32px;
  padding-bottom: 24px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 0.3s ease;
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 0.3s ease;
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 0.3s ease;
}

.header-actions {
  display: flex;
  align-items: center;
  gap: 16px;
}

.search-input {
  min-width: 250px;
}

/* Table Styles */
.table-wrapper {
  margin-bottom: 24px;
}

/* Column width definitions */
:deep(.col-flex) { width: auto; text-align: left; }
:deep(.col-medium) { width: 180px; text-align: center; }
:deep(.col-small) { width: 100px; text-align: center; }
:deep(.col-date) { width: 150px; text-align: center; }
:deep(.col-actions) { width: 120px; text-align: center; }

/* General table styling */
:deep(.promotions-table) {
  border-radius: 8px;
  overflow: hidden;
  box-shadow: var(--iluria-shadow-sm);
  background: var(--iluria-color-surface) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.promotions-table .p-datatable-table) {
  table-layout: auto;
  width: 100%;
}

:deep(.promotions-table .p-datatable-thead > tr > th) {
  background: var(--iluria-color-sidebar-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  font-weight: 600;
  font-size: 12px;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 1px solid var(--iluria-color-border) !important;
  padding: 16px;
  text-align: center;
}

:deep(.promotions-table .p-datatable-thead > tr > th.col-flex) {
  text-align: left;
}

:deep(.promotions-table .p-datatable-tbody > tr) {
  border-bottom: 1px solid var(--iluria-color-border) !important;
  background: var(--iluria-color-surface) !important;
}

:deep(.promotions-table .p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-hover) !important;
}

:deep(.promotions-table .p-datatable-tbody > tr > td) {
  padding: 16px;
  border: none;
  border-bottom: 1px solid var(--iluria-color-border);
  vertical-align: middle;
  background: inherit !important;
  color: var(--iluria-color-text) !important;
  font-size: 14px;
  text-align: center;
}

:deep(.promotions-table .p-datatable-tbody > tr > td.col-flex) {
  text-align: left;
}

/* Column header style */
:deep(.promotions-table th .column-header) {
  display: block;
  width: 100%;
  text-align: inherit;
  cursor: default;
  user-select: none;
}

:deep(.promotions-table th .column-header[data-sortable="true"]) {
  cursor: pointer;
}

/* Promotion Name */
.promotion-name {
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.name-text {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s ease;
}

.description-text {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

/* Type Badge */
.type-badge-container {
  display: flex;
  align-items: center;
}

.type-badge {
  font-size: 12px;
  font-weight: 500;
  padding: 4px 10px;
  border-radius: 16px;
  text-transform: capitalize;
}

.type-simple_discount {
  background: #dbeafe;
  color: #1e40af;
}

.type-progressive_discount {
  background: #d1fae5;
  color: #047857;
}

.type-free_shipping {
  background: #fef3c7;
  color: #d97706;
}

.type-gift {
  background: #fce7f3;
  color: #be185d;
}

.type-cross_selling {
  background: #e0e7ff;
  color: #4338ca;
}

.type-first_purchase {
  background: #ecfdf5;
  color: #059669;
}


.value-text {
  font-weight: 500;
  color: #059669;
}

/* Status Badges */
.status-badge, .combine-badge {
  font-size: 11px;
  font-weight: 500;
  padding: 3px 8px;
  border-radius: 12px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-active {
  background: #dcfce7;
  color: #166534;
}

.status-inactive {
  background: #fee2e2;
  color: #dc2626;
}

.combine-yes {
  background: #dbeafe;
  color: #1e40af;
}

.combine-no {
  background: #f3f4f6;
  color: #6b7280;
}

/* Date */
.date-text {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s ease;
}

/* Actions */
.action-buttons {
  display: flex;
  gap: 4px;
  justify-content: center;
}






/* Empty State */
.empty-state {
  text-align: center;
  padding: 48px 24px;
  background: var(--iluria-color-surface);
  transition: background-color 0.3s ease;
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
}

.empty-icon {
  display: flex;
  justify-content: center;
  align-items: center;
  margin-bottom: 16px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s ease;
}

.empty-title {
  font-size: 18px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  transition: color 0.3s ease;
  text-align: center;
}

.empty-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 16px 0;
  transition: color 0.3s ease;
  text-align: center;
  line-height: 1.5;
  max-width: 400px;
}

/* Loading State */
.loading-state {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 12px;
  padding: 32px;
  color: var(--iluria-color-text-secondary);
  font-size: 14px;
  background: var(--iluria-color-surface);
  transition: all 0.3s ease;
}

.loading-spinner {
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

/* Pagination */
.pagination-container {
  display: flex;
  justify-content: center;
  margin-top: 24px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Responsive */
@media (max-width: 1024px) {
  .page-header {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
  }
  
  .header-actions {
    justify-content: space-between;
  }
  
  .search-input {
    min-width: 200px;
  }
}

@media (max-width: 768px) {
  .promotions-list-container {
    padding: 16px;
  }
  
  .search-input {
    min-width: 150px;
  }
  
  :deep(.promotions-table .p-datatable-tbody > tr > td) {
    padding: 12px 16px;
  }
  
  :deep(.promotions-table .p-datatable-thead > tr > th) {
    padding: 12px 16px;
    font-size: 11px;
  }
}
</style>
