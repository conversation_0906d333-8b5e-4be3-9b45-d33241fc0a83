import axios from 'axios';

class ProductImportService {
    #endpoint = 'http://localhost:8081/products/imports';

    // Parse CSV file and extract headers
    async parseCSVHeaders(file) {
        try {
            const formData = new FormData();
            formData.append('file', file);

            const response = await axios.post(`${this.#endpoint}/parse-headers`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Process import directly
    async createImport(file, fieldMapping) {
        try {
            const formData = new FormData();
            formData.append('file', file);
            formData.append('fieldMapping', JSON.stringify(fieldMapping));

            const response = await axios.post(this.#endpoint, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data'
                }
            });

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Get available fields for import mapping
    async getAvailableFields() {
        try {
            const response = await axios.get(`${this.#endpoint}/fields`);
            return response.data;
        } catch (error) {
            return {
                basic: [
                    'name',
                    'sku', 
                    'description',
                    'shortDescription',
                    'price',
                    'originalPrice',
                    'costPrice',
                    'stock',
                    'stockQuantityTotal',
                    'weight',
                    'boxLength',
                    'boxWidth', 
                    'boxDepth',
                    'type',
                    'status',
                    'hasVariation',
                    'highlight',
                    'newTag',
                    'barCode',
                    'supplierName',
                    'supplierLink',
                    'supplierNotes',
                    'metaTitle',
                    'metaDescription',
                    'createdAt',
                    'updatedAt'
                ],
                variations: [
                    'variation_id',
                    'variation_sku',
                    'variation_price',
                    'variation_originalPrice',
                    'variation_costPrice',
                    'variation_stock',
                    'variation_weight',
                    'variation_boxLength',
                    'variation_boxWidth',
                    'variation_boxDepth',
                    'variation_attributes'
                ]
            };
        }
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Get status display text
    getStatusText(status) {
        const statusMap = {
            'PENDING': 'Pendente',
            'IN_PROGRESS': 'Em andamento',
            'COMPLETED': 'Concluído',
            'FAILED': 'Falhou'
        };
        return statusMap[status] || status;
    }

    // Get status color for UI
    getStatusColor(status) {
        const colorMap = {
            'PENDING': 'warning',
            'IN_PROGRESS': 'info',
            'COMPLETED': 'success',
            'FAILED': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    // Check if import can be deleted
    canDelete(importJob) {
        return ['COMPLETED', 'FAILED'].includes(importJob.status);
    }

    // Get field display name
    getFieldDisplayName(fieldName) {
        const fieldMap = {
            // Basic fields
            'name': 'Nome do Produto',
            'sku': 'SKU',
            'description': 'Descrição',
            'shortDescription': 'Descrição Curta',
            'price': 'Preço',
            'originalPrice': 'Preço Original',
            'costPrice': 'Preço de Custo',
            'stock': 'Estoque',
            'stockQuantityTotal': 'Quantidade Total em Estoque',
            'weight': 'Peso',
            'boxLength': 'Comprimento da Caixa',
            'boxWidth': 'Largura da Caixa',
            'boxDepth': 'Profundidade da Caixa',
            'type': 'Tipo',
            'status': 'Status',
            'hasVariation': 'Tem Variação',
            'highlight': 'Destaque',
            'newTag': 'Tag Novo',
            'barCode': 'Código de Barras',
            'supplierName': 'Nome do Fornecedor',
            'supplierLink': 'Link do Fornecedor',
            'supplierNotes': 'Notas do Fornecedor',
            'metaTitle': 'Meta Título',
            'metaDescription': 'Meta Descrição',
            'createdAt': 'Data de Criação',
            'updatedAt': 'Data de Atualização',
            
            // Variation fields
            'variation_id': 'ID da Variação',
            'variation_sku': 'SKU da Variação',
            'variation_price': 'Preço da Variação',
            'variation_originalPrice': 'Preço Original da Variação',
            'variation_costPrice': 'Preço de Custo da Variação',
            'variation_stock': 'Estoque da Variação',
            'variation_weight': 'Peso da Variação',
            'variation_boxLength': 'Comprimento da Caixa da Variação',
            'variation_boxWidth': 'Largura da Caixa da Variação',
            'variation_boxDepth': 'Profundidade da Caixa da Variação',
            'variation_attributes': 'Atributos da Variação'
        };
        return fieldMap[fieldName] || fieldName;
    }

    // Validate CSV file
    validateCSVFile(file) {
        const errors = [];
        
        if (!file) {
            errors.push('Nenhum arquivo selecionado');
            return errors;
        }

        // Check file type
        if (!file.type.includes('csv') && !file.name.toLowerCase().endsWith('.csv')) {
            errors.push('Apenas arquivos CSV são suportados');
        }

        // Check file size (max 10MB)
        const maxSize = 10 * 1024 * 1024; // 10MB
        if (file.size > maxSize) {
            errors.push('Arquivo muito grande. Tamanho máximo: 10MB');
        }

        return errors;
    }
}

export default new ProductImportService();