<template>
    <IluriaModal
        :model-value="true"
        :title="$t('themes.create.category')"
        :dialog-style="{ width: '800px', maxWidth: '90vw' }"
        @update:model-value="handleClose"
    >
    <div class="container-form">
        <div class="form-group">
            <label class="form-label">{{ $t("themes.categoryName") }}</label>
            <IluriaInputText
                v-model="formData.name"
                :placeholder="$t('themes.categoryName')"
                required
            />
        </div>
        <div class="form-group">
            <label class="form-label">{{ $t("themes.categoryDescription") }}</label>
            <textarea
                v-model="formData.description"
                :placeholder="$t('themes.categoryDescription')"
                rows="3"
                class="form-textarea"
            ></textarea>
        </div>
        <div class="form-group">
            <label class=form-label>{{ $t("themes.categoryColor") }}</label>
            <div class="color-input-group">
                <input type="color" v-model="formData.color" class="color-picker" />
                <IluriaInputText
                    v-model="formData.color"
                    class="color-text-input"
                />
            </div>
        </div>
        <div class="form-actions">
            <IluriaButton @click="createCategory" :loading="isCreating">
                {{ $t("common.create") }}
            </IluriaButton>
        </div>
    </div>
    </IluriaModal>
</template>

<script setup>
import { ref } from 'vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'

const { t } = useI18n()
const isCreating = ref(false)
const toast = useToast()
const emit = defineEmits(['close', 'create'])

const formData = ref({
    name: '',
    description: '',
    color: '#FFFFFF',
    slug: '',
    sort_order: 0,
    is_active: true
})

const handleClose = () => {
    emit('close')
}

const createCategory = async () => {
    isCreating.value = true;
    try {
        await new Promise(resolve => setTimeout(resolve, 1500));
        emit('create', formData.value);
        toast.showSuccess(t('themes.createCategorySuccess'));
    } catch (error) {
        toast.showError(t('themes.createCategoryError'));
    } finally {
        isCreating.value = false;
    }
}
</script>

<style scoped>
.container-form{
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
    padding: 1rem;
}
.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 500;
    color: var(--iluria-color-text-primary);
}

.form-textarea {
    padding: 0.75rem;
    border: 1px solid var(--iluria-color-border);
    border-radius: 6px;
    font-family: inherit;
    resize: vertical;
    min-height: 100px;
    
}

.color-input-group {
    display: flex;
    gap: 0.5rem;
    align-items: center;
}

.color-picker {
    width: 3rem;
    height: 2.5rem;
    border: 1px solid var(--iluria-color-border);
    border-radius: 4px;
    cursor: pointer;
}

.color-text-input {
    flex: 1;
}

.form-actions { 
    align-self: end;
}
</style>