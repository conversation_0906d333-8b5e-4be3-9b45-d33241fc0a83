import axios from 'axios'
import { API_URLS } from '@/config/api.config'

const THEME_BASE_URL = API_URLS.THEME_BASE_URL

/**
 * Theme Service - Handles theme and template operations
 */
class ThemeService {
  constructor() {
    this.baseURL = THEME_BASE_URL
  }

  // ===== THEME OPERATIONS =====

  /**
   * Get all themes for the current store
   * @param {Array} categoryIds - Optional category filter
   * @returns {Promise<Array>} List of themes
   */
  async getThemes(categoryIds = null) {
    try {
      let url = `${this.baseURL}`
      if (categoryIds && categoryIds.length > 0) {
        const params = new URLSearchParams()
        categoryIds.forEach(id => params.append('categoryIds', id))
        url += `?${params.toString()}`
      }
      
      const response = await axios.get(url)
      return response.data
    } catch (error) {
      console.error('Error fetching themes:', error)
      throw error
    }
  }

  /**
   * Get default themes (global templates)
   * @returns {Promise<Array>} List of default themes
   */
  async getDefaultThemes() {
    try {
      const response = await axios.get(`${this.baseURL}/defaults`)
      return response.data
    } catch (error) {
      console.error('Error fetching default themes:', error)
      // Se não conseguir carregar temas padrão, retorna array vazio
      return []
    }
  }

  /**
   * Get theme by ID
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Theme object
   */
  async getThemeById(themeId) {
    try {
      const response = await axios.get(`${this.baseURL}/${themeId}`)
      return response.data
    } catch (error) {
      console.error('Error fetching theme:', error)
      throw error
    }
  }

  /**
   * Get active theme for the current store
   * @returns {Promise<Object>} Active theme object
   */
  async getActiveTheme() {
    try {
      const response = await axios.get(`${this.baseURL}/active`)
      return response.data
    } catch (error) {
      console.error('Error fetching active theme:', error)
      throw error
    }
  }

  /**
   * Create a new theme
   * @param {Object} themeData - Theme data
   * @returns {Promise<Object>} Created theme
   */
  async createTheme(themeData) {
    try {
      const response = await axios.post(`${this.baseURL}`, themeData)
      return response.data
    } catch (error) {
      console.error('Error creating theme:', error)
      throw error
    }
  }

  /**
   * Update theme
   * @param {string} themeId - Theme ID
   * @param {Object} updates - Theme updates
   * @returns {Promise<Object>} Updated theme
   */
  async updateTheme(themeId, updates) {
    try {
      // Verificar autenticação
      const { useAuthStore } = await import('@/stores/auth.store')
      const { useStoreStore } = await import('@/stores/store.store')
      const authStore = useAuthStore()
      const storeStore = useStoreStore()

      // Verificar se temos tokens necessários
      if (!authStore.userToken && !authStore.storeToken) {
        throw new Error('No authentication tokens available')
      }

      if (!storeStore.selectedStore?.id) {
        throw new Error('No store selected')
      }

      const response = await axios.put(`${this.baseURL}/${themeId}`, updates)
      return response.data
    } catch (error) {
      console.error('Error updating theme:', error)
      throw error
    }
  }

  /**
   * Delete theme
   * @param {string} themeId - Theme ID
   * @returns {Promise<void>}
   */
  async deleteTheme(themeId) {
    try {
      await axios.delete(`${this.baseURL}/${themeId}`)
    } catch (error) {
      console.error('Error deleting theme:', error)
      throw error
    }
  }

  /**
   * Activate theme
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Activated theme
   */
  async activateTheme(themeId) {
    try {
      const response = await axios.post(`${this.baseURL}/${themeId}/activate`)
      return response.data
    } catch (error) {
      console.error('Error activating theme:', error)
      throw error
    }
  }

  /**
   * Duplicate theme
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Duplicated theme
   */
  async duplicateTheme(themeId) {
    try {
      const response = await axios.post(`${this.baseURL}/${themeId}/duplicate`)
      return response.data
    } catch (error) {
      console.error('Error duplicating theme:', error)
      throw error
    }
  }

  /**
   * Switch theme with backup/restore logic
   * @param {string} themeId - Theme ID
   * @param {boolean} preserveCustomizations - Whether to preserve customizations
   * @returns {Promise<Object>} Switched theme
   */
  async switchTheme(themeId, preserveCustomizations = true) {
    try {
      const response = await axios.post(
        `${this.baseURL}/${themeId}/switch`,
        null,
        { params: { preserveCustomizations } }
      )
      return response.data
    } catch (error) {
      console.error('Error switching theme:', error)
      throw error
    }
  }

  /**
   * Use a default theme (copy and activate)
   * @param {string} defaultThemeId - Default theme ID to use
   * @returns {Promise<Object>} Activated theme copy
   */
  async useDefaultTheme(defaultThemeId) {
    try {
      // Get store ID from store store to ensure header is sent
      const { useStoreStore } = await import('@/stores/store.store')
      const storeStore = useStoreStore()
      const storeId = storeStore.selectedStore?.id
      
      if (!storeId) {
        throw new Error('Store ID not available')
      }
      
      const response = await axios.post(
        `${this.baseURL}/defaults/${defaultThemeId}/use`,
        {},
        {
          headers: {
            'X-Store-Id': storeId
          }
        }
      )
      return response.data
    } catch (error) {
      console.error('Error using default theme:', error)
      throw error
    }
  }

  /**
   * Get theme CSS
   * @param {string} themeId - Theme ID
   * @returns {Promise<string>} CSS content
   */
  async getThemeCss(themeId) {
    try {
      const response = await axios.get(`${this.baseURL}/${themeId}/css`)
      return response.data
    } catch (error) {
      console.error('Error fetching theme CSS:', error)
      throw error
    }
  }

  // ===== TEMPLATE OPERATIONS =====

  /**
   * Apply theme template
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Applied theme
   */
  async applyThemeTemplate(themeId) {
    try {
      const response = await axios.post(`${this.baseURL}/${themeId}/apply-template`)
      return response.data
    } catch (error) {
      console.error('Error applying theme template:', error)
      throw error
    }
  }

  /**
   * Switch theme with optional customization preservation
   * @param {string} themeId - New theme ID
   * @param {boolean} preserveCustomizations - Whether to preserve customizations
   * @returns {Promise<Object>} Switched theme
   */
  async switchTheme(themeId, preserveCustomizations = true) {
    try {
      const response = await axios.post(
        `${this.baseURL}/${themeId}/switch`,
        null,
        { params: { preserveCustomizations } }
      )
      return response.data
    } catch (error) {
      console.error('Error switching theme:', error)
      throw error
    }
  }

  /**
   * Upload template files
   * @param {string} themeId - Theme ID
   * @param {Object} templateFiles - Template files object
   * @param {string} templateVersion - Template version
   * @returns {Promise<Object>} Updated theme
   */
  async uploadTemplateFiles(themeId, templateFiles, templateVersion = '1.0.0') {
    try {
      const response = await axios.post(
        `${this.baseURL}/${themeId}/templates/upload`,
        templateFiles,
        { params: { templateVersion } }
      )
      return response.data
    } catch (error) {
      console.error('Error uploading template files:', error)
      throw error
    }
  }

  /**
   * Sync template files from S3
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Synced theme
   */
  async syncTemplateFiles(themeId) {
    try {
      const response = await axios.post(`${this.baseURL}/${themeId}/templates/sync`)
      return response.data
    } catch (error) {
      console.error('Error syncing template files:', error)
      throw error
    }
  }

  /**
   * Get template files
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Template files object
   */
  async getTemplateFiles(themeId) {
    try {
      const response = await axios.get(`${this.baseURL}/${themeId}/templates`)
      return response.data
    } catch (error) {
      console.error('Error fetching template files:', error)
      throw error
    }
  }

  /**
   * Remove template files
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Updated theme
   */
  async removeTemplateFiles(themeId) {
    try {
      const response = await axios.delete(`${this.baseURL}/${themeId}/templates`)
      return response.data
    } catch (error) {
      console.error('Error removing template files:', error)
      throw error
    }
  }

  /**
   * Get template manifest
   * @param {string} themeId - Theme ID
   * @returns {Promise<string>} Template manifest
   */
  async getTemplateManifest(themeId) {
    try {
      const response = await axios.get(`${this.baseURL}/${themeId}/template-manifest`)
      return response.data
    } catch (error) {
      console.error('Error fetching template manifest:', error)
      throw error
    }
  }

  /**
   * Get template status
   * @param {string} themeId - Theme ID
   * @returns {Promise<Object>} Template status object
   */
  async getTemplateStatus(themeId) {
    try {
      const response = await axios.get(`${this.baseURL}/${themeId}/template-status`)
      return response.data
    } catch (error) {
      console.error('Error fetching template status:', error)
      throw error
    }
  }

  // ===== TEMPLATE VALIDATION =====

  /**
   * Validate template compatibility
   * @param {string} themeId - Theme ID
   * @param {string} templateVersion - Template version to validate
   * @returns {Promise<Object>} Validation result
   */
  async validateTemplateCompatibility(themeId, templateVersion) {
    try {
      const status = await this.getTemplateStatus(themeId)
      
      return {
        isCompatible: status.templateVersion === templateVersion,
        currentVersion: status.templateVersion,
        requestedVersion: templateVersion,
        hasTemplateFiles: status.hasTemplateFiles,
        fileCount: status.templateFileCount
      }
    } catch (error) {
      console.error('Error validating template compatibility:', error)
      throw error
    }
  }

  // ===== TEMPLATE OPERATIONS HELPERS =====

  /**
   * Check if theme has template files
   * @param {string} themeId - Theme ID
   * @returns {Promise<boolean>} Whether theme has template files
   */
  async hasTemplateFiles(themeId) {
    try {
      const status = await this.getTemplateStatus(themeId)
      return status.hasTemplateFiles || false
    } catch (error) {
      console.error('Error checking template files:', error)
      return false
    }
  }

  /**
   * Get template file count
   * @param {string} themeId - Theme ID
   * @returns {Promise<number>} Number of template files
   */
  async getTemplateFileCount(themeId) {
    try {
      const status = await this.getTemplateStatus(themeId)
      return status.templateFileCount || 0
    } catch (error) {
      console.error('Error getting template file count:', error)
      return 0
    }
  }

  /**
   * Get template updated date
   * @param {string} themeId - Theme ID
   * @returns {Promise<Date|null>} Template updated date
   */
  async getTemplateUpdatedDate(themeId) {
    try {
      const status = await this.getTemplateStatus(themeId)
      return status.templateUpdatedAt ? new Date(status.templateUpdatedAt) : null
    } catch (error) {
      console.error('Error getting template updated date:', error)
      return null
    }
  }

  /**
   * Initialize default themes for the system
   * @returns {Promise<Array>} List of created default themes
   */
  async initializeDefaultThemes() {
    try {
      const response = await axios.post(`${this.baseURL}/initialize-defaults`)
      return response.data
    } catch (error) {
      console.error('Error initializing default themes:', error)
      throw error
    }
  }

  // ===== PUBLICATION OPERATIONS =====

  /**
   * Get publication status
   * @returns {Promise<Object>} Publication status object
   */
  async getPublishStatus() {
    try {
      const response = await axios.get(`${this.baseURL}/publish/status`)
      return response.data
    } catch (error) {
      console.error('Error fetching publish status:', error)
      throw error
    }
  }

  /**
   * Publish theme to production
   * @returns {Promise<Object>} Publication result
   */
  async publishToProduction() {
    try {
      const response = await axios.post(`${this.baseURL}/publish`)
      return response.data
    } catch (error) {
      console.error('Error publishing to production:', error)
      throw error
    }
  }
}

// Export instance
export const themeService = new ThemeService()
export default themeService