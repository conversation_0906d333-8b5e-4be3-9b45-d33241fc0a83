{"title": "Products", "add": "Add Product", "description": "Product Description", "edit": "Edit Product", "products": "Products", "noProducts": "No products found for this store", "noProductsAdded": "No products added", "loadError": "Error loading products", "viewProducts": "View Products", "image": "Image", "name": "Product Name", "sku": "SKU", "price": "Price", "stock": "Stock", "addProduct": "Add Product", "remove": "Remove", "onlyInStock": "Only in stock", "noProductsFound": "No products found", "confirmDelete": "Are you sure you want to delete {name}?", "confirmDeleteTitle": "Confirm Delete", "skuCode": "SKU Code", "barCode": "Barcode", "shortDescription": "Short Description", "pricesAndStock": "Prices and Stock", "priceConfiguration": "Price Configuration", "priceConfigurationSubtitle": "Set the product selling prices", "stockControl": "Stock Control", "stockControlSubtitle": "Available product quantity", "dimensionsAndWeight": "Dimensions and Weight", "dimensionsAndWeightSubtitle": "Measurements and weight for shipping calculation", "originalPrice": "Original Price", "costPrice": "Cost Price", "stockQuantity": "Stock Quantity", "weight": "Weight", "boxLength": "Length", "boxWidth": "<PERSON><PERSON><PERSON>", "boxDepth": "De<PERSON><PERSON>", "productData": "Product Data", "tags": "Tags", "variations": "Variations", "customFields": "Custom Fields", "customFieldsSubtitle": "Create and configure custom fields for the product", "pricesAndStockSubtitle": "Information about the stock and price", "type": "Product Type", "settings": "Product Settings", "highlight": "Highlight", "newTag": "New", "supplierData": "Supplier Data (optional)", "supplierDataSubtitle": "Use the fields below to keep track of the supplier information for this product. These information are not shown in the store and are only for your internal control.", "supplierName": "Supplier Name (optional)", "supplierLink": "Link to the product or supplier (optional)", "supplierNotes": "Notes (optional)", "selectStatus": "Select a status", "status": "Status", "statusDraft": "Invisible", "statusActive": "Visible", "typePhysical": "Physical", "typeDigital": "Digital", "typeGift": "Gift", "hasVariation": "Has variations", "hasVariantionTooltip": "Products with variations allow you to offer different options for the same product, such as Color (Blue, Green, Yellow), Size (P, M, G), or Material. Each variation can have its own price, stock and images.", "variationTypeSimple": "Simple", "variationTypeWithVariation": "With variation", "optionName": "Option Name", "variationPlaceholder": "Size, Color, Material, etc.", "showOnSearch": "Show on search", "addValue": "Add {value}", "addVariation": "Add variation (Color, Size, Material...)", "groupBy": "Group by", "variant": "<PERSON><PERSON><PERSON>", "variants": "Variants", "editVariations": "Edit Variations", "editVariation": "Edit Variation", "editGroupVariations": "Edit Group of Variations", "editingVariations": "Editing {count} variations", "differentValuesInGroup": "Different values in group", "differentValues": "Different values", "mixedValues": "Mixed values", "mixed": "Mixed", "noVariations": "No variations", "filterByAttributes": "Filter by attributes", "images": "Images", "dragAndDropImages": "Drag and drop images here or click to select", "uploadImages": "Select Images", "imageUploaded": "Image uploaded successfully", "imageUploadError": "Error uploading image", "saveProductFirst": "Save the product first to enable image upload", "confirmDeleteImage": "Confirm delete image", "confirmDeleteImageMessage": "Are you sure you want to delete this image?", "giftPackaging": "Gift packaging", "giftPackagingQuestion": "Allow gift packaging?", "giftPackagingPrice": "Gift packaging price", "giftPackagingType": "How do you want to calculate the value in the cart?", "giftPackagingPricePlaceholder": "R$: 00,00", "giftPackagingTypeSum": "Sum the price of all selected packaging", "giftPackagingTypeMax": "Use only the highest price among the packaged products", "quantityLimit": "Quantity limit", "minQuantityLimitCheck": "Enable minimum quantity", "maxQuantityLimitCheck": "Enable maximum quantity", "minQuantityLimit": "Minimum quantity", "maxQuantityLimit": "Maximum quantity", "addPriceRanges": "Add price ranges", "addPriceRangeButton": "Add price range", "customization": {"sectionTitle": "Customization options", "sectionDescription": "Configure how customers can customize this product during purchase.", "enabled": "Customizable Product", "customization": "Customization", "newCustomization": "New customization", "addCustomization": "Add customization", "emptyTitle": "No customization defined", "emptyDescription": "Add customization options for this product", "type": "Type", "selectType": "Select a type", "title": "Title", "titlePlaceholder": "Ex: Name to be engraved", "description": "Description", "descriptionPlaceholder": "Ex: Enter the name to be engraved on the shirt", "required": "Required field", "additionalPrice": "Additional price", "pricePlaceholder": "0.00", "characterLimit": "Character limit", "characterLimitPlaceholder": "Ex: 50", "options": "Choice options", "optionTitle": "Option", "optionLabel": "Option Title", "optionLabelPlaceholder": "Ex: Yes, No", "addOption": "Add option", "noOptionsAdded": "No options added", "nestedCustomization": "Nested customization", "enableNested": "Enable", "moveUp": "Move up", "moveDown": "Move down", "types": {"multipleChoice": "Multiple choice", "text": "Text", "number": "Number", "image": "Image"}}, "hasPriceRanges": "This variation has different price ranges", "fromQuantity": "From", "currency": "$", "units": "unit(s)", "addPriceRange": "Add Price Range", "copyPriceToAll": "Copy price to all variations", "copyStockToAll": "Copy stock to all variations", "copyOriginalPriceToAll": "Copy original price to all variations", "copyCostPriceToAll": "Copy cost price to all variations", "copyWeightToAll": "Copy weight to all variations", "copyLengthToAll": "Copy length to all variations", "copyWidthToAll": "Copy width to all variations", "copyDepthToAll": "Copy depth to all variations", "newProduct": "New Product", "newProductSubtitle": "Create a new product for your store", "listSubtitle": "Manage your products and variations", "basicDataTab": "Basic Data", "attributesTab": "Attributes & Filters", "variationsTab": "Variations", "pricingTab": "Prices and Stock", "mediaTab": "Images", "shippingTab": "Shipping and Delivery", "settingsTab": "Settings", "newProductSubtitleText": "Register and edit product information", "editProductSubtitleText": "Edit the product information", "productSettingsTitle": "Product Settings", "seoOptimizationSubtitle": "Search engine optimization", "priceConfigTitle": "Price Configuration", "priceConfigSubtitle": "Set the product sale values", "stockControlTitle": "Stock Control", "dimensionsWeightTitle": "Dimensions and Weight", "dimensionsWeightSubtitle": "Measurements and weight for shipping calculation", "editProduct": "Edit Product", "editProductSubtitle": "Edit the product information", "productCreated": "Product created successfully", "productUpdated": "Product updated successfully", "errorLoadingProduct": "Error loading product", "errorUpdatingProduct": "Error updating product", "anErrorOccurred": "An error occurred", "variation": "Variation", "newVariation": "New variation", "imagesUploadedSuccessfully": "Images uploaded successfully", "variationImagesUploaded": "Variation images uploaded successfully", "errorUploadingImages": "Error uploading images", "errorDeletingImage": "Error deleting image", "variationStructureChanged": "The variation structure has been changed. The old images may no longer make sense for the new variations.", "variationStructureChangedHelp": "Click on the camera icon next to each variation to add or reassign images. The old images were removed automatically.", "variationsRemoved": "Variations were removed. The variation images will be discarded.", "variationsAdded": "Variations were added. You can now add specific images for each variation.", "imagePositionsUpdated": "Image positions updated", "errorUpdatingPositions": "Error updating image positions", "nameRequired": "The product name is required", "shortDescriptionRequired": "The short description is required", "shipping": {"title": "Shipping", "free": "This product has free shipping: ", "fixed": "This product has fixed shipping: ", "yes": "Yes", "no": "No", "dimensionsInPriceAndStock": "Add the product dimensions in the prices and stock section", "dimensionsInVariations": "Add the product dimensions in the variations section", "value": "Fixed shipping value:", "combinedValue": "Combined shipping value:", "maxUnities": "Use fixed shipping until:", "dimensions": "Package dimensions:", "length": "Length:", "width": "Width:", "height": "Height:", "weight": "Estimated package weight:", "valuePlaceholder": "", "combinedValuePlaceholder": "", "maxUnitiesPlaceholder": "units"}, "relatedProducts": "Related products", "relatedProductsSubtitle": "Add related products to improve the customer experience", "photosMovedToVariations": "The photos were removed because the product now has variations", "uploadingImagesInBackground": "Uploading images in background...", "allImagesUploadedSuccessfully": "All images uploaded successfully", "someImagesFailedToUpload": "Some images failed to upload", "imagesWillBeUploadedOnSave": "The images will be uploaded when you save the product", "maxFilesReached": "Maximum limit of {max} files reached", "uploadingImages": "Uploading images... Please wait until completed.", "label": {"title": "Iluria Product Labels", "create": "Create", "AiGeneratorTitle": "AI Label Generator", "used": "0/0 used", "upgrade": "Upgrade", "buttonDescription": "Create labels from text", "createFromAi": "Create from AI", "badgesTitle": "Badges", "badgesDescription": "Create product badges", "labelsTitle": "Labels", "labelsDescription": "Create product labels", "listTitle": "Lists", "viewFullListButton": "View full list"}, "searchProducts": "Search products", "searchByName": "Search by name", "productsSelected": "products selected", "addToOrder": "Add to order", "itemAdded": "item added", "itemsAdded": "items added", "product": "Product", "quantity": "Quantity", "unitPrice": "Unit Price", "availableStock": "Available Stock", "loading": "Loading...", "category": "Category", "selectCategory": "Select category", "allCategories": "All categories", "measurementTableSubtitle": "Select a measurement table for this product (optional)", "skuCodeLabel": "Unique internal code used for identification in the system", "profitMargin": "<PERSON><PERSON>", "negativeMargin": "Negative margin", "negativeMarginHint": "The price is lower than the cost price", "negativeMarginError": "Cannot save a product with negative profit margin", "noOptionsFound": "No options found", "percentage": "%", "supplierNamePlaceholder": "Supplier name", "supplierLinkPlaceholder": "Supplier link or product", "supplierNotesPlaceholder": "Notes about the supplier", "identificationCodes": "Identification codes", "giftCardTitle": "Gift Cards", "giftCardSubtitle": "Manage your gift cards", "giftCardButton": "Add Gift Card", "newGiftCardTitle": "New Gift Card", "editGiftCardTitle": "Edit Gift Card", "newGiftCardSubtitle": "Add and edit gift card informations", "seoConfiguration": "SEO Configuration", "basicGiftCard": "Gift Card Data", "giftCardSavedSuccessfully": "Gift Card created successfully", "giftCardSavedFailure": "Gift Card Creation Failed", "giftCardExcludedSuccess": "Gift card deleted successfully", "giftCardExcludedFail": "Gift card deletion failed", "loadGiftCardFailure": "Error loading gift card information", "customer": "Customer", "denominations": "Denominations", "giftCardName": "Gift Card Title", "import": "Import", "export": "Export", "exportList": {"title": "Product Exports", "subtitle": "Manage your product data exports", "newExport": "New Export", "fileName": "File Name", "createdAt": "Created At", "fileSize": "Size", "status": "Status", "actions": "Actions", "download": "Download file", "delete": "Delete export", "noExports": "No exports found", "noExportsDescription": "You haven't created any product exports yet", "createFirstExport": "Create First Export", "loadError": "Error loading exports", "downloadStarted": "Download started", "downloadError": "Error downloading file", "deleteTitle": "Confirm Deletion", "deleteMessage": "Are you sure you want to delete this export?", "deleteConfirm": "Delete", "deleteSuccess": "Export deleted successfully", "deleteError": "Error deleting export", "loading": "Loading exports...", "backToProducts": "Back to Product List", "infoTitle": "Export Information", "retentionTitle": "File Retention", "retentionDescription": "Export files are kept for 30 days and then automatically removed.", "processingTitle": "Processing", "processingDescription": "Large exports may take a few minutes to process.", "formatsTitle": "Supported Formats", "formatsDescription": "We support CSV and Excel (XLSX) export for maximum compatibility."}, "attributes": {"title": "Attributes & Filters", "subtitle": "Manage the attributes and filters available for products", "sectionTitle": "Attributes & Filters", "sectionDescription": "Configure the attributes for this product and define which ones will be used as filters in the store", "addAttribute": "Add Attribute", "createAttribute": "Create Attribute", "createFirstAttribute": "Create First Attribute", "attribute": "Attribute", "values": "Values", "useAsFilter": "Use as filter in store", "attributeName": "Attribute Name", "attributeNamePlaceholder": "Ex: Color, Size, Material, Brand...", "attributeValues": "Attribute values", "addValue": "Add value", "addNewAttribute": "Add new attribute", "noAttributesAdded": "No attributes added", "emptyTitle": "No attributes found", "emptyDescription": "Create your first attribute to better organize your products", "searchAttributes": "Search attributes...", "searchPlaceholder": "Search attributes...", "createNewAttribute": "Create new attribute", "allCategories": "All categories", "clearFilters": "Clear filters", "total": "Total", "active": "Active", "inactive": "Inactive", "status": {"active": "Active", "inactive": "Inactive"}, "stats": {"totalAttributes": "attribute(s) total", "subcategories": "subcategory(ies)", "values": "value(s)", "usage": "use(s)", "more": "more"}, "actions": {"edit": "Edit attribute", "delete": "Delete attribute", "previous": "Previous", "next": "Next", "page": "Page"}, "pagination": {"previous": "Previous", "next": "Next", "page": "Page", "of": "of"}, "modal": {"createTitle": "Create New Attribute", "editTitle": "Edit Attribute", "valuesTitle": "Manage Values for \"{name}\"", "createDescription": "Configure a new attribute to organize your products.", "editDescription": "Update the attribute information.", "valuesDescription": "Add, edit or remove values for this attribute.", "categoryContext": "For category: {category}", "name": "Attribute Name", "namePlaceholder": "Ex: Color, Size, Material, Brand...", "nameHelper": "This will be the attribute name that appears in filters and product page", "category": "Category", "categoryRequired": "*", "categoryPlaceholder": "Select a category", "categoryHelper": "The attribute will be specific to this category and its subcategories, or to all if \"All categories\" is selected", "allCategories": "All categories", "activeLabel": "Active attribute", "activeHelper": "Active attributes can be used in products and appear in filters.", "activeHelperFixed": "Attributes created on the product page are always active.", "categoryHelperFixed": "The attribute will be created for the product category, which cannot be changed here.", "valuesLabel": "Values", "valuesOptional": "(optional)", "valuesAdded": "Added Values ({filtered}/{total})", "valuesPlaceholder": "Type a value and press Enter", "valuesHelper": "Add values one at a time. You can edit and remove values later.", "addValue": "Add", "searchValues": "Search values...", "clearSearch": "Clear search", "showingValues": "Showing {filtered} of {total} values.", "noValues": "No values added", "noValuesDescription": "Add values for this attribute using the field above.", "noValuesFound": "No values found", "saving": "Saving...", "create": "Create", "save": "Save", "cancel": "Cancel", "errors": {"nameRequired": "Attribute name is required.", "categoryRequired": "Category is required."}, "buttons": {"editValue": "Edit value", "saveValue": "Save", "cancelEdit": "Cancel", "removeValue": "Remove value"}}, "createAttributeModal": {"title": "Add New Attribute", "name": "Attribute Name", "namePlaceholder": "Ex: Color, Size, Material, Brand...", "nameHelper": "This will be the attribute name (ex: Color, Size, Material)", "category": "Linked category", "categoryHelper": "The attribute will be specific to this category", "initialValues": "Initial values", "initialValuesPlaceholder": "Ex: Blue, Green, Yellow", "initialValuesHelper": "Add some initial values separated by comma (optional)", "cancel": "Cancel", "save": "Save Attribute"}, "deleteModal": {"title": "Delete Attribute '{name}'", "message": "Are you sure you want to delete the attribute '{name}'? This action cannot be undone.", "messageWithUsage": "This attribute is being used in {count} product(s). Deletion will remove the attribute from these products.", "affectedProducts": "Affected products:", "andMore": "... and {count} more product(s).", "confirm": "Delete", "cancel": "Cancel"}, "suggestions": {"recentlyUsed": "Recently used", "popular": "Most popular", "allAttributes": "All attributes"}, "validation": {"attributeRequired": "Select an attribute", "valuesRequired": "Add at least one value", "nameRequired": "Attribute name is required", "categoryRequired": "Select a category"}, "success": {"attributeCreated": "Attribute created successfully", "attributeUpdated": "Attribute updated successfully", "attributeDeleted": "Attribute deleted successfully", "attributesSaved": "Attributes saved successfully"}, "error": {"loadingAttributes": "Error loading attributes", "savingAttributes": "Error saving attributes", "creatingAttribute": "Error creating attribute", "updatingAttribute": "Error updating attribute", "deletingAttribute": "Error deleting attribute", "loadingCategories": "Error loading categories"}}, "bulk": {"selected": "selected", "selectedPlural": "selected", "actions": "Bulk actions", "deleteSelected": "Delete selected", "deleteSelectedShort": "Delete", "selectProduct": "Select product", "confirmDeleteTitle": "Confirm bulk deletion", "confirmDeleteMessage": "Are you sure you want to delete {count} {entity}? This action cannot be undone.", "confirmDeleteMessageSingle": "Are you sure you want to delete 1 {entity}? This action cannot be undone.", "deleteSuccess": "{count} {entity} deleted successfully", "deleteSuccessSingle": "1 {entity} deleted successfully", "deleteError": "Error deleting selected {entity}", "deletePartialError": "Some {entity} could not be deleted", "processing": "Processing...", "clearSelection": "Clear selection", "selectAll": "Select all", "deselectAll": "Deselect all", "product": "product", "products": "products"}}