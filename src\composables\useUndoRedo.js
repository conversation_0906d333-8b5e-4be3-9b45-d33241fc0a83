import { ref, reactive, watch } from 'vue'

/**
 * Sistema de Undo/Redo para o Editor de Layout
 * 
 * Funcionalidades:
 * - Controle de versões do estado completo do editor
 * - Suporte a Ctrl+Z (undo) e Ctrl+Y (redo)
 * - Limite de histórico para performance
 * - Detecção automática de mudanças significativas
 */

// Estado global do sistema
const undoRedoState = reactive({
  history: [], // Array de snapshots do estado
  currentIndex: -1, // Índice atual no histórico
  maxHistorySize: 50, // Máximo de estados no histórico
  isUndoing: false, // Flag para evitar loops durante undo/redo
  isRedoing: false,
  hasChanges: false // Se há mudanças não salvas
})

// Referências reativas para os botões
const canUndo = ref(false)
const canRedo = ref(false)

// Sistema de debounce
const debounceTimers = new Map()
const pendingSnapshots = new Map()

/**
 * Cria um snapshot do estado atual do editor
 */
function createSnapshot(editorFrame, pageSettings, selectedElement = null, description = '') {
  if (!editorFrame || !editorFrame.contentDocument) {
    console.warn('[UndoRedo] Editor frame não disponível para snapshot')
    return null
  }

  try {
    const doc = editorFrame.contentDocument
    const html = doc.documentElement.outerHTML
    
    // Captura o ID do elemento selecionado para reselecionar após undo/redo
    let selectedElementId = null
    if (selectedElement) {
      if (!selectedElement.hasAttribute('data-element-id')) {
        selectedElement.setAttribute('data-element-id', 'el-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
      }
      selectedElementId = selectedElement.getAttribute('data-element-id')
    }

    const snapshot = {
      id: 'snapshot-' + Date.now() + '-' + Math.floor(Math.random() * 1000),
      timestamp: Date.now(),
      description: description || 'Mudança no editor',
      html: html,
      pageSettings: JSON.parse(JSON.stringify(pageSettings)), // Deep clone
      selectedElementId: selectedElementId,
      scrollPosition: {
        top: doc.documentElement.scrollTop || doc.body.scrollTop || 0,
        left: doc.documentElement.scrollLeft || doc.body.scrollLeft || 0
      }
    }

    return snapshot
  } catch (error) {
    console.error('[UndoRedo] Erro ao criar snapshot:', error)
    return null
  }
}

/**
 * Adiciona um novo snapshot ao histórico com lógica inteligente
 */
function pushSnapshot(snapshot, options = {}) {
  if (!snapshot) return

  const { 
    immediate = false,
    replaceIfSimilar = true,
    consolidationWindow = 2000 // 2 segundos
  } = options

  // Se estamos no meio do histórico, remove tudo depois do índice atual
  if (undoRedoState.currentIndex < undoRedoState.history.length - 1) {
    undoRedoState.history = undoRedoState.history.slice(0, undoRedoState.currentIndex + 1)
  }

  // Verifica se deve consolidar com o último snapshot
  if (replaceIfSimilar && undoRedoState.history.length > 0) {
    const lastSnapshot = undoRedoState.history[undoRedoState.history.length - 1]
    const timeDiff = snapshot.timestamp - lastSnapshot.timestamp
    
    // Se foi muito recente e é uma mudança similar, substitui o último
    if (timeDiff < consolidationWindow && shouldConsolidate(lastSnapshot, snapshot)) {
      undoRedoState.history[undoRedoState.history.length - 1] = snapshot
      updateCanUndoRedo()
      return
    }
  }

  // Adiciona o novo snapshot
  undoRedoState.history.push(snapshot)
  undoRedoState.currentIndex = undoRedoState.history.length - 1

  // Remove snapshots antigos se exceder o limite
  if (undoRedoState.history.length > undoRedoState.maxHistorySize) {
    const excess = undoRedoState.history.length - undoRedoState.maxHistorySize
    undoRedoState.history.splice(0, excess)
    undoRedoState.currentIndex -= excess
  }

  // Atualiza flags
  undoRedoState.hasChanges = true
  updateCanUndoRedo()
}

/**
 * Verifica se dois snapshots devem ser consolidados
 */
function shouldConsolidate(lastSnapshot, newSnapshot) {
  // Se o elemento selecionado é o mesmo
  if (lastSnapshot.selectedElementId && 
      lastSnapshot.selectedElementId === newSnapshot.selectedElementId) {
    
    // Se a descrição indica mudanças similares
    const similarChanges = [
      'Texto alterado',
      'Cor alterada', 
      'Tamanho da fonte alterado',
      'Header configurado',
      'Footer configurado',
      'Espaçamento alterado',
      'Bordas alteradas'
    ]
    
    const lastDesc = lastSnapshot.description
    const newDesc = newSnapshot.description
    
    // Se ambas são mudanças similares
    if (similarChanges.some(change => 
        lastDesc.includes(change.split(' ')[0]) && 
        newDesc.includes(change.split(' ')[0])
    )) {
      return true
    }
  }
  
  return false
}

/**
 * Atualiza as flags de disponibilidade de undo/redo
 */
function updateCanUndoRedo() {
  canUndo.value = undoRedoState.currentIndex > 0
  canRedo.value = undoRedoState.currentIndex < undoRedoState.history.length - 1
}

/**
 * Aplica um snapshot ao editor
 */
async function applySnapshot(snapshot, editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor) {
  if (!snapshot || !editorFrame) return

  try {
    undoRedoState.isUndoing = true
    undoRedoState.isRedoing = true

    const doc = editorFrame.contentDocument
    if (!doc) return

    // Aplica o HTML
    doc.open()
    doc.write(snapshot.html)
    doc.close()

    // Aplica as configurações da página
    if (applyPageSettings && snapshot.pageSettings) {
      applyPageSettings(snapshot.pageSettings)
    }

    // Aguarda o DOM ser atualizado
    await new Promise(resolve => setTimeout(resolve, 100))

    // CRÍTICO: Reinicializa o editor após reescrever o HTML
    // Isso reconecta todos os event listeners que foram perdidos
    if (reinitializeEditor) {
      try {
        reinitializeEditor()
      } catch (error) {
        console.warn('[UndoRedo] Erro ao reinicializar editor:', error)
      }
    }

    // Aguarda um pouco mais para garantir que o editor foi reinicializado
    await new Promise(resolve => setTimeout(resolve, 50))

    // Restaura a posição de scroll
    if (snapshot.scrollPosition) {
      doc.documentElement.scrollTop = snapshot.scrollPosition.top
      doc.documentElement.scrollLeft = snapshot.scrollPosition.left
      doc.body.scrollTop = snapshot.scrollPosition.top
      doc.body.scrollLeft = snapshot.scrollPosition.left
    }

    // Restaura a seleção do elemento
    if (snapshot.selectedElementId && handleElementSelect) {
      const elementToSelect = doc.querySelector(`[data-element-id="${snapshot.selectedElementId}"]`)
      if (elementToSelect) {
        setTimeout(() => {
          try {
            handleElementSelect(elementToSelect)
          } catch (error) {
            console.warn('[UndoRedo] Erro ao reselecionar elemento:', error)
          }
        }, 150)
      }
    }

  } catch (error) {
    console.error('[UndoRedo] Erro ao aplicar snapshot:', error)
  } finally {
    // Reset das flags após um delay para evitar captura do próprio undo/redo
    setTimeout(() => {
      undoRedoState.isUndoing = false
      undoRedoState.isRedoing = false
    }, 200)
  }
}

/**
 * Hook principal do sistema de Undo/Redo
 */
export function useUndoRedo() {
  
  /**
   * Salva o estado atual com debounce inteligente
   */
  const saveState = (editorFrame, pageSettings, selectedElement, description = '', options = {}) => {
    // Não salva durante undo/redo para evitar loops
    if (undoRedoState.isUndoing || undoRedoState.isRedoing) {
      return
    }

    const {
      immediate = false,
      debounceMs = 800,
      debounceKey = description + (selectedElement?.getAttribute?.('data-element-id') || 'global')
    } = options

    // Se for salvamento imediato, executa direto
    if (immediate) {
      const snapshot = createSnapshot(editorFrame, pageSettings, selectedElement, description)
      if (snapshot) {
        pushSnapshot(snapshot, { immediate: true, replaceIfSimilar: false })
      }
      return
    }

    // Cancela timer anterior se existir
    if (debounceTimers.has(debounceKey)) {
      clearTimeout(debounceTimers.get(debounceKey))
    }

    // Salva o snapshot pendente mais recente
    pendingSnapshots.set(debounceKey, {
      editorFrame,
      pageSettings,
      selectedElement,
      description
    })

    // Cria novo timer de debounce
    const timer = setTimeout(() => {
      const pending = pendingSnapshots.get(debounceKey)
      if (pending) {
        const snapshot = createSnapshot(
          pending.editorFrame,
          pending.pageSettings,
          pending.selectedElement,
          pending.description
        )
        if (snapshot) {
          pushSnapshot(snapshot, { replaceIfSimilar: true })
        }
        pendingSnapshots.delete(debounceKey)
      }
      debounceTimers.delete(debounceKey)
    }, debounceMs)

    debounceTimers.set(debounceKey, timer)
  }

  /**
   * Força o salvamento de todos os estados pendentes
   */
  const flushPendingStates = () => {
    // Executa todos os timers pendentes imediatamente
    for (const [key, timer] of debounceTimers.entries()) {
      clearTimeout(timer)
      const pending = pendingSnapshots.get(key)
      if (pending) {
        const snapshot = createSnapshot(
          pending.editorFrame,
          pending.pageSettings,
          pending.selectedElement,
          pending.description
        )
        if (snapshot) {
          pushSnapshot(snapshot, { replaceIfSimilar: true })
        }
        pendingSnapshots.delete(key)
      }
    }
    debounceTimers.clear()
  }

  /**
   * Executa undo
   */
  const undo = async (editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor) => {
    if (!canUndo.value || undoRedoState.isUndoing) return

    const targetIndex = undoRedoState.currentIndex - 1
    if (targetIndex >= 0 && targetIndex < undoRedoState.history.length) {
      undoRedoState.currentIndex = targetIndex
      const snapshot = undoRedoState.history[targetIndex]
      
      await applySnapshot(snapshot, editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor)
      updateCanUndoRedo()
      
      return snapshot
    }
  }

  /**
   * Executa redo
   */
  const redo = async (editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor) => {
    if (!canRedo.value || undoRedoState.isRedoing) return

    const targetIndex = undoRedoState.currentIndex + 1
    if (targetIndex < undoRedoState.history.length) {
      undoRedoState.currentIndex = targetIndex
      const snapshot = undoRedoState.history[targetIndex]
      
      await applySnapshot(snapshot, editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor)
      updateCanUndoRedo()
      
      return snapshot
    }
  }

  /**
   * Configura atalhos de teclado
   */
  const setupKeyboardShortcuts = (editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor) => {
    const handleKeyDown = (e) => {
      // Ctrl+Z ou Cmd+Z (Mac)
      if ((e.ctrlKey || e.metaKey) && e.key === 'z' && !e.shiftKey) {
        e.preventDefault()
        undo(editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor)
        return
      }

      // Ctrl+Y ou Ctrl+Shift+Z ou Cmd+Shift+Z (Mac)
      if (((e.ctrlKey || e.metaKey) && e.key === 'y') || 
          ((e.ctrlKey || e.metaKey) && e.shiftKey && e.key === 'z')) {
        e.preventDefault()
        redo(editorFrame, applyPageSettings, handleElementSelect, reinitializeEditor)
        return
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    
    // Retorna função de cleanup
    return () => {
      document.removeEventListener('keydown', handleKeyDown)
    }
  }

  /**
   * Limpa o histórico
   */
  const clearHistory = () => {
    // Limpa timers pendentes
    for (const timer of debounceTimers.values()) {
      clearTimeout(timer)
    }
    debounceTimers.clear()
    pendingSnapshots.clear()
    
    undoRedoState.history = []
    undoRedoState.currentIndex = -1
    undoRedoState.hasChanges = false
    updateCanUndoRedo()
  }

  /**
   * Inicializa com estado inicial
   */
  const initialize = (editorFrame, pageSettings, description = 'Estado inicial') => {
    clearHistory()
    const snapshot = createSnapshot(editorFrame, pageSettings, null, description)
    if (snapshot) {
      undoRedoState.history.push(snapshot)
      undoRedoState.currentIndex = 0
      updateCanUndoRedo()
    }
  }

  /**
   * Obtém informações do histórico atual
   */
  const getHistoryInfo = () => {
    return {
      total: undoRedoState.history.length,
      current: undoRedoState.currentIndex + 1,
      canUndo: canUndo.value,
      canRedo: canRedo.value,
      hasChanges: undoRedoState.hasChanges,
      currentSnapshot: undoRedoState.history[undoRedoState.currentIndex] || null
    }
  }

  /**
   * Marca como salvo (remove flag de mudanças)
   */
  const markAsSaved = () => {
    undoRedoState.hasChanges = false
  }

  return {
    // Estados reativos
    canUndo,
    canRedo,
    
    // Funções principais
    saveState,
    undo,
    redo,
    
    // Configuração
    setupKeyboardShortcuts,
    initialize,
    clearHistory,
    markAsSaved,
    flushPendingStates,
    
    // Informações
    getHistoryInfo,
    
    // Estado interno (para debug)
    undoRedoState: undoRedoState
  }
}

/**
 * Versão singleton do hook para usar em múltiplos componentes
 */
let singletonInstance = null

export function useUndoRedoSingleton() {
  if (!singletonInstance) {
    singletonInstance = useUndoRedo()
  }
  return singletonInstance
}

export default useUndoRedo 