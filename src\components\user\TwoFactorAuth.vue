<template>
  <div class="two-factor-content">
    <ViewContainer
      :title="$t('userSettings.sections.twoFactor.title')"
      :icon="ShieldKeyIcon"
      iconColor="green"
    >
      <div class="security-item">
        <div class="security-info">
          <div class="flex items-center gap-2 mb-2">
            <h4 class="security-title">{{ $t('userSettings.sections.security.authenticatorApp') }}</h4>
          </div>
          <p class="security-description">{{ $t('userSettings.sections.twoFactor.description') }}</p>
          <div class="security-status">
            <span class="status-badge" :class="totpStatus?.enabled ? 'status-enabled' : 'status-disabled'">
              <HugeiconsIcon 
                :icon="totpStatus?.enabled ? CheckmarkCircle02Icon : Alert02Icon" 
                size="14" 
                :strokeWidth="1.5" 
              />
              {{ totpStatus?.enabled ? $t('userSettings.sections.twoFactor.statusEnabled') : $t('userSettings.sections.twoFactor.statusDisabled') }}
            </span>
          </div>
        </div>
        <div class="security-actions">
          <IluriaButton
            v-if="!totpStatus?.enabled"
            color="primary"
            :icon="ShieldKeyIcon"
            @click="showSetupModal = true"
            :loading="isLoadingStatus"
          >
            {{ $t('userSettings.sections.twoFactor.enable') }}
          </IluriaButton>
          
          <IluriaButton
            v-else
            variant="danger"
            :icon="Settings02Icon"
            @click="showDisableModal = true"
            :loading="isLoadingStatus"
          >
            {{ $t('userSettings.sections.twoFactor.disable') }}
          </IluriaButton>
        </div>
      </div>
    </ViewContainer>

    <!-- Setup TOTP Modal -->
    <IluriaModal 
      v-model:visible="showSetupModal" 
      :title="$t('userSettings.sections.twoFactor.setupTitle')"
      dismissableMask
      style="width: 500px"
    >
      <div class="totp-setup">
        <div v-if="setupData" class="space-y-6">
          <!-- Instructions -->
          <div class="text-center">
            <p class=" totp-description text-sm mb-4">{{ $t('userSettings.sections.twoFactor.setupDescription') }}</p>
          </div>

          <!-- QR Code Section -->
          <div class="qr-section text-center">
            <p class="totp-qrcode text-sm font-medium mb-3">{{ $t('userSettings.sections.twoFactor.qrInstructions') }}</p>
            <div class="qr-code-container mb-4">
              <img :src="setupData.qrCodeUri" alt="QR Code" class="mx-auto" />
            </div>
            
            <!-- Manual key -->
            <div class="manual-key mb-4">
              <p class="text-xs totp-manual mb-2">{{ $t('userSettings.sections.twoFactor.manualInstructions') }}</p>
              <div class="bg-gray-100 p-2 rounded text-xs font-mono break-all">
                {{ setupData.secret }}
              </div>
            </div>
          </div>

          <!-- App suggestions -->
          <div class="app-suggestions text-center">
            <p class="text-xs totp-tips mb-4">{{ $t('userSettings.sections.twoFactor.appSuggestions') }}</p>
          </div>

          <!-- Verification Code Input -->
          <div class="verification-section">
            <IluriaLabel class="text-center block mb-3">{{ $t('userSettings.sections.twoFactor.enterCode') }}</IluriaLabel>
            <div class="mfa-code-input flex justify-center space-x-2">
              <input
                type="text"
                v-for="(digit, index) in 6"
                :key="index"
                v-model="verificationDigits[index]"
                maxlength="1"
                @input="focusNext(index)"
                @keydown.delete="focusPrevious(index)"
                @paste="handlePaste"
                class="mfa-digit w-12 h-12 text-center text-lg border border-gray-300 rounded-md focus:border-blue-500 focus:outline-none"
              />
            </div>
          </div>
        </div>
        
        <div v-else class="text-center py-8">
          <div class="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto"></div>
          <p class="mt-4 text-sm text-gray-600">{{ $t('userSettings.sections.twoFactor.generatingQR') }}</p>
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end">
          <IluriaButton
            color="primary"
            @click="verifyAndActivate"
            :loading="isVerifying"
            :disabled="!verificationCode || verificationCode.length !== 6"
          >
            {{ isVerifying ? $t('userSettings.sections.twoFactor.verifying') : $t('userSettings.sections.twoFactor.verifySetup') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>

    <!-- Disable TOTP Modal -->
    <IluriaModal 
      v-model:visible="showDisableModal" 
      :title="$t('userSettings.sections.twoFactor.disableTitle')"
      dismissableMask
      style="width: 400px"
    >
      <div class="disable-totp space-y-4">
        <p class="text-sm text-gray-600">{{ $t('userSettings.sections.twoFactor.disableDescription') }}</p>
        
        <div>
          <IluriaLabel>{{ $t('userSettings.sections.twoFactor.currentPassword') }}</IluriaLabel>
          <IluriaInputText
            v-model="currentPassword"
            type="password"
            :placeholder="$t('userSettings.sections.twoFactor.currentPasswordPlaceholder')"
          />
        </div>
      </div>

      <template #footer>
        <div class="flex justify-end space-x-3">
          <IluriaButton
            variant="outline"
            @click="cancelDisable"
          >
            {{ $t('userSettings.cancel') }}
          </IluriaButton>
          <IluriaButton
            variant="danger"
            @click="confirmDisable"
            :loading="isDisabling"
            :disabled="!currentPassword"
          >
            {{ isDisabling ? $t('userSettings.sections.twoFactor.disabling') : $t('userSettings.sections.twoFactor.disable') }}
          </IluriaButton>
        </div>
      </template>
    </IluriaModal>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from '@/services/toast.service'
import { useI18n } from 'vue-i18n'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  ShieldKeyIcon,
  Settings02Icon,
  CheckmarkCircle02Icon,
  Alert02Icon
} from '@hugeicons-pro/core-stroke-standard'
import authService from '@/services/auth.service'

// Composables
const { addToast } = useToast()
const { t } = useI18n()

// State
const totpStatus = ref(null)
const isLoadingStatus = ref(false)
const showSetupModal = ref(false)
const showDisableModal = ref(false)
const setupData = ref(null)
const verificationCode = ref('')
const verificationDigits = ref(Array(6).fill(''))
const currentPassword = ref('')
const isVerifying = ref(false)
const isDisabling = ref(false)

// Methods
const loadTotpStatus = async () => {
  isLoadingStatus.value = true
  try {
    totpStatus.value = await authService.getTotpStatus()
  } catch (error) {
    console.error('Error loading TOTP status:', error)
    addToast(t('userSettings.sections.twoFactor.setupError'), 'error')
  } finally {
    isLoadingStatus.value = false
  }
}

const loadSetupData = async () => {
  try {
    setupData.value = await authService.enableTotp()
  } catch (error) {
    console.error('Error generating TOTP setup:', error)
    addToast(t('userSettings.sections.twoFactor.setupError'), 'error')
    showSetupModal.value = false
  }
}

// Input navigation functions for 6-digit code
const focusNext = (index) => {
  if (index < 5 && verificationDigits.value[index]) {
    document.querySelectorAll('.mfa-digit')[index + 1].focus()
  }
  updateVerificationCode()
}

const focusPrevious = (index) => {
  if (index > 0 && !verificationDigits.value[index]) {
    document.querySelectorAll('.mfa-digit')[index - 1].focus()
  }
  updateVerificationCode()
}

const handlePaste = (event) => {
  event.preventDefault()

  const pasteData = event.clipboardData.getData('text').trim()
  if (pasteData.length === 6 && /^[0-9]+$/.test(pasteData)) {
    verificationDigits.value = pasteData.split('')
    updateVerificationCode()

    setTimeout(() => {
      document.querySelectorAll('.mfa-digit')[5].focus()
    }, 10)
  }
}

const updateVerificationCode = () => {
  verificationCode.value = verificationDigits.value.join('')
}



const verifyAndActivate = async () => {
  if (!setupData.value || !verificationCode.value || verificationCode.value.length !== 6) {
    return
  }

  isVerifying.value = true
  try {
    await authService.verifyTotpSetup(verificationCode.value, setupData.value.secret)
    
    addToast(t('userSettings.sections.twoFactor.totpEnabled'), 'success')
    showSetupModal.value = false
    resetSetupModal()
    await loadTotpStatus()
  } catch (error) {
    console.error('Error verifying TOTP setup:', error)
    addToast(t('userSettings.sections.twoFactor.invalidCode'), 'error')
  } finally {
    isVerifying.value = false
  }
}

const confirmDisable = async () => {
  if (!currentPassword.value) return

  isDisabling.value = true
  try {
    await authService.disableTotp(currentPassword.value)
    
    addToast(t('userSettings.sections.twoFactor.totpDisabled'), 'success')
    showDisableModal.value = false
    resetDisableModal()
    await loadTotpStatus()
  } catch (error) {
    console.error('Error disabling TOTP:', error)
    
    // Handle specific error cases
    if (error.response?.status === 400) {
      addToast(t('userSettings.sections.twoFactor.invalidPassword'), 'error')
    } else {
      addToast(t('userSettings.sections.twoFactor.disableError'), 'error')
    }
  } finally {
    isDisabling.value = false
  }
}



const cancelDisable = () => {
  showDisableModal.value = false
  resetDisableModal()
}

const resetSetupModal = () => {
  setupData.value = null
  verificationCode.value = ''
}

const resetDisableModal = () => {
  currentPassword.value = ''
}

// Watch for setup modal opening
const handleSetupModal = () => {
  if (showSetupModal.value) {
    loadSetupData()
  }
}

// Lifecycle
onMounted(() => {
  loadTotpStatus()
})

// Watch setup modal visibility
import { watch } from 'vue'
watch(showSetupModal, handleSetupModal)
</script>

<style scoped>
.two-factor-content {
  width: 100%;
  max-width: none;
}

.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 24px;
  padding: 20px 24px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  transition: all 0.2s ease;
}



.security-info {
  flex: 1;
  min-width: 0;
}

.security-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
  line-height: 1.4;
}

.security-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0 0 12px 0;
  line-height: 1.5;
}

.security-status {
  display: flex;
  align-items: center;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 4px 12px;
  border-radius: 16px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.status-enabled {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
  border: 1px solid var(--iluria-color-success-light);
}

.status-disabled {
  background: var(--iluria-color-warning-bg);
  color: var(--iluria-color-warning);
  border: 1px solid var(--iluria-color-warning-light);
}

.security-actions {
  flex-shrink: 0;
}

/* TOTP Setup Styles */
.totp-setup {
  max-width: 100%;
}

.totp-description {
  color: var(--iluria-color-text-primary) !important;
}

.totp-tips,
.totp-manual,
.totp-qrcode {
  color: var(--iluria-color-text-secondary) !important;
}

.qr-code-container img {
  max-width: 200px;
  height: auto;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 8px;
  background: white;
}

.manual-key {
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  padding: 12px;
  margin: 0 auto;
  max-width: 300px;
}

.manual-key div {
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-primary);
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  word-break: break-all;
  user-select: all;
}

.verification-section {
  max-width: 200px;
  margin: 0 auto;
}

.disable-totp {
  max-width: 100%;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .security-item {
    flex-direction: column;
    align-items: stretch;
    gap: 16px;
    padding: 16px 20px;
  }
  
  .security-actions {
    align-self: stretch;
  }

  .qr-code-container img {
    max-width: 160px;
  }

  .manual-key {
    max-width: 280px;
  }

  /* Remove hover border from security item */
  .security-item:hover {
    border-color: transparent !important;
    box-shadow: none !important;
  }


}
</style>
