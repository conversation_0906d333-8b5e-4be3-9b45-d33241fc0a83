import { BaseApi } from "./api"

export default class extends BaseApi {
    api = "http://localhost:8081/product";

    async listByStore(storeId, success, error) {
        super.authenticated()
            .onSuccess(success)
            .onApiError(error)
            .__get(`${this.api}/store/${storeId}`)
    }

    async getProduct(id, success, error) {
        super.authenticated()
            .onSuccess(success)
            .onApiError(error)
            .__get(`${this.api}/${id}`)
    }

    async createProduct(product, success, error) {
        super.authenticated()
            .withBody(product)
            .onSuccess(success)
            .onApiError(error)
            .__post(this.api)
    }

    async updateProduct(product, success, error) {
        super.authenticated()
            .withBody(product)
            .onSuccess(success)
            .onApiError(error)
            .__put(this.api)
    }

    async deleteProduct(id, success, error) {
        super.authenticated()
            .onSuccess(success)
            .onApiError(error)
            .__delete(`${this.api}/${id}`)
    }
}
