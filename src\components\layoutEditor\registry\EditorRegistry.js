import { ref, reactive } from 'vue'

// Sistema de registro de editores de propriedades
const registeredEditors = ref(new Map())
const editorCategories = ref(new Map())
const editorLoaders = ref(new Map())

/**
 * Sistema de Registro de Editores Modulares
 * 
 * Permite registro isolado de editores de propriedades sem modificar arquivos centralizados
 */
export const EditorRegistry = {
  /**
   * Registra um novo editor de propriedade
   * @param {string} type - Tipo do editor (deve corresponder ao botão da toolbar)
   * @param {Object} config - Configuração do editor
   */
  registerEditor(type, config) {
    const editorConfig = {
      type,
      component: config.component, // Nome do componente Vue
      loader: config.loader || null, // Função de carregamento dinâmico
      path: config.path || null, // Caminho do arquivo do componente
      category: config.category || 'basic',
      dependencies: config.dependencies || [], // Outros editores necessários
      dataPersistence: config.dataPersistence || {}, // Configuração de persistência
      validation: config.validation || {}, // Regras de validação
      computedCheckers: config.computedCheckers || {}, // Verificadores específicos
      props: config.props || {}, // Props padrão para o editor
      emits: config.emits || ['close'], // Eventos emitidos
      autoSave: config.autoSave !== false, // Auto-salvar mudanças
      debounceTime: config.debounceTime || 300, // Tempo de debounce para auto-save
      beforeOpen: config.beforeOpen || null, // Hook antes de abrir o editor
      afterClose: config.afterClose || null, // Hook após fechar o editor
      onDataChange: config.onDataChange || null, // Hook quando dados mudam
      ...config
    }
    
    registeredEditors.value.set(type, editorConfig)
    
    // Organiza por categoria
    const category = editorConfig.category
    if (!editorCategories.value.has(category)) {
      editorCategories.value.set(category, [])
    }
    editorCategories.value.get(category).push(editorConfig)
    
    // Registra loader se fornecido
    if (editorConfig.loader) {
      editorLoaders.value.set(type, editorConfig.loader)
    }
    
  },

  /**
   * Obtém configuração de um editor
   * @param {string} type - Tipo do editor
   * @returns {Object|null} Configuração do editor
   */
  getEditor(type) {
    return registeredEditors.value.get(type) || null
  },

  /**
   * Verifica se um editor está registrado
   * @param {string} type - Tipo do editor
   * @returns {boolean}
   */
  hasEditor(type) {
    return registeredEditors.value.has(type)
  },

  /**
   * Obtém todos os editores registrados
   * @returns {Map} Map de editores
   */
  getAllEditors() {
    return registeredEditors.value
  },

  /**
   * Obtém editores por categoria
   * @param {string} category - Categoria dos editores
   * @returns {Array} Array de editores da categoria
   */
  getEditorsByCategory(category) {
    return editorCategories.value.get(category) || []
  },

  /**
   * Carrega um editor dinamicamente
   * @param {string} type - Tipo do editor
   * @returns {Promise<Component>} Componente Vue carregado
   */
  async loadEditor(type) {
    const config = this.getEditor(type)

    if (!config) {
      console.error(`Editor não encontrado no registry: ${type}`)
      throw new Error(`Editor não encontrado: ${type}`)
    }

    // Se tem loader customizado, usa ele
    if (config.loader) {
      try {
        const result = await config.loader()
        return result.default || result
      } catch (error) {
        console.error(`Erro no loader customizado para ${type}:`, error)
        throw error
      }
    }

    // Se tem path, carrega o arquivo
    if (config.path) {
      try {
        const module = await import(/* @vite-ignore */ config.path)
        return module.default || module
      } catch (error) {
        console.error(`Erro ao carregar editor ${type}:`, error)
        throw error
      }
    }

    console.error(`Nenhum método de carregamento definido para editor: ${type}`)
    throw new Error(`Nenhum método de carregamento definido para editor: ${type}`)
  },

  /**
   * Executa verificadores computados de um editor
   * @param {string} type - Tipo do editor
   * @param {HTMLElement} element - Elemento selecionado
   * @returns {Object} Resultado dos verificadores
   */
  runComputedCheckers(type, element) {
    const config = this.getEditor(type)
    if (!config || !config.computedCheckers) return {}

    const results = {}
    Object.entries(config.computedCheckers).forEach(([name, checker]) => {
      try {
        results[name] = checker(element)
      } catch (error) {
        console.error(`Erro no verificador ${name} do editor ${type}:`, error)
        results[name] = false
      }
    })
    return results
  },

  /**
   * Valida dados antes de persistir
   * @param {string} type - Tipo do editor
   * @param {Object} data - Dados a validar
   * @returns {Object} { isValid: boolean, errors: Array }
   */
  validateData(type, data) {
    const config = this.getEditor(type)
    if (!config || !config.validation) {
      return { isValid: true, errors: [] }
    }

    const errors = []
    const rules = config.validation

    Object.entries(rules).forEach(([field, rule]) => {
      const value = data[field]
      
      if (rule.required && (value === undefined || value === null || value === '')) {
        errors.push(`Campo ${field} é obrigatório`)
      }
      
      if (rule.type && value !== undefined) {
        if (typeof value !== rule.type) {
          errors.push(`Campo ${field} deve ser do tipo ${rule.type}`)
        }
      }
      
      if (rule.min !== undefined && value < rule.min) {
        errors.push(`Campo ${field} deve ser maior que ${rule.min}`)
      }
      
      if (rule.max !== undefined && value > rule.max) {
        errors.push(`Campo ${field} deve ser menor que ${rule.max}`)
      }
      
      if (rule.pattern && !rule.pattern.test(value)) {
        errors.push(`Campo ${field} não atende ao padrão exigido`)
      }
      
      if (rule.custom && typeof rule.custom === 'function') {
        const customResult = rule.custom(value, data)
        if (customResult !== true) {
          errors.push(customResult || `Campo ${field} é inválido`)
        }
      }
    })

    return {
      isValid: errors.length === 0,
      errors
    }
  },

  /**
   * Persiste dados do editor no elemento DOM
   * @param {string} type - Tipo do editor
   * @param {HTMLElement} element - Elemento DOM
   * @param {Object} data - Dados a persistir
   */
  persistData(type, element, data) {
    const config = this.getEditor(type)
    if (!config || !config.dataPersistence) return

    const persistence = config.dataPersistence

    Object.entries(persistence).forEach(([dataKey, mapping]) => {
      const value = data[dataKey]
      if (value === undefined) return

      if (mapping.attribute) {
        // Persiste como atributo data-*
        element.setAttribute(mapping.attribute, value)
      }
      
      if (mapping.style) {
        // Persiste como estilo CSS
        element.style[mapping.style] = value
      }
      
      if (mapping.content && mapping.selector) {
        // Persiste no conteúdo de um elemento filho
        const targetElement = element.querySelector(mapping.selector)
        if (targetElement) {
          if (mapping.html) {
            targetElement.innerHTML = value
          } else {
            targetElement.textContent = value
          }
        }
      }
      
      if (mapping.class) {
        // Persiste como classe CSS
        if (value) {
          element.classList.add(mapping.class)
        } else {
          element.classList.remove(mapping.class)
        }
      }
      
      if (mapping.custom && typeof mapping.custom === 'function') {
        // Persistência customizada
        mapping.custom(element, value, data)
      }
    })
  },

  /**
   * Carrega dados do elemento DOM para o editor
   * @param {string} type - Tipo do editor
   * @param {HTMLElement} element - Elemento DOM
   * @returns {Object} Dados carregados
   */
  loadData(type, element) {
    const config = this.getEditor(type)
    if (!config || !config.dataPersistence) return {}

    const data = {}
    const persistence = config.dataPersistence

    Object.entries(persistence).forEach(([dataKey, mapping]) => {
      if (mapping.attribute) {
        // Carrega de atributo data-*
        data[dataKey] = element.getAttribute(mapping.attribute) || mapping.default
      }
      
      if (mapping.style) {
        // Carrega de estilo CSS
        data[dataKey] = element.style[mapping.style] || mapping.default
      }
      
      if (mapping.content && mapping.selector) {
        // Carrega do conteúdo de um elemento filho
        const targetElement = element.querySelector(mapping.selector)
        if (targetElement) {
          data[dataKey] = mapping.html ? 
            targetElement.innerHTML : 
            targetElement.textContent || mapping.default
        } else {
          data[dataKey] = mapping.default
        }
      }
      
      if (mapping.class) {
        // Carrega de classe CSS
        data[dataKey] = element.classList.contains(mapping.class)
      }
      
      if (mapping.computed && typeof mapping.computed === 'function') {
        // Cálculo customizado
        data[dataKey] = mapping.computed(element)
      }
    })

    return data
  }
}

/**
 * Composable para usar o sistema de editores
 */
export function useEditorRegistry() {
  return {
    registerEditor: EditorRegistry.registerEditor.bind(EditorRegistry),
    getEditor: EditorRegistry.getEditor.bind(EditorRegistry),
    hasEditor: EditorRegistry.hasEditor.bind(EditorRegistry),
    getAllEditors: EditorRegistry.getAllEditors.bind(EditorRegistry),
    getEditorsByCategory: EditorRegistry.getEditorsByCategory.bind(EditorRegistry),
    loadEditor: EditorRegistry.loadEditor.bind(EditorRegistry),
    runComputedCheckers: EditorRegistry.runComputedCheckers.bind(EditorRegistry),
    validateData: EditorRegistry.validateData.bind(EditorRegistry),
    persistData: EditorRegistry.persistData.bind(EditorRegistry),
    loadData: EditorRegistry.loadData.bind(EditorRegistry),
    registeredEditors: registeredEditors.value,
    editorCategories: editorCategories.value
  }
}

export default EditorRegistry 