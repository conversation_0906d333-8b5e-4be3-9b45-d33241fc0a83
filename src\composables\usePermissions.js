import { ref, computed, watch } from 'vue';
import { useAuthStore } from '@/stores/auth.store';
import permissionsService from '@/services/permissions.service';
import { PERMISSIONS, PERMISSION_CODES, isValidPermissionCode } from '@/constants/permissions';

// Estado global das permissões (singleton)
const userPermissions = ref(new Set());
const permissionsLoaded = ref(false);
const loading = ref(false);
const error = ref(null);

/**
 * Composable para gerenciamento de permissões do usuário
 * Integra com o backend API para buscar permissões baseadas no token JWT
 */
export function usePermissions() {
  const authStore = useAuthStore();

  /**
   * Carrega as permissões do usuário através da API do backend
   * SEGURANÇA: API valida o token e retorna permissões corretas baseadas no storeRoleId
   * PERFORMANCE: Cache automático no service layer
   */
  const loadUserPermissions = async () => {
    try {
      loading.value = true;
      error.value = null;

      // Verificar se tem token válido
      const token = authStore.storeToken;
      if (!token) {
        console.warn('Nenhum token de loja disponível');
        userPermissions.value = new Set();
        permissionsLoaded.value = true;
        return;
      }

      // Buscar permissões através da API
      const permissionsData = await permissionsService.getCurrentUserPermissions();
      
      userPermissions.value = permissionsData.permissions;
      permissionsLoaded.value = true;
      
      
    } catch (err) {
      console.error('ERRO ao carregar permissões do usuário:', err);
      error.value = err.message || 'Erro ao carregar permissões';
      
      // SEGURANÇA: Em caso de erro, usuário fica SEM permissões (fail-safe)
      userPermissions.value = new Set();
      permissionsLoaded.value = true;
    } finally {
      loading.value = false;
    }
  };


  /**
   * Verifica se o usuário possui uma permissão específica
   * @param {string|object} permission - Código da permissão ou objeto de permissão
   * @returns {boolean}
   */
  const hasPermission = (permission) => {
    if (!permissionsLoaded.value) return false;
    
    const code = typeof permission === 'string' ? permission : permission?.code;
    if (!code) return false;

    return userPermissions.value.has(code);
  };

  /**
   * Verifica se o usuário possui TODAS as permissões especificadas (AND)
   * @param {Array<string|object>} permissions - Array de permissões
   * @returns {boolean}
   */
  const hasAllPermissions = (permissions) => {
    if (!Array.isArray(permissions) || permissions.length === 0) return true;
    return permissions.every(permission => hasPermission(permission));
  };

  /**
   * Verifica se o usuário possui QUALQUER uma das permissões especificadas (OR)
   * @param {Array<string|object>} permissions - Array de permissões
   * @returns {boolean}
   */
  const hasAnyPermission = (permissions) => {
    if (!Array.isArray(permissions) || permissions.length === 0) return true;
    return permissions.some(permission => hasPermission(permission));
  };

  /**
   * Filtra uma lista baseada nas permissões do usuário
   * @param {Array} items - Lista de itens
   * @param {Function} getPermissions - Função que retorna as permissões necessárias para um item
   * @returns {Array} Lista filtrada
   */
  const filterByPermissions = (items, getPermissions) => {
    if (!Array.isArray(items)) return [];
    
    return items.filter(item => {
      const requiredPermissions = getPermissions(item);
      if (!requiredPermissions) return true;
      
      if (Array.isArray(requiredPermissions)) {
        return hasAnyPermission(requiredPermissions);
      } else {
        return hasPermission(requiredPermissions);
      }
    });
  };

  /**
   * Computed que indica se o usuário é admin da loja
   */
  const isStoreAdmin = computed(() => {
    return hasPermission(PERMISSIONS.STORE_ADMIN);
  });

  /**
   * Computed com lista de todas as permissões do usuário
   */
  const currentUserPermissions = computed(() => {
    return Array.from(userPermissions.value);
  });

  /**
   * Recarrega as permissões do usuário
   * Força uma nova busca da API ignorando cache
   */
  const refreshPermissions = async () => {
    permissionsLoaded.value = false;
    await permissionsService.refreshPermissions();
    await loadUserPermissions();
  };

  /**
   * Limpa o cache de permissões (útil no logout)
   */
  const clearPermissions = () => {
    userPermissions.value = new Set();
    permissionsLoaded.value = false;
    error.value = null;
    permissionsService.clearCache();
  };

  // Observa mudanças no token da loja para recarregar permissões
  watch(
    () => authStore.storeToken,
    async (newToken, oldToken) => {
      if (newToken !== oldToken) {
        // SEMPRE limpa as permissões primeiro para evitar estados inconsistentes
        clearPermissions();
        
        // Aguarda o próximo tick para garantir que a limpeza seja processada
        await new Promise(resolve => setTimeout(resolve, 0));
        
        if (newToken) {
          await loadUserPermissions();
        } else {
        }
      }
    },
    { immediate: true }
  );

  // Se já tem token mas permissões não foram carregadas, carrega
  if (authStore.storeToken && !permissionsLoaded.value && !loading.value) {
    loadUserPermissions();
  }

  return {
    // Estado
    userPermissions: currentUserPermissions,
    permissionsLoaded,
    loading,
    error,
    isStoreAdmin,

    // Métodos de verificação
    hasPermission,
    hasAllPermissions,
    hasAnyPermission,
    filterByPermissions,

    // Métodos de gerenciamento
    loadUserPermissions,
    refreshPermissions,
    clearPermissions,

    // Constantes
    PERMISSIONS,
    PERMISSION_CODES
  };
}

/**
 * Versão singleton do composable para uso global
 * Garante que as permissões sejam carregadas apenas uma vez
 */
let permissionsInstance = null;

export function useGlobalPermissions() {
  if (!permissionsInstance) {
    permissionsInstance = usePermissions();
  }
  return permissionsInstance;
}

/**
 * Força reset da instância singleton
 * Útil quando há mudança crítica de contexto (logout, troca de loja)
 */
export function resetGlobalPermissions() {
  if (permissionsInstance) {
    // Limpa as permissões da instância atual
    permissionsInstance.clearPermissions();
  }
  // Reset da instância para forçar nova criação
  permissionsInstance = null;
}