<template>
  <div class="themes-gallery">
    <!-- Header -->
    <IluriaHeader
      :title="$t('themes.title')"
      :subtitle="$t('themes.description')"
      :show-add="true"
      :add-text="$t('themes.addTheme')"
      :add-icon="PlusSignSquareIcon"
      @add-click="openCreateThemeModal"
    />

    <!-- Current Theme Section -->
    <ViewContainer
      :title="$t('themes.currentTheme')"
      :icon="CheckmarkSquare02Icon"
      iconColor="green"
    >
      <!-- Onboarding para loja nova -->
      <div v-if="isNewStore" class="onboarding-content">
        <!-- Header principal -->
        <div class="onboarding-header">
          <h1 class="welcome-title">Bem-vindo à Personalização da sua Loja!</h1>
          <p class="welcome-subtitle">
            Escolha um tema para começar a personalizar a aparência
            da sua loja online. O tema define as cores, tipografia e
            layout geral.
          </p>
        </div>

        <!-- Steps com números -->
        <div class="onboarding-steps">
          <div class="step-item">
            <div class="step-content">
              <span class="step-number">1</span>
              <span class="step-text">Escolha um tema da biblioteca abaixo</span>
            </div>
          </div>

          <div class="step-item">
            <div class="step-content">
              <span class="step-number">2</span>
              <span class="step-text">Personalize cores e estilos conforme sua marca</span>
            </div>
          </div>

          <div class="step-item">
            <div class="step-content">
              <span class="step-number">3</span>
              <span class="step-text">Publique e comece a vender!</span>
            </div>
          </div>
        </div>

        <!-- Call to action button -->
        <div class="onboarding-cta">
          <IluriaButton
            variant="solid"
            color="primary"
            size="large"
            @click="scrollToThemeLibrary"
            class="choose-theme-btn"
          >
            Escolher Meu Primeiro Tema
          </IluriaButton>
        </div>


      </div>

      <!-- Tema atual (quando existe) -->
      <div class="current-theme-card" :class="{ 'published': isPublished, 'not-published': !isPublished }" v-else-if="currentTheme">
        <div class="theme-preview">
          <iframe
            :src="getThemePreviewUrl(currentTheme)"
            class="preview-iframe"
            title="Theme Preview"
          ></iframe>
        </div>
        
        <div class="theme-info">
          <div class="theme-header">
            <!-- Editable theme name -->
            <InlineEditableText
              ref="themeNameEditableRef"
              v-model="currentTheme.name"
              tag="h3"
              text-class="theme-name"
              :max-length="100"
              :min-length="2"
              @save="handleThemeNameSave"
              @validation-error="handleValidationError"
            />
            <div class="theme-meta">
              <span class="theme-status" :class="{'published': isPublished, 'not-published': !isPublished}">{{ isPublished ? $t('themes.published') : $t('themes.notPublished') }}</span>
              <div class="theme-tags">
                <span
                  v-for="tag in currentTheme.tags"
                  :key="tag"
                  class="tag"
                >
                  {{ tag }}
                </span>
              </div>
            </div>
          </div>
          
          <div class="theme-actions">
            <div class="theme-hint" v-if="!publishStatus.hasValidDomain">
              <span class="hint-text">{{ $t('themes.hintDomainUrl') }} </span>
            </div>
            <IluriaButton
              variant="solid"
              color="primary"
              @click="customizeTheme(currentTheme)"
            >
              {{ $t('themes.customize') }}
            </IluriaButton>

            <!-- Botão Publicar -->
            <IluriaButton
              :variant="publishButtonVariant"
              :color="publishButtonColor"
              @click="handlePublish"
              :disabled="publishButtonDisabled"
              :loading="isPublishing"
              :huge-icon="publishButtonIcon"
            >
              {{ publishButtonText }}
            </IluriaButton>

            <ThemeActionMenu
              :theme="currentTheme"
              :is-current="true"
              :editable-text-ref="themeNameEditableRef"
              @action="handleThemeAction"
            />
          </div>
        </div>
      </div>
    </ViewContainer>

    <!-- Theme Library Section -->
    <ViewContainer
      :title="$t('themes.themeLibrary')"
      :icon="PaintBoardIcon"
      iconColor="purple"
      class="theme-library"
    >
      
      <!-- Filter Bar -->
      <div class="filter-bar">
        <div class="filter-tags">
          <!-- Mostrar filtros se disponíveis -->
          <template v-if="availableFilters.length > 0">
            <IluriaButton
              v-for="filter in availableFilters"
              :key="filter.id"
              size="small"
              :variant="activeFilters.includes(filter.id) ? 'solid' : 'outline'"
              :color="activeFilters.includes(filter.id) ? 'primary' : 'dark'"
              @click="toggleFilter(filter)"
              @keydown="(event) => { if (event.key === 'Enter' || event.key === ' ') { event.preventDefault(); toggleFilter(filter); } }"
              class="filter-button"
              :class="{ 'filter-active': activeFilters.includes(filter.id) }"
              :aria-pressed="activeFilters.includes(filter.id)"
              :aria-label="$t('themes.filterBy', { category: $t(`themes.categories.${filter.slug}`) || filter.name })"
            >
              {{ $t(`themes.categories.${filter.slug}`) || filter.name }}
            </IluriaButton>
          </template>
          
          <!-- Mostrar placeholder quando não há filtros -->
          <template v-else>
            <span class="filter-placeholder">
              {{ $t('themes.noFiltersAvailable') }}
            </span>
          </template>
        </div>
        
        <fieldset class="view-options" :aria-label="$t('themes.viewOptions')">
          <IluriaButton
            size="small"
            :variant="viewMode === 'grid' ? 'solid' : 'ghost'"
            :color="viewMode === 'grid' ? 'primary' : 'dark'"
            @click="viewMode = 'grid'"
            @keydown="handleViewModeKeydown($event, 'grid')"
            class="view-button"
            :class="{ 'view-active': viewMode === 'grid' }"
            :hugeIcon="GridIcon"
            :aria-pressed="viewMode === 'grid'"
            :aria-label="$t('themes.gridView')"
          />
          <IluriaButton
            size="small"
            :variant="viewMode === 'list' ? 'solid' : 'ghost'"
            :color="viewMode === 'list' ? 'primary' : 'dark'"
            @click="viewMode = 'list'"
            @keydown="handleViewModeKeydown($event, 'list')"
            class="view-button"
            :class="{ 'view-active': viewMode === 'list' }"
            :hugeIcon="LeftToRightListBulletIcon"
            :aria-pressed="viewMode === 'list'"
            :aria-label="$t('themes.listView')"
          />
        </fieldset>
      </div>

      <!-- Themes Grid -->
      <div class="themes-grid" :class="viewMode">
        <!-- Loading skeletons -->
        <template v-if="isLoadingThemes">
          <ThemeCardSkeleton
            v-for="i in skeletonCount"
            :key="`skeleton-${i}`"
          />
        </template>
        
        <!-- Theme cards -->
        <template v-else>
          <ThemeCard
            v-for="theme in filteredThemes"
            :key="theme.id"
            :theme="theme"
            :is-current="theme.isActive || (currentTheme && theme.id === currentTheme.id)"
            :view-mode="viewMode"
            @select="selectTheme"
            @use-default="useDefaultTheme"
            @preview="previewTheme"
            @customize="customizeTheme"
            @action="handleThemeAction"
          />
        </template>
      </div>
    </ViewContainer>


    <!-- Modals -->
    <ThemePreviewModal
      v-if="showPreviewModal"
      :key="`preview-${selectedThemeForPreview?.id}`"
      :theme="selectedThemeForPreview"
      :visible="showPreviewModal"
      @update:visible="showPreviewModal = $event"
      @close="showPreviewModal = false"
      @select="selectTheme"
    />

    <CreateThemeModal
      v-if="showCreateModal"
      key="create-theme-modal"
      :available-themes="availableThemes"
      @close="showCreateModal = false"
      @create="handleCreateTheme"
    />

    <ChangeThemeColorsModal
      v-if="showColorsModal"
      :key="`colors-${selectedThemeForColors?.id}`"
      :theme="selectedThemeForColors"
      :visible="showColorsModal"
      @update:visible="showColorsModal = $event"
      @save="handleSaveThemeColors"
      @cancel="showColorsModal = false"
    />

    <!-- Theme Backup Modal -->
    <ThemeBackupModal
      v-if="showBackupModal"
      :key="`backup-${selectedThemeForBackup?.id}`"
      :show="showBackupModal"
      :theme="selectedThemeForBackup"
      @close="showBackupModal = false"
    />
    
    <!-- Confirmation Dialog -->
    <IluriaConfirmationModal
      :is-visible="showConfirmModal"
      :title="confirmModalTitle"
      :message="confirmModalMessage"
      :confirm-text="confirmModalConfirmText"
      :cancel-text="confirmModalCancelText"
      :type="confirmModalType"
      @confirm="handleConfirm"
      @cancel="handleCancel"
    />

    <SitePublishingProgress v-if="publishProgress.isPublishing.value"
      :progress="publishProgress.progress.value"
      :error="publishProgress.error.value"/>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, nextTick, watchEffect } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import {
  PlusSignSquareIcon,
  GridIcon,
  LeftToRightListBulletIcon,
  CheckmarkSquare02Icon,
  PaintBoardIcon,
  PaintBrushIcon,
  Upload04Icon,
  Settings02Icon,
  CheckmarkCircle02Icon,
  ClockIcon
} from '@hugeicons-pro/core-stroke-standard'

import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import InlineEditableText from '@/components/utils/InlineEditableText.vue'
import ThemeCard from './components/ThemeCard.vue'
import ThemeCardSkeleton from './components/ThemeCardSkeleton.vue'
import ThemeActionMenu from './components/ThemeActionMenu.vue'
import ThemePreviewModal from './components/ThemePreviewModal.vue'
import CreateThemeModal from './components/CreateThemeModal.vue'
import ChangeThemeColorsModal from './components/ChangeThemeColorsModal.vue'
import ThemeBackupModal from './components/ThemeBackupModal.vue'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import SitePublishingProgress from '../../components/themes/SitePublishingProgress.vue'
import { useSitePublishingProgress } from '../../components/themes/useSitePublishingProgress'

import { useThemeManager } from '@/composables/useThemeManager'
import { useThemePreview } from '@/composables/useThemePreview'
import { useToast } from '@/services/toast.service'
import themeService from '@/services/themeService'
import { useConfirm } from 'primevue/useconfirm'
import { useAuthStore } from '@/stores/auth.store'


const { t } = useI18n()
const router = useRouter()
const toast = useToast()
const confirm = useConfirm()
const publishProgress = useSitePublishingProgress()
const authStore = useAuthStore()

// Composables
const {
  availableThemes,
  availableCategories,
  currentTheme,
  isNewStore,
  setCurrentTheme,
  createTheme,
  deleteTheme,
  updateTheme,
  duplicateTheme: duplicateThemeAction,
  loadThemes,
  isThemeChanging,
  themeProgress
} = useThemeManager()

const {
  getThemePreviewUrl,
  previewDevice,
  changePreviewDevice
} = useThemePreview()


// State
const viewMode = ref('grid')
const activeFilters = ref([])
const showPreviewModal = ref(false)
const showCreateModal = ref(false)
const showColorsModal = ref(false)
const showBackupModal = ref(false)
const selectedThemeForPreview = ref(null)
const selectedThemeForColors = ref(null)
const selectedThemeForBackup = ref(null)

const themeNameEditableRef = ref(null)

// Loading states
const isActivating = ref(false)
const isDuplicating = ref(false)
const isDeleting = ref(false)
const isDownloading = ref(false)
const isLoadingThemes = ref(true)
const isPublishing = ref(false)
const isPublished = ref(false)

// Estado de publicação
const publishStatus = ref({
  canPublish: false,
  hasValidDomain: false,
  hasSSL: false,
  isOutdated: false,
  message: '',
  domains: []
})

// Refs
// Modal state
const showConfirmModal = ref(false)
const confirmModalTitle = ref('')
const confirmModalMessage = ref('')
const confirmModalConfirmText = ref('Confirmar')
const confirmModalCancelText = ref('Cancelar')
const confirmModalType = ref('info')
const confirmCallback = ref(null)
const themeToduplicate = ref(null)

// Computed
const availableFilters = computed(() => {
  // Primeiro, tenta usar categorias carregadas
  if (availableCategories.value && availableCategories.value.length > 0) {
    return availableCategories.value.map(category => ({
      id: category.id,
      slug: category.slug,
      name: category.name
    }))
  }
  
  // Fallback: usar tags dos temas se não houver categorias
  const allTags = availableThemes.value.flatMap(theme => theme.tags || [])
  const uniqueTags = [...new Set(allTags)]
  
  // Converter tags em formato de filtro
  return uniqueTags.map(tag => ({
    id: tag,
    slug: tag,
    name: tag
  }))
})

const filteredThemes = computed(() => {

  if (activeFilters.value.length === 0) {
    return availableThemes.value
  }

  // Se todas as categorias estão selecionadas, mostrar todos os temas
  if (availableFilters.value.length > 0 && activeFilters.value.length === availableFilters.value.length) {
    return availableThemes.value
  }

  return availableThemes.value.filter(theme => {
    // Verificar por IDs de categoria (se estão usando categorias)
    if (theme.categoryIds && theme.categoryIds.length > 0) {
      return theme.categoryIds.some(categoryId =>
        activeFilters.value.includes(categoryId)
      )
    }

    // Fallback: verificar por tags
    if (theme.tags && theme.tags.length > 0) {
      return theme.tags.some(tag => activeFilters.value.includes(tag))
    }

    // Se o tema não tem categorias nem tags, mostrar apenas se nenhum filtro específico está ativo
    return false
  })
})

const existingThemeNames = computed(() => {
  return availableThemes.value.map(theme => theme.name)
})

// Computed para verificar se o tema atual pode ser editado
const canEditCurrentTheme = computed(() => {
  if (!currentTheme.value) return false
  // Só pode editar temas customizados ou temas padrão da loja (não globais)
  return !currentTheme.value.isDefault || (currentTheme.value.storeId && currentTheme.value.storeId !== null)
})

const skeletonCount = computed(() => {
  // Show different number of skeletons based on view mode
  return viewMode.value === 'grid' ? 6 : 4
})

// Computed properties para o botão de publicação
const publishButtonText = computed(() => {
  if (!publishStatus.value.hasValidDomain) {
    return t('themes.publish.configureDomain')
  }
  if (!publishStatus.value.hasSSL) {
    return t('themes.publish.waitingSSL')
  }
  if (!publishStatus.value.isOutdated) {
    return t('themes.publish.published')
  }
  if(isPublished.value){
    return t('themes.publish.published')
  }
  return t('themes.publish.publish')
})

const publishButtonVariant = computed(() => {
  if (!publishStatus.value.hasValidDomain) return 'outline'
  if (!publishStatus.value.hasSSL) return 'outline'
  if (!publishStatus.value.isOutdated) return 'outline'
  if (isPublished.value) return 'outline'
  return 'solid'
})

const publishButtonColor = computed(() => {
  if (!publishStatus.value.hasValidDomain) return 'warning'
  if (!publishStatus.value.hasSSL) return 'warning'
  if (!publishStatus.value.isOutdated) return 'success'
  // if (isPublished.value) return 'success'
  return 'primary'
})

const publishButtonDisabled = computed(() => {
  // Botão só fica desabilitado quando está aguardando SSL
  // Para "Configurar Domínio" e "Publicar", deve estar clicável
  return publishStatus.value.hasValidDomain && !publishStatus.value.hasSSL
})

const publishButtonIcon = computed(() => {
  if (!publishStatus.value.hasValidDomain) return Settings02Icon
  if (!publishStatus.value.hasSSL) return ClockIcon
  if (!publishStatus.value.isOutdated) return CheckmarkCircle02Icon
  // if (isPublished.value) return CheckmarkCircle02Icon
  return Upload04Icon
})

// Methods
const toggleFilter = (filter) => {
  // Usar o ID do filtro (que pode ser categoria ID ou tag)
  const filterId = typeof filter === 'object' ? filter.id : filter
  const index = activeFilters.value.indexOf(filterId)

  if (index > -1) {
    activeFilters.value.splice(index, 1)
  } else {
    activeFilters.value.push(filterId)
  }
}

const selectTheme = async (theme) => {
 
  confirmThemeActivation(theme)
}

const confirmThemeActivation = (theme) => {
 
  // Usar o modal customizado ao invés do PrimeVue confirm
  showConfirm(
    t('themes.confirmActivation.message', { themeName: theme.name }),
    t('themes.confirmActivation.title'),
    async () => {
     
      try {
        await setCurrentTheme(theme)
       
      } catch (error) {
        console.error('❌ ThemesGallery: Error selecting theme:', error)
      }
    }
  )
}

// Modal control functions
const showConfirm = (message, title, onConfirm) => {
  confirmModalMessage.value = message
  confirmModalTitle.value = title
  confirmModalConfirmText.value = t('confirm')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'info'
  confirmCallback.value = onConfirm
  showConfirmModal.value = true
}

const handleConfirm = () => {
  if (confirmCallback.value) {
    confirmCallback.value()
  }
  showConfirmModal.value = false
}

const handleCancel = () => {
  showConfirmModal.value = false
}

const useDefaultTheme = async (theme) => {
  // Mostrar diálogo de confirmação
  showConfirm(
    t('themes.confirmUseTheme.message', { themeName: theme.name }),
    t('themes.confirmUseTheme.title'),
    async () => {
      // Usuário confirmou - aplicar o tema
      try {
        await setCurrentTheme(theme)
      } catch (error) {
        console.error('ThemesGallery: Error applying default theme:', error)
        toast.showError(
          t('themes.errorApplyingTheme'),
          { title: t('themes.error') }
        )
      }
    }
  )
}

const customizeTheme = (theme) => {
  // Redirecionar para o editor com o tema selecionado
  router.push({
    name: 'layout-editor',
    query: { theme: theme.id }
  })
}

// Método para lidar com publicação
const handlePublish = async () => {
 

  if (!publishStatus.value.hasValidDomain) {
    // Redirecionar para configuração de domínios
    router.push({ path: '/settings/domain-manager' })
    return
  }

  if (!publishStatus.value.canPublish) {
    return
  }

  isPublishing.value = true

  await publishProgress.startListening(authStore.userEmail)

  try {
    await publishToProduction()
    await checkPublishStatus()

    toast.showSuccess(
      t('themes.publish.publishSuccess'),
      { title: t('themes.success') }
    )
  } catch (error) {
    console.error('ThemesGallery: Error publishing theme:', error)
    toast.showError(
      t('themes.publish.publishError'),
      { title: t('themes.error') }
    )
  } finally {
    isPublishing.value = false
  }
}

watchEffect(() => {
  if (publishProgress.progress.value.step === 'COMPLETED') {
    setTimeout(() => {
      publishProgress.disconnect()
    }, 3000)
  }

  if (publishProgress.progress.value.step === 'ERROR') {
    setTimeout(() => {
      publishProgress.disconnect()
    }, 3000)
  }
})

// Método para verificar status de publicação
const checkPublishStatus = async () => {
  try {
    const response = await themeService.getPublishStatus()
    publishStatus.value = response
  } catch (error) {
    // Fallback em caso de erro
    publishStatus.value = {
      canPublish: false,
      hasValidDomain: false,
      hasSSL: false,
      isOutdated: false,
      message: 'Erro ao verificar status de publicação',
      domains: []
    }
    console.error('ThemesGallery: Error checking publish status:', error)
  }
}

// Método para publicar para produção
const publishToProduction = async () => {
  try{
  const response = await themeService.publishToProduction()

    // Atualizar status após publicação bem-sucedida
    publishStatus.value.isOutdated = false
    publishStatus.value.message = 'Sua loja está atualizada e publicada'
    isPublished.value = response.success
    return response
  } catch (error) {
    console.error('Erro ao publicar:', error)
    throw error
  }
}

const previewTheme = (theme) => {
  // Ensure clean state before opening
  if (showPreviewModal.value) {
    showPreviewModal.value = false
    // Use nextTick to ensure the modal is fully closed before reopening
    nextTick(() => {
      selectedThemeForPreview.value = theme
      showPreviewModal.value = true
    })
  } else {
    selectedThemeForPreview.value = theme
    showPreviewModal.value = true
  }
}

const openCreateThemeModal = () => {
  showCreateModal.value = true
}

const scrollToThemeLibrary = () => {
  const libraryElement = document.querySelector('.theme-library')
  if (libraryElement) {
    libraryElement.scrollIntoView({ behavior: 'smooth', block: 'start' })
  }
}

const handleCreateTheme = async (themeData) => {
  try {
    const newTheme = await createTheme(themeData)
    showCreateModal.value = false
    // Optionally set as current theme
    if (themeData.setAsCurrent) {
      setCurrentTheme(newTheme)
    }
  } catch (error) {
    console.error('ThemesGallery: Error creating theme:', error)
  }
}



const handleThemeAction = async (actionData, theme) => {
  // Determinar o tema a ser usado
  let targetTheme = theme

  // Se actionData é um objeto com theme, usar esse tema
  if (actionData && typeof actionData === 'object' && actionData.theme) {
    targetTheme = actionData.theme
  }

  // Se ainda não temos tema, usar currentTheme
  if (!targetTheme) {
    targetTheme = currentTheme.value
  }

  // Determinar o tipo da ação
  const actionType = actionData?.type || actionData

  if (!targetTheme) {
    toast.showError('Erro: Nenhum tema disponível para esta ação.', {
      title: 'Erro'
    })
    return
  }

  switch (actionType) {
    case 'preview':
      previewTheme(targetTheme)
      break
    case 'activate':
      await setCurrentTheme(targetTheme)
      break
    case 'duplicate':
      await duplicateTheme(targetTheme)
      break
    case 'delete':
      confirmThemeDeletion(targetTheme)
      break
    case 'rename':
      if (actionData.newName) {
        await renameTheme(targetTheme, actionData.newName)
      }
      break
    case 'change-colors':
      await changeThemeColors(targetTheme)
      break
    case 'view-backups':
      viewThemeBackups(targetTheme)
      break
    default:
      break
  }
}

const duplicateTheme = async (theme) => {
  // Configurar o modal de confirmação
  themeToduplicate.value = theme
  confirmModalTitle.value = t('themes.confirmDuplication.title')
  confirmModalMessage.value = t('themes.confirmDuplication.message', { themeName: theme.name })
  confirmModalConfirmText.value = t('themes.confirmDuplication.confirm')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'info'

  // Definir callback de confirmação
  confirmCallback.value = async () => {
    isDuplicating.value = true

    // Mostrar toast informativo antes de iniciar a duplicação
    toast.showInfo(
      t('themes.duplicating.message', { themeName: theme.name }),
      {
        title: t('themes.duplicating.title'),
        duration: 3000
      }
    )

    try {
      await duplicateThemeAction(theme)
    } catch (error) {
      console.error('ThemesGallery: Error duplicating theme:', error)
    } finally {
      isDuplicating.value = false
    }
  }

  // Mostrar modal
  showConfirmModal.value = true
}



const confirmThemeDeletion = (theme) => {
  // Configurar o modal de confirmação para exclusão
  confirmModalTitle.value = t('themes.confirmDeletion.title')
  confirmModalMessage.value = t('themes.confirmDeletion.message', { themeName: theme.name })
  confirmModalConfirmText.value = t('themes.confirmDeletion.confirm')
  confirmModalCancelText.value = t('cancel')
  confirmModalType.value = 'error'

  // Definir callback de confirmação
  confirmCallback.value = async () => {
    isDeleting.value = true
    try {
      await deleteTheme(theme)
    } catch (error) {
      console.error('ThemesGallery: Error deleting theme:', error)
    } finally {
      isDeleting.value = false
    }
  }

  // Mostrar modal
  showConfirmModal.value = true
}



const changeThemeColors = async (theme) => {
  if (!theme || !theme.id) {
    toast.showError('Erro: Tema inválido selecionado.', {
      title: 'Erro'
    })
    return
  }

  // Verificar se o tema pode ser editado
  // Não pode editar apenas se for um tema padrão global (sem storeId)
  if (theme.isDefault && (!theme.storeId || theme.storeId === null)) {
    toast.showError('Não é possível alterar as cores de temas da biblioteca global. Duplique o tema primeiro para personalizá-lo.', {
      title: 'Ação não permitida'
    })
    return
  }

  selectedThemeForColors.value = theme
  showColorsModal.value = true
}

const handleSaveThemeColors = async ({ theme, colors }) => {
  try {
    // Converter cores para cssVariables (formato esperado pelo backend)
    const cssVariables = {
      'color-background-general': colors.backgroundGeneral,
      'color-background-components': colors.backgroundComponents,
      'color-title': colors.titleColor,
      'color-text': colors.textColor
    }

    // Validar cores antes de enviar
    for (const [key, value] of Object.entries(cssVariables)) {
      if (!value || typeof value !== 'string') {
        toast.showError(`Cor inválida para ${key}: ${value}`, {
          title: 'Erro de validação'
        })
        return
      }
      if (!value.match(/^#[0-9A-Fa-f]{6}$/)) {
        toast.showError(`Formato de cor inválido para ${key}: ${value}`, {
          title: 'Erro de validação'
        })
        return
      }
    }

    // Manter cssVariables existentes e adicionar/atualizar as cores
    const existingCssVariables = theme.cssVariables || {}
    const updatedCssVariables = {
      ...existingCssVariables,
      ...cssVariables
    }

    // Atualizar tema com as novas cores
    await updateTheme(theme.id, {
      cssVariables: updatedCssVariables
    })

  } catch (error) {
    toast.showError('Não foi possível salvar as cores do tema.', {
      title: 'Erro ao salvar cores'
    })
    throw error // Re-throw para o modal tratar
  }
}

const viewThemeBackups = (theme) => {
  if (!theme) {
    return
  }

  selectedThemeForBackup.value = theme
  showBackupModal.value = true
}

// Inline editing methods
const handleThemeNameSave = async (newName) => {
  try {
    await updateTheme(currentTheme.value.id, { name: newName })
  } catch (error) {
    console.error('ThemesGallery: Error updating theme name:', error)
  }
}

const renameTheme = async (theme, newName) => {
  try {
    await updateTheme(theme.id, { name: newName })
  } catch (error) {
    toast.showError(error.message || 'Não foi possível renomear o tema.', {
      title: 'Erro ao renomear tema'
    })
  }  
}

const handleValidationError = (errorMessage) => {
  toast.showError(errorMessage, {
    title: 'Erro de validação'
  })
}

// Keyboard event handler for view mode buttons
const handleViewModeKeydown = (event, mode) => {
  // Handle Enter and Space key presses for accessibility
  if (event.key === 'Enter' || event.key === ' ') {
    event.preventDefault()
    viewMode.value = mode
  }
}

// Lifecycle
onMounted(async () => {
  try {
    isLoadingThemes.value = true
    await loadThemes()

    // Verificar status de publicação
    await checkPublishStatus()
  } catch (error) {
    console.error('ThemesGallery: Error loading themes or checking publish status:', error)
  } finally {
    // Add a small delay to show skeleton animation
    setTimeout(() => {
      isLoadingThemes.value = false
    }, 800)
  }
})
</script>

<style scoped>
.themes-gallery {
  padding: 3rem 2.5rem;
  max-width: 1600px;
  margin: 0 auto;
  background: 
    radial-gradient(circle at 20% 80%, rgba(var(--iluria-color-primary-rgb), 0.03) 0%, transparent 50%),
    radial-gradient(circle at 80% 20%, rgba(var(--iluria-color-secondary-rgb), 0.03) 0%, transparent 50%),
    linear-gradient(135deg, rgba(var(--iluria-color-primary-rgb), 0.01) 0%, transparent 50%, rgba(var(--iluria-color-secondary-rgb), 0.01) 100%);
  min-height: 100vh;
  position: relative;
}


/* Sections com ViewContainer */
.themes-gallery > :not(:last-child) {
  margin-bottom: 4rem;
}

.themes-gallery::before {
  content: '';
  position: absolute;
  top: 0;
  left: 50%;
  transform: translateX(-50%);
  width: 120%;
  height: 300px;
  background: linear-gradient(180deg, rgba(var(--iluria-color-primary-rgb), 0.02) 0%, transparent 100%);
  pointer-events: none;
  z-index: -1;
}

.section-title {
  font-size: 1.75rem;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin-bottom: 2rem;
  display: flex;
  align-items: center;
  gap: 0.75rem;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.section-icon {
  width: 1.75rem;
  height: 1.75rem;
  opacity: 0.9;
}

.current-theme-card {
  display: grid;
  grid-template-columns: 1fr .7fr;
  background: linear-gradient(135deg, var(--iluria-color-surface) 0%, rgba(var(--iluria-color-primary-rgb), 0.02) 100%);
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  overflow: hidden;
  box-shadow: 0 8px 25px -5px rgba(0, 0, 0, 0.1), 0 8px 10px -6px rgba(0, 0, 0, 0.1);
  transition: all 0.3s ease;
  position: relative;
}

.current-theme-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  z-index: 1;
}

.current-theme-card.published::before {
  background: linear-gradient(90deg, var(--iluria-color-success) 0%, var(--iluria-color-primary) 100%);
}

.current-theme-card.not-published::before {
  background: linear-gradient(90deg, var(--iluria-color-warning) 0%, var(--iluria-color-error) 100%);
}

.theme-preview {
  position: relative;
  aspect-ratio: 16/10;
  background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
  border-radius: 16px;
  padding: 1.25rem;
  overflow: hidden;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.preview-iframe {
  width: 100%;
  height: 100%;
  border: none;
}


.theme-info {
  padding: 1.5rem;
  display: flex;
  flex-direction: column;
  justify-content: space-between;
  background: linear-gradient(180deg, transparent 0%, rgba(0, 0, 0, 0.01) 100%);
}

.theme-header {
  margin-bottom: 2rem;
}

.current-theme-card .theme-name,
.current-theme-card .theme-name.editable-text,
.current-theme-card h3.theme-name,
.current-theme-card h3.theme-name.editable-text {
  font-size: 2rem !important;
  font-weight: 700 !important;
  color: var(--iluria-color-text-primary) !important;
  margin-bottom: 1rem !important;
  line-height: 1.3 !important;
  letter-spacing: -0.02em !important;
}

.theme-meta {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.theme-status {
  display: inline-flex;
  align-items: center;
  padding: 0.25rem 0.75rem;
  
  border-radius: 20px;
  font-size: 0.875rem;
  font-weight: 500;
  width: fit-content;
}

.theme-status.published{
  color: var(--iluria-color-success);
}

.theme-status.not-published{
  color: var(--iluria-color-warning);
}

.theme-tags {
  display: flex;
  flex-wrap: wrap;
  gap: 0.5rem;
}

.tag {
  padding: 0.25rem 0.75rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 20px;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
}

.theme-actions {
  display: flex;
  justify-content: space-around;
  align-items: center;
  flex-wrap: wrap;
  /* margin-top: 2rem; */
}

.theme-actions .iluria-button {
  min-width: 120px;
  white-space: nowrap;
  font-weight: 500;
  flex-shrink: 0;
}

.theme-hint{
  font-size: smaller;
  padding: 0.25rem 0.75rem;
  margin-bottom: .2rem;
  color: var(--iluria-color-text-secondary);
}

/* Theme Library */
.theme-library-section {
  margin-bottom: 4rem;
}

.filter-bar {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 3rem;
  padding: 2rem;
  background: 
    linear-gradient(135deg, var(--iluria-color-surface) 0%, rgba(var(--iluria-color-primary-rgb), 0.03) 100%);
  border: 1px solid rgba(var(--iluria-color-primary-rgb), 0.08);
  border-radius: 20px;
  box-shadow: 
    0 8px 32px rgba(0, 0, 0, 0.06),
    0 1px 4px rgba(0, 0, 0, 0.02),
    inset 0 1px 0 rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(12px);
  position: relative;
  overflow: hidden;
}

.filter-bar::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 1px;
  background: linear-gradient(90deg, transparent 0%, rgba(var(--iluria-color-primary-rgb), 0.1) 50%, transparent 100%);
}

.filter-tags {
  display: flex;
  gap: 0.75rem;
  flex-wrap: wrap;
  position: relative;
  z-index: 2;
}

/* Filter button improvements */
.filter-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.filter-button.filter-active {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(var(--iluria-color-primary-rgb), 0.3);
}

.filter-button:hover {
  transform: translateY(-1px);
}

.filter-button:focus {
  outline: 2px solid var(--iluria-color-primary);
  outline-offset: 2px;
}

.filter-placeholder {
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  font-style: italic;
  padding: 0.5rem 1rem;
  background: rgba(var(--iluria-color-primary-rgb), 0.05);
  border-radius: 8px;
  border: 1px dashed var(--iluria-color-border);
}

.view-options {
  display: flex;
  gap: 0.5rem;
  padding: 0.5rem;
  background: rgba(var(--iluria-color-primary-rgb), 0.05);
  border-radius: 12px;
  position: relative;
  z-index: 2;
}

/* View option button improvements */
.view-button {
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.view-button.view-active {
  box-shadow: 0 2px 8px rgba(var(--iluria-color-primary-rgb), 0.3);
}

.view-button:hover {
  transform: translateY(-1px);
}

.view-button:focus {
  outline: 2px solid var(--iluria-color-primary);
  outline-offset: 2px;
}

.themes-grid {
  display: grid;
  gap: 2rem;
  overflow: visible;
  position: relative;
  padding: 1rem 0;
  transition: all 0.3s ease;
  z-index: 1;
}

.themes-grid.grid {
  grid-template-columns: repeat(auto-fill, minmax(420px, 1fr));
  align-items: start;
}

.themes-grid.list {
  grid-template-columns: 1fr;
  gap: 1.5rem;
}

/* Grid item animations */
.themes-grid > * {
  animation: theme-card-enter 0.6s cubic-bezier(0.4, 0, 0.2, 1) both;
}

.themes-grid > *:nth-child(1) { animation-delay: 0ms; }
.themes-grid > *:nth-child(2) { animation-delay: 100ms; }
.themes-grid > *:nth-child(3) { animation-delay: 200ms; }
.themes-grid > *:nth-child(4) { animation-delay: 300ms; }
.themes-grid > *:nth-child(5) { animation-delay: 400ms; }
.themes-grid > *:nth-child(6) { animation-delay: 500ms; }
.themes-grid > *:nth-child(n+7) { animation-delay: 600ms; }

@keyframes theme-card-enter {
  from {
    opacity: 0;
    transform: translateY(20px) scale(0.95);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

/* AI Suggestions */
.ai-suggestions-section {
  margin-bottom: 4rem;
}

.section-header {
  margin-bottom: 1.5rem;
}

.beta-badge {
  background: var(--iluria-color-warning-light);
  color: var(--iluria-color-warning);
  padding: 0.25rem 0.75rem;
  border-radius: 20px;
  font-size: 0.75rem;
  font-weight: 500;
  margin-left: 1rem;
}

.ai-suggestions-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 1.5rem;
}

.ai-suggestion-card {
  padding: 1.5rem;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  display: flex;
  align-items: center;
  gap: 1rem;
  opacity: 0.7;
}

.suggestion-icon {
  width: 3rem;
  height: 3rem;
  background: var(--iluria-color-primary-light);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-primary);
}

.suggestion-content h4 {
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin-bottom: 0.5rem;
}

.suggestion-content p {
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
}

/* Responsive */
@media (max-width: 1024px) {
  .current-theme-card {
    grid-template-columns: 1fr;
  }
  
  .theme-info {
    padding: 1.5rem;
  }
  
  .themes-grid.grid {
    grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
  }
}

/* Onboarding styles */

.onboarding-content {
  max-width: 48rem;
  margin: 0 auto 3rem auto;
  position: relative;
  z-index: 1;
  text-align: center;
  padding: 2rem 1rem;
}

/* Header principal */
.onboarding-header {
  margin-bottom: 3rem;
}

.welcome-title {
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--iluria-color-text-primary);
  margin-bottom: 1.5rem;
  letter-spacing: -0.025em;
  line-height: 1.2;
}

.welcome-subtitle {
  font-size: 1.125rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.7;
  max-width: 600px;
  margin: 0 auto;
}

/* Steps redesenhados */
.onboarding-steps {
  margin-bottom: 3rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
  max-width: 600px;
  margin-left: auto;
  margin-right: auto;
}

.step-item {
  display: flex;
  align-items: center;
  text-align: left;
  padding: 1.5rem;
  background: var(--iluria-color-background);
  border: 1px solid var(--iluria-color-border);
  border-radius: 16px;
  backdrop-filter: blur(10px);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
}

.step-item:hover {
  background: var(--iluria-color-surface);
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
  border-color: var(--iluria-color-border-hover);
}

.step-content {
  display: flex;
  align-items: center;
  gap: 1rem;
  flex: 1;
  width: 100%;
}

.step-number {
  flex-shrink: 0;
  width: 2rem;
  height: 2rem;
  background: var(--iluria-color-text-primary);
  color: var(--iluria-color-surface);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: 700;
  font-size: 0.875rem;
}

.step-text {
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  font-size: 1rem;
  line-height: 1.5;
}

/* Call to action */
.onboarding-cta {
  margin-bottom: 2.5rem;
  text-align: center;
  display: flex;
  justify-content: center;
}

.choose-theme-btn {
  font-size: 1.125rem;
  font-weight: 600;
  padding: 1rem 2.5rem !important;
  border-radius: 12px !important;
  box-shadow: 0 4px 14px rgba(59, 130, 246, 0.3) !important;
  transition: all 0.3s ease !important;
}

.choose-theme-btn:hover {
  transform: translateY(-1px);
  box-shadow: 0 6px 20px rgba(59, 130, 246, 0.4) !important;
}



@media (max-width: 768px) {
  .themes-gallery {
    padding: 2rem 1rem;
  }
  
  .filter-bar {
    flex-direction: column;
    gap: 1.5rem;
    align-items: stretch;
    padding: 1.5rem;
  }
  
  .filter-tags {
    justify-content: center;
  }
  
  .view-options {
    align-self: center;
  }
  
  .themes-grid.grid {
    grid-template-columns: 1fr;
    gap: 1.5rem;
  }
  
  .themes-grid > * {
    animation-delay: 0ms !important;
  }

  .welcome-title {
    font-size: 2rem;
  }
  
  .welcome-subtitle {
    font-size: 1rem;
  }

  .current-theme-card .theme-name,
  .current-theme-card .theme-name.editable-text,
  .current-theme-card h3.theme-name,
  .current-theme-card h3.theme-name.editable-text {
    font-size: 1.875rem !important;
  }

  .onboarding-description {
    font-size: 1rem;
  }

  .new-store-onboarding {
    padding: 2.5rem 1.5rem;
    margin: 1rem;
  }

  .onboarding-steps {
    gap: 1rem;
  }

  .step-item {
    text-align: center;
    padding: 1.25rem;
  }

  .step-content {
    flex-direction: column;
    gap: 0.75rem;
    justify-content: center;
  }

  .choose-theme-btn {
    font-size: 1rem;
    padding: 0.875rem 2rem !important;
  }
  
  .section-title {
    font-size: 1.5rem;
  }
}

  /* Dark mode compatibility */
  [data-theme="dark"] .filter-button.filter-active,
  .dark .filter-button.filter-active {
    box-shadow: 0 4px 12px rgba(59, 130, 246, 0.4);
  }

  [data-theme="dark"] .view-button.view-active,
  .dark .view-button.view-active {
    box-shadow: 0 2px 8px rgba(59, 130, 246, 0.4);
  }

  /* Dark mode specific styles for onboarding */
  [data-theme="dark"] .new-store-onboarding,
  .dark .new-store-onboarding {
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.3),
      0 10px 10px -5px rgba(0, 0, 0, 0.1);
  }

  [data-theme="dark"] .step-item,
  .dark .step-item {
    background: var(--iluria-color-card-bg);
    border-color: var(--iluria-color-border);
  }

  [data-theme="dark"] .step-item:hover,
  .dark .step-item:hover {
    background: var(--iluria-color-surface);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
    border-color: var(--iluria-color-border-hover);
  }

  [data-theme="dark"] .choose-theme-btn,
  .dark .choose-theme-btn {
    box-shadow: 0 4px 14px rgba(59, 130, 246, 0.4) !important;
  }

  [data-theme="dark"] .choose-theme-btn:hover,
  .dark .choose-theme-btn:hover {
    box-shadow: 0 6px 20px rgba(59, 130, 246, 0.5) !important;
  }

  /* Regra global para garantir que o tamanho da fonte seja aplicado */
  .current-theme-card .theme-name,
  .current-theme-card .theme-name.editable-text,
  .current-theme-card h3.theme-name,
  .current-theme-card h3.theme-name.editable-text,
  .current-theme-card [class*="theme-name"],
  .current-theme-card h3[class*="theme-name"] {
    font-size: 2.25rem !important;
    font-weight: 700 !important;
    color: var(--iluria-color-text-primary) !important;
    line-height: 1.3 !important;
    letter-spacing: -0.02em !important;
  }

  /* Força aplicação em elementos inline-editable */
  .current-theme-card .inline-editable .editable-text.theme-name,
  .current-theme-card .inline-editable h3.editable-text.theme-name {
    font-size: 2.25rem !important;
    font-weight: 700 !important;
  }

  @media (max-width: 768px) {
    .current-theme-card .theme-name,
    .current-theme-card .theme-name.editable-text,
    .current-theme-card h3.theme-name,
    .current-theme-card h3.theme-name.editable-text,
    .current-theme-card [class*="theme-name"],
    .current-theme-card h3[class*="theme-name"],
    .current-theme-card .inline-editable .editable-text.theme-name,
    .current-theme-card .inline-editable h3.editable-text.theme-name {
      font-size: 1.875rem !important;
    }
  }

</style>