<template>
  <div class="top-products">
    <div v-if="loading" class="loading-state">
      <div v-for="i in 5" :key="i" class="product-skeleton"></div>
    </div>
    
    <div v-else-if="!products.length" class="empty-state">
      <div class="empty-icon">
        <PackageIcon class="icon" />
      </div>
      <h3 class="empty-title">{{ $t('dashboard.noProductSales') }}</h3>
      <p class="empty-description">{{ $t('dashboard.noProductSalesDesc') }}</p>
    </div>
    
    <div v-else class="products-list">
      <div class="products-header">
        <span class="header-item">{{ $t('dashboard.product') }}</span>
        <span class="header-item text-center">{{ $t('dashboard.sales') }}</span>
        <span class="header-item text-right">{{ $t('dashboard.revenue') }}</span>
      </div>
      
      <div class="products-body">
        <div 
          v-for="(product, index) in products" 
          :key="product.id"
          class="product-row"
          @click="handleProductClick(product)"
        >
          <div class="product-info">
            <div class="product-rank">
              {{ index + 1 }}
            </div>
            <div class="product-image">
              <img 
                v-if="product.image" 
                :src="product.image" 
                :alt="product.name"
                class="image"
              />
              <div v-else class="image-placeholder">
                <PackageIcon class="placeholder-icon" />
              </div>
            </div>
            <div class="product-details">
              <h4 class="product-name">{{ product.name }}</h4>
              <p class="product-id">ID: {{ product.id }}</p>
            </div>
          </div>
          
          <div class="product-sales">
            <span class="sales-count">{{ product.sales }}</span>
            <span class="sales-label">{{ $t('dashboard.units') }}</span>
          </div>
          
          <div class="product-revenue">
            <span class="revenue-amount">R$ {{ formatCurrency(product.revenue) }}</span>
            <div class="revenue-bar">
              <div 
                class="revenue-fill" 
                :style="{ width: getRevenuePercentage(product.revenue) + '%' }"
              ></div>
            </div>
          </div>
        </div>
      </div>
      
      <div class="products-footer">
        <IluriaButton 
          variant="ghost" 
          color="dark"
          size="small"
          @click="viewAllProducts"
        >
          {{ $t('dashboard.viewAllProducts') }}
        </IluriaButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue'
import { useRouter } from 'vue-router'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { PackageIcon } from '@hugeicons-pro/core-stroke-rounded'

const props = defineProps({
  products: {
    type: Array,
    default: () => []
  },
  loading: {
    type: Boolean,
    default: false
  }
})

const router = useRouter()
const { t } = useI18n()

const maxRevenue = computed(() => {
  if (!props.products.length) return 0
  return Math.max(...props.products.map(p => p.revenue))
})

const formatCurrency = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    minimumFractionDigits: 2,
    maximumFractionDigits: 2
  }).format(value)
}

const getRevenuePercentage = (revenue) => {
  if (maxRevenue.value === 0) return 0
  return (revenue / maxRevenue.value) * 100
}

const handleProductClick = (product) => {
  router.push(`/products/${product.id}`)
}

const viewAllProducts = () => {
  router.push('/products')
}
</script>

<style scoped>
.top-products {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.loading-state {
  display: flex;
  flex-direction: column;
  gap: 12px;
  padding: 8px 0;
}

.product-skeleton {
  height: 64px;
  background: linear-gradient(90deg, #f3f4f6 25%, #e5e7eb 50%, #f3f4f6 75%);
  background-size: 200% 100%;
  animation: loading 1.5s infinite;
  border-radius: 8px;
}

@keyframes loading {
  0% { background-position: 200% 0; }
  100% { background-position: -200% 0; }
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  height: 100%;
  padding: 32px 16px;
  text-align: center;
}

.empty-icon {
  width: 48px;
  height: 48px;
  background: rgba(107, 114, 128, 0.1);
  border-radius: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  margin-bottom: 16px;
}

.empty-icon .icon {
  width: 24px;
  height: 24px;
  color: var(--iluria-color-text-secondary);
}

.empty-title {
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 8px 0;
}

.empty-description {
  font-size: 0.875rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.products-list {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.products-header {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr;
  gap: 16px;
  padding: 12px 16px;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 8px 8px 0 0;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.header-item {
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.05em;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.text-center {
  text-align: center;
}

.text-right {
  text-align: right;
}

.products-body {
  flex: 1;
  overflow-y: auto;
}

.product-row {
  display: grid;
  grid-template-columns: 2fr 1fr 1.5fr;
  gap: 16px;
  padding: 16px;
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  align-items: center;
}

.product-row:hover {
  background: var(--iluria-color-hover);
}

.product-row:last-child {
  border-bottom: none;
}

.product-info {
  display: flex;
  align-items: center;
  gap: 12px;
  min-width: 0;
}

.product-rank {
  width: 24px;
  height: 24px;
  background: var(--iluria-color-sidebar-bg);
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  flex-shrink: 0;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-image {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  overflow: hidden;
  flex-shrink: 0;
}

.image {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  background: var(--iluria-color-sidebar-bg);
  display: flex;
  align-items: center;
  justify-content: center;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.placeholder-icon {
  width: 20px;
  height: 20px;
  color: var(--iluria-color-text-muted);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-details {
  min-width: 0;
  flex: 1;
}

.product-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 2px 0;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  line-height: 1.2;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-id {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-sales {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 2px;
}

.sales-count {
  font-size: 1rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.sales-label {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.product-revenue {
  display: flex;
  flex-direction: column;
  align-items: flex-end;
  gap: 6px;
}

.revenue-amount {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  transition: color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.revenue-bar {
  width: 60px;
  height: 4px;
  background: var(--iluria-color-border);
  border-radius: 2px;
  overflow: hidden;
  transition: background-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.revenue-fill {
  height: 100%;
  background: linear-gradient(90deg, var(--iluria-color-success), #059669);
  border-radius: 2px;
  transition: width 0.3s ease;
}

.products-footer {
  padding: 16px;
  border-top: 1px solid var(--iluria-color-border);
  display: flex;
  justify-content: center;
  transition: border-color 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

@media (max-width: 768px) {
  .products-header {
    display: none;
  }
  
  .product-row {
    grid-template-columns: 1fr;
    gap: 12px;
    padding: 16px;
    border: 1px solid #e5e7eb;
    border-radius: 8px;
    margin-bottom: 8px;
    
  }
  
  .product-info {
    gap: 12px;
  }
  
  .product-rank {
    position: absolute;
    top: 12px;
    right: 12px;
  }
  
  .product-sales,
  .product-revenue {
    flex-direction: row;
    justify-content: space-between;
    align-items: center;
    padding: 8px 0;
    border-top: 1px solid #f3f4f6;
  }
  
  .product-sales::before {
    content: "Vendas:";
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
  }
  
  .product-revenue::before {
    content: "Receita:";
    font-size: 0.75rem;
    font-weight: 600;
    color: #6b7280;
  }
  
  .revenue-bar {
    width: 80px;
  }
}
</style> 
