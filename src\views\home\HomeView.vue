<template>
  <div class="dashboard-container">
    <!-- Header -->
    <IluriaHeader
      :title="$t('dashboard.title')"
      :subtitle="$t('dashboard.subtitle')"
    />

    <!-- Main Dashboard Content -->
    <div class="dashboard-content">
      <!-- Key Metrics Row - FIRST -->
      <div class="metrics-grid">
        <ViewContainer 
          :title="$t('dashboard.totalSales')"
          :icon="DollarSquareIcon"
          iconColor="green"
          class="metric-card"
        >
          <MetricCard 
            :value="metrics.totalSales"
            :change="metrics.salesChange"
            :trend="metrics.salesTrend"
            currency
          />
        </ViewContainer>

        <ViewContainer 
          :title="$t('dashboard.totalOrders')"
          :icon="ShoppingCart01Icon"
          iconColor="blue"
          class="metric-card"
        >
          <MetricCard 
            :value="metrics.totalOrders"
            :change="metrics.ordersChange"
            :trend="metrics.ordersTrend"
          />
        </ViewContainer>

        <ViewContainer 
          :title="$t('dashboard.totalReviews')"
          :icon="StarIcon"
          iconColor="yellow"
          class="metric-card"
        >
          <MetricCard 
            :value="metrics.averageRating"
            :change="metrics.reviewsChange"
            :trend="metrics.reviewsTrend"
            :showDecimal="true"
          />
        </ViewContainer>

        <ViewContainer 
          :title="$t('dashboard.totalCustomers')"
          :icon="User03Icon"
          iconColor="orange"
          class="metric-card"
        >
          <MetricCard 
            :value="metrics.totalCustomers"
            :change="metrics.customersChange"
            :trend="metrics.customersTrend"
          />
        </ViewContainer>
      </div>

      <!-- Quick Actions and Recent Activity -->
      <div class="actions-activity-grid">
        <ViewContainer 
          :title="$t('dashboard.quickActions')"
          :icon="ZapIcon"
          iconColor="yellow"
          class="quick-actions-container"
        >
          <QuickActions />
        </ViewContainer>

        <ViewContainer 
          :title="$t('dashboard.recentOrders')"
          :subtitle="$t('dashboard.recentOrdersSubtitle')"
          :icon="LeftToRightListTriangleIcon"
          iconColor="blue"
          class="recent-orders-container"
        >
          <RecentOrders :orders="recentOrders" :loading="loading" />
        </ViewContainer>
      </div>

      <!-- Charts Row -->
      <div class="charts-grid">
        <ViewContainer 
          :title="$t('dashboard.salesChart')"
          :subtitle="$t('dashboard.salesChartSubtitle')"
          :icon="ChartUpIcon"
          iconColor="green"
          class="chart-container"
        >
          <SalesChart :data="salesChartData" :loading="loading" />
        </ViewContainer>

        <ViewContainer 
          :title="$t('dashboard.ordersChart')"
          :subtitle="$t('dashboard.ordersChartSubtitle')"
          :icon="Analytics02Icon"
          iconColor="blue"
          class="chart-container"
        >
          <OrdersChart :data="ordersChartData" :loading="loading" />
        </ViewContainer>
      </div>

      <!-- Top Products and Combined Analytics/Alerts -->
      <div class="products-analytics-grid">
        <ViewContainer 
          :title="$t('dashboard.topProducts')"
          :subtitle="$t('dashboard.topProductsSubtitle')"
          :icon="AnalyticsUpIcon"
          iconColor="purple"
          class="top-products-container"
        >
          <TopProducts :products="topProducts" :loading="loading" />
        </ViewContainer>

        <!-- Combined Analytics and Alerts Container -->
        <div class="combined-analytics-alerts">
          <ViewContainer 
            :title="$t('dashboard.storeAnalytics')"
            :subtitle="$t('dashboard.storeAnalyticsSubtitle')"
            :icon="PieChartIcon"
            iconColor="indigo"
            class="analytics-container-small"
          >
            <StoreAnalytics :data="analyticsData" :loading="loading" />
          </ViewContainer>

          <ViewContainer 
            :title="$t('dashboard.alerts')"
            :subtitle="$t('dashboard.alertsSubtitle')"
            :icon="Alert02Icon"
            iconColor="yellow"
            class="alerts-container-small"
          >
            <AlertsPanel :alerts="alerts" :loading="loading" />
          </ViewContainer>
        </div>
      </div>

      <!-- Store Health (with Reviews instead of Categories) -->
      <div class="health-container-full">
        <ViewContainer 
          :title="$t('dashboard.storeHealth')"
          :subtitle="$t('dashboard.storeHealthSubtitle')"
          :icon="HealthIcon"
          iconColor="red"
          class="store-health-container-full"
        >
          <StoreHealth :health="storeHealth" :loading="loading" />
        </ViewContainer>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import MetricCard from '@/components/dashboard/MetricCard.vue'
import SalesChart from '@/components/dashboard/SalesChart.vue'
import OrdersChart from '@/components/dashboard/OrdersChart.vue'
import QuickActions from '@/components/dashboard/QuickActions.vue'
import RecentOrders from '@/components/dashboard/RecentOrders.vue'
import TopProducts from '@/components/dashboard/TopProducts.vue'
import StoreAnalytics from '@/components/dashboard/StoreAnalytics.vue'
import StoreHealth from '@/components/dashboard/StoreHealth.vue'
import AlertsPanel from '@/components/dashboard/AlertsPanel.vue'
import {
  DollarSquareIcon,
  ShoppingCart01Icon,
  StarIcon,
  User03Icon,
  ChartUpIcon,
  Analytics02Icon,
  ZapIcon,
  LeftToRightListTriangleIcon,
  AnalyticsUpIcon,
  PieChartIcon,
  HealthIcon,
  Alert02Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const loading = ref(false)

// Sample data - in real app, this would come from API
const metrics = ref({
  totalSales: 127450.00,
  salesChange: 12.5,
  salesTrend: 'up',
  totalOrders: 1248,
  ordersChange: 8.3,
  ordersTrend: 'up',
  totalProducts: 156,
  productsChange: 3.2,
  productsTrend: 'up',
  totalCustomers: 2456,
  customersChange: 15.7,
  customersTrend: 'up',
  averageRating: 4.5,
  reviewsChange: 0.2,
  reviewsTrend: 'up'
})

const salesChartData = ref([
  { date: '2024-01-01', value: 12500 },
  { date: '2024-01-02', value: 15200 },
  { date: '2024-01-03', value: 18300 },
  { date: '2024-01-04', value: 14700 },
  { date: '2024-01-05', value: 19800 },
  { date: '2024-01-06', value: 22100 },
  { date: '2024-01-07', value: 25400 }
])

const ordersChartData = ref([
  { date: '2024-01-01', value: 45 },
  { date: '2024-01-02', value: 52 },
  { date: '2024-01-03', value: 48 },
  { date: '2024-01-04', value: 61 },
  { date: '2024-01-05', value: 55 },
  { date: '2024-01-06', value: 68 },
  { date: '2024-01-07', value: 72 }
])

const recentOrders = ref([
  { id: '12345', customer: 'João Silva', value: 289.90, status: 'paid', date: '2024-01-07' },
  { id: '12346', customer: 'Maria Santos', value: 156.50, status: 'pending', date: '2024-01-07' },
  { id: '12347', customer: 'Pedro Costa', value: 425.30, status: 'shipped', date: '2024-01-07' },
  { id: '12348', customer: 'Ana Oliveira', value: 89.90, status: 'paid', date: '2024-01-06' },
  { id: '12349', customer: 'Carlos Lima', value: 320.00, status: 'cancelled', date: '2024-01-06' }
])

const topProducts = ref([
  { id: 1, name: 'Camiseta Premium', sales: 145, revenue: 4350.00, image: null },
  { id: 2, name: 'Tênis Sport', sales: 89, revenue: 8900.00, image: null },
  { id: 3, name: 'Jaqueta Jeans', sales: 67, revenue: 6700.00, image: null },
  { id: 4, name: 'Calça Casual', sales: 54, revenue: 3240.00, image: null },
  { id: 5, name: 'Boné Vintage', sales: 43, revenue: 1290.00, image: null }
])

const analyticsData = ref({
  salesByCategory: [
    { name: 'Roupas', value: 45 },
    { name: 'Calçados', value: 30 },
    { name: 'Acessórios', value: 25 }
  ],
  paymentMethods: [
    { name: 'Cartão de Crédito', value: 60 },
    { name: 'PIX', value: 25 },
    { name: 'Boleto', value: 15 }
  ]
})

const storeHealth = ref({
  score: 85,
  indicators: [
    { name: 'Produtos Ativos', value: 95, status: 'good' },
    { name: 'Estoque Baixo', value: 12, status: 'warning' },
    { name: 'Reviews Positivas', value: 92, status: 'good' },
    { name: 'SEO Score', value: 78, status: 'ok' }
  ]
})

const alerts = ref([
  { id: 1, type: 'warning', title: 'Estoque Baixo', message: '5 produtos com estoque abaixo de 10 unidades', date: '2024-01-07' },
  { id: 2, type: 'info', title: 'Nova Avaliação', message: 'Produto "Camiseta Premium" recebeu uma nova avaliação', date: '2024-01-07' },
  { id: 3, type: 'success', title: 'Meta Atingida', message: 'Vendas do mês superaram a meta em 12%', date: '2024-01-06' }
])

const refreshData = async () => {
  loading.value = true
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    // Here you would load real data from your API
  } catch (error) {
    // Handle error
  } finally {
    loading.value = false
  }
}

onMounted(() => {
  // Load initial data
  refreshData()
})
</script>

<style scoped>
.dashboard-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  
  min-height: 100vh;
}



/* Dashboard Content */
.dashboard-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Metrics Grid */
.metrics-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
}

.metric-card {
  min-height: 120px;
}

/* Charts Grid */
.charts-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(500px, 1fr));
  gap: 20px;
}

.chart-container {
  min-height: 400px;
}

/* Actions and Activity Grid */
.actions-activity-grid {
  display: grid;
  grid-template-columns: 1fr 2fr;
  gap: 20px;
}

.quick-actions-container {
  min-height: 300px;
}

.recent-orders-container {
  min-height: 300px;
}

/* Products and Analytics Grid */
.products-analytics-grid {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 20px;
}

.top-products-container {
  min-height: 350px;
}

/* Combined Analytics and Alerts */
.combined-analytics-alerts {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.analytics-container-small,
.alerts-container-small {
  min-height: 160px;
}

/* Health and Alerts Grid */
.health-container-full {
  min-height: 300px;
}

/* Responsive Design */
@media (max-width: 1200px) {
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    min-height: 350px;
  }
}

@media (max-width: 900px) {
  .actions-activity-grid,
  .products-analytics-grid {
    grid-template-columns: 1fr;
  }
  
  .metrics-grid {
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
  }
  
  .combined-analytics-alerts {
    gap: 20px;
  }
}

@media (max-width: 768px) {
  .dashboard-container {
    padding: 16px;
  }
  

  
  .metrics-grid {
    grid-template-columns: 1fr;
  }
  
  .charts-grid {
    grid-template-columns: 1fr;
  }
  
  .chart-container {
    min-height: 300px;
  }
  
  .analytics-container-small,
  .alerts-container-small {
    min-height: 200px;
  }
}

@media (max-width: 480px) {

}
</style>
