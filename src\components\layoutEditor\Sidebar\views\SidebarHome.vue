<template>
  <div class="sidebar-home">
    <!-- Seção: <PERSON><PERSON><PERSON><PERSON> da Página -->
    <div class="menu-section">

      
      <!-- Lista das seções atuais -->
      <div ref="sectionsListRef" class="sections-list">
        <div 
          v-for="(section, index) in pageSections" 
          :key="section.id"
          class="section-item"
          :class="{ 'is-hovered': hoveredSection === section.id }"
          @click="selectSection(section)"
          @mouseenter="highlightSection(section.id)"
          @mouseleave="unhighlightSection"
        >
          <!-- Ícone de posição fixa para componentes protegidos -->
          <div v-if="isProtectedComponent(section)" class="section-fixed-indicator">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <path d="M21 8a2 2 0 0 0-1-1.73l-7-4a2 2 0 0 0-2 0l-7 4A2 2 0 0 0 3 8v8a2 2 0 0 0 1 1.73l7 4a2 2 0 0 0 2 0l7-4A2 2 0 0 0 21 16Z"/>
              <path d="m3.3 7 8.7 5 8.7-5"/>
              <path d="M12 22V12"/>
            </svg>
          </div>
          
          <!-- Ícone de drag apenas para componentes não protegidos -->
          <div v-else class="section-drag-handle">
            <svg width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <circle cx="9" cy="5" r="1"/>
              <circle cx="9" cy="12" r="1"/>
              <circle cx="9" cy="19" r="1"/>
              <circle cx="15" cy="5" r="1"/>
              <circle cx="15" cy="12" r="1"/>
              <circle cx="15" cy="19" r="1"/>
            </svg>
          </div>
          
          <div class="section-info">
            <div class="section-name">{{ section.name }}</div>
            <div class="section-type">{{ section.type }}</div>
          </div>
          
          <div class="section-actions">
            <!-- Botão Editar -->
            <button 
              class="section-action-btn edit-btn"
              @click.stop="editSection(section)"
              :title="$t('layoutEditor.edit')"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M9 18L15 12L9 6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
            
            <!-- Botão Deletar (não aparece para header/footer) -->
            <button 
              v-if="!isProtectedComponent(section)"
              class="section-action-btn delete-btn"
              @click.stop="deleteSection(section)"
              :title="$t('layoutEditor.delete')"
            >
              <svg width="14" height="14" viewBox="0 0 24 24" fill="none" stroke="currentColor">
                <path d="M3 6h18M19 6v14a2 2 0 01-2 2H7a2 2 0 01-2-2V6M8 6V4a2 2 0 012-2h4a2 2 0 012 2v2M10 11v6M14 11v6" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/>
              </svg>
            </button>
          </div>
        </div>
        
        <!-- Estado vazio -->
        <div v-if="pageSections.length === 0" class="empty-sections">
          <div class="empty-icon">
            <svg width="32" height="32" viewBox="0 0 24 24" fill="none" stroke="currentColor">
              <rect x="3" y="3" width="18" height="18" rx="2" ry="2"/>
              <line x1="9" y1="9" x2="15" y2="15"/>
              <line x1="15" y1="9" x2="9" y2="15"/>
            </svg>
          </div>
          <p>Nenhuma seção encontrada</p>
        </div>
      </div>
      
      <!-- Botão Adicionar Seção -->
      <button 
        class="add-section-btn"
        @click="showAddComponentView"
      >
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <line x1="12" y1="5" x2="12" y2="19" stroke-width="2"/>
          <line x1="5" y1="12" x2="19" y2="12" stroke-width="2"/>
        </svg>
        Adicionar seção
      </button>
    </div>
    
    <!-- Modal de Confirmação -->
    <IluriaConfirmationModal
      :isVisible="showDeleteModal"
      :title="'Confirmar Exclusão'"
      :message="sectionToDelete ? `Tem certeza que deseja excluir '${sectionToDelete.name}'? Esta ação não pode ser desfeita.` : ''"
      :confirmText="'Sim, excluir'"
      :cancelText="'Cancelar'"
      :isDestructive="true"
      type="error"
      @confirm="confirmDelete"
      @cancel="cancelDelete"
    />
  </div>
</template>

<script setup>
import { ref, onMounted, watch, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue'
import { setupSectionDragAndDrop } from '@/utils/dragDropUtils'

const { t } = useI18n()
const emit = defineEmits(['navigate-to', 'component-select', 'section-select', 'section-highlight', 'section-deleted', 'section-will-delete'])

// Estado do modal de confirmação
const showDeleteModal = ref(false)
const sectionToDelete = ref(null)

// Referências para drag and drop
const sectionsListRef = ref(null)
let dragDropCleanup = null

// Props para receber o iframe document
const props = defineProps({
  iframeDocument: {
    type: Object,
    default: null
  }
})

// Estado interno
const pageSections = ref([])
const hoveredSection = ref(null)

// Mock de seções (será substituído pela detecção real)
const mockSections = [
  { id: 'comunicado', name: 'Comunicado (Cópia)', type: 'Comunicado' },
  { id: 'cabecalho', name: 'Cabeçalho', type: 'Header' },
  { id: 'capa1', name: 'Capa', type: 'Hero Section' },
  { id: 'capa2', name: 'Capa', type: 'Content Section' },
  { id: 'nova-colecao', name: 'Nova coleção', type: 'Product Section' },
  { id: 'envio-pagamento1', name: 'Envio e pagamento', type: 'Benefits' },
  { id: 'avaliacoes', name: 'Avaliações de clientes', type: 'Reviews' },
  { id: 'info-empresa1', name: 'Informações da empresa', type: 'Company Info' },
  { id: 'colecao-produto', name: 'Coleção do produto', type: 'Product Collection' },
  { id: 'envio-pagamento2', name: 'Envio e pagamento', type: 'Benefits' },
  { id: 'dadada', name: 'Dadada', type: 'Custom Section' },
  { id: 'info-cliente', name: 'Informações do cliente', type: 'Customer Info' },
  { id: 'info-empresa2', name: 'Informações da empresa', type: 'Company Info' },
  { id: 'capa3', name: 'Capa 1', type: 'Banner' },
  { id: 'localizacoes', name: 'Localizações', type: 'Location' },
  { id: 'video', name: 'Vídeo', type: 'Media' }
]

// Detectar seções da página do iframe REAL
const detectPageSections = () => {
  if (!props.iframeDocument) {
    pageSections.value = []
    return
  }

  const body = props.iframeDocument.body
  
  if (!body) {
    pageSections.value = []
    return
  }

  // Array para armazenar todas as seções (permitindo múltiplos do mesmo tipo)
  const sectionsArray = []
  const processedElements = new Set()

  // Detectar componentes específicos (ordem de prioridade)
  const componentSelectors = [
    { selector: '[data-component="header"]', type: 'header', priority: 1 },
    { selector: '[data-component="carousel"]', type: 'carousel', priority: 2 },
    { selector: '[data-component="video"]', type: 'video', priority: 3 },
    { selector: '[data-component="company-information"]', type: 'company-information', priority: 4 },
    { selector: '[data-component="location"]', type: 'location', priority: 5 },
    { selector: '[data-component="statement"]', type: 'statement', priority: 6 },
    { selector: '[data-component="payment-benefits"]', type: 'payment-benefits', priority: 7 },
    { selector: '[data-component="customer-review"]', type: 'customer-review', priority: 8 },
    { selector: '[data-component="special-offers"]', type: 'special-offers', priority: 9 },
    { selector: '[data-component="product-selection"]', type: 'product-selection', priority: 10 },
    { selector: '[data-component="product-grid"]', type: 'product-grid', priority: 11 },
    { selector: '[data-component="product-showcase"]', type: 'product-showcase', priority: 11 },
    { selector: '[data-component="dynamic-grid-produtos"]', type: 'dynamic-grid-produtos', priority: 11 },
    { selector: '[data-component="grid-produtos"]', type: 'grid-produtos', priority: 11 },
    { selector: '[data-product-grid="true"]', type: 'product-grid-alt', priority: 11 },
    { selector: '[data-component="product-checkout"]', type: 'product-checkout', priority: 12 },
    { selector: '[data-component="footer"]', type: 'footer', priority: 100 }
  ]

  let sectionIndex = 0
  
  // Processar TODOS os componentes encontrados (não apenas o primeiro)
  componentSelectors.forEach(({ selector, type, priority }) => {
    try {
      const elements = body.querySelectorAll(selector)
      
      // Debug: Log elementos encontrados
      if (elements.length > 0) {
        elements.forEach((el, idx) => {
        })
      }
      
      // Processar TODOS os elementos encontrados
      elements.forEach((element, elementIndex) => {
        if (!processedElements.has(element)) {
          const sectionInfo = getSectionInfo(element, sectionIndex++, elementIndex + 1, elements.length)
          if (sectionInfo) {
            sectionInfo.priority = priority
            sectionsArray.push(sectionInfo)
            processedElements.add(element)
          }
        }
      })
    } catch (error) {
      console.error(`❌ [SidebarHome] Erro ao processar seletor ${selector}:`, error)
    }
  })
  
  // Ordenar: Header primeiro, Footer último, outros no meio
  sectionsArray.sort((a, b) => {
    // Header sempre primeiro
    if (a.priority <= 1) return -1
    if (b.priority <= 1) return 1
    
    // Footer sempre último
    if (a.priority >= 100) return 1
    if (b.priority >= 100) return -1
    
    // Outros elementos ordenados por posição na página
    try {
      const aRect = a.element.getBoundingClientRect()
      const bRect = b.element.getBoundingClientRect()
      return aRect.top - bRect.top
    } catch (error) {
      return a.priority - b.priority
    }
  })

  pageSections.value = sectionsArray
  
  // Configurar drag and drop após detectar seções
  nextTick(() => {
    setupSectionDragDrop()
  })
}

// Obter informações de uma seção real (apenas componentes com data-component)
const getSectionInfo = (element, index, instanceNumber = 1, totalInstances = 1) => {
  const dataComponent = element.getAttribute('data-component')
  
  // Só processar elementos com data-component
  if (!dataComponent) {
    return null
  }
  
  // Componente específico detectado
  const componentNames = {
    'header': 'Cabeçalho',
    'footer': 'Rodapé',
    'carousel': 'Carrossel',
    'video': 'Vídeo',
    'company-information': 'Informações da empresa',
    'location': 'Localizações',
    'statement': 'Comunicado',
    'payment-benefits': 'Envio e pagamento',
    'customer-review': 'Avaliações de clientes',
    'special-offers': 'Ofertas especiais',
    'product-selection': 'Seleção de produtos',
    'product-grid': 'Grid de produtos',
    'product-showcase': 'Produtos em destaque',
    'dynamic-grid-produtos': 'Coleção do produto',
    'grid-produtos': 'Grid de produtos',
    'product-grid-alt': 'Grid de produtos',
    'product-checkout': 'Checkout de Produto'
  }
  
  let baseName = componentNames[dataComponent] || dataComponent.replace(/-/g, ' ').replace(/\b\w/g, l => l.toUpperCase())
  
  // Adicionar numeração se há múltiplas instâncias
  let name = baseName
  if (totalInstances > 1) {
    // Para componentes únicos, não adicionar número
    if (!['header', 'footer'].includes(dataComponent)) {
      name = `${baseName} ${instanceNumber}`
    }
  }
  
  // Mapear para tipo de componente
  const componentTypes = {
    'header': 'Header',
    'footer': 'Footer',
    'carousel': 'Hero Section',
    'video': 'Media',
    'company-information': 'Company Info',
    'location': 'Location',
    'statement': 'Comunicado',
    'payment-benefits': 'Benefits',
    'customer-review': 'Reviews',
    'special-offers': 'Marketing',
    'product-selection': 'Product Section',
    'product-grid': 'Product Collection',
    'product-showcase': 'Product Collection',
    'dynamic-grid-produtos': 'Product Collection',
    'grid-produtos': 'Product Collection',
    'product-grid-alt': 'Product Collection',
    'product-checkout': 'Checkout'
  }
  
  const type = componentTypes[dataComponent] || 'Component'

  return {
    id: `${dataComponent}-${index}`,
    name,
    type,
    element,
    dataComponent,
    instanceNumber,
    totalInstances
  }
}

// Selecionar seção
const selectSection = (section) => {
  // Destacar elemento no iframe como selecionado
  if (section.element) {
    highlightElementInIframe(section.element, false) // false = selected, não hover
  }
  
  // Emitir evento para o parent (vai conectar com sistema de seleção existente)
  emit('section-select', section)
  
  // Navegar para o editor específico baseado no tipo
  const editorTypeMap = {
    'Header': 'header-config',
    'Footer': 'footer-config',
    'Hero Section': 'carousel-config',
    'Media': 'video-config',
    'Company Info': 'company-information-config',
    'Location': 'location-config',
    'Benefits': 'payment-benefits-config',
    'Reviews': 'customer-review-config',
    'Comunicado': 'statement-config',
    'Marketing': 'special-offers-config',
    'Product Section': 'product-selection-config',
    'Product Collection': 'product-selection-config',
    'Checkout': 'product-checkout-config',
    'Component': 'spacing',
    'Element': 'spacing',
    'HTML Element': 'spacing'
  }
  
  // Usar dataComponent se disponível, senão usar tipo
  let editorType = 'spacing'
  
  if (section.dataComponent) {
    editorType = `${section.dataComponent}-config`
  } else {
    editorType = editorTypeMap[section.type] || 'spacing'
  }
  
  emit('navigate-to', 'editor', { 
    editorType,
    section,
    element: section.element
  })
}

// Editar seção (mesmo que selecionar por enquanto)
const editSection = (section) => {
  selectSection(section)
}

// Destacar seção no hover
const highlightSection = (sectionId) => {
  hoveredSection.value = sectionId
  
  const section = pageSections.value.find(s => s.id === sectionId)
  if (section && section.element) {
    highlightElementInIframe(section.element, true)
    emit('section-highlight', { section, action: 'highlight' })
  }
}

// Remover destaque
const unhighlightSection = () => {
  hoveredSection.value = null
  
  // Remover destaque de todos os elementos
  if (props.iframeDocument) {
    const highlighted = props.iframeDocument.querySelectorAll('.iluria-sidebar-highlight')
    highlighted.forEach(el => el.classList.remove('iluria-sidebar-highlight'))
  }
  
  emit('section-highlight', { action: 'unhighlight' })
}

// Destacar elemento no iframe
const highlightElementInIframe = (element, isHover = false) => {
  if (!element || !props.iframeDocument) return
  
  // Remover destaques anteriores
  const previousHighlighted = props.iframeDocument.querySelectorAll(
    '.iluria-sidebar-highlight, .iluria-sidebar-selected'
  )
  previousHighlighted.forEach(el => {
    el.classList.remove('iluria-sidebar-highlight', 'iluria-sidebar-selected')
  })
  
  // Adicionar novo destaque
  const className = isHover ? 'iluria-sidebar-highlight' : 'iluria-sidebar-selected'
  element.classList.add(className)
  
  // Garantir que os estilos estejam no iframe
  addHighlightStylesToIframe()
}

// Adicionar estilos de destaque ao iframe (sem seleção verde)
const addHighlightStylesToIframe = () => {
  if (!props.iframeDocument) return
  
  const existingStyle = props.iframeDocument.querySelector('#iluria-sidebar-highlight-styles')
  if (existingStyle) return
  
  const style = props.iframeDocument.createElement('style')
  style.id = 'iluria-sidebar-highlight-styles'
  style.textContent = `
    .iluria-sidebar-highlight {
      outline: 2px solid #3b82f6 !important;
      outline-offset: 2px !important;
      background: rgba(59, 130, 246, 0.1) !important;
      transition: all 0.2s ease !important;
      position: relative !important;
      z-index: 1000 !important;
    }
    
    .iluria-sidebar-selected {
      /* Sem destaque visual na seleção */
    }
    
    .iluria-sidebar-highlight:hover {
      outline-color: #1d4ed8 !important;
      background: rgba(59, 130, 246, 0.2) !important;
    }
  `
  
  if (props.iframeDocument.head) {
    props.iframeDocument.head.appendChild(style)
  }
}

// Verificar se o componente é protegido (header/footer)
const isProtectedComponent = (section) => {
  return section.dataComponent === 'header' || 
         section.dataComponent === 'footer' || 
         section.type === 'Header' || 
         section.type === 'Footer'
}

// Deletar seção
const deleteSection = (section) => {
  if (isProtectedComponent(section)) {
    return // Não deve chegar aqui, mas por segurança
  }
  
  // Configurar e abrir modal de confirmação
  sectionToDelete.value = section
  showDeleteModal.value = true
}

// Confirmar exclusão
const confirmDelete = () => {
  try {
    const section = sectionToDelete.value
    
    // 🔄 CRÍTICO: Emitir evento ANTES da exclusão para salvar estado do undo/redo
    emit('section-will-delete', section)
    
    // Pequena pausa para garantir que o estado seja salvo
    setTimeout(() => {
      try {
        // Remover o elemento do DOM
        if (section.element && section.element.parentNode) {
          section.element.parentNode.removeChild(section.element)
        }
        
        // Redetectar seções
        detectPageSections()
        
        // Emitir evento para notificar que houve mudança
        emit('section-deleted', section)
        
      } catch (error) {
        console.error('Erro ao deletar seção:', error)
        alert('Erro ao deletar seção. Tente novamente.')
      } finally {
        // Fechar modal
        cancelDelete()
      }
    }, 50) // 50ms de delay para garantir que o estado seja salvo
    
  } catch (error) {
    console.error('Erro ao deletar seção:', error)
    alert('Erro ao deletar seção. Tente novamente.')
    cancelDelete()
  }
}

// Cancelar exclusão
const cancelDelete = () => {
  showDeleteModal.value = false
  sectionToDelete.value = null
}

// Mostrar view de adicionar componente
const showAddComponentView = () => {
  emit('navigate-to', 'add-component')
}

// Watchers
watch(() => props.iframeDocument, (newDoc) => {
  if (newDoc) {
    setTimeout(() => {
      detectPageSections()
    }, 100)
  }
}, { immediate: true })

// Função para refresh manual das seções
const refreshSections = () => {
  detectPageSections()
}

// Configurar drag and drop das seções
const setupSectionDragDrop = async () => {
  // Aguardar o próximo tick para garantir que o DOM está atualizado
  await nextTick()
  
  // Limpar configuração anterior se existir
  if (dragDropCleanup) {
    dragDropCleanup()
    dragDropCleanup = null
  }
  
  // Configurar drag and drop se temos seções e o container
  if (sectionsListRef.value && pageSections.value.length > 0 && props.iframeDocument) {
    dragDropCleanup = setupSectionDragAndDrop(
      sectionsListRef.value,
      pageSections.value,
      props.iframeDocument,
      handleSectionReorder
    )
  }
}

// Callback chamado após reordenação de seções
const handleSectionReorder = async () => {
  // Aguardar um pouco antes de re-detectar para dar tempo do DOM se estabilizar
  setTimeout(() => {
    detectPageSections()
  }, 100)
}

// Lifecycle
onMounted(() => {
  detectPageSections()
})

// Watcher para configurar drag and drop após mudanças nas seções
watch([pageSections, () => props.iframeDocument], () => {
  setupSectionDragDrop()
}, { deep: true })

// Cleanup no unmount
watch(() => props.iframeDocument, (newDoc) => {
  if (!newDoc && dragDropCleanup) {
    dragDropCleanup()
    dragDropCleanup = null
  }
})

// Expor função para controle externo
defineExpose({
  refreshSections
})
</script>

<style scoped>
.sidebar-home {
  height: 100%;
  overflow-y: auto;
  padding: 0;
  background: var(--iluria-color-surface);
}

.sidebar-home::-webkit-scrollbar {
  width: 6px;
}

.sidebar-home::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
}

.sidebar-home::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

/* Seções do menu */
.menu-section {
  border-bottom: 1px solid var(--iluria-color-border);
}

.section-header {
  padding: 1rem 1rem 0.75rem 1rem;
  background: var(--iluria-color-container-bg);
}

.section-title {
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.section-description {
  margin: 0;
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  line-height: 1.4;
}

/* Lista de seções */
.sections-list {
  background: var(--iluria-color-background);
}

.section-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
  cursor: pointer;
  transition: all 0.2s ease;
  position: relative;
}

.section-item:hover {
  background: var(--iluria-color-hover);
}

.section-item.is-hovered {
  background: var(--iluria-color-hover);
  border-left: 3px solid var(--iluria-color-primary);
}

.section-item:last-child {
  border-bottom: none;
}

.section-drag-handle {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: var(--iluria-color-text-secondary);
  opacity: 0.5;
  cursor: grab;
  flex-shrink: 0;
}

.section-drag-handle:hover {
  opacity: 0.8;
}

.section-fixed-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  color: var(--iluria-color-text-secondary);
  opacity: 0.5;
  flex-shrink: 0;
}

.section-info {
  flex: 1;
  min-width: 0;
}

.section-name {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  line-height: 1.3;
  margin-bottom: 0.125rem;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
}

.section-type {
  font-size: 0.75rem;
  color: var(--iluria-color-text-secondary);
  opacity: 0.7;
}

.section-actions {
  display: flex;
  gap: 0.25rem;
  opacity: 1;
  transition: opacity 0.2s ease;
  flex-shrink: 0;
}

.section-action-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 28px;
  height: 28px;
  background: transparent;
  border: 1px solid var(--iluria-color-border);
  border-radius: 4px;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
}

.section-action-btn:hover {
  background: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
  border-color: var(--iluria-color-primary-border);
}

.section-action-btn.delete-btn {
  color: #dc2626;
  border-color: #dc2626;
}

.section-action-btn.delete-btn:hover {
  background: #dc2626;
  color: white;
  border-color: #dc2626;
}

/* Estado vazio */
.empty-sections {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

.empty-icon {
  width: 32px;
  height: 32px;
  margin-bottom: 0.75rem;
  opacity: 0.5;
}

.empty-sections p {
  margin: 0;
  font-size: 0.875rem;
}

/* Botão adicionar seção */
.add-section-btn {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 0.5rem;
  width: 100%;
  padding: 0.875rem 1rem;
  background: transparent;
  border: 1px dashed var(--iluria-color-border);
  color: var(--iluria-color-text-secondary);
  font-size: 0.875rem;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  margin: 0.5rem 0;
}

.add-section-btn:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary-border);
  color: var(--iluria-color-primary);
}

/* Estilos para drag and drop */
.section-item.dragging {
  opacity: 0.5;
  transform: scale(0.95);
  z-index: 1000;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.section-item.drop-target {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary-border);
}

.section-item.drop-above {
  border-top: 2px solid var(--iluria-color-primary);
  margin-top: -1px;
}

.section-item.drop-below {
  border-bottom: 2px solid var(--iluria-color-primary);
  margin-bottom: -1px;
}

.section-item[draggable="true"] .section-drag-handle {
  cursor: grab;
}

.section-item[draggable="true"] .section-drag-handle:active {
  cursor: grabbing;
}

.section-item[draggable="false"] .section-drag-handle {
  cursor: not-allowed;
  opacity: 0.3;
}

/* Responsividade */
@media (max-width: 768px) {
  .section-header {
    padding: 0.75rem;
  }
  
  .section-title {
    font-size: 0.8rem;
  }
  
  .section-description {
    font-size: 0.7rem;
  }
  
  .section-item {
    padding: 0.75rem;
  }
  
  .section-name {
    font-size: 0.8rem;
  }
  
  .section-type {
    font-size: 0.7rem;
  }
  
  .section-drag-handle {
    width: 18px;
    height: 18px;
  }
  
  .section-action-btn {
    width: 24px;
    height: 24px;
  }
  
  .add-section-btn {
    padding: 0.75rem;
    font-size: 0.8rem;
  }
  
  .empty-sections {
    padding: 1.5rem 1rem;
  }
}
</style> 
