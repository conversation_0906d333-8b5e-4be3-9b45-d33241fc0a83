<template>
  <div :class="wrapperClass">
    <div class="checkbox-container">
      <label :for="checkboxId" class="checkbox-label">
      <!-- Checkbox nativo HTML com estilo personalizado -->
      <div class="checkbox-wrapper">
        <input 
          type="checkbox" 
          :id="checkboxId" 
          :name="name || checkboxId" 
          :checked="modelValue"
          @change="handleChange"
          class="hidden-checkbox" 
        />
        <div class="custom-checkbox" :class="{ 'checked': modelValue }">
          <svg v-if="modelValue" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" class="check-icon">
            <path d="M9 16.17L4.83 12l-1.42 1.41L9 19 21 7l-1.41-1.41L9 16.17z" />
          </svg>
        </div>
      </div>
      <!-- Texto do label -->
        <IluriaText v-if="label" class="checkbox-text">{{ label }}</IluriaText>
      </label>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import IluriaText from '@/components/iluria/IluriaText.vue';

const props = defineProps({
  id: {
    type: String,
    default: null
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  wrapperClass: {
    type: String,
    default: ''
  },
  labelPosition: {
    type: String,
    default: 'vertical'
  },
  modelValue: {
    type: Boolean,
    default: false
  }
});

const emit = defineEmits(['update:modelValue']);

// Gera um ID único se não for fornecido um ID explicitamente
const uniqueId = `checkbox-${Math.random().toString(36).substring(2, 11)}`;
const checkboxId = computed(() => props.id || uniqueId);

// Handler para mudanças do checkbox
const handleChange = (event) => {
  emit('update:modelValue', event.target.checked);
}
</script>

<style scoped>
.checkbox-container {
  display: flex;
  align-items: center;
  gap: 8px;
}

.checkbox-label {
  display: flex;
  align-items: center;
  gap: 8px;
  cursor: pointer;
  min-height: 20px;
}

.checkbox-label:hover .custom-checkbox {
  border-color: var(--iluria-color-checkbox-border-active);
}

.checkbox-wrapper {
  position: relative;
  display: flex;
  align-items: center;
  justify-content: center;
  width: 20px;
  height: 20px;
  flex-shrink: 0;
}

.hidden-checkbox {
  position: absolute;
  opacity: 0;
  cursor: pointer;
  height: 0;
  width: 0;
}

.custom-checkbox {
  position: relative;
  height: 18px;
  width: 18px;
  background-color: var(--iluria-color-checkbox-bg);
  border: 2px solid var(--iluria-color-checkbox-border);
  border-radius: 4px;
  transition: border-color 0.2s ease, background-color 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
  box-sizing: border-box;
  overflow: hidden;
}

.custom-checkbox::before {
  content: '';
  position: absolute;
  inset: 0;
  background: var(--iluria-color-checkbox-bg-active);
  transform: scale(0);
  transition: opacity 0.15s ease;
  border-radius: 2px;
  opacity: 0;
}

.custom-checkbox.checked {
  border-color: var(--iluria-color-checkbox-border-active);
  background-color: var(--iluria-color-checkbox-bg-active);
}

.custom-checkbox.checked::before {
  transform: scale(1);
  opacity: 1;
}

/* Garantir que o estado não-checked seja limpo */
.custom-checkbox:not(.checked) {
  background-color: var(--iluria-color-checkbox-bg);
  border-color: var(--iluria-color-checkbox-border);
}

.custom-checkbox:not(.checked)::before {
  transform: scale(0);
  opacity: 0;
}

.check-icon {
  fill: var(--iluria-color-checkbox-check);
  width: 12px;
  height: 12px;
  flex-shrink: 0;
  position: relative;
  z-index: 1;
  opacity: 0;
  transition: opacity 0.15s ease;
}

.custom-checkbox.checked .check-icon {
  opacity: 1;
}

/* Garantir que o ícone seja escondido quando não checked */
.custom-checkbox:not(.checked) .check-icon {
  opacity: 0;
}

.checkbox-text {
  margin-left: 1px;
  font-size: 0.875rem;
  color: var(--iluria-color-text-muted);
  line-height: 1.25rem;
  display: flex;
  align-items: center;
  transition: color 0.2s ease;
}

.checkbox-label:hover .checkbox-text {
  color: var(--iluria-color-text);
}

/* Animações removidas para comportamento limpo */

/* Estados de focus para acessibilidade - removido outline visível */
.hidden-checkbox:focus + .custom-checkbox {
  /* Mantém foco para screen readers mas remove outline visual */
  outline: none;
}

/* Efeito ripple removido - mantendo apenas o foco para acessibilidade */
</style>
