<template>
  <IluriaModal 
    v-model:visible="isVisible" 
    :title="displayTitle"
    dismissableMask
    style="width: 450px"
  >
    <div class="mfa-verification">
      <!-- Description -->
      <div class="text-center mb-6">
        <p class="mfa-description text-sm">{{ displayDescription }}</p>
      </div>

      <!-- EMAIL_VERIFICATION specific content -->
      <div v-if="mfaType === 'EMAIL_VERIFICATION'" class="email-verification mb-6">
        <div class="text-center">
          <p class="text-sm mfa-email-info mb-4">
            {{ $t('userSettings.mfa.emailSent') }} <strong>{{ userEmail }}</strong>
          </p>
          <p class="text-xs mfa-email-instruction">
            {{ $t('userSettings.mfa.checkInbox') }}
          </p>
        </div>
      </div>

      <!-- TOTP specific content -->
      <div v-else-if="mfaType === 'TOTP'" class="totp-verification mb-6">
        <div class="text-center">
          <p class="text-sm mfa-totp-instruction">
            {{ $t('userSettings.mfa.totpInstruction') }}
          </p>
        </div>
      </div>

      <!-- Verification Code Input -->
      <div class="verification-section">
        <IluriaLabel class="text-center block mb-3">
          {{ mfaType === 'TOTP' ? $t('userSettings.mfa.totpCode') : $t('userSettings.mfa.emailCode') }}
        </IluriaLabel>
        <div class="mfa-code-input flex justify-center space-x-2">
          <input
            type="text"
            v-for="(digit, index) in 6"
            :key="index"
            v-model="verificationDigits[index]"
            maxlength="1"
            @input="focusNext(index)"
            @keydown.delete="focusPrevious(index)"
            @paste="handlePaste"
            class="mfa-digit w-12 h-12 text-center text-lg border border-gray-300 rounded-md focus:border-blue-500 focus:outline-none"
          />
        </div>
      </div>

    </div>

    <template #footer>
      <div class="flex justify-between items-center gap-4">
        <!-- Resend button for EMAIL_VERIFICATION - agora no footer -->
        <IluriaButton
          v-if="mfaType === 'EMAIL_VERIFICATION'"
          variant="outline"
          @click="handleResend"
          :disabled="resendDisabled"
          :class="{ 'opacity-50 cursor-not-allowed': resendDisabled }"
        >
          {{ resendButtonText }}
        </IluriaButton>
        
        <!-- Spacer quando não tem botão resend -->
        <div v-else class="flex-1"></div>
        
        <!-- Botão Verificar sempre à direita -->
        <IluriaButton
          color="primary"
          @click="handleVerify"
          :loading="loading"
          :disabled="!verificationCode || verificationCode.length !== 6"
        >
          {{ loading ? $t('userSettings.mfa.verifying') : $t('userSettings.mfa.verify') }}
        </IluriaButton>
      </div>
    </template>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'

const { t } = useI18n()

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  mfaType: {
    type: String,
    required: true,
    validator: (value) => ['EMAIL_VERIFICATION', 'TOTP'].includes(value)
  },
  userEmail: {
    type: String,
    default: ''
  },
  title: {
    type: String,
    default: ''
  },
  description: {
    type: String,
    default: ''
  },
  operation: {
    type: String,
    default: 'verification'
  },
  loading: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['verified', 'resend-email', 'update:visible'])

// State
const verificationDigits = ref(Array(6).fill(''))
const verificationCode = ref('')
const resendDisabled = ref(false)
const resendTimeout = ref(30)

// Computed
const isVisible = computed({
  get: () => props.visible,
  set: (value) => emit('update:visible', value)
})

const displayTitle = computed(() => {
  return props.title || t('userSettings.mfa.title')
})

const displayDescription = computed(() => {
  return props.description || t('userSettings.mfa.description')
})

const resendButtonText = computed(() => {
  return resendDisabled.value 
    ? t('userSettings.mfa.resendIn', { seconds: resendTimeout.value }) 
    : t('userSettings.mfa.resend')
})

// Methods
const focusNext = (index) => {
  if (index < 5 && verificationDigits.value[index]) {
    document.querySelectorAll('.mfa-digit')[index + 1].focus()
  }
  updateVerificationCode()
}

const focusPrevious = (index) => {
  if (index > 0 && !verificationDigits.value[index]) {
    document.querySelectorAll('.mfa-digit')[index - 1].focus()
  }
  updateVerificationCode()
}

const handlePaste = (event) => {
  event.preventDefault()

  const pasteData = event.clipboardData.getData('text').trim()
  if (pasteData.length === 6 && /^[0-9]+$/.test(pasteData)) {
    verificationDigits.value = pasteData.split('')
    updateVerificationCode()

    setTimeout(() => {
      document.querySelectorAll('.mfa-digit')[5].focus()
    }, 10)
  }
}

const updateVerificationCode = () => {
  verificationCode.value = verificationDigits.value.join('')
}

const handleVerify = () => {
  if (verificationCode.value && verificationCode.value.length === 6) {
    emit('verified', {
      code: verificationCode.value,
      operation: props.operation
    })
  }
}


const handleResend = async () => {
  if (props.mfaType === 'EMAIL_VERIFICATION' && !resendDisabled.value) {
    resendDisabled.value = true
    resendTimeout.value = 30
    
    const interval = setInterval(() => {
      resendTimeout.value -= 1
      if (resendTimeout.value <= 0) {
        clearInterval(interval)
        resendDisabled.value = false
      }
    }, 1000)

    emit('resend-email', {
      email: props.userEmail,
      operation: props.operation
    })
  }
}

const resetForm = () => {
  verificationDigits.value = Array(6).fill('')
  verificationCode.value = ''
}

// Watch for modal visibility changes
watch(() => props.visible, (newValue) => {
  if (newValue) {
    resetForm()
    // Auto focus first input when modal opens
    setTimeout(() => {
      const firstInput = document.querySelector('.mfa-digit')
      if (firstInput) {
        firstInput.focus()
      }
    }, 100)
  }
})
</script>

<style scoped>
.mfa-verification {
  max-width: 100%;
}

.mfa-description {
  color: var(--iluria-color-text-primary);
  line-height: 1.5;
}

.mfa-email-info,
.mfa-email-instruction,
.mfa-totp-instruction {
  color: var(--iluria-color-text-secondary);
}

.verification-section {
  max-width: 280px;
  margin: 0 auto;
}

.mfa-code-input {
  gap: 8px;
}

.mfa-digit {
  width: 40px;
  height: 40px;
  font-size: 1.2rem;
  text-align: center;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  transition: all 0.2s ease;
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-bg);
}

.mfa-digit:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
  box-shadow: 0 0 0 2px var(--iluria-color-primary-alpha);
}

.mfa-digit:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.resend-section {
  margin-top: 1rem;
}

/* Footer específico do modal MFA */
.flex.justify-between.items-center {
  min-height: 48px;
  gap: 1rem; /* Garantir espaçamento mínimo entre botões */
}

/* Espaçamento do botão resend quando presente */
.flex.justify-between.items-center > *:first-child {
  margin-right: auto;
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .verification-section {
    max-width: 240px;
  }
  
  .mfa-digit {
    width: 36px;
    height: 36px;
    font-size: 1.1rem;
  }
  
  .mfa-code-input {
    gap: 6px;
  }
  
  /* Footer responsivo em mobile */
  .flex.justify-between.items-center {
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .flex.justify-between.items-center > div:empty {
    display: none;
  }
}
</style>