<template>
  <div class="security-content">
    <!-- Two-Factor Authentication -->
    <ViewContainer 
      title="Autenticação de Dois Fatores"
      :icon="ShieldKeyIcon"
      iconColor="green"
    >
      <div class="security-item">
        <div class="security-info">
          <h4 class="security-title">Aplicativo Autenticador</h4>
          <p class="security-description">Use um aplicativo autenticador para adicionar uma camada extra de segurança</p>
          <div class="security-status">
            <span class="status-badge" :class="twoFactorEnabled ? 'status-enabled' : 'status-disabled'">
              <HugeiconsIcon 
                :icon="twoFactorEnabled ? CheckmarkCircle02Icon : Alert02Icon" 
                size="14" 
                :strokeWidth="1.5" 
              />
              {{ twoFactorEnabled ? 'Ativado' : 'Desativado' }}
            </span>
          </div>
        </div>
        <div class="security-actions">
          <IluriaButton
            @click="toggleTwoFactor"
            :variant="twoFactorEnabled ? 'outline' : 'solid'"
            :color="twoFactorEnabled ? 'primary' : 'primary'"
            :disabled="isTogglingTwoFactor"
            :loading="isTogglingTwoFactor"
            :hugeIcon="twoFactorEnabled ? Settings02Icon : SecurityCheckIcon"
          >
            {{ twoFactorEnabled ? 'Configurar' : 'Ativar' }}
          </IluriaButton>
        </div>
      </div>
    </ViewContainer>

    <!-- Active Sessions -->
    <ViewContainer 
      title="Sessões Ativas"
      subtitle="Gerencie os dispositivos conectados à sua conta"
      :icon="ComputerIcon"
      iconColor="blue"
    >
      <div class="sessions-header">
        <IluriaButton
          @click="refreshSessions"
          variant="outline"
          color="primary"
          :disabled="isLoadingSessions"
          :loading="isLoadingSessions"
          :hugeIcon="ReloadIcon"
        >
          Atualizar
        </IluriaButton>
      </div>
      
      <div class="sessions-list">
        <div 
          v-for="session in sessions" 
          :key="session.sessionId" 
          class="session-item"
          :class="{ 'session-current': session.isCurrent }"
        >
          <div class="session-icon">
            <HugeiconsIcon 
              :icon="getDeviceIcon(session.device)" 
              size="20" 
              :strokeWidth="1.5" 
            />
          </div>
          <div class="session-info">
            <div class="session-main">
              <h4 class="session-title">{{ session.device }} • {{ session.browser }}</h4>
              <span v-if="session.isCurrent" class="session-current-badge">
                Atual
              </span>
            </div>
            <div class="session-details">
              <span class="session-location">{{ session.location }}</span>
              <span class="session-date">{{ formatDate(session.lastActivity) }}</span>
            </div>
          </div>
          <div class="session-actions">
            <IluriaButton
              v-if="!session.isCurrent"
              @click="terminateSession(session.sessionId)"
              variant="outline"
              color="danger"
              size="small"
              :disabled="isTerminating === session.sessionId"
              :loading="isTerminating === session.sessionId"
              :hugeIcon="LogoutIcon"
            >
              Encerrar
            </IluriaButton>
          </div>
        </div>
      </div>
      
      <div class="sessions-footer">
        <IluriaButton
          @click="terminateAllOtherSessions"
          color="danger"
          :disabled="isTerminatingAll"
          :loading="isTerminatingAll"
          :hugeIcon="LogoutIcon"
        >
          Encerrar Todas as Outras Sessões
        </IluriaButton>
      </div>
    </ViewContainer>

    <!-- Security Activity -->
    <ViewContainer 
      title="Atividade de Segurança"
      :icon="ActivityIcon"
      iconColor="orange"
    >
      <div class="activity-list">
        <div v-for="activity in securityActivity" :key="activity.id" class="activity-item">
          <div class="activity-icon">
            <HugeiconsIcon 
              :icon="getActivityIcon(activity.type)" 
              size="16" 
              :strokeWidth="1.5" 
              :class="getActivityIconClass(activity.type)"
            />
          </div>
          <div class="activity-info">
            <h4 class="activity-title">{{ activity.title }}</h4>
            <p class="activity-description">{{ activity.description }}</p>
            <span class="activity-date">{{ formatDate(activity.timestamp) }}</span>
          </div>
        </div>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from '@/services/toast.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  ShieldKeyIcon,
  SecurityCheckIcon,
  ComputerIcon,
  SmartPhone02Icon,
  TabletIcon,
  Settings02Icon,
  CheckmarkCircle02Icon,
  Alert02Icon,
  ReloadIcon,
  LogoutIcon,
  ActivityIcon,
  LockKeyIcon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const toast = useToast()

// State
const isTogglingTwoFactor = ref(false)
const isLoadingSessions = ref(false)
const isTerminating = ref(null)
const isTerminatingAll = ref(false)
const twoFactorEnabled = ref(false)

// Mock data
const sessions = ref([
  {
    sessionId: '1',
    device: 'Windows PC',
    browser: 'Chrome 120',
    location: 'São Paulo, Brasil',
    lastActivity: new Date(),
    isCurrent: true
  },
  {
    sessionId: '2',
    device: 'iPhone',
    browser: 'Safari',
    location: 'São Paulo, Brasil',
    lastActivity: new Date(Date.now() - 2 * 60 * 60 * 1000)
  },
  {
    sessionId: '3',
    device: 'MacBook',
    browser: 'Firefox 119',
    location: 'Rio de Janeiro, Brasil',
    lastActivity: new Date(Date.now() - 24 * 60 * 60 * 1000)
  }
])

const securityActivity = ref([
  {
    id: '1',
    type: 'login',
    title: 'Login realizado',
    description: 'Login realizado com sucesso via Chrome',
    timestamp: new Date()
  },
  {
    id: '2',
    type: 'password_change',
    title: 'Senha alterada',
    description: 'Senha alterada com sucesso',
    timestamp: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)
  },
  {
    id: '3',
    type: 'failed_login',
    title: 'Tentativa de login falhada',
    description: 'Tentativa de login falhada',
    timestamp: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000)
  }
])

// Methods
const toggleTwoFactor = async () => {
  isTogglingTwoFactor.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    twoFactorEnabled.value = !twoFactorEnabled.value
    
    const message = twoFactorEnabled.value 
      ? 'Autenticação de dois fatores ativada' 
      : 'Autenticação de dois fatores desativada'
    
    toast.addToast(message, 'success')
  } catch (error) {
    toast.addToast('Erro ao alterar configuração de dois fatores', 'error')
  } finally {
    isTogglingTwoFactor.value = false
  }
}

const refreshSessions = async () => {
  isLoadingSessions.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    toast.addToast('Sessões atualizadas', 'success')
  } catch (error) {
    toast.addToast('Erro ao atualizar sessões', 'error')
  } finally {
    isLoadingSessions.value = false
  }
}

const terminateSession = async (sessionId) => {
  isTerminating.value = sessionId
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1000))
    
    sessions.value = sessions.value.filter(s => s.sessionId !== sessionId)
    toast.addToast('Sessão encerrada', 'success')
  } catch (error) {
    toast.addToast('Erro ao encerrar sessão', 'error')
  } finally {
    isTerminating.value = null
  }
}

const terminateAllOtherSessions = async () => {
  isTerminatingAll.value = true
  
  try {
    // Simulate API call
    await new Promise(resolve => setTimeout(resolve, 1500))
    
    sessions.value = sessions.value.filter(s => s.isCurrent)
    toast.addToast('Todas as outras sessões foram encerradas', 'success')
  } catch (error) {
    toast.addToast('Erro ao encerrar sessões', 'error')
  } finally {
    isTerminatingAll.value = false
  }
}

const getDeviceIcon = (device) => {
  if (device.includes('iPhone') || device.includes('Android')) {
    return SmartPhone02Icon
  } else if (device.includes('iPad') || device.includes('Tablet')) {
    return TabletIcon
  }
  return ComputerIcon
}

const getActivityIcon = (type) => {
  switch (type) {
    case 'login':
      return CheckmarkCircle02Icon
    case 'password_change':
      return LockKeyIcon
    case 'failed_login':
      return Alert02Icon
    default:
      return ActivityIcon
  }
}

const getActivityIconClass = (type) => {
  switch (type) {
    case 'login':
      return 'text-green-500'
    case 'password_change':
      return 'text-blue-500'
    case 'failed_login':
      return 'text-red-500'
    default:
      return 'text-gray-500'
  }
}

const formatDate = (date) => {
  return new Intl.DateTimeFormat('pt-BR', {
    year: 'numeric',
    month: 'short',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit'
  }).format(date)
}

// Lifecycle
onMounted(() => {
  // Load user security settings
})
</script>

<style scoped>
.security-content {
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.card-content {
  padding: 1.5rem;
}

.security-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  gap: 1.5rem;
}

.security-info {
  flex: 1;
}

.security-title {
  font-size: 1rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.5rem 0;
}

.security-description {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 0.5rem 0;
  font-size: 0.875rem;
}

.security-status {
  margin-top: 0.5rem;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.25rem 0.75rem;
  border-radius: 12px;
  font-size: 0.75rem;
  font-weight: 500;
}

.status-enabled {
  background: rgba(34, 197, 94, 0.1);
  color: #059669;
}

.status-disabled {
  background: rgba(239, 68, 68, 0.1);
  color: #dc2626;
}

.security-actions {
  flex-shrink: 0;
}

.sessions-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: 1.5rem;
  gap: 1rem;
  flex-wrap: wrap;
}

.sessions-description {
  color: var(--iluria-color-text-secondary);
  margin: 0;
  font-size: 0.875rem;
}

.sessions-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
  margin-bottom: 1.5rem;
}

.session-item {
  display: flex;
  align-items: center;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
}

.session-current {
  border-color: var(--iluria-color-primary);
  background: rgba(59, 130, 246, 0.05);
}

.session-icon {
  flex-shrink: 0;
  color: var(--iluria-color-text-secondary);
}

.session-info {
  flex: 1;
}

.session-main {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  margin-bottom: 0.25rem;
}

.session-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.session-current-badge {
  padding: 0.125rem 0.5rem;
  background: var(--iluria-color-primary);
  color: white;
  border-radius: 12px;
  font-size: 0.625rem;
  font-weight: 500;
  text-transform: uppercase;
}

.session-details {
  display: flex;
  gap: 1rem;
  font-size: 0.75rem;
  color: var(--iluria-color-text-muted);
}

.session-actions {
  flex-shrink: 0;
}

.sessions-footer {
  padding-top: 1rem;
  border-top: 1px solid var(--iluria-color-border);
}

.activity-list {
  display: flex;
  flex-direction: column;
  gap: 1rem;
}

.activity-item {
  display: flex;
  align-items: flex-start;
  gap: 1rem;
  padding: 1rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
}

.activity-icon {
  flex-shrink: 0;
  margin-top: 0.125rem;
}

.activity-info {
  flex: 1;
}

.activity-title {
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin: 0 0 0.25rem 0;
}

.activity-description {
  color: var(--iluria-color-text-secondary);
  margin: 0 0 0.5rem 0;
  font-size: 0.75rem;
}

.activity-date {
  font-size: 0.75rem;
  color: var(--iluria-color-text-muted);
}



/* Responsive */
@media (max-width: 768px) {
  .security-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 1rem;
  }
  
  .security-actions {
    width: 100%;
  }
  
  .sessions-header {
    flex-direction: column;
    align-items: flex-start;
  }
  
  .session-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.75rem;
  }
  
  .session-main {
    flex-direction: column;
    align-items: flex-start;
    gap: 0.5rem;
  }
  
  .session-details {
    flex-direction: column;
    gap: 0.25rem;
  }
  
  .session-actions {
    width: 100%;
  }
  

}
</style>