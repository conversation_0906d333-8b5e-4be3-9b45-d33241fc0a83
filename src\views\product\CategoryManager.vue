<template>
  <div class="product-editor-container">
    <!-- Header with actions -->
    <IluriaHeader
      :title="$t('editCategory.title')"
      :subtitle="$t('editCategory.subtitle')"
      :showCancel="true"
      :cancelText="$t('cancel')"
      :showSave="true"
      :saveText="$t('save')"
      @cancel-click="cancelChanges"
      @save-click="confirmSave"
    />

    <!-- Category Manager Container -->
    <ViewContainer title="Categorias" :icon="HierarchyFilesIcon" iconColor="green">
      <div class="select-none text-sm">

        <!-- First Level Menu -->
        <div v-for="(item, index) in menuItems" :key="item.id" class="relative">
          <div :id="item.id" class="menu-container first-level-menu flex items-center p-3 bg-white rounded-lg shadow-sm mb-3 relative"
            @dragover.prevent="handleDragOver($event, item.id, null, 1)"
            @dragenter.prevent="handleDragEnter($event, item.id, null)" @dragleave="handleDragLeave($event, item.id)"
            @drop="handleDrop($event, item.id, null)" @mouseenter="hoveredItemId = item.id" @mouseleave="hoveredItemId = null"
            draggable="true" @dragstart="handleDragStart($event, item, null)" @dragend="handleDragEnd">
            <div class="grid place-items-center mr-3">
              <HugeiconsIcon :icon="More03Icon" size="20" class="text-gray-400" />
            </div>

            <button v-if="item.children && item.children.length > 0" @click="toggleCollapse(item)"
              class="mr-3 cursor-pointer p-1 hover:bg-gray-100 rounded">
              <svg v-if="!item.collapsed" xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-5 w-5 text-gray-500">
                <path d="m6 9 6 6 6-6" />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-5 w-5 text-gray-500">
                <path d="m9 18 6-6-6-6" />
              </svg>
            </button>

            <!-- Inline editable menu title -->
            <div class="flex-grow flex items-center">
              <span v-if="editingItemId !== item.id" @click="startEditing(item.id, item.title)" class="menu-title text-gray-800 cursor-pointer hover:text-gray-900 font-medium">
                {{ item.title }}
              </span>
              <div v-else class="flex items-center w-full">
                <input type="text" v-model="editingText" ref="editInput" @keydown.enter="confirmEdit(item)"
                  @keydown.esc="cancelEdit" @blur="confirmEdit(item)" class="menu-title-edit-field w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                  :class="{ 'border-red-500': editingError }" />
                <!-- Confirm button -->
                <button @click.stop="confirmEdit(item)"
                  class="ml-2 text-gray-400 hover:text-green-500 transition-colors duration-200 cursor-pointer p-1">
                  <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                    stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                    class="h-5 w-5">
                    <path d="M20 6 9 17l-5-5" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Action buttons - only visible on hover -->
            <div :class="['flex items-center gap-1 transition-opacity', { 'invisible': hoveredItemId !== item.id || !!draggedItem || editingItemId === item.id } ]">
              <!-- Edit button -->
              <button @click.stop="startEditing(item.id, item.title)"
                class="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer">
                <HugeiconsIcon :icon="Edit02Icon" size="18" :strokeWidth="2" />
              </button>

              <!-- Delete button -->
              <button @click.stop="deleteMenuItem(item.id, null)"
                class="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer">
                <HugeiconsIcon :icon="Delete03Icon" size="18" :strokeWidth="2" />
              </button>

              <!-- Add button (for adding a second level menu) -->
              <button @click.stop="addMenuItem(item)"
                class="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer">
                <HugeiconsIcon :icon="AddSquareIcon" size="18" :strokeWidth="2" />
              </button>

              <button @click.stop="addSeoConfig(item)"
                class="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors duration-200 cursor-pointer">
                <HugeiconsIcon :icon="SeoIcon" size="18" :strokeWidth="2" />
              </button>
            </div>

            <!-- Drop indicators for level 1 -->
            <div
              v-if="dropIndicator.visible && dropIndicator.targetId === item.id && dropIndicator.position === 'before'"
              class="drop-indicator drop-indicator-before indicator-level-1"></div>
            <div
              v-if="dropIndicator.visible && dropIndicator.targetId === item.id && dropIndicator.position === 'after'"
              class="drop-indicator drop-indicator-after indicator-level-1"></div>
            <div
              v-if="dropIndicator.visible && dropIndicator.targetId === item.id && dropIndicator.position === 'child'"
              :class="[
                'drop-as-child-indicator',
                { 'drop-forbidden': dropIndicator.forbidden }
              ]">
              <svg v-if="!dropIndicator.forbidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                stroke-linejoin="round" class="plus-icon">
                <path d="M5 12h14" />
                <path d="M12 5v14" />
              </svg>
              <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="forbidden-icon">
                <circle cx="12" cy="12" r="10" />
                <path d="m15 9-6 6" />
                <path d="m9 9 6 6" />
              </svg>
            </div>
          </div>

          <!-- Second Level Menu -->
          <div :style="{ opacity: item.collapsed ? 0 : 1 }" class="animate-opacity">
            <div v-if="item.children && item.children.length > 0 && !item.collapsed"
              class="pl-16 relative second-level-menus-container">
              <div v-for="(child, childIndex) in item.children" :key="child.id" class="relative">

                <!-- Second Level line connector -->
                <div :id="`level-2-line-connector-${child.id}`" class="line-connector absolute w-px bg-gray-300"
                  :style="{
                    height: `${child.connectorHeight}px`,
                    top: `${child.connectorTop}px`
                  }">
                </div>

                <div class="menu-container second-level-menu flex items-center p-3 bg-white rounded-lg shadow-sm mb-3 relative"
                  :id="child.id"
                  :class="{ 'opacity-50': isDragging(child.id) }"
                  @dragover.prevent="handleDragOver($event, child.id, item.id, 2)"
                  @dragenter.prevent="handleDragEnter($event, child.id, item.id)"
                  @dragleave="handleDragLeave($event, child.id)" @drop="handleDrop($event, child.id, item.id)"
                  @mouseenter="hoveredItemId = child.id" @mouseleave="hoveredItemId = null" draggable="true"
                  @dragstart="handleDragStart($event, child, item)" @dragend="handleDragEnd">
                  <div class="grid place-items-center mr-3">
                    <HugeiconsIcon :icon="More03Icon" size="20" class="text-gray-400" />
                  </div>

                  <button v-if="child.children && child.children.length > 0" @click="toggleCollapse(child)"
                    class="mr-3 cursor-pointer p-1 hover:bg-gray-100 rounded">
                    <svg v-if="!child.collapsed" xmlns="http://www.w3.org/2000/svg" width="20" height="20"
                      viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" class="h-5 w-5 text-gray-500">
                      <path d="m6 9 6 6 6-6" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24"
                      fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="h-5 w-5 text-gray-500">
                      <path d="m9 18 6-6-6-6" />
                    </svg>
                  </button>

                  <!-- Inline editable menu title for child -->
                  <div class="flex-grow flex items-center">
                    <span v-if="editingItemId !== child.id" @click="startEditing(child.id, child.title)"
                      class="menu-title text-gray-800 cursor-pointer hover:text-gray-900 font-medium">
                      {{ child.title }}
                    </span>
                    <div v-else class="flex items-center w-full">
                      <input type="text" v-model="editingText" ref="editInput" @keydown.enter="confirmEdit(child)"
                        @keydown.esc="cancelEdit" @blur="confirmEdit(child)" class="menu-title-edit-field w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                        :class="{ 'border-red-500': editingError }" />
                      <!-- Confirm button -->
                      <button @click.stop="confirmEdit(child)"
                        class="ml-2 text-gray-400 hover:text-green-500 transition-colors duration-200 cursor-pointer p-1">
                        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                          stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                          class="h-5 w-5">
                          <path d="M20 6 9 17l-5-5" />
                        </svg>
                      </button>
                    </div>
                  </div>

                  <!-- Action buttons - only visible on hover -->
                  <div :class="['flex items-center gap-1 transition-opacity', { 'invisible': hoveredItemId !== child.id || !!draggedItem || editingItemId === child.id } ]">
                    <!-- Edit button -->
                    <button @click.stop="startEditing(child.id, child.title)"
                      class="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer">
                      <HugeiconsIcon :icon="Edit02Icon" size="18" :strokeWidth="2" />
                    </button>

                    <!-- Delete button -->
                    <button @click.stop="deleteMenuItem(child.id, item.id)"
                      class="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer">
                      <HugeiconsIcon :icon="Delete03Icon" size="18" :strokeWidth="2" />
                    </button>

                    <!-- Add button (for adding a third level menu) -->
                    <button @click.stop="addMenuItem(child)"
                      class="p-2 text-gray-400 hover:text-green-500 hover:bg-green-50 rounded-lg transition-colors duration-200 cursor-pointer">
                      <HugeiconsIcon :icon="AddSquareIcon" size="18" :strokeWidth="2" />
                    </button>
                    <button @click.stop="addSeoConfig(child)"
                      class="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors duration-200 cursor-pointer">
                      <HugeiconsIcon :icon="SeoIcon" size="18" :strokeWidth="2" />
                    </button>
                  </div>

                 <!-- Drop indicators for level 2 -->
                 <div
                    v-if="dropIndicator.visible && dropIndicator.targetId === child.id && dropIndicator.position === 'before'"
                    class="drop-indicator drop-indicator-before indicator-level-2"></div>
                  <div
                    v-if="dropIndicator.visible && dropIndicator.targetId === child.id && dropIndicator.position === 'after'"
                    class="drop-indicator drop-indicator-after indicator-level-2"></div>
                  <div
                    v-if="dropIndicator.visible && dropIndicator.targetId === child.id && dropIndicator.position === 'child'"
                    :class="[
                      'drop-as-child-indicator',
                      { 'drop-forbidden': dropIndicator.forbidden }
                    ]">
                    <svg v-if="!dropIndicator.forbidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                      viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                      stroke-linejoin="round" class="plus-icon">
                      <path d="M5 12h14" />
                      <path d="M12 5v14" />
                    </svg>
                    <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                      fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                      class="forbidden-icon">
                      <circle cx="12" cy="12" r="10" />
                      <path d="m15 9-6 6" />
                      <path d="m9 9 6 6" />
                    </svg>
                  </div>
                </div>

                <!-- Third Level Menu -->
                <div :style="{ opacity: child.collapsed ? 0 : 1 }" class="animate-opacity">
                  <div v-if="child.children && child.children.length > 0 && !child.collapsed"
                    class="pl-16 relative third-level-menus-container">
                    <div v-for="(grandchild, grandchildIndex) in child.children" :key="grandchild.id" class="relative">

                      <!-- Third Level line connector -->
                      <div :id="`level-3-line-connector-${grandchild.id}`" class="line-connector absolute w-px bg-gray-300"
                        :style="{
                          height: `${grandchild.connectorHeight}px`,
                          top: `${grandchild.connectorTop}px`
                        }">
                      </div>

                      <div class="menu-container third-level-menu flex items-center p-3 bg-white rounded-lg shadow-sm mb-3 relative"
                        :id="grandchild.id"
                        :class="{ 'opacity-50': isDragging(grandchild.id) }"
                        @dragover.prevent="handleDragOver($event, grandchild.id, child.id, 3)"
                        @dragenter.prevent="handleDragEnter($event, grandchild.id, child.id)"
                        @dragleave="handleDragLeave($event, grandchild.id)"
                        @drop="handleDrop($event, grandchild.id, child.id)" @mouseenter="hoveredItemId = grandchild.id"
                        @mouseleave="hoveredItemId = null" draggable="true"
                        @dragstart="handleDragStart($event, grandchild, child)" @dragend="handleDragEnd">
                        <div class="grid place-items-center mr-3">
                          <HugeiconsIcon :icon="More03Icon" size="20" class="text-gray-400" />
                        </div>

                        <!-- Inline editable menu title for grandchild -->
                        <div class="flex-grow flex items-center">
                          <span v-if="editingItemId !== grandchild.id"
                            @click="startEditing(grandchild.id, grandchild.title)" class="menu-title text-gray-800 cursor-pointer hover:text-gray-900 font-medium">
                            {{ grandchild.title }}
                          </span>
                          <div v-else class="flex items-center w-full">
                            <input type="text" v-model="editingText" ref="editInput"
                              @keydown.enter="confirmEdit(grandchild)" @keydown.esc="cancelEdit"
                              @blur="confirmEdit(grandchild)" class="menu-title-edit-field w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500"
                              :class="{ 'border-red-500': editingError }" />
                            <!-- Confirm button -->
                            <button @click.stop="confirmEdit(grandchild)"
                              class="ml-2 text-gray-400 hover:text-green-500 transition-colors duration-200 cursor-pointer p-1">
                              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                                class="h-5 w-5">
                                <path d="M20 6 9 17l-5-5" />
                              </svg>
                            </button>
                          </div>
                        </div>

                        <!-- Action buttons - only visible on hover -->
                        <div :class="['flex items-center gap-1 transition-opacity', { 'invisible': hoveredItemId !== grandchild.id || !!draggedItem || editingItemId === grandchild.id } ]">
                          <!-- Edit button -->
                          <button @click.stop="startEditing(grandchild.id, grandchild.title)"
                            class="p-2 text-gray-400 hover:text-blue-500 hover:bg-blue-50 rounded-lg transition-colors duration-200 cursor-pointer">
                            <HugeiconsIcon :icon="Edit02Icon" size="18" :strokeWidth="2" />
                          </button>

                          <!-- Delete button -->
                          <button @click.stop="deleteMenuItem(grandchild.id, child.id)"
                            class="p-2 text-gray-400 hover:text-red-500 hover:bg-red-50 rounded-lg transition-colors duration-200 cursor-pointer">
                            <HugeiconsIcon :icon="Delete03Icon" size="18" :strokeWidth="2" />
                          </button>
                          
                          <button @click.stop="addSeoConfig(grandchild)"
                            class="p-2 text-gray-400 hover:text-purple-500 hover:bg-purple-50 rounded-lg transition-colors duration-200 cursor-pointer">
                            <HugeiconsIcon :icon="SeoIcon" size="18" :strokeWidth="2" />
                          </button>

                        </div>

                        <!-- Drop indicators for level 3 -->
                        <div
                          v-if="dropIndicator.visible && dropIndicator.targetId === grandchild.id && dropIndicator.position === 'before'"
                          class="drop-indicator drop-indicator-before indicator-level-3"></div>
                        <div
                          v-if="dropIndicator.visible && dropIndicator.targetId === grandchild.id && dropIndicator.position === 'after'"
                          class="drop-indicator drop-indicator-after indicator-level-3"></div>
                        <div
                          v-if="dropIndicator.visible && dropIndicator.targetId === grandchild.id && dropIndicator.position === 'child'"
                          :class="[
                            'drop-as-child-indicator',
                            { 'drop-forbidden': dropIndicator.forbidden }
                          ]">
                          <svg v-if="!dropIndicator.forbidden" xmlns="http://www.w3.org/2000/svg" width="24" height="24"
                            viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2"
                            stroke-linecap="round" stroke-linejoin="round" class="plus-icon">
                            <path d="M5 12h14" />
                            <path d="M12 5v14" />
                          </svg>
                          <svg v-else xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24"
                            fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round"
                            stroke-linejoin="round" class="forbidden-icon">
                            <circle cx="12" cy="12" r="10" />
                            <path d="m15 9-6 6" />
                            <path d="m9 9 6 6" />
                          </svg>
                        </div>
                      </div>
                    </div>

                                <!-- Add submenu item button (level 3) -->
            <button @click="addMenuItem(child)"
              class="flex items-center text-gray-700 hover:text-gray-900 hover:bg-gray-50 p-3 w-full cursor-pointer rounded-lg transition-colors duration-200">
              <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                class="h-5 w-5 mr-2">
                <circle cx="12" cy="12" r="10" />
                <path d="M12 8v8" />
                <path d="M8 12h8" />
              </svg>
              {{ $t('add') }} {{ $t('editCategory.actions.to') }} &nbsp;
              <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-sm font-medium">{{ child.title }}</span>
            </button>
                  </div>
                </div>
              </div>

              <!-- Add submenu item button (level 2) -->
              <button @click="addMenuItem(item)"
                class="flex items-center text-gray-700 hover:text-gray-900 hover:bg-gray-50 p-3 w-full cursor-pointer rounded-lg transition-colors duration-200">
                <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
                  stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"
                  class="h-5 w-5 mr-2">
                  <circle cx="12" cy="12" r="10" />
                  <path d="M12 8v8" />
                  <path d="M8 12h8" />
                </svg>
                {{ $t('add') }} {{ $t('editCategory.actions.to') }} &nbsp;
                <span class="bg-gray-100 text-gray-700 px-2 py-1 rounded-lg text-sm font-medium">{{ item.title }}</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Add root menu item button -->
        <button @click="addRootMenuItem"
          class="flex items-center justify-center text-gray-700 hover:text-gray-900 hover:bg-gray-50 p-4 w-full border-2 border-dashed border-gray-300 hover:border-gray-400 rounded-lg cursor-pointer transition-all duration-200">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none"
            stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round" class="h-5 w-5 mr-2">
            <circle cx="12" cy="12" r="10" />
            <path d="M12 8v8" />
            <path d="M8 12h8" />
          </svg>
          <span class="font-medium">{{ $t('editCategory.actions.addRoot') }}</span>
        </button>
      </div>

      <IluriaConfirmationModal 
        :isVisible="showConfirmDialog"
        :title="confirmData.title" 
        :message="confirmData.message"
        :type="confirmData.type || 'error'"
        @confirm="handleConfirm"
        @cancel="showConfirmDialog = false"
      />
    </ViewContainer>
  </div>
</template>

<script setup>
import { HugeiconsIcon } from '@hugeicons/vue';
import { More03Icon } from '@hugeicons-pro/core-bulk-rounded';
import { Edit02Icon, Delete03Icon, AddSquareIcon, SeoIcon, HierarchyFilesIcon, FloppyDiskIcon } from '@hugeicons-pro/core-stroke-rounded';
import { categoryService } from '@/services/category.service';
import IluriaConfirmationModal from '@/components/iluria/modal/IluriaConfirmationModal.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import { useToast } from '@/services/toast.service';
import { useTheme } from '@/composables/useTheme';

const { initTheme } = useTheme();

// Inicializar o tema
initTheme();
</script>

<script>
export default {
  data() {
    return {
      // Maximum allowed depth for menu items
      MAX_DEPTH: 3,

      // Initial menu structure (will be populated from API)
      menuItems: [],

      // Track changes to be saved
      pendingChanges: {
        deletedCategories: [],
        updatedCategories: [],
        newCategories: []
      },

      // Store original data for reverting changes
      originalMenuItems: [],

      // Drag and drop state
      draggedItem: null,
      draggedItemParent: null,
      dragTargets: new Set(),

      dropIndicator: {
        visible: false,
        targetId: null,
        position: null, // 'before', 'after', 'child'
        level: 1,
        forbidden: false // Property to track if drop would exceed depth limit
      },

      // Inline editing state
      editingItemId: null,
      editingText: '',
      originalText: '',
      editingError: false,

      // Hover state for action buttons visibility
      hoveredItemId: null,
      toast: null,
      
      // Loading state for buttons
      saving: false,
      
      // Confirmation dialog state
      showConfirmDialog: false,
      confirmData: { title: '', message: '', type: 'error', onConfirm: null }
    }
  },
  created() {
    this.toast = useToast();
  },
  async mounted() {
    this.updateConnectors();
    await this.loadCategories();
    this.updateConnectors();
  },
  methods: {
    // Load categories from API
    async loadCategories() {
      try {
        const categories = await categoryService.fetchCategories();
        this.menuItems = categories.map(category => ({
          id: category.id,
          parentId: category.parentId,
          title: category.title,
          children: category.children ? category.children.map(child => ({
            id: child.id,
            parentId: child.parentId,
            title: child.title,
            children: child.children ? child.children.map(grandchild => ({
              id: grandchild.id,
              parentId: grandchild.parentId,
              title: grandchild.title,
              children: [],
              collapsed: false
            })) : [],
            collapsed: false,
            connectorHeight: 0,
            connectorTop: 0
          })) : [],
          collapsed: false
        }));
        // Store original state
        this.originalMenuItems = JSON.parse(JSON.stringify(this.menuItems));
        this.updateConnectors();
      } catch (error) {
        this.$toast.add({
          severity: 'error',
          summary: this.$t('UnknownError'),
          detail: this.$t('editCategory.actions.errorLoading'),
          life: 3000
        });
      }
    },
    updateConnectors() {
      this.menuItems.forEach(item => {
        if (item.children && item.children.length > 0) {
          item.children.forEach(child => {
            child.connectorHeight = this.calculateConnectorHeight(item.id, child.id)
            child.connectorTop = this.calculateConnectorTop(item.id, child.id)

            // Third level connectors
            if (child.children && child.children.length > 0) {
              child.children.forEach(grandchild => {
                grandchild.connectorHeight = this.calculateConnectorHeight(child.id, grandchild.id)
                grandchild.connectorTop = this.calculateConnectorTop(child.id, grandchild.id)
              })
            }
          })
        }
      })
    },

    calculateConnectorTop(parentId, childId) {
      const parentElement = document.getElementById(parentId)
      const childElement = document.getElementById(childId)
      if (parentElement && childElement) {
        const parentBottom = parentElement.getBoundingClientRect().bottom
        const childTop = childElement.getBoundingClientRect().top

        const topPosition = (childTop - parentBottom) * -1
        return topPosition
      }
      return 30
    },

    calculateConnectorHeight(parentId, childId) {
      const parentElement = document.getElementById(parentId)
      const childElement = document.getElementById(childId)
      if (parentElement && childElement) {
        const parentBottom = parentElement.getBoundingClientRect().bottom
        const childTop = childElement.getBoundingClientRect().top
        const childHeight = childElement.getBoundingClientRect().height

        return childTop - parentBottom + childHeight / 2
      }

      return 30;
    },

    findMenuItemById(id, items = this.menuItems) {
      for (const item of items) {
        if (item.id === id) {
          return item
        }
        if (item.children) {
          const found = this.findMenuItemById(id, item.children)
          if (found) return found
        }
      }
      return null
    },

    // Delete a menu item
    deleteMenuItem(itemId, parentId) {
      // Add to pending deletions
      this.pendingChanges.deletedCategories.push(itemId);

      // Visual removal from menu
      if (parentId) {
        const parent = this.findMenuItem(this.menuItems, parentId);
        if (parent && parent.item) {
          parent.item.children = parent.item.children.filter(child => child.id !== itemId);
        }
      } else {
        this.menuItems = this.menuItems.filter(item => item.id !== itemId);
      }
    },
    cancelChanges() {
      this.confirmData = {
        title: this.$t('editCategory.modal.cancelTitle'),
        message: this.$t('editCategory.modal.cancelMessage'),
        type: 'error',
        onConfirm: () => {
          this.saving = true;
          this.menuItems = JSON.parse(JSON.stringify(this.originalMenuItems));
          
          this.pendingChanges = {
            deletedCategories: []
          };
          
          this.$toast.add({
            severity: 'info',
            summary: this.$t('editCategory.modal.cancelSuccessTitle'),
            detail: this.$t('editCategory.modal.cancelSuccessMessage'),
            life: 3000
          });
          
          this.saving = false;
        }
      }
      this.showConfirmDialog = true
    },
    
    // Confirmation dialog handler
    handleConfirm() {
      if (this.confirmData.onConfirm) {
        this.confirmData.onConfirm()
      }
      this.showConfirmDialog = false
    },

    confirmSave() {
      this.saveChanges(true);
    },

    async addSeoConfig(category) {
      try {
        const isTempId = typeof category.id === 'string' && category.id.startsWith('menu-');
        
        if (isTempId) {
          
          await this.saveChanges(false);
          const savedCategory = this.findCategoryByTitle(category.title);
          
          if (savedCategory) {
            this.$router.push({
              path: `/product/category-manager/seo-config/${savedCategory.id}`,
              query: {
                name: savedCategory.title
              }
            });
          } else {
            this.$toast.add({
              severity: 'error',
              summary: this.$t('UnknownError'),
              detail: this.$t('editCategory.modal.errorMessage'),
              life: 3000
            });
          }
        } else {
          this.$router.push({
            path: `/product/category-manager/seo-config/${category.id}`,
            query: {
              name: category.title
            }
          });
        }
      } catch (error) {
        this.$toast.add({
          severity: 'error',
          summary: this.$t('UnknownError'),
          detail: this.$t('editCategory.modal.errorMessage'),
          life: 3000
        });
      }
    },

    findCategoryByTitle(title) {
      const findInList = (items) => {
        for (const item of items) {
          if (item.title === title) {
            return item;
          }
          
          if (item.children && item.children.length > 0) {
            const found = findInList(item.children);
            if (found) return found;
          }
        }
        return null;
      };
      
      return findInList(this.menuItems);
    },

    async saveChanges(showToast = true) {
      try {
        this.saving = true;
        await categoryService.saveCategories({
          storeId: this.storeId,
          categories: this.menuItems,
          deletedCategories: this.pendingChanges.deletedCategories
        });

        this.pendingChanges = {
          deletedCategories: []
        };

        if (showToast) {
          this.$toast.add({
            severity: 'success',
            summary: this.$t('editCategory.modal.successTitle'),
            detail: this.$t('editCategory.modal.successMessage'),
            life: 3000
          });
        }

        // Refresh categories from server
        await this.loadCategories();
      } catch (error) {
        this.$toast.add({
          severity: 'error',
          summary: this.$t('UnknownError'),
          detail: this.$t('editCategory.modal.errorMessage'),
          life: 3000
        });

        // Revert to original state on error
        this.menuItems = JSON.parse(JSON.stringify(this.originalMenuItems));
      } finally {
        this.saving = false;
      }
    },

    // Start editing a menu item
    startEditing(itemId, currentText) {
      // Don't allow editing while dragging
      if (this.draggedItem) return;

      this.editingItemId = itemId;
      this.editingText = currentText;
      this.originalText = currentText;
      this.editingError = false;

      // Focus the input field after it's rendered
      this.$nextTick(() => {
        if (this.$refs.editInput) {
          // If editInput is an array, get the first element
          const input = Array.isArray(this.$refs.editInput)
            ? this.$refs.editInput[0]
            : this.$refs.editInput;

          if (input) {
            input.focus();
            input.select();
          }
        }
      });
    },

    // Confirm the edit and update the menu item
    confirmEdit(item) {
      // Validate the input - don't allow empty titles
      if (!this.editingText.trim()) {
        this.editingError = true;
        return;
      }

      // Find the item and update its title
      const itemToUpdate = this.findMenuItem(this.menuItems, item.id);
      if (itemToUpdate && itemToUpdate.item) {
        itemToUpdate.item.title = this.editingText.trim();
      }

      // Reset editing state
      this.editingItemId = null;
      this.editingText = '';
      this.editingError = false;
    },

    // Cancel the edit and revert to the original text
    cancelEdit() {
      // Find the item being edited
      if (this.editingItemId) {
        const itemToUpdate = this.findMenuItem(this.menuItems, this.editingItemId);
        if (itemToUpdate && itemToUpdate.item) {
          // No need to update the title as we're canceling the edit
        }
      }
      
      this.editingItemId = null;
      this.editingText = '';
      this.editingError = false;
    },


    // Utility functions for drag and drop
    isDragging(id) {
      return this.draggedItem && this.draggedItem.id === id;
    },

    isDragTarget(id) {
      return this.dragTargets.has(id);
    },

    calculateDropPosition(y, height) {
      const threshold = height / 3;
      if (y < threshold) return 'before';
      if (y > height - threshold) return 'after';
      return 'child';
    },

    wouldExceedDepthLimit(targetId, position) {
      if (position !== 'child') return false;
      
      const MAX_DEPTH = 3;
      const getDepth = (item) => {
        if (!item.children || item.children.length === 0) return 1;
        return 1 + Math.max(...item.children.map(getDepth));
      };

      const target = this.findMenuItem(this.menuItems, targetId);
      if (!target || !target.item) return true;

      const currentDepth = getDepth(target.item);
      const draggedDepth = this.draggedItem ? getDepth(this.draggedItem) : 1;

      return currentDepth + draggedDepth > MAX_DEPTH;
    },

    findMenuItem(items, id, parent = null) {
      for (let i = 0; i < items.length; i++) {
        const item = items[i];
        if (item.id === id) {
          return { item, parent, index: i };
        }
        if (item.children) {
          const found = this.findMenuItem(item.children, id, item);
          if (found) return found;
        }
      }
      return null;
    },

    // Toggle collapse state of menu item
    toggleCollapse(item) {
      item.collapsed = !item.collapsed

      // Wait for DOM to update
      this.$nextTick(() => {
        this.updateConnectors()
      })
    },

    // Add a new menu item to a parent
    addMenuItem(parent) {
      // Check if adding would exceed depth limit
      const parentDepth = this.getItemDepth(this.menuItems, parent.id)
      if (parentDepth >= this.MAX_DEPTH - 1) {
        return
      }
      // Ensure parent is expanded if it was collapsed
      if (parent.collapsed) {
        parent.collapsed = false
      }

      const newItem = {
        id: this.generateId(),
        title: this.$t('editCategory.actions.newItem'),
        children: [],
        collapsed: false,
        connectorHeight: 20
      }

      parent.children.push(newItem)

      // Wait for DOM to update
      this.$nextTick(() => {
        this.updateConnectors()
        this.startEditing(newItem.id, newItem.title)
      })
    },

    // Generate unique ID
    generateId() {
      return `menu-${Date.now()}-${Math.floor(Math.random() * 1000)}`
    },

    // Add a new root menu item
    addRootMenuItem() {
      const newItem = {
        id: this.generateId(),
        title: this.$t('editCategory.actions.newRootItem'),
        children: [],
        collapsed: false
      }
      this.menuItems.push(newItem)

      // Wait for DOM to update, then start editing the new item
      this.$nextTick(() => {
        this.startEditing(newItem.id, newItem.title)
      })
    },

    // Calculate the depth of a menu item
    getItemDepth(items, id, currentDepth = 0) {
      for (const item of items) {
        if (item.id === id) {
          return currentDepth
        }
        if (item.children && item.children.length > 0) {
          const depth = this.getItemDepth(item.children, id, currentDepth + 1)
          if (depth !== -1) {
            return depth
          }
        }
      }
      return -1
    },

    getSubtreeDepth(item) {
      if (!item.children || item.children.length === 0) {
        return 0
      }

      let maxChildDepth = 0
      for (const child of item.children) {
        const childDepth = this.getSubtreeDepth(child)
        maxChildDepth = Math.max(maxChildDepth, childDepth)
      }

      return maxChildDepth + 1
    },

    getMaxDepthOfSubtree(item) {
      if (!item.children || item.children.length === 0) {
        return 0
      }

      let maxDepth = 0
      for (const child of item.children) {
        const childDepth = this.getMaxDepthOfSubtree(child)
        maxDepth = Math.max(maxDepth, childDepth)
      }

      return maxDepth + 1
    },

    // Check if an item is a root level menu item
    isRootMenuItem(itemId) {
      return this.menuItems.some(item => item.id === itemId)
    },

    // Check if the dragged item is a second-level menu item
    isDraggedItemSecondLevel() {
      if (!this.draggedItem) return false
      const depth = this.getItemDepth(this.menuItems, this.draggedItem.id)
      return depth === 1 // 0-based index, so level 2 is depth 1
    },

    // Check if the dragged item is a third-level menu item
    isDraggedItemThirdLevel() {
      if (!this.draggedItem) return false
      const depth = this.getItemDepth(this.menuItems, this.draggedItem.id)
      return depth === 2 // 0-based index, so level 3 is depth 2
    },

    // Check if an item is a descendant of another item
    isDescendantOf(possibleAncestor, itemId) {
      if (!possibleAncestor.children || possibleAncestor.children.length === 0) {
        return false
      }

      for (const child of possibleAncestor.children) {
        if (child.id === itemId || this.isDescendantOf(child, itemId)) {
          return true
        }
      }

      return false
    },

    // Determine drop position based on mouse position
    determineDropPosition(event, element) {
      const rect = element.getBoundingClientRect()
      const y = event.clientY - rect.top
      const height = rect.height

      // Top 25% - drop before
      if (y < height * 0.33) return 'before'

      // Bottom 25% - drop after
      if (y > height * 0.66) return 'after'

      // Middle 50% - drop as child
      return 'child'
    },

    // Calculate the resulting depth if an item is moved to a new position
    calculateResultingDepth(draggedItem, targetId, position) {
      // Find the target item
      const targetInfo = this.findMenuItem(this.menuItems, targetId)
      if (!targetInfo) return this.MAX_DEPTH + 1 // Invalid operation

      // Get the subtree depth of the dragged item
      const draggedSubtreeDepth = this.getSubtreeDepth(draggedItem)

      // Calculate the base depth based on position
      let baseDepth

      if (position === 'before' || position === 'after') {
        // For before/after, the item will be at the same level as the target
        const targetDepth = this.getItemDepth(this.menuItems, targetId)
        baseDepth = targetDepth
      } else if (position === 'child') {
        // For child position, the item will be one level deeper than the target
        const targetDepth = this.getItemDepth(this.menuItems, targetId)
        baseDepth = targetDepth + 1
      }

      // The resulting depth is the base depth plus the subtree depth
      return baseDepth + draggedSubtreeDepth + 1
    },

    // Check if target is a third-level menu item
    isThirdLevelMenuItem(targetId) {
      const depth = this.getItemDepth(this.menuItems, targetId)
      return depth === 2 // 0-based index, so level 3 is depth 2
    },

    // Enhanced function to check if drop would exceed depth limit or violate other rules
    wouldExceedDepthLimit(targetId, position) {
      if (!this.draggedItem) return false

      // Special case: If dragging a root menu item onto a third-level menu as a child
      if (this.isRootMenuItem(this.draggedItem.id) &&
        this.isThirdLevelMenuItem(targetId) &&
        position === 'child') {
        return true
      }



      // Special case: If dragging a second-level menu item onto a third-level menu as a child
      if (this.isDraggedItemSecondLevel() &&
        this.isThirdLevelMenuItem(targetId) &&
        position === 'child') {
        return true
      }

      // Get the target item's current depth
      const targetDepth = this.getItemDepth(this.menuItems, targetId)

      // Get the maximum depth of the target's subtree
      const targetInfo = this.findMenuItem(this.menuItems, targetId)
      const targetMaxDepth = targetInfo ? this.getMaxDepthOfSubtree(targetInfo.item) : 0

      // Get the maximum depth of the dragged item's subtree
      const draggedSubtreeDepth = this.getSubtreeDepth(this.draggedItem)

      // For 'child' position, check if target already has maximum depth structure
      if (position === 'child') {
        // If target already has a structure at max depth and dragged item has children
        if (targetMaxDepth >= this.MAX_DEPTH - targetDepth - 1 && draggedSubtreeDepth > 0) {
          return true
        }
      }

      // Calculate the resulting depth after the potential drop
      const resultingDepth = this.calculateResultingDepth(this.draggedItem, targetId, position)

      // Check if it would exceed the maximum allowed depth
      return resultingDepth > this.MAX_DEPTH
    },

    // Handle drag start
    handleDragStart(event, item, parent) {
      // Cancel any active editing when starting to drag
      if (this.editingItemId) {
        this.cancelEdit();
      }

      this.draggedItem = item;
      this.draggedItemParent = parent;
      event.dataTransfer.effectAllowed = 'move';
      event.target.classList.add('opacity-50');
    },

    // Handle drag end
    handleDragEnd(event) {
      event.target.classList.remove('opacity-50');
      this.draggedItem = null;
      this.draggedItemParent = null;
      this.dropIndicator.visible = false;
    },

    // Handle drag over
    handleDragOver(event, targetId, parentId, level) {
      event.preventDefault();
      if (!this.draggedItem) return;

      // Não permitir arrastar para dentro de si mesmo ou seus descendentes
      if (this.draggedItem.id === targetId || this.isDescendantOf(this.draggedItem, targetId)) {
        // Mostrar indicador de proibido
        this.dropIndicator = {
          visible: true,
          targetId,
          position: 'child', // Forçar posição 'child' para mostrar o indicador de proibido
          level,
          forbidden: true
        };
        // Mudar cursor para indicar que não é permitido
        event.dataTransfer.dropEffect = 'none';
        return;
      }

      const rect = event.currentTarget.getBoundingClientRect();
      const y = event.clientY - rect.top;
      const height = rect.height;

      // Calcular posição de drop
      const position = this.calculateDropPosition(y, height);

      // Verificar se o drop excederia o limite de profundidade
      const forbidden = this.wouldExceedDepthLimit(targetId, position);

      // Atualizar indicador de drop
      this.dropIndicator = {
        visible: true,
        targetId,
        position,
        level,
        forbidden
      };

      // Mudar cursor para indicar se o drop é permitido
      event.dataTransfer.dropEffect = forbidden ? 'none' : 'move';
    },
        handleDragLeave(event, id) {
      // Only remove if we're leaving the actual target, not entering a child
      if (!event.currentTarget.contains(event.relatedTarget)) {
        this.dragTargets.delete(id)
        // Hide drop indicators if we're leaving the element
        if (this.dropIndicator.targetId === id) {
          this.dropIndicator.visible = false
        }
      }
    },

    handleDragEnter(event, id, parentId) {
      if (!this.draggedItem || this.draggedItem.id === id) return
      // Don't allow dropping on descendants of the dragged item
      if (this.draggedItem && this.isDescendantOf(this.draggedItem, id)) return
      this.dragTargets.add(id)
    },

    // Handle drop
    handleDrop(event, targetId, parentId) {
      event.preventDefault();
      if (!this.draggedItem || !this.dropIndicator.visible) return;

      // Verifica se está tentando mover para dentro de si mesmo ou seus descendentes
      if (this.isDescendantOf(this.draggedItem, targetId)) {
        return;
      }

      const { position } = this.dropIndicator;
      const sourceParentId = this.draggedItemParent ? this.draggedItemParent.id : null;
      
      // Guarda uma cópia do item arrastado com suas subcategorias
      const draggedItemWithChildren = JSON.parse(JSON.stringify(this.draggedItem));

      // Remove item from its current position
      if (sourceParentId) {
        const sourceParent = this.findMenuItem(this.menuItems, sourceParentId);
        if (sourceParent && sourceParent.item) {
          sourceParent.item.children = sourceParent.item.children.filter(
            child => child.id !== this.draggedItem.id
          );
        }
      } else {
        this.menuItems = this.menuItems.filter(item => item.id !== this.draggedItem.id);
      }

      // Add item to its new position
      if (position === 'child') {
        const target = this.findMenuItem(this.menuItems, targetId);
        if (target && target.item) {
          if (!target.item.children) {
            target.item.children = [];
          }
          draggedItemWithChildren.parentId = target.item.id;
          target.item.children.push(draggedItemWithChildren);
        }
      } else {
        const target = this.findMenuItem(this.menuItems, targetId);
        if (target) {
          const targetArray = target.parent ? target.parent.children : this.menuItems;
          const targetIndex = targetArray.findIndex(item => item.id === targetId);
          
          // Update parentId
          draggedItemWithChildren.parentId = target.parent ? target.parent.id : null;
          
          // Insert at the correct position
          if (position === 'before') {
            targetArray.splice(targetIndex, 0, draggedItemWithChildren);
          } else {
            targetArray.splice(targetIndex + 1, 0, draggedItemWithChildren);
          }
        }
      }

      // Reset drag state
      this.draggedItem = null;
      this.draggedItemParent = null;
      this.dropIndicator.visible = false;

      // Update connectors after drop
      this.$nextTick(() => {
        this.updateConnectors();
      });
    },
  }
};
</script>

<style scoped>
/* Product Editor Container (Base styles from NewProduct.vue) */
.product-editor-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background-color: var(--iluria-color-body-bg);
  color: var(--iluria-color-text-primary);
  min-height: 100vh;
  transition: background-color 300ms cubic-bezier(0.4, 0, 0.2, 1), color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Header (Exact same as NewProduct.vue) */
.editor-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
  transition: border-color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text-primary);
  margin: 0;
  line-height: 1.2;
  transition: color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.header-content .page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-secondary);
  margin: 4px 0 0 0;
  transition: color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Line connectors */
.line-connector {
  position: absolute;
  width: 25px;
  left: -25px;
  height: auto;
  border-left: 2px solid var(--iluria-color-border);
  border-bottom: 2px solid var(--iluria-color-border);
  border-bottom-left-radius: 10px;
  background-color: transparent;
  transition: border-color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.cursor-move {
  cursor: grab;
}

.cursor-move:active {
  cursor: grabbing;
}

/* Transition for all visual effects */
.transition-all {
  transition: all 0.2s ease;
}

/* Drop indicator styles */
.drop-indicator {
  position: absolute;
  left: 0;
  right: 0;
  height: 7px;
  z-index: 10;
  border-radius: 4px;
}

.drop-indicator-before {
  top: -10px;
}

.drop-indicator-after {
  bottom: -10px;
}

/* Level-specific colors */
.indicator-level-1 {
  background-color: var(--iluria-color-primary);
  box-shadow: 0 0 4px rgba(59, 130, 246, 0.5);
}

.indicator-level-2 {
  background-color: var(--iluria-color-success);
  box-shadow: 0 0 4px rgba(16, 185, 129, 0.5);
}

.indicator-level-3 {
  background-color: var(--iluria-color-warning);
  box-shadow: 0 0 4px rgba(249, 115, 22, 0.5);
}

/* Submenu indicator */
.drop-as-child-indicator {
  position: absolute;
  inset: 0;
  background-color: var(--iluria-color-hover);
  display: flex;
  align-items: center;
  justify-content: center;
  border-radius: 0.5rem;
  z-index: 5;
  border: 2px dashed var(--iluria-color-border);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Forbidden drop indicator */
.drop-forbidden {
  background-color: rgba(254, 226, 226, 0.7) !important;
  border: 2px dashed var(--iluria-color-error) !important;
}

.plus-icon {
  color: var(--iluria-color-text-secondary);
  width: 24px;
  height: 24px;
  animation: pulse 1.5s infinite;
}

.forbidden-icon {
  color: var(--iluria-color-error);
  width: 24px;
  height: 24px;
  animation: pulse 1.5s infinite;
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }

  50% {
    transform: scale(1.2);
  }

  100% {
    transform: scale(1);
  }
}

.animate-opacity {
  transition: opacity 1.2s ease;
}

.menu-container {
  @apply flex items-center;
  @apply transition-all duration-200;
  @apply relative;
  @apply cursor-grab;
  margin-bottom: 9px;
  background-color: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 0.5rem;
  padding: 12px;
  box-shadow: var(--iluria-shadow-sm);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-container:hover {
  background-color: var(--iluria-color-hover);
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-md);
}

.menu-title {
  @apply cursor-pointer;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
  transition: color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-title:hover {
  color: var(--iluria-color-primary);
}

/* Campo de edição de título */
.menu-title-edit-field {
  background-color: var(--iluria-color-input-bg) !important;
  border-color: var(--iluria-color-input-border) !important;
  color: var(--iluria-color-input-text) !important;
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-title-edit-field:focus {
  border-color: var(--iluria-color-input-border-focus) !important;
  box-shadow: 0 0 0 3px var(--iluria-color-focus-ring) !important;
}

.menu-title-edit-field::placeholder {
  color: var(--iluria-color-input-placeholder) !important;
}

.menu-title-edit-field.border-red-500 {
  border-color: var(--iluria-color-error) !important;
}

/* Botões de ação */
.menu-container button {
  background-color: transparent;
  border: none;
  border-radius: 0.5rem;
  padding: 8px;
  color: var(--iluria-color-text-muted);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-container button:hover {
  transform: scale(1.05);
}

/* Botão de editar (azul) */
.menu-container button:hover:nth-child(1) {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

/* Botão de deletar (vermelho) */
.menu-container button:hover:nth-child(2) {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--iluria-color-error);
}

/* Botão de adicionar (verde) */
.menu-container button:hover:nth-child(3) {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--iluria-color-success);
}

/* Botão de SEO (roxo) */
.menu-container button:hover:nth-child(4) {
  background-color: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

/* Seletores mais específicos para os botões de ação */
.menu-container .flex.items-center.gap-1 button:first-child:hover {
  background-color: rgba(59, 130, 246, 0.1);
  color: #3b82f6;
}

.menu-container .flex.items-center.gap-1 button:nth-child(2):hover {
  background-color: rgba(239, 68, 68, 0.1);
  color: var(--iluria-color-error);
}

.menu-container .flex.items-center.gap-1 button:nth-child(3):hover {
  background-color: rgba(16, 185, 129, 0.1);
  color: var(--iluria-color-success);
}

.menu-container .flex.items-center.gap-1 button:nth-child(4):hover {
  background-color: rgba(168, 85, 247, 0.1);
  color: #a855f7;
}

/* Ícones de arrastar */
.menu-container svg[data-icon="More03Icon"] {
  color: var(--iluria-color-text-muted);
  transition: color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-container:hover svg[data-icon="More03Icon"] {
  color: var(--iluria-color-text-secondary);
}

/* Botões de colapso/expansão */
.menu-container button[class*="hover:bg-gray-100"] {
  background-color: transparent;
  border-radius: 0.375rem;
  padding: 4px;
  color: var(--iluria-color-text-secondary);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-container button[class*="hover:bg-gray-100"]:hover {
  background-color: var(--iluria-color-hover);
  color: var(--iluria-color-text-primary);
}

/* Setas de expansão/colapso */
.menu-container svg {
  color: var(--iluria-color-text-secondary);
  transition: color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Botão de confirmação de edição */
.menu-container .ml-2.text-gray-400 {
  color: var(--iluria-color-text-muted) !important;
  transition: color 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-container .ml-2.text-gray-400:hover {
  color: var(--iluria-color-success) !important;
}

/* Botões de adicionar categoria */
.menu-container ~ button,
button[class*="border-dashed"] {
  background-color: var(--iluria-color-container-bg);
  border-color: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

.menu-container ~ button:hover,
button[class*="border-dashed"]:hover {
  background-color: var(--iluria-color-hover);
  border-color: var(--iluria-color-border-hover);
  color: var(--iluria-color-primary);
}

/* Tags de categoria nos botões de adicionar */
.menu-container ~ button span[class*="bg-gray-100"],
button span[class*="bg-gray-100"] {
  background-color: var(--iluria-color-surface);
  color: var(--iluria-color-text-primary);
  border: 1px solid var(--iluria-color-border);
  transition: all 300ms cubic-bezier(0.4, 0, 0.2, 1);
}

/* Sobrescrever classes específicas do Tailwind para tematização */
:deep(.text-gray-400) {
  color: var(--iluria-color-text-muted) !important;
}

:deep(.text-gray-500) {
  color: var(--iluria-color-text-secondary) !important;
}

:deep(.text-gray-700) {
  color: var(--iluria-color-text-primary) !important;
}

:deep(.text-gray-800) {
  color: var(--iluria-color-text-primary) !important;
}

:deep(.text-gray-900) {
  color: var(--iluria-color-text-primary) !important;
}

:deep(.bg-white) {
  background-color: var(--iluria-color-container-bg) !important;
}

:deep(.bg-gray-50) {
  background-color: var(--iluria-color-hover) !important;
}

:deep(.bg-gray-100) {
  background-color: var(--iluria-color-surface) !important;
}

:deep(.border-gray-300) {
  border-color: var(--iluria-color-border) !important;
}

:deep(.border-gray-400) {
  border-color: var(--iluria-color-border-hover) !important;
}

/* Hover states específicos */
:deep(.hover\\:text-gray-900:hover) {
  color: var(--iluria-color-primary) !important;
}

:deep(.hover\\:bg-gray-50:hover) {
  background-color: var(--iluria-color-hover) !important;
}

:deep(.hover\\:bg-gray-100:hover) {
  background-color: var(--iluria-color-hover) !important;
}

:deep(.hover\\:border-gray-400:hover) {
  border-color: var(--iluria-color-border-hover) !important;
}

/* Responsive (Same pattern as NewProduct.vue) */
@media (max-width: 768px) {
  .product-editor-container {
    padding: 16px;
  }
  
  .editor-header {
    flex-direction: row;
    align-items: flex-start;
    justify-content: space-between;
    gap: 12px;
  }
  
  .header-content {
    flex: 1;
    min-width: 0;
  }
  
  .header-content h1.page-title {
    font-size: 24px;
  }
  
  .header-content .page-subtitle {
    font-size: 15px;
  }
  
  .header-actions {
    flex-direction: row;
    gap: 8px;
    flex-shrink: 0;
    align-items: flex-start;
  }
}

@media (max-width: 480px) {
  .header-content h1.page-title {
    font-size: 22px;
  }
  
  .header-content .page-subtitle {
    font-size: 14px;
  }
  
  .header-actions {
    gap: 6px;
  }
}
</style>
