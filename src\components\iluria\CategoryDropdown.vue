<template>
  <div class="relative">
    <label v-if="label" class="block text-sm font-medium text-[var(--color-text)] mb-2">
      {{ label }}
      <span v-if="required" class="text-[var(--color-danger)] ml-1">*</span>
    </label>
    
    <!-- Dropdown Button -->
    <div class="relative">
      <select
        class="hidden"
        :value="modelValue"
        @change="emit('update:modelValue', $event.target.value)"
      >
        <option value="">{{ placeholder }}</option>
        <option 
          v-for="option in flattenedOptions" 
          :key="option.id" 
          :value="option.id"
        >
          {{ option.displayName }}
        </option>
      </select>
      
      <button
        type="button"
        @click="toggleDropdown"
        :class="[
          'relative w-full pl-3 pr-10 py-3 text-left cursor-default focus:outline-none focus:ring-1 focus:ring-[var(--color-primary)] sm:text-sm transition-all duration-200',
          isOpen ? 'rounded-t-md' : 'rounded-md',
          disabled ? 'cursor-not-allowed opacity-50' : ''
        ]"
        :style="{
          background: disabled ? 'var(--iluria-color-surface)' : 'var(--iluria-color-container-bg)',
          border: '1px solid var(--iluria-color-border)',
          boxShadow: 'var(--iluria-shadow-sm)',
          height: '42px',
          minHeight: '42px',
          display: 'flex',
          alignItems: 'center'
        }"
        :disabled="disabled"
        :aria-expanded="isOpen"
      >
        <span class="block truncate text-[var(--color-text)]">
          {{ selectedCategory ? selectedCategory.displayName : placeholder }}
        </span>
        <span class="absolute inset-y-0 right-0 flex items-center pr-2 pointer-events-none">
          <svg 
            :class="[
              'h-5 w-5 text-[var(--color-text-light)] transition-transform duration-200',
              isOpen ? 'transform rotate-180' : ''
            ]" 
            fill="none" 
            stroke="currentColor" 
            viewBox="0 0 24 24"
          >
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
          </svg>
        </span>
      </button>

      <!-- Dropdown Panel -->
      <transition
        enter-active-class="transition ease-out duration-100"
        enter-from-class="transform opacity-0 scale-95"
        enter-to-class="transform opacity-100 scale-100"
        leave-active-class="transition ease-in duration-75"
        leave-from-class="transform opacity-100 scale-100"
        leave-to-class="transform opacity-0 scale-95"
      >
        <div 
          v-if="isOpen"
          ref="dropdownPanel"
          class="absolute z-50 w-full max-h-80 rounded-b-md text-base overflow-auto focus:outline-none sm:text-sm py-1"
          :style="{
            ...dropdownStyle,
            background: 'var(--iluria-color-container-bg)',
            border: '1px solid var(--iluria-color-border)',
            borderTop: 'none',
            boxShadow: 'var(--iluria-shadow-lg)'
          }"
        >
          <!-- Search Input -->
          <div class="sticky top-0 z-10 p-2" style="background: var(--iluria-color-container-bg); border-bottom: 1px solid rgba(var(--iluria-color-border-rgb), 0.15); margin: 0 8px;">
            <div class="relative">
              <input
                ref="searchInput"
                type="text"
                v-model="searchQuery"
                :placeholder="searchPlaceholder"
                class="w-full pl-8 pr-4 py-2 text-sm rounded-md focus:outline-none focus:ring-1 focus:ring-[var(--color-primary)] text-[var(--color-text)] transition-all duration-200"
                style="background: var(--iluria-color-container-bg); border: 1px solid var(--iluria-color-border); height: 36px; min-height: 36px;"
                @click.stop
              />
              <svg class="w-4 h-4 text-[var(--color-text-light)] absolute left-2.5 top-2.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"/>
              </svg>
            </div>
          </div>

          <!-- Categories List -->
          <div class="py-1">
            <div v-if="filteredCategories.length === 0" class="px-4 py-3 text-center text-[var(--color-text-light)] text-sm">
              {{ searchQuery ? 'Nenhuma categoria encontrada' : 'Nenhuma categoria disponível' }}
            </div>
            
            <!-- Opção "Todas as categorias" -->
            <div 
              v-if="showAllOption && !searchQuery"
              @click="handleCategorySelect(allOptionValue)"
              :class="[
                'flex items-center px-4 py-2 text-sm cursor-pointer hover:bg-[var(--color-background-alt)] transition-colors',
                modelValue === allOptionValue ? 'bg-[var(--color-primary)]/10 text-[var(--color-primary)] font-medium' : 'text-[var(--color-text)]'
              ]"
            >
              <div class="mr-3 flex-shrink-0">
                <svg class="w-4 h-4 text-[var(--color-text-light)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"/>
                </svg>
              </div>
              <span class="flex-1 truncate">{{ allOptionText }}</span>
            </div>
            
            <!-- Separador se houver opção "Todas" -->
            <div v-if="showAllOption && !searchQuery && filteredCategories.length > 0" class="border-t my-1" style="border-color: var(--iluria-color-secondary); margin-left: 12px; margin-right: 12px;"></div>
            
            <CategoryItem
              v-for="category in filteredCategories"
              :key="category.id"
              :category="category"
              :selectedId="modelValue"
              :searchQuery="searchQuery"
              :expandedCategories="expandedCategories"
              @select="handleCategorySelect"
              @toggle="handleCategoryToggle"
            />
          </div>
        </div>
      </transition>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, watch, onMounted, onUnmounted, nextTick } from 'vue';
import CategoryItem from './CategoryItem.vue';

// Props
const props = defineProps({
  modelValue: {
    type: [String, Number],
    default: null
  },
  categories: {
    type: Array,
    default: () => []
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: 'Selecione uma categoria'
  },
  searchPlaceholder: {
    type: String,
    default: 'Pesquisar categorias...'
  },
  required: {
    type: Boolean,
    default: false
  },
  disabled: {
    type: Boolean,
    default: false
  },
  showAllOption: {
    type: Boolean,
    default: false
  },
  allOptionText: {
    type: String,
    default: 'Todas as categorias'
  },
  allOptionValue: {
    type: [String, Number],
    default: null
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Refs
const searchInput = ref(null);
const dropdownPanel = ref(null);
const dropdownButton = ref(null);

// State
const isOpen = ref(false);
const searchQuery = ref('');
const expandedCategories = ref(new Set());

const dropdownStyle = ref({ top: '100%' });

// Computed
const selectedCategory = computed(() => {
  if (props.modelValue === props.allOptionValue) {
    return props.showAllOption ? { displayName: props.allOptionText } : null;
  }
  if (!props.modelValue) {
    return null;
  }
  return findCategoryById(props.categories, props.modelValue);
});

const filteredCategories = computed(() => {
  if (!searchQuery.value) {
    return props.categories.map(category => enhanceCategory(category));
  }
  
  return filterCategoriesBySearch(props.categories, searchQuery.value.toLowerCase());
});

const flattenedOptions = computed(() => {
  const options = [];
  
  // Adicionar opção "Todas as categorias" se habilitada
  if (props.showAllOption) {
    options.push({
      id: props.allOptionValue,
      displayName: props.allOptionText
    });
  }
  
  const flatten = (categories, level = 0) => {
    categories.forEach(category => {
      const indent = '  '.repeat(level);
      options.push({
        id: category.id,
        displayName: `${indent}${category.name || category.title}`
      });
      
      if (category.children && category.children.length > 0) {
        flatten(category.children, level + 1);
      }
    });
  };
  
  flatten(props.categories);
  return options;
});

// Methods
const toggleDropdown = async () => {
  if (!props.disabled) {
    isOpen.value = !isOpen.value;
    if (isOpen.value) {
      searchQuery.value = '';
      await nextTick();
      searchInput.value?.focus();
      handleResize();
    }
  }
};

const handleCategorySelect = (categoryId) => {
  emit('update:modelValue', categoryId);
  isOpen.value = false;
  searchQuery.value = '';
};

const handleCategoryToggle = (categoryId) => {
  if (expandedCategories.value.has(categoryId)) {
    expandedCategories.value.delete(categoryId);
  } else {
    expandedCategories.value.add(categoryId);
  }
};

const enhanceCategory = (category) => {
  return {
    ...category,
    expanded: expandedCategories.value.has(category.id),
    children: category.children ? category.children.map(child => enhanceCategory(child)) : []
  };
};

const handleResize = () => {
  if (!isOpen.value || !dropdownPanel.value) return;

  const buttonEl = dropdownPanel.value.parentElement.querySelector('button');
  if (!buttonEl) return;

  const rect = buttonEl.getBoundingClientRect();
  const spaceBelow = window.innerHeight - rect.bottom;
  const spaceAbove = rect.top;

  const panelHeight = Math.min(dropdownPanel.value.scrollHeight, 320);

  if (spaceBelow < panelHeight && spaceAbove > spaceBelow) {
    // Abre para cima
    dropdownStyle.value = { bottom: '100%' };
  } else {
    // Abre para baixo
    dropdownStyle.value = { top: '100%' };
  }
};

const findCategoryById = (categories, id) => {
  for (const category of categories) {
    if (category.id === id) {
      return {
        ...category,
        displayName: category.name || category.title
      };
    }
    if (category.children) {
      const found = findCategoryById(category.children, id);
      if (found) return found;
    }
  }
  return null;
};

const filterCategoriesBySearch = (categories, query) => {
  const filtered = [];
  
  for (const category of categories) {
    const categoryName = (category.name || category.title || '').toLowerCase();
    const matchesSearch = categoryName.includes(query);
    
    let filteredChildren = [];
    if (category.children) {
      filteredChildren = filterCategoriesBySearch(category.children, query);
    }
    
    if (matchesSearch || filteredChildren.length > 0) {
      filtered.push({
        ...category,
        children: filteredChildren,
        expanded: filteredChildren.length > 0 || expandedCategories.value.has(category.id)
      });
    }
  }
  
  return filtered;
};

const handleClickOutside = (event) => {
  const dropdown = event.target.closest('.relative');
  if (!dropdown || !dropdown.contains(event.target)) {
    isOpen.value = false;
  }
};

// Lifecycle
onMounted(() => {
  document.addEventListener('click', handleClickOutside);
  window.addEventListener('resize', handleResize);
});

onUnmounted(() => {
  document.removeEventListener('click', handleClickOutside);
  window.removeEventListener('resize', handleResize);
});

// Watch for external changes
watch(() => props.modelValue, (newValue) => {
  if (newValue && isOpen.value) {
    isOpen.value = false;
  }
});
</script>

<style scoped>
/* Ensure proper z-index for dropdown */
.relative {
  position: relative;
}

/* Custom scrollbar for dropdown */
.overflow-auto::-webkit-scrollbar {
  width: 6px;
}

.overflow-auto::-webkit-scrollbar-track {
  background: var(--color-background-alt);
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb {
  background: var(--color-text-light);
  border-radius: 3px;
}

.overflow-auto::-webkit-scrollbar-thumb:hover {
  background: var(--color-text);
}
</style> 
