<template>
    <div class="list-card">
      <div class="list-icon-container">
        <div :class="bgColor + ' list-icon-background'">
          <HugeiconsIcon :icon="icon" :size="20" class="list-icon" />
        </div>
      </div>
      
      <div class="list-content">
        <IluriaTitle class="list-title">{{ title }}</IluriaTitle>
        <IluriaText class="list-count">{{ count }}</IluriaText>
      </div>
    </div>
</template>

<script setup>
import IluriaTitle from "@/components/iluria/IluriaTitle.vue";
import IluriaText from "@/components/iluria/IluriaText.vue";
import {HugeiconsIcon} from '@hugeicons/vue';

defineProps({
  title: String,
  count: Number,
  bgColor: String,
  icon: Object
});
</script>

<style scoped>
.list-card {
  display: flex;
  align-items: center;
  padding: 24px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-height: 80px;
}

.list-card:hover {
  transform: translateY(-1px);
  border-color: var(--iluria-color-border-hover);
  box-shadow: var(--iluria-shadow-md);
}

.list-icon-container {
  display: flex;
  align-items: center;
  justify-content: center;
  margin-right: 16px;
  flex-shrink: 0;
}

.list-icon-background {
  width: 40px;
  height: 40px;
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  opacity: 0.9;
  transition: transform 0.2s ease;
}

.list-card:hover .list-icon-background {
  transform: scale(1.05);
  opacity: 1;
}

.list-icon {
  color: white;
  filter: drop-shadow(0 1px 2px rgba(0, 0, 0, 0.1));
}

.list-content {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.list-title {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  font-weight: 500;
  margin: 0;
}

.list-count {
  font-size: 20px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

/* Tailwind classes mapping for background colors */
.bg-green-400 {
  background-color: #4ade80;
}

.bg-yellow-400 {
  background-color: #facc15;
}

.bg-blue-400 {
  background-color: #60a5fa;
}

.bg-purple-400 {
  background-color: #c084fc;
}

.bg-red-400 {
  background-color: #f87171;
}

/* Responsive */
@media (max-width: 768px) {
  .list-card {
    padding: 20px;
    min-height: 72px;
  }
  
  .list-icon-background {
    width: 36px;
    height: 36px;
  }
  
  .list-icon-container {
    margin-right: 14px;
  }
  
  .list-title {
    font-size: 13px;
  }
  
  .list-count {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .list-card {
    padding: 16px;
    min-height: 64px;
  }
  
  .list-icon-background {
    width: 32px;
    height: 32px;
  }
  
  .list-icon-container {
    margin-right: 12px;
  }
  
  .list-count {
    font-size: 16px;
  }
}
</style>
