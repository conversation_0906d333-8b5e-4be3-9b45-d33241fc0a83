<template>
    <div>
      <IluriaSelect
        id="customer-select"
        name="customer-select"
        v-model="selectedClient"
        :options="filteredClients"
        :filter="true"
        optionLabel="label"
        :delay="0"
        :minLength="2"
        :placeholder="t('customerInfo.selectClient')"
        :loading="loading"
        @update:modelValue="emitSelectedClient"
        @filter="searchClient"
        @change="emitSelectedClient"
        @click="logOptions"
        class="w-full"
        :optionValue="'id'"
        :optionLabel="'label'"
      />
    </div>
  </template>
  
  <script setup>
  import { ref, onMounted, watch } from 'vue'
  import { useI18n } from 'vue-i18n'
  import CustomerService from '@/services/customer.service'
  import IluriaSelect from '../iluria/form/IluriaSelect.vue'
  
  const { t } = useI18n()
  
  const props = defineProps({
    modelValue: {
      type: [Object, String, Number, null],
      default: null
    }
  })
  
const selectedClient = ref(props.modelValue ? (props.modelValue.id || props.modelValue) : null)
  const filteredClients = ref([])
  const loading = ref(false)
  const filters = ref({ filter: '' }) 
  const currentPage = ref(0) 
  const totalPages = ref(0) 
  
  const emit = defineEmits(['select', 'update:modelValue'])
  
  watch(() => props.modelValue, (newValue) => {
    if (newValue) {
      selectedClient.value = newValue.id || newValue
    } else {
      selectedClient.value = null
    }
  }, { immediate: true })
  
  const emitSelectedClient = () => {
    if (selectedClient.value) {
      const cliente = filteredClients.value.find(c => c.id === selectedClient.value);
      
      if (cliente) {
        const clienteData = {
          id: cliente.id,
          name: cliente.name,
          email: cliente.email,
          phone: cliente.phone || '',
          label: cliente.label
        };
        
        emit('select', clienteData)
        emit('update:modelValue', clienteData)
      }
    } else {
      emit('update:modelValue', null)
    }
  }

  
  const logOptions = () => {
    if (filteredClients.value.length === 0) {
      loadCustomers()
    }
  }

  const loadCustomers = async (ignorePagination = false) => {
    loading.value = true
    try {
      const page = ignorePagination ? 0 : currentPage.value
      const size = ignorePagination ? 1000 : 5

      const response = await CustomerService.getCustomers(filters.value.filter, page, size)
      
      filteredClients.value = response.content.map(customer => ({
        id: customer.id,
        name: customer.name,
        email: customer.email,
        phone: customer.phone,
        label: `${customer.name} (${customer.email})`
      }))
      totalPages.value = response.totalPages
    } catch (error) {
      console.error('Error loading customers:', error)
    } finally {
      loading.value = false
    }
  }

  
  let searchTimeout = null
  const searchClient = (event) => {
    clearTimeout(searchTimeout)
    filters.value.filter = event.filter || ''
    searchTimeout = setTimeout(() => {
      loadCustomers(true) 
    }, 400)
  }

  
  onMounted(() => {
    loadCustomers()
  })
  </script>
  
