<template>
  <!-- Formulário de edição de endereço -->
  <div class="mt-4">
      <div class="flex justify-between items-center mb-4">
        <h4 class="font-medium">{{ t('addressList.editAddress') }}</h4>
      </div>
      <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <IluriaInputText
        id="shippingStreet"
        name="shippingStreet"
        v-model="localAddress.street"
        type="text"
        :label="t('addressList.street')"
        :placeholder="t('addressList.street')"
  
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingNumber"
        name="shippingNumber"
        v-model="localAddress.number"
        type="text"
        :label="t('addressList.number')"
        :placeholder="t('addressList.number')"
  
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingComplement"
        name="shippingComplement"
        v-model="localAddress.complement"
        type="text"
        :label="t('addressList.complement')"
        :placeholder="t('addressList.complement')"
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingDistrict"
        name="shippingDistrict"
        v-model="localAddress.district"
        type="text"
        :label="t('addressList.neighborhood')"
        :placeholder="t('addressList.neighborhood')"
  
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingCity"
        name="shippingCity"
        v-model="localAddress.city"
        type="text"
        :label="t('addressList.city')"
        :placeholder="t('addressList.city')"
  
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingState"
        name="shippingState"
        v-model="localAddress.state"
        type="text"
        :label="t('addressList.state')"
        :placeholder="t('addressList.state')"
  
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingZipCode"
        name="shippingZipCode"
        v-model="localAddress.zipCode"
        type="text"
        :label="t('addressList.zip')"
        :placeholder="t('addressList.zip')"
  
        @input="emitChange"
      />
      <IluriaInputText
        id="shippingCountry"
        name="shippingCountry"
        v-model="localAddress.country"
        type="text"
        :label="t('addressList.country')"
        :placeholder="t('addressList.country')"
        @input="emitChange"
      />
      </div>
    </div>
  </template>
  
  <script setup>
  import { ref,watch } from 'vue'
  import { useI18n } from 'vue-i18n'
  import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
  const { t } = useI18n()
  const props = defineProps({
      address: Object
  })
  const emit = defineEmits(['update:address', 'cancel'])
  const localAddress = ref({...props.address})
  watch(() => props.address, () => {
      localAddress.value = {...props.address}
  })
  function emitChange() {
      emit('update:address',{...localAddress.value})
  }
  </script>
