<template>
  <div class="blog-category-form-container">
    <!-- Loading State -->
    <div v-if="loading" class="loading-container">
      <div class="spinner"></div>
      <p>{{ t('blogCategory.loadingTitle') }}</p>
    </div>

    <!-- Main Content -->
    <div v-else>
      <!-- Header with actions -->
      <IluriaHeader
        :title="isEditing ? t('blogCategory.editFormTitle') : t('blogCategory.formTitle')"
        :subtitle="isEditing ? t('blogCategory.editFormSubtitle') : t('blogCategory.formSubtitle')"
        :showCancel="true"
        :cancelText="t('blogCategory.cancel')"
        :showSave="true"
        :saveText="isEditing ? t('blogCategory.update') : t('blogCategory.save')"
        @cancel-click="goBackToList"
        @save-click="saveCategory"
      />

      <!-- Form Content -->
      <div class="form-content">
        <div class="form-sections">
          <!-- <PERSON><PERSON> Básicos -->
          <ViewContainer :title="t('blogCategory.basicDataTitle')" :subtitle="t('blogCategory.basicDataSubtitle')"
            :icon="BookmarkAdd01Icon" iconColor="blue">
            <div class="space-y-6">
              <!-- Nome da Categoria -->
              <div class="space-y-2">
                <IluriaInputText
                  id="name"
                  v-model="form.name"
                  :label="t('blogCategory.nameLabel')"
                  :placeholder="t('blogCategory.namePlaceholder')"
                  class="w-full"
                  @input="onNameChange"
                  required
                />
              </div>

              <!-- Slug -->
              <div class="space-y-2">
                <IluriaInputText
                  id="slug"
                  v-model="form.slug"
                  :label="t('blogCategory.slugLabel')"
                  :placeholder="t('blogCategory.slugPlaceholder')"
                  prefix="/"
                  class="w-full"
                  required
                />
              </div>

              <!-- Descrição -->
              <div class="space-y-2">
                <IluriaTextarea
                  id="description"
                  v-model="form.description"
                  :label="t('blogCategory.descriptionLabel')"
                  :placeholder="t('blogCategory.descriptionPlaceholder')"
                  class="w-full"
                  :rows="3"
                />
              </div>
            </div>
          </ViewContainer>

          <!-- Upload de Imagem -->
          <ViewContainer :title="t('blogCategory.imageLabel')" :subtitle="t('blogCategory.imageHelp')"
            :icon="ImageUploadIcon" iconColor="purple">
            <div class="space-y-4">
              <IluriaSimpleImageUpload
                id="categoryImage"
                v-model="form.imageUrl"
                :label="t('blogCategory.imageLabel')"
                :add-button-text="t('blogCategory.uploadImage')"
                :change-button-text="t('blogCategory.changeImage')"
                :remove-button-text="t('blogCategory.removeImage')"
                :format-hint="t('blogCategory.imageFormats')"
                accept="image/*"
                :prevent-cache="true"
                @change="onImageChange"
                class="w-full"
              />
            </div>
          </ViewContainer>

          <!-- SEO Configuration -->
          <ViewContainer :title="t('blogCategory.seoTitle')" :subtitle="t('blogCategory.seoSubtitle')"
            :icon="SearchIcon" iconColor="green">
            <div class="space-y-6">
              <!-- Meta Title -->
              <div class="space-y-2">
                <IluriaInputText 
                  id="metaTitle" 
                  v-model="form.metaTitle"
                  :label="t('blogCategory.metaTitleLabel')" 
                  :placeholder="t('blogCategory.metaTitlePlaceholder')" 
                  class="w-full"
                  :maxlength="60"
                />
                <div class="flex justify-between items-center">
                  <span class="character-counter" :class="{ 'text-red-500': metaTitleLength > 60 }">
                    {{ metaTitleLength }}/60 {{ t('blogCategory.metaTitleCounter') }}
                  </span>
                </div>
              </div>

              <!-- Meta Description -->
              <div class="space-y-2">
                <IluriaTextarea 
                  id="metaDescription" 
                  v-model="form.metaDescription"
                  :label="t('blogCategory.metaDescriptionLabel')" 
                  :placeholder="t('blogCategory.metaDescriptionPlaceholder')" 
                  class="w-full"
                  :rows="3"
                  :maxlength="160"
                />
                <div class="flex justify-between items-center">
                  <span class="character-counter" :class="{ 'text-red-500': metaDescriptionLength > 160 }">
                    {{ metaDescriptionLength }}/160 {{ t('blogCategory.metaDescriptionCounter') }}
                  </span>
                </div>
              </div>
            </div>
          </ViewContainer>

          <!-- Google Preview -->
          <ViewContainer :title="t('blogCategory.googlePreviewTitle')" :subtitle="t('blogCategory.googlePreviewSubtitle')"
            :icon="SearchIcon" iconColor="blue">
            <div class="google-preview-container">
              <div class="google-result">
                <div class="google-title">
                  {{ form.metaTitle || form.name || 'Nome da Categoria - Seu Site' }}
                </div>
                <div class="google-url">
                  {{ t('blogCategory.googlePreviewUrl').replace('slug-da-categoria', form.slug || 'categoria') }}
                </div>
                <div class="google-description">
                  {{ form.metaDescription || form.description || t('blogCategory.googlePreviewDescription') }}
                </div>
              </div>
            </div>
          </ViewContainer>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, computed, watch } from 'vue'
import { useRouter, useRoute } from 'vue-router'
import { useToast } from 'primevue/usetoast'
import { useI18n } from 'vue-i18n'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaTextarea from '@/components/iluria/form/IluriaTextarea.vue'
import IluriaSimpleImageUpload from '@/components/iluria/form/IluriaSimpleImageUpload.vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import blogCategoryService from '@/services/blogCategory.service'
import { useTheme } from '@/composables/useTheme'
import {
  FloppyDiskIcon,
  BookmarkAdd01Icon,
  SearchIcon,
  ImageUploadIcon
} from '@hugeicons-pro/core-stroke-rounded'

const router = useRouter()
const route = useRoute()
const toast = useToast()
const { t } = useI18n()

// Initialize theme system
const { initTheme } = useTheme()
initTheme()

const defaultForm = {
  id: null,
  name: '',
  slug: '',
  description: '',
  imageUrl: '',
  metaTitle: '',
  metaDescription: '',
  newImageFile: null
}

const form = reactive({ ...defaultForm })
const loading = ref(false)
const saving = ref(false)
const isEditing = ref(false)

const metaTitleLength = computed(() => form.metaTitle?.length || 0)
const metaDescriptionLength = computed(() => form.metaDescription?.length || 0)

const onNameChange = () => {
  // Auto-generate slug if it's empty or hasn't been manually edited
  if (!form.slug || !isEditing.value) {
    form.slug = blogCategoryService.generateSlugFromName(form.name)
  }
}

const onImageChange = async (file) => {
  if (!file) {
    // If file is null, user removed the image
    form.newImageFile = null
    return;
  }
  
  if (file instanceof File) {
    // User selected a new file
    form.newImageFile = file
  } else {
    // This is a string URL (existing image), no new file to upload
    form.newImageFile = null
  }
}

const loadCategory = async (categoryId) => {
  try {
    loading.value = true
    const category = await blogCategoryService.getBlogCategory(categoryId)
    if (!category) throw new Error('Categoria não encontrada')

    Object.assign(form, {
      ...defaultForm,
      ...category
    })

    // The imageUrl comes directly from the backend now
    // No need to construct it in the frontend

    isEditing.value = true
  } catch (error) {
    console.error('Erro ao carregar categoria:', error)
    toast.add({ 
      severity: 'error', 
      summary: t('blogCategory.errorTitle'), 
      detail: t('blogCategory.loadError'), 
      life: 3000 
    })
    router.push('/blog/categories')
  } finally {
    loading.value = false
  }
}

const validateForm = () => {
  if (!form.name?.trim()) {
    toast.add({
      severity: 'error',
      summary: t('blogCategory.errorTitle'),
      detail: `${t('blogCategory.nameLabel')} ${t('validation.isRequired')}`,
      life: 3000
    })
    return false
  }

  if (!form.slug?.trim()) {
    toast.add({
      severity: 'error',
      summary: t('blogCategory.errorTitle'),
      detail: `${t('blogCategory.slugLabel')} ${t('validation.isRequired')}`,
      life: 3000
    })
    return false
  }

  if (metaTitleLength.value > 60) {
    toast.add({
      severity: 'error',
      summary: t('blogCategory.errorTitle'),
      detail: 'Meta título deve ter no máximo 60 caracteres',
      life: 3000
    })
    return false
  }

  if (metaDescriptionLength.value > 160) {
    toast.add({
      severity: 'error',
      summary: t('blogCategory.errorTitle'),
      detail: 'Meta descrição deve ter no máximo 160 caracteres',
      life: 3000
    })
    return false
  }

  return true
}

const saveCategory = async () => {
  if (!validateForm()) return

  try {
    saving.value = true

    const categoryData = { 
      ...form,
      newImageFile: undefined, // Remove from category data
      // Only include imageUrl if it's not a File and there's no new file to upload
      imageUrl: form.newImageFile ? undefined : form.imageUrl
    }

    let savedCategory;

    if (isEditing.value) {
      savedCategory = await blogCategoryService.updateBlogCategory(form.id, categoryData)
      toast.add({ 
        severity: 'success', 
        summary: t('blogCategory.successTitle'), 
        detail: t('blogCategory.updateSuccess'), 
        life: 3000 
      })
    } else {
      savedCategory = await blogCategoryService.createBlogCategory(categoryData)
      toast.add({ 
        severity: 'success', 
        summary: t('blogCategory.successTitle'), 
        detail: t('blogCategory.createSuccess'), 
        life: 3000 
      })
    }

    // Upload image if there's a new file
    if (form.newImageFile && savedCategory) {
      try {
        const categoryId = savedCategory.id || form.id;
        const uploadResponse = await blogCategoryService.uploadImage(categoryId, form.newImageFile);
        
        // Update form with the returned image URL
        if (uploadResponse && uploadResponse.imageUrl) {
          form.imageUrl = uploadResponse.imageUrl;
        }
        
        toast.add({ 
          severity: 'success', 
          summary: t('blogCategory.successTitle'), 
          detail: t('blogCategory.imageUploadSuccess') || 'Imagem carregada com sucesso', 
          life: 3000 
        })
      } catch (imageError) {
        console.error('Erro ao fazer upload da imagem:', imageError);
        toast.add({ 
          severity: 'warn', 
          summary: t('blogCategory.warningTitle') || 'Aviso', 
          detail: t('blogCategory.imageUploadError') || 'Categoria salva, mas erro ao fazer upload da imagem', 
          life: 3000 
        })
      }
    }

    router.push('/blog/categories')
  } catch (error) {
    console.error('Erro ao salvar categoria:', error)
    const errorMessage = isEditing.value ? t('blogCategory.updateError') : t('blogCategory.createError')
    toast.add({ 
      severity: 'error', 
      summary: t('blogCategory.errorTitle'), 
      detail: errorMessage, 
      life: 3000 
    })
  } finally {
    saving.value = false
  }
}

const goBackToList = () => {
  router.push('/blog/categories')
}

onMounted(() => {
  const categoryId = route.params.id
  if (categoryId && categoryId !== 'new') {
    isEditing.value = true
    loadCategory(categoryId)
  } else {
    loading.value = false
  }
})
</script>

<style scoped>
.blog-category-form-container {
  max-width: 900px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  color: var(--iluria-color-text);
  min-height: 100vh;
}

/* Loading */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 80px 24px;
  color: var(--iluria-color-text-muted);
}

.spinner {
  width: 48px;
  height: 48px;
  border: 4px solid #f3f4f6;
  border-top: 4px solid #3b82f6;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: 16px;
}



/* Main Content */
.form-content {
  display: flex;
  flex-direction: column;
}

.form-sections {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Field Labels */
.field-label {
  display: block;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
}

.field-help {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.character-counter {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

/* Google Preview */
.google-preview-container {
  padding: 20px;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-sm, 0 2px 4px rgba(0, 0, 0, 0.1));
}

.google-result {
  font-family: Arial, sans-serif;
  max-width: 600px;
}

.google-title {
  font-size: 20px;
  line-height: 1.3;
  color: #1a0dab;
  text-decoration: none;
  margin-bottom: 6px;
  cursor: pointer;
  font-weight: 400;
}

.google-title:hover {
  text-decoration: underline;
}

.google-url {
  font-size: 14px;
  color: #006621;
  margin-bottom: 6px;
  word-break: break-all;
  line-height: 1.3;
}

.google-description {
  font-size: 14px;
  color: #4d5156;
  line-height: 1.4;
  max-height: none;
  overflow: visible;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Responsive */
@media (max-width: 768px) {
  .blog-category-form-container {
    padding: 16px;
    max-width: 100%;
  }

  .form-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 16px;
  }

  .header-content h1.page-title {
    font-size: 24px;
  }

  .header-content .page-subtitle {
    font-size: 14px;
  }

  .header-actions {
    width: 100%;
    justify-content: space-between;
  }

  .google-preview-container {
    padding: 16px;
  }

  .google-title {
    font-size: 18px;
  }
}

@media (max-width: 480px) {
  .blog-category-form-container {
    padding: 12px;
  }

  .header-actions {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
  }

  .header-content h1.page-title {
    font-size: 22px;
  }

  .google-preview-container {
    padding: 12px;
  }

  .google-title {
    font-size: 16px;
  }
}
</style> 