import AnimationUtils from './animationUtils.js'
import fs from 'fs'
import path from 'path'

// Função para testar o AnimationUtils
export function testAnimationUtils() {
  
  // Carrega o HTML de exemplo
  const exampleHtmlPath = path.join(process.cwd(), 'src/examples/html-with-animations.html')
  let htmlContent = ''
  
  try {
    htmlContent = fs.readFileSync(exampleHtmlPath, 'utf8')
  } catch (error) {
    console.error('❌ Erro ao carregar HTML de exemplo:', error.message)
    return
  }
  
  // Testa extração de animações
  const animations = AnimationUtils.extractAnimationsFromHTML(htmlContent)
  
  animations.forEach((animation, index) => {
  })
  
  // Testa geração de CSS
  const animationCSS = AnimationUtils.generateCompleteAnimationCSS(animations)
  
  // Testa geração de JavaScript
  const animationJS = AnimationUtils.generateAnimationJS()
  
  // Testa injeção completa
  const processedHtml = AnimationUtils.injectAnimationsIntoHTML(htmlContent)
  
  // Salva o resultado para inspeção
  const outputPath = path.join(process.cwd(), 'src/examples/html-with-animations-processed.html')
  try {
    fs.writeFileSync(outputPath, processedHtml)
  } catch (error) {
    console.error('❌ Erro ao salvar HTML processado:', error.message)
  }
  
  // Testa limpeza de animações antigas
  const cleanedHtml = AnimationUtils.cleanOldAnimations(processedHtml)
  const reprocessedHtml = AnimationUtils.injectAnimationsIntoHTML(cleanedHtml)
  
  
  return {
    animations,
    css: animationCSS,
    js: animationJS,
    processedHtml
  }
}

// Executa os testes se este arquivo for executado diretamente
if (typeof require !== 'undefined' && require.main === module) {
  testAnimationUtils()
} 