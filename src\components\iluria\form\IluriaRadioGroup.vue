<template>
  <div class="radio-group-container">
    <span v-if="label" class="radio-group-label">
      {{ label }}
    </span>
    <div class="radio-group" :class="{ 'horizontal': direction === 'horizontal' }">
      <label 
        v-for="option in options" 
        :key="getOptionValue(option)" 
        class="radio-option"
        :class="{ 
          'checked': model === getOptionValue(option),
          'disabled': disabled,
          'invalid': formContext?.invalid
        }"
      >
        <input 
          type="radio"
          :id="`${id}-${getOptionValue(option)}`"
          :name="name || id" 
          :value="getOptionValue(option)" 
          v-model="model" 
          :disabled="disabled"
          class="radio-input"
        />
        <div class="radio-indicator">
          <div class="radio-dot"></div>
        </div>
        <span class="radio-label">
          {{ getOptionLabel(option) }}
        </span>
      </label>
    </div>
    <Message v-if="formContext && formContext?.invalid" severity="error" size="small" variant="simple"
      class="error-message">
      {{ formContext?.error?.message }} 
    </Message>
  </div>
</template>

<script setup>
import { Message } from 'primevue';

const model = defineModel();

const props = defineProps({
  id: {
    type: String,
    required: true
  },
  name: {
    type: String,
    default: null
  },
  label: {
    type: String,
    required: true
  },
  inputClass: {
    type: String,
    default: ''
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  formContext: {
    type: Object,
    default: null
  },
  options: {
    type: Array,
    default: () => []
  },
  optionLabel: {
    type: String,
    default: 'label'
  },
  optionValue: {
    type: String,
    default: 'value'
  },
  direction: {
    type: String,
    default: 'vertical', // 'vertical' ou 'horizontal'
    validator: (value) => ['vertical', 'horizontal'].includes(value)
  }
});

// Funções para obter o valor e o rótulo de cada opção
function getOptionValue(option) {
  if (typeof option === 'object' && option !== null) {
    return option[props.optionValue];
  }
  return option;
}

function getOptionLabel(option) {
  if (typeof option === 'object' && option !== null) {
    return option[props.optionLabel];
  }
  return option;
}
</script>

<style scoped>
.radio-group-container {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-group-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
}

.radio-group {
  display: flex;
  flex-direction: column;
  gap: 8px;
}

.radio-group.horizontal {
  flex-direction: row;
  flex-wrap: wrap;
  gap: 12px;
}

.radio-option {
  display: flex;
  align-items: center;
  gap: 12px;
  padding: 8px 12px;
  border-radius: 8px;
  background: transparent;
  cursor: pointer;
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  user-select: none;
  width: fit-content;
}

.radio-option:hover:not(.disabled) {
  background: var(--iluria-color-hover);
  transform: translateY(-1px);
}

.radio-option.checked {
  background: var(--iluria-color-primary-bg);
}

.radio-option.disabled {
  opacity: 0.5;
  cursor: not-allowed;
  background: transparent;
}

.radio-option.invalid {
  /* Removed border-color */
}

.radio-option.invalid.checked {
  background: var(--iluria-color-error-bg);
}

.radio-input {
  position: absolute;
  opacity: 0;
  width: 0;
  height: 0;
  pointer-events: none;
}

.radio-indicator {
  position: relative;
  width: 20px;
  height: 20px;
  border: 2px solid var(--iluria-color-border);
  border-radius: 50%;
  background: var(--iluria-color-input-bg);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  flex-shrink: 0;
}

.radio-option:hover:not(.disabled) .radio-indicator {
  border-color: var(--iluria-color-primary);
  transform: scale(1.1);
}

.radio-option.checked .radio-indicator {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary);
}

.radio-option.invalid .radio-indicator {
  border-color: var(--iluria-color-error);
}

.radio-option.invalid.checked .radio-indicator {
  border-color: var(--iluria-color-error);
  background: var(--iluria-color-error);
}

.radio-dot {
  position: absolute;
  top: 50%;
  left: 50%;
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--iluria-color-input-bg);
  transform: translate(-50%, -50%) scale(0);
  transition: transform 0.2s cubic-bezier(0.68, -0.55, 0.265, 1.55);
}

.radio-option.checked .radio-dot {
  transform: translate(-50%, -50%) scale(1);
}

.radio-label {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  line-height: 1.4;
  flex: 1;
}

.radio-option.disabled .radio-label {
  color: var(--iluria-color-text-disabled);
}

.error-message {
  margin-top: 4px;
}

/* Focus state */
.radio-input:focus + .radio-indicator {
  outline: 2px solid var(--iluria-color-focus-ring);
  outline-offset: 2px;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .radio-group.horizontal {
    flex-direction: column;
  }
  
  .radio-option {
    padding: 6px 10px;
  }
  
  .radio-indicator {
    width: 18px;
    height: 18px;
  }
  
  .radio-dot {
    width: 6px;
    height: 6px;
  }
}

/* Dark theme adjustments */
.theme-dark .radio-option {
  background: transparent;
}

.theme-dark .radio-option:hover:not(.disabled) {
  background: var(--iluria-color-hover);
}

.theme-dark .radio-option.checked {
  background: var(--iluria-color-primary-bg);
}
</style>
