<template>
    <div class="domain-validate-container">
        <IluriaHeader
            :title="t('domainUrl.validation')"
            :subtitle="t('domainUrl.validateSubtitle')"
            :customButtons="[
                {
                    text: t('domainUrl.back'),
                    color: 'secondary',
                    variant: 'outline',
                    onClick: () => router.push('/settings/domain-manager')
                },
                {
                    text: t('domainUrl.tryAgain'),
                    color: 'primary',
                    onClick: restartVerification
                }
            ]"
            @custom-click="handleCustomButtonClick"
        />

        <ViewContainer :title="t('domainUrl.validateTitle')" :icon="WebValidationIcon">
            <div class="page-content flex flex-col gap-4">
                <div class="flex gap-2 items-center">
                    <IluriaLabel> {{ t('domainUrl.validateNS') }} </IluriaLabel>
                    <IluriaLabel v-if="nsVerificationResult" class="status-badge status-success"> 
                        {{ t('domainUrl.success') }} 
                    </IluriaLabel>
                    <IluriaLabel v-else-if="nsVerificationDone" class="status-badge status-failed"> 
                        {{ t('domainUrl.failed') }} 
                    </IluriaLabel>
                    <IluriaLabel v-else class="status-badge status-pending">
                        {{ t('domainUrl.verifying') }}
                    </IluriaLabel>
                </div>
                <div class="flex gap-2 items-center">
                    <IluriaLabel> {{ t('domainUrl.validateCNAME') }} </IluriaLabel>
                    <IluriaLabel v-if="cnameVerificationResult" class="status-badge status-success"> 
                        {{ t('domainUrl.success') }}
                    </IluriaLabel>
                    <IluriaLabel v-else-if="cnameVerificationDone" class="status-badge status-failed"> 
                        {{ t('domainUrl.failed') }}
                    </IluriaLabel>
                    <IluriaLabel v-else class="status-badge status-pending">
                        {{ t('domainUrl.verifying') }}
                    </IluriaLabel>
                </div>
            </div>

        </ViewContainer>
    </div>
</template>

<script setup>
import { WebValidationIcon } from '@hugeicons-pro/core-stroke-rounded'
import { useRouter } from 'vue-router';
import { ref, onMounted, onUnmounted } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import IluriaHeader from '@/components/iluria/IluriaHeader.vue';
import domainsService from '@/services/domain.service'
import { useToast } from '@/services/toast.service'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'

const router = useRouter()
const { t } = useI18n();
const domainUrl = ref({});
const nsVerificationResult = ref(false);
const nsVerificationDone = ref(false);
const cnameVerificationResult = ref(false);
const cnameVerificationDone = ref(false);
const toast = useToast();
const isMobile = ref(window.innerWidth <= 768);

// Header button handler
const handleCustomButtonClick = (index, button) => {
  if (button.onClick) {
    button.onClick()
  }
}

function validateDomain() {

    nsVerificationDone.value = false;
    cnameVerificationDone.value = false;
    
    return domainsService.validateDomainUrl(domainUrl.value.id)
        .then((response) => {
            nsVerificationResult.value = response.result.includes("NsValid");
            nsVerificationDone.value = true;
            
            cnameVerificationResult.value = response.result.includes("CnameValid");
            cnameVerificationDone.value = true;
            
            if (response.domainUrl && response.domainUrl.status) {
                domainUrl.value.status = response.domainUrl.status;
                
                return domainsService.getDomainUrl(domainUrl.value.id);
            }
        })
        .then(updatedDomain => {
            if (updatedDomain) {
                domainUrl.value = updatedDomain;
            }
        })
        .catch((error) => {
            nsVerificationDone.value = true;
            nsVerificationResult.value = false;
            cnameVerificationDone.value = true;
            cnameVerificationResult.value = false;
        });
}

function restartVerification() {
    nsVerificationResult.value = false;
    nsVerificationDone.value = false;
    cnameVerificationResult.value = false;
    cnameVerificationDone.value = false;
    
    validateDomain();
}

function updateIsMobile() {
    isMobile.value = window.innerWidth <= 768;
}



function loadDomainUrl() {
    
    const id = router.currentRoute.value.params.id;

    if (id) {
        domainsService.getDomainUrl(id)
            .then((data) => {
                domainUrl.value = data;
                validateDomain();
            })
            .catch((error) => {
                console.error("Error loading domain url: ", error);
                toast.showError(t('domainUrl.loadError'));
            });
    }
}

onMounted(() => {
    loadDomainUrl();
    window.addEventListener('resize', updateIsMobile);
});

onUnmounted(() => {
    window.removeEventListener('resize', updateIsMobile);
});
</script>

<style scoped>

.domain-validate-container {
  padding: 24px;
  max-width: 1400px;
  margin: 0 auto;
  background: var(--iluria-color-bg);
  color: var(--iluria-color-text-secondary);
  min-height: 100vh;
  width: 80%;
}



.page-content {
  padding-top: 20px;
}

.page-content span{
    margin-bottom: 15px;
}

/* Status Badges */
.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

.status-failed {
  background: var(--iluria-color-error-bg);
  color: var(--iluria-color-error);
  border: 1px solid var(--iluria-color-error-border);
}

.status-success {
  background: var(--iluria-color-success-bg);
  color: var(--iluria-color-success);
  border: 1px solid var(--iluria-color-success-border);
}

.status-pending {
  background: var(--iluria-color-warning-bg);
  color: var(--iluria-color-warning);
  border: 1px solid var(--iluria-color-warning-border);
}

.header-mobile-actions {
    display: flex;
    flex-direction: column;
    align-items: flex-end;
    justify-content: flex-end;
    margin-top: 45px;
}
</style>