/**
 * Configuração do Componente de Footer
 * Criada para o novo sistema de configs
 */

export default {
  type: 'footer',
  name: '<PERSON><PERSON><PERSON>',
  description: 'Adiciona um rodapé personalizado com links e informações',
  category: 'layout',
  priority: 50,
  layoutTypes: ['static', 'product', 'collection', 'system'], // ✅ Disponível em todos os layouts
  icon: 'web_asset',
  
  html: `<footer 
    data-component="footer" 
    data-element-type="footer"
    data-config='{"logo":"Iluria","showNavigation":true,"navigationItems":[{"title":"Sobre","url":"#"},{"title":"Contato","url":"#"},{"title":"Política de Privacidade","url":"#"},{"title":"Termos de Uso","url":"#"}],"showContact":true,"contactInfo":{"email":"<EMAIL>","phone":"(11) 99999-9999","address":"Rua Exemplo, 123 - São Paulo, SP"},"showSocial":true,"socialLinks":{"facebook":"","instagram":"","twitter":"","youtube":""},"showCopyright":true,"copyrightText":"© 2024 Nome da Loja. Todos os direitos reservados.","backgroundColor":"#1a1a1a","textColor":"#ffffff","linkColor":"#60a5fa"}'
    class="iluria-footer"
    style="background: #1a1a1a; color: #ffffff; padding: 3rem 0 1rem 0; margin-top: auto;">
    
    <div class="footer-container" style="max-width: 1200px; margin: 0 auto; padding: 0 2rem;">
      <div class="footer-content" style="display: grid; grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); gap: 2rem; margin-bottom: 2rem;">
        
        <!-- Logo/Brand Section -->
        <div class="footer-section">
          <h3 class="footer-logo" style="font-size: 1.5rem; font-weight: 700; margin: 0 0 1rem 0; color: #ffffff;">
            Iluria
          </h3>
        </div>
        
        <!-- Navigation Links -->
        <div class="footer-section">
          <h4 class="footer-title" style="font-size: 1rem; font-weight: 600; margin: 0 0 1rem 0; color: #f3f4f6;">
            Links Úteis
          </h4>
          <ul class="footer-links" style="list-style: none; padding: 0; margin: 0;">
            <li style="margin-bottom: 0.5rem;">
              <a href="#" style="color: #d1d5db; text-decoration: none; transition: color 0.2s;">Sobre</a>
            </li>
            <li style="margin-bottom: 0.5rem;">
              <a href="#" style="color: #d1d5db; text-decoration: none; transition: color 0.2s;">Contato</a>
            </li>
            <li style="margin-bottom: 0.5rem;">
              <a href="#" style="color: #d1d5db; text-decoration: none; transition: color 0.2s;">Política de Privacidade</a>
            </li>
            <li style="margin-bottom: 0.5rem;">
              <a href="#" style="color: #d1d5db; text-decoration: none; transition: color 0.2s;">Termos de Uso</a>
            </li>
          </ul>
        </div>
        
        <!-- Contact Info -->
        <div class="footer-section">
          <h4 class="footer-title" style="font-size: 1rem; font-weight: 600; margin: 0 0 1rem 0; color: #f3f4f6;">
            Contato
          </h4>
          <div class="contact-info" style="color: #d1d5db; font-size: 0.875rem; line-height: 1.6;">
            <p style="margin: 0 0 0.5rem 0;">📧 <EMAIL></p>
            <p style="margin: 0 0 0.5rem 0;">📞 (11) 99999-9999</p>
            <p style="margin: 0;">📍 Rua Exemplo, 123 - São Paulo, SP</p>
          </div>
        </div>
        
        <!-- Social Links -->
        <div class="footer-section">
          <h4 class="footer-title" style="font-size: 1rem; font-weight: 600; margin: 0 0 1rem 0; color: #f3f4f6;">
            Redes Sociais
          </h4>
          <p style="color: #9ca3af; font-style: italic; font-size: 0.875rem;">
            Configure suas redes sociais
          </p>
        </div>
      </div>
      
      <!-- Copyright -->
      <div class="footer-bottom" style="border-top: 1px solid #374151; padding-top: 1.5rem; text-align: center;">
        <p class="copyright-text" style="margin: 0; color: #9ca3af; font-size: 0.875rem;">
          © 2024 Nome da Loja. Todos os direitos reservados.
        </p>
        <p style="margin: 0.5rem 0 0 0; color: #6b7280; font-size: 0.75rem;">
          Feito com <span style="color: #ef4444;">❤</span> Iluria
        </p>
      </div>
    </div>
    
    <style>
      .iluria-footer a:hover {
        color: #60a5fa !important;
      }
      
      @media (max-width: 768px) {
        .footer-content {
          grid-template-columns: 1fr !important;
          gap: 1.5rem !important;
        }
        
        .footer-container {
          padding: 0 1rem !important;
        }
        
        .iluria-footer {
          padding: 2rem 0 1rem 0 !important;
        }
      }
    </style>
  </footer>`,
  
  toolbarActions: [
    {
      type: 'footer-config',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M12 20h9"/>
        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"/>
      </svg>`,
      title: 'layoutEditor.editFooterConfig',
      condition: (element) => {
        return element?.getAttribute('data-component') === 'footer' ||
               element?.getAttribute('data-element-type') === 'footer'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'footer-config',
      component: 'FooterEditor',
              path: '@/components/layoutEditor/Sidebar/editors/FooterEditor.vue'
    }
  ],
  
  computedCheckers: {
    isFooterComponent: (element) => {
      return element?.getAttribute('data-component') === 'footer' ||
             element?.getAttribute('data-element-type') === 'footer'
    },
    
    hasNavigation: (element) => {
      const config = element?.getAttribute('data-config')
      try {
        const parsed = JSON.parse(config || '{}')
        return parsed.showNavigation && parsed.navigationItems?.length > 0
      } catch {
        return false
      }
    },
    
    hasContact: (element) => {
      const config = element?.getAttribute('data-config')
      try {
        const parsed = JSON.parse(config || '{}')
        return parsed.showContact && parsed.contactInfo
      } catch {
        return false
      }
    },
    
    hasSocial: (element) => {
      const config = element?.getAttribute('data-config')
      try {
        const parsed = JSON.parse(config || '{}')
        return parsed.showSocial && parsed.socialLinks
      } catch {
        return false
      }
    }
  },
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true
  },
  
  cleanup: {
    removeEditingStyles: true,
    preserveStyles: ['background', 'color', 'padding', 'margin']
  }
} 