<template>
  <div class="header-sidebar-editor">
    <!-- Tabs -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- Aba Logo -->
      <div v-if="activeTab === 'logo'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.logoConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.logoText') }}</IluriaLabel>
            <IluriaInputText
              v-model="headerConfig.logoText"
              :placeholder="$t('layoutEditor.enterLogoText')"
              @update:modelValue="updateHeader"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.logoPosition') }}</IluriaLabel>
            <IluriaSelect
              v-model="headerConfig.logoPosition" 
              :options="[
                { label: $t('layoutEditor.left'), value: 'left' },
                { label: $t('layoutEditor.center'), value: 'center' }
              ]"
              @update:modelValue="updateHeader"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.logoMobilePosition') }}</IluriaLabel>
            <IluriaSelect
              v-model="headerConfig.logoMobilePosition" 
              :options="[
                { label: $t('layoutEditor.left'), value: 'left' },
                { label: $t('layoutEditor.center'), value: 'center' }
              ]"
              @update:modelValue="updateHeader"
            />
          </div>
        </div>
      </div>

      <!-- Aba Menu -->
      <div v-if="activeTab === 'menu'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.menuConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.menuPosition') }}</IluriaLabel>
            <IluriaSelect
              v-model="headerConfig.menuPosition" 
              :options="[
                { label: $t('layoutEditor.left'), value: 'left' },
                { label: $t('layoutEditor.center'), value: 'center' },
                { label: $t('layoutEditor.right'), value: 'right' }
              ]"
              @update:modelValue="updateHeader"
            />
          </div>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.menuType') }}</IluriaLabel>
            <IluriaSelect
              v-model="headerConfig.menuType" 
              :options="[
                { label: $t('layoutEditor.horizontal'), value: 'horizontal' },
                { label: $t('layoutEditor.dropdown'), value: 'dropdown' }
              ]"
              @update:modelValue="updateHeader"
            />
          </div>

          <!-- Menu Items -->
          <div class="menu-items-section">
            <div class="form-group-header">
              <IluriaLabel>{{ $t('layoutEditor.menuItems') }}</IluriaLabel>
              <IluriaButton 
                color="primary" 
                size="sm" 
                @click="addMenuItem"
              >
                {{ $t('layoutEditor.addMenuItem') }}
              </IluriaButton>
            </div>

            <div v-if="headerConfig.menuItems?.length" class="menu-items-list">
              <div 
                v-for="(item, index) in headerConfig.menuItems" 
                :key="index"
                class="menu-item-card"
              >
                <div class="menu-item-content">
                  <div class="menu-item-fields">
                    <IluriaInputText
                      v-model="item.title"
                      :placeholder="$t('layoutEditor.menuTitle')"
                      @update:modelValue="updateHeader"
                    />
                    <IluriaInputText
                      v-model="item.url"
                      :placeholder="$t('layoutEditor.menuUrl')"
                      @update:modelValue="updateHeader"
                    />
                  </div>
                  
                  <div class="menu-item-options">
                    <IluriaCheckBox
                        v-model="item.hasDropdown"
                      :label="$t('layoutEditor.hasDropdown')"
                      @update:modelValue="updateHeader"
                      />
                  </div>

                  <div class="menu-item-actions">
                    <IluriaButton 
                      color="outline" 
                      size="sm" 
                      @click="moveMenuItem(index, -1)"
                      :disabled="index === 0"
                    >
                      ↑
                    </IluriaButton>
                    <IluriaButton 
                      color="outline" 
                      size="sm" 
                      @click="moveMenuItem(index, 1)"
                      :disabled="index === headerConfig.menuItems.length - 1"
                    >
                      ↓
                    </IluriaButton>
                    <IluriaButton 
                      color="danger" 
                      size="sm" 
                      @click="removeMenuItem(index)"
                    >
                      {{ $t('layoutEditor.remove') }}
                    </IluriaButton>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- Aba Cores -->
      <div v-if="activeTab === 'colors'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.colorConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.backgroundColor') }}</IluriaLabel>
            <IluriaColorPicker
                v-model="headerConfig.backgroundColor"
                @update:modelValue="updateHeader"
              />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.textColor') }}</IluriaLabel>
            <IluriaColorPicker
                v-model="headerConfig.textColor"
                @update:modelValue="updateHeader"
              />
          </div>
        </div>
      </div>

      <!-- Aba Layout -->
      <div v-if="activeTab === 'layout'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.layoutConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaCheckBox
                v-model="headerConfig.showSeparator"
              :label="$t('layoutEditor.showSeparator')"
              @update:modelValue="updateHeader"
              />
          </div>
        </div>
      </div>

      <!-- Aba Espaçamento -->
      <div v-if="activeTab === 'spacing'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.spacingConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.paddingTop') }}</IluriaLabel>
            <IluriaRange
                v-model="headerConfig.paddingTop"
              :min="0"
              :max="100"
              suffix="px"
              @update:modelValue="updateHeader"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.paddingBottom') }}</IluriaLabel>
            <IluriaRange
                v-model="headerConfig.paddingBottom"
              :min="0"
              :max="100"
              suffix="px"
              @update:modelValue="updateHeader"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.innerMargin') }}</IluriaLabel>
            <IluriaRange
                v-model="headerConfig.innerMargin"
              :min="0"
              :max="50"
              suffix="px"
              @update:modelValue="updateHeader"
            />
          </div>
        </div>
      </div>

      <!-- Aba Serviços -->
      <div v-if="activeTab === 'services'" class="tab-panel">
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.servicesConfiguration') }}</h3>
          
          <div class="form-group">
            <IluriaCheckBox
                v-model="headerConfig.showCountrySelector"
              :label="$t('layoutEditor.countrySelector')"
              @update:modelValue="updateHeader"
              />
          </div>

          <div class="form-group">
            <IluriaCheckBox
                v-model="headerConfig.showLanguageSelector"
              :label="$t('layoutEditor.languageSelector')"
              @update:modelValue="updateHeader"
              />
          </div>

          <div class="form-group">
            <IluriaCheckBox
                v-model="headerConfig.showAccountAvatar"
              :label="$t('layoutEditor.accountAvatar')"
              @update:modelValue="updateHeader"
              />
          </div>
        </div>
      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['close', 'save', 'data-changed'])

// Tabs
const tabs = [
  { value: 'logo', label: t('layoutEditor.logo') },
  { value: 'menu', label: t('layoutEditor.menu') },
  { value: 'colors', label: t('layoutEditor.colors') },
  { value: 'layout', label: t('layoutEditor.layout') },
  { value: 'spacing', label: t('layoutEditor.spacing') },
  { value: 'services', label: t('layoutEditor.services') }
]

// Estado das abas
const activeTab = ref('logo')

// Configuração do header
const headerConfig = ref({
  logoText: 'Logo',
  logoPosition: 'left',
  logoMobilePosition: 'center',
  menuPosition: 'left',
  menuType: 'horizontal',
  showSeparator: true,
  showCountrySelector: false,
  showLanguageSelector: false,
  showAccountAvatar: true,
  backgroundColor: '#ffffff',
  textColor: '#000000',
  paddingTop: 20,
  paddingBottom: 20,
  innerMargin: 0,
  menuItems: []
})

// Carrega configuração do elemento
const loadHeaderConfig = () => {
  if (!props.element) return

  try {
    headerConfig.value = {
      logoText: props.element.getAttribute('data-logo-text') || 'Logo',
      logoPosition: props.element.getAttribute('data-logo-position') || 'left',
      logoMobilePosition: props.element.getAttribute('data-logo-mobile-position') || 'center',
      menuPosition: props.element.getAttribute('data-menu-position') || 'left',
      menuType: props.element.getAttribute('data-menu-type') || 'horizontal',
      showSeparator: props.element.getAttribute('data-show-separator') !== 'false',
      showCountrySelector: props.element.getAttribute('data-show-country') === 'true',
      showLanguageSelector: props.element.getAttribute('data-show-language') === 'true',
      showAccountAvatar: props.element.getAttribute('data-show-account') !== 'false',
      backgroundColor: props.element.getAttribute('data-bg-color') || '#ffffff',
      textColor: props.element.getAttribute('data-text-color') || '#000000',
      paddingTop: parseInt(props.element.getAttribute('data-padding-top') || '20'),
      paddingBottom: parseInt(props.element.getAttribute('data-padding-bottom') || '20'),
      innerMargin: parseInt(props.element.getAttribute('data-inner-margin') || '0'),
      menuItems: JSON.parse(props.element.getAttribute('data-menu-items') || '[]')
    }
  } catch (error) {
    console.error('Erro ao carregar configuração do header:', error)
  }
}

// Atualiza o header
const updateHeader = () => {
  if (!props.element) return

  try {
    // 1. Atualiza atributos do elemento
    Object.entries(headerConfig.value).forEach(([key, value]) => {
      const attributeName = `data-${key.replace(/([A-Z])/g, '-$1').toLowerCase()}`
      
      if (typeof value === 'boolean') {
        props.element.setAttribute(attributeName, value.toString())
      } else if (Array.isArray(value)) {
        props.element.setAttribute(attributeName, JSON.stringify(value))
      } else {
        props.element.setAttribute(attributeName, value.toString())
      }
    })

    // 2. Re-gera o HTML completo
    regenerateHeaderHTML()
    
    // 3. Aplica estilos diretamente
    applyHeaderStylesDirectly()
    
    // Notifica mudanças
    emit('data-changed', headerConfig.value)
  } catch (error) {
    console.error('Erro ao atualizar header:', error)
  }
}

// Re-gera o HTML do header
const regenerateHeaderHTML = () => {
  if (!props.element) return
  
  try {
    const data = headerConfig.value
    
    const utilityServices = generateUtilityServices(data)
    const navigation = generateNavigation(data)
    const logo = generateLogo(data)
    const actions = generateHeaderActions(data)

    const headerHTML = `
      <header class="header-main logo-position-${data.logoPosition} menu-position-${data.menuPosition}">
        <div class="header-container">
          <div class="header-content">
            <div class="header-left">
              ${data.logoPosition === 'left' ? logo : ''}
              ${data.menuPosition === 'left' ? navigation : ''}
            </div>
            
            <div class="header-center">
              ${data.logoPosition === 'center' ? logo : ''}
              ${data.menuPosition === 'center' ? navigation : ''}
            </div>
            
            <div class="header-right">
              ${data.menuPosition === 'right' ? navigation : ''}
              ${utilityServices}
              ${actions}
            </div>
          </div>
        </div>
        
        ${data.showSeparator ? '<div class="header-separator"></div>' : ''}
      </header>
    `
    
    const existingStyles = props.element.querySelector('.header-dynamic-styles')
    props.element.innerHTML = headerHTML
    if (existingStyles) {
      props.element.appendChild(existingStyles)
    }
    
  } catch (error) {
    console.error('Erro ao regenerar HTML:', error)
  }
}

// Funções auxiliares para gerar HTML
const generateLogo = (data) => {
  return `
    <div class="header-logo">
      <a href="#" class="logo-link">
        <span class="logo-text">${data.logoText}</span>
      </a>
    </div>
  `
}

const generateNavigation = (data) => {
  if (!data.menuItems || data.menuItems.length === 0) return ''

  const menuHTML = data.menuItems.map(item => `
    <li class="nav-item ${item.hasDropdown ? 'has-dropdown' : ''}">
      <a href="${item.url}" class="nav-link">
        ${item.title}
        ${item.hasDropdown ? '<svg class="dropdown-icon" width="12" height="12" viewBox="0 0 24 24" fill="none" stroke="currentColor"><path d="m6 9 6 6 6-6"/></svg>' : ''}
      </a>
      ${item.hasDropdown ? '<ul class="dropdown-menu"><li><a href="#">Submenu 1</a></li><li><a href="#">Submenu 2</a></li></ul>' : ''}
    </li>
  `).join('')

  return `
    <nav class="header-navigation menu-type-${data.menuType}">
      <ul class="nav-menu">
        ${menuHTML}
      </ul>
    </nav>
  `
}

const generateUtilityServices = (data) => {
  let services = []

  if (data.showCountrySelector) {
    services.push(`
      <div class="utility-service country-selector">
        <button class="utility-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <circle cx="12" cy="12" r="10"/>
            <line x1="2" y1="12" x2="22" y2="12"/>
            <path d="M12 2a15.3 15.3 0 0 1 4 10 15.3 15.3 0 0 1-4 10 15.3 15.3 0 0 1-4-10 15.3 15.3 0 0 1 4-10z"/>
          </svg>
        </button>
      </div>
    `)
  }

  if (data.showLanguageSelector) {
    services.push(`
      <div class="utility-service language-selector">
        <button class="utility-btn">
          <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M5 8l6 6"/>
            <path d="M4 14l6-6 2-3"/>
            <path d="M2 5h12"/>
            <path d="M7 2h1"/>
            <path d="M22 22l-5-10-5 10"/>
            <path d="M14 18h6"/>
          </svg>
        </button>
      </div>
    `)
  }

  return services.length > 0 ? `<div class="utility-services">${services.join('')}</div>` : ''
}

const generateHeaderActions = (data) => {
  return `
    <div class="header-actions">
      <button class="action-btn search-btn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <circle cx="11" cy="11" r="8"/>
          <path d="m21 21-4.35-4.35"/>
        </svg>
      </button>
      
      ${data.showAccountAvatar ? `
        <button class="action-btn account-btn">
          <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
            <circle cx="12" cy="7" r="4"/>
          </svg>
        </button>
      ` : ''}
      
      <button class="action-btn cart-btn">
        <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor">
          <path d="M9 22C9.55228 22 10 21.5523 10 21C10 20.4477 9.55228 20 9 20C8.44772 20 8 20.4477 8 21C8 21.5523 8.44772 22 9 22Z"/>
          <path d="M21 22C21.5523 22 22 21.5523 22 21C22 20.4477 21.5523 20 21 20C20.4477 20 20 20.4477 20 21C20 21.5523 20.4477 22 21 22Z"/>
          <path d="M1 1H5L7.68 14.39C7.77144 14.8504 8.02191 15.264 8.38755 15.5583C8.75318 15.8526 9.2107 16.009 9.68 16H19.4C19.8693 16.009 20.3268 15.8526 20.6925 15.5583C21.0581 15.264 21.3086 14.8504 21.4 14.39L23 6H6"/>
        </svg>
      </button>
    </div>
  `
}

// Aplica estilos diretamente no elemento
const applyHeaderStylesDirectly = () => {
  if (!props.element) return
  
  try {
    const existingStyle = props.element.querySelector('.header-dynamic-styles')
    if (existingStyle) existingStyle.remove()

    const styles = `
      <style class="header-dynamic-styles">
        .header-main {
          background-color: ${headerConfig.value.backgroundColor} !important;
          color: ${headerConfig.value.textColor} !important;
          padding: ${headerConfig.value.paddingTop}px 0 ${headerConfig.value.paddingBottom}px 0 !important;
          margin: ${headerConfig.value.innerMargin}px 0 !important;
          width: 100%;
          transition: all 0.3s ease;
          position: relative;
          z-index: 1;
        }
        
        .header-container {
          max-width: 1200px;
          margin: 0 auto;
          padding: 0 20px;
        }
        
        .header-content {
          display: flex !important;
          align-items: center !important;
          justify-content: space-between !important;
          gap: 20px !important;
        }
        
        .header-left,
        .header-center,
        .header-right {
          display: flex !important;
          align-items: center !important;
          gap: 20px !important;
        }
        
        .header-center {
          flex: 1 !important;
          justify-content: center !important;
        }
        
        .logo-position-center .header-left {
          flex: 1 !important;
        }
        
        .logo-position-center .header-right {
          flex: 1 !important;
          justify-content: flex-end !important;
        }
        
        .header-logo .logo-text {
          font-size: 24px;
          font-weight: bold;
          color: ${headerConfig.value.textColor} !important;
          text-decoration: none;
        }
        
        .nav-menu {
          display: flex !important;
          list-style: none !important;
          margin: 0 !important;
          padding: 0 !important;
          gap: 30px !important;
        }
        
        .nav-link {
          color: ${headerConfig.value.textColor} !important;
          text-decoration: none;
          display: flex;
          align-items: center;
          gap: 5px;
          padding: 8px 0;
          transition: opacity 0.2s;
        }
        
        .nav-link:hover {
          opacity: 0.7;
        }
        
        .dropdown-icon {
          width: 12px;
          height: 12px;
        }
        
        .utility-services {
          display: flex !important;
          gap: 15px !important;
        }
        
        .utility-btn,
        .action-btn {
          background: none;
          border: none;
          color: ${headerConfig.value.textColor} !important;
          cursor: pointer;
          padding: 8px;
          border-radius: 4px;
          transition: background-color 0.2s;
        }
        
        .utility-btn:hover,
        .action-btn:hover {
          background-color: rgba(0,0,0,0.1);
        }
        
        .header-actions {
          display: flex !important;
          gap: 15px !important;
        }
        
        .header-separator {
          height: 1px;
          background-color: rgba(0,0,0,0.1);
          margin-top: 10px;
        }
      </style>
    `
    
    props.element.insertAdjacentHTML('beforeend', styles)
    
  } catch (error) {
    console.error('Erro ao aplicar estilos:', error)
  }
}

// Menu items functions
const addMenuItem = () => {
  headerConfig.value.menuItems.push({
    title: 'Novo Item',
    url: '#',
    hasDropdown: false
  })
  updateHeader()
}

const removeMenuItem = (index) => {
  headerConfig.value.menuItems.splice(index, 1)
  updateHeader()
}

const moveMenuItem = (index, direction) => {
  const newIndex = index + direction
  if (newIndex >= 0 && newIndex < headerConfig.value.menuItems.length) {
    const item = headerConfig.value.menuItems.splice(index, 1)[0]
    headerConfig.value.menuItems.splice(newIndex, 0, item)
    updateHeader()
  }
}

// Lifecycle
onMounted(() => {
  loadHeaderConfig()
})
</script>

<style scoped>
.header-sidebar-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Tabs */
.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  overflow-x: auto;
  flex-shrink: 0;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-bg);
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
}

.section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.form-group {
  margin-bottom: 1rem;
}

.form-group-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 1rem;
}

/* Menu Items */
.menu-items-section {
  margin-top: 1.5rem;
}

.menu-items-list {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.menu-item-card {
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 0.75rem;
}

.menu-item-content {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
}

.menu-item-fields {
  display: flex;
  flex-direction: column;
  gap: 0.5rem;
}

.menu-item-options {
  display: flex;
  align-items: center;
}

.menu-item-actions {
  display: flex;
  gap: 0.5rem;
  justify-content: flex-end;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-panel {
    padding: 0.75rem;
  }
  
  .menu-item-actions {
    flex-direction: column;
    align-items: stretch;
  }
}
</style> 
