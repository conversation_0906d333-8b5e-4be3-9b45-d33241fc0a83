import { ref } from 'vue'
import { useComponentScriptInjection } from '@/composables/useComponentScriptInjection.js'

/**
 * Sistema centralizado para processamento automático de componentes
 * 
 * Resolve o problema de código repetitivo para cada tipo de componente
 * Automatiza: 
 * - Definição de atributos corretos
 * - Criação de containers
 * - Estruturação HTML
 * - Injeção de scripts
 */

// Registry de tipos de componentes e suas configurações
const componentProcessingRegistry = ref(new Map())

/**
 * Registra um tipo de componente para processamento automático
 * @param {string} componentType - Tipo do componente
 * @param {Object} config - Configuração de processamento
 */
export function registerComponentProcessor(componentType, config) {
  componentProcessingRegistry.value.set(componentType, {
    ...config,
    componentType
  })

}

/**
 * Configurações padrão para processamento de componentes
 */
const defaultComponentProcessors = [
  {
    type: 'product-grid',
    selectors: [
      '[data-component="dynamic-grid-produtos"]',
      '[data-product-grid]',
      '[data-element-type="dynamicProductGrid"]'
    ],
    attributes: {
      'data-component': 'dynamic-grid-produtos',
      'data-element-type': 'dynamicProductGrid',
      'data-product-grid': 'true'
    },
    classes: [],
    description: 'Grid de produtos dinâmico'
  },
  {
    type: 'carousel',
    selectors: [
      '[data-component="carousel"]',
      '.iluria-carousel',
      '[data-element-type="carousel"]'
    ],
    attributes: {
      'data-component': 'carousel',
      'data-element-type': 'carousel'
    },
    classes: ['iluria-carousel'],
    description: 'Carrossel de imagens/conteúdo'
  },
  {
    type: 'video',
    selectors: [
      '[data-component="video"]',
      '.iluria-video',
      '[data-element-type="video"]'
    ],
    attributes: {
      'data-component': 'video',
      'data-element-type': 'video'
    },
    classes: ['iluria-video'],
    description: 'Componente de vídeo'
  },
  {
    type: 'company-information',
    selectors: [
      '[data-component="company-information"]',
      '.iluria-company-information',
      '[data-element-type="company-information"]'
    ],
    attributes: {
      'data-component': 'company-information',
      'data-element-type': 'company-information'
    },
    classes: ['iluria-company-information'],
    description: 'Informações da empresa'
  },
  {
    type: 'location',
    selectors: [
      '[data-component="location"]',
      '.location-section',
      '[data-element-type="location"]'
    ],
    attributes: {
      'data-component': 'location',
      'data-element-type': 'location'
    },
    classes: ['location-section'],
    description: 'Seção de localização'
  },
  {
    type: 'statement',
    selectors: [
      '[data-component="statement"]',
      '.iluria-statement',
      '[data-element-type="statement"]'
    ],
    attributes: {
      'data-component': 'statement',
      'data-element-type': 'statement'
    },
    classes: ['iluria-statement'],
    description: 'Componente de statement/depoimento'
  },
  {
    type: 'payment-benefits',
    selectors: [
      '[data-component="payment-benefits"]',
      '.iluria-payment-benefits',
      '[data-element-type="paymentBenefits"]'
    ],
    attributes: {
      'data-component': 'payment-benefits',
      'data-element-type': 'paymentBenefits'
    },
    classes: ['iluria-payment-benefits'],
    description: 'Componente de benefícios de pagamento'
  },
  {
    type: 'customer-review',
    selectors: [
      '[data-component="customer-review"]',
      '.iluria-customer-review',
      '[data-element-type="customerReview"]'
    ],
    attributes: {
      'data-component': 'customer-review',
      'data-element-type': 'customerReview'
    },
    classes: ['iluria-customer-review'],
    description: 'Componente de avaliações de clientes'
  },
  {
    type: 'product-checkout',
    selectors: [
      '[data-component="product-checkout"]',
      '.product-checkout-container',
      '.checkout-widget'
    ],
    attributes: {
      'data-component': 'product-checkout',
      'data-element-type': 'product-checkout'
    },
    classes: ['product-checkout-container'],
    description: 'Componente de checkout de produto'
  }
]

/**
 * Inicializa o sistema com processadores padrão
 */
export function initializeComponentProcessingSystem() {

  
  defaultComponentProcessors.forEach(config => {
    registerComponentProcessor(config.type, config)
  })
  

}

/**
 * Processa um elemento individual de acordo com sua configuração
 * @param {Element} element - Elemento a processar
 * @param {Object} config - Configuração do processador
 * @param {Document} doc - Documento
 * @param {number} index - Índice do elemento
 */
function processIndividualComponent(element, config, doc, index) {

  
  // 1. Aplicar atributos
  Object.entries(config.attributes).forEach(([attr, value]) => {
    element.setAttribute(attr, value)
  })
  
  // 2. Aplicar classes
  config.classes.forEach(className => {
    element.classList.add(className)
  })
  
  // 3. Verificar se já está em um container
  if (element.closest('.component-container')) {
    const existingContainer = element.closest('.component-container')
    const existingContentWrapper = existingContainer.querySelector('.element-content')
    
    // Garantir que o container tenha os atributos necessários
    existingContainer.setAttribute('data-editable-element', 'true')
    if (!existingContainer.hasAttribute('data-element-id')) {
      existingContainer.setAttribute('data-element-id', 'el-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
    }
    
    // Se não tem o wrapper correto, reorganizar
    if (!existingContentWrapper || !existingContentWrapper.contains(element)) {
      // Preservar o elemento com todos os atributos ANTES de limpar
      const elementClone = element.cloneNode(true)
      
      // Verificar se já existe um clone na estrutura antes de limpar
      const existingClone = existingContainer.querySelector(config.selectors[0])
      if (existingClone && existingClone !== element) {
        existingClone.remove()
      }
      
      while (existingContainer.firstChild) {
        existingContainer.removeChild(existingContainer.firstChild)
      }
      
      // Criar a estrutura correta
      const contentWrapper = doc.createElement('div')
      contentWrapper.className = 'element-content'
      contentWrapper.appendChild(elementClone)
      existingContainer.appendChild(contentWrapper)
    } else {
      // Verificar se não há duplicação mesmo quando o wrapper está correto
      const elementsInWrapper = existingContentWrapper.querySelectorAll(config.selectors[0])
      if (elementsInWrapper.length > 1) {
        // Remove duplicados, mantém apenas o primeiro
        for (let i = 1; i < elementsInWrapper.length; i++) {
          elementsInWrapper[i].remove()
        }
      }
    }
    
    return
  }
  
  // 4. Se não está em um container, criar um novo
  const componentContainer = doc.createElement('div')
  componentContainer.className = 'component-container'
  componentContainer.setAttribute('data-editable-element', 'true')
  componentContainer.setAttribute('data-element-id', 'el-' + Date.now() + '-' + Math.floor(Math.random() * 1000))
  
  const parent = element.parentNode
  
  // Verificar se já existe um container para este elemento antes de criar
  const existingContainer = parent.querySelector('.component-container')
  if (existingContainer && existingContainer.contains(element)) {
    return
  }
  
  parent.insertBefore(componentContainer, element)
  
  const contentWrapper = doc.createElement('div')
  contentWrapper.className = 'element-content'
  contentWrapper.appendChild(element)
  
  componentContainer.appendChild(contentWrapper)
}

/**
 * Processa todos os componentes de um tipo específico no documento
 * @param {Document} doc - Documento a processar
 * @param {string} componentType - Tipo de componente
 */
export function processComponentType(doc, componentType) {
  const config = componentProcessingRegistry.value.get(componentType)
  
  if (!config) {
    console.warn(`⚠️ Processador não encontrado para tipo: ${componentType}`)
    return
  }
  

  
  // Encontrar todos os elementos do tipo
  const elements = doc.querySelectorAll(config.selectors.join(', '))
  

  
  elements.forEach((element, index) => {
    processIndividualComponent(element, config, doc, index)
  })
  

}

/**
 * Processa todos os tipos de componentes registrados no documento
 * @param {Document} doc - Documento a processar
 */
export function processAllComponents(doc) {

  
  // Percorre todos os processadores registrados
  for (const [componentType] of componentProcessingRegistry.value) {
    processComponentType(doc, componentType)
  }
  

}

/**
 * Processa componentes e injeta scripts automaticamente
 * @param {Document} doc - Documento a processar
 * @param {string} authToken - Token de autenticação
 */
export function processComponentsAndInjectScripts(doc, authToken = '') {

  
  // 1. Processar componentes primeiro
  processAllComponents(doc)
  
  // 2. Injetar scripts usando o sistema centralizado
  try {
    useComponentScriptInjection(doc, authToken)

  } catch (error) {
    console.error('❌ Erro ao injetar scripts:', error)
  }
  

}

/**
 * Adiciona um novo processador de componente dinamicamente
 * @param {string} componentType - Tipo do componente
 * @param {Array<string>} selectors - Seletores para encontrar o componente
 * @param {Object} attributes - Atributos a aplicar
 * @param {Array<string>} classes - Classes a aplicar
 * @param {string} description - Descrição do componente
 */
export function addComponentProcessor(componentType, selectors, attributes, classes = [], description = '') {
  registerComponentProcessor(componentType, {
    selectors: Array.isArray(selectors) ? selectors : [selectors],
    attributes: attributes || {},
    classes: Array.isArray(classes) ? classes : [classes],
    description
  })
}

/**
 * Hook principal do composable
 */
export function useComponentProcessing() {
  // Inicializa o sistema se ainda não foi feito
  if (componentProcessingRegistry.value.size === 0) {
    initializeComponentProcessingSystem()
  }
  
  return {
    processAllComponents,
    processComponentType,
    processComponentsAndInjectScripts,
    addComponentProcessor,
    registerComponentProcessor,
    initializeComponentProcessingSystem
  }
}

/**
 * 🔧 SISTEMA ESPECÍFICO PARA WRITEHTML
 * Aplica atributos necessários para elementos serem editáveis
 */
export const processForEditing = (container) => {
  try {

    
    // Configuração específica para writeHtml (adiciona atributos de edição)
    const editingConfigs = [
      {
        selectors: ['[data-component="dynamic-grid-produtos"]'],
        process: (element) => {
          element.setAttribute('data-product-grid', 'true')
          element.setAttribute('data-element-type', 'product-grid')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="carousel"]', '.iluria-carousel'],
        process: (element) => {
          element.setAttribute('data-component', 'carousel')
          element.setAttribute('data-element-type', 'carousel')
          element.classList.add('iluria-carousel')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="video"]', '.iluria-video'],
        process: (element) => {
          element.setAttribute('data-component', 'video')
          element.setAttribute('data-element-type', 'video')
          element.classList.add('iluria-video')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="company-information"]'],
        process: (element) => {
          element.setAttribute('data-component', 'company-information')
          element.setAttribute('data-element-type', 'company-information')
          element.classList.add('iluria-company-information')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="location"]', '.location-section'],
        process: (element) => {
          element.setAttribute('data-component', 'location')
          element.setAttribute('data-element-type', 'location')
          element.classList.add('location-section')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="statement"]', '.iluria-statement'],
        process: (element) => {
          element.setAttribute('data-component', 'statement')
          element.setAttribute('data-element-type', 'statement')
          element.classList.add('iluria-statement')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="payment-benefits"]', '.iluria-payment-benefits'],
        process: (element) => {
          element.setAttribute('data-component', 'payment-benefits')
          element.setAttribute('data-element-type', 'paymentBenefits')
          element.classList.add('iluria-payment-benefits')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="header"]', '.iluria-header'],
        process: (element) => {
          element.setAttribute('data-component', 'header')
          element.setAttribute('data-element-type', 'header')
          element.classList.add('iluria-header')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      },
      {
        selectors: ['[data-component="product-checkout"]', '.product-checkout-container'],
        process: (element) => {
          element.setAttribute('data-component', 'product-checkout')
          element.setAttribute('data-element-type', 'product-checkout')
          element.classList.add('product-checkout-container')
          addEditableContainer(element)
          // 🚨 IMPORTANTE: Remove atributos de texto de elementos internos
          removeTextAttributesFromInternalElements(element)
        }
      }
    ]
    
    // Aplica configurações de edição
    editingConfigs.forEach(config => {
      config.selectors.forEach(selector => {
        const elements = container.querySelectorAll(selector)
        elements.forEach(element => {
          try {
            config.process(element)
          } catch (error) {
            console.warn(`Erro ao processar elemento ${selector}:`, error)
          }
        })
      })
    })
    

    
  } catch (error) {
    console.error('❌ Erro no processamento para edição:', error)
  }
}

/**
 * Remove atributos de texto de elementos internos para evitar editores indevidos
 */
const removeTextAttributesFromInternalElements = (componentElement) => {
  // Elementos que não devem ter editores de texto
  const internalElements = componentElement.querySelectorAll('h1, h2, h3, h4, h5, h6, p, span, div, button, a')
  
  internalElements.forEach(element => {
    // Remove atributos que tornariam o elemento editável como texto
    element.removeAttribute('data-element-type')
    element.removeAttribute('data-text-element')
    element.removeAttribute('data-interactive-element')
    element.removeAttribute('data-editable-element')
    

  })
}

/**
 * Adiciona container editável se necessário
 */
const addEditableContainer = (element) => {
  const container = element.closest('.component-container')
  if (container) {
    container.setAttribute('data-editable-element', 'true')
  }
} 