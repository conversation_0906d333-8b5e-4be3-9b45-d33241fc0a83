{"bellAriaLabel": "Open notifications", "title": "Notifications", "markAllAsRead": "Mark all as read", "markAllRead": "Mark all as read", "settings": "Notification settings", "loading": "Loading notifications...", "loadMore": "Load more", "allMarkedAsRead": "All notifications marked as read", "markAsRead": "<PERSON> as read", "delete": "Delete notification", "deleteAll": "Delete all", "markRead": "<PERSON> as read", "markedAsRead": "Notification marked as read", "deleted": "Notification deleted", "bulkDelete": {"title": "Delete all notifications", "message": "Are you sure you want to delete all {count} visible notifications?", "warning": "This action cannot be undone.", "confirm": "Yes, delete all", "success": "All notifications have been deleted", "error": "Error deleting notifications"}, "empty": {"title": "No notifications", "message": "You're all caught up! New notifications will appear here."}, "error": {"loadFailed": "Failed to load notifications", "loadMoreFailed": "Failed to load more notifications", "markReadFailed": "Failed to mark notifications as read", "deleteFailed": "Failed to delete notification", "bulkDeleteFailed": "Failed to delete all notifications", "navigationFailed": "Failed to navigate to notification"}, "time": {"now": "now", "minutes": "min", "hours": "h", "days": "d", "weeks": "w", "months": "mo", "years": "y"}, "mock": {"newSale": {"title": "New sale completed", "content": "Order #12345 has been successfully completed."}, "productReview": {"title": "New review", "content": "Your product received a 5-star review."}, "securityAlert": {"title": "Security alert", "content": "New login attempt detected."}, "featureAnnouncement": {"title": "New feature", "content": "Discover the platform's latest updates."}}, "byEmail": "By email", "comments": "Comments", "commentsDescription": "Get notified when someone posts a comment on a posting.", "candidates": "Candidates", "candidatesDescription": "Get notified when a candidate applies for a job.", "offers": "Offers", "offersDescription": "Get notified when a candidate accepts or rejects an offer.", "pushNotifications": "Push notifications", "pushNotificationsDescription": "These are delivered via SMS to your mobile phone.", "pushEverything": "Everything", "pushSameAsEmail": "Same as email", "pushNothing": "No push notifications", "emailNotifications": "Email Notifications", "emailNotificationsDescription": "Receive notifications via email.", "userTitle": "Personal Notifications", "storeTitle": "Store Notifications"}