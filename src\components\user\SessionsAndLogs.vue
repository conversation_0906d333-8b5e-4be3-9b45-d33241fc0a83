<template>
  <div class="sessions-content">


    <!-- Security Alerts -->
    <div v-if="suspiciousSessions.length > 0" class="security-alert">
      <HugeiconsIcon 
        :icon="Alert02Icon" 
        size="20" 
        :strokeWidth="1.5"
        class="alert-icon"
      />
      <div class="alert-content">
        <h4>Atividade Suspeita Detectada</h4>
        <p>Foram detectadas {{ suspiciousSessions.length }} sessões de localizações diferentes. Verifique se você reconhece todos os dispositivos.</p>
      </div>
    </div>

    <!-- Active Sessions -->
    <ViewContainer
      title="Sessões Ativas"
      subtitle="Gerencie os dispositivos conectados à sua conta"
      :icon="ComputerIcon"
      iconColor="blue"
    >

      <div class="sessions-list">
        <!-- Loading state -->
        <div v-if="loading" class="loading-container">
          <div class="loading-spinner"></div>
          <p>Carregando sessões...</p>
        </div>
        
        <!-- Empty state -->
        <div v-else-if="!loading && activeSessions.length === 0" class="empty-state">
          <HugeiconsIcon 
            :icon="ComputerIcon" 
            size="48" 
            :strokeWidth="1.5"
            class="empty-icon"
          />
          <p>Nenhuma sessão ativa encontrada</p>
        </div>
        
        <!-- Sessions list -->
        <div v-else>
          <div
            v-for="session in activeSessions"
            :key="session.id"
            class="session-item"
            :class="{
              'current-session': session.isCurrent,
              'suspicious-session': suspiciousSessions.includes(session)
            }"
          >
            <div class="session-icon">
              <HugeiconsIcon
                :icon="getDeviceIcon(session)"
                size="24"
                :strokeWidth="1.5"
                class="device-icon"
              />
            </div>
            <div class="session-info">
              <div class="session-header">
                <h4 class="session-title">{{ formatSessionTitle(session) }}</h4>
                <div v-if="session.isCurrent" class="current-badge">
                  <HugeiconsIcon
                    :icon="CheckmarkCircle02Icon"
                    size="14"
                    :strokeWidth="1.5"
                  />
                  <span>Atual</span>
                </div>
                <div v-if="suspiciousSessions.includes(session)" class="suspicious-badge">
                  <HugeiconsIcon
                    :icon="Alert02Icon"
                    size="12"
                    :strokeWidth="1.5"
                  />
                  <span>Suspeita</span>
                </div>
              </div>
              <div class="session-details">
                <span class="session-location">{{ session.locationDisplay }}</span>
                <span class="session-time">{{ session.lastActivityFormatted }}</span>
              </div>
            </div>
            <div class="session-actions">
              <button
                v-if="!session.isCurrent"
                class="terminate-btn"
                @click="terminateSession(session.sessionId)"
                :disabled="terminatingSession === session.sessionId || loading"
              >
                <HugeiconsIcon
                  v-if="terminatingSession !== session.sessionId"
                  :icon="LogoutIcon"
                  size="16"
                  :strokeWidth="1.5"
                />
                <div v-else class="loading-spinner small"></div>
                <span>{{ terminatingSession === session.sessionId ? 'Encerrando...' : 'Encerrar' }}</span>
              </button>
            </div>
          </div>
        </div>
      </div>

      <div class="sessions-footer" v-if="!loading && activeSessions.length > 1">
        <button
          class="terminate-all-btn"
          @click="terminateAllOtherSessions"
          :disabled="isTerminatingAll || loading"
        >
          <HugeiconsIcon
            v-if="!isTerminatingAll"
            :icon="LogoutIcon"
            size="18"
            :strokeWidth="1.5"
          />
          <div v-else class="loading-spinner small"></div>
          <span>{{ isTerminatingAll ? 'Encerrando sessões...' : 'Encerrar Todas as Outras Sessões' }}</span>
        </button>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import { useToast } from '@/services/toast.service'
import { useUserSessions } from '@/composables/useUserSessions'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { HugeiconsIcon } from '@hugeicons/vue'
import {
  ComputerIcon,
  SmartPhone02Icon,
  TabletIcon,
  LogoutIcon,
  CheckmarkCircle02Icon,
  Alert02Icon,
  ShieldIcon
} from '@hugeicons-pro/core-stroke-standard'

// Composables
const toast = useToast()
const {
  activeSessions,
  loading,
  terminatingSession,
  terminatingAll: isTerminatingAll,
  terminateSession,
  terminateAllOtherSessions,
  formatSessionTime,
  loadSessions,
  suspiciousSessions,
  getCurrentSessionInfo
} = useUserSessions()

// Environment detection
const isDevelopment = import.meta.env.MODE === 'development'

// Methods
const getDeviceIcon = (session) => {
  // Usa as propriedades já processadas pelo service
  if (session.isMobile) {
    return SmartPhone02Icon
  }
  if (session.isTablet) {
    return TabletIcon
  }
  return ComputerIcon
}

const formatSessionTitle = (session) => {
  // Prioriza plataforma sobre device genérico
  let platform = session.deviceDisplay || 'Dispositivo'
  const browser = session.browserDisplay || 'Navegador'

  // Se o deviceDisplay for muito genérico (como "Desktop"), tenta usar informações mais específicas
  if (platform === 'Desktop' || platform === 'Dispositivo desconhecido') {
    // Tenta extrair plataforma do browser ou user agent
    const browserLower = browser.toLowerCase()
    if (browserLower.includes('chrome') || browserLower.includes('firefox') || browserLower.includes('safari') || browserLower.includes('edge')) {
      // É provavelmente um desktop, vamos assumir Windows PC como padrão
      platform = 'Windows PC'
    } else {
      platform = 'Desktop'
    }
  }

  // Se a plataforma já inclui informações do navegador, usa apenas a plataforma
  if (platform.toLowerCase().includes(browser.toLowerCase().split(' ')[0])) {
    return platform
  }

  // Senão, combina plataforma e browser de forma inteligente
  return `${platform} • ${browser}`
}

// Report suspicious session
const reportSuspiciousSession = (session) => {
  // TODO: Implementar sistema de denúncia/logging
  toast.addToast(
    `Sessão suspeita reportada: ${session.deviceDisplay} de ${session.locationDisplay}`,
    'info'
  )
  
  // Em produção, isso enviaria dados para o backend para análise
  console.warn('Sessão suspeita reportada:', {
    sessionId: session.sessionId,
    deviceInfo: session.deviceDisplay,
    location: session.locationDisplay,
    timestamp: new Date().toISOString()
  })
}

// Force session validation
const forceValidation = async () => {
  // TODO: Implementar validação de sessão quando disponível no backend
  toast.addToast('Validação de sessão não implementada', 'warn')
}

// Get current session info for display
const currentSessionInfo = getCurrentSessionInfo()

// Lifecycle - O composable já carrega as sessões automaticamente
</script>

<style scoped>
.sessions-content {
  width: 100%;
  max-width: none;
}





/* Security Alert */
.security-alert {
  display: flex;
  align-items: flex-start;
  gap: 12px;
  background: var(--iluria-color-danger-bg);
  border: 1px solid var(--iluria-color-danger-light);
  border-radius: 8px;
  padding: 16px;
  margin-bottom: 24px;
}

.alert-icon {
  color: var(--iluria-color-danger);
  flex-shrink: 0;
  margin-top: 2px;
}

.alert-content h4 {
  color: var(--iluria-color-danger);
  font-size: 14px;
  font-weight: 600;
  margin: 0 0 4px 0;
}

.alert-content p {
  color: var(--iluria-color-text-secondary);
  font-size: 13px;
  margin: 0;
  line-height: 1.4;
}



.sessions-list {
  display: grid;
  gap: 16px;
  margin-bottom: 24px;
}

.session-item {
  display: flex;
  align-items: center;
  gap: 16px;
  padding: 20px;
  background: var(--iluria-color-container-bg);
  border: 1px solid #363636 ;
  border-radius: 8px;
  margin-bottom: 12px;
}

.current-session {
  border-color: #363636 !important;
  background: var(--iluria-color-primary-bg);
}

.session-item.current-session {
  border-color: #363636 !important;
}

.suspicious-session {
  border-color: var(--iluria-color-danger-light);
  background: var(--iluria-color-danger-bg);
}

.session-icon {
  flex-shrink: 0;
}

.device-icon {
  color: var(--iluria-color-text-secondary);
}

.current-session .device-icon {
  color: var(--iluria-color-primary);
}

.suspicious-session .device-icon {
  color: var(--iluria-color-danger);
}

.session-info {
  flex: 1;
  min-width: 0;
}

.session-header {
  display: flex;
  align-items: center;
  gap: 8px;
  margin-bottom: 4px;
}

.session-title {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0;
}

.current-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: var(--iluria-color-primary);
  color: var(--iluria-color-primary-contrast, white);
  font-size: 11px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--iluria-color-primary-dark, var(--iluria-color-primary));
}

.suspicious-badge {
  display: inline-flex;
  align-items: center;
  gap: 4px;
  background: var(--iluria-color-danger);
  color: var(--iluria-color-danger-contrast, white);
  font-size: 11px;
  font-weight: 600;
  padding: 4px 10px;
  border-radius: 16px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
  border: 1px solid var(--iluria-color-danger-dark, var(--iluria-color-danger));
}

.session-details {
  display: flex;
  align-items: center;
  gap: 12px;
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
}

.session-location::after {
  content: "•";
  margin-left: 12px;
  color: var(--iluria-color-border);
}

.session-actions {
  flex-shrink: 0;
}

.terminate-btn {
  display: inline-flex;
  align-items: center;
  gap: 6px;
  padding: 8px 16px;
  background: transparent;
  border: 1px solid var(--iluria-color-danger-light);
  border-radius: 6px;
  color: var(--iluria-color-danger);
  font-size: 13px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
}

.terminate-btn:hover:not(:disabled) {
  background: var(--iluria-color-danger-bg);
  border-color: var(--iluria-color-danger);
}

.terminate-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}



.sessions-footer {
  display: flex;
  justify-content: flex-end;
  margin-top: 1.5rem;
}

.terminate-all-btn {
  display: inline-flex;
  align-items: center;
  gap: 8px;
  padding: 12px 20px;
  background: var(--iluria-color-danger-bg);
  border: 1px solid var(--iluria-color-danger-light);
  border-radius: 8px;
  color: var(--iluria-color-danger);
  font-size: 14px;
  font-weight: 600;
  cursor: pointer;
  transition: all 0.2s ease;
}

.terminate-all-btn:hover:not(:disabled) {
  background: var(--iluria-color-danger);
  color: white;
  border-color: var(--iluria-color-danger);
}

.terminate-all-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

/* Loading and Empty States */
.loading-container {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  gap: 1rem;
  color: var(--iluria-color-text-secondary);
}

.empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 3rem 1rem;
  gap: 1rem;
  color: var(--iluria-color-text-secondary);
}

.empty-icon {
  opacity: 0.5;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--iluria-color-border);
  border-top: 2px solid var(--iluria-color-primary);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

.loading-spinner.small {
  width: 16px;
  height: 16px;
  border-width: 1.5px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Mobile responsiveness */
@media (max-width: 768px) {
  .session-item {
    flex-direction: column;
    align-items: stretch;
    gap: 12px;
    padding: 16px;
  }

  .session-header {
    justify-content: space-between;
  }

  .session-details {
    flex-direction: column;
    align-items: flex-start;
    gap: 4px;
  }

  .session-location::after {
    display: none;
  }

  .session-actions {
    align-self: stretch;
  }

  .terminate-btn {
    width: 100%;
    justify-content: center;
  }

  .terminate-all-btn {
    width: 100%;
    justify-content: center;
  }
}
</style>
