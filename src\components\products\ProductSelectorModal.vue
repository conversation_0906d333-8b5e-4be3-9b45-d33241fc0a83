<template>
  <IluriaModal 
    :visible="visible" 
    :style="{ width: '90vw', maxWidth: '1200px' }" 
    :modal="true"
    :closable="true"
    :dismissableMask="false"
    :closeOnEscape="true"
    header="Selecionar Produtos"
    class="product-selector-modal"
    @update:visible="$emit('update:visible', $event)"
  >
    <div class="modal-content" @click.stop>
      <!-- Filtros de pesquisa -->
      <div class="search-section">
        <div class="search-input">
          <IluriaInputText
            id="product-search"
            v-model="searchQuery"
            :label="t('product.searchProducts')"
            :placeholder="t('product.searchByName')"
            @input="handleSearchInput"
          />
        </div>

        <!-- Filtro de categoria (opcional) -->
        <div v-if="showCategoryFilter" class="category-filter">
          <CategoryDropdown
            v-model="selectedCategoryId"
            :categories="categories"
            :label="t('product.category')"
            :placeholder="t('product.selectCategory')"
            :show-all-option="true"
            :all-option-text="t('product.allCategories')"
            :all-option-value="'all'"
          />
        </div>
      </div>

      <!-- Tabela de produtos -->
      <div class="products-table-container">
        <DataTable
          :value="products"
          :loading="loading"
          dataKey="id"
          v-model:expandedRows="expandedRows"
          :rowClass="rowClass"
          class="products-table">
          <Column :expander="true" headerStyle="width: 3rem"/>
          <Column field="select" headerStyle="width: 3rem">
            <template #header>
            </template>
            <template #body="{data}">
              <div class="checkbox-container" @click.stop>
                <IluriaCheckBox 
                  v-if="!hasVariations(data)"
                  :modelValue="isProductSelected(data)"
                  @update:modelValue="toggleProductSelection(data, $event)"
                />
              </div>
            </template>
          </Column>
          <Column field="image" :header="t('product.image')" headerStyle="width: 4rem">
            <template #body="{data}">
              <div class="product-image">
                <img v-if="getProductImage(data)" :src="getProductImage(data)" alt="Product" class="product-img" />
                <div v-else class="image-placeholder">
                  <i class="fas fa-image"></i>
                </div>
              </div>
            </template>
          </Column>
          <Column field="name" :header="t('product.name')">
            <template #body="{data}">
              <div class="product-info">
                <div class="product-name">{{ data.name }}</div>
                <div class="product-description">
                  {{ data.description ? (data.description.substring(0, 60) + (data.description.length > 60 ? '...' : '')) : '' }}
                </div>
              </div>
            </template>
          </Column>
          <Column field="price" :header="t('product.price')" headerStyle="width: 5rem" style="text-align: right">
            <template #body="{data}">
              <div class="product-price">
                {{ formatPriceRange(data) }}
              </div>
            </template>
          </Column>
          <Column field="stock" :header="t('product.stock')" headerStyle="width: 5rem" style="text-align: right">
            <template #body="{data}">
              <div class="product-stock">
                {{ getTotalStock(data) }}
              </div>
            </template>
          </Column>
          <Column field="variations" :header="t('product.variations')" headerStyle="width: 8rem" style="text-align: center" ">
            <template #body="{data}">
              <div class="variations-badge">
                <span v-if="hasVariations(data)" class="badge-with-variations">
                  {{ data.variations.length }} {{ t('product.variations') }}
                </span>
                <span v-else class="badge-no-variations">
                  {{ t('product.noVariations') }}
                </span>
              </div>
            </template>
          </Column>
          
          <template #empty>
            <div class="empty-state">
              {{ t('product.noProductsFound') }}
            </div>
          </template>

          <template #loading>
            <div class="loading-state">
              <i class="fas fa-spinner fa-spin mr-2"></i>
              {{ t('product.loading') }}
            </div>
          </template>
          
          <template #expansion="{data}">
            <div class="expansion-content">
              <!-- Filtros de atributos -->
              <div class="attribute-filters-section" v-if="getAvailableAttributes(data).length > 0">
                <div class="filter-title">{{ t('product.filterByAttributes') }}:</div>
                <div class="attributes-container">
                  <div v-for="attr in getAvailableAttributes(data)" :key="attr.name" class="attribute-group">
                    <div class="attribute-name">{{ attr.name }}:</div>
                    <div class="attribute-values">
                      <button 
                        v-for="value in attr.values" 
                        :key="value"
                        type="button"
                        class="attribute-value-btn"
                        :class="{ 'active': isAttributeSelected(data.id, attr.name, value) }"
                        @click="toggleAttributeFilter(data.id, attr.name, value)"
                      >
                        {{ value }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
              
              <!-- Tabela de variações -->
              <DataTable
                :value="getFilteredVariations(data)"
                class="variations-table">
                
                <Column field="select" headerStyle="width: 3rem">
                  <template #body="{data: variation}">
                    <div class="checkbox-container" @click.stop>
                      <IluriaCheckBox 
                        :modelValue="isVariationSelected(data.id, variation.attributes)"
                        @update:modelValue="(val) => toggleVariationSelection(data.id, variation.attributes, val)"
                      />
                    </div>
                  </template>
                </Column>
                
                <Column field="image" headerStyle="width: 4rem">
                  <template #body="{data: variation}">
                    <div class="variation-image">
                      <div v-if="getVariationPhoto(variation)" class="variation-img-container">
                        <img :src="getVariationPhoto(variation)" alt="Variation" class="variation-img" />
                      </div>
                      <div v-else-if="variation.attributes?.Cor" 
                           class="color-variation" 
                           :style="{ backgroundColor: variation.attributes.Cor }">
                      </div>
                      <div v-else-if="variation.imageUrl" class="variation-img-container">
                        <img :src="variation.imageUrl" alt="Variation" class="variation-img" />
                      </div>
                      <div v-else class="variation-placeholder">
                        <i class="fas fa-image"></i>
                      </div>
                    </div>
                  </template>
                </Column>
                <Column field="attributes">
                  <template #body="{data: variation}">
                    <div class="variation-attributes">
                      {{ formatVariationAttributes(variation.attributes) }}
                    </div>
                  </template>
                </Column>
                
                <Column field="price" headerStyle="width: 8rem" style="text-align: right">
                  <template #body="{data: variation}">
                    <div class="variation-price">
                      {{ formatPrice(variation.price) }}
                    </div>
                  </template>
                </Column>
                
                <Column field="stock" headerStyle="width: 8rem" style="text-align: right">
                  <template #body="{data: variation}">
                    <div class="variation-stock">
                      {{ variation.stockQuantity || 0 }}
                    </div>
                  </template>
                </Column>
              </DataTable>
            </div>
          </template>
        </DataTable>
      </div>

      <!-- Paginação -->
      <div class="pagination-section">
        <div class="selection-count">
          {{ selectedProductIds.length + selectedVariationIds.length }} {{ t('product.productsSelected') }}
        </div>
        <IluriaPagination 
          v-if="totalPages > 0"
          :current-page="currentPage"
          :total-pages="totalPages"
          @go-to-page="changePage"
        />
      </div>
    </div>

    <template #footer>
      <div class="modal-footer">
        <IluriaButton @click="closeModal" variant="outline">
          {{ t('cancel') }}
        </IluriaButton>
        <IluriaButton 
          @click="addSelectedProducts" 
          :disabled="selectedProductIds.length === 0 && selectedVariationIds.length === 0"
          :loading="addingProducts"
        >
          <i class="fas fa-plus mr-2"></i>
          {{ props.addButtonLabel || t('product.addProduct') }}
        </IluriaButton>
      </div>
    </template>
  </IluriaModal>
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import productsApi from '@/services/product.service'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaPagination from '@/components/iluria/IluriaPagination.vue'
import CategoryDropdown from '@/components/iluria/CategoryDropdown.vue'
import DataTable from 'primevue/datatable'
import Column from 'primevue/column'

const { t } = useI18n()

const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  },
  // Lista de produtos pré-selecionados
  preSelectedProducts: {
    type: Array,
    default: () => []
  },
  // Define se o seletor permite múltipla seleção ou apenas um item
  multiSelect: {
    type: Boolean,
    default: true
  },
  // Define se deve mostrar o filtro de categorias
  showCategoryFilter: {
    type: Boolean,
    default: false
  },
  // Texto dinâmico para o botão de ação
  addButtonLabel: {
    type: String,
    default: null
  }
})

const emit = defineEmits(['update:visible', 'add-products', 'select'])

const loading = ref(false)
const addingProducts = ref(false)
const searchQuery = ref('')
const products = ref([])
const selectedProductIds = ref([])
const selectedVariationIds = ref([])
const expandedRows = ref([])
const pageSize = ref(5)
const totalRecords = ref(0)
const currentPage = ref(0)
const attributeFilters = ref({})
const categories = ref([])
const selectedCategoryId = ref(null)
let searchTimeout = null

const totalPages = computed(() => {
  return Math.ceil(totalRecords.value / pageSize.value)
})

const allSelected = computed(() => {
  return products.value.length > 0 && selectedProductIds.value.length === products.value.length
})

const handleSearchInput = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    searchProducts()
  }, 400)
}

const searchProducts = async () => {
  try {
    loading.value = true

    
    const params = {
      name: searchQuery.value || undefined,
      page: currentPage.value,
      size: pageSize.value
    }

    // Adicionar filtro de categoria se estiver selecionada
    if (selectedCategoryId.value &&
        selectedCategoryId.value !== 'all' &&
        selectedCategoryId.value !== null &&
        selectedCategoryId.value !== '') {
      params.categoryId = selectedCategoryId.value
    }

    const response = await productsApi.getProductsWithVariations(params)


    if (response && response.data) {
      const responseData = response.data
      const pageInfo = responseData.page || {}

      products.value = responseData.content || responseData || []
      totalRecords.value = pageInfo.totalElements || products.value.length

    } else {
      products.value = []
      totalRecords.value = 0
    }
  } catch (error) {
    console.error('Erro ao buscar produtos:', error)
    // Fallback para produtos mock em caso de erro
    products.value = [
      {
        id: '1',
        name: 'Produto de Exemplo 1',
        price: 99.99,
        imageUrl: '',
        variations: []
      },
      {
        id: '2',
        name: 'Produto de Exemplo 2',
        price: 149.99,
        imageUrl: '',
        variations: []
      }
    ]
    totalRecords.value = products.value.length
  } finally {
    loading.value = false
  }
}

const changePage = (page) => {
  currentPage.value = page
  searchProducts()
}


const closeModal = () => {
  emit('update:visible', false)
  selectedProductIds.value = []
  selectedVariationIds.value = []
  attributeFilters.value = {}
}


const isProductSelected = (product) => {
  return selectedProductIds.value.includes(product.id)
}

const isVariationSelected = (productId, attributes) => {
  const variationId = generateVariationId(productId, attributes)
  return selectedVariationIds.value.includes(variationId)
}

const toggleProductSelection = (product, isChecked) => {
  // Se for modo de seleção única, limpa todas as seleções anteriores
  if (!props.multiSelect && isChecked) {
    selectedProductIds.value = []
    selectedVariationIds.value = []
  }

  if (isChecked) {
    if (!selectedProductIds.value.includes(product.id)) {
      selectedProductIds.value.push(product.id)
    }
  } else {
    const index = selectedProductIds.value.indexOf(product.id)
    if (index !== -1) {
      selectedProductIds.value.splice(index, 1)
    }
    
    if (hasVariations(product)) {
      selectedVariationIds.value = selectedVariationIds.value.filter(id => {
        return !id.startsWith(`${product.id}_`)
      })
    }
  }
}

const toggleVariationSelection = (productId, attributes, isChecked) => {
  const variationId = generateVariationId(productId, attributes)
  
  // Se for modo de seleção única, limpa todas as seleções anteriores
  if (!props.multiSelect && isChecked) {
    selectedProductIds.value = []
    selectedVariationIds.value = []
  }
  
  if (isChecked) {
    if (!selectedVariationIds.value.includes(variationId)) {
      selectedVariationIds.value.push(variationId)
    }
  } else {
    const index = selectedVariationIds.value.indexOf(variationId)
    if (index !== -1) {
      selectedVariationIds.value.splice(index, 1)
    }
  }
}

const getVariationPhoto = (variation) => {
  if (variation.photos && variation.photos.length > 0) {
    const photo = variation.photos.find(p => p.position === 0) || variation.photos[0];
    if (photo) {
      // Handle both URL string and object with url property
      if (typeof photo === 'string') {
        return photo;
      } else if (photo.url) {
        return photo.url;
      }
    }
  }
  return null;
};

const getProductImage = (product) => {
  if (product.variations && product.variations.length > 0) {
    const variation = product.variations.find(v => v.photos && v.photos.length > 0);
    if (variation) {
      return getVariationPhoto(variation);
    }
  }
  
  if (product.photos && product.photos.length > 0) {
    const photo = product.photos.find(p => p.position === 0) || product.photos[0];
    if (photo) {
      // Handle both URL string and object with url property
      if (typeof photo === 'string') {
        return photo;
      } else if (photo.url) {
        return photo.url;
      }
    }
  }
  
  return product.imageUrl || null;
};


const getAvailableAttributes = (product) => {
  if (!hasVariations(product)) return []
  
  const attributes = {}
  
  product.variations.forEach(variation => {
    if (variation.attributes) {
      Object.entries(variation.attributes).forEach(([key, value]) => {
        if (!attributes[key]) {
          attributes[key] = new Set()
        }
        attributes[key].add(value)
      })
    }
  })
  
  return Object.entries(attributes).map(([name, valuesSet]) => ({
    name,
    values: Array.from(valuesSet)
  }))
}

const toggleAttributeFilter = (productId, attrName, value) => {
  if (!attributeFilters.value[productId]) {
    attributeFilters.value[productId] = {}
  }
  
  if (!attributeFilters.value[productId][attrName]) {
    attributeFilters.value[productId][attrName] = []
  }
  
  const index = attributeFilters.value[productId][attrName].indexOf(value)
  
  if (index === -1) {
    attributeFilters.value[productId][attrName].push(value)
  } else {
    attributeFilters.value[productId][attrName].splice(index, 1)
    
    if (attributeFilters.value[productId][attrName].length === 0) {
      delete attributeFilters.value[productId][attrName]
      
      if (Object.keys(attributeFilters.value[productId]).length === 0) {
        delete attributeFilters.value[productId]
      }
    }
  }
}

const isAttributeSelected = (productId, attrName, value) => {
  return attributeFilters.value[productId]?.[attrName]?.includes(value) || false
}

const getFilteredVariations = (product) => {
  if (!hasVariations(product)) return []
  
  if (!attributeFilters.value[product.id] || Object.keys(attributeFilters.value[product.id]).length === 0) {
    return product.variations
  }
  
  return product.variations.filter(variation => {
    if (!variation.attributes) return false
    
    return Object.entries(attributeFilters.value[product.id]).every(([attrName, selectedValues]) => {
      const variationValue = variation.attributes[attrName]
      return selectedValues.includes(variationValue)
    })
  })
}

const addSelectedProducts = async () => {
  try {
    addingProducts.value = true
    const selectedProducts = []

    // Processar produtos sem variações selecionados
    for (const productId of selectedProductIds.value) {
      const product = products.value.find(p => p.id === productId)
      
      if (!product) continue
      
      if (hasVariations(product)) {
        const selectedVariations = selectedVariationIds.value.filter(varId => varId.startsWith(`${product.id}_`))
        
        if (selectedVariations.length === 0) {
          product.variations.forEach(variation => {
            const variationTitle = formatVariationAttributes(variation.attributes)
            
            selectedProducts.push({
              // Campos necessários para o frontend
              id: product.id,
              productId: product.id,
              name: product.name,
              productName: product.name,
              sku: product.sku || '',
              price: variation.price,
              originalPrice: variation.originalPrice || variation.price,
              quantity: 1,
              variationTitle: variationTitle,
              variationId: product.id || null,
              shippingTime: variation.shippingTime || 3,
              productType: 'PHYSICAL',
              stockQuantity: variation.stockQuantity || 0,
              imageUrl: getProductImage(product),
              photos: product.photos || [],
              variations: product.variations || [],
              variation: {
                attributes: variation.attributes || {}
              },
              // Campos adicionais para garantir compatibilidade com o backend
              position: 0
            })
          })
        } 
      } else {
        selectedProducts.push({
          id: product.id,
          productId: product.id,
          name: product.name,
          productName: product.name,
          sku: product.sku || '',
          price: product.price,
          originalPrice: product.originalPrice || product.price,
          quantity: 1,
          variationTitle: '',
          variationId: null,
          shippingTime: product.shippingTime || 3,
          productType: 'PHYSICAL',
          stockQuantity: product.stockQuantityTotal || 0,
          imageUrl: getProductImage(product),
          photos: product.photos || [],
          variations: product.variations || [],
          position: 0
        })
      }
    }
    
    // Processar variações selecionadas
    for (const variationId of selectedVariationIds.value) {
      const [productId] = variationId.split('_')
      const product = products.value.find(p => p.id === parseInt(productId) || p.id === productId)
      
      if (product && hasVariations(product)) {
        const variation = product.variations.find(v => {
          const varId = generateVariationId(product.id, v.attributes)
          return varId === variationId
        })
        
        if (variation) {
          const variationTitle = formatVariationAttributes(variation.attributes)
          
          selectedProducts.push({
            id: product.id,
            productId: product.id,
            name: product.name,
            productName: product.name,
            sku: product.sku || '',
            price: variation.price,
            originalPrice: variation.originalPrice || variation.price,
            quantity: 1,
            variationTitle: variationTitle,
            variationId: product.id || null,
            shippingTime: variation.shippingTime || 3,
            productType: 'PHYSICAL',
            stockQuantity: variation.stockQuantity || 0,
            imageUrl: getProductImage(product),
            photos: product.photos || [],
            variations: product.variations || [],
            variation: {
              attributes: variation.attributes || {}
            },
            position: 0
          })
        }
      }
    }
    

    emit('add-products', selectedProducts)
    emit('select', selectedProducts)

    closeModal()
  } catch (error) {
    console.error('Erro ao adicionar produtos:', error)
  } finally {
    addingProducts.value = false
  }
}

const rowClass = (data) => {
  return {
    'has-variation': hasVariations(data)
  };
};

const hasVariations = (product) => {
  return product.hasVariation && product.variations && product.variations.length > 0
}

// Helper function to check if a product is currently expanded
const isExpanded = (product) => {
  return expandedRows.value.some(row => row.id === product.id);
};

// Toggle expansion state for a product
const toggleExpansion = (product) => {
  if (!hasVariations(product)) return;
  
  const index = expandedRows.value.findIndex(row => row.id === product.id);
  if (index >= 0) {
    expandedRows.value.splice(index, 1);
  } else {
    expandedRows.value.push(product);
  }
};

const getTotalStock = (product) => {
    if (!product) return 0
    
    if (hasVariations(product)) {
        return product.variations.reduce((total, variation) => {
            return total + (variation.stockQuantity || 0)
        }, 0)
    }
    
    return product.stockQuantityTotal || 0
}

const formatPriceRange = (product) => {
  if (!product) return ''

  if (!hasVariations(product)) {
    return formatPrice(product.price)
  }

  const prices = product.variations.map(v => v.price)
  const minPrice = Math.min(...prices)
  const maxPrice = Math.max(...prices)

  if (minPrice === maxPrice) {
    return formatPrice(minPrice)
  }

  return `${formatPrice(minPrice)} ~ ${formatPrice(maxPrice)}`
}

const formatPrice = (value) => {
  return new Intl.NumberFormat('pt-BR', {
    style: 'currency',
    currency: 'BRL'
  }).format(value)
}

const formatVariationAttributes = (attributes) => {
  if (!attributes) return ''
  return Object.entries(attributes).map(([key, value]) => `${key}: ${value}`).join(', ')
}

const generateVariationId = (productId, attributes) => {
  if (!attributes) return `${productId}_noattr`
  
  const sortedAttributes = Object.entries(attributes)
    .sort(([keyA], [keyB]) => keyA.localeCompare(keyB))
    .map(([key, value]) => `${key}:${value}`)
    .join('|')
  
  return `${productId}_${sortedAttributes}`
}

// Função para carregar categorias
const loadCategories = async () => {
  if (!props.showCategoryFilter) return

  try {
    // Importar o serviço de categorias dinamicamente para evitar dependência circular
    const { categoryService } = await import('@/services/category.service')
    const response = await categoryService.fetchCategories()
    categories.value = response || []
  } catch (error) {
    console.error('Erro ao carregar categorias:', error)
    categories.value = []
  }
}

// Handler para mudança de categoria (não usado mais, mantido para compatibilidade)
const handleCategoryChange = (categoryId) => {
  selectedCategoryId.value = categoryId
  currentPage.value = 0 // Reset para primeira página
  searchProducts()
}

// Watcher para mudanças na categoria selecionada
watch(selectedCategoryId, () => {
  currentPage.value = 0 // Reset para primeira página
  searchProducts()
})

watch(() => props.visible, (newValue) => {
  if (newValue) {

    // Reset values when modal opens
    currentPage.value = 0
    searchQuery.value = ''
    selectedProductIds.value = []
    selectedVariationIds.value = []
    attributeFilters.value = {}
    selectedCategoryId.value = 'all'

    // Load categories if filter is enabled
    if (props.showCategoryFilter) {
      loadCategories()
    }

    // Load products
    searchProducts()
  }
}, { immediate: true })
</script>

<style scoped>
/* Modal container */
.modal-content {
  padding: 24px;
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
  background-color: var(--iluria-color-container-bg) !important;
}

/* Search section */
.search-section {
  margin-bottom: 24px;
  display: flex;
  gap: 16px;
  align-items: baseline;
  background: transparent !important;
}

.search-input {
  flex: 1;
  background: transparent !important;
}

.category-filter {
  min-width: 250px;
  background: transparent !important;
}

/* Garantir que os labels tenham a mesma altura e espaçamento */
.search-input :deep(label),
.category-filter :deep(label) {
  height: 20px;
  margin-bottom: 8px;
  display: block;
  line-height: 20px;
}

/* Garantir que os containers dos campos tenham a mesma estrutura */
.search-input :deep(.form-field),
.search-input :deep(.input-wrapper),
.search-input :deep(.field-wrapper),
.category-filter {
  display: flex;
  flex-direction: column;
}

/* Padronizar altura dos campos - ajustar para altura do IluriaInputText */
.search-input :deep(.custom-input-group),
.category-filter :deep(button) {
  height: 42px !important;
  min-height: 42px !important;
  max-height: 42px !important;
  box-sizing: border-box !important;
}

/* Forçar altura específica do container do IluriaInputText */
.search-input :deep(.custom-input-group) {
  height: 42px !important;
}

/* Garantir que o botão do CategoryDropdown tenha a mesma altura */
.category-filter :deep(button) {
  height: 42px !important;
  min-height: 42px !important;
  max-height: 42px !important;
  display: flex !important;
  align-items: center !important;
  box-sizing: border-box !important;
  padding: 0 12px !important;
}

/* Sobrescrever altura específica do IluriaInputText para alinhar com CategoryDropdown */
.search-section .search-input :deep(.custom-input-group) {
  height: 42px !important;
  min-height: 42px !important;
  max-height: 42px !important;
}

/* Garantir que ambos os campos tenham exatamente a mesma linha de base */
.search-section .search-input,
.search-section .category-filter {
  display: flex;
  flex-direction: column;
  justify-content: flex-end;
}

/* Alinhamento final - forçar mesma altura visual dos campos de input */
.search-section .search-input :deep(.custom-input-group),
.search-section .category-filter :deep(button[type="button"]) {
  height: 42px !important;
  min-height: 42px !important;
  max-height: 42px !important;
  border-width: 1px !important;
  box-sizing: border-box !important;
  display: flex !important;
  align-items: center !important;
}

/* Remove any backgrounds from input labels and wrappers */
.search-input :deep(.form-field),
.search-input :deep(.input-wrapper),
.search-input :deep(.field-wrapper),
.search-input :deep(label),
.search-input :deep(.iluria-input-wrapper),
.search-input :deep(.iluria-label),
.search-input :deep(.form-group),
.search-input :deep(.input-container) {
  background: transparent !important;
}

/* Override any remaining white backgrounds */
.modal-content :deep(.bg-white),
.modal-content :deep([style*="background-color: white"]),
.modal-content :deep([style*="background-color: #fff"]),
.modal-content :deep([style*="background-color: #ffffff"]) {
  background-color: var(--iluria-color-container-bg) !important;
}

/* Products table container */
.products-table-container {
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  overflow: hidden;
  background: var(--iluria-color-container-bg);
  box-shadow: var(--iluria-shadow-sm);
}

/* Product image */
.product-image {
  width: 48px;
  height: 48px;
  background: var(--iluria-color-surface);
  border-radius: 6px;
  overflow: hidden;
  border: 1px solid var(--iluria-color-border);
}

.product-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.image-placeholder {
  width: 100%;
  height: 100%;
  display: flex;
  align-items: center;
  justify-content: center;
  color: var(--iluria-color-text-muted);
}

/* Product info */
.product-info {
  padding-left: 8px;
}

.product-name {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  margin-bottom: 4px;
}

.product-description {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

/* Product price */
.product-price {
  font-weight: 600;
  color: var(--iluria-color-primary);
  text-align: right;
}

/* Product stock */
.product-stock {
  text-align: right;
  color: var(--iluria-color-text-primary);
}

/* Variations badges */
.variations-badge {
  text-align: center;
}

.badge-with-variations {
  display: inline-block;
  padding: 4px 8px;
  background: var(--iluria-color-primary-light);
  color: var(--iluria-color-primary-dark);
  border-radius: 16px;
  font-size: 12px;
  white-space: nowrap;
}

.badge-no-variations {
  display: inline-block;
  padding: 4px 8px;
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text-secondary);
  border-radius: 16px;
  font-size: 12px;
  white-space: nowrap;
  border: 1px solid var(--iluria-color-border);
}

/* Checkbox containers */
.checkbox-container {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0;
  min-height: 40px;
  width: 100%;
}

.checkbox-container :deep(.checkbox-label) {
  margin: 0 !important;
  justify-content: center;
}

.checkbox-container :deep(.checkbox-container) {
  justify-content: center;
  align-items: center;
}

.checkbox-container :deep(.checkbox-wrapper) {
  margin: 0 auto;
}

/* Empty and loading states */
.empty-state {
  padding: 48px 24px;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

.loading-state {
  padding: 48px 24px;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

/* Expansion content */
.expansion-content {
  border-top: 1px solid rgba(var(--iluria-color-border-rgb), 0.3);
  background: var(--iluria-color-surface);
  padding: 16px;
  margin: 0 16px;
  border-left: none;
  border-right: none;
}

/* Attribute filters */
.attribute-filters-section {
  margin-bottom: 16px;
  padding-bottom: 16px;
  border-bottom: 1px solid rgba(var(--iluria-color-border-rgb), 0.3);
  margin-left: 16px;
  margin-right: 16px;
}

.filter-title {
  font-size: 14px;
  font-weight: 500;
  margin-bottom: 12px;
  color: var(--iluria-color-text-primary);
}

.attributes-container {
  display: flex;
  flex-wrap: wrap;
  gap: 16px;
}

.attribute-group {
  margin-bottom: 12px;
}

.attribute-name {
  font-size: 12px;
  font-weight: 500;
  margin-bottom: 6px;
  color: var(--iluria-color-text-secondary);
}

.attribute-values {
  display: flex;
  flex-wrap: wrap;
  gap: 6px;
}

.attribute-value-btn {
  padding: 4px 12px;
  font-size: 12px;
  border-radius: 16px;
  border: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  color: var(--iluria-color-text-primary);
  transition: all 0.2s ease;
  cursor: pointer;
}

.attribute-value-btn:hover {
  background: var(--iluria-color-surface);
  border-color: var(--iluria-color-primary-light);
}

.attribute-value-btn.active {
  background: var(--iluria-color-primary);
  color: white;
  border-color: var(--iluria-color-primary);
}

/* Variation table */
.variations-table {
  background: var(--iluria-color-container-bg) !important;
  margin: 0 16px;
}

/* Aplicar bordas mais claras na tabela de variações */
.variations-table :deep(.p-datatable-tbody > tr > td) {
  border-color: rgba(var(--iluria-color-border-rgb), 0.3) !important;
}

.variations-table :deep(.p-datatable-thead > tr > th) {
  border-color: rgba(var(--iluria-color-border-rgb), 0.3) !important;
}

/* Variation image */
.variation-image {
  width: 40px;
  height: 40px;
  background: var(--iluria-color-surface);
  border-radius: 4px;
  overflow: hidden;
  display: flex;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--iluria-color-border);
}

.variation-img-container {
  width: 100%;
  height: 100%;
}

.variation-img {
  width: 100%;
  height: 100%;
  object-fit: cover;
}

.color-variation {
  width: 100%;
  height: 100%;
  border: 2px solid var(--iluria-color-border);
}

.variation-placeholder {
  color: var(--iluria-color-text-muted);
}

/* Variation info */
.variation-attributes {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
}

.variation-price {
  text-align: right;
  font-weight: 600;
  color: var(--iluria-color-primary);
}

.variation-stock {
  text-align: right;
  color: var(--iluria-color-text-primary);
}

/* Pagination section */
.pagination-section {
  margin-top: 24px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.selection-count {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
}

/* Modal footer */
.modal-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}

/* DataTable theme overrides */
:deep(.p-datatable) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border: none !important;
}

:deep(.p-datatable-header) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
  border: none !important;
}

:deep(.p-datatable-thead > tr > th) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
  font-weight: 600;
  font-size: 14px !important;
  padding: 12px 8px !important;
}

:deep(.p-datatable-tbody > tr > td) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
  padding: 12px 8px !important;
}

:deep(.p-datatable-tbody > tr) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
  transition: background-color 0.2s ease !important;
}

:deep(.p-datatable-tbody > tr:hover) {
  background: var(--iluria-color-surface) !important;
}

:deep(.p-datatable-tbody > tr:hover > td) {
  background: var(--iluria-color-surface) !important;
}

:deep(.p-datatable-tbody > tr.p-datatable-row-expansion) {
  background: var(--iluria-color-surface) !important;
}

:deep(.p-datatable-tbody > tr.p-datatable-row-expansion > td) {
  background: var(--iluria-color-surface) !important;
}

:deep(.p-row-toggler) {
  color: var(--iluria-color-text-secondary) !important;
  background: transparent !important;
  border: none !important;
}

:deep(.p-datatable-row-toggle-button) {
  display: none !important;
}

:deep(.has-variation .p-datatable-row-toggle-button) {
  display: inline-flex !important;
}




/* Improve row expansion styling */
:deep(.p-datatable-row-expansion) {
  border: none !important;
}

:deep(.p-datatable-row-expansion td) {
  border-top: none !important;
}



/* Specific overrides for stubborn white borders */
:deep(.p-dialog) {
  border: 1px solid var(--iluria-color-border) !important;
  box-shadow: var(--iluria-shadow-xl) !important;
}

/* Force override any remaining white elements */
:deep(.p-dialog-mask) {
  background-color: rgba(0, 0, 0, 0.4) !important;
}

:deep(.p-component) {
  border-color: var(--iluria-color-border) !important;
}


/* Responsive design */
@media (max-width: 768px) {
  .modal-content {
    padding: 16px;
  }
  
  .search-section {
    margin-bottom: 16px;
  }
  
  .pagination-section {
    margin-top: 16px;
    flex-direction: column;
    gap: 12px;
    align-items: stretch;
  }
  
  .attributes-container {
    gap: 12px;
  }
  
  .attribute-values {
    gap: 4px;
  }
}

@media (max-width: 480px) {
  .product-info {
    padding-left: 4px;
  }
  
  .product-name {
    font-size: 14px;
  }
  
  .product-description {
    font-size: 11px;
  }
  
  .attribute-value-btn {
    padding: 3px 8px;
    font-size: 11px;
  }
}
</style>
