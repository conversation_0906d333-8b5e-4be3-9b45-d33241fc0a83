{"yes": "<PERSON>m", "no": "Não", "save": "<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "error": "Erro", "success": "Sucesso", "update": "<PERSON><PERSON><PERSON><PERSON>", "promotionsTitle": "Promoções", "promotionsSubtitle": "Gerencie e configure promoções para impulsionar suas vendas", "promotionsListTitle": "Lista de Promoções", "promotionsListSubtitle": "Visualize e gerencie todas as promoções ativas e inativas", "add": "Nova Promoção", "searchPlaceholder": "Buscar promoções...", "loadingPromotions": "Carregando promoções...", "noPromotions": "Nenhuma promoção encontrada", "noPromotionsDescription": "Você ainda não possui promoções cadastradas. Crie a primeira promoção para começar a impulsionar suas vendas.", "createFirstPromotion": "Criar Primeira Promoção", "nameHeader": "Nome", "typeHeader": "Tipo", "discountValueHeader": "Valor do Desconto", "activeHeader": "Ativa", "combinePromosHeader": "Combinar Promoções", "createdHeader": "<PERSON><PERSON><PERSON> em", "actionsHeader": "Ações", "editPromotion": "Editar promoção", "deletePromotion": "Excluir promoção", "viewPromotion": "Visualizar promoção", "confirmDeleteTitle": "Confirmar exclusão", "confirmDeleteMessage": "Tem certeza que deseja excluir a promoção {name}? Esta ação não pode ser desfeita.", "deleteSuccess": "Promoção excluída com sucesso", "deleteError": "Erro ao excluir promoção", "loadError": "Erro ao carregar promoções", "freeShippingValue": "Frete <PERSON>", "types": {"simpleDiscount": "Desconto Simples", "progressiveDiscount": "Desconto Progressivo", "freeShipping": "Frete <PERSON>", "gift": "<PERSON><PERSON><PERSON>", "crossSelling": "<PERSON><PERSON><PERSON>", "firstPurchase": "Primeira Compra"}, "editor": {"loading": "Carregando promoção...", "createTitle": "Criar Nova Promoção", "createSubtitle": "Configure uma nova promoção para impulsionar suas vendas", "editTitle": "Editar Promoção", "editSubtitle": "Atualize as configurações da promoção", "basicDataTitle": "Dados Básicos", "basicDataSubtitle": "Informações principais da promoção", "promotionName": "Nome da Promoção", "promotionNamePlaceholder": "Ex: Black Friday 2024", "promotionType": "Tipo de Promoção", "selectPromotionType": "Selecione o tipo de promoção", "selectDiscountType": "Selecione o tipo de desconto", "discountConfigTitle": "Configuração de Desconto", "discountConfigSubtitle": "Defina o tipo e valor do desconto", "freeShippingTitle": "Configuração de Frete Grátis", "freeShippingSubtitle": "Configure as condições para frete gratuito", "progressiveDiscountTitle": "Configuração de Desconto Progressivo", "progressiveDiscountSubtitle": "Configure descontos que aumentam com a quantidade ou valor", "giftConfigTitle": "Configuração de Brinde", "giftConfigSubtitle": "Defina os brindes e suas condições", "crossSellingTitle": "Configuração de Venda Cruzada", "crossSellingSubtitle": "Configure produtos sugeridos e gatilhos", "minimumRequirementsTitle": "Requisitos <PERSON>", "minimumRequirementsSubtitle": "<PERSON><PERSON>a as condições mínimas para aplicar a promoção", "generalConditionsTitle": "Condições Gerais", "generalConditionsSubtitle": "Configure período de validade e outras condições", "simpleDiscount": "Desconto Simples", "progressiveDiscount": "Desconto Progressivo", "crossSelling": "<PERSON><PERSON><PERSON>", "firstPurchase": "Primeira Compra", "freeShipping": "Frete <PERSON>", "gift": "<PERSON><PERSON><PERSON>", "nameRequired": "Nome da promoção é obrigatório", "typeRequired": "Tipo de promoção é obrigatório", "createSuccess": "Promoção criada com sucesso", "updateSuccess": "Promoção atualizada com sucesso", "saveError": "Erro ao salvar promoção", "loadError": "Erro ao carregar dados da promoção", "noMinimumRequired": "Sem requisitos mínimos", "minimumOrderValue": "Valor mínimo do pedido", "minimumItemQuantity": "Quantidade mínima de itens", "minimumRequirements": "Requisitos <PERSON>", "minimumOrderPlaceholder": "Ex: 100.00", "minimumItemsPlaceholder": "Ex: 5 unidades", "minQuantity": "Quantidade Mínima", "minOrderValue": "Valor Mínimo do Pedido", "discount": "Desconto", "productsPlaceholder": "Ex: 3 unidades", "progressiveDiscountType": "Tipo de Desconto Progressivo", "discountByQuantity": "Desconto por Quantidade", "discountByOrderValue": "Desconto por Valor do Pedido", "addProgressiveTier": "Adicionar Nova Faixa", "giftType": "<PERSON><PERSON><PERSON>", "simplifiedGift": "Brinde Simplificado", "productGift": "Produto como Brinde", "giftDescription": "Descrição do Brinde", "product": "Produ<PERSON>", "addGift": "<PERSON><PERSON><PERSON><PERSON>", "discountToApply": "Desconto a Aplicar", "discountToApplyDescription": "Desconto aplicado nos produtos sugeridos", "productToReceiveDiscount": "Produto que Receberá o Desconto", "highestValueProduct": "<PERSON><PERSON><PERSON>", "lowestValueProduct": "Produto de Menor Valor", "trigger": "<PERSON><PERSON><PERSON><PERSON>", "triggerDescription": "Define quando a promoção será ativada", "allStore": "Toda a Loja", "categories": "Categorias", "products": "<PERSON><PERSON><PERSON>", "discountConfiguration": "Configuração de Desconto", "discountType": "Tipo de Desconto", "discountValue": "Valor do Desconto", "discountValuePlaceholder": "Ex: 10", "discountTypeDescription": "Selecione o tipo de desconto para configurar o valor", "percentage": "Percentual", "fixed": "Fixo", "selectMinimumRequirementType": "Selecione o tipo de requisito mínimo"}}