{"title": "Shipping", "description": "Configure shipping settings and add extra costs to calculated shipping fees to cover packaging, taxes and other costs.", "increaseShippingTitle": "Shipping Settings", "increaseShippingSubtitle": "Add fixed values or percentages to calculated shipping", "pickupTitle": "Store Pickup", "pickupSubtitle": "Configure the store pickup option", "correiosTitle": "Correios Integration", "correiosSubtitle": "Configure Correios integration to offer SEDEX and PAC", "localShippingTitle": "Custom Delivery", "localShippingSubtitle": "Configure deliveries to specific regions", "saveButton": "Save", "savedSuccess": "Shipping settings saved successfully", "savedError": "Error saving shipping settings", "validationError": "Please correct validation errors before saving.", "loadError": "Error loading shipping settings.", "addFixedValueLabel": "Add a fixed value to shipping price?", "fixedValueLabel": "Fixed value to add to shipping:", "addPercentageLabel": "Add a percentage to shipping price?", "percentageLabel": "Percentage to add:", "fixedValuePlaceholder": "Ex: 10.00", "percentagePlaceholder": "0", "atLeastOneOption": "Select at least one option to add.", "savedLocallyMessage": "Shipping settings saved locally. Will be synchronized when server is available.", "offerPickupLabel": "Offer 'Store Pickup' as a shipping option in your store?", "pickupOption": "Offer 'Store Pickup' as a shipping option in your store?", "pickupDescription": "This shipping option can be used if the customer wants to pick up the order at the store, atelier, office, etc.", "pickupDetailedDescription": "This shipping option can be used if the customer wants to pick up the order at the store, atelier, office, etc. All orders with this shipping option will have their shipping automatically set to zero, and payment can be made online at the store at the time of purchase.", "pickupDescriptionLabel": "Enter the description for the store pickup option. Example: \"Store Pickup\":", "pickupDescriptionPlaceholder": "Enter how the customer can pick up the order at the store", "yesLabel": "Yes", "noLabel": "No", "fixedValueRequired": "Fixed value is required when this option is enabled.", "percentageRequired": "Percentage is required when this option is enabled.", "pickupDescriptionRequired": "Pickup description is required when this option is enabled.", "cepRangeRequired": "At least one ZIP code range is required.", "cepRangeFieldsRequired": "All ZIP code ranges must be completely filled.", "cepRangeInvalid": "In region '{name}', the start ZIP code must be lower than the end ZIP code.", "originCep": {"title": "Origin CEP", "placeholder": "Enter origin CEP", "createSuccess": "Origin CEP created successfully", "updateSuccess": "Origin CEP updated successfully", "createError": "Error creating origin CEP", "updateError": "Error updating origin CEP", "loadError": "Error loading origin CEP"}, "pickupConfig": {"subtitle": "Configure the local pickup option"}, "zipCodeRestrictionConfig": {"title": "Configure Shipping Restrictions by CEP Ranges", "subtitle": "Define regions that do not receive delivery or have special restrictions"}, "correiosConfig": {"title": "Correios Integration", "subtitle": "Configure the integration with Correios to offer SEDEX and PAC"}, "localShipping": {"title": "Local Delivery", "description": "Configure this shipping option if you want to offer deliveries via motorcycle courier, messenger, own delivery or any other local delivery option.", "enableLabel": "Offer \"Local Delivery\" as a shipping method in your store?", "descriptionLabel": "Enter the description for the local delivery option (example: Motorcycle Courier):", "descriptionPlaceholder": "Motorcycle Courier", "uploadTitle": "File Upload", "uploadDescription": "Upload a CSV file with ZIP code ranges for local delivery.", "uploadLabel": "Select a CSV file", "uploadHint": "The file must contain columns: start_zipcode, end_zipcode, start_weight, end_weight, price, delivery_time", "uploadSuccess": "File uploaded successfully!", "uploadError": "Error uploading file. Check the format and try again.", "rangesTitle": "Registered ZIP code ranges", "startZipcode": "Start ZIP code", "endZipcode": "End ZIP code", "weight": "Weight (kg)", "price": "Price", "deliveryTime": "Deadline", "day": "day", "days": "days", "discountRules": "Discount rules", "discountDescription": "Configure discount rules for local delivery.", "noDiscount": "No discount", "freeShipping": "Free shipping", "freeShippingMinValue": "Free shipping for orders above a value", "discountAboveValue": "Shipping discount for orders above a value", "minimumOrderValue": "Minimum order value", "percentageDiscount": "Percentage discount", "noDiscountDescription": "Orders will be delivered normally without discount.", "freeShippingDescription": "Orders above a value will have free shipping.", "freeShippingMinValueDescription": "Orders above a value will have free shipping.", "minimumOrderValueDescription": "Orders above a value will have shipping discount.", "percentageDiscountDescription": "Orders above a value will have shipping discount.", "minOrderValue": "Minimum order value", "discountPercentage": "Discount", "dragAndDrop": "Drag and drop the CSV file with ZIP code ranges for local delivery here or click to load.", "chooseFile": "Choose file"}, "zipCodeRestriction": {"title": "Restrict Deliveries by Region", "subtitle": "Configure which regions will be served", "description": "Here you can define which ZIP code ranges will be served by the store. If you do not wish to restrict the delivery area, leave this option as 'No'. If you wish to restrict delivery to only specific ZIP code ranges, you can enable this option and specify the ZIP code ranges below.", "toggleTitle": "Restrict deliveries by ZIP code ranges", "enableLabel": "Do you want to enable ZIP code ranges?", "rangesDescription": "Give a name to the served region and specify a starting and ending ZIP code. Orders within the ZIP code ranges will be served normally.", "completeAllFields": "Please fill in all required fields before adding a new zip code range.", "atLeastOneRange": "At least one zip code range is required.", "fieldsRequired": "All fields are required. Please fill in or remove empty rows.", "regionNameLabel": "Served region name:", "startZipLabel": "Initial CEP:", "endZipLabel": "Final CEP:", "regionPlaceholder": "Ex: North Zone", "zipPlaceholder": "00000-000", "removeRange": "Remove range", "addRange": "Add CEP range"}, "correios": {"description": "Configure the integration with Correios to offer SEDEX and PAC as shipping options in your store.", "offerSedex": "Offer SEDEX as a shipping method in your store", "offerPac": "Offer PAC as a shipping method in your store", "includeInsurance": "Include insurance price in shipping", "showDeliveryTime": "Show estimated delivery time", "addFixedValue": "Add a fixed value to the shipping price", "fixedValueAmount": "Value to add:", "addPercentageValue": "Add a percentage value to the shipping price", "percentageAmount": "Percentage to add:", "hasContract": "Do you have a contract with Correios?", "credentials": "Correios Credentials", "user": "User:", "apiKey": "API KEY:"}}