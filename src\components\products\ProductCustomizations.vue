<template>
  <div class="space-y-6">
    <!-- Header da seção -->
    <div class="flex justify-between items-center">
      <div>
      </div>
      <button
        v-if="customizationEnabled && customizations.length > 0"
        type="button"
        class="flex items-center text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-primary)] font-medium px-4 py-2 border border-[var(--iluria-color-border)] rounded-lg hover:bg-[var(--iluria-color-hover)]"
        @click="addCustomization"
      >
        <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
        </svg>
        {{ t('product.customization.addCustomization') }}
      </button>
    </div>

    <!-- Estado vazio -->
    <div v-if="!customizationEnabled || customizations.length === 0" class="text-center py-12 rounded-lg border-2 border-dashed border-[var(--iluria-color-border)]">
      <svg class="mx-auto h-12 w-12 text-[var(--iluria-color-text-tertiary)] mb-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="1.5" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"/>
      </svg>
      <h4 class="text-lg font-medium text-[var(--iluria-color-text-primary)] mb-2">{{ t('product.customization.emptyTitle') }}</h4>
      <p class="text-[var(--iluria-color-text-secondary)] mb-6">{{ t('product.customization.emptyDescription') }}</p>
      <IluriaButton
        color="primary"
        variant="outline"
        @click="addFirstCustomization"
      >
        {{ t('product.customization.addCustomization') }}
      </IluriaButton>
    </div>

    <!-- Lista de personalizações com drag and drop -->
    <div v-if="customizationEnabled && customizations.length > 0" class="space-y-6">
      <!-- Customizações com drag and drop -->
      <draggable 
        v-model="customizations" 
        @end="onSortCustomizations"
        item-key="id"
        handle=".drag-handle-main"
        class="space-y-6"
        :animation="150"
        ghost-class="opacity-50"
        chosen-class="chosen-customization"
      >
        <template #item="{ element: customization, index }">
          <div class="border border-[var(--iluria-color-border)] rounded-lg relative" :key="customization.id">
            <!-- Header da personalização -->
            <div class="flex items-center justify-between px-6 py-4 border-b border-[var(--iluria-color-border)]">
              <div class="flex items-center space-x-3">
                <!-- Ícone de drag and drop -->
                <div class="drag-handle-main cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-text-secondary)] p-1">
                  <svg class="w-5 h-5" fill="currentColor" viewBox="0 0 20 20">
                    <path d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM3 12a1 1 0 000 2h.01a1 1 0 100-2H3zM8 4a1 1 0 000 2h.01a1 1 0 100-2H8zM8 8a1 1 0 000 2h.01a1 1 0 100-2H8zM8 12a1 1 0 000 2h.01a1 1 0 100-2H8z"/>
                  </svg>
                </div>
                
                <!-- Ícone da customização -->
                <div class="flex items-center justify-center w-8 h-8 bg-[var(--iluria-color-surface-hover)] rounded-full">
                  <svg class="w-4 h-4 text-[var(--iluria-color-text-primary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                  </svg>
                </div>
                
                <div>
                  <h4 class="text-lg font-medium text-[var(--iluria-color-text-primary)]">
                    {{ customization.title || `${t('product.customization.newCustomization')}` }}
                  </h4>
                  <p class="text-sm text-[var(--iluria-color-text-secondary)]" v-if="customization.type">
                    {{ getTypeLabel(customization.type) }}
                  </p>
                </div>
              </div>
              
              <div class="flex items-center space-x-2">
                <button
                  type="button"
                  class="p-2 text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-text-secondary)] rounded-full hover:bg-[var(--iluria-color-hover)]"
                  @click="toggleCustomization(index)"
                >
                  <svg class="w-5 h-5" :class="{ 'rotate-180': !customization.collapsed }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                  </svg>
                </button>
                <button
                  type="button"
                  class="p-2 text-[var(--iluria-color-error)] hover:text-[var(--iluria-color-error-hover)] rounded-full hover:bg-[var(--iluria-color-error-bg)]"
                  @click="removeCustomization(index)"
                >
                  <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5" viewBox="0 0 20 20" fill="currentColor">
                    <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                  </svg>
                </button>
              </div>
            </div>

            <!-- Conteúdo da personalização -->
            <div v-show="!customization.collapsed" class="p-6 space-y-6">
              <!-- Primeira linha: Tipo e Título -->
              <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                <IluriaSelect
                  :key="`customization-type-${customization.id}`"
                  :id="`customization-type-${customization.id}`"
                  :label="t('product.customization.type')"
                  v-model="customization.type"
                  :options="typeOptions"
                  option-label="label"
                  option-value="value"
                  :placeholder="t('product.customization.selectType')"
                  @update:modelValue="() => onTypeChange(customization, index)"
                />

                <IluriaInputText
                  v-if="customization.type"
                  :key="`customization-title-${customization.id}`"
                  :id="`customization-title-${customization.id}`"
                  :label="t('product.customization.title')"
                  v-model="customization.title"
                  :placeholder="t('product.customization.titlePlaceholder')"
                  @input="updateModelValue"
                />
              </div>

              <!-- Campos adicionais -->
              <div v-if="customization.type && customization.title">
                <!-- Descrição -->
                <IluriaInputText
                  :key="`customization-description-${customization.id}`"
                  :id="`customization-description-${customization.id}`"
                  :label="t('product.customization.description')"
                  v-model="customization.description"
                  :placeholder="t('product.customization.descriptionPlaceholder')"
                  @input="updateModelValue"
                />

                <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <!-- Preço adicional -->
                  <IluriaInputText
                    v-if="customization.type !== 'MULTIPLE_CHOICE'"
                    :key="`customization-price-${customization.id}`"
                    :id="`customization-price-${customization.id}`"
                    :label="t('product.customization.additionalPrice')"
                    v-model="customization.additionalPrice"
                    type="money"
                    prefix="R$"
                    :placeholder="t('product.customization.pricePlaceholder')"
                    @input="updateModelValue"
                  />

                  <!-- Obrigatório -->
                  <IluriaCheckBox
                    :key="`customization-required-${customization.id}`"
                    :id="`customization-required-${customization.id}`"
                    v-model="customization.required"
                    :label="t('product.customization.required')"
                    @update:modelValue="updateModelValue"
                  />
                </div>

                <!-- Limite de caracteres para campo texto -->
                <div v-if="customization.type === 'TEXT'">
                  <IluriaInputText
                    :key="`customization-char-limit-${customization.id}`"
                    :id="`customization-char-limit-${customization.id}`"
                    :label="t('product.customization.characterLimit')"
                    v-model="customization.characterLimit"
                    type="number"
                    :placeholder="t('product.customization.characterLimitPlaceholder')"
                    @input="updateModelValue"
                  />
                </div>

                <!-- Opções para múltipla escolha -->
                <div v-if="customization.type === 'MULTIPLE_CHOICE'" class="space-y-4">
                  <!-- Estado vazio para opções -->
                  <div v-if="!customization.options || customization.options.length === 0" class="text-center py-8 rounded-lg border border-[var(--iluria-color-border)]">
                    <p class="text-[var(--iluria-color-text-secondary)] mb-4">{{ t('product.customization.noOptionsAdded') }}</p>
                    <button
                      type="button"
                      class="inline-flex items-center px-3 py-2 bg-[var(--iluria-color-primary)] text-white text-sm font-medium rounded-lg hover:bg-[var(--iluria-color-primary-hover)]"
                      @click="addOption(index)"
                    >
                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                      </svg>
                      {{ t('product.customization.addOption') }}
                    </button>
                  </div>

                  <!-- Lista de opções com drag and drop -->
                  <div v-else>
                    <draggable
                      v-model="customization.options"
                      @end="(event) => onSortOptions(index, event)"
                      item-key="id"
                      handle=".drag-handle-option"
                      class="space-y-4 mb-4"
                      :animation="150"
                      ghost-class="opacity-50"
                      chosen-class="chosen-customization"
                    >
                      <template #item="{ element: option, index: optionIndex }">
                        <div class="p-4 border border-[var(--iluria-color-border)] rounded-lg" :key="option.id">
                          <div class="flex justify-between items-start mb-4">
                            <div class="flex items-center space-x-2">
                              <!-- Drag handle para opções -->
                              <div class="drag-handle-option cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-primary)]">
                                <svg class="w-4 h-4" fill="currentColor" viewBox="0 0 20 20">
                                  <path d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM3 12a1 1 0 000 2h.01a1 1 0 100-2H3zM8 4a1 1 0 000 2h.01a1 1 0 100-2H8zM8 8a1 1 0 000 2h.01a1 1 0 100-2H8zM8 12a1 1 0 000 2h.01a1 1 0 100-2H8z"/>
                                </svg>
                              </div>
                              
                              <h6 class="text-md font-medium text-[var(--iluria-color-text-primary)]">
                                {{ option.label || `${t('product.customization.optionTitle')} ${optionIndex + 1}` }}
                              </h6>
                            </div>
                            
                            <div class="flex items-center space-x-2">
                              <button
                                type="button"
                                class="p-1 text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-text-secondary)] rounded-full hover:bg-[var(--iluria-color-hover)]"
                                @click="toggleOption(index, optionIndex)"
                              >
                                <svg class="w-4 h-4" :class="{ 'rotate-180': !option.collapsed }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                </svg>
                              </button>
                              <button
                                type="button"
                                class="p-1 text-[var(--iluria-color-error)] hover:text-[var(--iluria-color-error-hover)] rounded-full hover:bg-[var(--iluria-color-error-bg)]"
                                @click="removeOption(index, optionIndex)"
                              >
                                <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4" viewBox="0 0 20 20" fill="currentColor">
                                  <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                </svg>
                              </button>
                            </div>
                          </div>

                          <!-- Conteúdo da opção -->
                          <div v-show="!option.collapsed" class="space-y-4">
                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
                              <!-- Título da opção -->
                              <IluriaInputText
                                :key="`option-title-${option.id}`"
                                :id="`option-title-${option.id}`"
                                :label="t('product.customization.optionLabel')"
                                v-model="option.label"
                                :placeholder="t('product.customization.optionLabelPlaceholder')"
                                @input="updateModelValue"
                              />

                              <!-- Preço adicional da opção -->
                              <IluriaInputText
                                :key="`option-price-${option.id}`"
                                :id="`option-price-${option.id}`"
                                :label="t('product.customization.additionalPrice')"
                                v-model="option.additionalPrice"
                                type="money"
                                prefix="R$"
                                :placeholder="t('product.customization.pricePlaceholder')"
                                @input="updateModelValue"
                              />
                            </div>

                            <!-- Múltiplas personalizações aninhadas -->
                            <div class="mt-4 p-4 bg-[var(--iluria-color-surface-hover)] border border-[var(--iluria-color-border)] rounded-lg">
                              <div class="flex items-center justify-between mb-4">
                                <div class="flex items-center space-x-2">
                                  <svg class="w-5 h-5 text-[var(--iluria-color-text-secondary)]" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                    <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M13.828 10.172a4 4 0 00-5.656 0l-4 4a4 4 0 105.656 5.656l1.102-1.101m-.758-4.899a4 4 0 005.656 0l4-4a4 4 0 00-5.656-5.656l-1.1 1.1"></path>
                                  </svg>
                                  <h6 class="text-sm font-medium text-[var(--iluria-color-text-primary)]">
                                    {{ t('product.customization.nestedCustomization') }}
                                  </h6>
                                </div>
                                <IluriaToggleSwitch 
                                  v-model="option.hasNestedCustomization"
                                  :label="t('product.customization.enableNested')"
                                  @update:modelValue="() => toggleNestedCustomization(option)"
                                />
                              </div>

                              <!-- Lista de nested customizations -->
                              <div v-if="option.hasNestedCustomization" class="space-y-4">
                                <!-- Drag and drop para nested customizations -->
                                <div v-if="option.nestedCustomizations && option.nestedCustomizations.length > 0">
                                  <draggable
                                    v-model="option.nestedCustomizations"
                                    @end="(event) => onSortNestedCustomizations(index, optionIndex, event)"
                                    item-key="id"
                                    handle=".drag-handle-nested"
                                    class="space-y-3 mb-4"
                                    :animation="150"
                                    ghost-class="opacity-50"
                                    chosen-class="bg-gray-50"
                                  >
                                    <template #item="{ element: nested, index: nestedIndex }">
                                      <div class="p-3 bg-[var(--iluria-color-surface)] border border-[var(--iluria-color-border)] rounded-md" :key="nested.id">
                                        <div class="flex items-center justify-between mb-3">
                                          <div class="flex items-center space-x-2">
                                            <!-- Drag handle para nested -->
                                            <div class="drag-handle-nested cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-primary)]">
                                              <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                <path d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM3 12a1 1 0 000 2h.01a1 1 0 100-2H3zM8 4a1 1 0 000 2h.01a1 1 0 100-2H8zM8 8a1 1 0 000 2h.01a1 1 0 100-2H8zM8 12a1 1 0 000 2h.01a1 1 0 100-2H8z"/>
                                              </svg>
                                            </div>
                                            
                                            <span class="text-sm font-medium text-[var(--iluria-color-text-primary)]">
                                              {{ nested.title || `Nested ${nestedIndex + 1}` }}
                                            </span>
                                          </div>
                                          
                                          <div class="flex items-center space-x-1">
                                            <button
                                              type="button"
                                              class="p-1 text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-text-secondary)] rounded-full hover:bg-[var(--iluria-color-hover)]"
                                              @click="toggleNestedCustomization2(index, optionIndex, nestedIndex)"
                                            >
                                              <svg class="w-3 h-3" :class="{ 'rotate-180': !nested.collapsed }" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"></path>
                                              </svg>
                                            </button>
                                            <button
                                              type="button"
                                              class="p-1 text-[var(--iluria-color-error)] hover:text-[var(--iluria-color-error-hover)] rounded-full hover:bg-[var(--iluria-color-error-bg)]"
                                              @click="removeNestedCustomization(index, optionIndex, nestedIndex)"
                                            >
                                              <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                                <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                              </svg>
                                            </button>
                                          </div>
                                        </div>

                                        <!-- Conteúdo da nested customization -->
                                        <div v-show="!nested.collapsed">
                                          <!-- Campos da nested customization -->
                                          <div class="grid grid-cols-1 md:grid-cols-2 gap-4 mb-3">
                                            <IluriaSelect
                                              :key="`nested-type-${nested.id}`"
                                              :id="`nested-type-${nested.id}`"
                                              :label="t('product.customization.type')"
                                              v-model="nested.type"
                                              :options="nestedTypeOptions"
                                              option-label="label"
                                              option-value="value"
                                              :placeholder="t('product.customization.selectType')"
                                              @update:modelValue="() => onNestedTypeChange(customization, option, nested, nestedIndex)"
                                            />

                                            <IluriaInputText
                                              v-if="nested.type"
                                              :key="`nested-title-${nested.id}`"
                                              :id="`nested-title-${nested.id}`"
                                              :label="t('product.customization.title')"
                                              v-model="nested.title"
                                              :placeholder="t('product.customization.titlePlaceholder')"
                                              @input="updateModelValue"
                                            />
                                          </div>

                                          <!-- Campos adicionais para nested -->
                                          <div v-if="nested.type && nested.title" class="space-y-3 mb-4">
                                            <!-- Descrição -->
                                            <IluriaInputText
                                              :key="`nested-description-${nested.id}`"
                                              :id="`nested-description-${nested.id}`"
                                              :label="t('product.customization.description')"
                                              v-model="nested.description"
                                              :placeholder="t('product.customization.descriptionPlaceholder')"
                                              @input="updateModelValue"
                                            />

                                            <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
                                              <!-- Preço adicional para nested (exceto MULTIPLE_CHOICE) -->
                                              <IluriaInputText
                                                v-if="nested.type !== 'MULTIPLE_CHOICE'"
                                                :key="`nested-price-${nested.id}`"
                                                :id="`nested-price-${nested.id}`"
                                                :label="t('product.customization.additionalPrice')"
                                                v-model="nested.additionalPrice"
                                                type="money"
                                                prefix="R$"
                                                :placeholder="t('product.customization.pricePlaceholder')"
                                                @input="updateModelValue"
                                              />

                                              <!-- Obrigatório para nested -->
                                              <IluriaCheckBox
                                                :key="`nested-required-${nested.id}`"
                                                :id="`nested-required-${nested.id}`"
                                                v-model="nested.required"
                                                :label="t('product.customization.required')"
                                                @update:modelValue="updateModelValue"
                                              />
                                            </div>

                                            <!-- Limite de caracteres para nested TEXT -->
                                            <div v-if="nested.type === 'TEXT'">
                                              <IluriaInputText
                                                :key="`nested-char-limit-${nested.id}`"
                                                :id="`nested-char-limit-${nested.id}`"
                                                :label="t('product.customization.characterLimit')"
                                                v-model="nested.characterLimit"
                                                type="number"
                                                :placeholder="t('product.customization.characterLimitPlaceholder')"
                                                @input="updateModelValue"
                                              />
                                            </div>

                                            <!-- Opções da nested customization se for MULTIPLE_CHOICE -->
                                            <div v-if="nested.type === 'MULTIPLE_CHOICE'" class="mt-4">
                                              <!-- Opções com drag and drop -->
                                              <div v-if="nested.options && nested.options.length > 0">
                                                <draggable
                                                  :model-value="getNestedOptions(index, optionIndex, nestedIndex)"
                                                  @update:model-value="async (newValue) => await setNestedOptions(index, optionIndex, nestedIndex, newValue)"
                                                  @end="(event) => onSortNestedOptions(index, optionIndex, nestedIndex, event)"
                                                  item-key="id"
                                                  handle=".drag-handle-nested-option"
                                                  class="space-y-2 mb-3"
                                                  :animation="150"
                                                  ghost-class="opacity-50"
                                                  chosen-class="bg-blue-50"
                                                  :key="`nested-options-${nested.id}-${nested.options?.map(o => o.id).join('-')}`"
                                                >
                                                  <template #item="{ element: nestedOption, index: nestedOptionIndex }">
                                                    <div class="flex items-center space-x-2 p-2 bg-[var(--iluria-color-surface-hover)] rounded" :key="nestedOption.id">
                                                      <!-- Drag handle para nested options -->
                                                      <div class="drag-handle-nested-option cursor-move text-[var(--iluria-color-text-tertiary)] hover:text-[var(--iluria-color-primary)]">
                                                        <svg class="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                                                          <path d="M3 4a1 1 0 000 2h.01a1 1 0 100-2H3zM3 8a1 1 0 000 2h.01a1 1 0 100-2H3zM3 12a1 1 0 000 2h.01a1 1 0 100-2H3zM8 4a1 1 0 000 2h.01a1 1 0 100-2H8zM8 8a1 1 0 000 2h.01a1 1 0 100-2H8zM8 12a1 1 0 000 2h.01a1 1 0 100-2H8z"/>
                                                        </svg>
                                                      </div>
                                                      
                                                      <IluriaInputText
                                                        :key="`nested-option-label-${nestedOption.id}`"
                                                        :id="`nested-option-label-${nestedOption.id}`"
                                                        v-model="nestedOption.label"
                                                        :placeholder="t('product.customization.optionLabelPlaceholder')"
                                                        class="flex-1"
                                                        @input="updateModelValue"
                                                      />
                                                      
                                                      <IluriaInputText
                                                        :key="`nested-option-price-${nestedOption.id}`"
                                                        :id="`nested-option-price-${nestedOption.id}`"
                                                        v-model="nestedOption.additionalPrice"
                                                        type="money"
                                                        prefix="R$"
                                                        :placeholder="t('product.customization.pricePlaceholder')"
                                                        class="w-32"
                                                        @input="updateModelValue"
                                                      />
                                                      
                                                      <button
                                                        type="button"
                                                        class="p-1 text-[var(--iluria-color-error)] hover:text-[var(--iluria-color-error-hover)]"
                                                        @click="removeNestedOption(index, optionIndex, nestedIndex, nestedOptionIndex)"
                                                      >
                                                        <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3" viewBox="0 0 20 20" fill="currentColor">
                                                          <path fill-rule="evenodd" d="M9 2a1 1 0 00-.894.553L7.382 4H4a1 1 0 000 2v10a2 2 0 002 2h8a2 2 0 002-2V6a1 1 0 100-2h-3.382l-.724-1.447A1 1 0 0011 2H9zM7 8a1 1 0 012 0v6a1 1 0 11-2 0V8zm5-1a1 1 0 00-1 1v6a1 1 0 102 0V8a1 1 0 00-1-1z" clip-rule="evenodd" />
                                                        </svg>
                                                      </button>
                                                    </div>
                                                  </template>
                                                </draggable>

                                                <!-- Botão adicionar opção nested no final -->
                                                <div class="flex justify-end">
                                                  <button
                                                    type="button"
                                                    class="flex items-center text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-primary)] text-xs font-medium"
                                                    @click="addNestedOption(index, optionIndex, nestedIndex)"
                                                  >
                                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                                    </svg>
                                                    {{ t('product.customization.addOption') }}
                                                  </button>
                                                </div>
                                              </div>

                                              <!-- Estado inicial quando não há opções -->
                                              <div v-else class="flex justify-end">
                                                <button
                                                  type="button"
                                                  class="flex items-center text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-primary)] text-xs font-medium"
                                                  @click="addNestedOption(index, optionIndex, nestedIndex)"
                                                >
                                                  <svg xmlns="http://www.w3.org/2000/svg" class="h-3 w-3 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                                    <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                                  </svg>
                                                  {{ t('product.customization.addOption') }}
                                                </button>
                                              </div>
                                            </div>
                                          </div>
                                        </div>
                                      </div>
                                    </template>
                                  </draggable>

                                  <!-- Botão adicionar nested customization no final -->
                                  <div class="flex justify-end">
                                    <button
                                      type="button"
                                      class="flex items-center text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-primary)] text-sm font-medium"
                                      @click="addNestedCustomization(index, optionIndex)"
                                    >
                                      <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                        <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                      </svg>
                                      {{ t('product.customization.addNestedCustomization') }}
                                    </button>
                                  </div>
                                </div>

                                <!-- Estado inicial quando não há nested customizations -->
                                <div v-else class="flex justify-end">
                                  <button
                                    type="button"
                                    class="flex items-center text-[var(--iluria-color-text-primary)] hover:text-[var(--iluria-color-primary)] text-sm font-medium"
                                    @click="addNestedCustomization(index, optionIndex)"
                                  >
                                    <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-1" viewBox="0 0 20 20" fill="currentColor">
                                      <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                                    </svg>
                                    {{ t('product.customization.addNestedCustomization') }}
                                  </button>
                                </div>
                              </div>
                            </div>
                          </div>
                        </div>
                      </template>
                    </draggable>

                    <!-- Botão adicionar opção no final -->
                    <div class="flex justify-end pt-4">
                      <button
                        type="button"
                        class="inline-flex items-center px-3 py-2 bg-[var(--iluria-color-primary)] text-white text-sm font-medium rounded-lg hover:bg-[var(--iluria-color-primary-hover)]"
                        @click="addOption(index)"
                      >
                        <svg xmlns="http://www.w3.org/2000/svg" class="h-4 w-4 mr-2" viewBox="0 0 20 20" fill="currentColor">
                          <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
                        </svg>
                        {{ t('product.customization.addOption') }}
                      </button>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </template>
      </draggable>

      <!-- Botão adicionar personalização no final -->
      <div class="flex justify-end pt-4">
        <IluriaButton
          color="primary"
          @click="addCustomization"
        >
          <svg xmlns="http://www.w3.org/2000/svg" class="h-5 w-5 mr-2" viewBox="0 0 20 20" fill="currentColor">
            <path fill-rule="evenodd" d="M10 3a1 1 0 011 1v5h5a1 1 0 110 2h-5v5a1 1 0 11-2 0v-5H4a1 1 0 110-2h5V4a1 1 0 011-1z" clip-rule="evenodd" />
          </svg>
          {{ t('product.customization.addCustomization') }}
        </IluriaButton>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, watch, inject, nextTick } from 'vue';
import { useI18n } from 'vue-i18n';
import draggable from 'vuedraggable';
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';


const { t } = useI18n();

// Inject the product form data from parent component
const form = inject('productForm');

// Props
const props = defineProps({
  modelValue: {
    type: Array,
    default: () => []
  }
});

// Emits
const emit = defineEmits(['update:modelValue']);

// Estado local
const customizationEnabled = ref(false);
const customizations = ref([]);
const isUpdatingFromProps = ref(false);

// Opções de tipo
const typeOptions = ref([
  { label: t('product.customization.types.multipleChoice'), value: 'MULTIPLE_CHOICE' },
  { label: t('product.customization.types.text'), value: 'TEXT' },
  { label: t('product.customization.types.number'), value: 'NUMBER' },
  { label: t('product.customization.types.image'), value: 'IMAGE' }
]);

// Opções de tipo para personalização aninhada (incluindo múltipla escolha)
const nestedTypeOptions = ref([
  { label: t('product.customization.types.multipleChoice'), value: 'MULTIPLE_CHOICE' },
  { label: t('product.customization.types.text'), value: 'TEXT' },
  { label: t('product.customization.types.number'), value: 'NUMBER' },
  { label: t('product.customization.types.image'), value: 'IMAGE' }
]);

// Função para criar uma nova personalização
const createNewCustomization = () => ({
  id: `customization_${Date.now()}_${Math.random()}`,
  type: '',
  title: '',
  description: '',
  required: false,
  additionalPrice: '',
  characterLimit: '',
  options: [],
  collapsed: false
});

// Função para criar uma nova personalização aninhada
const createNewNestedCustomization = () => ({
  id: `nested_${Date.now()}_${Math.random()}`,
  type: '',
  title: '',
  description: '',
  required: false,
  additionalPrice: '',
  characterLimit: '',
  position: 0,
  options: [],
  collapsed: false
});

// Função para criar uma nova opção de personalização aninhada
const createNewNestedOption = () => {
  const timestamp = Date.now();
  const random = Math.random().toString(36).substr(2, 9);
  return {
    id: `nested_option_${timestamp}_${random}`,
    label: '',
    additionalPrice: '',
    position: 0
  };
};

// Função para criar uma nova opção
const createNewOption = () => ({
  id: `option_${Date.now()}_${Math.random()}`,
  label: '',
  additionalPrice: '',
  position: 0,
  hasNestedCustomization: false,
  nestedCustomizations: [],
  collapsed: false
});

// Funções de drag and drop corrigidas
const onSortCustomizations = (event) => {
  updateModelValue();
};

const onSortOptions = (customizationIndex, event) => {
  updateOptionPositions(customizationIndex);
  updateModelValue();
};

const onSortNestedCustomizations = (customizationIndex, optionIndex, event) => {
  updateNestedCustomizationPositions(customizationIndex, optionIndex);
  updateModelValue();
};

const onSortNestedOptions = async (customizationIndex, optionIndex, nestedIndex, event) => {
  // Aguarda para garantir que o model-value foi atualizado
  await nextTick();
  
  // Apenas atualiza as posições, pois o setNestedOptions já cuidou da reatividade
  const nested = customizations.value[customizationIndex]?.options[optionIndex]?.nestedCustomizations[nestedIndex];
  if (nested && nested.options) {
    nested.options.forEach((option, index) => {
      option.position = index;
    });
    
    updateModelValue();
  }
};

// Funções auxiliares
const updateOptionPositions = (customizationIndex) => {
  const options = customizations.value[customizationIndex].options;
  options.forEach((option, index) => {
    option.position = index;
  });
};

const updateNestedCustomizationPositions = (customizationIndex, optionIndex) => {
  const nestedCustomizations = customizations.value[customizationIndex].options[optionIndex].nestedCustomizations;
  nestedCustomizations.forEach((nested, index) => {
    nested.position = index;
  });
};

const updateNestedOptionPositions = (customizationIndex, optionIndex, nestedIndex) => {
  const nestedOptions = customizations.value[customizationIndex].options[optionIndex].nestedCustomizations[nestedIndex].options;
  nestedOptions.forEach((option, index) => {
    option.position = index;
  });
};

// Função para adicionar primeira personalização
const addFirstCustomization = () => {
  customizationEnabled.value = true;
  const newCustomization = createNewCustomization();
  customizations.value.push(newCustomization);
  updateModelValue();
};

// Função para adicionar personalização
const addCustomization = () => {
  customizations.value.push(createNewCustomization());
  updateModelValue();
};

// Função para remover personalização
const removeCustomization = (index) => {
  customizations.value.splice(index, 1);
  if (customizations.value.length === 0) {
    customizationEnabled.value = false;
  }
  updateModelValue();
};

// Função para toggle da personalização
const toggleCustomization = (index) => {
  customizations.value[index].collapsed = !customizations.value[index].collapsed;
};

// Função para toggle da opção
const toggleOption = (customizationIndex, optionIndex) => {
  customizations.value[customizationIndex].options[optionIndex].collapsed = !customizations.value[customizationIndex].options[optionIndex].collapsed;
};

// Função para toggle da nested customization
const toggleNestedCustomization2 = (customizationIndex, optionIndex, nestedIndex) => {
  customizations.value[customizationIndex].options[optionIndex].nestedCustomizations[nestedIndex].collapsed = 
    !customizations.value[customizationIndex].options[optionIndex].nestedCustomizations[nestedIndex].collapsed;
};

// Função para ativar/desativar personalização aninhada
const toggleNestedCustomization = (option) => {
  if (!option.hasNestedCustomization) {
    option.nestedCustomizations = [];
  } else {
    if (!option.nestedCustomizations || option.nestedCustomizations.length === 0) {
      option.nestedCustomizations = [createNewNestedCustomization()];
    }
  }
  updateModelValue();
};

// Função para mudança de tipo
const onTypeChange = (customization, index) => {
  if (customization.type === 'MULTIPLE_CHOICE' && (!customization.options || customization.options.length === 0)) {
    customization.options = [createNewOption()];
  } else if (customization.type !== 'MULTIPLE_CHOICE') {
    customization.options = [];
  }
  updateModelValue();
};

// Função para adicionar opção
const addOption = (customizationIndex) => {
  if (!customizations.value[customizationIndex].options) {
    customizations.value[customizationIndex].options = [];
  }
  const newOption = createNewOption();
  newOption.position = customizations.value[customizationIndex].options.length;
  customizations.value[customizationIndex].options.push(newOption);
  updateModelValue();
};

// Função para remover opção
const removeOption = (customizationIndex, optionIndex) => {
  customizations.value[customizationIndex].options.splice(optionIndex, 1);
  updateOptionPositions(customizationIndex);
  updateModelValue();
};

// Função para adicionar personalização aninhada
const addNestedCustomization = (customizationIndex, optionIndex) => {
  if (!customizations.value[customizationIndex].options[optionIndex].nestedCustomizations) {
    customizations.value[customizationIndex].options[optionIndex].nestedCustomizations = [];
  }
  const newNested = createNewNestedCustomization();
  newNested.position = customizations.value[customizationIndex].options[optionIndex].nestedCustomizations.length;
  customizations.value[customizationIndex].options[optionIndex].nestedCustomizations.push(newNested);
  updateModelValue();
};

// Função para remover personalização aninhada
const removeNestedCustomization = (customizationIndex, optionIndex, nestedIndex) => {
  customizations.value[customizationIndex].options[optionIndex].nestedCustomizations.splice(nestedIndex, 1);
  updateNestedCustomizationPositions(customizationIndex, optionIndex);
  updateModelValue();
};

// Função para mudança de tipo em nested customization
const onNestedTypeChange = (customization, option, nested, nestedIndex) => {
  if (nested.type === 'MULTIPLE_CHOICE' && (!nested.options || nested.options.length === 0)) {
    const newOption = createNewNestedOption();
    newOption.position = 0;
    nested.options = [newOption];
  } else if (nested.type !== 'MULTIPLE_CHOICE') {
    nested.options = [];
  }
  updateModelValue();
};

// Função para adicionar opção em nested customization
const addNestedOption = (customizationIndex, optionIndex, nestedIndex) => {
  const nested = customizations.value[customizationIndex].options[optionIndex].nestedCustomizations[nestedIndex];
  if (!nested.options) {
    nested.options = [];
  }
  const newOption = createNewNestedOption();
  newOption.position = nested.options.length;
  nested.options.push(newOption);
  
  updateModelValue();
};

// Função para remover opção de nested customization
const removeNestedOption = (customizationIndex, optionIndex, nestedIndex, nestedOptionIndex) => {
  const nested = customizations.value[customizationIndex].options[optionIndex].nestedCustomizations[nestedIndex];
  nested.options.splice(nestedOptionIndex, 1);
  updateNestedOptionPositions(customizationIndex, optionIndex, nestedIndex);
  updateModelValue();
};

// Função para atualizar o modelo
const updateModelValue = () => {
  if (isUpdatingFromProps.value) {
    return;
  }
  
  const value = customizationEnabled.value ? customizations.value : [];
  
  emit('update:modelValue', value);
  
  if (form.value) {
    form.value.customizations = value;
    form.value.hasCustomizations = customizationEnabled.value;
  }
};

// Watch para mudanças no modelValue
watch(() => props.modelValue, (newValue) => {
  isUpdatingFromProps.value = true;
  
  if (newValue && newValue.length > 0) {
    // Garantir que todos os elementos tenham IDs únicos
    customizations.value = JSON.parse(JSON.stringify(newValue)).map(customization => {
      if (!customization.id) {
        customization.id = `customization_${Date.now()}_${Math.random()}`;
      }
      
      if (customization.options) {
        customization.options = customization.options.map((option, optIndex) => {
          if (!option.id) {
            option.id = `option_${Date.now()}_${Math.random()}_${optIndex}`;
          }
          
          if (option.nestedCustomizations) {
            option.nestedCustomizations = option.nestedCustomizations.map((nested, nestedIndex) => {
              if (!nested.id) {
                nested.id = `nested_${Date.now()}_${Math.random()}_${nestedIndex}`;
              }
              
              if (nested.options) {
                nested.options = nested.options.map((nestedOption, nestedOptIndex) => {
                  if (!nestedOption.id) {
                    nestedOption.id = `nested_option_${Date.now()}_${Math.random()}_${nestedOptIndex}`;
                  }
                  return nestedOption;
                });
              }
              
              return nested;
            });
          }
          
          return option;
        });
      }
      
      return customization;
    });
    
    customizationEnabled.value = true;

  } else {
    customizations.value = [];
    customizationEnabled.value = false;
  }
  
  setTimeout(() => {
    isUpdatingFromProps.value = false;
  }, 0);
}, { immediate: true, deep: true });

// Função para obter label do tipo
const getTypeLabel = (type) => {
  const typeOption = typeOptions.value.find(option => option.value === type);
  return typeOption ? typeOption.label : '';
};

// Função helper para obter nested options com reatividade
const getNestedOptions = (customizationIndex, optionIndex, nestedIndex) => {
  return customizations.value[customizationIndex]?.options[optionIndex]?.nestedCustomizations[nestedIndex]?.options || [];
};

// Função helper para definir nested options com reatividade
const setNestedOptions = async (customizationIndex, optionIndex, nestedIndex, newOptions) => {
  const nested = customizations.value[customizationIndex].options[optionIndex].nestedCustomizations[nestedIndex];
  if (nested) {
    // Atualiza o array de opções
    nested.options = newOptions;
    
    // Força reatividade completa recreando todo o array de customizations
    customizations.value = customizations.value.map(cust => ({
      ...cust,
      options: cust.options ? cust.options.map(opt => ({
        ...opt,
        nestedCustomizations: opt.nestedCustomizations ? opt.nestedCustomizations.map(nest => ({
          ...nest,
          options: nest.options ? [...nest.options] : []
        })) : []
      })) : []
    }));
    
    // Aguarda a próxima atualização do DOM
    await nextTick();
  }
};
</script>

<style scoped>
.drag-handle-main,
.drag-handle-option,
.drag-handle-nested,
.drag-handle-nested-option {
  transition: all 0.2s ease;
}

.drag-handle-main:hover,
.drag-handle-option:hover,
.drag-handle-nested:hover,
.drag-handle-nested-option:hover {
  transform: scale(1.1);
}

/* Estilos para drag and drop com temas */
:deep(.chosen-customization) {
  background: var(--iluria-color-surface-hover) !important;
  border: 1px solid var(--iluria-color-primary) !important;
}

:deep(.chosen-customization *) {
  background: var(--iluria-color-surface-hover) !important;
  color: var(--iluria-color-text-primary) !important;
  border-color: var(--iluria-color-border) !important;
}

/* Para o draggable das opções nested */
:deep(.bg-blue-50) {
  background: var(--iluria-color-primary-bg) !important;
  border: 1px solid var(--iluria-color-primary) !important;
}
</style> 
