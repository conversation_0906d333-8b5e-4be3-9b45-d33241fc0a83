<template>
  <!-- Este componente não renderiza nada, apenas fornece funcionalidades -->
</template>

<script>
import { onMounted, onUnmounted } from 'vue';

// Função para injetar o script de carrossel no documento
export function injectCarouselScript(doc, authToken) {
  if (!doc || !doc.querySelector) {
    return;
  }

  const carousels = doc.querySelectorAll('[data-component="carousel"], .iluria-carousel, [data-element-type="carousel"]');
  
  if (!carousels || carousels.length === 0) {
    return;
  }
  
  if (doc.querySelector('#carousel-init-script')) {
    return;
  }
  
  try {

    
    // Script principal para carrossel
    const script = doc.createElement('script');
    script.id = 'carousel-init-script';
    
    script.textContent = `
      (function() {
        if (window.IluriaCarouselInitialized) {
          return;
        }
        
        window.IluriaCarouselInitialized = true;
        
        // Classe principal do carrossel
        class IluriaCarousel {
          constructor(element) {
            this.element = element;
            this.currentSlide = 0;
            this.autoPlayInterval = null;
            this.isPlaying = false;
            this.touchStartX = 0;
            this.touchStartY = 0;
            this.isTransitioning = false;
            

            this.init();
          }
          
          init() {
            try {
              this.slidesContainer = this.element.querySelector('.carousel-slides');
              this.slides = this.element.querySelectorAll('.carousel-slide');
              this.indicators = this.element.querySelectorAll('.indicator');
              this.prevButton = this.element.querySelector('.nav-button.prev');
              this.nextButton = this.element.querySelector('.nav-button.next');
              
              if (this.slides.length === 0) {
                console.warn('⚠️ Carrossel sem slides encontrado:', this.element);
                return;
              }
              
              
              this.setupEventListeners();
              this.setupTransitions();
              this.updateSlideState();
              this.startAutoPlay();
              
              // Marcar como inicializado
              this.element.setAttribute('data-carousel-initialized', 'true');
              this.element._carouselInstance = this;
              
              
            } catch (error) {
              console.error('❌ Erro ao inicializar carrossel:', error);
            }
          }
          
          setupTransitions() {
            const transitionType = this.element.getAttribute('data-transition') || 'fade';
            const transitionDuration = this.element.getAttribute('data-transition-duration') || '500';
            
            
            
            if (this.slidesContainer) {
              this.slidesContainer.style.position = 'relative';
              this.slidesContainer.style.overflow = 'hidden';
              
              this.slides.forEach((slide, index) => {
                slide.style.position = 'absolute';
                slide.style.top = '0';
                slide.style.left = '0';
                slide.style.width = '100%';
                slide.style.height = '100%';
                slide.style.transition = \`all \${transitionDuration}ms ease-in-out\`;
                
                // Remover animações de carregamento indesejadas
                slide.style.removeProperty('animation');
                slide.style.removeProperty('animation-delay');
                
                switch (transitionType) {
                  case 'slide':
                    slide.style.transform = index === 0 ? 'translateX(0%)' : 'translateX(100%)';
                    slide.style.opacity = '1';
                    slide.style.transformStyle = 'initial';
                    break;
                  case 'fade':
                    slide.style.opacity = index === 0 ? '1' : '0';
                    slide.style.transform = 'none';
                    slide.style.transformStyle = 'initial';
                    break;
                  case 'cube':
                    slide.style.opacity = index === 0 ? '1' : '0';
                    slide.style.transformStyle = 'preserve-3d';
                    slide.style.transform = index === 0 ? 'rotateY(0deg)' : 'rotateY(-90deg)';
                    break;
                  case 'cover':
                    slide.style.opacity = '1';
                    slide.style.transform = index === 0 ? 'translateY(0%)' : 'translateY(100%)';
                    slide.style.transformStyle = 'initial';
                    break;
                  case 'flip':
                    slide.style.opacity = index === 0 ? '1' : '0';
                    slide.style.transformStyle = 'preserve-3d';
                    slide.style.transform = index === 0 ? 'rotateX(0deg)' : 'rotateX(90deg)';
                    break;
                  case 'none':
                    slide.style.opacity = index === 0 ? '1' : '0';
                    slide.style.transform = 'none';
                    slide.style.transition = 'none';
                    slide.style.transformStyle = 'initial';
                    break;
                  default:
                    slide.style.opacity = index === 0 ? '1' : '0';
                    slide.style.transform = 'none';
                    slide.style.transformStyle = 'initial';
                }
                
              });
              
            }
          }
          
          setupEventListeners() {
            
            // Remover listeners existentes para evitar duplicação
            this.removeExistingListeners();
            
            // Botões de navegação
            if (this.prevButton) {
              this.prevButton.addEventListener('click', this.handlePrevClick.bind(this));
            }
            
            if (this.nextButton) {
              this.nextButton.addEventListener('click', this.handleNextClick.bind(this));
            }
            
            // Indicadores
            this.indicators.forEach((indicator, index) => {
              indicator.addEventListener('click', (e) => {
                this.handleIndicatorClick(e, index);
              });
            });
            
            // Touch/Swipe support
            this.setupTouchListeners();
            
            // Auto-play controls
            this.element.addEventListener('mouseenter', this.handleMouseEnter.bind(this));
            this.element.addEventListener('mouseleave', this.handleMouseLeave.bind(this));
            
            // Navegação por teclado
            this.element.addEventListener('keydown', this.handleKeyDown.bind(this));
            
            // Listener para mudanças de transição
            this.element.addEventListener('transitionUpdated', this.handleTransitionUpdate.bind(this));
            
          }
          
          removeExistingListeners() {
            // Clonar botões para remover listeners
            if (this.prevButton) {
              const newPrev = this.prevButton.cloneNode(true);
              this.prevButton.parentNode.replaceChild(newPrev, this.prevButton);
              this.prevButton = newPrev;
            }
            
            if (this.nextButton) {
              const newNext = this.nextButton.cloneNode(true);
              this.nextButton.parentNode.replaceChild(newNext, this.nextButton);
              this.nextButton = newNext;
            }
            
            // Recriar indicadores
            this.indicators.forEach((indicator, index) => {
              const newIndicator = indicator.cloneNode(true);
              indicator.parentNode.replaceChild(newIndicator, indicator);
            });
            this.indicators = this.element.querySelectorAll('.indicator');
          }
          
          handlePrevClick(e) {
            e.preventDefault();
            e.stopPropagation();
            this.prevSlide();
          }
          
          handleNextClick(e) {
            e.preventDefault();
            e.stopPropagation();
            this.nextSlide();
          }
          
          handleIndicatorClick(e, index) {
            e.preventDefault();
            e.stopPropagation();
            this.goToSlide(index);
          }
          
          handleMouseEnter() {
            this.stopAutoPlay();
          }
          
          handleMouseLeave() {
            this.startAutoPlay();
          }
          
          handleKeyDown(e) {
            switch (e.key) {
              case 'ArrowLeft':
                e.preventDefault();
                this.prevSlide();
                break;
              case 'ArrowRight':
                e.preventDefault();
                this.nextSlide();
                break;
              case ' ':
                e.preventDefault();
                this.toggleAutoPlay();
                break;
              case 'Home':
                e.preventDefault();
                this.goToSlide(0);
                break;
              case 'End':
                e.preventDefault();
                this.goToSlide(this.slides.length - 1);
                break;
            }
          }
          
          handleTransitionUpdate(e) {
            
            // Salvar configurações atuais
            const oldType = this.element.getAttribute('data-transition') || 'fade';
            const newType = e.detail.type || oldType;
            const newDuration = e.detail.duration || parseInt(this.element.getAttribute('data-transition-duration') || '500');
            
            
            // Atualizar os atributos
            this.element.setAttribute('data-transition', newType);
            this.element.setAttribute('data-transition-duration', newDuration.toString());
            
            // Parar qualquer transição em andamento
            this.isTransitioning = false;
            
            // Atualizar todas as configurações de transição
            this.setupTransitions();
            
            // Forçar atualização do estado atual
            setTimeout(() => {
              this.updateSlideState();
            }, 50);
            
          }
          
          setupTouchListeners() {
            this.element.addEventListener('touchstart', (e) => {
              this.touchStartX = e.touches[0].clientX;
              this.touchStartY = e.touches[0].clientY;
            }, { passive: true });
            
            this.element.addEventListener('touchend', (e) => {
              const endX = e.changedTouches[0].clientX;
              const endY = e.changedTouches[0].clientY;
              const deltaX = this.touchStartX - endX;
              const deltaY = this.touchStartY - endY;
              
              if (Math.abs(deltaX) > Math.abs(deltaY) && Math.abs(deltaX) > 50) {
                if (deltaX > 0) {
                  this.nextSlide();
                } else {
                  this.prevSlide();
                }
              }
            }, { passive: true });
            
            
          }
          
          updateSlideState() {
            if (this.isTransitioning) return;
            
            
            const transitionType = this.element.getAttribute('data-transition') || 'fade';
            const duration = parseInt(this.element.getAttribute('data-transition-duration') || '500');
            
            
            this.isTransitioning = true;
            
            // Atualizar slides com animação específica
            this.slides.forEach((slide, index) => {
              const isActive = index === this.currentSlide;
              
              // Reset para evitar conflitos
              slide.style.zIndex = isActive ? '2' : '1';
              
              switch (transitionType) {
                case 'slide':
                  if (isActive) {
                    slide.style.transform = 'translateX(0%)';
                    slide.style.opacity = '1';
                  } else if (index < this.currentSlide) {
                    slide.style.transform = 'translateX(-100%)';
                    slide.style.opacity = '1';
                  } else {
                    slide.style.transform = 'translateX(100%)';
                    slide.style.opacity = '1';
                  }
                  slide.style.transformStyle = 'initial';
                  break;
                  
                case 'fade':
                  slide.style.opacity = isActive ? '1' : '0';
                  slide.style.transform = 'none';
                  slide.style.transformStyle = 'initial';
                  break;
                  
                case 'cube':
                  slide.style.transformStyle = 'preserve-3d';
                  slide.style.transformOrigin = 'center center';
                  if (isActive) {
                    slide.style.transform = 'rotateY(0deg)';
                    slide.style.opacity = '1';
                  } else if (index < this.currentSlide) {
                    slide.style.transform = 'rotateY(90deg)';
                    slide.style.opacity = '0';
                  } else {
                    slide.style.transform = 'rotateY(-90deg)';
                    slide.style.opacity = '0';
                  }
                  break;
                  
                case 'cover':
                  slide.style.opacity = '1';
                  slide.style.transformStyle = 'initial';
                  if (isActive) {
                    slide.style.transform = 'translateY(0%)';
                  } else if (index < this.currentSlide) {
                    slide.style.transform = 'translateY(-100%)';
                  } else {
                    slide.style.transform = 'translateY(100%)';
                  }
                  break;
                  
                case 'flip':
                  slide.style.transformStyle = 'preserve-3d';
                  slide.style.transformOrigin = 'center center';
                  if (isActive) {
                    slide.style.transform = 'rotateX(0deg)';
                    slide.style.opacity = '1';
                  } else if (index < this.currentSlide) {
                    slide.style.transform = 'rotateX(-90deg)';
                    slide.style.opacity = '0';
                  } else {
                    slide.style.transform = 'rotateX(90deg)';
                    slide.style.opacity = '0';
                  }
                  break;
                  
                case 'none':
                  slide.style.opacity = isActive ? '1' : '0';
                  slide.style.transform = 'none';
                  slide.style.transition = 'none';
                  slide.style.transformStyle = 'initial';
                  break;
                  
                default:
                  // Fade como padrão
                  slide.style.opacity = isActive ? '1' : '0';
                  slide.style.transform = 'none';
                  slide.style.transformStyle = 'initial';
              }
              
              slide.classList.toggle('active', isActive);
              slide.setAttribute('aria-hidden', !isActive);
              

            });
            
            // Atualizar indicadores
            this.indicators.forEach((indicator, index) => {
              const isActive = index === this.currentSlide;
              indicator.classList.toggle('active', isActive);
              indicator.setAttribute('aria-selected', isActive);
              

            });
            
            // Resetar flag de transição após a animação
            setTimeout(() => {
              this.isTransitioning = false;
            }, duration);
          }
          
          nextSlide() {
            if (this.isTransitioning) return;
            this.currentSlide = (this.currentSlide + 1) % this.slides.length;
            this.updateSlideState();
          }
          
          prevSlide() {
            if (this.isTransitioning) return;
            this.currentSlide = this.currentSlide === 0 ? this.slides.length - 1 : this.currentSlide - 1;
            this.updateSlideState();
          }
          
          goToSlide(index) {
            if (this.isTransitioning) return;
            if (index >= 0 && index < this.slides.length) {
              this.currentSlide = index;
              this.updateSlideState();
            }
          }
          
          startAutoPlay() {
            const autoPlay = this.element.getAttribute('data-auto-play') === 'true';
            const interval = parseInt(this.element.getAttribute('data-interval') || '4000');
            
            if (autoPlay && !this.isPlaying) {
              this.isPlaying = true;
              this.autoPlayInterval = setInterval(() => {
                this.nextSlide();
              }, interval);
              
            }
          }
          
          stopAutoPlay() {
            if (this.autoPlayInterval) {
              clearInterval(this.autoPlayInterval);
              this.autoPlayInterval = null;
              this.isPlaying = false;
              
            }
          }
          
          toggleAutoPlay() {
            if (this.isPlaying) {
              this.stopAutoPlay();
            } else {
              this.startAutoPlay();
            }
          }
          
          destroy() {
            this.stopAutoPlay();
            this.element.removeAttribute('data-carousel-initialized');
            this.element._carouselInstance = null;

          }
        }
        
        // Função para inicializar todos os carrosseis
        function initAllCarousels() {
          const carousels = document.querySelectorAll('[data-component="carousel"], .iluria-carousel, [data-element-type="carousel"]');
          
          carousels.forEach((carousel, index) => {
            if (!carousel.hasAttribute('data-carousel-initialized')) {

              try {
                new IluriaCarousel(carousel);
              } catch (error) {
                console.error(\`❌ Erro ao inicializar carrossel \${index + 1}:\`, error);
              }
            } else {

            }
          });
          

        }
        
        // Inicializar quando DOM estiver pronto
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', initAllCarousels);
        } else {
          initAllCarousels();
        }
        
        // MutationObserver para novos carrosseis adicionados dinamicamente
        const observer = new MutationObserver((mutations) => {
          mutations.forEach((mutation) => {
            mutation.addedNodes.forEach((node) => {
              if (node.nodeType === 1) {
                // Verificar se o próprio nó é um carrossel
                if (node.hasAttribute && 
                    (node.getAttribute('data-component') === 'carousel' ||
                     node.getAttribute('data-element-type') === 'carousel' ||
                     node.classList.contains('iluria-carousel'))) {
                  if (!node.hasAttribute('data-carousel-initialized')) {

                    try {
                      new IluriaCarousel(node);
                    } catch (error) {
                      console.error('❌ Erro ao inicializar novo carrossel:', error);
                    }
                  }
                }
                
                // Verificar carrosseis dentro do nó
                if (node.querySelectorAll) {
                  const newCarousels = node.querySelectorAll('[data-component="carousel"], .iluria-carousel, [data-element-type="carousel"]');
                  newCarousels.forEach(carousel => {
                    if (!carousel.hasAttribute('data-carousel-initialized')) {

                      try {
                        new IluriaCarousel(carousel);
                      } catch (error) {
                        console.error('❌ Erro ao inicializar carrossel interno:', error);
                      }
                    }
                  });
                }
              }
            });
          });
        });
        
        observer.observe(document.body, {
          childList: true,
          subtree: true
        });
        
        // Expor classe globalmente para depuração
        window.IluriaCarousel = IluriaCarousel;
        window.initAllCarousels = initAllCarousels;
        

      })();
    `;
    
    if (doc.head) {
      doc.head.appendChild(script);
    }
  } catch (error) {
    console.error('Erro ao injetar script de carrossel:', error);
  }
}

// Exporta o componente
export default {
  name: 'CarouselLoader',
  // Propriedades do componente
  props: {
    // Token de autenticação para a API (se necessário no futuro)
    authToken: {
      type: String,
      required: false
    },
    // Documento onde o script será injetado
    document: {
      type: Object,
      required: true
    }
  },
  // Métodos do componente
  methods: {
    // Inicializa o carregador de carrosseis
    init() {
      injectCarouselScript(this.document, this.authToken);
    },
    
    // Handler para atualização do carrossel
    handleCarouselUpdate(event) {
      try {
        const { element, transitionType, transitionDuration } = event.detail || {}
        if (!element || !this.document) return
        
        // Atualiza o carrossel com as novas configurações
        const carousel = this.document.getElementById(element.id) || element
        if (carousel) {
          if (transitionType) {
            carousel.setAttribute('data-transition', transitionType)
          }
          if (transitionDuration) {
            carousel.setAttribute('data-transition-duration', transitionDuration.toString())
          }
          
          // Disparar evento para atualizar as transições
          const transitionEvent = new CustomEvent('transitionUpdated', {
            detail: { 
              type: transitionType, 
              duration: transitionDuration,
              force: true 
            }
          })
          carousel.dispatchEvent(transitionEvent)
        }
      } catch (error) {
        console.error('Erro ao atualizar carrossel:', error)
      }
    }
  },
  mounted() {
    this.init();
    // Adiciona o event listener quando o componente é montado
    document.addEventListener('carousel-updated', this.handleCarouselUpdate)
  },
  unmounted() {
    // Remove o event listener quando o componente é desmontado
    document.removeEventListener('carousel-updated', this.handleCarouselUpdate)
  },
  // Observadores
  watch: {
    // Observa mudanças no documento para reinicializar se necessário
    document: {
      handler(newDoc) {
        if (newDoc) {
          this.init();
        }
      },
      immediate: true
    },
    // Observa mudanças no token para reinicializar se necessário
    authToken: {
      handler() {
        this.init();
      },
      immediate: true
    }
  }
};

// Exporta os componentes de carrossel
export const carouselComponents = {
  carousel: {
    name: 'Image Carousel',
    html: `<div 
        data-component="carousel" 
        data-element-type="carousel"
        data-auto-play="true"
        data-interval="4000"
        data-show-indicators="true"
        data-show-navigation="true"
        data-transition="fade"
        data-transition-duration="500"
        class="iluria-carousel"
        style="
          position: relative;
          width: 100%;
          height: 450px;
          min-height: 350px;
          overflow: hidden;
          border-radius: 8px;
          background-color: #f3f4f6;
          box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
        ">
        <!-- Conteúdo do carrossel aqui -->
      </div>`,
    defaultStyles: {}
  }
};
</script>

<style scoped>
/* Estilos específicos do componente, se necessário */
</style> 
