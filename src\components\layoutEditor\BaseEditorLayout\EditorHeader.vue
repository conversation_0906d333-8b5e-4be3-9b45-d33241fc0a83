<template>
  <header class="editor-header">
    <div class="header-left">
      <IluriaButton 
        class="back-button"
        size="medium"
        :hugeIcon="Backward02Icon"
        @click="navigateToDashboard"
      >
        <span class="button-text">{{ $t('layoutEditor.back') }}</span>
      </IluriaButton>
    </div>
    
    <div class="header-center">
      <!-- Controles de viewport - escondidos em mobile -->
      <div class="viewport-controls">
        <button 
          v-for="size in ['mobile', 'desktop']"
          :key="size"
          :class="{ active: viewportSize === size }"
          @click="$emit('viewport-change', size)"
          class="viewport-btn"
          :title="$t(`layoutEditor.${size}View`)"
        >
          <span class="icon" :class="size">
            <svg v-if="size === 'mobile'" width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="5" y="2" width="14" height="20" rx="2" ry="2"></rect>
              <line x1="12" y1="18" x2="12" y2="18"></line>
            </svg>
            <svg v-else width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
              <rect x="2" y="3" width="20" height="14" rx="2" ry="2"></rect>
              <line x1="8" y1="21" x2="16" y2="21"></line>
              <line x1="12" y1="17" x2="12" y2="21"></line>
            </svg>
          </span>
        </button>
      </div>
    </div>
    
    <div class="header-right">
      <!-- ✅ NOVO: Sistema de Navegação de Layouts (movido para direita) -->
      <div class="layout-navigation">
        <div class="current-layout-selector" @click="toggleLayoutMenu($event)">
          <HugeiconsIcon :icon="getCurrentLayoutIcon()" class="layout-icon-huge" />
          <span class="layout-name">{{ getCurrentLayoutName() }}</span>
          <svg 
            class="dropdown-icon" 
            :class="{ open: isLayoutMenuOpen }"
            width="16" 
            height="16" 
            viewBox="0 0 24 24" 
            fill="none" 
            stroke="currentColor" 
            stroke-width="2"
          >
            <path d="M6 9l6 6 6-6"/>
          </svg>
        </div>
        
        <!-- Menu Dropdown de Layouts -->
        <div v-show="isLayoutMenuOpen" class="layout-menu-dropdown" v-click-outside="closeLayoutMenu">
          <!-- Pesquisa -->
          <div class="search-section">
            <input 
              v-model="searchQuery"
              type="text" 
              :placeholder="t('layoutEditor.searchLayouts')"
              class="search-input"
            />
          </div>
          
          <!-- Página Inicial (sem categoria) -->
          <template v-for="layout in filteredLayouts.static" :key="layout.layoutId">
            <div 
              class="menu-item page-item" 
              @click="switchToLayout(layout)"
              :class="{ active: currentLayout?.layoutId === layout.layoutId }"
            >
              <div class="menu-item-content">
                <HugeiconsIcon :icon="Home03Icon" class="menu-icon-huge" />
                <span>{{ layout.name }}</span>
                <span v-if="layout.isDefault" class="default-badge">{{ t('layoutEditor.defaultBadge') }}</span>
              </div>
            </div>
          </template>
          
          <!-- Layouts de Produto -->
          <div class="menu-section">
            <div class="section-header expandable" @click="toggleSubmenu('products')">
              <span>{{ t('layoutEditor.products') }}</span>
              <svg 
                class="submenu-arrow" 
                :class="{ expanded: expandedMenus.products }"
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2"
              >
                <path d="M9 18l6-6-6-6"/>
              </svg>
            </div>
            
            <div v-if="expandedMenus.products" class="submenu">
              <template v-for="layout in filteredLayouts.product" :key="layout.layoutId">
                <div 
                  class="submenu-item" 
                  @click="switchToLayout(layout)"
                  :class="{ active: currentLayout?.layoutId === layout.layoutId }"
                >
                  <div class="submenu-item-content">
                    <HugeiconsIcon :icon="ShoppingBag01Icon" class="submenu-icon-huge" />
                    <span>{{ layout.name }}</span>
                    <span v-if="layout.isDefault" class="default-badge">{{ t('layoutEditor.defaultBadge') }}</span>
                  </div>
                  
                  <!-- Botão duplicar (só aparece no hover) -->
                  <button 
                    v-if="layout.duplicatable"
                    @click.stop="createNewLayout('product', layout.layoutId)"
                    class="duplicate-btn"
                    :title="t('layoutEditor.duplicateLayout')"
                  >
                    <HugeiconsIcon :icon="Copy01Icon" class="duplicate-icon" />
                  </button>
                </div>
              </template>
              

            </div>
          </div>
          
          <!-- Layouts de Coleção -->
          <div class="menu-section">
            <div class="section-header expandable" @click="toggleSubmenu('collections')">
              <span>{{ t('layoutEditor.collections') }}</span>
              <svg 
                class="submenu-arrow" 
                :class="{ expanded: expandedMenus.collections }"
                width="16" 
                height="16" 
                viewBox="0 0 24 24" 
                fill="none" 
                stroke="currentColor" 
                stroke-width="2"
              >
                <path d="M9 18l6-6-6-6"/>
              </svg>
            </div>
            
            <div v-if="expandedMenus.collections" class="submenu">
              <template v-for="layout in filteredLayouts.collection" :key="layout.layoutId">
                <div 
                  class="submenu-item" 
                  @click="switchToLayout(layout)"
                  :class="{ active: currentLayout?.layoutId === layout.layoutId }"
                >
                  <div class="submenu-item-content">
                    <HugeiconsIcon :icon="Folder01Icon" class="submenu-icon-huge" />
                    <span>{{ layout.name }}</span>
                    <span v-if="layout.isDefault" class="default-badge">{{ t('layoutEditor.defaultBadge') }}</span>
                  </div>
                  
                  <button 
                    v-if="layout.duplicatable"
                    @click.stop="createNewLayout('collection', layout.layoutId)"
                    class="duplicate-btn"
                    :title="t('layoutEditor.duplicateLayout')"
                  >
                    <HugeiconsIcon :icon="Copy01Icon" class="duplicate-icon" />
                  </button>
                </div>
              </template>
              

            </div>
          </div>
          
          <!-- Layouts do Sistema -->
          <div class="menu-section">
            <div class="section-header">{{ t('layoutEditor.system') }}</div>
            <template v-for="layout in availableLayouts.system" :key="layout.layoutId">
              <div 
                class="menu-item" 
                @click="switchToLayout(layout)"
                :class="{ active: currentLayout?.layoutId === layout.layoutId }"
              >
                <HugeiconsIcon :icon="ShoppingCart01Icon" v-if="layout.layoutId === 'cart'" class="menu-icon-huge" />
                <HugeiconsIcon v-else :icon="getSystemIcon(layout.layoutId)" class="menu-icon-huge" />
                <span>{{ layout.name }}</span>
              </div>
            </template>
          </div>
        </div>
      </div>

      <!-- Alternador de modo -->
      <div class="mode-toggle">
        <button
          :class="{ active: mode === 'edit' }"
          @click="$emit('mode-change', 'edit')"
          class="mode-btn"
          :title="$t('layoutEditor.editMode')"
        >
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M11 5H6a2 2 0 00-2 2v11a2 2 0 002 2h11a2 2 0 002-2v-5m-1.414-9.414a2 2 0 112.828 2.828L11.828 15H9v-2.828l8.586-8.586z"/>
          </svg>
          <span class="button-text">{{ $t('layoutEditor.editMode') }}</span>
        </button>
        <button
          :class="{ active: mode === 'view' }"
          @click="$emit('mode-change', 'view')"
          class="mode-btn"
          :title="$t('layoutEditor.viewMode')"
        >
          <svg class="icon" viewBox="0 0 24 24" fill="none" stroke="currentColor">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"/>
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"/>
          </svg>
          <span class="button-text">{{ $t('layoutEditor.viewMode') }}</span>
        </button>
      </div>

      <!-- Botões de Undo/Redo -->
      <div v-if="mode === 'edit'" class="undo-redo-controls">
        <button 
          class="undo-redo-btn undo-btn"
          :disabled="!canUndo"
          @click="$emit('undo')"
          :title="$t('layoutEditor.undo') + ' (Ctrl+Z)'"
        >
          <HugeiconsIcon :icon="Undo03Icon" :size="20" />
        </button>
        
        <button 
          class="undo-redo-btn redo-btn"
          :disabled="!canRedo"
          @click="$emit('redo')"
          :title="$t('layoutEditor.redo') + ' (Ctrl+Y)'"
        >
         <HugeiconsIcon :icon="Redo03Icon" :size="20" />
        </button>
      </div>
      
      <!-- Botão de salvar -->
      <button 
        class="save-button"
        :class="{ 
          'loading': saveState === 'loading', 
          'success': saveState === 'success' 
        }"
        :disabled="saveState === 'loading'"
        @click="handleSave"
        :title="$t('layoutEditor.save')"
      >
        <!-- Ícone normal -->
        <svg 
          v-if="saveState === 'normal'" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2.2"
          stroke-linecap="round"
          stroke-linejoin="round"
        >
          <path d="M19 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h11l5 5v11a2 2 0 0 1-2 2z"/>
          <polyline points="17,21 17,13 7,13 7,21"/>
          <polyline points="7,3 7,8 15,8"/>
        </svg>
        
        <!-- Ícone de loading (girando) -->
        <svg 
          v-if="saveState === 'loading'" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2.5"
          stroke-linecap="round"
          class="loading-spinner"
        >
          <path d="M21 12a9 9 0 11-6.219-8.56"/>
        </svg>
        
        <!-- Ícone de sucesso -->
        <svg 
          v-if="saveState === 'success'" 
          width="20" 
          height="20" 
          viewBox="0 0 24 24" 
          fill="none" 
          stroke="currentColor" 
          stroke-width="2.8"
          stroke-linecap="round"
          stroke-linejoin="round"
          class="success-check"
        >
          <polyline points="9,11 12,14 22,4"/>
          <path d="M21 12c0 5-4 9-9 9s-9-4-9-9 4-9 9-9c1.5 0 2.9.4 4.1 1"/>
        </svg>
        
        <span class="button-text">
          {{ saveState === 'loading' ? $t('layoutEditor.saving') : 
             saveState === 'success' ? $t('layoutEditor.saved') : 
             $t('layoutEditor.save') }}
        </span>
      </button>
    </div>
  </header>
  
  <!-- ✅ NOVO: Modal de Criação de Layout -->
  <CreateLayoutModal
    v-model="showCreateLayoutModal"
    :base-layout-id="createLayoutData.baseLayoutId"
    :base-type="createLayoutData.baseType"
    @created="handleLayoutCreated"
  />

</template>

<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { HugeiconsIcon } from '@hugeicons/vue';
import { 
  Backward02Icon, 
  Settings01Icon, 
  Download05Icon, 
  ShoppingCart01Icon, 
  Home03Icon, 
  ShoppingBag01Icon, 
  Folder01Icon,
  Copy01Icon,
  Alert01Icon,
  Undo03Icon,
  Redo03Icon
} from '@hugeicons-pro/core-stroke-rounded';
import { useRouter, useRoute } from 'vue-router';
import { useI18n } from 'vue-i18n';
import { useLayoutManager } from '@/composables/useLayoutManager';
import CreateLayoutModal from '@/components/layoutEditor/modals/CreateLayoutModal.vue';


const router = useRouter();
const route = useRoute();
const { t } = useI18n();
const saveState = ref('normal'); // 'normal', 'loading', 'success'

// ✅ NOVO: Sistema de gerenciamento de layouts
const {
  availableLayouts,
  currentLayout,
  isLoading: isLoadingLayouts,
  loadLayouts,
  createLayout,
  setCurrentLayout,
  findLayoutById
} = useLayoutManager();

// Estado do menu de layouts
const isLayoutMenuOpen = ref(false);
const searchQuery = ref('');
const expandedMenus = ref({
  products: false,
  collections: false
});

// Modal de criação de layout
const showCreateLayoutModal = ref(false);
const createLayoutData = ref({
  baseLayoutId: null,
  baseType: null
});

const navigateToDashboard = () => {
  if (route.query.theme) {
    router.push({ name: 'themes-gallery' });
  } else {
    router.push('/');
  }
};

// ✅ NOVO: Métodos do sistema de layouts
const toggleLayoutMenu = (event) => {
  event?.stopPropagation();
  isLayoutMenuOpen.value = !isLayoutMenuOpen.value;
};

const closeLayoutMenu = () => {
  isLayoutMenuOpen.value = false;
  searchQuery.value = '';
  expandedMenus.value = {
    products: false,
    collections: false
  };
};

const toggleSubmenu = (menuKey) => {
  expandedMenus.value[menuKey] = !expandedMenus.value[menuKey];
};

const switchToLayout = (layout) => {
  setCurrentLayout(layout);
  emit('layout-change', layout);
  closeLayoutMenu();
};

const createNewLayout = (type, baseLayoutId) => {
  // Sempre exige um baseLayoutId - não permite criar do zero
  if (!baseLayoutId) {
    console.warn('createNewLayout: baseLayoutId é obrigatório');
    return;
  }
  
  createLayoutData.value = {
    baseLayoutId,
    baseType: type
  };
  showCreateLayoutModal.value = true;
  closeLayoutMenu();
};

const handleLayoutCreated = async (layoutData) => {
  try {
    const newLayout = await createLayout(
      layoutData.baseLayoutId,
      layoutData.newName,
      layoutData.type
    );
    
    // Navegar para o novo layout
    switchToLayout(newLayout);
    
    // Notificar sucesso
    // TODO: Adicionar toast de sucesso
  } catch (error) {
    console.error('Erro ao criar layout:', error);
    // TODO: Adicionar toast de erro
  }
};

// Computed para filtros
const filteredLayouts = computed(() => {
  if (!searchQuery.value) return availableLayouts.value;
  
  const query = searchQuery.value.toLowerCase();
  const filtered = {};
  
  Object.entries(availableLayouts.value).forEach(([category, layouts]) => {
    const filteredCategory = layouts.filter(layout => 
      layout.name.toLowerCase().includes(query) ||
      layout.layoutId.toLowerCase().includes(query)
    );
    if (filteredCategory.length > 0) {
      filtered[category] = filteredCategory;
    }
  });
  
  return filtered;
});

// Métodos para obter informações do layout atual
const getCurrentLayoutIcon = () => {
  if (!currentLayout.value) return Home03Icon;
  
  const iconMap = {
    'index': Home03Icon,
    'product': ShoppingBag01Icon,
    'collection': Folder01Icon,
    'cart': ShoppingCart01Icon,
    '404': ShoppingCart01Icon
  };
  
  return iconMap[currentLayout.value.layoutId] || Home03Icon;
};

const getCurrentLayoutName = () => {
  return currentLayout.value?.name || 'Página inicial';
};

// Função para obter ícones do sistema
const getSystemIcon = (layoutId) => {
  const systemIcons = {
    '404': Alert01Icon,
    'error': Alert01Icon
  };
  return systemIcons[layoutId] || ShoppingCart01Icon;
};

// Carregar layouts quando componente monta
onMounted(async () => {
  try {

    await loadLayouts();
    

    
    // Definir layout padrão se não houver um selecionado
    if (!currentLayout.value && availableLayouts.value.static?.length > 0) {
      setCurrentLayout(availableLayouts.value.static[0]);
    }
  } catch (error) {
    console.error('❌ [EditorHeader] Erro ao carregar layouts:', error);
  }
});

// Click outside directive (implementação simples)
const vClickOutside = {
  mounted(el, binding) {
    el.clickOutsideEvent = (event) => {
      if (!(el === event.target || el.contains(event.target))) {
        binding.value();
      }
    };
    document.addEventListener('click', el.clickOutsideEvent);
  },
  unmounted(el) {
    if (el.clickOutsideEvent) {
      document.removeEventListener('click', el.clickOutsideEvent);
    }
  }
};

const emit = defineEmits(['mode-change', 'viewport-change', 'save-html', 'undo', 'redo', 'layout-change', 'settings-update']);

const handleSave = async () => {
  if (saveState.value === 'loading') return;
  
  saveState.value = 'loading';
  
  // Emite o evento para salvar
  emit('save-html');
};

// Função para ser chamada quando o salvamento for bem-sucedido
const onSaveSuccess = () => {
  saveState.value = 'success';
  
  // Volta ao normal após 2 segundos
  setTimeout(() => {
    saveState.value = 'normal';
  }, 2000);
};

// Função para ser chamada quando o salvamento falhar
const onSaveError = () => {
  saveState.value = 'normal';
};

const props = defineProps({
  mode: {
    type: String,
    required: true,
    validator: (value) => ['edit', 'view'].includes(value)
  },
  viewportSize: {
    type: String,
    required: true,
    validator: (value) => ['mobile', 'desktop'].includes(value)
  },
  canUndo: {
    type: Boolean,
    default: false
  },
  canRedo: {
    type: Boolean,
    default: false
  }
});

defineExpose({
  onSaveSuccess,
  onSaveError
});
</script>

<style scoped>
.editor-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1.5rem;
  background-color: var(--iluria-color-surface);
  border-bottom: 1px solid var(--iluria-color-border);
  height: 64px;
  box-sizing: border-box;
}

.header-left,
.header-center,
.header-right {
  display: flex;
  align-items: center;
  gap: 1rem;
}

.header-center {
  flex: 1;
  justify-content: center;
}

.page-title {
  font-size: 1.25rem;
  font-weight: 600;
  color: white;
  margin: 0;
  letter-spacing: 0.5px;
}

/* Viewport Controls */
.viewport-controls {
  display: flex;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 4px;
  gap: 4px;
  margin-left: 1rem;
}

.viewport-btn {
  background: none;
  border: none;
  padding: 6px 12px;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
}

.viewport-btn:hover {
  background: var(--iluria-color-hover);
}

.viewport-btn.active {
  background: var(--iluria-color-border);
}

.viewport-btn .icon {
  width: 18px;
  height: 18px;
  color: var(--iluria-color-text-secondary);
}

.viewport-btn.active .icon {
  color: var(--iluria-color-text-primary);
}

/* Mode Toggle */
.mode-toggle {
  display: flex;
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  padding: 4px;
  gap: 4px;
  position: relative;
}

.mode-btn {
  position: relative;
  z-index: 1;
  background: transparent;
  border: none;
  padding: 8px 16px;
  cursor: pointer;
  border-radius: 6px;
  display: flex;
  align-items: center;
  gap: 8px;
  font-size: 0.875rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  transition: all 0.2s ease;
}

.mode-btn:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.mode-btn.active {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-border);
}

.mode-btn .icon {
  width: 16px;
  height: 16px;
}

/* Undo/Redo Controls */
.undo-redo-controls {
  display: flex;
  gap: 6px;
}

.undo-redo-btn {
  width: 40px;
  height: 40px;
  background: transparent;
  color: var(--iluria-color-text-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.2s ease;
  position: relative;
}

.undo-redo-btn:hover:not(:disabled) {
  background: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
}

.undo-redo-btn:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: transparent;
  color: var(--iluria-color-text-disabled);
  border-color: var(--iluria-color-border);
}

.undo-redo-btn:disabled:hover {
  background: transparent;
  color: var(--iluria-color-text-disabled);
  border-color: var(--iluria-color-border);
}

.undo-redo-btn svg {
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

/* Buttons */
:deep(.back-button) {
  background: transparent;
  color: var(--iluria-color-text-secondary);
  border: 1px solid var(--iluria-color-border);
  
  &:hover {
    background: var(--iluria-color-border);
    color: var(--iluria-color-text-primary);
  }
}

:deep(.settings-button) {
  background: transparent;
  color: var(--iluria-color-text-secondary);
  border: 1px solid var(--iluria-color-border);
  
  &:hover {
    background: var(--iluria-color-border);
    color: var(--iluria-color-text-primary);
  }
}

/* Save Button */
.save-button {
  height: 40px;
  padding: 0 16px;
  background: transparent;
  color: var(--iluria-color-text-secondary);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  font-weight: 600;
  font-size: 0.875rem;
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 8px;
  transition: all 0.2s ease;
  position: relative;
}

.save-button:hover:not(:disabled) {
  background: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
}

.save-button:disabled {
  opacity: 0.4;
  cursor: not-allowed;
  background: transparent;
  color: var(--iluria-color-text-disabled);
  border-color: var(--iluria-color-border);
}

.save-button.loading {
  background: transparent;
  color: var(--iluria-color-text-secondary);
  border-color: var(--iluria-color-border);
  cursor: wait;
}

.save-button.success {
  background: transparent;
  color: var(--iluria-color-text-secondary);
  border-color: var(--iluria-color-border);
}

/* Loading spinner animation */
.loading-spinner {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from {
    transform: rotate(0deg);
  }
  to {
    transform: rotate(360deg);
  }
}

/* Success check animation */
.success-check {
  animation: checkmark 0.6s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

@keyframes checkmark {
  0% {
    transform: scale(0) rotate(45deg);
    opacity: 0;
  }
  50% {
    transform: scale(1.2) rotate(45deg);
    opacity: 1;
  }
  100% {
    transform: scale(1) rotate(45deg);
    opacity: 1;
  }
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .editor-header {
    padding: 0.5rem 1rem;
    height: 56px;
  }
  
  /* Esconder textos dos botões, mantendo apenas ícones */
  .button-text {
    display: none;
  }
  
  /* Esconder controles de viewport em mobile */
  .viewport-controls {
    display: none;
  }
  
  /* Ajustar padding dos botões de modo */
  .mode-btn {
    padding: 8px;
    min-width: 40px;
  }
  
  /* Ajustar espaçamento entre elementos */
  .header-left,
  .header-center,
  .header-right {
    gap: 0.5rem;
  }
  
  /* Garantir que os botões IluriaButton também sigam o padrão */
  :deep(.back-button .button-text),
  :deep(.settings-button .button-text),
  :deep(.save-button .button-text),
  :deep(.undo-button .button-text),
  :deep(.redo-button .button-text) {
    display: none;
  }
  
  /* Ajustar controles de undo/redo em mobile */
  .undo-redo-controls {
    gap: 4px;
  }
  
  .undo-redo-btn {
    width: 36px;
    height: 36px;
    border-radius: 6px;
  }
  
  .save-button {
    height: 36px;
    padding: 0 12px;
    border-radius: 6px;
    font-size: 0.8rem;
  }
}

/* ✅ NOVO: Estilos do Sistema de Navegação de Layouts */
.layout-navigation {
  position: relative;
}

.current-layout-selector {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  padding: 0.5rem 0.75rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  min-width: 180px;
}

.current-layout-selector:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-primary);
}

.layout-icon {
  font-size: 1.125rem;
  flex-shrink: 0;
}

.layout-icon-huge {
  width: 20px;
  height: 20px;
  flex-shrink: 0;
  color: currentColor;
}

.layout-name {
  flex: 1;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  font-size: 0.875rem;
}

.dropdown-icon {
  color: var(--iluria-color-text-secondary);
  transition: transform 0.2s ease;
  flex-shrink: 0;
}

.dropdown-icon.open {
  transform: rotate(180deg);
}

.layout-menu-dropdown {
  position: absolute;
  top: calc(100% + 8px);
  left: 0;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  box-shadow: var(--iluria-shadow-lg);
  z-index: 1100;
  max-height: 400px;
  overflow-y: auto;
  min-width: 320px;
}

.search-section {
  padding: 1rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.search-input {
  width: 100%;
  padding: 0.5rem 0.75rem;
  border: 1px solid var(--iluria-color-border);
  border-radius: 6px;
  background: var(--iluria-color-surface);
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  transition: border-color 0.2s ease;
}

.search-input:focus {
  outline: none;
  border-color: var(--iluria-color-primary);
}

.search-input::placeholder {
  color: var(--iluria-color-text-secondary);
}

.menu-section {
  border-bottom: 1px solid var(--iluria-color-border);
}

.menu-section:last-child {
  border-bottom: none;
}

.section-header {
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 600;
  color: var(--iluria-color-text-secondary);
  text-transform: uppercase;
  letter-spacing: 0.5px;
  background: var(--iluria-color-surface);
  border-bottom: 1px solid var(--iluria-color-border);
}

.section-header.expandable {
  display: flex;
  align-items: center;
  justify-content: space-between;
  cursor: pointer;
  transition: background-color 0.2s ease;
}

.section-header.expandable:hover {
  background: var(--iluria-color-hover);
}

.submenu-arrow {
  color: var(--iluria-color-text-secondary);
  transition: transform 0.2s ease;
}

.submenu-arrow.expanded {
  transform: rotate(90deg);
}

.menu-item {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  padding: 0.875rem 1rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  background: var(--iluria-color-surface);
  border: 1px solid transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.menu-item:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-border);
  transform: translateY(-1px);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}

.menu-item.active {
  background: var(--iluria-color-primary-bg);
  color: var(--iluria-color-primary);
  font-weight: 500;
  border-color: var(--iluria-color-primary-border);
  box-shadow: 0 2px 6px rgba(0, 0, 0, 0.12);
}



.menu-icon {
  font-size: 1rem;
  flex-shrink: 0;
}

.menu-icon-huge {
  width: 18px;
  height: 18px;
  flex-shrink: 0;
  color: currentColor;
}

.submenu-icon-huge {
  width: 16px;
  height: 16px;
  flex-shrink: 0;
  color: currentColor;
}

.submenu-item-content,
.menu-item-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.page-item {
  border-bottom: none;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  background: var(--iluria-color-surface);
  border: 1px solid transparent;
  box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
}

.page-item:hover {
  transform: translateY(-1px);
  border-color: var(--iluria-color-border);
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.15);
}



.submenu {
  background: var(--iluria-color-surface);
}

.submenu-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem 1rem 0.75rem 1.5rem;
  cursor: pointer;
  transition: all 0.2s ease;
  color: var(--iluria-color-text-primary);
  font-size: 0.875rem;
  position: relative;
  border-radius: 8px;
  margin: 0.25rem 0.5rem;
  background: var(--iluria-color-surface);
  border: 1px solid transparent;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.submenu-item:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-border);
  transform: translateX(3px);
  box-shadow: 0 3px 6px rgba(0, 0, 0, 0.12);
}

.submenu-item.active {
  background: var(--iluria-color-primary-bg);
  color: var(--iluria-color-primary);
  font-weight: 500;
  border-color: var(--iluria-color-primary-border);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}



.default-badge {
  background: var(--iluria-color-primary-bg);
  color: var(--iluria-color-primary);
  font-size: 0.625rem;
  font-weight: 600;
  padding: 0.125rem 0.375rem;
  border-radius: 4px;
  text-transform: uppercase;
  letter-spacing: 0.5px;
}

.duplicate-btn {
  background: none;
  border: none;
  padding: 0.25rem;
  border-radius: 4px;
  cursor: pointer;
  color: var(--iluria-color-text-secondary);
  opacity: 1;
  transition: all 0.2s ease;
  display: flex;
  align-items: center;
  justify-content: center;
}

.duplicate-btn:hover {
  background: var(--iluria-color-border);
  color: var(--iluria-color-text-primary);
}

.duplicate-icon {
  width: 14px;
  height: 14px;
  color: currentColor;
}

/* Estilos para ícones HugeIcons */
.layout-icon-huge,
.menu-icon-huge,
.submenu-icon-huge {
  flex-shrink: 0;
  color: currentColor;
}

.layout-icon-huge {
  width: 20px;
  height: 20px;
}

.menu-icon-huge {
  width: 18px;
  height: 18px;
}

.submenu-icon-huge {
  width: 16px;
  height: 16px;
}

/* Responsive adjustments para layout navigation */
@media (max-width: 768px) {
  .layout-navigation {
    margin-left: 0.5rem;
  }
  
  .current-layout-selector {
    min-width: 140px;
    padding: 0.375rem 0.5rem;
  }
  
  .layout-name {
    font-size: 0.8rem;
  }
  
  .layout-menu-dropdown {
    min-width: 280px;
    max-height: 300px;
  }
  
  .search-section {
    padding: 0.75rem;
  }
  
  .menu-item,
  .submenu-item {
    padding: 0.5rem 0.75rem;
    font-size: 0.8rem;
  }
  
  .submenu-item {
    padding-left: 1.5rem;
  }
}

/* Scrollbar customizado para o dropdown */
.layout-menu-dropdown::-webkit-scrollbar {
  width: 6px;
}

.layout-menu-dropdown::-webkit-scrollbar-track {
  background: var(--iluria-color-surface);
  border-radius: 3px;
}

.layout-menu-dropdown::-webkit-scrollbar-thumb {
  background: var(--iluria-color-border);
  border-radius: 3px;
}

.layout-menu-dropdown::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-text-secondary);
}
</style>
