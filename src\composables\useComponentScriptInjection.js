import { ref } from 'vue'
import { injectProductGridScript } from '@/components/layoutEditor/scripts/ProductGridLoader.vue'
import { injectCarouselScript } from '@/components/layoutEditor/scripts/CarouselLoader.vue'
import { injectPaymentBenefitsScript } from '@/components/layoutEditor/scripts/PaymentBenefitsLoader.vue'
import { injectLocationScript } from '@/components/layoutEditor/scripts/LocationLoader.vue'
import { injectVideoScript } from '@/components/layoutEditor/scripts/VideoLoader.vue'



/**
 * Sistema centralizado para injeção de scripts de componentes
 * 
 * Remove a necessidade de configuração manual em arquivos grandes como
 * EditorCanvas.vue e EditorView.vue
 */

// Registry de componentes e seus scripts
const componentScriptRegistry = ref(new Map())

/**
 * Registra um script para um tipo de componente
 * @param {string} componentType - Tipo do componente (ex: 'carousel', 'video')
 * @param {Object} config - Configuração do script
 */
export function registerComponentScript(componentType, config) {
  componentScriptRegistry.value.set(componentType, {
    ...config,
    componentType
  })

}

/**
 * Detecta componentes no documento e retorna lista dos que precisam de scripts
 * @param {Document} doc - Documento para verificar
 * @returns {Array} Lista de componentes encontrados
 */
export function detectComponentsInDocument(doc) {

  
  if (!doc) {

    return []
  }
  
  const foundComponents = []
  

  
  // Para cada componente registrado, verifica se existe no documento
  for (const [componentType, config] of componentScriptRegistry.value) {
    const { selectors } = config

    
    let elementsFound = 0
    for (const selector of selectors) {
      const elements = doc.querySelectorAll(selector);

      elementsFound += elements.length
      
      // Log detalhado dos elementos encontrados
      elements.forEach((el, index) => {

      });
    }
    

    
    if (elementsFound > 0) {
      foundComponents.push({
        componentType,
        config,
        elementsCount: elementsFound
      })
    }
  }
  

  return foundComponents
}

/**
 * Injeta scripts para todos os componentes detectados no documento
 * @param {Document} doc - Documento onde injetar os scripts
 * @param {string} authToken - Token de autenticação se necessário
 */
export function injectAllComponentScripts(doc, authToken = '') {
 

  if (!doc) {
    console.warn('⚠️ [ComponentScripts] Documento não fornecido')
    return
  }
  
  // TESTE MANUAL - buscar grids de produtos diretamente
  const manualGrids = doc.querySelectorAll('[data-component="product-grid"], [data-component="dynamic-grid-produtos"], .iluria-product-grid');

  const foundComponents = detectComponentsInDocument(doc)
 
  // Debug específico para location components
  const locationElements = doc.querySelectorAll('[data-component="location"]')

  locationElements.forEach((el, i) => {

  })
  
  if (foundComponents.length === 0) {
   

    // Fallback para produtos
    if (manualGrids.length > 0) {
    
      try {
        // Import direto com await para função async
        import('@/components/layoutEditor/scripts/ProductGridLoader.vue').then(async module => {
          await module.injectProductGridScript(doc, authToken)

        }).catch(error => {
          console.error('❌ [injectAllComponentScripts] Erro no fallback:', error);
        });
      } catch (error) {
        console.error('❌ [injectAllComponentScripts] Erro no fallback:', error);
      }
    }
    
    // Fallback para location components
    if (locationElements.length > 0) {

      try {
        import('@/components/layoutEditor/scripts/LocationLoader.vue').then(module => {
          module.injectLocationScript(doc, authToken)

        }).catch(error => {
          console.error('❌ [injectAllComponentScripts] Erro no fallback de location:', error);
        });
      } catch (error) {
        console.error('❌ [injectAllComponentScripts] Erro no fallback de location:', error);
      }
    }
    
    return
  }
  

  
  // Injeta scripts para cada componente encontrado
  foundComponents.forEach(({ componentType, config }) => {
    try {

      const { injectionFunction, requiresAuth } = config
      
      if (requiresAuth && !authToken) {
        console.warn(`⚠️ [injectAllComponentScripts] Script ${componentType} requer autenticação mas token não foi fornecido`)
        return
      }
      

      injectionFunction(doc, authToken)

      
    } catch (error) {
      console.error(`❌ [injectAllComponentScripts] Erro ao injetar script para ${componentType}:`, error)
    }
  })
  

}

/**
 * Registra um componente com detecção automática
 * @param {string} componentType - Tipo do componente
 * @param {Array<string>} selectors - Seletores CSS para detectar o componente
 * @param {Function} injectionFunction - Função para injetar o script
 * @param {boolean} requiresAuth - Se requer token de autenticação
 */
export function registerAutoDetectComponent(componentType, selectors, injectionFunction, requiresAuth = false) {
  registerComponentScript(componentType, {
    selectors: Array.isArray(selectors) ? selectors : [selectors],
    injectionFunction,
    requiresAuth,
    autoDetect: true
  })
}

/**
 * Inicializa o sistema com os componentes padrão
 */
export function initializeComponentScriptSystem() {

  
  // Registra scripts dos componentes principais
  registerComponentScript('product-grid', {
    selectors: [
      '[data-component="product-grid"]',
      '[data-component="dynamic-grid-produtos"]',
      '[data-product-grid="true"]',
      '[data-element-type="dynamicProductGrid"]',
      '.iluria-product-grid'
    ],
    async injectionFunction(doc, authToken) {
      // Importa dinamicamente o ProductGridLoader
      try {

        const { injectProductGridScript } = await import('@/components/layoutEditor/scripts/ProductGridLoader.vue')
        await injectProductGridScript(doc, authToken)

      } catch (error) {
        console.error('❌ [ProductGrid] Erro ao carregar script:', error)
      }
    },
    requiresAuth: false, // Temporariamente false para debug
    description: 'Script para carregamento dinâmico de produtos da API'
  })
  
  registerAutoDetectComponent(
    'carousel',
    [
      '[data-component="carousel"]',
      '.iluria-carousel',
      '[data-element-type="carousel"]'
    ],
    injectCarouselScript,
    false // Não requer autenticação
  )
  
  registerAutoDetectComponent(
    'payment-benefits',
    [
      '[data-component="payment-benefits"]',
      '.iluria-payment-benefits',
      '[data-element-type="paymentBenefits"]'
    ],
    injectPaymentBenefitsScript,
    false // Não requer autenticação
  )
  

  registerAutoDetectComponent(
    'location',
    [
      '[data-component="location"]',
      '.iluria-location-component',
      '[data-element-type="location"]'
    ],
    async (doc, authToken) => {

      try {
        const { injectLocationScript } = await import('@/components/layoutEditor/scripts/LocationLoader.vue')
        injectLocationScript(doc, authToken)

      } catch (error) {
        console.error('❌ [LocationScript] Erro ao injetar:', error);
      }
    },
    false // Não requer autenticação
  )

  registerAutoDetectComponent(
    'video',
    [
      '[data-component="video"]',
      '.iluria-video',
      '[data-element-type="video"]'
    ],
    injectVideoScript,
    false // Não requer autenticação
  )

  // Componentes futuros podem ser facilmente adicionados aqui
  // Exemplo:
  // registerAutoDetectComponent(
  //   'video',
  //   [
  //     '[data-component="video"]',
  //     '.iluria-video',
  //     '[data-element-type="video"]'
  //   ],
  //   injectVideoScript,
  //   false
  // )
  
    
}

/**
 * Função principal de conveniência para usar em qualquer lugar
 * @param {Document} doc - Documento
 * @param {string} authToken - Token de autenticação
 */
export function useComponentScriptInjection(doc, authToken = '') {
 

  try {
    // Inicializa o sistema se ainda não foi feito
    if (componentScriptRegistry.value.size === 0) {
     
      initializeComponentScriptSystem()
    }

    // Injeta scripts para todos os componentes
    injectAllComponentScripts(doc, authToken)
  } catch (error) {
    console.error('❌ [useComponentScriptInjection] ERRO:', error);
  }
}

/**
 * Hook principal do composable
 */
export function useComponentScripts() {
  return {
    registerComponentScript,
    registerAutoDetectComponent,
    detectComponentsInDocument,
    injectAllComponentScripts,
    useComponentScriptInjection,
    initializeComponentScriptSystem
  }
} 