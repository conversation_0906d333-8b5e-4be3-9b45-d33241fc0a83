import axios from 'axios';
import { API_URLS } from '@/config/api.config';

const endpoint = API_URLS.SOCIAL_LINKS_BASE_URL;

const socialLinksService = {
  getSocialLinks: () => {
    return axios.get(endpoint).then(response => response.data);
  },
  
  saveSocialLinks: (socialLinksData) => {
    return axios.put(endpoint, socialLinksData).then(response => response.data);
  },
  
  createSocialLinks: (socialLinksData) => {
    return axios.post(endpoint, socialLinksData).then(response => response.data);
  },
  
  deleteSocialLinks: () => {
    return axios.delete(endpoint);
  }
};

export default socialLinksService; 