import { ref, computed, onMounted, nextTick, triggerRef } from 'vue'
import { getAllConfigs, getConfigsByCategories, getConfigsByLayoutType } from '@/components/layoutEditor/configs/index.js'

export const useComponentLibrary = (layoutType = null) => {
  const isInitialized = ref(false)
  const forceUpdate = ref(0) // trigger para forçar atualizações
  const currentLayoutType = ref(layoutType)


  // Força carregamento na próxima tick para garantir que as configs estejam prontas
  const initializeIfNeeded = async () => {
    if (!isInitialized.value) {
      try {
        
        // Aguardar próxima tick para garantir que tudo está carregado
        await nextTick()
        
        const configs = getAllConfigs()
        
        if (configs.length === 0) {
          return
        }
        
        isInitialized.value = true

      } catch (error) {
        console.error('❌ [useComponentLibrary] Erro ao inicializar:', error)
        isInitialized.value = true
      }
    }
  }

  // Biblioteca de componentes do novo sistema
  const componentLibrary = computed(() => {
    // Trigger forçado para reatividade
    forceUpdate.value

    const configs = getAllConfigs()
    
    if (configs.length === 0) {
      return {}
    }
    
    const library = {}
    configs.forEach((config, index) => {
      if (config.type && config.name) {
        library[config.type] = config
      } else {
      }
    })
    
    
    
    return library
  })

  // Categorias do novo sistema (com filtro por layout)
  const componentCategories = computed(() => {
    // Trigger forçado para reatividade
    forceUpdate.value

    // ✅ NOVO: Filtrar componentes por tipo de layout
    const allConfigs = currentLayoutType.value 
      ? getConfigsByLayoutType(currentLayoutType.value)
      : getAllConfigs()

    
    // Agrupar por categoria
    const categories = {}
    allConfigs.forEach(config => {
      const category = config.category || 'other'
      if (!categories[category]) {
        categories[category] = []
      }
      categories[category].push(config)
    })
    
    // Transformar para o formato esperado pelo ComponentMenu
    const formattedCategories = {}
    
    // Mapeamento de categorias para títulos legíveis
    const categoryTitles = {
      media: 'Mídia',
      layout: 'Layout', 
      ecommerce: 'E-commerce',
      'e-commerce': 'E-commerce',
      testimonials: 'Depoimentos',
      marketing: 'Marketing',
      content: 'Conteúdo'
    }
    
    // Lista de componentes protegidos (não podem ser adicionados)
    const protectedComponents = ['header', 'footer']
    
    Object.entries(categories).forEach(([categoryId, configs]) => {
      const components = {}
      configs.forEach(config => {
        // Filtrar componentes protegidos
        if (!protectedComponents.includes(config.type)) {
          components[config.type] = {
            title: config.name,
            description: config.description || `Componente ${config.name}`,
            type: config.type,
            layoutTypes: config.layoutTypes || [] // ✅ Incluir info dos layouts
          }
        }
      })
      
      // Só adicionar categoria se tiver componentes válidos
      if (Object.keys(components).length > 0) {
        formattedCategories[categoryId] = {
          title: categoryTitles[categoryId] || categoryId,
          components: components
        }
      }
    })
    
    return formattedCategories
  })

  // ✅ NOVO: Função para atualizar o tipo de layout
  const setLayoutType = (layoutType) => {
    currentLayoutType.value = layoutType
    forceRefresh()
  }

  // Função para forçar atualização
  const forceRefresh = () => {

    forceUpdate.value++
    triggerRef(forceUpdate)
  }

  // Inicializa automaticamente quando a função é chamada
  initializeIfNeeded()

  // Log periódico para debug
  if (import.meta.env.DEV) {
    const interval = setInterval(() => {


      
      if (Object.keys(componentLibrary.value).length > 0) {
          clearInterval(interval)
      }
    }, 2000)
    

    setTimeout(() => clearInterval(interval), 30000)
  }

  return {
    componentLibrary,
    componentCategories,
    isInitialized,
    initializeIfNeeded,
    forceRefresh,
    setLayoutType,
    currentLayoutType
  }
}