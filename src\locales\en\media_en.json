{"title": "Media Library", "library": "Library", "addNew": "Add New", "uploadFiles": "Upload Files", "mediaLibrary": "Media Library", "searchMedia": "Search media...", "noItems": "No media items found", "allMediaItems": "All Media Items", "allDates": "All Dates", "filter": "Filter", "listView": "List View", "gridView": "Grid View", "sortBy": "Sort By", "newest": "Newest", "oldest": "Oldest", "name": "Name", "date": "Date", "fileSize": "File Size", "dimensions": "Dimensions", "type": "Type", "allTypes": "All Types", "images": "Images", "videos": "Videos", "documents": "Documents", "audio": "Audio", "archives": "Archives", "other": "Other", "mediaItems": "{{count}} items", "mediaItemSelected": "{{count}} selected", "deleteSelected": "Delete Selected", "deleteConfirm": "Are you sure you want to permanently delete the selected items?", "deleteSuccess": "Selected items have been deleted successfully", "deleteError": "Error deleting items", "uploadProgress": "Uploading {{fileName}} ({{progress}}%)", "uploadComplete": "Upload complete", "uploadError": "Error uploading file", "fileTooLarge": "File is too large. Maximum size is {{maxSize}}MB.", "invalidFileType": "This file type is not allowed.", "maxFilesExceeded": "You can only upload {{maxFiles}} files at a time.", "dragAndDrop": "Drop files here or click to upload", "or": "or", "browseFiles": "Browse Files", "fileInfo": {"name": "Name", "fileUrl": "File URL", "fileSize": "File Size", "fileType": "File Type", "dimensions": "Dimensions", "uploadedOn": "Uploaded On", "modifiedOn": "Modified On", "altText": "Alt Text", "title": "Title", "caption": "Caption", "description": "Description", "copyUrl": "Copy URL", "editImage": "Edit Image", "download": "Download", "delete": "Delete", "close": "Close", "saveChanges": "Save Changes", "saving": "Saving...", "saved": "Saved", "error": "Error saving changes"}, "imageEditor": {"title": "Edit Image", "crop": "Crop", "rotate": "Rotate", "flip": "Flip", "brightness": "Brightness", "contrast": "Contrast", "saturation": "Saturation", "blur": "Blur", "sharpen": "Sharpen", "grayscale": "Grayscale", "sepia": "Sepia", "invert": "Invert", "reset": "Reset", "apply": "Apply", "cancel": "Cancel", "save": "Save", "saving": "Saving...", "saved": "Image saved successfully", "error": "Error saving image"}, "fileTypes": {"image": "Images", "video": "Videos", "audio": "Audio", "document": "Documents", "spreadsheet": "Spreadsheets", "presentation": "Presentations", "archive": "Archives", "code": "Code", "other": "Other"}, "fileIcons": {"image": "Image", "video": "Video", "audio": "Audio", "document": "Document", "pdf": "PDF", "spreadsheet": "Spreadsheet", "presentation": "Presentation", "archive": "Archive", "code": "Code", "other": "File"}, "fileActions": {"view": "View", "edit": "Edit", "download": "Download", "copyLink": "Copy Link", "delete": "Delete", "select": "Select", "deselect": "Deselect"}, "bulkActions": {"download": "Download Selected", "delete": "Delete Selected", "deleteConfirm": "Are you sure you want to delete the selected items?", "deleteSuccess": "Selected items have been deleted", "deleteError": "Error deleting items"}, "uploader": {"title": "Upload Files", "subtitle": "Drag and drop files here or click to browse", "browse": "Browse Files", "or": "or", "maxFileSize": "Max file size: {{size}}MB", "allowedFileTypes": "Allowed file types: {{types}}", "uploading": "Uploading...", "uploaded": "Uploaded", "failed": "Failed", "cancel": "Cancel", "retry": "Retry", "done": "Done", "error": {"tooManyFiles": "Too many files. Maximum allowed is {{maxFiles}}.", "fileTooLarge": "File '{{name}}' is too large. Maximum size is {{maxSize}}MB.", "invalidType": "File '{{name}}' has an invalid type.", "unknown": "An error occurred while uploading '{{name}}'."}}, "fileDetails": {"title": "File Details", "name": "Name", "url": "URL", "size": "Size", "type": "Type", "dimensions": "Dimensions", "uploaded": "Uploaded", "modified": "Modified", "altText": "Alt Text", "titleField": "Title", "caption": "Caption", "description": "Description", "copyUrl": "Copy URL", "copySuccess": "URL copied to clipboard", "copyError": "Error copying URL", "save": "Save", "saving": "Saving...", "saved": "Saved", "error": "Error saving changes"}, "filePreview": {"unsupported": "Preview not available for this file type.", "download": "Download"}, "fileList": {"name": "Name", "size": "Size", "type": "Type", "date": "Date", "actions": "Actions"}, "folderTree": {"title": "Folders", "createFolder": "New Folder", "renameFolder": "<PERSON><PERSON>", "deleteFolder": "Delete Folder", "folderName": "Folder Name", "create": "Create", "rename": "<PERSON><PERSON>", "delete": "Delete", "cancel": "Cancel", "deleteConfirm": "Are you sure you want to delete this folder?", "deleteSuccess": "Folder deleted successfully", "deleteError": "Error deleting folder", "createSuccess": "Folder created successfully", "createError": "Error creating folder", "renameSuccess": "Folder renamed successfully", "renameError": "Error renaming folder"}}