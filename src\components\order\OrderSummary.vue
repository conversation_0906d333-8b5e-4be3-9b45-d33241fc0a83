<!-- src/components/order/OrderSummary.vue -->
<template>
  <div class="order-summary-container">
    <!-- Customer Data -->
    <ViewContainer
      :title="t('order.customerDataTitle')"
      :icon="UserMultiple02Icon"
      iconColor="blue"
      class="mb-6"
    >
      <div v-if="customer && customer.name" class="customer-info">
        <div class="info-row">
          <span class="info-label">{{ t('order.customerName') }}:</span>
          <span class="info-value">{{ customer.name }}</span>
        </div>
        <div class="info-row">
          <span class="info-label">{{ t('order.customerEmail') }}:</span>
          <a :href="`mailto:${customer.email}`" class="info-email">{{ customer.email }}</a>
        </div>
        <div v-if="customer.phone" class="info-row">
          <span class="info-label">{{ t('order.customerPhone') }}:</span>
          <span class="info-value">{{ customer.phone }}</span>
        </div>
      </div>
      <div v-else class="no-customer">
        <div class="no-customer-icon">
          <HugeiconsIcon :icon="UserMultiple02Icon" :size="32" class="text-gray-400" />
        </div>
        <p class="no-customer-text">{{ t('customerInfo.selectClient') }}</p>
      </div>
    </ViewContainer>

    <!-- Shipping Address -->
    <ViewContainer
      :title="t('order.shippingAddress')"
      :icon="Location01Icon"
      iconColor="cyan"
      class="mb-6"
    >
      <div v-if="hasShippingAddress" class="address-section">
        <div class="address-info">
          <p class="address-text">{{ formattedAddress }}</p>
        </div>
      </div>
      <div v-else class="no-address">
        <div class="no-address-icon">
          <HugeiconsIcon :icon="Location01Icon" :size="32" class="text-gray-400" />
        </div>
        <p class="no-address-text">{{ t('order.selectShippingAddressFirst') }}</p>
      </div>
    </ViewContainer>

    <!-- Products -->
    <ViewContainer
      :title="t('product.products')"
      :icon="ShoppingBasket01Icon"
      iconColor="orange"
      class="mb-6"
    >
      <div v-if="items.length === 0" class="no-products">
        <div class="no-products-icon">
          <HugeiconsIcon :icon="ShoppingBasket01Icon" :size="32" class="text-gray-400" />
        </div>
        <p class="no-products-text">{{ t('product.noProductsAdded') }}</p>
      </div>
      <div v-else class="products-list">
        <div v-for="(item, index) in items" :key="index" class="product-item">
          <div class="product-main">
            <span class="product-name">{{ item.name }}</span>
            <span class="product-quantity">x{{ item.quantity }}</span>
          </div>
          <div class="product-price">R$ {{ formatCurrency(item.price * item.quantity) }}</div>
        </div>
      </div>
    </ViewContainer>

    <!-- Order Totals -->
    <ViewContainer
      :title="t('order.orderSummary')"
      :icon="DollarSquareIcon"
      iconColor="green"
      class="mb-6"
    >
      <div class="totals-section">
        <div class="total-row">
          <span class="total-label">{{ t('order.subtotal') }}</span>
          <span class="total-value">R$ {{ formatCurrency(subtotal) }}</span>
        </div>
        
        <div v-if="discount.amount > 0" class="total-row discount-row">
          <div class="total-label">
            <span>{{ t('order.discountFixed') }}</span>
            <span v-if="discount.description" class="discount-desc">({{ discount.description }})</span>
          </div>
          <span class="total-value discount-value">-R$ {{ formatCurrency(discount.amount) }}</span>
        </div>
        
        <div v-if="shipping > 0" class="total-row shipping-row">
          <div class="total-label">
            <span>{{ t('order.shipping') }}</span>
            <span v-if="shippingMethod" class="shipping-method">({{ shippingMethod }})</span>
          </div>
          <span class="total-value">R$ {{ formatCurrency(shipping) }}</span>
        </div>
        
        <div v-if="shippingDiscount > 0" class="total-row discount-row">
          <div class="total-label">
            <span>{{ t('order.shippingDiscount') }}</span>
            <span v-if="shippingDiscountDescription" class="discount-desc">({{ shippingDiscountDescription }})</span>
          </div>
          <span class="total-value discount-value">-R$ {{ formatCurrency(shippingDiscount) }}</span>
        </div>
        
        <div class="total-row final-total">
          <span class="total-label final-label">{{ t('order.total') }}</span>
          <span class="total-value final-value">R$ {{ formatCurrency(total) }}</span>
        </div>
      </div>
    </ViewContainer>

    <!-- Order Status -->
    <ViewContainer
      :title="t('order.orderStatusTitle')"
      :icon="Settings02Icon"
      iconColor="purple"
    >
      <div class="status-section">
        <div class="status-item">
          <div class="status-header">
            <HugeiconsIcon :icon="TruckDeliveryIcon" :size="20" class="text-purple-600 mr-2" />
            <span class="status-title">{{ t('order.shippingStatus') }}:</span>
          </div>
          <span class="status-badge" :class="statusClass">
            {{ statusLabel }}
          </span>
        </div>

        <div class="status-item">
          <div class="status-header">
            <HugeiconsIcon :icon="CreditCardIcon" :size="20" class="text-purple-600 mr-2" />
            <span class="status-title">{{ t('order.paymentStatus') }}:</span>
          </div>
          <span class="status-badge" :class="paymentStatusClass">
            {{ paymentStatusLabel }}
          </span>
        </div>
      </div>
    </ViewContainer>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import ViewContainer from '@/components/layout/ViewContainer.vue';
import { useStatus } from '@/utils/statusHelper';
import { HugeiconsIcon } from '@hugeicons/vue';
import {
  UserMultiple02Icon,
  ShoppingBasket01Icon,
  DollarSquareIcon,
  Settings02Icon,
  TruckDeliveryIcon,
  CreditCardIcon,
  Location01Icon
} from '@hugeicons-pro/core-stroke-rounded';

const { t } = useI18n();
const { getStatusClass, getStatusLabel } = useStatus();

const props = defineProps({
  orderForm: {
    type: Object,
    required: true
  }
});

const customer = computed(() => {
  if (!props.orderForm) return null;
  return {
    name: props.orderForm.customerName,
    email: props.orderForm.customerEmail,
    phone: props.orderForm.customerPhone
  };
});

const hasShippingAddress = computed(() => {
  return !!props.orderForm.addressStreet || !!props.orderForm.shippingAddress;
});

const shippingAddress = computed(() => {
  if (props.orderForm.shippingAddress) return props.orderForm.shippingAddress;
  return {
    street: props.orderForm.addressStreet,
    number: props.orderForm.addressNumber,
    district: props.orderForm.addressDistrict,
    city: props.orderForm.addressCity,
    state: props.orderForm.addressState,
    zipCode: props.orderForm.addressZipCode,
    complement: props.orderForm.addressComplement
  };
});

const formattedAddress = computed(() => {
  const addr = shippingAddress.value;
  if (!addr) return '';
  
  let address = `${addr.street || ''}, ${addr.number || ''}`;
  if (addr.complement) {
    address += ` - ${addr.complement}`;
  }
  address += `\n${addr.district || ''}, ${addr.city || ''} - ${addr.state || ''}`;
  if (addr.zipCode) {
    address += `\nCEP: ${addr.zipCode}`;
  }
  
  return address;
});

const items = computed(() => props.orderForm.items || []);
const subtotal = computed(() => Number(props.orderForm.productsValue) || 0);
const discount = computed(() => ({
  amount: Number(props.orderForm.productsDiscountValue) || 0,
  description: props.orderForm.productsDiscountDescription
}));
const shipping = computed(() => Number(props.orderForm.shippingValue) || 0);
const shippingMethod = computed(() => props.orderForm.shippingType);
const shippingDiscount = computed(() => Number(props.orderForm.shippingDiscount) || 0);
const shippingDiscountDescription = computed(() => props.orderForm.shippingDiscountDescription);

const total = computed(() => {
  if (typeof props.orderForm.calculateTotal === 'function') {
    return props.orderForm.calculateTotal(props.orderForm);
  }
  return subtotal.value + shipping.value - discount.value.amount - shippingDiscount.value;
});

const statusClass = computed(() => {
  return getStatusClass(props.orderForm.shippingStatus);
});

const statusLabel = computed(() => {
  return getStatusLabel(props.orderForm.shippingStatus);
});

const paymentStatusClass = computed(() => {
  return getStatusClass(props.orderForm.paymentStatus);
});

const paymentStatusLabel = computed(() => {
  return getStatusLabel(props.orderForm.paymentStatus);
});

const formatCurrency = (value) => {
  return Number(value).toFixed(2).replace('.', ',');
};
</script>

<style scoped>
.order-summary-container {
  display: flex;
  flex-direction: column;
  gap: 0;
}

/* Customer Info */
.customer-info {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.info-row {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.info-label {
  font-size: 13px;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
}

.info-value {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  font-weight: 500;
}

.info-email {
  font-size: 14px;
  color: var(--iluria-color-primary);
  text-decoration: none;
  font-weight: 500;
  transition: color 0.2s ease;
}

.info-email:hover {
  color: var(--iluria-color-primary-dark);
  text-decoration: underline;
}

.no-customer {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  text-align: center;
}

.no-customer-icon {
  color: var(--iluria-color-text-muted);
  margin-bottom: 12px;
}

.no-customer-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Address */
.address-section {
  padding: 0;
}

.address-info {
  background: var(--iluria-color-primary-lightest);
  padding: 16px;
  border-radius: 8px;
  border: 1px solid var(--iluria-color-primary-light);
}

.address-text {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  line-height: 1.5;
  white-space: pre-line;
  margin: 0;
}

.no-address {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  text-align: center;
}

.no-address-icon {
  color: var(--iluria-color-text-muted);
  margin-bottom: 12px;
}

.no-address-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

/* Products */
.no-products {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 24px 16px;
  text-align: center;
}

.no-products-icon {
  color: var(--iluria-color-text-muted);
  margin-bottom: 12px;
}

.no-products-text {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
}

.products-list {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.product-item {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 12px 0;
  border-bottom: 1px solid var(--iluria-color-border);
}

.product-item:last-child {
  border-bottom: none;
}

.product-main {
  flex: 1;
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.product-name {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  line-height: 1.4;
}

.product-quantity {
  font-size: 13px;
  color: var(--iluria-color-text-secondary);
}

.product-price {
  font-size: 14px;
  font-weight: 600;
  color: var(--iluria-color-success);
  white-space: nowrap;
  margin-left: 12px;
}

/* Totals */
.totals-section {
  display: flex;
  flex-direction: column;
  gap: 12px;
}

.total-row {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  padding: 8px 0;
}

.total-label {
  font-size: 14px;
  color: var(--iluria-color-text-primary);
  display: flex;
  flex-direction: column;
  gap: 2px;
}

.total-value {
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  white-space: nowrap;
  margin-left: 12px;
}

.discount-row {
  background: var(--iluria-color-error-lightest);
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid var(--iluria-color-error-light);
}

.discount-value {
  color: var(--iluria-color-error) !important;
}

.discount-desc,
.shipping-method {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
  font-weight: 400;
}

.shipping-row {
  background: var(--iluria-color-primary-lightest);
  margin: 0 -16px;
  padding: 12px 16px;
  border-radius: 6px;
  border: 1px solid var(--iluria-color-primary-light);
}

.final-total {
  border-top: 2px solid var(--iluria-color-border);
  padding-top: 16px;
  margin-top: 8px;
}

.final-label {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
}

.final-value {
  font-size: 18px;
  font-weight: 700;
  color: var(--iluria-color-success);
}

/* Status */
.status-section {
  display: flex;
  flex-direction: column;
  gap: 16px;
}

.status-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 12px;
  background: var(--iluria-color-surface);
  border-radius: 8px;
  border: 1px solid var(--iluria-color-border);
}

.status-header {
  display: flex;
  align-items: center;
  gap: 8px;
  color: var(--iluria-color-text-secondary);
}

.status-title {
  font-size: 14px;
  font-weight: 500;
}

.status-badge {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 9999px;
  font-size: 12px;
  font-weight: 500;
  white-space: nowrap;
}

/* Responsive */
@media (max-width: 768px) {
  .product-item {
    flex-direction: column;
    gap: 8px;
  }
  
  .product-price {
    margin-left: 0;
    align-self: flex-end;
  }
  
  .status-item {
    flex-direction: column;
    align-items: flex-start;
    gap: 8px;
  }
  
  .status-badge {
    align-self: flex-end;
  }
}
</style>
