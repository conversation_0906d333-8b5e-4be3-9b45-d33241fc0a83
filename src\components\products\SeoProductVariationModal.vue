<template>
  <IluriaModal 
    v-model="visible" 
    :dialog-style="{ width: '90vw', maxWidth: '1200px' }"
    @save="handleSave"
    @cancel="handleCancel"
    :save-label="t('seoProductVariation.edit.saveSettings')"
    :cancel-label="t('cancel')"
  >
    <!-- Custom Header -->
    <template #header>
      <div class="flex items-center justify-between w-full">
        <div class="flex items-center gap-3">
          <IluriaTitle class="text-lg font-medium text-white">
            {{ t('seoProductVariation.variationType') }}: {{ attributeValue }}
          </IluriaTitle>
        </div>
        
        <IluriaButton 
          @click="handleRestore"
          color="secondary"
          size="small"
          class="inline-flex items-center mr-4"
          :hugeIcon="RestoreBinIcon" 
        >
          {{ t('seoProductVariation.edit.restoreDefault') }}
        </IluriaButton>
      </div>
    </template>

    <!-- Form Content -->
    <div class="space-y-6 mt-8">
      <!-- Meta Title and URL Row -->
      <div class="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div>
          <IluriaInputText 
            v-model="formData.metaTitle"
            :label="t('seoProductVariation.metaTitle')"
            :placeholder="t('seoProductVariation.edit.metaTitlePlaceholder')"
            id="metaTitle"
          />
        </div>
        <div>
          <IluriaInputText 
            v-model="formData.url"
            :label="t('seoProductVariation.urlSlug')"
            :placeholder="t('seoProductVariation.edit.urlSlugPlaceholder')"
            id="urlSlug"
          />
        </div>
      </div>

      <!-- Meta Description -->
      <div>
        <IluriaLabel class="mb-2 block">
          {{ t('seoProductVariation.metaDescription') }}
        </IluriaLabel>
        <Textarea 
          v-model="formData.metaDescription"
          :rows="4"
          :placeholder="t('seoProductVariation.edit.metaDescriptionPlaceholder')"
          id="metaDescription"
        />
      </div>

      <!-- Image Section -->
      <div>
        <IluriaLabel class="mb-2 block">
          {{ t('seoProductVariation.edit.variationImage') }}
        </IluriaLabel>
        
        <!-- Image Preview Area -->
        <div v-if="currentSeoImage" class="flex items-center justify-between p-4 rounded-lg border mb-3">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 rounded-lg overflow-hidden flex-shrink-0" 
              :style="{ backgroundColor: getColorValue(attributeValue) }">
              <img 
                :src="currentSeoImage"
                :alt="attributeValue"
                class="w-full h-full object-cover"
              />
            </div>
                          <div>
                <div>
                  <IluriaLabel class="font-medium">{{ currentImageName }}</IluriaLabel>
                </div>
                <div class="mt-1">
                  <IluriaLabel class="text-sm">{{ t('seoProductVariation.edit.variationImageDescription') }}</IluriaLabel>
                </div>
              </div>
          </div>
          <div class="flex gap-2">
            <IluriaButton 
              @click="handleImageChange"
              color="secondary"
              size="small"
            >
              {{ t('seoProductVariation.edit.changeImage') }}
            </IluriaButton>
            <IluriaButton 
              @click="removeCurrentImage"
              color="danger"
              size="small"
            >
              {{ t('remove') }}
            </IluriaButton>
          </div>
        </div>
        
        <!-- No Image State -->
        <div v-else class="flex items-center justify-between p-4 rounded-lg border-2 border-dashed mb-3">
          <div class="flex items-center space-x-4">
            <div class="w-16 h-16 rounded-lg flex items-center justify-center">
              <div 
                class="w-12 h-12 rounded flex items-center justify-center"
                :style="{ backgroundColor: getColorValue(attributeValue) }"
              >
              </div>
            </div>
            <div>
              <div>
                <IluriaLabel class="font-medium text-gray-500">{{ t('seoProductVariation.edit.noImageSelected') }}</IluriaLabel>
              </div>
              <div class="mt-1">
                <IluriaLabel class="text-sm text-gray-400">{{ t('seoProductVariation.edit.seoImageNotification') }}</IluriaLabel>
              </div>
            </div>
          </div>
          <IluriaButton 
            @click="handleImageChange"
            color="secondary"
            size="small"
          >
            {{ t('seoProductVariation.edit.changeImage') }}
          </IluriaButton>
        </div>
      </div>
    </div>
  </IluriaModal>

  <!-- Photo Upload Modal -->
  <PhotoUploadModal
    v-if="showImageModal"
    :is-open="showImageModal"
    :initial-files="initialModalFiles"
    :product-id="props.productId"
    :variation-id="null"
    :is-editing="false"
    @close="handleImageCancelled"
    @save="handleImageSelected"
  />
</template>

<script setup>
import { ref, computed, watch } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaTitle from '@/components/iluria/IluriaTitle.vue'
import IluriaModal from '@/components/iluria/IluriaModal.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import { RestoreBinIcon } from '@hugeicons-pro/core-bulk-rounded'
import Textarea from '@/components/Textarea.vue'
import PhotoUploadModal from '@/components/productVariations/PhotoUploadModal.vue'
import { productsApi } from '@/services/product.service'

const { t } = useI18n()
const toast = useToast()

// Use defineModel for two-way binding
const modelValue = defineModel()

// Computed property to control dialog visibility
const visible = computed({
  get: () => !!modelValue.value,
  set: (value) => {
    if (!value) {
      modelValue.value = null
    }
  }
})

const props = defineProps({
  productId: {
    type: String,
    required: true
  },
  attribute: {
    type: String,
    required: true
  },
  attributeValue: {
    type: String,
    required: true
  },
  variationData: {
    type: Object,
    default: () => ({})
  }
})

const emit = defineEmits(['save'])

const formData = ref({
  metaTitle: '',
  url: '',
  metaDescription: ''
})

const showImageModal = ref(false)
const selectedSeoImage = ref('')

const initialModalFiles = computed(() => {
  if (selectedSeoImage.value && selectedSeoImage.value !== 'REMOVED_BY_USER') {
    return [{
      url: selectedSeoImage.value,
      isExisting: true,
      name: `${props.attributeValue}-seo.jpg`,
      id: `seo-${props.attributeValue}`
    }]
  }
  return []
})

function getColorValue(value) {
  const colorMap = {
    'Azul': '#3B82F6',
    'Blue': '#3B82F6',
    'Vermelho': '#EF4444', 
    'Red': '#EF4444',
    'Verde': '#10B981',
    'Green': '#10B981',
    'Amarelo': '#F59E0B',
    'Yellow': '#F59E0B',
    'Roxo': '#8B5CF6',
    'Purple': '#8B5CF6',
    'Rosa': '#EC4899',
    'Pink': '#EC4899',
    'Laranja': '#F97316',
    'Orange': '#F97316',
    'Preto': '#1F2937',
    'Black': '#1F2937',
    'Branco': '#F9FAFB',
    'White': '#F9FAFB',
    'Cinza': '#6B7280',
    'Gray': '#6B7280'
  }
  
  return colorMap[value] || '#6B7280'
}

watch(() => [modelValue.value, props.variationData], () => {
  if (modelValue.value && props.variationData) {
    const data = props.variationData
    if (data.currentConfig) {
      formData.value = {
        metaTitle: data.currentConfig.metaTitle || data.defaultValues?.metaTitle || '',
        url: data.currentConfig.urlSlug || data.defaultValues?.urlSlug || '',
        metaDescription: data.currentConfig.metaDescription || data.defaultValues?.metaDescription || ''
      }
      
      selectedSeoImage.value = data.currentConfig.seoImage || data.defaultValues?.seoImage || ''
    }
  }
}, { immediate: true })

const handleSave = () => {
  if (!formData.value.metaTitle.trim()) {
    toast.showError(t('seoProductVariation.edit.metaTitleRequired'))
    return
  }
  
  if (!formData.value.url.trim()) {
    toast.showError(t('seoProductVariation.edit.urlRequired'))
    return
  }
  
  if (!formData.value.metaDescription.trim()) {
    toast.showError(t('seoProductVariation.edit.metaDescriptionRequired'))
    return
  }
  
  let seoImageToEmit = selectedSeoImage.value;
  if (seoImageToEmit === 'REMOVED_BY_USER' || seoImageToEmit === '') {
    seoImageToEmit = undefined;
  }

  const defaultImage = props.variationData.defaultValues?.seoImage || ''
  const isCustomImage = seoImageToEmit !== undefined && seoImageToEmit !== defaultImage

  const updatedVariation = {
    attribute: props.attribute,
    attributeValue: props.attributeValue,
    metaTitle: formData.value.metaTitle.trim(),
    metaDescription: formData.value.metaDescription.trim(),
    urlSlug: formData.value.url.trim(),
    ...(seoImageToEmit !== undefined ? { 
      seoImage: seoImageToEmit,
      seoImageIsCustom: isCustomImage 
    } : { seoImageIsCustom: false })
  }
  
  // Se temos uma imagem blob que precisa de upload, incluir essas informações
  if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
    updatedVariation._pendingImageUpload = {
      blobUrl: selectedSeoImage.value,
      attribute: props.attribute,
      attributeValue: props.attributeValue
    }
  }
  
  toast.showSuccess(t('seoProductVariation.edit.success'))
  
  emit('save', updatedVariation)
  modelValue.value = null
}

const handleCancel = () => {
  if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
    URL.revokeObjectURL(selectedSeoImage.value)
  }
  
  formData.value = {
    metaTitle: '',
    url: '',
    metaDescription: ''
  }
  selectedSeoImage.value = ''
  
  modelValue.value = null
}

const handleRestore = () => {
  const data = props.variationData
  if (data.defaultValues) {
    formData.value = {
      metaTitle: data.defaultValues.metaTitle || '',
      url: data.defaultValues.urlSlug || '',
      metaDescription: data.defaultValues.metaDescription || ''
    }
    
    const defaultImage = data.defaultValues?.seoImage || ''
    
    if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
      URL.revokeObjectURL(selectedSeoImage.value)
    }
    
    selectedSeoImage.value = defaultImage
    
    if (defaultImage) {
      toast.showSuccess(t('seoProductVariation.edit.defaultValuesRestored'))
    } else {
      toast.showInfo(t('seoProductVariation.edit.defaultValuesRestoredNoImage'))
    }
  }
}

const handleImageChange = () => {
  showImageModal.value = true
}

const handleImageSelected = async (photos) => {
  if (photos && photos.length >= 1) {
    if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
      URL.revokeObjectURL(selectedSeoImage.value)
    }
    
    const selectedPhoto = photos[0]
    
    // Se é uma imagem existente, apenas usar a URL
    if (selectedPhoto.url && selectedPhoto.isExisting) {
      selectedSeoImage.value = selectedPhoto.url
      toast.showSuccess(t('seoProductVariation.edit.imageUpdated'))
      showImageModal.value = false
      return
    }
    
    // Se é um novo arquivo, fazer upload para S3
    if (selectedPhoto instanceof File || selectedPhoto._isNewUpload) {
      try {
        // Validar se productId existe - verificar primeiro na prop, depois em variationData
        const currentProductId = props.productId || props.variationData?.productId
        
        // Se não temos productId, significa que o produto ainda não foi salvo
        // Neste caso, apenas guardamos a imagem como blob para upload posterior
        if (!currentProductId || currentProductId.trim() === '') {
 
          
          // Criar URL blob para preview
          const previewUrl = URL.createObjectURL(selectedPhoto)
          selectedSeoImage.value = previewUrl
          
          // Marcar que precisamos fazer upload quando o produto for salvo
          selectedPhoto._needsUpload = true
          selectedPhoto._attributeValue = props.attributeValue
          selectedPhoto._attribute = props.attribute
          
          showImageModal.value = false
          return
        }
        
        // Mostrar preview temporário
        const previewUrl = URL.createObjectURL(selectedPhoto)
        selectedSeoImage.value = previewUrl
        
        toast.showInfo(t('seoProductVariation.edit.uploadingImage'))
        
        // Fazer upload para S3
        const response = await productsApi.uploadSeoVariationImage(
          currentProductId,
          props.attribute,
          props.attributeValue,
          selectedPhoto
        )
        
        // Limpar preview temporário
        URL.revokeObjectURL(previewUrl)
        
        // Usar URL do S3
        if (response.data && response.data.imageUrl) {
          selectedSeoImage.value = response.data.imageUrl
          toast.showSuccess(t('seoProductVariation.edit.imageUploadSuccess'))
        } else {
          throw new Error('URL da imagem não retornada pelo servidor')
        }
        
      } catch (error) {
        console.error('Erro ao fazer upload da imagem SEO:', error)
        
        // Limpar preview em caso de erro
        if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
          URL.revokeObjectURL(selectedSeoImage.value)
        }
        
        selectedSeoImage.value = ''
        toast.showError(t('seoProductVariation.edit.imageUploadGenericError', { error: error.response?.data?.message || error.message }))
      }
    }
    else if (selectedPhoto.url) {
      selectedSeoImage.value = selectedPhoto.url
      toast.showSuccess(t('seoProductVariation.edit.imageUpdated'))
    }
    
    showImageModal.value = false
  } 
  else if (photos && photos.length === 0) {
    if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
      URL.revokeObjectURL(selectedSeoImage.value)
    }
    
    selectedSeoImage.value = 'REMOVED_BY_USER'
    toast.showInfo(t('seoProductVariation.edit.imageRemoved'))
    showImageModal.value = false
  }
}

const handleImageCancelled = () => {
  showImageModal.value = false
}

const currentSeoImage = computed(() => {
  if (selectedSeoImage.value === 'REMOVED_BY_USER') {
    return ''
  }
  
  return selectedSeoImage.value || props.variationData.currentConfig?.seoImage || props.variationData.defaultValues?.seoImage || ''
})

const currentImageName = computed(() => {
  if (selectedSeoImage.value) {
    if (selectedSeoImage.value.startsWith('blob:')) {
      return `${props.attributeValue}-seo-nova.jpg`
    }
    return `${props.attributeValue}-seo.jpg`
  }
  return `${props.attributeValue}-seo.jpg`
})

const removeCurrentImage = async () => {
  try {
    // Se é uma imagem do servidor (não blob e não é 'REMOVED_BY_USER')
    if (selectedSeoImage.value && 
        !selectedSeoImage.value.startsWith('blob:') && 
        selectedSeoImage.value !== 'REMOVED_BY_USER' &&
        selectedSeoImage.value.includes('s3.amazonaws.com')) {
      
      // Extrair imageId da URL
      const imageId = extractImageIdFromUrl(selectedSeoImage.value)
      
      const currentProductId = props.productId || props.variationData?.productId
      if (imageId && currentProductId) {
        await productsApi.deleteSeoVariationImage(
          currentProductId,
          props.attribute,
          props.attributeValue,
          imageId
        )
        toast.showSuccess(t('seoProductVariation.edit.imageDeletedFromServer'))
      }
    }
    
    // Se é uma imagem blob local, limpar da memória
    if (selectedSeoImage.value && selectedSeoImage.value.startsWith('blob:')) {
      URL.revokeObjectURL(selectedSeoImage.value)
    }
    
    selectedSeoImage.value = 'REMOVED_BY_USER'
    toast.showInfo(t('seoProductVariation.edit.imageRemoved'))
    
  } catch (error) {
    console.error('Erro ao deletar imagem SEO:', error)
    toast.showError(t('seoProductVariation.edit.imageDeleteError'))
  }
}

const extractImageIdFromUrl = (url) => {
  // Exemplo: https://iluria-bucket-dev.s3.amazonaws.com/storeId/product/productId/seo/attribute/attributeValue/imageId
  try {
    const parts = url.split('/')
    return parts[parts.length - 1] // Último segmento é o imageId
  } catch (error) {
    console.error('Erro ao extrair imageId da URL:', error)
    return null
  }
}
</script>
