<template>
  <div class="customer-review-sidebar-editor">
    <!-- Tabs -->
    <div class="editor-tabs">
      <button 
        v-for="tab in tabs" 
        :key="tab.value"
        :class="['tab-button', { active: activeTab === tab.value }]"
        @click="activeTab = tab.value"
      >
        {{ tab.label }}
      </button>
    </div>

    <!-- Tab Content -->
    <div class="tab-content">
      
      <!-- <PERSON><PERSON> -->
      <div v-if="activeTab === 'content'" class="tab-panel">
        
        <!-- Se<PERSON> Cabeçalho -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.header') }}</h3>
          
          <div class="form-group">
            <IluriaCheckBox
              :model-value="reviewConfig.showTitle"
              :label="$t('layoutEditor.showTitle')"
              @update:model-value="(value) => updateField('showTitle', value)"
            />
          </div>

          <div v-if="reviewConfig.showTitle" class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.title') }}</IluriaLabel>
            <IluriaInputText
              :model-value="reviewConfig.title"
              :placeholder="$t('layoutEditor.enterTitle')"
              @update:model-value="(value) => updateField('title', value)"
            />
          </div>

          <div class="form-group">
            <IluriaCheckBox
              :model-value="reviewConfig.showSubtitle"
              :label="$t('layoutEditor.showSubtitle')"
              @update:model-value="(value) => updateField('showSubtitle', value)"
            />
          </div>

          <div v-if="reviewConfig.showSubtitle" class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.subtitle') }}</IluriaLabel>
            <IluriaInputText
              :model-value="reviewConfig.subtitle"
              :placeholder="$t('layoutEditor.enterSubtitle')"
              @update:model-value="(value) => updateField('subtitle', value)"
            />
          </div>
        </div>

        <!-- Seção Layout -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.layout') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.layoutType') }}</IluriaLabel>
            <IluriaSelect 
              :model-value="reviewConfig.layout" 
              :options="[
                { value: 'grid', label: $t('layoutEditor.grid') },
                { value: 'carousel', label: $t('layoutEditor.carousel') }
              ]"
              @update:model-value="(value) => updateField('layout', value)"
            />
          </div>

          <div v-if="reviewConfig.layout === 'grid'" class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.columns') }}</IluriaLabel>
            <IluriaSelect 
              :model-value="reviewConfig.columns" 
              :options="[
                { value: '1', label: `1 ${$t('layoutEditor.column')}` },
                { value: '2', label: `2 ${$t('layoutEditor.columns')}` },
                { value: '3', label: `3 ${$t('layoutEditor.columns')}` },
                { value: '4', label: `4 ${$t('layoutEditor.columns')}` }
              ]"
              @update:model-value="(value) => updateField('columns', value)"
            />
          </div>
        </div>

        <!-- Seção Elementos -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.elements') }}</h3>
          
          <div class="form-group">
            <IluriaCheckBox
              :model-value="reviewConfig.showStars"
              :label="$t('layoutEditor.showStars')"
              @update:model-value="(value) => updateField('showStars', value)"
            />
          </div>

          <div class="form-group">
            <IluriaCheckBox
              :model-value="reviewConfig.showAvatar"
              :label="$t('layoutEditor.showAvatar')"
              @update:model-value="(value) => updateField('showAvatar', value)"
            />
          </div>
        </div>
      </div>

      <!-- Aba Design -->
      <div v-if="activeTab === 'design'" class="tab-panel">
        
        <!-- Seção Cores -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.colors') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.backgroundColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                :model-value="reviewConfig.backgroundColor"
                @update:model-value="(value) => updateField('backgroundColor', value)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.titleColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                :model-value="reviewConfig.titleColor"
                @update:model-value="(value) => updateField('titleColor', value)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.textColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                :model-value="reviewConfig.textColor"
                @update:model-value="(value) => updateField('textColor', value)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.starColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                :model-value="reviewConfig.starColor"
                @update:model-value="(value) => updateField('starColor', value)"
              />
            </div>
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.cardBackgroundColor') }}</IluriaLabel>
            <div class="color-input-wrapper">
              <IluriaColorPicker
                :model-value="reviewConfig.cardBgColor"
                @update:model-value="(value) => updateField('cardBgColor', value)"
              />
            </div>
          </div>
        </div>

        <!-- Seção Estilos -->
        <div class="section">
          <h3 class="section-title">{{ $t('layoutEditor.styling') }}</h3>
          
          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.cardBorderRadius') }}</IluriaLabel>
            <IluriaRange
              :model-value="reviewConfig.borderRadius"
              :min="0"
              :max="30"
              :step="1"
              @update:model-value="(value) => updateField('borderRadius', value)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.cardShadow') }}</IluriaLabel>
            <IluriaSelect 
              :model-value="reviewConfig.cardShadow" 
              :options="[
                { value: 'none', label: $t('layoutEditor.noShadow') },
                { value: 'light', label: $t('layoutEditor.lightShadow') },
                { value: 'medium', label: $t('layoutEditor.mediumShadow') },
                { value: 'heavy', label: $t('layoutEditor.heavyShadow') }
              ]"
              @update:model-value="(value) => updateField('cardShadow', value)"
            />
          </div>

          <div class="form-group">
            <IluriaLabel>{{ $t('layoutEditor.spacing') }}</IluriaLabel>
            <IluriaRange
              :model-value="reviewConfig.spacing"
              :min="10"
              :max="60"
              :step="5"
              @update:model-value="(value) => updateField('spacing', value)"
            />
          </div>
        </div>

      </div>

    </div>
  </div>
</template>

<script setup>
import { ref, reactive, onMounted, nextTick } from 'vue'
import { useI18n } from 'vue-i18n'
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue'
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue'
import IluriaCheckBox from '@/components/iluria/IluriaCheckBox.vue'
import IluriaLabel from '@/components/iluria/IluriaLabel.vue'
import IluriaColorPicker from '@/components/iluria/form/IluriaColorPicker.vue'
import IluriaRange from '@/components/iluria/form/IluriaRange.vue'

const { t } = useI18n()

const props = defineProps({
  element: { type: Object, required: true }
})

const emit = defineEmits(['data-changed'])

// Tabs
const tabs = [
  { value: 'content', label: t('layoutEditor.content') },
  { value: 'design', label: t('layoutEditor.design') }
]

// Estado das abas
const activeTab = ref('content')

// Helper para leitura de atributos booleanos
const getBooleanAttribute = (element, attributeName, defaultValue = false) => {
  const value = element.getAttribute(attributeName)
  if (value === null || value === undefined) return defaultValue
  if (value === 'true') return true
  if (value === 'false') return false
  return defaultValue
}

// Configuração do componente usando reactive para melhor reatividade
const reviewConfig = reactive({
  title: 'O que nossos clientes dizem',
  subtitle: 'Confira as avaliações reais dos nossos clientes',
  layout: 'grid',
  columns: '3',
  backgroundColor: '#ffffff',
  titleColor: '#2d3748',
  textColor: '#4a5568',
  starColor: '#fbbf24',
  cardBgColor: '#ffffff',
  showTitle: true,
  showSubtitle: true,
  showStars: true,
  showAvatar: true,
  borderRadius: 12,
  cardShadow: 'medium',
  spacing: 30
})

// Função para encontrar o componente customer-review real
const findCustomerReviewComponent = () => {
  let component = null
  
  // Caso 1: O próprio elemento é o componente
  if (props.element.hasAttribute('data-component') && 
      props.element.getAttribute('data-component') === 'customer-review') {
    component = props.element
  }
  
  // Caso 2: O elemento tem a classe do componente
  else if (props.element.classList.contains('iluria-customer-review')) {
    component = props.element
  }
  
  // Caso 3: Buscar dentro do elemento atual
  else {
    component = props.element.querySelector('[data-component="customer-review"], .iluria-customer-review')
  }
  
  // Caso 4: Buscar nos elementos pais
  if (!component) {
    component = props.element.closest('[data-component="customer-review"], .iluria-customer-review')
  }
  
  return component
}

// Carrega configuração usando data-config centralizado
const loadReviewConfig = () => {
  if (!props.element) {
    return
  }

  try {
    // Primeiro, encontra o componente correto
    const reviewComponent = findCustomerReviewComponent()
    
    if (!reviewComponent) {
      setDefaultConfig()
      return
    }
    
    // PRIORIDADE 1: Carrega do data-config centralizado
    const configAttr = reviewComponent.getAttribute('data-config')
    let config = {}
    
    if (configAttr) {
      try {
        config = JSON.parse(configAttr)
        
        // Valida e aplica configuração do data-config
        Object.assign(reviewConfig, {
          title: config.title || 'O que nossos clientes dizem',
          subtitle: config.subtitle || 'Confira as avaliações reais dos nossos clientes',
          layout: config.layout || 'grid',
          columns: config.columns || '3',
          backgroundColor: config.backgroundColor || '#ffffff',
          titleColor: config.titleColor || '#2d3748',
          textColor: config.textColor || '#4a5568',
          starColor: config.starColor || '#fbbf24',
          cardBgColor: config.cardBgColor || '#ffffff',
          showTitle: config.showTitle !== undefined ? config.showTitle : true,
          showSubtitle: config.showSubtitle !== undefined ? config.showSubtitle : true,
          showStars: config.showStars !== undefined ? config.showStars : true,
          showAvatar: config.showAvatar !== undefined ? config.showAvatar : true,
          borderRadius: config.borderRadius || 12,
          cardShadow: config.cardShadow || 'medium',
          spacing: config.spacing || 30
        })
        
        return
      } catch (error) {
        // Silently continue to fallback
      }
    }
    
    // PRIORIDADE 2: Fallback para atributos individuais
    Object.assign(reviewConfig, {
      title: reviewComponent.getAttribute('data-title') || 'O que nossos clientes dizem',
      subtitle: reviewComponent.getAttribute('data-subtitle') || 'Confira as avaliações reais dos nossos clientes',
      layout: reviewComponent.getAttribute('data-layout') || 'grid',
      columns: reviewComponent.getAttribute('data-columns') || '3',
      backgroundColor: reviewComponent.getAttribute('data-background-color') || '#ffffff',
      titleColor: reviewComponent.getAttribute('data-title-color') || '#2d3748',
      textColor: reviewComponent.getAttribute('data-text-color') || '#4a5568',
      starColor: reviewComponent.getAttribute('data-star-color') || '#fbbf24',
      cardBgColor: reviewComponent.getAttribute('data-card-bg-color') || '#ffffff',
      showTitle: getBooleanAttribute(reviewComponent, 'data-show-title', true),
      showSubtitle: getBooleanAttribute(reviewComponent, 'data-show-subtitle', true),
      showStars: getBooleanAttribute(reviewComponent, 'data-show-stars', true),
      showAvatar: getBooleanAttribute(reviewComponent, 'data-show-avatar', true),
      borderRadius: parseInt(reviewComponent.getAttribute('data-border-radius')) || 12,
      cardShadow: reviewComponent.getAttribute('data-card-shadow') || 'medium',
      spacing: parseInt(reviewComponent.getAttribute('data-spacing')) || 30
    })
    
    // IMPORTANTE: Salva no data-config para futuras cargas
    updateElementConfig()
    
  } catch (error) {
    setDefaultConfig()
  }
}

// Define configuração padrão
const setDefaultConfig = () => {
  Object.assign(reviewConfig, {
    title: 'O que nossos clientes dizem',
    subtitle: 'Confira as avaliações reais dos nossos clientes',
    layout: 'grid',
    columns: '3',
    backgroundColor: '#ffffff',
    titleColor: '#2d3748',
    textColor: '#4a5568',
    starColor: '#fbbf24',
    cardBgColor: '#ffffff',
    showTitle: true,
    showSubtitle: true,
    showStars: true,
    showAvatar: true,
    borderRadius: 12,
    cardShadow: 'medium',
    spacing: 30
  })
  
  // Salva a configuração padrão
  updateElementConfig()
}

// Função centralizada para atualizar qualquer campo
const updateField = (fieldName, value) => {
  reviewConfig[fieldName] = value
  
  // Aguarda próximo tick para garantir que o DOM foi atualizado
  nextTick(() => {
    updateReview()
  })
}

// Atualiza data-config centralizado
const updateElementConfig = () => {
  const reviewComponent = findCustomerReviewComponent()
  if (!reviewComponent) return
  
  try {
    const configAtual = {
      title: reviewConfig.title,
      subtitle: reviewConfig.subtitle,
      layout: reviewConfig.layout,
      columns: reviewConfig.columns,
      backgroundColor: reviewConfig.backgroundColor,
      titleColor: reviewConfig.titleColor,
      textColor: reviewConfig.textColor,
      starColor: reviewConfig.starColor,
      cardBgColor: reviewConfig.cardBgColor,
      showTitle: reviewConfig.showTitle,
      showSubtitle: reviewConfig.showSubtitle,
      showStars: reviewConfig.showStars,
      showAvatar: reviewConfig.showAvatar,
      borderRadius: reviewConfig.borderRadius,
      cardShadow: reviewConfig.cardShadow,
      spacing: reviewConfig.spacing
    }
    
    // Salva no data-config centralizado
    reviewComponent.setAttribute('data-config', JSON.stringify(configAtual))
    
    // TAMBÉM mantém atributos individuais para compatibilidade
    reviewComponent.setAttribute('data-title', configAtual.title)
    reviewComponent.setAttribute('data-subtitle', configAtual.subtitle)
    reviewComponent.setAttribute('data-layout', configAtual.layout)
    reviewComponent.setAttribute('data-columns', configAtual.columns)
    reviewComponent.setAttribute('data-background-color', configAtual.backgroundColor)
    reviewComponent.setAttribute('data-title-color', configAtual.titleColor)
    reviewComponent.setAttribute('data-text-color', configAtual.textColor)
    reviewComponent.setAttribute('data-star-color', configAtual.starColor)
    reviewComponent.setAttribute('data-card-bg-color', configAtual.cardBgColor)
    reviewComponent.setAttribute('data-show-title', configAtual.showTitle.toString())
    reviewComponent.setAttribute('data-show-subtitle', configAtual.showSubtitle.toString())
    reviewComponent.setAttribute('data-show-stars', configAtual.showStars.toString())
    reviewComponent.setAttribute('data-show-avatar', configAtual.showAvatar.toString())
    reviewComponent.setAttribute('data-border-radius', configAtual.borderRadius.toString())
    reviewComponent.setAttribute('data-card-shadow', configAtual.cardShadow)
    reviewComponent.setAttribute('data-spacing', configAtual.spacing.toString())

  } catch (error) {
    // Silently handle error
  }
}

// Atualiza o componente
const updateReview = () => {
  const reviewComponent = findCustomerReviewComponent()
  if (!reviewComponent) {
    return
  }

  try {
    // 1. Atualiza configuração centralizada
    updateElementConfig()
    
    // 2. Aplica mudanças específicas
    updateTitle()
    updateSubtitle()
    updateLayout()
    updateElementVisibility()
    
    // 3. Aplica estilos diretamente
    applyCustomerReviewStylesDirectly()
    
    // 4. Re-inicializa o componente se disponível
    refreshComponent()
    
    // 5. Chama handler do CustomerReviewComponent se disponível
    if (window.CustomerReviewComponent?.eventHandlers?.onCustomerReviewConfigChanged) {
      window.CustomerReviewComponent.eventHandlers.onCustomerReviewConfigChanged(reviewComponent, reviewConfig)
    }
    
    // 6. Notifica mudanças
    emit('data-changed', reviewConfig)

  } catch (error) {
    // Silently handle error
  }
}

// Aplica estilos diretamente no elemento
const applyCustomerReviewStylesDirectly = () => {
  if (!props.element) return
  
  try {
    // Remove estilos anteriores
    const existingStyle = props.element.querySelector('.customer-review-dynamic-styles')
    if (existingStyle) existingStyle.remove()

    const shadows = {
      none: 'none',
      light: '0 2px 4px rgba(0, 0, 0, 0.1)',
      medium: '0 4px 6px rgba(0, 0, 0, 0.1)',
      heavy: '0 10px 25px rgba(0, 0, 0, 0.15)'
    }

    // Cria novos estilos COMPLETOS com as configurações atuais
    const styles = `
      <style class="customer-review-dynamic-styles">
        .iluria-customer-review {
          background-color: ${reviewConfig.backgroundColor} !important;
          padding: ${reviewConfig.spacing}px 0 !important;
        }
        
        .iluria-customer-review h2 {
          color: ${reviewConfig.titleColor} !important;
          display: ${reviewConfig.showTitle ? 'block' : 'none'} !important;
        }
        
        .iluria-customer-review .reviews-header p,
        .iluria-customer-review .reviews-header span {
          color: ${reviewConfig.textColor} !important;
          display: ${reviewConfig.showSubtitle ? 'inline' : 'none'} !important;
        }
        
        .reviews-grid {
          display: ${reviewConfig.layout === 'grid' ? 'grid' : 'flex'} !important;
          grid-template-columns: repeat(${reviewConfig.columns}, 1fr) !important;
          gap: ${reviewConfig.spacing / 2}px !important;
        }
        
        .review-card {
          background-color: ${reviewConfig.cardBgColor} !important;
          border-radius: ${reviewConfig.borderRadius}px !important;
          box-shadow: ${shadows[reviewConfig.cardShadow]} !important;
          color: ${reviewConfig.textColor} !important;
        }
        
        .iluria-customer-review .stars svg,
        .iluria-customer-review .stars {
          display: ${reviewConfig.showStars ? 'flex' : 'none'} !important;
        }
        
        .iluria-customer-review .stars svg {
          fill: ${reviewConfig.starColor} !important;
        }
        
        .iluria-customer-review .avatar {
          display: ${reviewConfig.showAvatar ? 'flex' : 'none'} !important;
        }
        
        @media (max-width: 768px) {
          .reviews-grid { 
            grid-template-columns: ${reviewConfig.layout === 'grid' ? '1fr' : 'auto'} !important; 
          }
        }
      </style>
    `

    // Adiciona os estilos ao elemento
    props.element.insertAdjacentHTML('beforeend', styles)

  } catch (error) {
    // Silently handle error
  }
}

// Atualiza título
const updateTitle = () => {
  const reviewComponent = findCustomerReviewComponent()
  if (!reviewComponent) return
  
  const titleElement = reviewComponent.querySelector('h2')
  if (titleElement) {
    titleElement.textContent = reviewConfig.title
  }
}

// Atualiza subtítulo
const updateSubtitle = () => {
  const reviewComponent = findCustomerReviewComponent()
  if (!reviewComponent) return
  
  // Procura por span que contém o texto de avaliações
  const subtitleElement = reviewComponent.querySelector('.overall-rating span')
  if (subtitleElement) {
    // Mantém o formato "X.X de 5.0 (X,XXX avaliações)" mas permite customização
    const currentText = subtitleElement.textContent
    const ratingMatch = currentText.match(/(\d+\.?\d*\s+de\s+\d+\.?\d*\s+\(\d+,?\d*\s+avaliações\))/)
    if (ratingMatch) {
      // Se tem texto personalizado, usa ele, senão mantém o rating
      subtitleElement.textContent = reviewConfig.subtitle !== 'Confira as avaliações reais dos nossos clientes' 
        ? reviewConfig.subtitle 
        : ratingMatch[0]
    } else {
      subtitleElement.textContent = reviewConfig.subtitle
    }
  }
}

// Atualiza layout
const updateLayout = () => {
  const reviewComponent = findCustomerReviewComponent()
  if (!reviewComponent) return
  
  const gridElement = reviewComponent.querySelector('.reviews-grid')
  if (gridElement) {
    if (reviewConfig.layout === 'grid') {
      gridElement.style.display = 'grid'
      gridElement.style.gridTemplateColumns = `repeat(${reviewConfig.columns}, 1fr)`
    } else {
      gridElement.style.display = 'flex'
      gridElement.style.gridTemplateColumns = 'none'
      gridElement.style.flexWrap = 'wrap'
    }
  }
}

// Atualiza visibilidade dos elementos
const updateElementVisibility = () => {
  const reviewComponent = findCustomerReviewComponent()
  if (!reviewComponent) return
  
  // Título
  const titleElement = reviewComponent.querySelector('h2')
  if (titleElement) {
    titleElement.style.display = reviewConfig.showTitle ? 'block' : 'none'
  }
  
  // Subtítulo/Rating
  const subtitleElement = reviewComponent.querySelector('.overall-rating')
  if (subtitleElement) {
    subtitleElement.style.display = reviewConfig.showSubtitle ? 'flex' : 'none'
  }
  
  // Estrelas individuais nos cards
  const starElements = reviewComponent.querySelectorAll('.stars')
  starElements.forEach(el => {
    el.style.display = reviewConfig.showStars ? 'flex' : 'none'
  })
  
  // Avatares
  const avatarElements = reviewComponent.querySelectorAll('.avatar')
  avatarElements.forEach(el => {
    el.style.display = reviewConfig.showAvatar ? 'flex' : 'none'
  })
}

// Re-inicializa o componente
const refreshComponent = () => {
  if (!props.element) return
  
  try {
    // Remove inicialização para forçar re-processamento
    props.element.removeAttribute('data-initialized')
    
    // Re-inicializa via sistema global
    if (window.reinitializeComponent) {
      window.reinitializeComponent(props.element)
    } else if (window.CustomerReviewComponent) {
      // Fallback para inicialização direta
      setTimeout(() => {
        window.CustomerReviewComponent.initializeReview(props.element)
      }, 50)
    }

  } catch (error) {
    // Silently handle error
  }
}

// Lifecycle
onMounted(() => {
  loadReviewConfig()
})
</script>

<style scoped>
.customer-review-sidebar-editor {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

/* Tabs */
.editor-tabs {
  display: flex;
  border-bottom: 1px solid var(--iluria-color-border);
  background: var(--iluria-color-container-bg);
  overflow-x: auto;
  flex-shrink: 0;
}

.tab-button {
  background: none;
  border: none;
  padding: 0.75rem 1rem;
  font-size: 0.75rem;
  font-weight: 500;
  color: var(--iluria-color-text-secondary);
  cursor: pointer;
  transition: all 0.2s ease;
  white-space: nowrap;
  text-transform: uppercase;
  letter-spacing: 0.05em;
  border-bottom: 2px solid transparent;
}

.tab-button:hover {
  color: var(--iluria-color-text-primary);
  background: var(--iluria-color-hover);
}

.tab-button.active {
  color: var(--iluria-color-primary);
  border-bottom-color: var(--iluria-color-primary);
  background: var(--iluria-color-primary-bg);
}

/* Tab Content */
.tab-content {
  flex: 1;
  overflow-y: auto;
}

.tab-panel {
  padding: 1rem;
}

.section {
  margin-bottom: 1.5rem;
}

.section-title {
  font-size: 0.875rem;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 1rem 0;
  padding-bottom: 0.5rem;
  border-bottom: 1px solid var(--iluria-color-border);
}

.form-group {
  margin-bottom: 1rem;
}

.color-input-wrapper {
  display: flex;
  gap: 0.5rem;
  align-items: center;
}

/* Responsive */
@media (max-width: 768px) {
  .tab-panel {
    padding: 0.75rem;
  }
}
</style> 
