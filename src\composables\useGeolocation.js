import { ref, computed } from 'vue'
import geolocationService from '@/services/geolocation.service'

/**
 * Composable para gerenciamento de geolocalização
 */
export function useGeolocation() {
    // Estado reativo
    const currentLocation = ref(null)
    const loading = ref(false)
    const error = ref(null)
    
    // Cache de localizações por IP
    const locationCache = ref(new Map())
    
    // Computed
    const hasCurrentLocation = computed(() => currentLocation.value !== null)
    
    const currentLocationDisplay = computed(() => {
        if (!currentLocation.value) return 'Localização desconhecida'
        return currentLocation.value.display || 'Localização desconhecida'
    })
    
    const cacheStats = computed(() => ({
        size: locationCache.value.size,
        entries: Array.from(locationCache.value.keys())
    }))
    
    /**
     * Obtém localização atual do usuário
     */
    async function getCurrentLocation() {
        if (loading.value) return currentLocation.value
        
        loading.value = true
        error.value = null
        
        try {
            const location = await geolocationService.getCurrentLocation()
            currentLocation.value = location
            return location
        } catch (err) {
            error.value = err.message || 'Erro ao obter localização atual'
            console.error('Erro ao obter localização atual:', err)
            return null
        } finally {
            loading.value = false
        }
    }
    
    /**
     * Obtém localização por IP com cache local
     * @param {string} ip - Endereço IP
     * @returns {Promise<Object|null>} Dados de localização
     */
    async function getLocationByIP(ip) {
        if (!ip) return null
        
        // Verifica cache local primeiro
        if (locationCache.value.has(ip)) {
            return locationCache.value.get(ip)
        }
        
        try {
            const location = await geolocationService.getLocationByIP(ip)
            
            // Salva no cache local
            locationCache.value.set(ip, location)
            
            return location
        } catch (err) {
            console.warn(`Erro ao obter localização para IP ${ip}:`, err.message)
            return null
        }
    }
    
    /**
     * Obtém localizações para múltiplos IPs em paralelo
     * @param {Array<string>} ips - Lista de endereços IP
     * @returns {Promise<Map>} Mapa de IP -> localização
     */
    async function getLocationsByIPs(ips) {
        const uniqueIPs = [...new Set(ips.filter(Boolean))]
        const results = new Map()
        
        // Busca localizações em paralelo
        const promises = uniqueIPs.map(async (ip) => {
            const location = await getLocationByIP(ip)
            if (location) {
                results.set(ip, location)
            }
        })
        
        await Promise.all(promises)
        return results
    }
    
    /**
     * Enriquece uma lista de objetos com dados de geolocalização
     * @param {Array} items - Lista de objetos que contêm IPs
     * @param {string|Function} ipExtractor - Propriedade ou função para extrair IP
     * @returns {Promise<Array>} Lista enriquecida com dados de localização
     */
    async function enrichWithGeolocation(items, ipExtractor = 'ip') {
        if (!Array.isArray(items) || items.length === 0) {
            return items
        }
        
        // Extrai IPs dos itens
        const ips = items.map(item => {
            if (typeof ipExtractor === 'function') {
                return ipExtractor(item)
            }
            return item[ipExtractor]
        }).filter(Boolean)
        
        // Obtém localizações
        const locations = await getLocationsByIPs(ips)
        
        // Enriquece os itens
        return items.map(item => {
            const ip = typeof ipExtractor === 'function' ? ipExtractor(item) : item[ipExtractor]
            const location = locations.get(ip)
            
            if (location) {
                return {
                    ...item,
                    locationData: location,
                    locationDisplay: location.display
                }
            }
            
            return item
        })
    }
    
    /**
     * Detecta se duas localizações são suspeitas (muito distantes)
     * @param {Object} location1 - Primeira localização
     * @param {Object} location2 - Segunda localização
     * @returns {boolean} Se as localizações são suspeitas
     */
    function areLocationsSuspicious(location1, location2) {
        if (!location1 || !location2) return false
        
        // Se não tem coordenadas, compara por país
        if (!location1.latitude || !location1.longitude || 
            !location2.latitude || !location2.longitude) {
            return location1.countryCode !== location2.countryCode
        }
        
        // Calcula distância aproximada
        const distance = calculateDistance(
            location1.latitude, location1.longitude,
            location2.latitude, location2.longitude
        )
        
        // Considera suspeito se a distância for maior que 500km
        return distance > 500
    }
    
    /**
     * Calcula distância entre duas coordenadas (fórmula de Haversine)
     * @param {number} lat1 
     * @param {number} lon1 
     * @param {number} lat2 
     * @param {number} lon2 
     * @returns {number} Distância em quilômetros
     */
    function calculateDistance(lat1, lon1, lat2, lon2) {
        const R = 6371 // Raio da Terra em km
        const dLat = (lat2 - lat1) * Math.PI / 180
        const dLon = (lon2 - lon1) * Math.PI / 180
        const a = 
            Math.sin(dLat/2) * Math.sin(dLat/2) +
            Math.cos(lat1 * Math.PI / 180) * Math.cos(lat2 * Math.PI / 180) * 
            Math.sin(dLon/2) * Math.sin(dLon/2)
        const c = 2 * Math.atan2(Math.sqrt(a), Math.sqrt(1-a))
        return R * c
    }
    
    /**
     * Limpa cache de localizações
     */
    function clearCache() {
        locationCache.value.clear()
        geolocationService.clearCache()
    }
    
    /**
     * Força atualização da localização atual
     */
    async function refreshCurrentLocation() {
        currentLocation.value = null
        return await getCurrentLocation()
    }
    
    /**
     * Valida se uma localização é válida
     * @param {Object} location 
     * @returns {boolean}
     */
    function isValidLocation(location) {
        return location && 
               location.country && 
               location.country !== 'Desconhecido' &&
               location.display && 
               location.display !== 'Localização desconhecida'
    }
    
    return {
        // Estado
        currentLocation,
        loading,
        error,
        locationCache,
        
        // Computed
        hasCurrentLocation,
        currentLocationDisplay,
        cacheStats,
        
        // Métodos
        getCurrentLocation,
        getLocationByIP,
        getLocationsByIPs,
        enrichWithGeolocation,
        areLocationsSuspicious,
        calculateDistance,
        clearCache,
        refreshCurrentLocation,
        isValidLocation
    }
}
