<template>
  <div class="iluria-select-wrapper">
    <IluriaLabel v-if="label" :for="selectId" class="iluria-select-label">
      {{ label }}
    </IluriaLabel>
    
    <div v-if="!filter" class="select-container">
      <select
        :id="selectId"
        :name="name || selectId"
        v-model="selectedValue"
        :disabled="disabled"
        :required="required"
        :aria-describedby="ariaDescribedBy"
        class="iluria-select"
        @change="handleNativeChange"
      >
        <option v-if="placeholder" value="" disabled>
          {{ placeholder }}
        </option>
        <option
          v-for="option in normalizedOptions"
          :key="getOptionKey(option)"
          :value="option.value"
          :disabled="option.disabled"
        >
          {{ option.label }}
        </option>
      </select>
      
      <div class="select-arrow" aria-hidden="true">
        <ChevronDownIcon />
      </div>
    </div>

    <div v-else class="filter-select-container">
      <div class="relative">
        <div 
          ref="filterSelectRef"
          :id="selectId"
          role="combobox"
          :aria-expanded="showDropdown"
          :aria-haspopup="true"
          :aria-describedby="ariaDescribedBy"
          class="iluria-select filter-select"
          :class="selectClasses"
          @click="toggleDropdown"
          @keydown="handleSelectKeydown"
        >
          <div class="selected-value">
            <slot name="value" :value="selectedValue" :placeholder="placeholder" :selected-option="selectedOption">
              <span v-if="selectedOption" class="selected-text">
                {{ selectedOption.label }}
              </span>
              <span v-else class="placeholder-text">
                {{ placeholder }}
              </span>
            </slot>
          </div>
          
          <div class="select-controls">
            <button
              v-if="clearable && selectedValue && !disabled"
              type="button"
              class="clear-button"
              :aria-label="$t?.('clear') || 'Limpar seleção'"
              @click.stop="clearSelection"
            >
              <XMarkIcon />
            </button>
            
            <div class="select-arrow" :class="{ 'rotate': showDropdown }" aria-hidden="true">
              <ChevronDownIcon />
            </div>
          </div>
        </div>

        <Teleport to="body">
          <div 
            v-if="showDropdown" 
            ref="dropdownRef"
            class="iluria-select-dropdown"
            :style="dropdownPosition"
            role="listbox"
            :aria-labelledby="selectId"
          >
            <div class="dropdown-filter">
              <div class="filter-input-wrapper">
                <MagnifyingGlassIcon class="filter-search-icon" aria-hidden="true" />
                <input
                  ref="filterInputRef"
                  v-model="filterQuery"
                  type="text"
                  class="filter-input"
                  :placeholder="filterPlaceholder || $t?.('search') || 'Buscar...'"
                  :aria-label="$t?.('search') || 'Buscar opções'"
                  autocomplete="off"
                  @input="handleFilterInput"
                  @keydown="handleFilterKeydown"
                />
              </div>
            </div>

            <div class="options-container" :class="{ 'loading': loading }">
              <div v-if="loading" class="loading-indicator" role="status" :aria-label="$t?.('loading') || 'Carregando...'">
                <div class="spinner" aria-hidden="true"></div>
                <span>{{ $t?.('loading') || 'Carregando...' }}</span>
              </div>
              
              <template v-else>
                <div
                  v-for="(option, index) in filteredOptions"
                  :key="getOptionKey(option)"
                  role="option"
                  :aria-selected="option.value === selectedValue"
                  class="option-item"
                  :class="getOptionClasses(option, index)"
                  @click="selectOption(option)"
                  @mouseenter="highlightedIndex = index"
                >
                  <slot name="option" :option="option" :index="index" :selected="option.value === selectedValue">
                    <span class="option-text">{{ option.label }}</span>
                  </slot>
                </div>

                <div v-if="filteredOptions.length === 0" class="empty-state" role="status">
                  <slot name="empty">
                    <span>{{ $t('product.noOptionsFound') || 'Nenhuma opção encontrada' }}</span>
                  </slot>
                </div>
              </template>
            </div>
          </div>
        </Teleport>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed, ref, nextTick, onMounted, onUnmounted, h } from 'vue'
import IluriaLabel from '../IluriaLabel.vue'

const ChevronDownIcon = () => h('svg', { viewBox: '0 0 20 20', fill: 'currentColor' }, [
  h('path', { 'fill-rule': 'evenodd', d: 'M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z', 'clip-rule': 'evenodd' })
])

const XMarkIcon = () => h('svg', { viewBox: '0 0 20 20', fill: 'currentColor' }, [
  h('path', { 'fill-rule': 'evenodd', d: 'M4.293 4.293a1 1 0 011.414 0L10 8.586l4.293-4.293a1 1 0 111.414 1.414L11.414 10l4.293 4.293a1 1 0 01-1.414 1.414L10 11.414l-4.293 4.293a1 1 0 01-1.414-1.414L8.586 10 4.293 5.707a1 1 0 010-1.414z', 'clip-rule': 'evenodd' })
])

const MagnifyingGlassIcon = () => h('svg', { viewBox: '0 0 20 20', fill: 'currentColor' }, [
  h('path', { 'fill-rule': 'evenodd', d: 'M8 4a4 4 0 100 8 4 4 0 000-8zM2 8a6 6 0 1110.89 3.476l4.817 4.817a1 1 0 01-1.414 1.414l-4.816-4.816A6 6 0 012 8z', 'clip-rule': 'evenodd' })
])

const props = defineProps({
  modelValue: {
    type: [String, Number, Boolean, null],
    default: null
  },
  label: {
    type: String,
    default: ''
  },
  placeholder: {
    type: String,
    default: ''
  },
  options: {
    type: Array,
    required: true,
    validator: (options) => Array.isArray(options)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  required: {
    type: Boolean,
    default: false
  },
  name: {
    type: String,
    default: ''
  },
  filter: {
    type: Boolean,
    default: false
  },
  optionLabel: {
    type: String,
    default: 'label'
  },
  optionValue: {
    type: String,
    default: 'value'
  },
  delay: {
    type: Number,
    default: 400,
    validator: (value) => value >= 0
  },  
  minLength: {
    type: Number,
    default: 1,
    validator: (value) => value >= 0
  },
  loading: {
    type: Boolean,
    default: false
  },
  clearable: {
    type: Boolean,
    default: false
  },
  filterPlaceholder: {
    type: String,
    default: ''
  },
  ariaDescribedBy: {
    type: String,
    default: ''
  },
  dropdownPosition: {
    type: String,
    default: 'auto', // 'auto', 'down', 'up'
    validator: (value) => ['auto', 'down', 'up'].includes(value)
  }
})

const emit = defineEmits({
  'update:modelValue': (value) => true,
  'change': (value) => true,
  'filter': (payload) => payload && typeof payload === 'object',
  'show': () => true,
  'hide': () => true,
  'clear': () => true
})

const filterSelectRef = ref(null)
const filterInputRef = ref(null)
const dropdownRef = ref(null)

const showDropdown = ref(false)
const filterQuery = ref('')
const highlightedIndex = ref(-1)
const dropdownPosition = ref({})

let searchTimeout = null

const DROPDOWN_Z_INDEX = 9999

// Altura máxima baseada no tamanho da tela
const getMaxDropdownHeight = () => {
  const isMobile = window.innerWidth <= 768
  return isMobile ? 240 : 320
}

const selectId = computed(() => `iluria-select-${Math.random().toString(36).substring(2, 11)}`)

const selectedValue = computed({
  get: () => props.modelValue,
  set: (value) => emit('update:modelValue', value)
})

const normalizedOptions = computed(() => {
  if (!Array.isArray(props.options)) return []
  
  return props.options.map((option, index) => {
    if (typeof option === 'string' || typeof option === 'number' || typeof option === 'boolean') {
      return {
        label: String(option),
        value: option,
        disabled: false,
        originalIndex: index
      }
    }
    
    if (option && typeof option === 'object') {
      const label = option[props.optionLabel] || option.label || String(option.value || '')
      const value = option[props.optionValue] !== undefined ? option[props.optionValue] : option.value
      
      return {
        label: String(label),
        value,
        disabled: Boolean(option.disabled),
        originalIndex: index,
        ...option
      }
    }
    
    return {
      label: String(option),
      value: option,
      disabled: false,
      originalIndex: index
    }
  })
})

const filteredOptions = computed(() => {
  if (!props.filter || !filterQuery.value || filterQuery.value.length < props.minLength) {
    return normalizedOptions.value
  }
  
  const query = filterQuery.value.toLowerCase().trim()
  return normalizedOptions.value.filter(option => 
    option.label.toLowerCase().includes(query)
  )
})

const selectedOption = computed(() => 
  normalizedOptions.value.find(option => option.value === selectedValue.value) || null
)

const selectClasses = computed(() => ({
  'open': showDropdown.value,
  'disabled': props.disabled,
  'has-value': selectedValue.value !== null && selectedValue.value !== ''
}))

const getOptionKey = (option) => {
  if (option.value !== null && option.value !== undefined) {
    return `${option.originalIndex}-${option.value}`
  }
  return `${option.originalIndex}-${option.label}`
}

const getOptionClasses = (option, index) => ({
  'selected': option.value === selectedValue.value,
  'highlighted': index === highlightedIndex.value,
  'disabled': option.disabled
})

const handleNativeChange = (event) => {
  const value = event.target.value
  emit('update:modelValue', value)
  emit('change', value)
}

const calculateDropdownPosition = () => {
  if (!filterSelectRef.value) return {}
  
  const rect = filterSelectRef.value.getBoundingClientRect()
  const scrollY = window.scrollY || document.documentElement.scrollTop
  const scrollX = window.scrollX || document.documentElement.scrollLeft
  
  const windowHeight = window.innerHeight
  const spaceBelow = windowHeight - rect.bottom
  const spaceAbove = rect.top
  
  let top = rect.bottom + scrollY + 4
  let left = rect.left + scrollX
  const width = rect.width
  
  const DROPDOWN_MAX_HEIGHT = getMaxDropdownHeight()
  
  // Lógica de posicionamento baseada na propriedade dropdownPosition
  if (props.dropdownPosition === 'up') {
    // Força abertura para cima
    top = rect.top + scrollY - DROPDOWN_MAX_HEIGHT - 4
  } else if (props.dropdownPosition === 'down') {
    // Força abertura para baixo, mas ajusta se não há espaço suficiente
    const maxDropdownHeight = Math.min(DROPDOWN_MAX_HEIGHT, spaceBelow - 16)
    if (maxDropdownHeight < 100 && spaceAbove > 200) {
      // Se realmente não há espaço, abre para cima
      top = rect.top + scrollY - DROPDOWN_MAX_HEIGHT - 4
    }
    // Se há pelo menos 100px abaixo, mantém para baixo (posição padrão já definida)
  } else {
    // Auto: só abre para cima se não há espaço mínimo abaixo E há mais espaço acima
    const minSpaceNeeded = Math.min(200, DROPDOWN_MAX_HEIGHT * 0.6)
    if (spaceBelow < minSpaceNeeded && spaceAbove > spaceBelow && spaceAbove > minSpaceNeeded) {
      top = rect.top + scrollY - Math.min(DROPDOWN_MAX_HEIGHT, spaceAbove - 8) - 4
    }
  }
  
  const maxLeft = window.innerWidth - width - 16
  if (left > maxLeft) left = maxLeft
  if (left < 16) left = 16
  
  // Calcula altura máxima baseada no posicionamento
  let maxHeight = DROPDOWN_MAX_HEIGHT
  if (props.dropdownPosition === 'down' || (props.dropdownPosition === 'auto' && top === rect.bottom + scrollY + 4)) {
    // Se abrindo para baixo, limita pela altura disponível
    maxHeight = Math.min(DROPDOWN_MAX_HEIGHT, spaceBelow - 16)
  } else if (top < rect.bottom + scrollY) {
    // Se abrindo para cima, limita pela altura disponível acima
    maxHeight = Math.min(DROPDOWN_MAX_HEIGHT, spaceAbove - 16)
  }

  return {
    position: 'absolute',
    top: `${top}px`,
    left: `${left}px`,
    width: `${width}px`,
    maxHeight: `${maxHeight}px`,
    zIndex: DROPDOWN_Z_INDEX
  }
}

const updateDropdownPosition = () => {
  dropdownPosition.value = calculateDropdownPosition()
}

const openDropdown = async () => {
  if (props.disabled) return
  
  showDropdown.value = true
  highlightedIndex.value = -1
  filterQuery.value = ''
  
  await nextTick()
  updateDropdownPosition()
  
  if (props.filter && filterInputRef.value) {
    filterInputRef.value.focus({ preventScroll: true })
  }
  
  emit('show')
}

const closeDropdown = () => {
  if (!showDropdown.value) return
  
  showDropdown.value = false
  filterQuery.value = ''
  highlightedIndex.value = -1
  dropdownPosition.value = {}
  
  clearTimeout(searchTimeout)
  emit('hide')
}

const toggleDropdown = () => {
  if (showDropdown.value) {
    closeDropdown()
  } else {
    openDropdown()
  }
}

const handleFilterInput = () => {
  clearTimeout(searchTimeout)
  searchTimeout = setTimeout(() => {
    emit('filter', { filter: filterQuery.value })
  }, props.delay)
}

const selectOption = (option) => {
  if (option.disabled) return
  
  selectedValue.value = option.value
  emit('change', option.value)
  closeDropdown()
}

const clearSelection = () => {
  selectedValue.value = null
  emit('update:modelValue', null)
  emit('change', null)
  emit('clear')
}

const navigateDown = () => {
  const maxIndex = filteredOptions.value.length - 1
  if (highlightedIndex.value < maxIndex) {
    highlightedIndex.value++
  }
}

const navigateUp = () => {
  if (highlightedIndex.value > 0) {
    highlightedIndex.value--
  }
}

const selectHighlighted = () => {
  const option = filteredOptions.value[highlightedIndex.value]
  if (option && !option.disabled) {
    selectOption(option)
  }
}

const selectFirst = () => {
  const firstOption = filteredOptions.value.find(option => !option.disabled)
  if (firstOption) {
    selectOption(firstOption)
  }
}

const handleSelectKeydown = (event) => {
  if (props.disabled) return
  
  switch (event.key) {
    case 'Enter':
    case ' ':
      event.preventDefault()
      if (!showDropdown.value) {
        openDropdown()
      }
      break
    case 'Escape':
      if (showDropdown.value) {
        event.preventDefault()
        closeDropdown()
      }
      break
    case 'ArrowDown':
      event.preventDefault()
      if (!showDropdown.value) {
        openDropdown()
      } else {
        navigateDown()
      }
      break
    case 'ArrowUp':
      event.preventDefault()
      if (!showDropdown.value) {
        openDropdown()
      } else {
        navigateUp()
      }
      break
  }
}

const handleFilterKeydown = (event) => {
  switch (event.key) {
    case 'Enter':
      event.preventDefault()
      if (highlightedIndex.value >= 0) {
        selectHighlighted()
      } else {
        selectFirst()
      }
      break
    case 'Escape':
      event.preventDefault()
      closeDropdown()
      break
    case 'ArrowDown':
      event.preventDefault()
      navigateDown()
      break
    case 'ArrowUp':
      event.preventDefault()
      navigateUp()
      break
    case 'Tab':
      closeDropdown()
      break
  }
}

const handleOutsideClick = (event) => {
  if (!dropdownRef.value || !filterSelectRef.value) return
  
  const selectContainer = filterSelectRef.value.closest('.filter-select-container')
  const isClickInside = selectContainer?.contains(event.target) || dropdownRef.value.contains(event.target)
  
  if (!isClickInside) {
    closeDropdown()
  }
}

const handleScroll = () => {
  if (showDropdown.value) {
    closeDropdown()
  }
}

const handleResize = () => {
  if (showDropdown.value) {
    updateDropdownPosition()
  }
}

onMounted(() => {
  if (props.filter) {
    document.addEventListener('click', handleOutsideClick, true)
    window.addEventListener('scroll', handleScroll, true)
    window.addEventListener('resize', handleResize)
  }
})

onUnmounted(() => {
  if (props.filter) {
    document.removeEventListener('click', handleOutsideClick, true)
    window.removeEventListener('scroll', handleScroll, true)
    window.removeEventListener('resize', handleResize)
  }
  clearTimeout(searchTimeout)
})

defineExpose({
  open: openDropdown,
  close: closeDropdown,
  toggle: toggleDropdown,
  clear: clearSelection,
  focus: () => {
    if (props.filter && filterSelectRef.value) {
      filterSelectRef.value.focus()
    }
  }
})
</script>

<style scoped>
.iluria-select-wrapper {
  position: relative;
  width: 100%;
}

.iluria-select-label {
  display: block;
  margin-bottom: 0.5rem;
}

.select-container {
  position: relative;
  width: 100%;
}

.iluria-select {
  width: 100%;
  height: 38px;
  padding: 0.5rem 2.5rem 0.5rem 0.75rem;
  background-color: var(--iluria-color-input-bg, #ffffff);
  border: 2px solid var(--iluria-color-input-border, #e2e8f0);
  border-radius: 8px;
  color: var(--iluria-color-input-text, #374151);
  font-size: 0.875rem;
  line-height: 1.25rem;
  appearance: none;
  cursor: pointer;
  transition: all 0.2s ease;
}

.iluria-select:hover:not(:disabled) {
  border-color: var(--iluria-color-input-border-hover, #d1d5db);
}

.iluria-select:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus, #3b82f6);
  box-shadow: 0 0 0 3px var(--iluria-color-input-shadow-focus, rgba(59, 130, 246, 0.1));
}

.iluria-select:disabled {
  background-color: var(--iluria-color-input-bg-disabled, #f9fafb);
  color: var(--iluria-color-text-muted, #9ca3af);
  cursor: not-allowed;
  opacity: 0.6;
}

.filter-select-container {
  position: relative;
  width: 100%;
}

.filter-select {
  display: flex;
  align-items: center;
  width: 100%;
  min-height: 38px;
  padding: 0.5rem 0.75rem;
  background-color: var(--iluria-color-input-bg, #ffffff);
  border: 2px solid var(--iluria-color-input-border, #e2e8f0);
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.2s ease;
  outline: none;
}

.filter-select:hover:not(.disabled) {
  border-color: var(--iluria-color-input-border-hover, #d1d5db);
}

.filter-select:focus-visible,
.filter-select.open {
  border-color: var(--iluria-color-input-border-focus, #3b82f6);
  box-shadow: 0 0 0 3px var(--iluria-color-input-shadow-focus, rgba(59, 130, 246, 0.1));
}

.filter-select.disabled {
  background-color: var(--iluria-color-input-bg-disabled, #f9fafb);
  cursor: not-allowed;
  opacity: 0.6;
}

.selected-value {
  flex: 1;
  overflow: hidden;
  text-overflow: ellipsis;
  white-space: nowrap;
  font-size: 0.875rem;
}

.selected-text {
  color: var(--iluria-color-input-text, #374151);
}

.placeholder-text {
  color: var(--iluria-color-text-muted, #9ca3af);
}

.select-controls {
  display: flex;
  align-items: center;
  gap: 0.5rem;
  margin-left: 0.5rem;
}

.select-container .select-arrow {
  position: absolute;
  top: 50%;
  right: 0.75rem;
  transform: translateY(-50%);
  pointer-events: none;
}

.clear-button {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1rem;
  height: 1rem;
  padding: 0;
  background: none;
  border: none;
  color: var(--iluria-color-text-secondary, #6b7280);
  cursor: pointer;
  transition: color 0.2s ease;
}

.clear-button:hover {
  color: var(--iluria-color-hover-text, #1e293b);
}

.select-arrow {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 1.25rem;
  height: 1.25rem;
  color: var(--iluria-color-text-secondary, #6b7280);
  transition: transform 0.2s ease;
}

.select-arrow.rotate {
  transform: rotate(180deg);
}

.iluria-select-dropdown {
  background: var(--iluria-color-input-bg, #ffffff);
  border: 1px solid var(--iluria-color-input-border, #e2e8f0);
  border-radius: 8px;
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
  overflow: hidden;
  display: flex;
  flex-direction: column;
  min-width: 200px;
  /* max-height definido dinamicamente via style inline */
}

.dropdown-filter {
  padding: 8px;
  border-bottom: 1px solid var(--iluria-color-input-border, #e2e8f0);
  background: var(--iluria-color-input-bg, #ffffff);
}

.filter-input-wrapper {
  position: relative;
  display: flex;
  align-items: center;
}

.filter-search-icon {
  position: absolute;
  left: 8px;
  width: 16px;
  height: 16px;
  color: var(--iluria-color-text-muted, #9ca3af);
  pointer-events: none;
  z-index: 1;
}

.filter-input {
  width: 100%;
  padding: 8px 12px 8px 36px;
  border: 1px solid var(--iluria-color-input-border, #e2e8f0);
  border-radius: 6px;
  font-size: 14px;
  background: var(--iluria-color-input-bg, #ffffff);
  color: var(--iluria-color-input-text, #374151);
  transition: all 0.2s ease;
}

.filter-input::placeholder {
  color: var(--iluria-color-text-muted, #9ca3af);
}

.filter-input:focus {
  outline: none;
  border-color: var(--iluria-color-input-border-focus, #3b82f6);
  box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.1);
}

.options-container {
  flex: 1;
  overflow-y: auto;
  padding: 4px;
  background: var(--iluria-color-input-bg, #ffffff);
  /* altura ajustada automaticamente pelo flex: 1 */
}

.options-container.loading {
  display: flex;
  align-items: center;
  justify-content: center;
  min-height: 60px;
}

.loading-indicator {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  color: var(--iluria-color-text-secondary, #6b7280);
  font-size: 14px;
  min-height: 40px;
}

.spinner {
  width: 16px;
  height: 16px;
  border: 2px solid var(--iluria-color-input-border, #e2e8f0);
  border-top: 2px solid var(--iluria-color-input-border-focus, #3b82f6);
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.option-item {
  display: flex;
  align-items: center;
  padding: 10px 12px;
  margin: 2px 0;
  border-radius: 6px;
  cursor: pointer;
  transition: all 0.15s ease;
  border: 1px solid transparent;
  font-size: 14px;
  color: var(--iluria-color-input-text, #374151);
  min-height: 40px;
  line-height: 1.25;
}

.option-item:hover:not(.disabled),
.option-item.highlighted:not(.disabled) {
  background: var(--iluria-color-hover-bg, #f8fafc);
  color: var(--iluria-color-hover-text, #1e293b);
}

.option-item.selected {
  background: var(--iluria-color-input-shadow-focus, rgba(59, 130, 246, 0.1));
  color: var(--iluria-color-input-border-focus, #3b82f6);
  font-weight: 500;
}

.option-item.disabled {
  color: var(--iluria-color-text-muted, #9ca3af);
  cursor: not-allowed;
  opacity: 0.6;
}

.option-text {
  flex: 1;
  text-overflow: ellipsis;
  overflow: hidden;
  white-space: nowrap;
}

.empty-state {
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 16px 12px;
  text-align: center;
  color: var(--iluria-color-text-muted, #6b7280);
  font-style: italic;
  background: transparent;
  margin: 4px 0;
  border-radius: 6px;
  font-size: 14px;
  min-height: 40px;
  transition: all 0.15s ease;
}

.empty-state:hover {
  background: var(--iluria-color-hover-bg);
  color: var(--iluria-color-hover-text, #1e293b);
}

.options-container::-webkit-scrollbar {
  width: 6px;
}

.options-container::-webkit-scrollbar-track {
  background: var(--iluria-color-input-bg-disabled, #f1f5f9);
  border-radius: 10px;
}

.options-container::-webkit-scrollbar-thumb {
  background: var(--iluria-color-text-muted, #cbd5e1);
  border-radius: 10px;
}

.options-container::-webkit-scrollbar-thumb:hover {
  background: var(--iluria-color-border-hover, #94a3b8);
}

.iluria-select:focus + .select-arrow {
  color: var(--iluria-color-input-border-focus, #3b82f6);
}

@media (max-width: 768px) {
  /* max-height será definido dinamicamente para mobile também */
  .iluria-select-dropdown {
    min-width: 150px;
  }
}

@media (prefers-contrast: high) {
  .iluria-select,
  .filter-select {
    border-width: 3px;
  }
  
  .option-item.selected {
    border: 2px solid var(--iluria-color-input-border-focus, #3b82f6);
  }
}

@media (prefers-reduced-motion: reduce) {
  .iluria-select,
  .filter-select,
  .select-arrow,
  .option-item,
  .clear-button,
  .filter-input {
    transition: none;
  }
  
  .spinner {
    animation: none;
  }
}
</style>
