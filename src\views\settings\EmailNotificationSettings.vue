<template>
  <div class="email-notifications-container">
    <!-- Header Section -->
    <IluriaHeader
      :title="t('emailNotifications.title')"
      :subtitle="t('emailNotifications.subtitle')"
      :showSave="true"
      :saveText="t('emailNotifications.buttons.save')"
      @save-click="saveSettings"
    />

    <!-- Main Content -->
    <div class="form-content">
      <!-- Configurações de Notificações -->
      <ViewContainer 
        :title="t('emailNotifications.description')"
        :subtitle="t('emailNotifications.selectDescription')"
        :icon="Notification02Icon"
        iconColor="blue"
      >
        <div class="notifications-grid">
          <!-- Novas vendas -->
          <div class="notification-item">
            <div class="notification-info">
              <div class="notification-icon-wrapper">
                <HugeiconsIcon :icon="ShoppingCart01Icon" size="20" class="icon-color-green" />
              </div>
              <div class="notification-content">
                <h3 class="notification-title">{{ t('emailNotifications.notifications.newSales.title') }}</h3>
                <p class="notification-description">{{ t('emailNotifications.notifications.newSales.description') }}</p>
              </div>
            </div>
            <IluriaToggleSwitch
              v-model="settings.newSales"
              :id="'toggle-new-sales'"
              labelPosition="horizontal"
            />
          </div>

          <!-- Novas avaliações de produtos -->
          <div class="notification-item">
            <div class="notification-info">
              <div class="notification-icon-wrapper">
                <HugeiconsIcon :icon="StarIcon" size="20" class="icon-color-yellow" />
              </div>
              <div class="notification-content">
                <h3 class="notification-title">{{ t('emailNotifications.notifications.productReviews.title') }}</h3>
                <p class="notification-description">{{ t('emailNotifications.notifications.productReviews.description') }}</p>
              </div>
            </div>
            <IluriaToggleSwitch
              v-model="settings.productReviews"
              :id="'toggle-product-reviews'"
              labelPosition="horizontal"
            />
          </div>

          <!-- Novas perguntas em produtos -->
          <div class="notification-item">
            <div class="notification-info">
              <div class="notification-icon-wrapper">
                <HugeiconsIcon :icon="HelpCircleIcon" size="20" class="icon-color-purple" />
              </div>
              <div class="notification-content">
                <h3 class="notification-title">{{ t('emailNotifications.notifications.productQuestions.title') }}</h3>
                <p class="notification-description">{{ t('emailNotifications.notifications.productQuestions.description') }}</p>
              </div>
            </div>
            <IluriaToggleSwitch
              v-model="settings.productQuestions"
              :id="'toggle-product-questions'"
              labelPosition="horizontal"
            />
          </div>

          <!-- Inscrição na newsletter -->
          <div class="notification-item">
            <div class="notification-info">
              <div class="notification-icon-wrapper">
                <HugeiconsIcon :icon="MailAtSign01Icon" size="20" class="icon-color-blue" />
              </div>
              <div class="notification-content">
                <h3 class="notification-title">{{ t('emailNotifications.notifications.newsletterSubscriptions.title') }}</h3>
                <p class="notification-description">{{ t('emailNotifications.notifications.newsletterSubscriptions.description') }}</p>
              </div>
            </div>
            <IluriaToggleSwitch
              v-model="settings.newsletterSubscriptions"
              :id="'toggle-newsletter'"
              labelPosition="horizontal"
            />
          </div>

          <!-- Novo cliente cadastrado na loja -->
          <div class="notification-item">
            <div class="notification-info">
              <div class="notification-icon-wrapper">
                <HugeiconsIcon :icon="UserAdd01Icon" size="20" class="icon-color-indigo" />
              </div>
              <div class="notification-content">
                <h3 class="notification-title">{{ t('emailNotifications.notifications.newCustomerRegistrations.title') }}</h3>
                <p class="notification-description">{{ t('emailNotifications.notifications.newCustomerRegistrations.description') }}</p>
              </div>
            </div>
            <IluriaToggleSwitch
              v-model="settings.newCustomerRegistrations"
              :id="'toggle-new-customers'"
              labelPosition="horizontal"
            />
          </div>
        </div>
      </ViewContainer>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, computed } from 'vue'
import { useI18n } from 'vue-i18n'
import { useToast } from '@/services/toast.service'
import { HugeiconsIcon } from '@hugeicons/vue'
import ViewContainer from '@/components/layout/ViewContainer.vue'
import IluriaButton from '@/components/iluria/IluriaButton.vue'
import IluriaHeader from '@/components/iluria/IluriaHeader.vue'
import IluriaToggleSwitch from '@/components/iluria/IluriaToggleSwitch.vue'
import {
  FloppyDiskIcon,
  Notification02Icon,
  ShoppingCart01Icon,
  StarIcon,
  HelpCircleIcon,
  MailAtSign01Icon,
  UserAdd01Icon
} from '@hugeicons-pro/core-stroke-rounded'

const { t } = useI18n()
const { showSuccess, showError, showWarning } = useToast()

const settings = ref({
  newSales: true,
  productReviews: false,
  productQuestions: true,
  newsletterSubscriptions: false,
  newCustomerRegistrations: true
})

const isSaving = ref(false)

const defaultSettings = {
  newSales: true,
  productReviews: false,
  productQuestions: true,
  newsletterSubscriptions: false,
  newCustomerRegistrations: true
}

const loadSettings = async () => {
  try {
    const notificationService = (await import('@/services/notification.service')).default
    const savedSettings = await notificationService.getEmailSettings()
    settings.value = { ...defaultSettings, ...savedSettings }
  } catch (error) {
    console.error(t('emailNotifications.messages.loadError'), error)
    
    try {
      const localSettings = localStorage.getItem('emailNotificationSettings')
      if (localSettings) {
        settings.value = { ...defaultSettings, ...JSON.parse(localSettings) }
        showWarning('Configurações carregadas do armazenamento local. Conecte-se à internet para sincronizar.')
      } else {
        settings.value = { ...defaultSettings }
        showError('Erro ao carregar configurações. Usando valores padrão.')
      }
    } catch (localError) {
      settings.value = { ...defaultSettings }
      showError('Erro ao carregar configurações. Usando valores padrão.')
    }
  }
}

const saveSettings = async () => {
  isSaving.value = true
  try {
    const notificationService = (await import('@/services/notification.service')).default
    await notificationService.saveEmailSettings(settings.value)
    localStorage.setItem('emailNotificationSettings', JSON.stringify(settings.value))
    
    showSuccess(t('emailNotifications.messages.saveSuccess'))
  } catch (error) {
    console.error(t('emailNotifications.messages.saveError'), error)
    
    if (error.code === 'NETWORK_ERROR' || error.response?.status >= 500) {
      localStorage.setItem('emailNotificationSettings', JSON.stringify(settings.value))
      showWarning(t('emailNotifications.messages.saveLocal'))
    } else {
      showError(t('emailNotifications.messages.saveError'))
    }
  } finally {
    isSaving.value = false
  }
}

onMounted(() => {
  loadSettings()
})
</script>

<style scoped>
.email-notifications-container {
  max-width: 1400px;
  margin: 0 auto;
  padding: 24px;
  background: var(--iluria-color-background);
  min-height: 100vh;
}

/* Header */
.form-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 24px;
  padding-bottom: 20px;
  border-bottom: 1px solid var(--iluria-color-border);
}

.header-content h1.page-title {
  font-size: 28px;
  font-weight: 700;
  color: var(--iluria-color-text);
  margin: 0;
  line-height: 1.2;
}

.header-content p.page-subtitle {
  font-size: 16px;
  color: var(--iluria-color-text-muted);
  margin: 4px 0 0 0;
  line-height: 1.5;
}

.header-actions {
  display: flex;
  gap: 12px;
  align-items: center;
}

/* Content */
.form-content {
  display: flex;
  flex-direction: column;
  gap: 24px;
}

/* Notifications Grid */
.notifications-grid {
  display: flex;
  flex-direction: column;
  gap: 20px;
}

.notification-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 20px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.notification-item:hover {
  background: var(--iluria-color-hover);
  border-color: var(--iluria-color-border-hover);
  transform: translateY(-1px);
  box-shadow: var(--iluria-shadow-md);
}

.notification-info {
  display: flex;
  align-items: center;
  gap: 16px;
  flex: 1;
}

.notification-icon-wrapper {
  display: flex;
  align-items: center;
  justify-content: center;
  width: 48px;
  height: 48px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
  border-radius: 12px;
  transition: all 0.2s ease;
}

.notification-item:hover .notification-icon-wrapper {
  background: var(--iluria-color-container-bg);
  border-color: var(--iluria-color-border-hover);
}

.notification-content {
  flex: 1;
}

.notification-title {
  font-size: 16px;
  font-weight: 600;
  color: var(--iluria-color-text-primary);
  margin: 0 0 4px 0;
  line-height: 1.4;
}

.notification-description {
  font-size: 14px;
  color: var(--iluria-color-text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* Icon colors */
.icon-color-green { color: #059669; }
.icon-color-yellow { color: #d97706; }
.icon-color-purple { color: #7c3aed; }
.icon-color-blue { color: var(--iluria-color-primary, #3b82f6); }
.icon-color-indigo { color: #4f46e5; }

/* Responsive */
@media (max-width: 768px) {
  .form-header {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
  }
  
  .header-actions {
    justify-content: flex-end;
  }
  
  .notification-item {
    flex-direction: column;
    gap: 16px;
    align-items: stretch;
    text-align: center;
  }
  
  .notification-info {
    justify-content: center;
  }
}
</style> 
