import { ref, onUnmounted } from 'vue'

// Estado global compartilhado
let mobileMenuOpen = null
let cleanupFunctions = []

/**
 * Composable para gerenciar o estado do menu mobile do usuário
 * Compartilha estado entre UserNavBar e UserSidebar
 */
export function useMobileUserMenu() {
  // Inicializar estado global apenas uma vez
  if (mobileMenuOpen === null) {
    mobileMenuOpen = ref(false)
  }

  /**
   * Alternar estado do menu mobile
   */
  const toggleMobileMenu = () => {
    mobileMenuOpen.value = !mobileMenuOpen.value
    
    if (mobileMenuOpen.value) {
      // Bloquear scroll do body quando menu aberto
      document.body.style.overflow = 'hidden'
    } else {
      // Restaurar scroll do body quando menu fechado
      document.body.style.overflow = ''
    }
  }

  /**
   * Fechar menu mobile
   */
  const closeMobileMenu = () => {
    mobileMenuOpen.value = false
    document.body.style.overflow = ''
  }

  /**
   * Abrir menu mobile
   */
  const openMobileMenu = () => {
    mobileMenuOpen.value = true
    document.body.style.overflow = 'hidden'
  }

  /**
   * Função de limpeza para restaurar estado do body
   */
  const cleanup = () => {
    document.body.style.overflow = ''
  }

  // Registrar função de limpeza
  cleanupFunctions.push(cleanup)

  // Cleanup automático quando componente for desmontado
  onUnmounted(() => {
    cleanup()
    // Remover da lista de cleanups
    const index = cleanupFunctions.indexOf(cleanup)
    if (index > -1) {
      cleanupFunctions.splice(index, 1)
    }
  })

  return {
    mobileMenuOpen,
    toggleMobileMenu,
    closeMobileMenu,
    openMobileMenu
  }
}

/**
 * Função para cleanup global (usado em casos especiais)
 */
export function cleanupMobileUserMenu() {
  cleanupFunctions.forEach(cleanup => cleanup())
  cleanupFunctions = []
  document.body.style.overflow = ''
}