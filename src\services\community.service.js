import axios from 'axios';
import { API_URLS } from '../config/api.config';

class CommunityService {
  #baseUrl = `${API_URLS.COMMUNITY_BASE_URL}`;

  constructor() {}

  // Group methods
  async createGroup(group) {
    try {
      const response = await axios.post(`${this.#baseUrl}/groups`, group);
      return response.data;
    } catch (error) {
      console.error('CreateGroup error:', error);
      throw error;
    }
  }

  async updateGroup(id, group) {
    try {
      const response = await axios.put(`${this.#baseUrl}/groups/${id}`, group);
      return response.data;
    } catch (error) {
      console.error('UpdateGroup error:', error);
      throw error;
    }
  }

  async deleteGroup(id) {
    try {
      if (!id) {
        throw new Error('Group ID is required');
      }
      const response = await axios.delete(`${this.#baseUrl}/groups/${id}`);
      return response.data;
    } catch (error) {
      console.error('DeleteGroup error:', error);
      throw error;
    }
  }

  async listGroups(params = {}) {
    try {
      // Se tiver busca ou outros filtros, usa o endpoint de search
      if (params.search || params.sort) {
        const queryParams = new URLSearchParams();
        if (params.categoryId) queryParams.append('categoryId', params.categoryId);
        if (params.search) queryParams.append('search', params.search);
        if (params.page !== undefined) queryParams.append('page', params.page);
        if (params.size !== undefined) queryParams.append('size', params.size);

        const response = await axios.get(`${this.#baseUrl}/groups/search?${queryParams.toString()}`);
        return response.data;
      }
      
      // Se tiver apenas categoryId, usa o endpoint principal com query param
      if (params.categoryId) {
        const response = await axios.get(`${this.#baseUrl}/groups?categoryId=${params.categoryId}`);
        return response.data;
      }
      
      // Caso contrário, lista todos os grupos
      const response = await axios.get(`${this.#baseUrl}/groups`);
      return response.data;
    } catch (error) {
      console.error('ListGroups error:', error);
      throw error;
    }
  }

  async getGroup(id) {
    try {
      const response = await axios.get(`${this.#baseUrl}/groups/${id}`);
      return response.data;
    } catch (error) {
      console.error('GetGroup error:', error);
      throw error;
    }
  }

  // Category methods
  async createCategory(category) {
    try {
      const response = await axios.post(`${this.#baseUrl}/categories`, category);
      return response.data;
    } catch (error) {
      console.error('CreateCategory error:', error);
      throw error;
    }
  }

  async updateCategory(id, category) {
    try {
      const response = await axios.put(`${this.#baseUrl}/categories/${id}`, category);
      return response.data;
    } catch (error) {
      console.error('UpdateCategory error:', error);
      throw error;
    }
  }

  async deleteCategory(id) {
    try {
      if (!id) {
        throw new Error('Category ID is required');
      }
      const response = await axios.delete(`${this.#baseUrl}/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('DeleteCategory error:', error);
      throw error;
    }
  }

  async listCategories(paginated = false, params = {}) {
    try {
      if (paginated) {
        const queryParams = new URLSearchParams();
        if (params.page !== undefined) queryParams.append('page', params.page);
        if (params.size !== undefined) queryParams.append('size', params.size);
        
        const response = await axios.get(`${this.#baseUrl}/categories/paginated?${queryParams.toString()}`);
        return response.data;
      }
      
      const response = await axios.get(`${this.#baseUrl}/categories`);
      return response.data;
    } catch (error) {
      console.error('ListCategories error:', error);
      throw error;
    }
  }

  async getCategory(id) {
    try {
      const response = await axios.get(`${this.#baseUrl}/categories/${id}`);
      return response.data;
    } catch (error) {
      console.error('GetCategory error:', error);
      throw error;
    }
  }

  async uploadGroupImage(groupId, file) {
    const formData = new FormData();
    formData.append('file', file);
    const { data } = await axios.post(`${this.#baseUrl}/groups/${groupId}/image`, formData, {
      headers: { 'Content-Type': 'multipart/form-data' }
    });
    return data;
  }

  async reorderCategories(categoryIds) {
    try {
      const response = await axios.put(`${this.#baseUrl}/categories/reorder`, categoryIds);
      return response.data;
    } catch (error) {
      console.error('ReorderCategories error:', error);
      throw error;
    }
  }
}

export default new CommunityService(); 