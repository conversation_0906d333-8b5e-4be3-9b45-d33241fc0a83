<template>
  <div class="mb-6">
    <div class="grid grid-cols-1 md:grid-cols-2 gap-4">
      <!-- Discount Type -->
      <div>
        <IluriaSelect
          v-model="modelValue.discountType"
          :options="discountTypes"
          :label="t('promotions.editor.discountType')"
          :placeholder="t('promotions.editor.selectDiscountType')"
          class="w-full"
          @update:modelValue="emitUpdate"
        />
      </div>

      <!-- Discount Value -->
      <div v-if="showDiscountValueInput">
        <IluriaInputText
          id="discountValue"
          v-model.number="modelValue.discountValue"
          :label="t('promotions.editor.discountValue')"
          :placeholder="t('promotions.editor.discountValuePlaceholder')"
          :type="isPercentageType ? 'number' : 'money'"
          :prefix="isPercentageType ? '' : 'R$'"
          :suffix="isPercentageType ? '%' : ''"
          class="w-full"
          @update:modelValue="emitUpdate"
        />
      </div>
      <div v-else>
        <p class="text-gray-500 italic mt-2">{{ t('promotions.editor.discountTypeDescription') }}</p>
      </div>
    </div>
  </div>
</template>

<script setup>
import { computed } from 'vue';
import { useI18n } from 'vue-i18n';
import IluriaSelect from '@/components/iluria/form/IluriaSelect.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const discountTypes = computed(() => [
  { label: t('promotions.editor.percentage'), value: 'PERCENTAGE_TOTAL' },
  { label: t('promotions.editor.fixed'), value: 'FIXED_TOTAL' }
]);

const isPercentageType = computed(() => {
  return [
    'PERCENTAGE_TOTAL'
  ].includes(props.modelValue.discountType);
});

const showDiscountValueInput = computed(() => {
  return [
    'PERCENTAGE_TOTAL',
    'FIXED_TOTAL'
  ].includes(props.modelValue.discountType);
});

const emitUpdate = () => {
  emit('update:modelValue', props.modelValue);
};
</script>
