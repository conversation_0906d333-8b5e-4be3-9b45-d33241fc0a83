{"customersTitle": "Clientes", "customersSubtitle": "Gerencie sua base de clientes e suas informações", "customersListTitle": "Lista de Clientes", "customersListSubtitle": "Visualize e gerencie todos os clientes cadastrados", "new": "Novo Cliente", "newCustomer": "Novo Cliente", "searchPlaceholder": "Buscar clientes...", "loadingCustomers": "Carregando clientes...", "noCustomers": "Nenhum cliente encontrado", "noCustomersDescription": "Você ainda não possui clientes cadastrados. Adicione o primeiro cliente para começar.", "createFirstCustomer": "<PERSON><PERSON><PERSON>", "imageHeader": "Imagem", "nameHeader": "Nome", "emailHeader": "E-mail", "phoneHeader": "<PERSON><PERSON><PERSON>", "createdAt": "C<PERSON><PERSON> em", "expiresAt": "Expira em", "actionsHeader": "Ações", "noPhone": "Sem celular", "editCustomer": "Editar cliente", "deleteCustomer": "Excluir cliente", "viewCustomer": "Visualizar cliente", "confirmDeleteTitle": "Confirmar exclusão", "confirmDeleteMessage": "Tem certeza que deseja excluir o cliente {name}? Esta ação não pode ser desfeita.", "deleteSuccess": "Cliente excluído com sucesso", "deleteError": "Erro ao excluir cliente", "loadError": "Erro ao carregar clientes", "createTitle": "Cadastrar Novo Cliente", "createSubtitle": "Adicione um novo cliente ao sistema", "editTitle": "<PERSON><PERSON>", "editSubtitle": "Atualize as informações do cliente", "basicDataTitle": "Dados do Cliente", "basicDataSubtitle": "Informações principais do cliente", "addressTitle": "Endereços", "addressSubtitle": "Gerencie os endereços do cliente", "typeLabel": "Tipo de Cliente", "cpfLabel": "Pessoa Física (CPF)", "cnpjLabel": "<PERSON><PERSON><PERSON> (CNPJ)", "documentLabel": "Documento", "statusLabel": "Cliente Bloqueado", "activeStatus": "Cliente Ativo", "inactiveStatus": "Cliente Bloqueado", "nameLabel": "Nome <PERSON>to", "emailLabel": "E-mail", "phoneLabel": "<PERSON><PERSON><PERSON>", "birthDateLabel": "Data de Nascimento", "marketingLabel": "Marketing", "marketingDescription": "Aceita receber mensagens promocionais por email", "phoneMarketingDescription": "Aceita receber mensagens promocionais por telefone (SMS/Whatsapp)", "photoLabel": "Foto do Cliente", "selectPhoto": "Selecionar Foto", "changePhoto": "<PERSON><PERSON><PERSON>", "removePhoto": "Remover Foto", "photoFormatHint": "Formatos aceitos: JPG, PNG, GIF (máx. 2MB)", "photoUploadError": "Erro ao fazer upload da foto. Cliente salvo sem foto.", "save": "<PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "createSuccess": "Cliente criado com sucesso", "updateSuccess": "Cliente atualizado com sucesso", "saveError": "Erro ao salvar cliente", "status": {"active": "Ativo", "inactive": "Inativo", "banned": "Banido", "pending": "Pendente"}, "fields": {"name": "Nome", "email": "E-mail", "phone": "<PERSON><PERSON><PERSON>", "status": "Status", "registrationDate": "Data de Cadastro", "lastLogin": "<PERSON><PERSON><PERSON>", "orders": "Pedidos", "totalSpent": "Total Gasto", "actions": "Ações", "billingAddress": "Endereço de Cobrança", "shippingAddress": "Endereço de Entrega", "address1": "Endereço Linha 1", "address2": "Endereço Linha 2", "city": "Cidade", "state": "Estado/Província", "postalCode": "CEP", "country": "<PERSON><PERSON>", "company": "Empresa", "taxNumber": "CNPJ/CPF", "notes": "Observações"}, "tabs": {"overview": "Visão Geral", "orders": "Pedidos", "addresses": "Endereços", "activity": "Atividade", "notes": "Notas"}, "messages": {"loadError": "Erro ao carregar clientes", "deleteConfirm": "Tem certeza que deseja excluir este cliente?", "deleteSuccess": "Cliente excluído com sucesso", "deleteError": "Erro ao excluir cliente", "saveSuccess": "Cliente salvo com sucesso", "saveError": "Erro ao salvar cliente"}, "actions": {"addNew": "Adicionar Novo Cliente", "export": "Exportar", "import": "Importar", "sendEmail": "Enviar E-mail", "resetPassword": "<PERSON><PERSON><PERSON><PERSON>", "viewOrders": "Ver Pedidos", "viewAddresses": "<PERSON><PERSON>", "viewActivity": "Ver Atividade", "addNote": "<PERSON><PERSON><PERSON><PERSON>", "editNote": "<PERSON><PERSON>", "deleteNote": "Excluir Nota"}, "filters": {"all": "Todos os Clientes", "new": "Novos Clientes", "returning": "Clientes Recorrentes", "highValue": "Clientes de Alto Valor", "inactive": "Clientes Inativos"}, "groups": {"title": "Grupos de Clientes", "addGroup": "Adicionar Grupo", "editGroup": "Editar Grupo", "deleteGroup": "Excluir Grupo", "groupName": "Nome do Grupo", "groupDescription": "Descrição", "groupDiscount": "<PERSON><PERSON><PERSON> (%)", "groupMinimumOrder": "<PERSON>ed<PERSON>", "groupTaxExempt": "Isento de Impostos", "groupFreeShipping": "Frete <PERSON>", "groupCustomers": "Clientes no Grupo"}, "importExport": {"title": "Importar/Exportar Clientes", "import": {"title": "Importar Clientes", "description": "Faça upload de um arquivo CSV contendo dados de clientes.", "downloadTemplate": "Baixar Modelo", "uploadFile": "Enviar Arquivo", "mapping": "Mapeamento CSV", "preview": "Pré-visualizar", "import": "Importar", "success": "{count} clientes importados com sucesso", "error": "Erro ao importar clientes"}, "export": {"title": "Exportar Clientes", "description": "Exporte os dados dos clientes para um arquivo CSV.", "format": "Formato", "fields": "Campos para Exportar", "export": "Exportar", "success": "Exportação concluída com sucesso", "error": "Erro ao exportar clientes"}}, "export": {"title": "Exportar Lista de Clientes", "subtitle": "Selecione os campos que deseja incluir na exportação", "fileFormat": "Formato do Arquivo", "csvDescription": "Arquivo CSV (valores separados por vírgula)", "xlsxDescription": "Planilha Excel (XLSX)", "fieldSelection": "Seleção de Campos", "selectAllFields": "Selecionar todos os campos", "selectAllDescription": "Marque esta opção para selecionar todos os campos disponíveis de uma vez", "basicData": "Dados Básicos", "basicDataDescription": "Selecione o formato do arquivo para exportação", "basicFields": "Campos Básicos", "addressFields": "Endereço", "totalCustomers": "Total de clientes", "customers": "clientes", "fieldSelectionDescription": "Escolha quais campos incluir na exportação", "summaryDescription": "Revise as configuraç<PERSON><PERSON> antes de iniciar", "fields": "campos", "summary": "Resumo da Exportação", "format": "Formato", "selectedFieldsCount": "Campos selecionados", "exportData": "Exportar Dados", "confirmTitle": "Confirmar <PERSON>rta<PERSON>", "confirmMessage": "Deseja iniciar a exportação dos dados selecionados?", "confirmExport": "Iniciar <PERSON>", "startExport": "Iniciar <PERSON>", "viewExports": "Ver Exportações", "exportStarted": "Exportação Iniciada", "exportStartedMessage": "A exportação foi iniciada com sucesso.", "exportError": "Erro ao iniciar a exportação", "noFieldsSelected": "Selecione pelo menos um campo para exportar"}, "exportList": {"title": "Exportações de Clientes", "subtitle": "Gerencie suas exportações de dados de clientes", "newExport": "Nova Exportação", "fileName": "Nome do Arquivo", "createdAt": "Data de Criação", "fileSize": "<PERSON><PERSON><PERSON>", "status": "Status", "actions": "Ações", "download": "Baixar arquivo", "delete": "Excluir exportação", "noExports": "Nenhuma exportação encontrada", "noExportsDescription": "Você ainda não criou nenhuma exportação de clientes", "createFirstExport": "Criar Primeira Exportação", "loadError": "Erro ao carregar exportações", "downloadStarted": "Download iniciado", "downloadError": "Erro ao baixar arquivo", "deleteTitle": "Confirmar <PERSON>", "deleteMessage": "Tem certeza que deseja excluir esta exportação?", "deleteConfirm": "Excluir", "deleteSuccess": "Exportação excluída com sucesso", "deleteError": "Erro ao excluir exportação", "infoTitle": "Informações Importantes", "retentionTitle": "Retenção de Arquivos", "retentionDescription": "Arquivos são mantidos automaticamente por 30 dias e depois removidos", "processingTitle": "Tempo de Processamento", "processingDescription": "Exportações grandes podem demorar alguns minutos para serem concluídas", "formatsTitle": "Formatos Disponíveis", "formatsDescription": "Suporte para CSV e Excel (.xlsx) com dados completos dos clientes", "loading": "Carregando exportações...", "backToList": "Voltar para Lista de Clientes"}, "list": {"title": "Lista de Clientes", "importButton": "Importar Clientes", "exportButton": "Exportar Clientes", "name": "Nome", "email": "E-mail", "phone": "Telefone", "document": "Documento", "creationDate": "Data de Cadastro", "noCustomers": "Nenhum cliente encontrado"}, "import": {"title": "Importar Clientes", "subtitle": "Importe dados de clientes a partir de um arquivo CSV", "viewImports": "Ver Importações", "startImport": "Inici<PERSON>", "fileSelection": "Seleção de Arquivo", "fileSelectionDescription": "Selecione um arquivo CSV para importar", "selectFile": "Selecionar Arquivo CSV", "selectFileDescription": "Arraste e solte um arquivo CSV aqui ou clique para selecionar", "chooseFile": "Escolher <PERSON>", "removeFile": "Remover Arquivo", "nextStep": "Próximo <PERSON>", "fieldMapping": "Mapeamento de Campos", "fieldMappingDescription": "Configure como os campos do CSV serão mapeados", "csvPreview": "Prévia do CSV", "columns": "colu<PERSON>", "extraColumnsWarning": "Arquivo contém {count} colunas extras que podem não ser mapeadas", "missingColumnsInfo": "Considere adicionar campos obrigatórios como nome e email", "mapFields": "Mapear <PERSON>s", "mappingPreview": "Preview dos Dados Mapeados", "mappingPreviewDescription": "Veja como os dados serão salvos no banco", "csvField": "Campo do CSV", "iluriaField": "Campo do Iluria", "selectField": "Selecionar campo...", "fieldIgnored": "Campo ignorado (gerado automaticamente)", "basicFields": "Campos Básicos", "addressFields": "Campos de Endereço", "completeAddressFields": "Endereços Completos", "addressComponentFields": "Componentes de Endereço", "backToFile": "Voltar ao Arquivo", "confirmMessage": "Tem certeza que deseja iniciar a importação?", "confirmTitle": "Confirma<PERSON>", "importStarted": "Importação Iniciada", "importStartedMessage": "A importação foi iniciada e será processada em segundo plano", "importError": "Erro ao iniciar importação", "parseError": "Erro ao processar arquivo CSV", "fieldsMapped": "campos mapeados", "fieldsAutomaticallyMapped": "campos mapeados automaticamente", "fieldsWithoutValidData": "campos sem dados válidos foram ocultados", "exampleRecords": "registros de exemplo", "howWillBeSaved": "Como será salvo no banco de dados", "mainCustomerData": "Dados principais dos clientes", "customerAddresses": "Endereços dos clientes", "intelligentAddressMapping": "Mapeamento Inteligente de Endereços", "completeAddresses": "Endereços Completos", "completeAddressesDescription": "Campos com endereços completos são automaticamente detectados e parseados em campos separados.", "completeAddressesExample": "Exemplo: \"Rua A, 123 - <PERSON><PERSON>, Cidade - SP, CEP 01001-000\"", "separateFields": "Campos Separados", "separateFieldsDescription": "Campos individuais como \"street\", \"city\", \"state\" são mapeados diretamente para os campos correspondentes do sistema.", "customerData": "Dados dos Clientes", "customerDataDescription": "Informações principais que serão salvas na tabela customers", "customer": "Cliente", "fields": {"name": "Nome <PERSON>to", "email": "E-mail", "nickname": "Apelido", "phone": "Telefone", "document": "CPF/CNPJ", "documentType": "Tipo de Documento", "dayOfBirth": "Data de Nascimento", "defaultLanguage": "Idioma Padrão", "allowsEmailMarketing": "Permite Marketing por E-mail", "allowsPhoneMarketing": "Permite Marketing por Telefone", "active": "Ativo", "street": "Rua/Endereço", "number": "Número", "complement": "Complemento", "district": "Bairro", "city": "Cidade", "state": "Estado", "country": "<PERSON><PERSON>", "zipCode": "CEP", "type": "Tipo", "label": "<PERSON><PERSON><PERSON><PERSON>"}, "tabs": {"import": "Importar", "guide": "Guia de Importação"}, "guide": {"csvPreparation": "Preparação do Arquivo CSV", "csvPreparationDescription": "Como estruturar seu arquivo CSV para obter os melhores resultados", "csvStructure": "Estrutura do CSV", "csvStructureDescription": "Seu arquivo CSV deve ter uma linha de cabeçalho com os nomes dos campos, seguida pelos dados dos clientes:", "bestPractices": "<PERSON><PERSON><PERSON>", "practice1": "Use UTF-8 como codificação para caracteres especiais", "practice2": "Mantenha os cabeçalhos em português ou inglês simples", "practice3": "<PERSON><PERSON><PERSON> c<PERSON> vazias - use '-' ou 'N/A' quando necessário", "practice4": "Teste com um arquivo pequeno primeiro (10-20 registros)", "fieldMapping": "Mapeamento de Campos", "fieldMappingDescription": "Como mapear os campos do seu CSV para os campos do sistema", "customerFields": "Campos de Cliente", "addressFields": "Campos de Endereço", "advancedTips": "Di<PERSON>", "advancedTipsDescription": "Recursos especiais para maximizar sua importação", "addressTip": "Endereços Completos", "addressTipDescription": "Use um campo com endereço completo e o sistema fará o parsing automático:", "dataTip": "Formato de Datas", "dataTipDescription": "Use formatos padrão para datas de nascimento:", "validationTip": "Validação Automática", "validationTipDescription": "O sistema valida automaticamente emails, telefones e documentos durante a importação"}}, "importList": {"title": "Importações de Clientes", "subtitle": "Gerencie suas importações de dados de clientes", "fileName": "Arquivo", "status": "Status", "records": "Registros", "createdAt": "C<PERSON><PERSON> em", "completedAt": "<PERSON>clu<PERSON><PERSON> em", "newImport": "Nova Importação", "processed": "processados", "actions": "Ações", "noImports": "Nenhuma importação encontrada", "noImportsDescription": "Você ainda não criou nenhuma importação de clientes", "createFirstImport": "Criar Primeira Importação", "loadError": "Erro ao carregar importações", "deleteConfirmMessage": "Tem certeza que deseja excluir a importação '{fileName}'?", "deleteConfirmTitle": "Confirmar <PERSON>", "deleteSuccess": "Importação excluída com sucesso", "deleteError": "Erro ao excluir importação"}, "bulk": {"selectedOne": "selecionado", "selectedPlural": "selecionados", "selectedAny": "selecionado", "selectedPluralAny": "selecionados", "actions": "Ações em massa", "deleteSelected": "Excluir selecionados", "deleteSelectedShort": "Excluir", "selectCustomer": "Selecionar cliente", "confirmDeleteTitle": "Confirmar exclusão em massa", "confirmDeleteMessage": "Tem certeza que deseja excluir {count} {entity}? Esta ação não pode ser desfeita.", "confirmDeleteMessageSingle": "Tem certeza que deseja excluir 1 {entity}? Esta ação não pode ser desfeita.", "deleteSuccess": "{count} {entity} excluídos com sucesso", "deleteSuccessSingle": "1 {entity} excluído com sucesso", "deleteError": "Erro ao excluir {entity} selecionados", "deletePartialError": "Alguns {entity} não puderam ser excluídos", "processing": "Processando...", "clearSelection": "<PERSON><PERSON>", "selectAll": "Selecionar todos", "deselectAll": "<PERSON><PERSON><PERSON> to<PERSON>", "customer": "cliente", "customers": "clientes"}}