<template>
  <div class="mb-6">
    <IluriaLabel>{{ t('promotions.editor.minimumOrderValue') }}</IluriaLabel>
    <IluriaInputText
      id="minimumOrderValue"
      v-model.number="modelValue.minValue"
      :placeholder="t('promotions.editor.minimumOrderPlaceholder')"
      type="money"
      prefix="R$"
      class="w-full"
      @update:modelValue="emitUpdate"
    />
  </div>
</template>

<script setup>
import { useI18n } from 'vue-i18n';
import IluriaLabel from '@/components/iluria/IluriaLabel.vue';
import IluriaInputText from '@/components/iluria/form/IluriaInputText.vue';

const { t } = useI18n();

const props = defineProps({
  modelValue: {
    type: Object,
    required: true
  }
});

const emit = defineEmits(['update:modelValue']);

const emitUpdate = () => {
  emit('update:modelValue', props.modelValue);
};
</script>
