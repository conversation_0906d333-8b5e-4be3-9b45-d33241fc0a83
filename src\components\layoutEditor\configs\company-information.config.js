/**
 * Configuração do Componente de Informações da Empresa
 * Migrado de CompanyInformation.js para o novo sistema de configs
 */

export default {
  type: 'company-information',
  name: 'Informações da Empresa',
  description: 'Adiciona um bloco com informações da empresa',
  category: 'content',
  priority: 70,
  icon: 'business',
  
  html: `<div data-component="company-information" data-element-type="company-information" class='iluria-company-information' style="margin: 0; width: 100%; box-sizing: border-box;">
    <div class="company-info-content">
      <h2 class="company-info-title">Alimentos frescos e deliciosos</h2>
      <div class="company-info-description">
        <p>Temos orgulho de levar nossos pratos premiados aos nossos clientes. Todos os alimentos favoritos da estação podem ser encomendados on-line com a coleta ou entrega disponível. Peça o máximo ou o mínimo que quiser com ótimos descontos para grandes pedidos. Entre em contato conosco se tiver alguma dúvida.</p>
      </div>
      <div class="company-info-action">
        <button class="company-info-button">Saiba mais</button>
      </div>
    </div>
    <div class="company-info-image">
      <img src="https://placehold.co/300x250" alt="Imagem da empresa">
    </div>
    
    <!-- CSS para neutralizar margens do container -->
    <style>
      .iluria-company-information {
        margin: 0 !important;
        width: 100% !important;
        box-sizing: border-box !important;
      }
      
      /* Neutralizar padding do element-content apenas para company-information */
      .component-container:has(.iluria-company-information) .element-content {
        padding: 0 !important;
        margin: 0 !important;
      }
      
      /* Fallback para navegadores sem :has() */
      .element-content:has(.iluria-company-information) {
        padding: 0 !important;
        margin: 0 !important;
      }
    </style>
  </div>`,
  
  toolbarActions: [
    {
      type: 'company-information-config',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor">
        <path d="M3 21h18"/>
        <path d="M5 21V7l8-4v18"/>
        <path d="M19 21V11l-6-4"/>
      </svg>`,
      title: 'layoutEditor.editCompanyInformation',
      condition: (element) => {
        return element?.hasAttribute('data-element-type') && 
               element?.getAttribute('data-element-type') === 'company-information'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'company-information-config',
      component: 'CompanyInformationSidebarEditor',
      path: '@/components/layoutEditor/Sidebar/editors/CompanyInformationSidebarEditor.vue'
    }
  ],
  
  computedCheckers: {
    showCompanyInfoEditor: (element) => {
      return element?.hasAttribute('data-element-type') && 
             element?.getAttribute('data-element-type') === 'company-information'
    }
  },
  
  options: {
    selectable: true,
    editable: true,
    deletable: true,
    movable: true
  },
  
  cleanup: {
    removeEditingStyles: true,
    specialElements: []
  }
} 