<template>
  <div>
    <label v-if="label" class="file-upload-label">
      {{ label }}
    </label>
    <FileUpload ref="fileUploadRef" :multiple="multiple" :accept="accept" :maxFileSize="maxFileSize" @select="onSelect"
      @error="onError" :customUpload="true" :auto="auto" :showCancelButton="showCancelButton"
      :showUploadButton="showUploadButton" :chooseLabel="localChooseLabel" :uploadLabel="localUploadLabel"
      :cancelLabel="localCancelLabel" :showPreviewBox="true">
      <template #empty>
        <div class="flex flex-col items-center justify-center">
          <!-- Empty state is handled by header template -->
        </div>
      </template>
      <template #header="{ chooseCallback, uploadCallback, clearCallback, files }">
        <div class="upload-empty-state">
          <IluriaButton color="primary" @click="chooseCallback()" :hugeIcon="ImageUploadIcon">{{ localChooseLabel }}
          </IluriaButton>
          <p class="empty-message">{{ localEmptyMessage }}</p>
        </div>
      </template>
      <template #content="{ files, uploadedFiles, removeUploadedFileCallback, removeFileCallback, messages }">
        <div>
          <!-- Image Preview (for image files) -->
          <div v-if="showPreviewBox && isImageUpload" class="images-container">
            <!-- Existing Images -->
            <div v-for="image in existingImages" :key="image.id" class="image-preview-container">
              <div class="image-preview">
                <img role="presentation" :alt="image.id" :src="image.url" class="preview-image" />
              </div>

              <IluriaButton color="text-danger" size="small" :hugeIcon="Delete01Icon"
                @click.prevent="confirmDeleteImage(image)">{{ t('delete') }}</IluriaButton>
            </div>

            <!-- New Images -->
            <div v-for="(file, index) of files" :key="file.name + file.type + file.size"
              class="image-preview-container">
              <div class="image-preview">
                <img role="presentation" :alt="file.name" :src="file.objectURL" class="preview-image" />
              </div>

              <IluriaButton color="text-danger" size="small" :hugeIcon="Delete01Icon"
                @click.prevent="removeFileCallback(index)">{{ t('delete') }}</IluriaButton>
            </div>
          </div>

          <!-- File List (for non-image files) -->
          <div v-if="files && files.length > 0 && !isImageUpload" class="files-container">
            <div v-for="(file, index) of files" :key="file.name + file.type + file.size" class="file-item">
              <div class="file-info">
                <div class="file-icon">
                  <i class="pi pi-file"></i>
                </div>
                <div class="file-details">
                  <span class="file-name">{{ file.name }}</span>
                  <span class="file-size">{{ formatFileSize(file.size) }}</span>
                </div>
              </div>
              <IluriaButton color="text-danger" size="small" :hugeIcon="Delete01Icon"
                @click.prevent="removeFileCallback(index)">{{ t('delete') }}</IluriaButton>
            </div>
          </div>
        </div>
      </template>
    </FileUpload>

    <!-- Confirmation Dialog -->
    <Dialog v-model:visible="confirmDialogVisible" :header="t('product.confirmDeleteImage')" :style="{ width: '450px' }"
      :modal="true">
      <div class="confirmation-content">
        <i class="pi pi-exclamation-triangle" />
        <span>{{ t('product.confirmDeleteImageMessage') }}</span>
      </div>
      <template #footer>
        <Button :label="t('cancel')" icon="pi pi-times" @click="confirmDialogVisible = false" class="p-button-text" />
        <Button :label="t('delete')" icon="pi pi-check" @click="deleteExistingImage" class="p-button-danger" />
      </template>
    </Dialog>
  </div>
</template>

<script setup>
import { FileUpload } from 'primevue';
import IluriaButton from '@/components/iluria/IluriaButton.vue';
import { ref, computed } from 'vue';
import { useI18n } from 'vue-i18n';
import { Cancel01Icon, Delete01Icon, ImageUploadIcon } from '@hugeicons-pro/core-bulk-rounded';
import Dialog from 'primevue/dialog';
import Button from 'primevue/button';

const { t } = useI18n();
const fileUploadRef = ref(null);
const confirmDialogVisible = ref(false);
const imageToDelete = ref(null);

const props = defineProps({
  label: {
    type: String,
    default: ''
  },
  multiple: {
    type: Boolean,
    default: true
  },
  accept: {
    type: String,
    default: 'image/*'
  },
  maxFileSize: {
    type: Number,
    default: 1000000 // 1MB
  },
  auto: {
    type: Boolean,
    default: false
  },
  showCancelButton: {
    type: Boolean,
    default: false
  },
  showUploadButton: {
    type: Boolean,
    default: false
  },
  chooseLabel: {
    type: String,
    default: ''
  },
  uploadLabel: {
    type: String,
    default: ''
  },
  cancelLabel: {
    type: String,
    default: ''
  },
  emptyMessage: {
    type: String,
    default: ''
  },
  existingImages: {
    type: Array,
    default: () => []
  },
  showPreviewBox: {
    type: Boolean,
    default: true
  }
});

// Use computed properties with i18n for labels
const localChooseLabel = computed(() => props.chooseLabel || t('product.uploadImages'));
const localUploadLabel = computed(() => props.uploadLabel || t('save'));
const localCancelLabel = computed(() => props.cancelLabel || t('cancel'));
const localEmptyMessage = computed(() => props.emptyMessage || t('product.dragAndDropImages'));

const emit = defineEmits(['select', 'error', 'remove-image']);

// Check if this is an image upload based on accept prop
const isImageUpload = computed(() => {
  return props.accept.includes('image') || props.accept === 'image/*';
});

function onSelect(event) {
  emit('select', event);
}

function onError(event) {
  emit('error', event);
}

function formatFileSize(bytes) {
  if (bytes === 0) return '0 Bytes';
  const k = 1024;
  const sizes = ['Bytes', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
}

function confirmDeleteImage(image) {
  imageToDelete.value = image;
  confirmDialogVisible.value = true;
}

function deleteExistingImage() {
  if (imageToDelete.value) {
    emit('remove-image', imageToDelete.value.id);
    imageToDelete.value = null;
  }
  confirmDialogVisible.value = false;
}

// Expose the file upload ref to parent components
defineExpose({
  fileUploadRef
});
</script>

<style scoped>
/* Label */
.file-upload-label {
  display: block;
  margin-bottom: 0.5rem;
  font-size: 14px;
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  cursor: pointer;
  transition: color 0.3s ease;
}

/* Empty State with integrated button */
.upload-empty-state {
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 2rem 1rem;
  background: var(--iluria-color-surface);
  border: 2px dashed var(--iluria-color-border);
  border-radius: 8px;
  color: var(--iluria-color-text-secondary);
  gap: 0.75rem;
  transition: all 0.3s ease;
  cursor: pointer;
  min-height: 120px;
  width: calc(100% - 4px);
  margin: 2px;
}

.upload-empty-state:hover {
  border-color: var(--iluria-color-primary);
  background: var(--iluria-color-hover);
}

/* Remove button shadow and adjust styling */
.upload-empty-state :deep(.p-button),
.upload-empty-state :deep(button) {
  box-shadow: none !important;
  border: 1px solid var(--iluria-color-primary) !important;
  transition: all 0.2s ease !important;
}

.upload-empty-state :deep(.p-button:hover),
.upload-empty-state :deep(button:hover) {
  box-shadow: none !important;
  transform: translateY(-1px) !important;
  border-color: var(--iluria-color-primary-hover) !important;
}

.upload-empty-state :deep(.p-button:focus),
.upload-empty-state :deep(button:focus) {
  box-shadow: 0 0 0 2px var(--iluria-color-focus-ring) !important;
}

.upload-empty-state :deep(.p-button:active),
.upload-empty-state :deep(button:active) {
  transform: translateY(0) !important;
  box-shadow: none !important;
}

.empty-message {
  margin-bottom: 0.5rem;
  font-size: 14px;
  font-weight: normal;
  text-align: center;
  color: var(--iluria-color-text-secondary);
}

/* Images Container */
.images-container {
  display: flex;
  flex-wrap: wrap;
  gap: 1rem;
}

/* Files Container (for non-image files) */
.files-container {
  display: flex;
  flex-direction: column;
  gap: 0.75rem;
  padding: 1rem 0;
}

.file-item {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: 0.75rem;
  background: var(--iluria-color-container-bg);
  border: 1px solid var(--iluria-color-border);
  border-radius: 8px;
  transition: all 0.3s ease;
}

.file-item:hover {
  border-color: var(--iluria-color-primary);
  transform: translateY(-1px);
  box-shadow: var(--iluria-shadow-sm);
}

.file-info {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  flex: 1;
}

.file-icon {
  width: 2.5rem;
  height: 2.5rem;
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--iluria-color-primary-light);
  color: var(--iluria-color-primary);
  border-radius: 6px;
  font-size: 1.25rem;
}

.file-details {
  display: flex;
  flex-direction: column;
  gap: 0.25rem;
}

.file-name {
  font-weight: 500;
  color: var(--iluria-color-text-primary);
  font-size: 14px;
}

.file-size {
  font-size: 12px;
  color: var(--iluria-color-text-secondary);
}

/* Image Preview Container */
.image-preview-container {
  padding: 0.25rem 0.25rem 0.5rem 0.25rem;
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 0.5rem;
  width: 7rem;
  height: 8.75rem;
  border: 2px dashed var(--iluria-color-border);
  border-radius: 8px;
  background: var(--iluria-color-container-bg);
  transition: all 0.3s ease;
}

.image-preview-container:hover {
  border-color: var(--iluria-color-primary);
  transform: translateY(-2px);
  box-shadow: var(--iluria-shadow-md);
}

/* Image Preview */
.image-preview {
  width: 4rem;
  height: 4rem;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
  border-radius: 6px;
  background: var(--iluria-color-surface);
  border: 1px solid var(--iluria-color-border);
}

.preview-image {
  object-fit: cover;
  width: 100%;
  height: 100%;
  transition: transform 0.3s ease;
}

.image-preview-container:hover .preview-image {
  transform: scale(1.05);
}

/* Confirmation Dialog */
.confirmation-content {
  display: flex;
  align-items: center;
  gap: 0.75rem;
  color: var(--iluria-color-text-primary);
}

.confirmation-content i {
  font-size: 2rem;
  color: var(--iluria-color-warning);
}

.confirmation-content span {
  color: var(--iluria-color-text-primary);
}

/* FileUpload Component Overrides */
:deep(.p-fileupload) {
  background: transparent !important;
  border: none !important;
  border-radius: 0 !important;
  color: var(--iluria-color-text-primary) !important;
  width: 100% !important;
  min-height: 140px !important;
}

:deep(.p-fileupload-content) {
  background: transparent !important;
  color: var(--iluria-color-text-primary) !important;
  border: none !important;
  padding: 0 !important;
  min-height: 10px !important;
  width: 100% !important;
}

:deep(.p-fileupload-buttonbar) {
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  height: 100% !important;
  min-height: 120px !important;
  width: 100% !important;
}

/* Dialog Overrides */
:deep(.p-dialog) {
  background: var(--iluria-color-container-bg) !important;
  color: var(--iluria-color-text-primary) !important;
  border: 1px solid var(--iluria-color-border) !important;
}

:deep(.p-dialog-header) {
  background: var(--iluria-color-surface) !important;
  color: var(--iluria-color-text-primary) !important;
  border-bottom: 1px solid var(--iluria-color-border) !important;
}

:deep(.p-dialog-footer) {
  background: var(--iluria-color-surface) !important;
  border-top: 1px solid var(--iluria-color-border) !important;
}

/* Responsive */
@media (max-width: 768px) {
  .images-container {
    gap: 0.75rem;
  }

  .image-preview-container {
    width: 6rem;
    height: 7.5rem;
  }

  .image-preview {
    width: 3.5rem;
    height: 3.5rem;
  }
}

@media (max-width: 480px) {
  .images-container {
    gap: 0.5rem;
  }

  .image-preview-container {
    width: 5rem;
    height: 6.5rem;
  }

  .image-preview {
    width: 3rem;
    height: 3rem;
  }
}
</style>
