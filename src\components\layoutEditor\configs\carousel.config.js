export default {
  name: '<PERSON>ossel',
  type: 'carousel',
  dataComponent: 'carousel',
  elementType: 'carousel',
  className: 'iluria-carousel',
  selectors: ['[data-component="carousel"]', '.iluria-carousel'],
  category: 'media',
  priority: 85,
  icon: 'view_carousel',
  description: 'Carrossel de imagens interativo com navegação e indicadores',
  
  html: `<div
    data-component="carousel"
    data-element-type="carousel"
    data-autoplay="true"
    data-interval="5000"
    data-show-dots="true"
    data-show-arrows="true"
    data-infinite="true"
    data-transition="slide"
    data-height="400"
    data-border-radius="12"
    data-title-color="#ffffff"
    data-description-color="#f0f0f0"

    data-title-font-size="48"
    data-description-font-size="18"
    data-slides='[{"id":"slide-1","title":"Slide 1","description":"Descrição do primeiro slide","imageUrl":"","active":true,"titleColor":"#ffffff","descriptionColor":"#f0f0f0","overlayColor":"#000000","overlayOpacity":0.3,"overlayEnabled":null,"titleFontSize":48,"descriptionFontSize":18},{"id":"slide-2","title":"Slide 2","description":"Descrição do segundo slide","imageUrl":"","active":true,"titleColor":"#ffffff","descriptionColor":"#f0f0f0","overlayColor":"#000000","overlayOpacity":0.3,"overlayEnabled":null,"titleFontSize":48,"descriptionFontSize":18}]'
    class="iluria-carousel"
    style="width: 100%; height: 400px; position: relative; overflow: hidden; border-radius: 12px; background: #f8fafc;">
    <div class="carousel-container" style="position: relative; width: 100%; height: 100%;">
      <div class="carousel-track" style="display: flex; width: 200%; height: 100%; transition: transform 0.5s ease;">
        <div class="carousel-slide active" style="flex: 0 0 50%; height: 100%; background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; background-size: cover !important; background-position: center !important; background-repeat: no-repeat !important;">
          <div class="slide-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; z-index: 3; max-width: 90%; padding: 0 20px;">
            <h3 style="font-size: 48px; margin-bottom: 1rem; font-weight: bold; color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); line-height: 1.2;">Slide 1</h3>
            <p style="font-size: 18px; opacity: 0.95; margin-bottom: 1.5rem; color: #f0f0f0; text-shadow: 1px 1px 2px rgba(0,0,0,0.7); line-height: 1.4;">Descrição do primeiro slide</p>
          </div>
        </div>
        <div class="carousel-slide" style="flex: 0 0 50%; height: 100%; background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%); position: relative; background-size: cover !important; background-position: center !important; background-repeat: no-repeat !important;">
          <div class="slide-content" style="position: absolute; top: 50%; left: 50%; transform: translate(-50%, -50%); text-align: center; color: white; z-index: 3; max-width: 90%; padding: 0 20px;">
            <h3 style="font-size: 48px; margin-bottom: 1rem; font-weight: bold; color: #ffffff; text-shadow: 2px 2px 4px rgba(0,0,0,0.7); line-height: 1.2;">Slide 2</h3>
            <p style="font-size: 18px; opacity: 0.95; margin-bottom: 1.5rem; color: #f0f0f0; text-shadow: 1px 1px 2px rgba(0,0,0,0.7); line-height: 1.4;">Descrição do segundo slide</p>
          </div>
        </div>
      </div>
      <div class="carousel-navigation" style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; pointer-events: none; z-index: 5;">
        <button class="carousel-prev" onclick="this.closest('[data-component=carousel]').dispatchEvent(new CustomEvent('carousel:prev'))" style="background: rgba(255,255,255,0.6); border: 2px solid rgba(0,0,0,0.3); width: 44px; height: 44px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); pointer-events: auto; box-shadow: 0 4px 16px rgba(0,0,0,0.25); position: absolute; left: 15px; top: 50%; transform: translateY(-50%); z-index: 10; backdrop-filter: blur(4px);">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
            <path d="m15 18-6-6 6-6"/>
          </svg>
        </button>
        <button class="carousel-next" onclick="this.closest('[data-component=carousel]').dispatchEvent(new CustomEvent('carousel:next'))" style="background: rgba(255,255,255,0.6); border: 2px solid rgba(0,0,0,0.3); width: 44px; height: 44px; border-radius: 50%; cursor: pointer; display: flex; align-items: center; justify-content: center; transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1); pointer-events: auto; box-shadow: 0 4px 16px rgba(0,0,0,0.25); position: absolute; right: 15px; top: 50%; transform: translateY(-50%); z-index: 10; backdrop-filter: blur(4px);">
          <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2.5">
            <path d="m9 18 6-6-6-6"/>
          </svg>
        </button>
      </div>
      <div class="carousel-indicators" style="position: absolute; bottom: 16px; left: 50%; transform: translateX(-50%); display: flex; align-items: center; justify-content: center; gap: 0px; background: rgba(0,0,0,0.35); padding: 6px 12px; border-radius: 25px; backdrop-filter: blur(8px); border: 1px solid rgba(255,255,255,0.1); box-shadow: 0 4px 16px rgba(0,0,0,0.3); min-height: 16px;">
        <div class="carousel-indicator-wrapper" style="position: relative; padding: 0px; display: flex; align-items: center; justify-content: center; height: 16px;">
          <button class="carousel-indicator active" data-slide="0" onclick="this.closest('[data-component=carousel]').dispatchEvent(new CustomEvent('carousel:goto', {detail: {slide: 0}}))" style="width: 12px; height: 1.5px; border-radius: 0.75px; border: none; background: rgba(255,255,255,0.95); cursor: pointer; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); margin: 0 2px; box-shadow: 0 1px 4px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, rgba(255,255,255,0.4), transparent, rgba(255,255,255,0.4)); border-radius: 0.75px;"></div>
          </button>
        </div>
        <div class="carousel-indicator-wrapper" style="position: relative; padding: 0px; display: flex; align-items: center; justify-content: center; height: 16px;">
          <button class="carousel-indicator" data-slide="1" onclick="this.closest('[data-component=carousel]').dispatchEvent(new CustomEvent('carousel:goto', {detail: {slide: 1}}))" style="width: 4px; height: 1.5px; border-radius: 0.75px; border: none; background: rgba(255,255,255,0.5); cursor: pointer; transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1); margin: 0 2px; box-shadow: 0 1px 4px rgba(0,0,0,0.3); position: relative; overflow: hidden;">
            <div style="position: absolute; top: 0; left: 0; width: 100%; height: 100%; background: linear-gradient(90deg, rgba(255,255,255,0.4), transparent, rgba(255,255,255,0.4)); border-radius: 0.75px;"></div>
          </button>
        </div>
      </div>
    </div>
  </div>`,
  
  toolbarActions: [
    {
      type: 'carousel-config',
      title: 'Configurar Carrossel',
      icon: `<svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
        <rect x="2" y="6" width="20" height="12" rx="2"/>
        <circle cx="8" cy="12" r="2"/>
        <path d="m8 12 5-5 7 7"/>
      </svg>`,
      condition: (element) => {
        return element?.getAttribute('data-component') === 'carousel'
      }
    }
  ],
  
  propertyEditors: [
    {
      type: 'carousel-config',
      component: 'CarouselSidebarEditor',
      fallback: 'DefaultConfigEditor'
    }
  ],
  
  initialization: {
    autoInit: true,
    reinitOnChange: true,
    scriptLoader: 'initializeCarouselComponent'
  },
  
  cleanup: {
    removeEditingStyles: false,
    preserveStyles: [
      'width', 'height', 'position', 'overflow', 'border-radius', 'margin',
      'display', 'flex', 'transition', 'transform', 'background', 'color',
      'text-align', 'font-size', 'font-weight', 'opacity', 'padding',
      'border', 'cursor', 'box-shadow', 'z-index', 'backdrop-filter',
      'text-shadow', 'line-height', 'max-width'
    ],
    childSelectors: [
      '.carousel-container', '.carousel-track', '.carousel-slide', '.slide-content',
      '.carousel-navigation', '.carousel-prev', '.carousel-next', '.carousel-indicators', '.carousel-indicator'
    ]
  },
  
  detection: {
    priority: 65
  }
} 