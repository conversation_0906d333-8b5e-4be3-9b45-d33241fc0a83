import axios from 'axios';
import { API_URLS } from '../config/api.config';

class ProductExportService {
    #endpoint = API_URLS.PRODUCT_EXPORT_BASE_URL;

    // Create a new export job
    async createExport(fileType, selectedFields) {
        try {
            const response = await axios.post(this.#endpoint, {
                fileType,
                selectedFields
            }, {
                headers: {
                    'Content-Type': 'application/json'
                }
            });

            if (!response.data) {
                throw new Error('No response data received');
            }

            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Get list of export jobs
    async getExports(page = 0, size = 10) {
        try {
            const response = await axios.get(this.#endpoint, {
                params: { page, size }
            });
            return {
                content: response.data.content || [],
                totalPages: response.data.page?.totalPages || response.data.totalPages || 0,
                totalElements: response.data.page?.totalElements || response.data.totalElements || 0
            };
        } catch (error) {
            throw error;
        }
    }

    // Get a specific export job
    async getExport(id) {
        try {
            const response = await axios.get(`${this.#endpoint}/${id}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Generate secure download token
    async generateDownloadToken(id) {
        try {
            const response = await axios.get(`${this.#endpoint}/${id}/download-token`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Download file using secure token
    async downloadExportFile(id, fileName) {
        try {
            // First, generate a secure download token
            const tokenResponse = await this.generateDownloadToken(id);
            const downloadToken = tokenResponse.downloadToken;

            // Then download using the token
            const response = await axios.get(`${this.#endpoint}/download/${downloadToken}`, {
                responseType: 'blob',
                timeout: 60000 // 60 seconds timeout for large files
            });

            // Create blob link to download
            const url = window.URL.createObjectURL(new Blob([response.data]));
            const link = document.createElement('a');
            link.href = url;
            link.setAttribute('download', fileName);
            document.body.appendChild(link);
            link.click();
            link.remove();
            window.URL.revokeObjectURL(url);

        } catch (error) {
            throw error;
        }
    }

    // Delete an export job
    async deleteExport(id) {
        try {
            const response = await axios.delete(`${this.#endpoint}/${id}`);
            return response.data;
        } catch (error) {
            throw error;
        }
    }

    // Get available fields for export
    async getAvailableFields() {
        try {
            const response = await axios.get(`${this.#endpoint}/fields`);
            return response.data;
        } catch (error) {
            // Return default fields structure in case of error
            return {
                basic: [
                    'name',
                    'sku',
                    'description',
                    'shortDescription',
                    'price',
                    'originalPrice',
                    'costPrice',
                    'stock',
                    'stockQuantityTotal',
                    'weight',
                    'boxLength',
                    'boxWidth',
                    'boxDepth',
                    'type',
                    'status',
                    'hasVariation',
                    'highlight',
                    'newTag',
                    'barCode',
                    'supplierName',
                    'supplierLink',
                    'supplierNotes',
                    'metaTitle',
                    'metaDescription',
                    'createdAt',
                    'updatedAt'
                ],
                variations: [
                    'color',
                    'size',
                    'material',
                    'variationPrice',
                    'variationStock',
                    'variationSku'
                ]
            };
        }
    }

    // Get total product count for export preview
    async getProductCount() {
        try {
            const response = await axios.get(`${this.#endpoint}/count`);
            return response.data.count || 0;
        } catch (error) {
            return 0;
        }
    }

    // Format file size for display
    formatFileSize(bytes) {
        if (!bytes) return '0 B';
        
        const k = 1024;
        const sizes = ['B', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(k));
        
        return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
    }

    // Get status display text
    getStatusText(status) {
        const statusMap = {
            'PENDING': 'Pendente',
            'IN_PROGRESS': 'Em andamento',
            'COMPLETED': 'Concluído',
            'FAILED': 'Falhou'
        };
        return statusMap[status] || status;
    }

    // Get status color for UI
    getStatusColor(status) {
        const colorMap = {
            'PENDING': 'warning',
            'IN_PROGRESS': 'info',
            'COMPLETED': 'success',
            'FAILED': 'danger'
        };
        return colorMap[status] || 'secondary';
    }

    // Check if export can be downloaded
    canDownload(exportJob) {
        return exportJob.status === 'COMPLETED' && exportJob.fileSize > 0;
    }

    // Check if export can be deleted
    canDelete(exportJob) {
        return ['COMPLETED', 'FAILED'].includes(exportJob.status);
    }

    // Get field display name
    getFieldDisplayName(fieldName) {
        const fieldMap = {
            // Basic fields
            'name': 'Nome do Produto',
            'sku': 'SKU',
            'description': 'Descrição',
            'shortDescription': 'Descrição Curta',
            'price': 'Preço',
            'originalPrice': 'Preço Original',
            'costPrice': 'Preço de Custo',
            'stock': 'Estoque',
            'stockQuantityTotal': 'Quantidade Total em Estoque',
            'weight': 'Peso',
            'boxLength': 'Comprimento da Caixa',
            'boxWidth': 'Largura da Caixa',
            'boxDepth': 'Profundidade da Caixa',
            'type': 'Tipo',
            'status': 'Status',
            'hasVariation': 'Tem Variação',
            'highlight': 'Destaque',
            'newTag': 'Tag Novo',
            'barCode': 'Código de Barras',
            'supplierName': 'Nome do Fornecedor',
            'supplierLink': 'Link do Fornecedor',
            'supplierNotes': 'Notas do Fornecedor',
            'metaTitle': 'Meta Título',
            'metaDescription': 'Meta Descrição',
            'createdAt': 'Data de Criação',
            'updatedAt': 'Data de Atualização',
            
            // Variation fields
            'color': 'Cor',
            'size': 'Tamanho',
            'material': 'Material',
            'variationPrice': 'Preço da Variação',
            'variationStock': 'Estoque da Variação',
            'variationSku': 'SKU da Variação'
        };
        return fieldMap[fieldName] || fieldName;
    }
}

export default new ProductExportService();