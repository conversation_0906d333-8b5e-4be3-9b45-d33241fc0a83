<template>
  <button 
    :type="type" 
    :class="buttonClasses" 
    v-bind="$attrs" 
    :disabled="disabled || loading"
  >
    <div v-if="loading" class="loading-spinner"></div>
    <HugeiconsIcon 
      v-if="hugeIcon && !loading" 
      :icon="hugeIcon" 
      :size="iconSize"  
      class="button-icon"
    />
    <span v-if="$slots.default" class="button-text">
      <slot/>
    </span> 
  </button>
</template>

<script setup>
import { computed } from 'vue'
import { HugeiconsIcon } from '@hugeicons/vue'

const props = defineProps({
  hugeIcon: {
    type: Object,
    default: undefined
  },
  color: {
    type: String,
    default: 'dark'
  },
  type: {
    type: String,
    default: 'button'
  },
  size: {
    type: String,
    default: 'medium',
    validator: (value) => ['small', 'medium', 'large'].includes(value)
  },
  disabled: {
    type: Boolean,
    default: false
  },
  loading: {
    type: Boolean,
    default: false
  },
  variant: {
    type: String,
    default: 'solid',
    validator: (value) => ['solid', 'outline', 'ghost'].includes(value)
  }
})

const buttonClasses = computed(() => {
  const classes = ['btn', `btn-${props.color}`, `btn-${props.size}`]
  
  if (props.variant !== 'solid') {
    classes.push(`btn-${props.variant}`)
  }
  
  if (props.loading) {
    classes.push('btn-loading')
  }
  
  return classes.join(' ')
})

const iconSize = computed(() => {
  const sizes = {
    small: 14,
    medium: 16,
    large: 18
  }
  return sizes[props.size] || 16
})

defineOptions({
  name: 'IluriaButton',
  inheritAttrs: false
})
</script>

<style scoped>
.btn {
  position: relative;
  display: inline-flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-weight: 500;
  border-radius: 8px;
  border: 1px solid transparent;
  cursor: pointer;
  transition: all 0.15s ease;
  text-decoration: none;
  min-width: fit-content;
  white-space: nowrap;
  
  /* DaisyUI Button Press Effect - Shadow por padrão */
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.15);
}

/* DaisyUI Button Press Effect - Estado :active (pressionado) */
.btn:active:not(:disabled):not(.category-menu-button) {
  transform: translateY(1px);
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.1);
}

/* Exceção: Botões de abas/categorias não devem ter efeito de pressionar */
.btn.category-menu-button {
  box-shadow: none !important;
}

.btn.category-menu-button:active {
  transform: none;
}

.btn:focus {
  outline: none;
}

.btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
  pointer-events: none;
  box-shadow: none; /* Remove shadow quando desabilitado */
}

/* Sizes */
.btn-small {
  padding: 6px 12px;
  font-size: 13px;
  min-height: 32px;
}

.btn-medium {
  padding: 8px 16px;
  font-size: 14px;
  min-height: 36px;
}

.btn-large {
  padding: 12px 20px;
  font-size: 16px;
  min-height: 44px;
}

/* Primary Button */
.btn-primary {
  background: var(--iluria-color-button-primary-bg);
  color: var(--iluria-color-button-primary-fg);
  border-color: var(--iluria-color-button-primary-bg);
}

.btn-primary:hover:not(:disabled) {
  background: var(--iluria-color-button-primary-bg-hover);
  border-color: var(--iluria-color-button-primary-bg-hover);
}

.btn-primary:active:not(:disabled) {
  background: var(--iluria-color-button-primary-bg-hover);
}

/* Secondary Button */
.btn-secondary {
  background: var(--iluria-color-button-secondary-bg);
  color: var(--iluria-color-button-secondary-fg);
  border-color: var(--iluria-color-button-secondary-bg);
}

.btn-secondary:hover:not(:disabled) {
  background: var(--iluria-color-button-secondary-bg-hover);
  border-color: var(--iluria-color-button-secondary-bg-hover);
}

/* Outline Variants */
.btn-primary.btn-outline {
  background: transparent;
  color: var(--iluria-color-text-primary);
  border-color: var(--iluria-color-button-primary-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Shadow mais sutil para outline */
}

.btn-primary.btn-outline:hover:not(:disabled) {
  background: var(--iluria-color-button-primary-bg);
  color: var(--iluria-color-button-primary-fg);
}

.btn-secondary.btn-outline {
  background: transparent;
  color: var(--iluria-color-button-text-primary);
  border-color: var(--iluria-color-button-secondary-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Shadow mais sutil para outline */
}

.btn-secondary.btn-outline:hover:not(:disabled) {
  background: var(--iluria-color-button-secondary-bg);
  color: var(--iluria-color-button-secondary-fg);
}

/* Danger Button */
.btn-danger {
  background: var(--iluria-color-button-danger-bg);
  color: var(--iluria-color-button-danger-fg);
  border-color: var(--iluria-color-button-danger-bg);
}

.btn-danger:hover:not(:disabled) {
  background: var(--iluria-color-button-danger-bg-hover);
  border-color: var(--iluria-color-button-danger-bg-hover);
}

.btn-danger.btn-outline {
  background: transparent;
  color: var(--iluria-color-button-danger-bg);
  border-color: var(--iluria-color-button-danger-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Shadow mais sutil para outline */
}

.btn-danger.btn-outline:hover:not(:disabled) {
  background: var(--iluria-color-button-danger-bg);
  color: var(--iluria-color-button-danger-fg);
}

/* Text/Ghost Variants */
.btn-text-primary,
.btn-primary.btn-ghost {
  background: transparent;
  color: var(--iluria-color-button-primary-bg);
  border-color: transparent;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08); /* Shadow muito sutil para ghost */
}

.btn-text-primary:hover:not(:disabled),
.btn-primary.btn-ghost:hover:not(:disabled) {
  background: rgba(59, 130, 246, 0.1);
  color: var(--iluria-color-button-primary-bg-hover);
}

.btn-text-danger,
.btn-danger.btn-ghost {
  background: transparent;
  color: var(--iluria-color-button-danger-bg);
  border-color: transparent;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08); /* Shadow muito sutil para ghost */
}

.btn-text-danger:hover:not(:disabled),
.btn-danger.btn-ghost:hover:not(:disabled) {
  background: rgba(239, 68, 68, 0.1);
  color: var(--iluria-color-button-danger-bg-hover);
}

.btn-text-green-600 {
  background: transparent;
  color: var(--iluria-color-button-confirm-bg);
  border-color: transparent;
  box-shadow: 0 1px 2px rgba(0, 0, 0, 0.08);
}

.btn-text-green-600:hover:not(:disabled) {
  background: rgba(16, 185, 129, 0.1); 
  color: var(--iluria-color-button-confirm-bg-hover);
}

/* Loading State */
.btn-loading {
  pointer-events: none;
}

.loading-spinner {
  width: 16px;
  height: 16px;
  border: 2px solid transparent;
  border-top: 2px solid currentColor;
  border-radius: 50%;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Icon spacing */
.button-icon {
  flex-shrink: 0;
}

.button-text {
  line-height: 1;
}

/* Only icon buttons */
.btn:not(:has(.button-text)) {
  padding-left: 8px;
  padding-right: 8px;
  min-width: auto;
}

.btn-small:not(:has(.button-text)) {
  padding: 6px;
  width: 32px;
}

.btn-medium:not(:has(.button-text)) {
  padding: 8px;
  width: 36px;
}

.btn-large:not(:has(.button-text)) {
  padding: 12px;
  width: 44px;
}

/* Dark/Black Button */
.btn-dark {
  background: var(--iluria-color-button-dark-bg);
  color: var(--iluria-color-button-dark-fg);
  border-color: var(--iluria-color-button-dark-bg);
}

.btn-dark:hover:not(:disabled) {
  background: var(--iluria-color-button-dark-bg-hover);
  border-color: var(--iluria-color-button-dark-bg-hover);
}

.btn-dark:active:not(:disabled) {
  background: var(--iluria-color-button-dark-bg-hover);
}

.btn-dark.btn-outline {
  background: transparent;
  color: var(--iluria-color-button-dark-bg);
  border-color: var(--iluria-color-button-dark-bg);
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1); /* Shadow mais sutil para outline */
}

.btn-dark.btn-outline:hover:not(:disabled) {
  background: var(--iluria-color-button-dark-bg);
  color: var(--iluria-color-button-dark-fg);
}
</style>
