{"title": "Theme Gallery", "description": "Manage and customize your store themes", "addTheme": "Create Theme", "initializeDefaults": "Initialize Default Themes", "initializeDefaultsTooltip": "Add system default themes to your store", "themes": "Themes", "themeName": "Theme Name", "themeDescription": "Theme Description", "published": "Published", "notPublished": "Not published", "themeLibrary": "Theme Library", "noFiltersAvailable": "No filters available", "filterBy": "Filter by {category}", "viewOptions": "View options", "gridView": "Grid view", "listView": "List view", "tags": "Tags", "categoriesTitle": "Categories", "hintDomainUrl": "The domain is the address of your website, which your customers will search to access it.", "categories": {"fashion": "Fashion", "electronics": "Electronics", "food": "Food & Beverages", "beauty": "Beauty", "home": "Home & Decor", "sports": "Sports", "books": "Books", "minimal": "Minimal", "modern": "Modern", "classic": "Classic", "business": "Business", "creative": "Creative", "store": "Store", "blog": "Blog", "portfolio": "Portfolio", "moderno": "Modern", "minimalista": "Minimalist", "luxo": "Luxury", "tecnologia": "Technology", "escandinavo": "Scandinavian", "dark": "Dark", "vibrante": "Vibrant"}, "hidePreview": "Hide Preview", "showPreview": "Show Preview", "previewSimple": "Preview", "fullPreview": "Full Preview", "current": "Current", "select": "Select", "useTheme": "Use This Theme", "customize": "Customize", "confirmUseTheme": {"title": "Apply Theme", "message": "Are you sure you want to apply the theme \"{themeName}\"? This will replace your store's current theme."}, "themeAppliedSuccess": "Theme \"{themeName}\" applied successfully!", "errorApplyingTheme": "Error applying theme. Please try again.", "success": "Success", "error": "Error", "activate": "Activate", "rename": "<PERSON><PERSON>", "changeColors": "Change Colors", "duplicate": "Duplicate", "download": "Download", "editCode": "Edit Code", "editContent": "Edit Content", "delete": "Delete", "currentTheme": "Current Theme", "preview": {"title": "Theme Preview", "desktop": "Desktop", "tablet": "Tablet", "mobile": "Mobile", "selectTheme": "Select Theme", "dualView": "Dual View", "singleView": "Single View"}, "noThemesAvailable": "No themes available", "noThemesDescription": "Iluria default themes are being loaded. If the problem persists, check your connection.", "colorPalette": "Color Palette", "details": "Details", "layout": "Layout", "author": "Author", "version": "Version", "refresh": "Refresh", "back": "Back", "forward": "Forward", "home": "Home", "previewUrl": "Preview URL", "loadingPreview": "Loading preview...", "layoutContainer": "Container", "layoutFullWidth": "Full Width", "layoutContainerDesc": "Centered content with side margins", "layoutFullWidthDesc": "Content spans the full width of the screen", "basedOn": "Based on", "summary": "Summary", "setAsCurrent": "Set as current theme", "setAsCurrentDesc": "Apply this theme immediately after creation", "colorPreviewText": "This is an example of how the theme will look with the selected colors.", "resetColors": "Reset Colors", "randomColors": "Random Colors", "primaryColor": "Primary Color", "secondaryColor": "Secondary Color", "backgroundColor": "Background Color", "textColor": "Text Color", "renameDescription": "Enter a new name for the theme", "currentName": "Current name", "newName": "New name", "enterNewName": "Enter the new theme name", "renaming": "Renaming...", "renameInfo": "The theme name will be changed only for you. Other users will not be affected.", "nameRequired": "Name is required", "nameTooShort": "Name must be at least 2 characters", "nameTooLong": "Name must be at most 100 characters", "nameSameAsCurrent": "New name must be different from current", "nameAlreadyExists": "A theme with this name already exists", "renameError": "Error renaming theme. Please try again.", "changeColorsDescription": "Customize your theme colors", "saveColors": "Save Colors", "savingColors": "Saving...", "primaryButton": "Primary Button", "secondaryButton": "Secondary Button", "sampleText": "Sample text for preview", "changeColorsInfo": "Colors will be applied only to this theme. You can revert changes at any time.", "colorWheel": "Color Wheel", "colorConfiguration": "Color Configuration", "backgroundGeneral": "General Background", "backgroundComponents": "Components Background", "titleColor": "Title Color", "harmoniousColors": "Harmonious Colors", "applyColor": "Apply Color", "stylePreviewText": "See how your theme will look with the selected colors", "aiSuggestions": "AI Suggestions", "comingSoon": "Coming Soon", "tryIt": "Try It", "create": {"title": "Create New Theme", "step1": "Basic Information", "step2": "Select Base Theme", "step3": "Customize Colors", "step4": "Final Settings", "previous": "Previous", "next": "Next", "finish": "Finish", "category": "Create New Category"}, "categoryDescriptions": {"moderno": "Themes with contemporary and clean design", "fashion": "Themes for fashion and clothing stores", "minimalista": "Themes with simple and elegant design", "luxo": "Sophisticated themes for premium products", "tecnologia": "Themes for tech and electronic products", "escandinavo": "Themes with Nordic and natural style", "dark": "Themes with dark mode", "vibrante": "Themes with vibrant and energetic colors"}, "onboarding": {"title": "Welcome to Store Customization!", "description": "Choose a theme to start customizing your online store appearance. The theme defines colors, typography and overall layout.", "step1": "Choose a theme from the library below", "step2": "Customize colors and styles to match your brand", "step3": "Publish and start selling!", "chooseTheme": "Choose My First Theme"}, "ai": {"improveColors": "Improve Colors", "optimizeLayout": "Optimize Layout", "suggestions": {"improveColors": {"title": "Improve Color Palette", "description": "Your current palette could have better contrast to increase conversions by 12%"}, "optimizeLayout": {"title": "Optimize Mobile Layout", "description": "Reorganizing elements can improve mobile experience and reduce bounce rate"}, "improvePerformance": {"title": "Improve Performance", "description": "Automatic optimizations can reduce loading time by 1.2s"}, "generateVariants": {"title": "Generate A/B Variants", "description": "We create 3 variations of your theme for automatic A/B testing"}, "checkAccessibility": {"title": "Check Accessibility", "description": "We detected 3 accessibility issues that can be automatically fixed"}}}, "template": {"apply": "Apply Template", "version": "Template Version", "upload": "Upload Template", "download": "Download Template", "status": "Template Status", "available": "Available", "notAvailable": "Not Available", "updating": "Updating...", "pending": "Pending", "error": "Template <PERSON><PERSON><PERSON>", "synced": "Template Synced", "files": "{count} file(s)"}, "confirmActivation": {"title": "Activate Theme", "message": "Do you want to activate the theme \"{themeName}\"? The current theme will be replaced.", "confirm": "Activate Theme"}, "confirmDuplication": {"title": "Duplicate Theme", "message": "Do you want to create a copy of the theme \"{themeName}\"?", "confirm": "Duplicate Theme"}, "duplicating": {"title": "Duplicating theme", "message": "The theme \"{themeName}\" is being duplicated. Please wait a moment..."}, "confirmDeletion": {"title": "Delete Theme", "message": "This action cannot be undone. Do you want to delete the theme \"{themeName}\"?", "confirm": "Delete Theme"}, "addCategory": "Add Category", "categoryName": "Category Name", "categoryDescription": "Category Description", "categoryColor": "Category Color"}