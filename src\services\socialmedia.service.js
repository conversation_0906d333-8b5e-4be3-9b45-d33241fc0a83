import axios from 'axios';
import { API_URLS } from '../config/api.config';

class SocialMediaService {
  #endpoint;

  constructor() {
    this.#endpoint = `${API_URLS.STORE_BASE_URL}/social-media-settings`;
  }
  
  getEndpoint() {
    return this.#endpoint;
  }
  
  async getSocialMediaSettings() {
    try {
      try {
        const response = await axios.get(`${this.#endpoint}`);
        return response.data;
      } catch (serverError) {
        if (serverError.response && serverError.response.status === 404) {
          return this.#getDefaultSettings();
        }
        throw serverError;
      }
    } catch (error) {
      console.error('Erro ao buscar configurações de redes sociais:', error);
      return this.#getDefaultSettings();
    }
  }

  #getDefaultSettings() {
    return {
      id: null,
      title: '',
      description: '',
      profileImage: null
    };
  }

  async updateSocialMediaSettings(data) {
    try {
      // Se profileImage estiver vazio ou não for fornecido, não o enviamos
      // O backend manterá a referência existente
      const serverData = {
        title: data.title || '',
        description: data.description || ''
      };
      
      // Só incluir profileImage se for explicitamente fornecido
      if (data.profileImage !== undefined) {
        serverData.profileImage = data.profileImage;
      }
      
      const config = {
        headers: {
          'Content-Type': 'application/json'
        }
      };
      
      try {
        const response = await axios.put(`${this.#endpoint}`, serverData, config);
        return response.data;
      } catch (putError) {
        if (putError.response && putError.response.status === 404) {
          const postResponse = await axios.post(`${this.#endpoint}`, serverData, config);
          return postResponse.data;
        }
        throw putError;
      }
    } catch (error) {
      console.error('Erro ao atualizar configurações de redes sociais:', error);
      throw error;
    }
  }
  
  /**
   * Faz o upload de uma imagem para as configurações de redes sociais
   * @param {File} file - Arquivo de imagem para upload
   * @param {Object} currentSettings - Configurações atuais de redes sociais
   * @returns {Promise<Object>} - Configurações de redes sociais atualizadas
   */
  async uploadImage(file, currentSettings) {
    try {
      if (!file?.type?.startsWith('image/')) {
        throw new Error('O arquivo deve ser uma imagem válida');
      }
      
      const formData = new FormData();
      formData.append('file', file);
      
      await axios.post(`${this.#endpoint}/images`, formData, {
        headers: {
          'Content-Type': 'multipart/form-data'
        }
      });

      const response = await axios.get(this.#endpoint);
      return response.data;
    } catch (error) {
      console.error('Erro ao fazer upload da imagem:', error);
      throw error;
    }
  }
  
  /**
   * Remove uma imagem das configurações de redes sociais
   * @param {string} imageUrl - URL da imagem a ser removida
   * @returns {Promise<void>}
   */
  async deleteImage(imageUrl) {
    if (!imageUrl) {
      return;
    }
    
    try {
      // Não precisamos do nome do arquivo, o backend usa o ID da loja para identificar a imagem
      await axios.delete(`${this.#endpoint}/images`);
      
      const currentSettings = await this.getSocialMediaSettings();
      if (currentSettings.profileImage === imageUrl) {
        await this.updateSocialMediaSettings({
          ...currentSettings,
          profileImage: ''
        });
      }
    } catch (error) {
      console.error('Erro ao remover a imagem:', error);
      if (error.response && error.response.status === 404) {
        return;
      }
      throw error;
    }
  }
}

export default new SocialMediaService();
